<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="%PUBLIC_URL%/icon.svg" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<meta name="theme-color" content="#000000" />
		<meta name="description" content="" />
		<link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
		<title>Zeus</title>
		<script src="./iconfont.js"></script>
		<script src="./diff_match_patch.js"></script>
		<script src="./sql-formatter.min.js"></script>
		<script src="./sql-formatter.min.cjs"></script>
		<script>
			(function (window, document, undefined) {
				function WwLogin(data) {
					var host = data.host || 'https://hxwxlt.95577.com.cn';
					var frame = document.createElement('iframe');
					var url =
						host +
						'/wwopen/sso/qrConnect?appid=' +
						data.appid +
						'&agentid=' +
						data.agentid +
						'&redirect_uri=' +
						data.redirect_uri +
						'&state=' +
						data.state +
						'&login_type=jssdk';
					url += data.style ? '&style=' + data.style : '';
					url += data.href ? '&href=' + data.href : '';
					// console.log(url);
					frame.src = url;
					frame.frameBorder = '0';
					frame.allowTransparency = 'true';
					frame.scrolling = 'no';
					frame.width = '300px';
					frame.height = '400px';
					var el = document.getElementById(data.id);
					el.innerHTML = '';
					el.appendChild(frame);
					frame.onload = function () {
						if (
							frame.contentWindow.postMessage &&
							window.addEventListener
						) {
							window.addEventListener(
								'message',
								function (event) {
									var hostArr = host.split(':');
									if (hostArr[1] == 80) host = hostArr[0];
									if (
										event.data &&
										event.origin.indexOf(host) > -1
									) {
										// console.log(event);
										data.myLoad(event);
										// window.location.href = event.data;
									}
								},
								{ once: true }
							);
							frame.contentWindow.postMessage(
								'ask_usePostMessage',
								'*'
							);
						}
					};
				}

				window.WwLogin = WwLogin;
			})(window, document);
		</script>
		<script>
			// 禁用alt（command) + 方向键的前进后退快捷键
			function keyDown() {
				var agent = navigator.userAgent.toLocaleLowerCase();
				var isMac = /macintosh|mac os x/i.test(navigator.userAgent);
				if (isMac) {
					if (
						window.event.metaKey &&
						(window.event.keyCode == 37 ||
							window.event.keyCode == 39)
					) {
						event.returnValue = false;
					}
				} else {
					if (
						window.event.altKey &&
						(window.event.keyCode == 37 ||
							window.event.keyCode == 39)
					) {
						event.returnValue = false;
					}
				}
			}
			document.onkeydown = keyDown;
		</script>
		<!-- 禁用所有前进、后退操作 -->
		<!-- <script>
		history.pushState(null, null, document.URL);
		window.addEventListener('popstate', function() {
			history.pushState(null, null, document.URL);
		})
	</script> -->
	</head>
	<body>
		<noscript>You need to enable JavaScript to run this app.</noscript>
		<div id="root"></div>
	</body>
</html>

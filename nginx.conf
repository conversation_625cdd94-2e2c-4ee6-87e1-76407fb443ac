user root;
    worker_processes 1;

    #error_log  logs/error.log;
    #error_log  logs/error.log  notice;
    #error_log  logs/error.log  info;

    #pid        logs/nginx.pid;
    events {
        worker_connections 1024;
    }


    http {
        charset utf-8;

        include mime.types;
        default_type application/octet-stream;

        #access_log  logs/access.log  main;
        sendfile on;
        #tcp_nopush     on;

        #keepalive_timeout  0;
        keepalive_timeout 600;

        gzip on;
        gzip_types text/plain application/x-javascript text/css text/javascript application/x-httpd-php image/jpeg image/gif image/png;
        #ssl on;
        client_max_body_size 3072m;
        # HTTPS server
		add_header X-Frame-Options sameorigin always;

        server {
            listen 30088;
            server_name _;
            #server_name_in_redirect off;
            #port_in_redirect off;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Server $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Host $host:$server_port;

            location / {
                root /usr/share/nginx/html;
                index index.html;
            }

            location /api/ {
                proxy_pass http://zeus:8080/;
                proxy_http_version 1.1;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
				add_header Cache-Control no-cache;
				add_header Pragma no-cache;
            }

            location /user/ {
                proxy_pass http://zeus:8080/;
                proxy_http_version 1.1;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            }

			location /ws/ {
                proxy_pass http://zeus:8080/;
                proxy_http_version 1.1;
                proxy_connect_timeout 10m;
                proxy_read_timeout 10m;
                proxy_send_timeout 10m;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header Upgrade $http_upgrade;   # 升级协议头
                proxy_set_header Connection upgrade;
            }

        }
	}

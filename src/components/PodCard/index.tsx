import { Badge, Space } from 'antd';
import React from 'react';
import './index.less';

interface PodCardProps {
	role: string;
	podName: string;
	status: string;
	ip: string;
	color?: string;
	selectKeys?: string[];
	onChange?: () => void;
	type?: string;
	nowPodName?: string;
}
export default function PodCard(props: PodCardProps): JSX.Element {
	const {
		role,
		podName,
		status,
		ip,
		color,
		selectKeys,
		onChange,
		type,
		nowPodName
	} = props;
	const roleRender = () => {
		if (type && type === 'postgresql') {
			switch (role) {
				case 'master':
					return '主';
				case 'slave':
					return '异';
				case 'sync_slave':
					return '同';
				case 'syncslave':
					return '同';
				default:
					return '从';
			}
		} else {
			switch (role) {
				case 'master':
					return '主';
				case 'syncslave':
					return '从';
				case 'sync_slave':
					return '从';
				default:
					return '从';
			}
		}
	};
	const statusRender = () => {
		switch (status) {
			case 'Running':
				return <Badge status="success" text={status} />;
			default:
				return <Badge status="error" text={status} />;
		}
	};
	return (
		<div
			className={`pod-card-content ${
				role !== 'master' && (type === 'mysql' || type === 'postgresql')
					? 'hover'
					: ''
			} ${selectKeys?.includes(podName) ? 'active' : ''}`}
			onClick={() => onChange && onChange()}
		>
			<div
				className="pod-card-top"
				style={{ borderBottomColor: color ? color : '#226ee7' }}
			>
				<div
					className="pod-card-top-label"
					style={{
						backgroundColor: color ? color : '#226ee7',
						marginRight: 8
					}}
				>
					{roleRender()}
				</div>
				<div
					className="text-overflow"
					style={{ maxWidth: '108px' }}
					title={podName}
				>
					{podName}
				</div>
				<div className="pod-card-top-status">{statusRender()}</div>
			</div>
			<div className="pod-card-bottom">
				<Space>
					<span className="pod-card-ip-label">IP</span>
					<span className="pod-card-ip-address">{ip}</span>
				</Space>
			</div>
		</div>
	);
}

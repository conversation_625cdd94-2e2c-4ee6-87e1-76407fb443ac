.pod-card-content {
	width: 224px;
	height: 82px;
	background: @white;
	box-shadow: 0px 5px 16px rgba(0, 0, 0, 0.04);
	border-radius: 4px;
	border: 1px solid transparent;
	&.hover {
		cursor: pointer;
		&:hover {
			border-color: @primary-color;
		}
	}
	&.active {
		border-color: @primary-color;
	}
	.pod-card-top {
		display: flex;
		height: 33px;
		border-bottom: 1px solid @orange-base;
		justify-content: space-between;
		align-items: center;
		.pod-card-top-label {
			width: 33px;
			height: 33px;
			background-color: @orange-base;
			color: @white;
			font-size: @font-3;
			text-align: center;
			line-height: 33px;
			border-radius: 4px 0px 0px 0px;
		}
		.pod-card-top-status {
			margin-right: @margin;
		}
	}
	.pod-card-bottom {
		text-align: center;
		line-height: 48px;
		height: 48px;
		.pod-card-ip-label {
			font-weight: @font-weight-sm;
			font-size: @font-2 - 2px;
			line-height: @line-height-2 - 4px;
			color: @black-5;
		}
		.pod-card-ip-address {
			font-weight: @font-weight;
			font-size: @font-3;
			line-height: @line-height-3;
		}
	}
}

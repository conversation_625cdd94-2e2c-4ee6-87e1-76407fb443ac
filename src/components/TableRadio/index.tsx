import React from 'react';
import { useHistory } from 'react-router';
import { Table, Select, Input, Button, Tooltip, Checkbox } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import { WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';
import storage from '@/utils/storage';

import './index.less';

const { Search } = Input;
export default function TableRadio(props: any): JSX.Element {
	const history = useHistory();
	const {
		selectedRow,
		selectedRowKeys,
		setSelectedRowKeys,
		setSelectedRow,
		showHeader,
		label,
		select,
		search,
		showRefresh,
		onRefresh,
		getCheckboxProps,
		backupComponent,
		middlewareInfo,
		...option
	} = props;
	const createBackOperatorId = maintenances['Create Backup'];

	const disabledLock = (locked: string) => {
		const projectId = storage.getSession('projectId');
		const role = JSON.parse(storage.getLocal('role'));
		const curProjectAuth = role?.userRoleList.find(
			(item: any) => item.projectId === projectId
		);
		// * 内置普通用户权限没有细分，未锁定服务可以操作，暂时将普通用户所有权限禁用，仅支持现有菜单查看
		if (curProjectAuth?.roleId === 4) return true;
		if (locked && (locked === 'unlocked' || locked === 'locking')) {
			// * 判断当前服务是否已解锁 或处于 锁定的审批中时
			return false;
		}
		if (role.isAdmin) {
			// * 判断当前用户是否为超级管理员
			return false;
		}
		if (curProjectAuth) {
			if (curProjectAuth.weight === 3 || curProjectAuth.weight === 2) {
				// * 判断当前项目下用户的权限为组织管理员或者项目管理员，可以直接进行操作
				return false;
			} else {
				return true;
			}
		} else {
			if (!role.dba) {
				// * 当用户找不到当前项目的权限且不是dba类型的角色时
				return true;
			} else {
				return false;
			}
		}
	};
	const judgeDisabled = (record: any) => {
		if (disabledLock(record.lock)) {
			return {
				disabled: true,
				title: (
					<div style={{ overflow: 'hidden' }}>
						<p style={{ color: '#585858' }}>
							当前服务已锁定，请先发起工单获取备份任务创建权限
						</p>
						<Button
							type="link"
							style={{ float: 'right' }}
							onClick={() => {
								storage.setSession('currentService', record);
								WorkOrderFuc(
									() => {
										console.log(1);
									},
									record.lock,
									record.name,
									createBackOperatorId,
									history,
									middlewareInfo.type,
									middlewareInfo.name,
									middlewareInfo.aliasName,
									record.clusterId,
									record.namespace
								);
							}}
						>
							立即前往
						</Button>
					</div>
				)
			};
		}
		if (record.status !== 'Running') {
			return {
				disabled: true,
				title: (
					<p style={{ color: '#585858' }}>
						该服务运行异常，无法进行备份
					</p>
				)
			};
		}
		if (selectedRow.length > 0) {
			if (record.clusterId !== selectedRow[0].clusterId) {
				return {
					disabled: true,
					title: (
						<p style={{ color: '#585858' }}>
							批量备份不支持跨集群备份
						</p>
					)
				};
			}
		}
	};
	const rowSelection = {
		selectedRowKeys: selectedRowKeys,
		onChange: (selectedRowKeys: any, selectedRows: any) => {
			setSelectedRow(
				selectedRows?.filter(
					(item: any) =>
						!disabledLock(item.lock) &&
						item.status === 'Running' &&
						backupComponent[item.clusterId]?.status === 3
				)
			);
			setSelectedRowKeys(
				selectedRows.length
					? selectedRows
							?.filter(
								(item: any) =>
									!disabledLock(item.lock) &&
									item.status === 'Running' &&
									backupComponent[item.clusterId]?.status ===
										3
							)
							?.map((item: any) => item.name)
					: []
			);
		},
		renderCell: (value: any, record: any) => {
			return judgeDisabled(record)?.disabled ? (
				<Tooltip color="#fff" title={judgeDisabled(record)?.title}>
					<Checkbox disabled />
				</Tooltip>
			) : (
				<Checkbox
					disabled={backupComponent[record.clusterId]?.status !== 3}
					checked={selectedRowKeys?.includes(record.name)}
					onChange={(e) => {
						if (e.target.checked) {
							setSelectedRow([...selectedRow, record]);
							setSelectedRowKeys([
								...selectedRowKeys,
								record.name
							]);
						} else {
							setSelectedRow(
								selectedRow.filter(
									(item: any) => item.name !== record.name
								)
							);
							setSelectedRowKeys(
								selectedRowKeys.filter(
									(item: string) => item !== record.name
								)
							);
						}
					}}
				/>
			);
		}
	};
	return (
		<div>
			{showHeader ? (
				<div className="table-radio-header">
					<div className="header-search">
						{label && <div className="label">{label}</div>}
						{select && <Select {...select} />}
						{search && <Search allowClear {...search} />}
					</div>
					{showRefresh && (
						<Button
							type="default"
							icon={<ReloadOutlined />}
							onClick={onRefresh}
						/>
					)}
				</div>
			) : null}
			<Table
				key="name"
				rowSelection={{
					...rowSelection
				}}
				size="middle"
				{...option}
			/>
		</div>
	);
}

#time-line-content {
	width: 100%;
	padding: 16px 0 0 16px;
	overflow-y: auto;
	.time-line-title {
		font-size: @font-1;
		font-weight: @font-weight;
		color: @text-color-title;
		cursor: pointer;
		.mixin (lineHeight, 20px);
	}
	.time-line-time {
		// width: 118px;
		height: 20px;
		font-size: @font-1;
		font-weight: @font-weight-sm;
		color: rgba(0, 0, 0, 0.45);
		line-height: @line-height-4;
	}
	.ant-timeline-item-content {
		margin-left: 34px;
	}
	.details-msg {
		display: flex;
		align-items: center;
		width: 100%;
		// height: 50px;
		line-height: 22px;
		cursor: pointer;
		.details-summary {
			max-width: 80%;
			text-overflow: ellipsis;
			overflow: hidden;
			display: -webkit-box; /*必须结合的属性 ，将对象作为弹性伸缩盒子模型显示 。*/
			-webkit-line-clamp: 2; /*用来限制在一个块元素显示的文本的行数。*/
			-webkit-box-orient: vertical; /*必须结合的属性 ，设置或检索伸缩盒对象的子元素的排列方式 。*/
		}
	}
	.details-color {
		color: @primary-color;
		cursor: pointer;
		margin-left: 8px;
	}
}
.info-tip {
	@include alarm-tip;
	background: #c7ecff;
	border: 1px solid #94dbff;
	color: #00a7fa;
}
.warning-tip {
	@include alarm-tip;
	background: #ffecc7;
	border: 1px solid #ffdb94;
	color: #faa700;
}
.critical-tip {
	@include alarm-tip;
	background: #fff1f0;
	border: 1px solid #ffa39e;
	color: #ff4d4f;
}
.active {
	color: @primary-color !important;
}
.no-content {
	width: 100%;
	height: calc(100% - 90px);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

import React, { useState, useEffect } from 'react';
import { Timeline, Empty } from 'antd';
import { useHistory, useLocation } from 'react-router-dom';
import { connect } from 'react-redux';

import transTime from '@/utils/transTime';
import { getProjects } from '@/services/project';
import { alarmTimeLineProps } from './alarmTimeLine';
import { StoreState } from '@/types';
import storage from '@/utils/storage';

import './index.less';
import { findInNestedArray } from '@/utils/utils';

function AlarmTimeLine(props: alarmTimeLineProps): JSX.Element {
	const {
		type,
		style = {},
		list = [],
		buttonList = [],
		allMenu = []
	} = props;
	const [data, setData] = useState<any>(list);
	const history = useHistory();
	const location = useLocation();
	const show = (code: string) =>
		buttonList.find((item: MenuResItem) => item.name === code);

	// props变化时修改list值
	useEffect(() => {
		setData(list);
	}, [props]);

	const isLink = (item: any): boolean => {
		if (item.lay === 'system') {
			if (show('overviewJumpAlarm')) {
				return true;
			} else {
				return false;
			}
		} else if (item.lay === 'cluster') {
			if (show('overviewJumpClusterAlarm')) {
				return true;
			} else {
				return false;
			}
		} else {
			if (
				item.chartVersion &&
				item.type &&
				item.clusterId &&
				item.namespace &&
				item.projectId &&
				item.organId &&
				findInNestedArray(allMenu, 'workspaceDateJump', true)
			) {
				return true;
			} else {
				return false;
			}
		}
	};

	const dotRender = (value: string) => {
		return (
			<div className={`${value}-tip`}>
				{value === 'info'
					? '一般'
					: value === 'warning'
					? '次要'
					: '重要'}
			</div>
		);
	};

	const toDetail = async (item: any) => {
		if (item.lay === 'system' && show('overviewJumpAlarm')) {
			history.push('/platform/alarmCenter/system');
			return;
		}
		if (item.lay === 'cluster' && show('overviewJumpClusterAlarm')) {
			history.push('/platform/alarmCenter/cluster');
			return;
		}
		if (
			!item.chartVersion ||
			!item.type ||
			!item.clusterId ||
			!item.namespace ||
			!item.projectId ||
			!item.organId ||
			!findInNestedArray(allMenu, 'workspaceDateJump', true)
		) {
			return;
		} else {
			const res = await getProjects({
				organId: item?.organId || '',
				key: ''
			});
			if (res.success) {
				const project = res.data?.find(
					(str) => str.projectId === item.projectId
				);
				storage.setSession('organId', item?.organId);
				storage.setSession('project', project);
				storage.setSession('projectId', item.projectId);
				storage.setSession('preUrl', location.pathname);
				history.push({
					pathname: `/project/${item.chartType}/${item.type}/${item.chartAliasName}/container/basicInfo/${item.name}/${item?.chartVersion}/${item.clusterId}/${item.namespace}`
				});
			}
		}
	};

	if (!data || !data.length) {
		if (type === 'workspace') {
			return (
				<div style={{ marginTop: 100 }}>
					<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
				</div>
			);
		} else {
			return (
				<div className="no-content">
					<Empty
						imageStyle={{
							height: 52
						}}
					/>
				</div>
			);
		}
	}

	return (
		<div style={style} id="time-line-content">
			<Timeline>
				{data &&
					data.map((item: any, index: number) => {
						return (
							<Timeline.Item
								key={index}
								dot={dotRender(item.level)}
							>
								<p>
									<span className="time-line-title">
										{item.lay === 'system'
											? '(系统级) '
											: item.lay === 'cluster'
											? '(集群级)'
											: '(服务级) '}
									</span>
									<span
										className={`time-line-title ${
											isLink(item) ? 'active' : ''
										}`}
										onClick={() => toDetail(item)}
									>
										{item.name}
									</span>
								</p>
								<div className="time-line-time">
									{transTime.gmt2local(item.alertTime)}
								</div>
								<div className="details-msg">
									<div className="details-summary">
										{item.content && (
											<span title={item.content}>
												{item.content}
											</span>
										)}
										<span
											title={
												item.message ||
												item.summary ||
												''
											}
										>
											{item.message
												? item.message + '；'
												: item.summary || ''}
										</span>
									</div>
								</div>
							</Timeline.Item>
						);
					})}
			</Timeline>
		</div>
	);
}
export default connect((state: StoreState) => ({
	allMenu: state.auth.allMenu,
	buttonList: state.auth.buttonList
}))(AlarmTimeLine);

import React from 'react';
import { SelectBlockProps } from './selectBlock';
import './index.less';

/**
 * 块选择
 * @param { options current disabled } props 选项 当前项 是否是禁用状态
 */
export default function SelectBlock(props: SelectBlockProps): JSX.Element {
	const {
		options = [],
		currentValue = '',
		onCallBack,
		disabled,
		itemStyle
	} = props;

	return (
		<div
			className={`display-flex ${
				disabled ? 'select-box-disabled' : 'select-box'
			}`}
		>
			{options.map((option: any, index: number) => {
				return (
					<div
						key={index}
						style={itemStyle}
						className={`select-box-item ${
							currentValue === option.value
								? 'select-box-item-active'
								: ''
						} ${option.disabled ? 'disabled' : ''}`}
						onClick={() =>
							!disabled &&
							!option.disabled &&
							onCallBack &&
							onCallBack(option.value)
						}
						title={option.label}
					>
						{option.label}
					</div>
				);
			})}
		</div>
	);
}

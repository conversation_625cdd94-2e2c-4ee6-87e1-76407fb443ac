.select-box {
	.select-box-item {
		cursor: pointer;
		width: 182px;
		line-height: 14px;
		color: @text-color-title;
		font-weight: @font-weight;
		margin-right: 8px;
		border: 1px solid @border-color;
		text-align: center;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		padding: 8px;
		&:hover {
			color: @primary-color;
			border: 1px solid #b2d4ef;
		}
		&.disabled {
			color: #d2d2d2;
			cursor: not-allowed;
		}
		&.disabled:hover {
			color: #d2d2d2;
			border: 1px solid @border-color;
		}
	}
	.select-box-item:last-child {
		margin-right: 0;
	}
	.select-box-item.select-box-item-active {
		background: #dee9f6;
		border: 1px solid #b2d4ef;
	}
	.select-box-item.select-box-item-active.disabled {
		background: #fff;
		border: 1px solid @border-color;
	}
}
.select-box-disabled {
	.select-box-item {
		cursor: not-allowed;
		width: 182px;
		height: 32px;
		line-height: 14px;
		color: #d2d2d2;
		font-weight: @font-weight;
		margin-right: 8px;
		border: 1px solid @border-color;
		text-align: center;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		padding: 8px;
		&:hover {
			color: #d2d2d2;
			border: 1px solid @border-color;
		}
	}
	.select-box-item:last-child {
		margin-right: 0;
	}
	.select-box-item.select-box-item-active {
		background: #f3f3f3;
		border: 1px dashed #d1d5d9;
		color: @white;
	}
}

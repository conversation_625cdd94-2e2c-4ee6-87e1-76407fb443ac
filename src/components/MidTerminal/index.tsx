import React, { useEffect, useRef, useState } from 'react';
import 'xterm/css/xterm.css';
import { Terminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import cache from '@/utils/storage';
import { TOKEN } from '@/services/request';
const action = (type: any, data?: any) => {
	const action = Object.assign(
		{
			type: type
		},
		data
	);
	return JSON.stringify(action);
};
interface MidTerminalProps {
	middlewareName: string;
	middlewareType: string;
	scriptType: string;
	pod: string;
	container: string;
	namespace: string;
	clusterId: string;
	open: boolean;
}
export default function MidTerminal(props: MidTerminalProps): JSX.Element {
	const {
		middlewareName,
		middlewareType,
		scriptType,
		pod,
		container,
		namespace,
		clusterId,
		open
	} = props;
	const socket = useRef<any>();
	const terminal = useRef<any>();
	const [socketUrl, setSocketUrl] = useState<string>('');
	const MyHeight = document.body.clientHeight - 236;
	useEffect(() => {
		if (pod && container && scriptType) {
			if (window.location.protocol.toLowerCase() === 'https:') {
				setSocketUrl(
					`wss://${window.location.hostname}:${window.location.port}/ws/terminal?/middlewareName=${middlewareName}&middlewareType=${middlewareType}&source=container&terminalType=console&scriptType=${scriptType}&container=${container}&pod=${pod}&namespace=${namespace}&clusterId=${clusterId}`
				);
			} else {
				setSocketUrl(
					`ws://${window.location.hostname}:${window.location.port}/ws/terminal?/middlewareName=${middlewareName}&middlewareType=${middlewareType}&source=container&terminalType=console&scriptType=${scriptType}&container=${container}&pod=${pod}&namespace=${namespace}&clusterId=${clusterId}`
				);
			}
			// if (window.location.protocol.toLowerCase() === 'https:') {
			// 	setSocketUrl(
			// 		`wss://*************:31088/ws/terminal?/middlewareName=${middlewareName}&middlewareType=${middlewareType}&source=container&terminalType=console&scriptType=${scriptType}&container=${container}&pod=${pod}&namespace=${namespace}&clusterId=${clusterId}`
			// 	);
			// } else {
			// 	setSocketUrl(
			// 		`ws://*************:31088/ws/terminal?/middlewareName=${middlewareName}&middlewareType=${middlewareType}&source=container&terminalType=console&scriptType=${scriptType}&container=${container}&pod=${pod}&namespace=${namespace}&clusterId=${clusterId}`
			// 	);
			// }
		}
	}, [pod, container, scriptType]);
	useEffect(() => {
		terminal.current = new Terminal({
			cursorStyle: 'block',
			cursorBlink: true,
			theme: {
				foreground: '#dddddd',
				cursor: 'gray'
			},
			windowsMode: true
		});
		// xterm 对websocket适用的插件，但在这里用的时候，在页面显示上有问题，就先注解了。
		// const attachAddon = new AttachAddon(socket);
		// terminal.loadAddon(attachAddon);
		// attachAddon.activate(socket);
		const fitAddon = new FitAddon();
		terminal.current.loadAddon(fitAddon);
		const terminalDom = document.getElementById('terminal-container');
		terminal.current.open(terminalDom as HTMLElement);
		fitAddon.fit();
		if (open) {
			// socket.current && socket.current.close();
			socket.current = new WebSocket(socketUrl, cache.getLocal(TOKEN));
			socket.current.onopen = () => {
				socket.current.send(action('TERMINAL_INIT'));
				socket.current.send(action('TERMINAL_READY'));
				socket.current.send(
					action('TERMINAL_RESIZE', {
						columns: fitAddon.proposeDimensions().cols,
						rows: fitAddon.proposeDimensions().rows
					})
				);
				terminal.current.write('Welcome to terminal! \r\n$');
			};
			socket.current.onclose = () => {
				terminal.current.write('Bye Bye! \r\n$');
			};
			socket.current.onerror = () => {
				terminal.current.write('Something errors \r\n$');
			};
			terminal.current.onData((e: string) => {
				socket.current.send(
					action('TERMINAL_COMMAND', {
						command: e
					})
				);
			});
			socket.current.onmessage = (e: MessageEvent<any>) => {
				console.log(e);
				const data = JSON.parse(e?.data);
				// terminal.clear();
				if (data?.type == 'TERMINAL_PRINT') {
					terminal.current.write(data.text);
				}
			};
		}
		return () => {
			socket.current && socket.current.close();
			terminal.current && terminal.current.dispose();
		};
	}, [open, socketUrl]);

	return (
		<div
			id="terminal-container"
			style={{ width: '100%', height: `${MyHeight}px` }}
		></div>
	);
}

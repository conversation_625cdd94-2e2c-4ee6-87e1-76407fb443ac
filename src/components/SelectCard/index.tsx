import React from 'react';
import { SelectBlockProps } from '../SelectBlock/selectBlock';
import { IconFont } from '../IconFont';
import './index.less';

/**
 * 块选择
 * @param { options current disabled } props 选项 当前项 是否是禁用状态
 */
enum IconRender {
	readWrite = 'icon-duxie1',
	readOnly = 'icon-zhidu',
	client = 'icon-fuwulianjie',
	proxy = 'icon-duxiefenli',
	manager = 'icon-yemianguanli',
	external = 'icon-jiqunwaifangwen',
	console = 'icon-yemianguanli',
	predixy = 'icon-duxiefenli',
	kibana = 'icon-yemianguanli'
}
export default function SelectCard(props: SelectBlockProps): JSX.Element {
	const {
		options = [],
		currentValue = '',
		onCallBack,
		disabled,
		itemStyle
	} = props;

	return (
		<div
			className={`display-flex select-card ${
				disabled && 'select-card-disabled'
			}`}
		>
			{options.map((option: any, index: number) => {
				return (
					<div
						key={index}
						style={itemStyle}
						className={`select-card-item ${
							currentValue === option.exposeType ? 'active' : ''
						} ${option.disabled ? 'disabled' : ''}`}
						onClick={() =>
							!disabled &&
							!option.disabled &&
							onCallBack &&
							onCallBack(option.exposeType)
						}
						title={option.aliasExposeType}
					>
						<IconFont
							type={IconRender[option.exposeType]}
							style={{ fontSize: 40, marginBottom: 8 }}
						/>
						<span>{option.aliasExposeType}</span>
					</div>
				);
			})}
		</div>
	);
}

.acl-user-config {
	width: 100%;
	background: #fafafa;
	border: 1px solid #ebebeb;
	// margin-bottom: 16px;
	display: flex;
	justify-content: space-between;
	align-content: center;
	cursor: pointer;
	.mixin (lineHeight, 48px);
	.acl-user-title {
		margin-left: 16px;
		color: @text-color-title;
	}
	.acl-user-close {
		margin-right: 16px;
	}
}
.acl-config-list {
	padding: 16px;
	border: 1px solid #ebebeb;
}
.acl-equal {
	margin: 0 7px;
	line-height: @line-height-2 * 2;
}
.acl-custom-label {
	margin-left: 16px;
	line-height: @line-height-2 * 2;
}
.acl-add-user-config {
	color: @primary-color;
	cursor: pointer;
	margin: 8px 0 0 0;
	& span {
		margin-left: 8px;
	}
	&.disabled {
		color: rgba(0, 0, 0, 0.25);
		cursor: not-allowed;
	}
}
.acl-error-text {
	margin: 0px 0px 10px 120px;
	color: @error-color;
}

import React, { useEffect } from 'react';
import { Form, Input, Select, Button, Row, Col } from 'antd';
import { MinusOutlined, PlusOutlined } from '@ant-design/icons';

const { Option } = Select;

const MonitorAddressFormItem = (props: {
	layout: number;
	required?: boolean;
}) => {
	const { layout, required } = props;
	const form = Form.useFormInstance();

	useEffect(() => {
		form.setFieldsValue({
			extraMiddlewareMonitorInfoList: [
				{
					protocol: null,
					address: '',
					port: '',
					path: ''
				}
			]
		});
	}, []);

	const handleAddAddress = () => {
		const extraMiddlewareMonitorInfoList =
			form.getFieldValue('extraMiddlewareMonitorInfoList') || [];
		const newAddress = {
			protocol: null,
			address: '',
			port: '',
			path: ''
		};
		form.setFieldsValue({
			extraMiddlewareMonitorInfoList: [
				...extraMiddlewareMonitorInfoList,
				newAddress
			]
		});
	};

	const handleRemoveAddress = (index: number) => {
		const extraMiddlewareMonitorInfoList =
			form.getFieldValue('extraMiddlewareMonitorInfoList') || [];
		extraMiddlewareMonitorInfoList.splice(index, 1);
		form.setFieldsValue({ extraMiddlewareMonitorInfoList });
	};

	return (
		<Form.List name="extraMiddlewareMonitorInfoList">
			{(fields, { add, remove }) => (
				<>
					{fields.map((field, index) => (
						<div key={index}>
							<Form.Item
								colon={false}
								label={index === 0 ? '监控配置地址：' : ' '}
								shouldUpdate={(prevValues, curValues) =>
									prevValues.extraMiddlewareMonitorInfoList !==
									curValues.extraMiddlewareMonitorInfoList
								}
								style={{ marginBottom: 0 }}
							>
								{() => (
									<>
										<div className="display-flex">
											<Button
												icon={
													<MinusOutlined
														style={{
															fontSize: 12
														}}
													/>
												}
												style={{
													width: 16,
													height: 16,
													padding: 0,
													margin: '8px 16px 7px -32px'
												}}
												disabled={fields.length == 1}
												onClick={() =>
													handleRemoveAddress(index)
												}
											/>
											<Form.Item
												{...field}
												name={[field.name, 'protocol']}
												rules={[
													{
														required,
														message: '请选择协议'
													}
												]}
												style={{ width: '30%' }}
											>
												<Select
													placeholder="请选择协议"
													style={{
														width: '100%'
													}}
												>
													<Option value="http">
														http
													</Option>
													<Option value="https">
														https
													</Option>
												</Select>
											</Form.Item>
											<Form.Item
												{...field}
												name={[field.name, 'address']}
												rules={[
													{
														required,
														message: '请输入地址'
													}
												]}
												style={{ width: '40%' }}
											>
												<Input
													placeholder="请输入地址"
													style={{
														width: '100%',
														borderLeft: 0,
														borderRight: 0
													}}
												/>
											</Form.Item>
											<Form.Item
												{...field}
												name={[field.name, 'port']}
												rules={[
													{
														required,
														message: '请输入端口号'
													}
												]}
												style={{ width: '30%' }}
											>
												<Input
													placeholder="请输入端口号"
													style={{
														width: '100%'
													}}
												/>
											</Form.Item>
										</div>
										<Form.Item
											{...field}
											name={[field.name, 'path']}
											rules={[
												{
													required,
													message: '请输入路径'
												}
											]}
											style={{ width: '100%' }}
										>
											<Input
												placeholder="请输入路径"
												style={{
													width: '100%'
												}}
											/>
										</Form.Item>
									</>
								)}
							</Form.Item>
						</div>
					))}
					<Row
						style={{
							marginBottom: 24
						}}
					>
						<Col
							offset={layout}
							span={18}
							style={{
								alignItems: 'center'
							}}
						>
							<Button
								icon={
									<PlusOutlined
										style={{
											fontSize: 12
										}}
									/>
								}
								style={{
									width: 16,
									height: 16,
									padding: 0,
									marginRight: 16,
									marginLeft: '-32px',
									color: '#226ee7',
									borderColor: '#226ee7'
								}}
								onClick={handleAddAddress}
							/>
							<Button
								type="link"
								style={{ padding: 0 }}
								onClick={handleAddAddress}
							>
								新增
							</Button>
						</Col>
					</Row>
				</>
			)}
		</Form.List>
	);
};

export default MonitorAddressFormItem;

.zeus-actions-content {
	& > span {
		position: relative;
		&::after {
			content: '';
			position: absolute;
			width: 1px;
			height: 14px;
			top: 1px;
			right: -8px;
			border-right: 1px solid @black-7;
		}
		&:nth-last-child(1)::after {
			content: '';
			position: absolute;
			width: 0;
			height: 14px;
			top: 1px;
			right: -8px;
			border-right: none;
		}
	}

	.name-link:first-of-type {
		margin-left: 0 !important;
	}
	.displayed-name:first-of-type {
		margin-left: 0 !important;
	}
	// span:nth-last-child()::after {
	// 	content: '';
	// position: absolute;
	// width: 1px;
	// height: 14px;
	// right: -8px;
	// border-right: 1px solid @black-7;
	// }
}

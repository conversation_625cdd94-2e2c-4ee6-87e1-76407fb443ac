.mid-card-content {
	width: 240px;
	height: 124px;
	background: @white;
	box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.09);
	border-radius: @border-radius;
	border: 1px solid #e9e9e9;
	margin-right: 16px;
	margin-top: 16px;
	cursor: pointer;
	&:hover {
		box-shadow: 1px 1px 5px 5px rgba(0, 0, 0, 0.09);
	}
	.mid-card-display {
		padding: 16px;
		display: flex;
		.mid-card-add {
			width: 100%;
			height: 60px;
			text-align: center;
			line-height: 60px;
			font-size: @font-2;
			& > i {
				margin-right: 8px;
			}
		}
		.mid-card-icon {
			width: 60px;
			height: 60px;
			border-radius: @border-radius-lg;
			text-align: center;
			line-height: 80px;
		}
		.mid-card-title {
			width: calc(100% - 60px);
			height: 60px;
			font-size: @font-2;
			font-weight: @font-weight-sm;
			color: rgba(0, 0, 0, 0.85);
			margin-left: 12px;
			display: flex;
			flex-direction: column;
			justify-content: center;
			.mid-card-icon {
				text-align: left;
				width: 100px;
				height: 18px;
				line-height: @line-height-3;
				font-size: @font-1;
				margin-top: 4px;
				& > img {
					vertical-align: text-top;
					width: 12px;
					margin-top: 1.5px;
				}
			}
			&:hover {
				.text-overflow-2 {
					color: @blue-base;
				}
			}
		}
	}
	.mid-card-action {
		width: 239px;
		height: 32px;
		background: #f7f7f7;
		border-radius: 0px 0px @border-radius @border-radius;
		border-top: 1px solid #e9e9e9;
		border-right: 1px solid #e9e9e9;
		border-bottom: 1px solid #e9e9e9;
		display: flex;
		color: #878787;
		.mid-card-left, .mid-card-right {
			width: 50%;
			border-right: 1px solid #e8e8e8;
			height: 16px;
			line-height: 16px;
			text-align: center;
			margin-top: 8px;
			cursor: pointer;
		}
		.mid-card-right {
			width: 49%;
			border-right: none;
		}
	}
	.mid-card-center {
		width: 239px;
		height: 32px;
		background: #f7f7f7;
		border-radius: 0px 0px @border-radius @border-radius;
		color: #878787;
		text-align: center;
		line-height: @line-height-5;
		cursor: pointer;
		border-top: 1px solid #e9e9e9;
		border-right: 1px solid #e9e9e9;
		border-bottom: 1px solid #e9e9e9;
	}
	.danger:hover {
		color: #c80000;
	}
	.link:hover{
		color: @primary-color;
	}
}

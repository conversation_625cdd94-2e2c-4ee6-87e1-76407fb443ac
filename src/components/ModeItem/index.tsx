import React, { useEffect, useState } from 'react';
import { Input } from 'antd';
import EditQuotaForm from './EditQuotaForm';
import { minmax } from '@/utils/const';
import './index.less';

export interface modeItemProps {
	data: {
		cpu: number;
		disabled: boolean;
		memory: number;
		num: number;
		specId: string;
		storageClass: string;
		storageQuota: number;
		title: string;
		sentinelPort?: number;
		predixyPort?: number;
		predixyExporterPort?: number;
		sentinelExporterPort?: number;
	};
	mode?: string;
	clusterId: string;
	namespace?: string;
	type: string;
	onChange: (value: modeItemProps['data']) => void;
	middlewareType: string;
	isActiveActive?: boolean;
	disabled?: boolean;
	storageVisible?: boolean;
	directory?: boolean;
	handleStorageCompute?: (storage: number) => void;
	obj: any;
	defaultStorage?: number;
	customPortAPI?: boolean;
}
const ModeItem = (props: modeItemProps): JSX.Element => {
	const {
		data,
		clusterId,
		namespace,
		type,
		mode,
		obj,
		directory,
		onChange,
		middlewareType,
		isActiveActive,
		disabled,
		storageVisible,
		handleStorageCompute,
		defaultStorage,
		customPortAPI
	} = props;
	const [modifyData, setModifyData] = useState<modeItemProps['data']>(data);
	const [isEdit, setIsEdit] = useState<boolean>(false);
	const [visible, setVisible] = useState<boolean>(false);
	const [instanceType, setInstanceType] = useState<string>('General');
	// * 判断proxy是推荐规格还是自定义修改
	const [isAuto, setIsAuto] = useState<boolean>(false);
	const onCreate = (value: any) => {
		onChange(value);
		setModifyData({
			...modifyData,
			...value
		});
		setVisible(false);
	};
	useEffect(() => {
		setModifyData(data);
	}, [data]);
	useEffect(() => {
		if (type === 'proxy') {
			setIsAuto(true);
			console.log(
				minmax(
					obj[middlewareType].memory / 4,
					0.256,
					middlewareType === 'redis' ? 2 : 0
				)
			);

			setModifyData({
				...data,
				cpu: middlewareType === 'mysql' ? obj.mysql.cpu / 4 : 1,
				memory: minmax(
					obj[middlewareType].memory / 4,
					0.256,
					middlewareType === 'redis' ? 2 : 0
				)
			});
		}
	}, [obj[middlewareType]]);
	useEffect(() => {
		onChange(modifyData);
	}, [modifyData]);
	const inputChange = (value: any) => {
		setModifyData({
			...modifyData,
			num: value?.target?.value
		});
	};
	if (data.disabled) {
		return (
			<div className="mode-item-box">
				<div className="mode-item-title data-disabled">
					<span>{data.title}</span>
				</div>
				<div className="mode-item-data-disabled">未启用</div>
			</div>
		);
	} else {
		return (
			<div className="mode-item-box">
				<div className="mode-item-title">
					<span>{data.title}</span>
					{props.type !== 'mysql' &&
					props.type !== 'redis' &&
					type !== 'proxy' ? (
						isEdit ? (
							<Input
								size="small"
								value={modifyData.num}
								type="number"
								bordered={false}
								onChange={inputChange}
								onBlur={() => setIsEdit(false)}
								autoFocus={true}
								min={
									data.title === '主节点' ||
									data.title === '数据节点'
										? 3
										: 1
								}
								className="mode-item-number-input"
							/>
						) : (
							<span
								className="mode-item-circle"
								onClick={() => setIsEdit(true)}
							>
								{modifyData.num}
							</span>
						)
					) : null}
				</div>
				<div
					className="mode-item-data"
					onClick={() => setVisible(true)}
				>
					<ul>
						<li>
							<span>CPU：</span>
							<span>
								{type === 'proxy' && !isAuto
									? middlewareType === 'mysql'
										? obj.mysql.cpu / 4
										: 1
									: data.cpu}
								Core
							</span>
						</li>
						<li>
							<span>内存：</span>
							<span>
								{type === 'proxy' && !isAuto
									? minmax(
											obj[middlewareType].memory / 4,
											0.256,
											middlewareType === 'redis' ? 2 : 0
									  )
									: data.memory}
								Gi
							</span>
						</li>
						{data.storageClass &&
							data.storageClass !== '' &&
							!directory && (
								<li>
									<span>
										{typeof data.storageClass === 'string'
											? data.storageClass.split('/')[1]
											: (
													data.storageClass as string[]
											  )[0].split('/')[1]}
										{(typeof data.storageClass ===
											'string' &&
											data.storageClass
												.split('/')[0]
												.indexOf(',') !== -1) ||
										typeof data.storageClass ===
											'object' ? (
											<span className="available-domain">
												可用区
											</span>
										) : null}
										：
									</span>
									<span>{data.storageQuota} GB</span>
								</li>
							)}
						{type !== 'kibana' &&
							type !== 'sentinel' &&
							type !== 'proxy' &&
							!data.storageClass &&
							storageVisible &&
							!directory && (
								<li>
									<span style={{ color: '#D93026' }}>
										存储配额：未配置
									</span>
								</li>
							)}
						{customPortAPI &&
							type === 'sentinel' &&
							data.sentinelPort && (
								<>
									<li>
										<span>哨兵节点端口：</span>
										<span>{data.sentinelPort}</span>
									</li>
									<li>
										<span>哨兵节点Exporter端口：</span>
										<span>{data.sentinelExporterPort}</span>
									</li>
								</>
							)}
						{customPortAPI &&
							type === 'proxy' &&
							middlewareType === 'redis' &&
							data.predixyPort && (
								<>
									<li>
										<span>代理节点端口：</span>
										<span>{data.predixyPort}</span>
									</li>
									<li>
										<span>代理节点Exporter端口：</span>
										<span>{data.predixyExporterPort}</span>
									</li>
								</>
							)}
					</ul>
				</div>
				{visible && (
					<EditQuotaForm
						middlewareType={middlewareType}
						visible={visible}
						onCancel={() => setVisible(false)}
						onCreate={onCreate}
						data={modifyData}
						clusterId={clusterId}
						namespace={namespace}
						type={type}
						mode={mode}
						obj={obj}
						directory={directory}
						onChange={onChange}
						isActiveActive={isActiveActive}
						disabled={disabled}
						instanceType={instanceType}
						changeInstance={(value) => setInstanceType(value)}
						storageVisible={storageVisible}
						handleStorageCompute={handleStorageCompute}
						defaultStorage={defaultStorage}
						customPortAPI={customPortAPI}
					/>
				)}
			</div>
		);
	}
};
export default ModeItem;

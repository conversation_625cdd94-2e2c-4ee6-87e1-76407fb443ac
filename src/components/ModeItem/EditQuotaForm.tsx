import React, { useEffect, useState } from 'react';
import { Modal, Form, InputNumber, notification } from 'antd';
import SelectBlock from '@/components/SelectBlock';
import TableRadio from '@/pages/ServiceCatalog/components/TableRadio';
import {
	esDataList,
	instanceSpecList,
	mysqlProxyList,
	redisDataList,
	redisSentinelDataList
} from '@/utils/const';
import { modeItemProps } from './index';
import StorageQuota from '../StorageQuota';
import { minmax } from '@/utils/const';
import transUnit from '@/utils/transUnit';
interface EditQuotaFormProps extends modeItemProps {
	visible: boolean;
	onCreate: (value: any) => void;
	onCancel: () => void;
	obj: any;
	instanceType: string;
	changeInstance: (value: string) => void;
	handleStorageCompute?: (storage: any) => void;
	defaultStorage?: number;
}

export const redisNewList = [
	{
		value: '1',
		label: (
			<div>
				<p>CPU: 1Core</p>
				<p>内存: 2Gi</p>
			</div>
		)
	},
	{
		value: '2',
		label: (
			<div>
				<p>CPU: 2Core</p>
				<p>内存: 8Gi</p>
			</div>
		)
	},
	{
		value: '3',
		label: (
			<div>
				<p>CPU: 4Core</p>
				<p>内存: 16Gi</p>
			</div>
		)
	},
	{
		value: '4',
		label: (
			<div>
				<p>CPU: 8Core</p>
				<p>内存: 32Gi</p>
			</div>
		)
	},
	{
		value: '5',
		label: <div style={{ lineHeight: '28px' }}>自定义</div>
	}
];

const FormItem = Form.Item;
const EditQuotaForm = (props: EditQuotaFormProps) => {
	const {
		visible,
		onCancel,
		onCreate,
		data,
		clusterId,
		namespace,
		type,
		obj,
		mode,
		directory,
		middlewareType,
		isActiveActive,
		disabled,
		instanceType,
		changeInstance,
		storageVisible,
		handleStorageCompute,
		defaultStorage,
		customPortAPI
	} = props;
	const [instanceSpec, setInstanceSpec] = useState<string>(
		instanceType || 'General'
	);
	const [currentStorageQuota, setCurrentStorageQuota] = useState<number>(0);
	const [modifyData, setModifyData] = useState<modeItemProps['data']>(data);
	const [form] = Form.useForm();
	useEffect(() => {
		if (disabled) {
			setInstanceSpec('Customize');
		}
		if (middlewareType === 'elasticsearch' && modifyData.specId === '0') {
			setInstanceSpec('Customize');
		}
	}, []);
	useEffect(() => {
		if (data) {
			form.setFieldsValue({
				cpu: data.cpu,
				memory: data.memory,
				storageClass:
					data.storageClass === '' ? undefined : data.storageClass,
				storageQuota: data.storageQuota || null
			});
		}
	}, [data]);
	const onOk = () => {
		form.validateFields().then((values) => {
			if (type === 'proxy' && modifyData.specId === '0') {
				if (instanceSpec === 'General') {
					const value = {
						...modifyData,
						...values,
						...checkGeneral(modifyData.specId)
					};
					onCreate(value);
				} else {
					const value = {
						...modifyData,
						cpu:
							middlewareType === 'mysql'
								? obj[middlewareType]?.cpu / 4
								: 1,
						memory: obj[middlewareType]?.memory / 4,
						...values
					};
					onCreate(value);
				}
			} else {
				if (instanceSpec === 'General') {
					const value = {
						...modifyData,
						...values,
						...checkGeneral(modifyData.specId)
					};
					onCreate(value);
				} else {
					const value = { ...modifyData, ...values };
					onCreate(value);
				}
			}
		});
	};
	const dataListRender = () => {
		switch (middlewareType) {
			case 'elasticsearch':
				return esDataList;
			case 'redis':
				return redisList(redisSentinelDataList);
			case 'mysql':
				return mysqlList(mysqlProxyList);
			default:
				return [];
		}
	};
	const checkGeneral = (value: any) => {
		const cur_data = dataListRender().find(
			(item: any) => item.id === value
		);
		if (!cur_data) {
			notification.warning({
				message: '提示',
				description: '未找到当前对应的CPU和内容'
			});
			return;
		}
		const cur_cpu_number = Number(
			transUnit.removeUnit(cur_data?.cpu, ' Core')
		);
		const cur_memory_number = Number(
			transUnit.removeUnit(cur_data?.memory, ' Gi')
		);
		const data = {
			specId: value,
			cpu: cur_cpu_number,
			memory: cur_memory_number
		};
		setModifyData({
			...modifyData,
			...data
		});
		return data;
	};
	const redisList = (list: any) => {
		if (type === 'redis') {
			return redisDataList;
		} else if (type === 'proxy') {
			const newList = [
				{
					id: '0',
					cpu: `1 Core`,
					memory: `${minmax(obj['redis'].memory / 4, 0.256, 2)} Gi`
				}
			];
			return newList.concat(list.slice(2, list.length));
		} else if (type === 'sentinel') {
			return list.slice(0, 1);
		}
	};
	const mysqlList = (list: any) => {
		if (type === 'mysql') {
			return list.slice(1, list.length);
		} else if (type === 'proxy') {
			const newList = [
				{
					id: '0',
					cpu: `${obj['mysql'].cpu / 4} Core`,
					memory: `${
						obj['mysql'].memory / 4 > 0.256
							? obj['mysql'].memory / 4
							: 0.256
					} Gi`
				}
			];
			return newList.concat(
				list
					.slice(1, list.length - 3)
					.concat(list.slice(list.length - 2, list.length - 1))
			);
		}
	};
	return (
		<Modal
			title="实例配置"
			open={visible}
			onCancel={onCancel}
			onOk={onOk}
			width="auto"
			centered
		>
			<Form form={form}>
				{type !== 'mysql' && type !== 'redis' && type !== 'proxy' ? (
					<FormItem
						label="数据节点数量"
						labelAlign="left"
						rules={[
							{
								type: 'number',
								min:
									data.title === '主节点' ||
									data.title === '数据节点'
										? 3
										: 1,
								message:
									data.title === '主节点' ||
									data.title === '数据节点'
										? `${data.title}数量最小值为3`
										: `${data.title}数量最小值为1`
							}
						]}
						name="num"
						className="ant-form-name"
						initialValue={data.num}
					>
						<InputNumber
							value={data.num}
							style={{ width: '150px' }}
						/>
					</FormItem>
				) : null}
				<ul className="form-layout">
					<li className="display-flex form-li">
						<label className="form-name">
							<span>节点规格</span>
						</label>
						<div className="form-content display-flex instance-spec-content">
							<SelectBlock
								options={instanceSpecList}
								currentValue={instanceSpec}
								onCallBack={(value: any) => {
									setInstanceSpec(value);
									changeInstance(value);
								}}
							/>
							{instanceSpec === 'General' ? (
								<div
									style={{
										width: 652,
										marginTop: 16
									}}
								>
									<TableRadio
										id={modifyData.specId}
										onCallBack={(value: any) =>
											checkGeneral(value)
										}
										dataList={dataListRender()}
									/>
								</div>
							) : null}
							{instanceSpec === 'Customize' ? (
								<div className="spec-custom">
									<ul className="form-layout">
										<li className="display-flex">
											<label className="form-name">
												<span className="ne-required">
													CPU
												</span>
											</label>
											<div className="form-content">
												<FormItem
													rules={[
														{
															type: 'number',
															min: 0.1,
															message: '最小为0.1'
														},
														{
															required: true,
															message:
																'请输入自定义CPU配额，单位为Core'
														}
													]}
													name="cpu"
												>
													<InputNumber
														step={0.1}
														style={{
															width: '100%'
														}}
														placeholder="请输入自定义CPU配额，单位为Core"
													/>
												</FormItem>
											</div>
										</li>
										<li className="display-flex">
											<label className="form-name">
												<span className="ne-required">
													内存
												</span>
											</label>
											<div className="form-content">
												<FormItem
													rules={[
														{
															type: 'number',
															min: 0.1,
															message: '最小为0.1'
														},
														{
															required: true,
															message:
																'请输入自定义内存配额，单位为Core'
														}
													]}
													name="memory"
												>
													<InputNumber
														step={0.1}
														style={{
															width: '100%'
														}}
														placeholder="请输入自定义内存配额，单位为Gi"
													/>
												</FormItem>
											</div>
										</li>
									</ul>
								</div>
							) : null}
						</div>
					</li>
					{type !== 'kibana' &&
						type !== 'sentinel' &&
						type !== 'proxy' &&
						storageVisible &&
						!directory && (
							<StorageQuota
								namespace={namespace}
								clusterId={clusterId}
								isActiveActive={isActiveActive}
								form={form}
								currentStorage={
									currentStorageQuota || data.storageQuota
								}
								chartName={middlewareType}
								setCurrentStorage={setCurrentStorageQuota}
								onChange={handleStorageCompute}
								defaultStorage={defaultStorage}
							/>
						)}
					{customPortAPI && type === 'sentinel' && (
						<>
							<li
								className="display-flex"
								style={{ paddingTop: 8 }}
							>
								<label className="form-name">
									<span>哨兵节点端口</span>
								</label>
								<div className="form-content">
									<FormItem
										name="sentinelPort"
										initialValue={data.sentinelPort}
										rules={[
											{
												min: 1,
												type: 'number',
												message:
													'端口范围为1至65535的正整数'
											},
											{
												max: 65535,
												type: 'number',
												message:
													'端口范围为1至65535的正整数'
											}
										]}
									>
										<InputNumber
											style={{ width: '380px' }}
											placeholder="请输入哨兵节点端口号，默认为26379"
										/>
									</FormItem>
								</div>
							</li>
							<li
								className="display-flex"
								style={{ paddingTop: 8 }}
							>
								<label className="form-name">
									<span>哨兵节点Exporter端口</span>
								</label>
								<div className="form-content">
									<FormItem
										name="sentinelExporterPort"
										initialValue={data.sentinelExporterPort}
										rules={[
											{
												min: 1,
												type: 'number',
												message:
													'端口范围为1至65535的正整数'
											},
											{
												max: 65535,
												type: 'number',
												message:
													'端口范围为1至65535的正整数'
											}
										]}
									>
										<InputNumber
											style={{ width: '380px' }}
											placeholder="请输入哨兵节点Exporter端口号，默认为9121"
										/>
									</FormItem>
								</div>
							</li>
						</>
					)}
					{customPortAPI &&
						type === 'proxy' &&
						middlewareType === 'redis' && (
							<>
								<li
									className="display-flex"
									style={{ paddingTop: 8 }}
								>
									<label className="form-name">
										<span>代理节点端口</span>
									</label>
									<div className="form-content">
										<FormItem
											name="predixyPort"
											initialValue={data.predixyPort}
											rules={[
												{
													min: 1,
													type: 'number',
													message:
														'端口范围为1至65535的正整数'
												},
												{
													max: 65535,
													type: 'number',
													message:
														'端口范围为1至65535的正整数'
												}
											]}
										>
											<InputNumber
												style={{ width: '380px' }}
												placeholder="请输入代理节点端口号，默认为7617"
											/>
										</FormItem>
									</div>
								</li>
								<li
									className="display-flex"
									style={{ paddingTop: 8 }}
								>
									<label className="form-name">
										<span>代理节点Exporter端口</span>
									</label>
									<div className="form-content">
										<FormItem
											name="predixyExporterPort"
											initialValue={
												data.predixyExporterPort
											}
											rules={[
												{
													min: 1,
													type: 'number',
													message:
														'端口范围为1至65535的正整数'
												},
												{
													max: 65535,
													type: 'number',
													message:
														'端口范围为1至65535的正整数'
												}
											]}
										>
											<InputNumber
												style={{ width: '380px' }}
												placeholder="请输入代理节点Exporter端口号，默认为9121"
											/>
										</FormItem>
									</div>
								</li>
							</>
						)}
				</ul>
			</Form>
		</Modal>
	);
};
export default EditQuotaForm;

import React, { useEffect, useState } from 'react';
import { Progress } from 'antd';
import { formatNumber } from '@/utils/utils';
import './index.less';

interface MidProgressProps {
	fromColor: string;
	toColor: string;
	used: number | undefined;
	total: number | undefined;
	unit: string;
	toFixed?: number;
}
export default function MidProgress(props: MidProgressProps): JSX.Element {
	const { fromColor, toColor, used, total, unit, toFixed = 2 } = props;
	const [percent, setPercent] = useState<number>(0);
	useEffect(() => {
		if (used && total) {
			if (typeof used === 'number' && typeof total === 'number') {
				setPercent((used / total) * 100);
			} else {
				setPercent(0);
			}
		} else {
			if (total === 0 || used === 0) {
				setPercent(0);
			}
		}
	}, [used, total]);
	return (
		<div className="mid-progress">
			<Progress
				strokeColor={{
					from: percent >= 100 ? '#f5222d' : fromColor,
					to: percent >= 100 ? '#ff4d4f' : toColor
				}}
				percent={percent}
				size="small"
				status="active"
				format={() => {
					return (
						<span className="mid-progress-format">
							<span
								className="mid-progress-format-value"
								style={{
									color:
										percent >= 100 ? '#f5222d' : fromColor
								}}
							>
								{formatNumber(percent, 1)}
							</span>
							<span
								className="mid-progress-format-unit"
								style={{
									color: percent >= 100 ? '#ff4d4f' : toColor
								}}
							>
								%
							</span>
						</span>
					);
				}}
			/>
			<div className="mid-progress-label">{`${formatNumber(
				used,
				toFixed
			)} / ${formatNumber(total, toFixed)} ${unit}`}</div>
		</div>
	);
}

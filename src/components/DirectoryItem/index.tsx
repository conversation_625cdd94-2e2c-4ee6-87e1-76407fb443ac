import React, { useEffect, useState } from 'react';
import { StorageItem } from '@/pages/StorageManagement/storageManage';
import EditDirectory from './editDirectory';
import { getNamespaceStorages } from '@/services/storage';
import '../ModeItem/index.less';

const titleMap = {
	pgdb: '数据目录',
	pgwal: 'wal日志目录',
	pglog: 'PostgreSQL日志目录',
	pgarch: 'wal日志归档目录',
	pgextension: 'PostgreSQL插件目录',
	'redis-data': '数据目录',
	'redis-logs': '日志目录'
};
export interface modeItemProps {
	data: {
		title: string;
		disabled: boolean;
		hostPath: string;
		mountPath: string;
		volumeSize: number;
		name: string;
		storageClass: string | string[];
		switch?: boolean;
	};
	mode?: string;
	clusterId: string;
	namespace?: string;
	type: string;
	isActiveActive?: boolean;
	onChange: (value: modeItemProps['data']) => void;
	middlewareType?: string;
	readOnly?: boolean;
	disabled?: boolean;
	handleDirectory?: (value: number) => void;
}
const ModeItem = (props: modeItemProps): JSX.Element => {
	const {
		data,
		clusterId,
		namespace,
		type,
		mode,
		onChange,
		isActiveActive,
		disabled,
		readOnly,
		handleDirectory
	} = props;
	const [modifyData, setModifyData] = useState<modeItemProps['data']>(data);
	const [storageClassList, setStorageClassList] = useState<StorageItem[]>([]);
	const [visible, setVisible] = useState<boolean>(false);

	useEffect(() => {
		if (clusterId && namespace) {
			getNamespaceStorages({
				clusterId,
				namespace
			}).then((res) => {
				if (res.success) {
					if (res.data.length > 0) {
						isActiveActive
							? setStorageClassList(
									res.data.filter(
										(item: any) =>
											item.storageClassList[0]
												.volumeType === 'LocalPath'
									)
							  )
							: setStorageClassList(
									res.data
										.filter(
											(item: any) => !item.isActiveActive
										)
										.filter(
											(item: any) =>
												item.storageClassList[0]
													.volumeType === 'LocalPath'
										)
							  );
					} else {
						setStorageClassList([]);
					}
				}
			});
		}
	}, [clusterId, namespace]);
	const onCreate = (value: any) => {
		const valueTemp = {
			...modifyData,
			...value
		};
		onChange(valueTemp);
		setModifyData(valueTemp);
		setVisible(false);
	};
	useEffect(() => {
		setModifyData(data);
		if (storageClassList.length) {
			const result: any = storageClassList.find(
				(item) => item.storageClassList[0].name === data.storageClass
			);
			handleDirectory &&
				handleDirectory(
					result?.quota.request - result?.quota.used || 0
				);
		}
	}, [data, storageClassList]);
	useEffect(() => {
		onChange(modifyData);
	}, [modifyData]);
	const inputChange = (value: any) => {
		setModifyData({
			...modifyData
		});
	};
	return (
		<div className="mode-item-box">
			<div className="mode-item-title">
				<span
					className={
						type === 'pgextension' || type === 'redis-logs'
							? 'align'
							: 'align ne-required'
					}
				>
					{data.title || titleMap[data.name]}
				</span>
			</div>
			<div
				className="mode-item-data"
				onClick={() => !readOnly && setVisible(true)}
			>
				<ul>
					{data.switch === false ? (
						<li>
							<span
								style={{
									color: '#d93026',
									display:
										(type === 'pgarch' ||
											type === 'pgextension' ||
											type === 'redis-logs') &&
										data.switch === false
											? 'initial'
											: 'none'
								}}
							>
								未启用
							</span>
						</li>
					) : (
						<>
							<li>
								<span>宿主机目录：</span>
								<span>{data.hostPath}</span>
							</li>
							<li>
								<span>容器内目录：</span>
								<span>{data.mountPath}</span>
							</li>
							{data.storageClass ? (
								<li>
									<span>存储：</span>
									<span>
										{
											storageClassList.find(
												(item) =>
													item.storageClassList[0]
														.name ===
													data.storageClass
											)?.aliasName
										}
									</span>
								</li>
							) : (
								<li>
									<span style={{ color: '#D93026' }}>
										存储：未配置
									</span>
								</li>
							)}
							<li>
								<span>存储大小：</span>
								<span>{data.volumeSize} GB</span>
							</li>
						</>
					)}
				</ul>
			</div>
			{visible && (
				<EditDirectory
					visible={visible}
					onCancel={() => setVisible(false)}
					onCreate={onCreate}
					data={modifyData}
					clusterId={clusterId}
					namespace={namespace}
					type={type}
					mode={mode}
					onChange={onChange}
					inputChange={inputChange}
					disabled={disabled}
					handleDirectory={handleDirectory}
					storageClassList={storageClassList}
				/>
			)}
		</div>
	);
};
export default ModeItem;

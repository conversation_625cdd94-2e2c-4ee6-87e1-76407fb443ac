import React from 'react';
import { formBlockProps } from './formBlock';
import './index.less';
export default function FormBlock(props: formBlockProps): JSX.Element {
	const { title, className = '', style = {}, children } = props;
	return (
		<div className={`form-block ${className}`} style={{ ...style }}>
			{typeof title === 'string' ? (
				<p className="title">{title}</p>
			) : (
				<div className="title">{title}</div>
			)}
			{children ? children : <div className="form-block-none"></div>}
		</div>
	);
}

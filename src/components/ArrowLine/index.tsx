import { CaretRightOutlined } from '@ant-design/icons';
import React from 'react';
import './index.less';

interface ArrowLineProps {
	label?: string;
	style?: any;
	iconColor?: string;
	color?: string;
}
export default function ArrowLine(props: ArrowLineProps): JSX.Element {
	const { label, style, iconColor, color } = props;
	return (
		<div className="arrow-line-content" style={style}>
			<div
				className="arrow-line_line"
				style={{ borderColor: color || '#8a8a8a' }}
			></div>
			<div className="arrow-line_arrow">
				<CaretRightOutlined
					style={{
						color: iconColor || '#8a8a8a',
						fontSize: 16,
						marginLeft: '-5px',
						marginTop: '3.5px'
					}}
				/>
			</div>
			{label && (
				<div className="arrow-line-label-box">
					<span className="arrow-line-label">{label}</span>
				</div>
			)}
		</div>
	);
}

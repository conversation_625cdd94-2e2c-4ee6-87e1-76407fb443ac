.tag-box {
	width: calc(100% - 52px);
	margin-left: 52px;
	height: 100px;
	overflow-y: auto;
	background: @black-9;
	margin-top: @padding-sm;
	padding: @padding-sm;
	border-radius: @border-radius-lg;
}
.ingress-type-content {
	display: flex;
	justify-content: center;
	column-gap: 60px;
	.ingress-type-box {
		width: 100px;
		height: 100px;
		display: flex;
		border: 1px solid @border-color;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		&:hover{
			color: @blue-6;
			border-color: @blue-6;
		}
	}
}
.ingress-type-box-active {
	color: @blue-6;
	border-color: @blue-6 !important;
}
.conflict-port-checkbox {
	margin-left: 8px;
}
.vip-content {
	display: flex;
	.vip-content-switch {
		height: 32px;
		display: flex;
		align-items: center;
		margin-right: 8px;
	}
}
.edit-yaml-page-header {
	.ant-page-header-heading {
		.ant-page-header-heading-left {
			.ant-page-header-back {
				font-size: @font-5;
				line-height: @line-height-5;
			}
			.ant-page-header-heading-title {
				font-size: @font-5;
				line-height: @line-height-5;
			}
		}
	}
}


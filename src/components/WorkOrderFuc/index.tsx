import React from 'react';
import { notification, Modal } from 'antd';
import moment from 'moment';
import { getOrdersByOperatorId } from '@/services/project';
import storage from '@/utils/storage';
import {
	NeedThirdPartyOperators,
	maintenanceTypes,
	maintenances
} from '@/utils/const';
import { executeOrder } from '@/services/workOrder';
const { info } = Modal;
export const WorkOrderFuc = (
	func: any,
	locked: string,
	middlewareName: string,
	operatorId: string,
	history: any,
	type: string,
	name: string,
	aliasName: string,
	clusterId?: string,
	namespace?: string
) => {
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const role = JSON.parse(storage.getLocal('role'));
	const ThirdPartyOACloseAPI =
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'thirdPartyOAClose')?.enabled ??
		true;
	storage.removeSession('currentOrderId');
	if (locked === 'unlocked' || locked === 'locking') {
		// * 判断当前服务是否已解锁 或处于 锁定的审批中时
		func();
		return;
	}
	if (role.isAdmin) {
		// * 判断当前用户是否为超级管理员
		func();
		return;
	}
	const curProjectAuth = role?.userRoleList.find(
		(item: any) => item.projectId === projectId
	);
	if (curProjectAuth) {
		if (curProjectAuth.weight === 3 || curProjectAuth.weight === 2) {
			// * 判断当前项目下用户的权限为组织管理员或者项目管理员，可以直接进行操作
			func();
			return;
		}
	} else {
		if (!role.dba) {
			// * 当用户找不到当前项目的权限且不是dba类型的角色时
			notification.warning({
				message: '提醒',
				description: '未找到该用户下当前项目的权限，请刷新页面！'
			});
			return;
		}
	}
	if (locked === 'locked' || locked === 'unlocking') {
		getOrdersByOperatorId({
			organId,
			projectId,
			operatorId,
			middlewareName,
			middlewareType: name,
			clusterId,
			namespace
		}).then((res: any) => {
			if (res.success) {
				if (res.data.available) {
					// * 当前操作是否需要审批
					if (!ThirdPartyOACloseAPI) {
						// * 当前操作是否需要使用马上办验证
						const third_party_flag = NeedThirdPartyOperators.map(
							(item) => item.operatorId
						).includes(operatorId);
						if (third_party_flag) {
							if (!role?.thirdPartyOAUserInfo?.thirdPartyId) {
								Modal.info({
									title: '未绑定马上办账号',
									content:
										'当前平台用户未绑定马上办账号，无法发起部分工单，请联系管理员绑定账号！',
									okText: '我知道了'
								});
								return;
							}
						}
					}
					if (res.data.orderId) {
						// * 当前操作是否存在工单
						if (res.data.orderStatus === 'waitExecuted') {
							if (res.data.startTime && res.data.endTime) {
								const currentTime = moment().unix();
								const startTime = moment(
									res.data.startTime
								).unix();
								const endTime = moment(res.data.endTime).unix();
								if (
									startTime <= currentTime &&
									currentTime <= endTime
								) {
									storage.setSession(
										'currentOrderId',
										res.data.orderId
									);
									// * 当前时间在范围内
									func();
									return;
								} else {
									info({
										title: '工单已存在',
										content:
											'您已存在正在进行的工单，请耐心等待审批！'
									});
								}
							}
						} else {
							info({
								title: '工单已存在',
								content:
									'您已存在正在进行的工单，请耐心等待审批！'
							});
						}
					} else {
						let maintenanceType;
						Object.keys(maintenanceTypes).map((key) => {
							maintenanceTypes[key].map((item: string) => {
								if (maintenances[item] === operatorId) {
									maintenanceType = key;
								}
							});
						});
						history.push(
							`/project/${type}/${name}/${aliasName}/workOrder/${maintenanceType}/${middlewareName}/${operatorId}`
						);
					}
				} else {
					func();
					return;
				}
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	}
};
export const ExecuteOrderFuc = async (fun?: any) => {
	const storageOrderId = storage.getSession('currentOrderId');
	if (storageOrderId) {
		const res = await executeOrder({
			orderId: storageOrderId
		});
		if (res.success) {
			fun && fun();
			return;
		}
	} else {
		fun && fun();
		return;
	}
};

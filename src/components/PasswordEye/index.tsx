import React, { useState } from 'react';
import { EyeInvisibleOutlined, EyeOutlined } from '@ant-design/icons';

interface PasswordEyeProps {
	value: string | null;
}
export default function PasswordEye(props: PasswordEyeProps): JSX.Element {
	const { value } = props;
	const [visible, setVisible] = useState<boolean>(false);
	return (
		<div style={{ width: '100%', display: 'flex', alignItems: 'center' }}>
			<div
				className="text-overflow"
				style={{ marginRight: 8, maxWidth: '80%' }}
				title={visible ? value || '' : ''}
			>
				{visible ? value : '********'}
			</div>
			{visible ? (
				<EyeOutlined
					style={{ cursor: 'pointer' }}
					onClick={() => {
						setVisible(!visible);
					}}
				/>
			) : (
				<EyeInvisibleOutlined
					style={{ cursor: 'pointer' }}
					onClick={() => {
						setVisible(!visible);
					}}
				/>
			)}
		</div>
	);
}

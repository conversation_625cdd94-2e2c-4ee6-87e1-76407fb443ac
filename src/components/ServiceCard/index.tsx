import React from 'react';
import { controlledOperationDisabled, formatNumber } from '@/utils/utils';
import { Badge, notification } from 'antd';
import './index.less';
interface ServiceCardProps {
	lock: string;
	type: string;
	aliasName: string;
	middlewareName: string;
	status: string;
	cpu: number;
	memory: number;
	num: number;
	disabled?: boolean;
	onMidClick?: () => void;
	onActionClick?: () => void;
	podLabel?: string;
	color?: string;
	isPod?: boolean;
	ifIncrement?: boolean;
	enableScale?: boolean;
}
// ! ifIncrement 字段来源未定
export default function ServiceCard(props: ServiceCardProps): JSX.Element {
	const {
		aliasName,
		type,
		lock,
		status,
		cpu,
		memory,
		num,
		onMidClick,
		onActionClick,
		middlewareName,
		podLabel,
		color,
		isPod,
		ifIncrement,
		enableScale
	} = props;
	const handleClick = (type: string) => {
		if (controlledOperationDisabled('maintenance', lock)) return;
		if (
			(status !== 'Running' && status !== 'running') ||
			(enableScale && ifIncrement !== true)
		) {
			return;
		}
		if (type === 'vertical') {
			// * 修改规格 / 纵向扩容
			onActionClick && onActionClick();
		} else {
			// * 横向扩容
			onMidClick && onMidClick();
		}
	};
	const lateralStatusRender: (type: 'style' | 'title') => boolean | string = (
		type: 'style' | 'title'
	) => {
		if (type === 'style') {
			if (status !== 'Running' && status !== 'running') return true;
			if (enableScale && ifIncrement !== true) return true;
			if (controlledOperationDisabled('maintenance', lock)) return true;
			return false;
		} else {
			if (status !== 'Running' && status !== 'running')
				return '该服务运行异常，无法对该服务节点进行横向扩容';
			if (enableScale && ifIncrement !== true)
				return '当前最近一次增量备份失败或未完成，继续基于备份进行横向扩容操作，可能会导致从节点异常，请先修复增量备份失败的问题';
			return '';
		}
	};
	const statusRender = () => {
		switch (status) {
			case 'Running':
				return <Badge status="success" text={status} />;
			case 'running':
				return <Badge status="success" text={status} />;
			default:
				return <Badge status="error" text={status} />;
		}
	};
	return (
		<div className="service-card-content">
			<div
				className="service-card-top"
				style={color ? { borderBottomColor: color } : {}}
			>
				<div className="service-card-top-label-content">
					<div
						className="service-card-top-label"
						title={isPod ? middlewareName : aliasName || type}
						style={color ? { backgroundColor: color } : {}}
					>
						{type?.substring(0, 1).toUpperCase()}
					</div>
					<div className="text-overflow service-card-top-title-content">
						{middlewareName}
					</div>
				</div>
				<div className="text-overflow service-card-top-status">
					{statusRender()}
				</div>
			</div>
			<div className="service-card-bottom">
				<div className="service-card-statistic-content">
					<div className="service-card-statistic-title">CPU</div>
					<div className="service-card-statistic-value">
						{formatNumber(cpu, 1)}
						<span className="service-card-statistic-value-unit">
							Core
						</span>
					</div>
				</div>
				<div className="service-card-statistic-content">
					<div className="service-card-statistic-title">内存</div>
					<div className="service-card-statistic-value">
						{formatNumber(memory, 1)}
						<span className="service-card-statistic-value-unit">
							GB
						</span>
					</div>
				</div>
				{onActionClick && (
					<div
						className="service-card-action-content"
						style={
							(status !== 'Running' && status !== 'running') ||
							controlledOperationDisabled('maintenance', lock)
								? { color: '#c0c6cc', cursor: 'not-allowed' }
								: {}
						}
						onClick={() => handleClick('vertical')}
						title={
							status !== 'Running' && status !== 'running'
								? '该服务运行异常，无法修改该服务节点规格'
								: ''
						}
					>
						修改规格
					</div>
				)}
			</div>
			<div
				className="service-card-middle"
				style={onMidClick ? {} : { justifyContent: 'center' }}
			>
				<div className="service-card-pod-content">
					<span className="service-card-pod-label">
						{podLabel ? podLabel : '从节点数'}
					</span>
					<span className="service-card-pod-value">{num}</span>
				</div>
				{type !== 'Kibana' && onMidClick && (
					<div
						className="service-card-pod-action"
						style={
							lateralStatusRender('style')
								? { color: '#c0c6cc', cursor: 'not-allowed' }
								: {}
						}
						title={lateralStatusRender('title') as string}
						onClick={() => handleClick('lateral')}
					>
						横向扩容
					</div>
				)}
			</div>
		</div>
	);
}

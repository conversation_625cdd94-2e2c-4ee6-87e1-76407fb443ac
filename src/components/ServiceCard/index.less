.service-card-content {
	width: 250px;
	height: 130px;
	background: @white;
	box-shadow: 0px 5px 16px rgba(0, 0, 0, 0.04);
	border-radius: 4px;
	.service-card-top {
		display: flex;
		height: 33px;
		border-bottom: 1px solid @orange-base;
		justify-content: space-between;
		align-items: center;
		.service-card-top-label-content {
			width: 147px;
			display: flex;
			align-items: center;
			gap: 8px;
			.service-card-top-label {
				width: 33px;
				height: 33px;
				background-color: @orange-base;
				color: @white;
				font-size: @font-3;
				text-align: center;
				line-height: 33px;
				cursor: pointer;
				border-radius: 4px 0px 0px 0px;
			}
			.service-card-top-title-content {
				width: 105px;
			}
		}
		.service-card-top-status {
			margin-right: @margin / 2;
		}
	}
	.service-card-middle {
		line-height: 36px;
		height: 36px;
		// border-bottom: 1px solid @black-6;
		display: flex;
		align-items: center;
		text-align: center;
		.service-card-pod-content {
			flex-grow: 2;
			.service-card-pod-label {
				font-weight: @font-weight-sm;
				font-size: @font-2 - 2px;
				line-height: @line-height-2 - 4px;
				color: @black-5;
			}
			.service-card-pod-value {
				font-weight: @font-weight;
				font-size: @font-3;
				line-height: @line-height-3;
				margin-left: 8px;
			}
		}
		.service-card-pod-action {
			cursor: pointer;
			position: relative;
			flex: 0 1 80px;
			&::before {
				content: '';
				position: absolute;
				top: 7px;
				left: 2px;
				width: 1px;
				height: 23px;
				border-right: 1px solid @black-6;
			}
			&:hover {
				color: @primary-color;
			}
		}
	}
	.service-card-bottom {
		padding: 4px;
		height: 60px;
		display: flex;
		border-bottom: 1px solid @black-6;
		align-items: center;
		.service-card-statistic-content {
			flex: 1 0 auto;
			height: 52px;
			display: flex;
			flex-direction: column;
			align-items: center;
			position: relative;
			&:nth-child(1):after {
				content: '';
				position: absolute;
				top: 10px;
				right: 1px;
				width: 1px;
				height: 30px;
				border-right: 1px solid @black-6;
			}
			.service-card-statistic-value-unit,
			.service-card-statistic-title {
				font-size: @font-1;
				color: @black-5;
				line-height: @line-height-1;
			}
			.service-card-statistic-value {
				font-size: @font-4;
			}
		}
		.service-card-action-content {
			flex: 0 1 72px;
			text-align: center;
			position: relative;
			&:hover{
				color: @primary-color;
				cursor: pointer;
			}
			&::before {
				content: '';
				position: absolute;
				top: -7px;
				left: -2px;
				width: 1px;
				height: 30px;
				border-right: 1px solid @black-6;
			}
		}
	}
}

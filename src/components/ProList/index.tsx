import React, { useEffect, useState } from 'react';
import { Space, Input, Button, Empty } from 'antd';
import { ProListProps } from './proList';
import { ReloadOutlined } from '@ant-design/icons';
import './index.less';

const { Search } = Input;
export default function ProList(props: ProListProps): JSX.Element {
	const {
		operation,
		showRefresh,
		onRefresh,
		search,
		refreshDisabled,
		children
	} = props;
	const [showHeader, setShowHeader] = useState<boolean>(false);
	useEffect(() => {
		if (operation || showRefresh || onRefresh || search) {
			setShowHeader(true);
		} else {
			setShowHeader(false);
		}
	}, [props]);
	const childrenRender = () => {
		if (typeof children === 'object' && !(children instanceof Array)) {
			return children;
		} else if (children?.length > 0) {
			return children;
		} else {
			return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />;
		}
	};
	return (
		<div className="zeus-pro-list-content">
			{showHeader && (
				<div className="zeus-pro-list-header">
					<div className="zeus-pro-list-left">
						<Space>
							{operation?.primary}
							{search && <Search allowClear {...search} />}
						</Space>
					</div>
					<div className="zeus-pro-list-right">
						<Space>
							{operation?.secondary}
							{showRefresh && (
								<Button
									disabled={refreshDisabled}
									type="default"
									icon={<ReloadOutlined />}
									onClick={onRefresh}
								/>
							)}
						</Space>
					</div>
				</div>
			)}
			<div className="zeus-pro-list-body">{childrenRender()}</div>
		</div>
	);
}

@cpu: cpu;
@memory: memory;
@storage: storage;
#midSlider@{storage},
#midSlider@{memory},
#midSlider@{cpu} {
	.slider-usage-tooltip {
		position: absolute;
		top: 4px;
		left: 6px;
		width: calc(100% - 4px);
		height: 6px;
		border-radius: 2px;
		background-color: @black-5;
		cursor: pointer;
		z-index: 1;
	}
	.ant-slider {
		height: 14px;
		.ant-slider-rail {
			height: 6px;
		}
		.ant-slider-track {
			height: 6px;
			background-color: #5C0EDF;
		}
		.ant-slider-step {
			height: 6px;
			.ant-slider-dot {
				&:nth-child(1) {
					visibility: hidden;
				}
				&:nth-last-child(1) {
					visibility: hidden;
				}
				top: -1px;
				z-index: 2;
			}
			.ant-slider-dot-active {
				border-color: #5C0EDF;
			}
		}
		.ant-slider-handle {
			margin-top: -4px;
			border: solid 2px #5C0EDF;
			z-index: 2;
		}
	}
}
#midSlider@{memory} {
	.ant-slider {
		.ant-slider-track {
			background-color: #1AC1C4;
		}
		.ant-slider-step {
			.ant-slider-dot-active {
				border-color: #1AC1C4;
			}
		}
		.ant-slider-handle {
			border: solid 2px #1AC1C4;
		}
	}
}
#midSlider@{storage} {
	.ant-slider {
		.ant-slider-track {
			background-color: #226EE7;
		}
		.ant-slider-step {
			.ant-slider-dot-active {
				border-color: #226EE7;
			}
		}
		.ant-slider-handle {
			border: solid 2px #226EE7;
		}
	}
}

import React, { useState, useEffect } from 'react';
import { Slider, Tooltip } from 'antd';
import { formatNumber } from '@/utils/utils';
import './index.less';

interface MidSliderProps {
	min?: number;
	max: number;
	used: number;
	tooltip: string;
	value?: number | null;
	type: string;
	setValue: (value: any) => void;
	style?: React.CSSProperties;
	[propsName: string]: any;
}
export default function MidSlider(props: MidSliderProps): JSX.Element {
	const { used, max, type, tooltip, value, setValue, style, ...orgProps } =
		props;
	const [toFixed, setToFixed] = useState<number>(1);
	useEffect(() => {
		if (type === 'storage') {
			setToFixed(0);
		} else {
			setToFixed(1);
		}
	}, [type]);
	const onChange = (value: number | null) => {
		if ((value || 0) < used) {
			setValue(used);
			return;
		}
		setValue(value);
	};
	return (
		<div
			id={`midSlider${type}`}
			style={
				used !== 0 && max !== 0
					? { position: 'relative', ...style }
					: style
			}
		>
			<Slider
				{...orgProps}
				value={(value || 0) < used ? used || 0 : value || used}
				onChange={onChange}
				max={max}
				marks={{
					0: '0',
					[used > max ? max : used]: formatNumber(
						used > max ? max : used,
						toFixed
					),
					[max]: formatNumber(max || 0, toFixed)
				}}
				tooltip={{ formatter: (value: any) => formatNumber(value, 1) }}
			/>
			{used !== 0 && max !== 0 && (
				<Tooltip title={tooltip}>
					<div
						className="slider-usage-tooltip"
						style={{
							width: `calc(${
								(used / max >= 1
									? 1
									: Number(formatNumber(used, toFixed)) /
									  Number(formatNumber(max, toFixed))) * 100
							}% - 4px)`
						}}
					></div>
				</Tooltip>
			)}
		</div>
	);
}

import React, { useState, useEffect } from 'react';
import {
	<PERSON><PERSON>,
	<PERSON>,
	Divider,
	Drawer,
	Form,
	Row,
	Select,
	Space,
	Switch,
	notification
} from 'antd';
import { IconFont } from '../IconFont';
import { getClusters } from '@/services/common';
import { clusterType } from '@/types';
import { installMiddleware } from '@/services/repository';
import Affinity from '@/pages/PublishService/components/Affinity';
import Toleration from '@/pages/PublishService/components/Toleration';

export interface SendDataProps {
	clusterId: string;
	chartName: string | undefined;
	chartVersion?: string;
	type: string;
}
interface installFormProps {
	visible: boolean;
	clusterId: string;
	chartName: string;
	chartVersion: string;
	onCancel: () => void;
	onRefresh: () => void;
}
const OperatorInstallForm = (props: installFormProps) => {
	const { visible, onCancel, clusterId, onRefresh, chartName, chartVersion } =
		props;
	const [type, setType] = useState<string>('high');
	const [clusterList, setClusterList] = useState<clusterType[]>([]);
	const [currentCluster, setCurrentCluster] = useState<string>();
	const [loading, setLoading] = useState<boolean>(false);
	const [form] = Form.useForm();
	const onOk = async () => {
		await form.validateFields();
		const values = form.getFieldsValue();
		const sendData = {
			clusterId,
			type: type,
			chartName,
			chartVersion,
			nodeAffinity: values.nodeAffinity,
			tolerations: values.tolerations
		};
		setLoading(true);
		installMiddleware(sendData)
			.then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '中间件安装升级成功，3秒后刷新数据'
					});
					onRefresh();
					onCancel();
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	useEffect(() => {
		getClusters().then((res) => {
			if (res.success) {
				setClusterList(res.data);
				setCurrentCluster(clusterId);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	}, [clusterId]);
	return (
		<Drawer
			title="控制器安装"
			open={visible}
			onClose={onCancel}
			// onOk={onOk}
			// okButtonProps={{ loading }}
			width={800}
		>
			<div className="install-title-content">
				<div className="install-title-name">目标集群</div>
			</div>
			<div>
				<label
					style={{
						width: 120,
						marginLeft: 10,
						display: 'inline-block'
					}}
				>
					集群选择
				</label>
				<Select
					disabled
					value={currentCluster}
					onChange={(value) => setCurrentCluster(value)}
					dropdownMatchSelectWidth={false}
					style={{ width: 'calc(100% - 146px)' }}
				>
					{clusterList.map((item: clusterType) => {
						return (
							<Select.Option value={item.id} key={item.id}>
								{item.nickname}
							</Select.Option>
						);
					})}
				</Select>
			</div>
			<div className="install-title-content">
				<div className="install-title-name">选择安装规格</div>
			</div>
			<p className="install-subtitle">
				依据您集群资源的富余情况进行灵活选择
			</p>
			<div
				className="install-item"
				style={type === 'simple' ? { border: '1px solid #0064C8' } : {}}
				onClick={() => setType('simple')}
			>
				<div>
					<IconFont
						type="icon-renwushili"
						style={
							type === 'simple'
								? { width: 80, fontSize: 80, color: '#0064C8' }
								: { width: 80, fontSize: 80, color: '#666666' }
						}
					/>
				</div>
				<div className="install-item-info">
					<h2>单实例版</h2>
					<p>资源占用少，保证安装后，该中间件可用，但是不稳定</p>
					<IconFont
						type="icon-xuanzhong"
						style={
							type === 'simple'
								? { position: 'absolute', right: 0, bottom: 0 }
								: { visibility: 'hidden' }
						}
					/>
				</div>
			</div>
			<div
				className="install-item"
				style={type === 'high' ? { border: '1px solid #0064C8' } : {}}
				onClick={() => setType('high')}
			>
				<div>
					<IconFont
						type="icon-gaokeyong"
						style={
							type === 'high'
								? { width: 80, fontSize: 80, color: '#0064C8' }
								: { width: 80, fontSize: 80, color: '#666666' }
						}
					/>
				</div>
				<div className="install-item-info">
					<h2>高可用版（推荐）</h2>
					<p>资源占用相对多，保证安装后，该中间件可用，且稳定</p>
					<IconFont
						type="icon-xuanzhong"
						style={
							type === 'high'
								? { position: 'absolute', right: 0, bottom: 0 }
								: { visibility: 'hidden' }
						}
					/>
				</div>
			</div>
			<h2>高级配置</h2>
			<Form form={form}>
				<Form.Item
					labelAlign="left"
					colon={false}
					name="schedulePolicy"
					label="调度策略"
					initialValue={true}
					valuePropName="checked"
				>
					<Switch />
				</Form.Item>
				<Row>
					<Col offset={2}>
						<Form.Item noStyle shouldUpdate>
							{({ getFieldValue }) => {
								if (getFieldValue('schedulePolicy') === true) {
									return (
										<>
											<Affinity
												name="nodeAffinity"
												label="主机亲和"
												isAnti={false}
												disabled={false}
												clusterId={clusterId}
											/>
											<Toleration
												name="tolerations"
												label="主机容忍"
												clusterId={clusterId}
											/>
										</>
									);
								}
							}}
						</Form.Item>
					</Col>
				</Row>
			</Form>
			<Divider />
			<Space>
				<Button type="default" onClick={onCancel}>
					取消
				</Button>
				<Button type="primary" onClick={onOk} loading={loading}>
					确定
				</Button>
			</Space>
		</Drawer>
	);
};
export default OperatorInstallForm;

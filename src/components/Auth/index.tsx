import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { StoreState } from '@/types';
import { findInNestedArray } from '@/utils/utils';

function Auth(props: any) {
	const { code, children, buttonList, allMenu } = props;
	const [show, setShow] = useState(false);
	const hasWebpage = window.location.href.includes('webpage');

	useEffect(() => {
		if (code) {
			// * 流程管理resourceType是菜单，特殊处理
			if (code === 'flowManagement') {
				const topMenu = allMenu?.find(
					(item: any) => item.name === 'top'
				)?.subMenu;
				if (
					topMenu?.find((item: any) => item.name === 'flowManagement')
				) {
					setShow(true);
				} else {
					setShow(false);
				}
			} else {
				setShow(findInNestedArray(buttonList, code, true));
			}
		} else {
			setShow(true);
		}
		if (hasWebpage) {
			setShow(true);
		}
	}, [buttonList, allMenu]);

	if (show) {
		return <>{children}</>;
	} else {
		return <></>;
	}
}

export default connect((state: StoreState) => ({
	allMenu: state.auth.allMenu,
	buttonList: state.auth.buttonList
}))(Auth);

#storage-slider {
	.storage-slider-content {
		width: 100%;
		height: 100%;
		.ant-slider-vertical {
			width: 130px;
			margin-left: 60px;
			box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.07);
		}
		.ant-slider-rail {
			width: 123px;
			background-color: @white;
		}
		.ant-slider-track {
			width: 122px;
			background-color: @primary-color;
		}
		.ant-slider-step {
			width: 122px;
		}
		.ant-slider-handle {
			margin-left: 55px;
			border: solid 2px @primary-color;
		}
	}
	.storage-slider-input-content {
		display: flex;
		align-items: flex-end;
		justify-content: center;
		color: rgba(0, 0, 0, 0.2);
		border-bottom: 1px solid;
		border-image: linear-gradient(90deg, rgba(98, 98, 94, 0), rgba(78, 78, 74, 0.16), rgba(70, 70, 66, 0.16), rgba(51, 51, 48, 0)) 1 1;
		#storage-slider-input {
			border: none;
			text-align: center;
			width: 55px;
			font-size: 18px;
			font-weight: @font-weight;
			color: @primary-color;
		}
	}
	.storage-slider-tips {
		width: 267px;
		font-size: @font-1;
		font-weight: 400;
		color: rgba(51,51,51,0.5);
		line-height: @line-height-1;
		margin-top: @margin-lg;
	}
}

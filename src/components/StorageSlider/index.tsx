import React, { useState } from 'react';
import { Slider, notification } from 'antd';
import { formatNumber } from '@/utils/utils';
import './index.less';
interface StorageSliderProps {
	min: number;
	max: number;
	residue: number;
	storageValue?: number;
	handleChange?: (value: any) => void;
}
const style: React.CSSProperties = {
	display: 'inline-block',
	height: 140,
	marginLeft: 60,
	marginTop: 24
};
export default function StorageSlider(props: StorageSliderProps): JSX.Element {
	const { min, handleChange, max, residue, storageValue } = props;
	const [value, setValue] = useState<number>(storageValue || min);
	const handleInputChange = (e: any) => {
		const t = Number(e.target?.value);
		// * v2.1 去除该逻辑
		// if (storageValue) {
		// 	if (t > storageValue) {
		// 		notification.error({
		// 			message: '错误',
		// 			description: '重新扩容只支持将扩容值缩小！'
		// 		});
		// 		return;
		// 	}
		// }
		if (t > max) {
			setValue(max);
			handleChange && handleChange(max);
			return;
		}
		if (!isNaN(t)) {
			setValue(t);
			handleChange && handleChange(t);
			return;
		}
	};
	const onBlur = () => {
		if (value < min) {
			setValue(min);
			handleChange && handleChange(min);
			return;
		}
		if (value > max) {
			setValue(max);
			handleChange && handleChange(max);
			return;
		}
	};
	const onChange = (value: any) => {
		if (min <= value && value <= max) {
			setValue(value as number);
			handleChange && handleChange(value as number);
		}
	};
	const onAfterChange = (value: any) => {
		console.log('onAfterChange: ', value);
	};
	return (
		<div id="storage-slider" style={style}>
			<div className="storage-slider-content">
				<Slider
					vertical
					value={value}
					onChange={onChange}
					max={max}
					tooltip={{ open: false }}
					disabled={!handleChange}
					onAfterChange={onAfterChange}
				/>
			</div>
			<div className="storage-slider-input-content">
				<input
					id="storage-slider-input"
					value={value}
					onChange={handleInputChange}
					onBlur={onBlur}
					disabled={!handleChange}
				/>
				<span>GB</span>
			</div>
			<div className="storage-slider-tips">
				当前命名空间剩余可分配的存储配额为：{formatNumber(residue)}GB
			</div>
		</div>
	);
}

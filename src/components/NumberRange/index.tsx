import React, { useState } from 'react';
import './index.less';
interface NumberRangeProps {
	unit: string;
	numberRange: (value: string[]) => void;
	style?: React.CSSProperties;
	leftInputConfig?: React.InputHTMLAttributes<HTMLInputElement>;
	rightInputConfig?: React.InputHTMLAttributes<HTMLInputElement>;
}

export default function NumberRange(props: NumberRangeProps): JSX.Element {
	const {
		unit,
		numberRange,
		style,
		leftInputConfig = {},
		rightInputConfig = {}
	} = props;
	const [start, setStart] = useState<string>('');
	const [end, setEnd] = useState<string>('');

	// 限制输入值
	function limitInput(value: string, type: string) {
		if (['', undefined, null].includes(value)) return value;
		let config,
			res = value;
		if (type === 'start') config = leftInputConfig;
		if (type === 'end') config = rightInputConfig;
		const min = config?.min,
			max = config?.max;
		if (min && +value < +min) {
			res = min + '';
		}
		if (max && +value > +max) {
			res = max + '';
		}
		return res;
	}

	const handleChange = (
		e: React.ChangeEvent<HTMLInputElement>,
		type: string
	) => {
		const tv = +e.target.value;
		let value = (tv && tv > 0) || tv === 0 ? e.target.value : '0';
		value = limitInput(value, type);
		switch (type) {
			case 'start':
				setStart(value);
				break;
			case 'end':
				setEnd(value);
		}
	};
	const onBlur = () => {
		numberRange([start, end]);
	};

	return (
		<div id="number-range" style={style}>
			<input
				className="number-range-input left"
				type="number"
				value={start}
				onChange={(e) => handleChange(e, 'start')}
				onBlur={onBlur}
			></input>
			<div className="number-range-connect">&nbsp; ~ &nbsp;</div>
			<input
				className="number-range-input right"
				type="number"
				value={end}
				onChange={(e) => handleChange(e, 'end')}
				onBlur={onBlur}
			></input>
			{unit && <div className="number-range-unit">{unit}</div>}
		</div>
	);
}

import React, { useEffect, useState } from 'react';
import { Form, Select, InputNumber } from 'antd';
import { getNamespaceStorages } from '@/services/storage';
import { StorageItem } from '@/pages/StorageManagement/storageManage';
import { StorageQuotaProps } from './storageQuota';
import { formatNumber } from '@/utils/utils';

const FormItem = Form.Item;
// * 目前命名空间需要分配存储
export default function StorageQuota(props: StorageQuotaProps): JSX.Element {
	const {
		clusterId,
		type,
		isActiveActive,
		namespace,
		form,
		currentStorage,
		setCurrentStorage,
		onChange,
		chartName,
		defaultStorage
	} = props;
	const [storageClassList, setStorageClassList] = useState<StorageItem[]>([]);
	const [currentStorageClass, setCurrentStorageClass] =
		useState<StorageItem>();
	useEffect(() => {
		const name =
			type === 'relation' ? 'relationStorageClass' : 'storageClass';
		form && form.setFieldValue(name, null);
		if (clusterId && namespace) {
			getNamespaceStorages({
				clusterId,
				namespace
			}).then((res) => {
				if (res.success) {
					if (res.data.length > 0) {
						isActiveActive
							? setStorageClassList(res.data)
							: setStorageClassList(
									res.data.filter(
										(item: any) => !item.isActiveActive
									)
							  );
					} else {
						setStorageClassList([]);
					}
				}
			});
		}
	}, [clusterId, namespace]);
	useEffect(() => {
		type === 'relation'
			? handleChange(form.getFieldValue('relationStorageClass'))
			: handleChange(form.getFieldValue('storageClass'));
	}, [
		form && form.getFieldValue('storageClass'),
		form && form.getFieldValue('relationStorageClass'),
		storageClassList.length
	]);
	const handleChange = (value: string) => {
		const temp = storageClassList.find((item: StorageItem) => {
			if (item.isActiveActive) {
				if (
					`${item.storageClassList[0].name},${item.storageClassList[1].name}/${item.aliasName}` ===
					value
				) {
					return item;
				}
			} else {
				if (
					`${item.storageClassList[0].name}/${item.aliasName}` ===
					value
				) {
					return item;
				}
			}
		});
		setCurrentStorageClass(temp);
		if (chartName && chartName === 'elasticsearch') {
			// * es的各个节点可以支持选择不同的存储，要根据不同的存储进行综合，然后单独判断各个存储是否都超过所选存储的最大值
			const name = temp?.isActiveActive
				? `${temp?.storageClassList[0].name},${temp?.storageClassList[1].name}/${temp?.aliasName}`
				: `${temp?.storageClassList[0].name}/${temp?.aliasName}`;
			onChange &&
				onChange({
					[name]:
						(temp?.quota?.request || 0) - (temp?.quota?.used || 0)
				});
		} else {
			onChange &&
				onChange(
					(temp?.quota?.request || 0) - (temp?.quota?.used || 0)
				);
		}
	};
	const onInputChange = (value: any) => {
		const max =
			(currentStorageClass?.quota?.request || 0) -
			(currentStorageClass?.quota?.used || 0);
		if (chartName && chartName === 'elasticsearch') {
			// * es的各个节点可以支持选择不同的存储，要根据不同的存储进行综合，然后单独判断各个存储是否都超过所选存储的最大值
			const name = currentStorageClass?.isActiveActive
				? `${currentStorageClass?.storageClassList[0].name},${currentStorageClass?.storageClassList[1].name}/${currentStorageClass?.aliasName}`
				: `${currentStorageClass?.storageClassList[0].name}/${currentStorageClass?.aliasName}`;
			onChange &&
				onChange({
					[name]: max
				});
		} else {
			onChange && onChange(max);
		}
		const name =
			type === 'relation' ? 'relationStorageQuota' : 'storageQuota';
		setCurrentStorage && setCurrentStorage(value);
		form && form.setFieldValue(name, value);
	};
	return (
		<li className="display-flex">
			<label className="form-name">
				<span className="ne-required">存储配额</span>
			</label>
			<div className={`form-content display-flex`}>
				<FormItem
					name={
						type === 'relation'
							? 'relationStorageClass'
							: 'storageClass'
					}
					required
					rules={[
						{
							required: true,
							message: '请选择存储'
						}
					]}
				>
					<Select
						placeholder="请选择存储"
						style={{
							marginRight: 8,
							width: isActiveActive ? 250 : 150
						}}
						onChange={handleChange}
						dropdownMatchSelectWidth={false}
					>
						{storageClassList.map((item: StorageItem) => {
							return (
								<Select.Option
									key={item.storageClassList[0].name}
									value={
										item.isActiveActive
											? `${item.storageClassList[0].name},${item.storageClassList[1].name}/${item.aliasName}`
											: `${item.storageClassList[0].name}/${item.aliasName}`
									}
								>
									{item.aliasName}
									{item.isActiveActive ? (
										<span className="available-domain">
											可用区
										</span>
									) : null}
								</Select.Option>
							);
						})}
					</Select>
				</FormItem>
				<FormItem
					rules={[
						{
							required: true,
							message: '请输入存储配额大小（GB）'
						},
						{
							type: 'number',
							min: defaultStorage,
							message: `存储配额不能小于${defaultStorage}GB`
						}
					]}
					name={
						type === 'relation'
							? 'relationStorageQuota'
							: 'storageQuota'
					}
				>
					<InputNumber
						style={{ width: '200px' }}
						placeholder="请输入存储配额"
						onChange={onInputChange}
						value={formatNumber(currentStorage || 0)}
						addonAfter="GB"
					/>
				</FormItem>
			</div>
		</li>
	);
}

import * as React from 'react';
import { useState } from 'react';
import { Collapse, Form } from 'antd';
import { StorageItem } from '@/pages/StorageManagement/storageManage';
import './index.less';

interface DirectoryCardProps {
	data: any;
	// clusterId: string;
	// namespace: string;
	// isActiveActive: boolean;
	// storageClass: string;
	// storageQuota: number;
	// handleDirectory: (value: number) => void;
}

const { Panel } = Collapse;
const FormItem = Form.Item;

const titleType = {
	master: '主节点',
	data: '数据节点',
	client: '协调节点',
	cold: '冷数据节点'
};
function DirectoryDetail(props: DirectoryCardProps): JSX.Element {
	const {
		data
		// clusterId,
		// namespace,
		// isActiveActive,
		// storageClass,
		// storageQuota,
		// handleDirectory
	} = props;
	const [storage, setStorage] = useState<string>();
	const [storageClassList, setStorageClassList] = useState<StorageItem[]>([]);

	return (
		<div className="directory-card detail">
			<div className="title">{titleType[data.name.split('Node')[0]]}</div>
			<div className="content">
				<Collapse className="directory-panel">
					<Panel header="数据目录" key={1}>
						<div>
							<label>存储：</label>
							<span>{data.data.storageClass}</span>
						</div>
						<div>
							<label>存储大小：</label>
							<span>{data.data.volumeSize} GB</span>
						</div>
						<div>
							<label>宿主机目录：</label>
							<span>{data.data.hostPath}</span>
						</div>
						<div>
							<label>容器内目录：</label>
							<span>{data.data.mountPath}</span>
						</div>
					</Panel>
				</Collapse>
				<Collapse className="directory-panel mb-none">
					<Panel header="日志目录" key={2}>
						<div>
							<label>存储：</label>
							<span>{data.logs.storageClass}</span>
						</div>
						<div>
							<label>存储大小：</label>
							<span>{data.logs.volumeSize} GB</span>
						</div>
						<div>
							<label>宿主机目录：</label>
							<span>{data.logs.hostPath}</span>
						</div>
						<div>
							<label>容器内目录：</label>
							<span>{data.logs.mountPath}</span>
						</div>
					</Panel>
				</Collapse>
			</div>
		</div>
	);
}

export default DirectoryDetail;

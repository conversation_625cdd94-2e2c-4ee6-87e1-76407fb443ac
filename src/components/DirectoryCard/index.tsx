import * as React from 'react';
import { useState, useEffect } from 'react';
import { Collapse, Form, Select, Input, InputNumber } from 'antd';
import { getNamespaceStorages } from '@/services/storage';
import { StorageItem } from '@/pages/StorageManagement/storageManage';
import './index.less';

interface DirectoryCardProps {
	form: any;
	current: string;
	title: string;
	clusterId: string;
	namespace: string;
	isActiveActive: boolean;
	storageClass: string;
	storageQuota: number;
	disabled: boolean;
	customVolumes: any;
	handleDirectory: (value: number) => void;
}

const { Panel } = Collapse;
const FormItem = Form.Item;
function DirectoryCard(props: DirectoryCardProps): JSX.Element {
	const {
		form,
		current,
		title,
		clusterId,
		namespace,
		disabled,
		isActiveActive,
		storageClass,
		storageQuota,
		customVolumes,
		handleDirectory
	} = props;
	const [storage, setStorage] = useState<string>();
	const [storageClassList, setStorageClassList] = useState<StorageItem[]>([]);

	useEffect(() => {
		if (clusterId && namespace) {
			console.log(namespace);

			getNamespaceStorages({
				clusterId,
				namespace
			}).then((res) => {
				if (res.success) {
					if (res.data.length > 0) {
						isActiveActive
							? setStorageClassList(
									res.data.filter(
										(item: any) =>
											item.storageClassList[0]
												.volumeType === 'LocalPath'
									)
							  )
							: setStorageClassList(
									res.data
										.filter(
											(item: any) => !item.isActiveActive
										)
										.filter(
											(item: any) =>
												item.storageClassList[0]
													.volumeType === 'LocalPath'
										)
							  );
					} else {
						setStorageClassList([]);
					}
				}
			});
		}
	}, [clusterId, namespace]);

	useEffect(() => {
		if (customVolumes) {
			for (const key in customVolumes) {
				for (const k in customVolumes[key]) {
					if (k === 'volumeSize') {
						form.setFieldValue(`${key}_${k}`, null);
					} else {
						form.setFieldValue(
							`${key}_${k}`,
							customVolumes[key][k]
						);
					}
				}
			}
		} else {
			if (
				storageClass &&
				storageClassList.length &&
				storageClassList.find(
					(item) => item.aliasName === storageClass.split('/')[1]
				)?.storageClassList[0].volumeType === 'LocalPath'
			) {
				form.setFieldsValue({
					[current + 'Node-data_storageClass']:
						storageClass.split('/')[0],
					[current + 'Node-data_volumeSize']: storageQuota,
					[current + 'Node-logs_storageClass']:
						storageClass.split('/')[0],
					[current + 'Node-logs_volumeSize']: storageQuota
				});
			} else {
				form.setFieldsValue({
					[current + 'Node-data_storageClass']: null,
					[current + 'Node-data_volumeSize']: 1,
					[current + 'Node-logs_storageClass']: null,
					[current + 'Node-logs_volumeSize']: 1
				});
			}
		}
	}, [storageClass, storageClassList, customVolumes]);

	return (
		<div className="directory-card">
			<div className="title">{title}</div>
			<div className="content">
				<Collapse
					className="directory-panel fix-height"
					defaultActiveKey={1}
				>
					<Panel
						header={
							storageClassList.find(
								(item) =>
									item.storageClassList[0].name ===
									(form.getFieldValue(
										current + 'Node-data_storageClass'
									) || storageClass.split('/')[1])
							)?.storageClassList[0].volumeType ===
							'LocalPath' ? (
								<div>数据目录</div>
							) : (
								<div>
									数据目录
									<span style={{ color: '#d93026' }}>
										(未配置)
									</span>
								</div>
							)
						}
						key={1}
					>
						<FormItem
							label="存储"
							labelAlign="left"
							rules={[
								{
									required: true,
									message: '请选择存储'
								}
							]}
							name={current + 'Node-data_storageClass'}
							className="ant-form-name"
							extra={
								storageClassList.find(
									(item) =>
										item.storageClassList[0].name ===
										(form.getFieldValue(
											current + 'Node-data_storageClass'
										) || storageClass.split('/')[0])
								)?.storageClassList[0].volumeType ===
								'LocalPath' ? (
									<div style={{ color: '#d93026' }}>
										选择local-path类型存储时采用动态存储方式，实际存储大小取决于挂载磁盘容量
									</div>
								) : (
									''
								)
							}
						>
							<Select
								placeholder="请选择存储"
								style={{
									marginRight: 8,
									width: '100%'
								}}
								dropdownMatchSelectWidth={false}
								value={storage}
								onChange={(value) => {
									const data: any = storageClassList.find(
										(item) =>
											item.storageClassList[0].name ===
											value
									);
									setStorage(value);
									handleDirectory &&
										handleDirectory(
											data.quota.request -
												data.quota.used || 0
										);
								}}
							>
								{storageClassList.map((item: StorageItem) => {
									return (
										<Select.Option
											key={item.storageClassList[0].name}
											value={
												item.storageClassList[0].name
											}
										>
											<p>
												{item.aliasName}
												<span
													className="available-domain"
													style={{ color: '#52c41a' }}
												>
													{item.storageClassList[0]
														?.volumeType ===
													'LocalPath'
														? 'local-path'
														: item
																.storageClassList[0]
																?.volumeType}
												</span>
											</p>
										</Select.Option>
									);
								})}
							</Select>
						</FormItem>
						<FormItem
							label="存储大小"
							labelAlign="left"
							rules={[
								{
									required: true,
									message: '请输入存储大小'
								}
							]}
							name={current + 'Node-data_volumeSize'}
							className="ant-form-name"
							initialValue={1}
						>
							<InputNumber
								min={0}
								style={{ width: '180px' }}
								addonAfter="GB"
								placeholder="请输入存储大小"
							/>
						</FormItem>
						<FormItem
							label="宿主机目录"
							labelAlign="left"
							name={current + 'Node-data_hostPath'}
							className="ant-form-name"
							rules={[
								{
									required: true,
									message: '输入宿主机目录'
								}
							]}
							initialValue={
								current === 'master'
									? '/es/data'
									: `/es/${current}node/data`
							}
						>
							<Input
								style={{ width: '100%' }}
								disabled={disabled}
								placeholder="请输入/开头的目录地址"
							/>
						</FormItem>
						<FormItem
							label="容器内目录"
							labelAlign="left"
							rules={[
								{
									required: true,
									message: '请输入容器内目录'
								}
							]}
							name={current + 'Node-data_mountPath'}
							className="ant-form-name"
							initialValue={
								current === 'master'
									? '/es/data'
									: `/es/${current}node_data`
							}
						>
							<Input
								disabled={disabled}
								style={{ width: '100%' }}
								placeholder="请输入/开头的目录地址"
							/>
						</FormItem>
					</Panel>
				</Collapse>
				<Collapse
					className="directory-panel fix-height mb-none"
					defaultActiveKey={2}
				>
					<Panel
						header={
							storageClassList.find(
								(item) =>
									item.storageClassList[0].name ===
									(form.getFieldValue(
										current + 'Node-logs_storageClass'
									) || storageClass.split('/')[0])
							)?.storageClassList[0].volumeType ===
							'LocalPath' ? (
								<div>日志目录</div>
							) : (
								<div>
									日志目录
									<span style={{ color: '#d93026' }}>
										(未配置)
									</span>
								</div>
							)
						}
						key={2}
					>
						<FormItem
							label="存储"
							labelAlign="left"
							rules={[
								{
									required: true,
									message: '请选择存储'
								}
							]}
							name={current + 'Node-logs_storageClass'}
							className="ant-form-name"
							extra={
								storageClassList.find(
									(item) =>
										item.storageClassList[0].name ===
										(form.getFieldValue(
											current + 'Node-logs_storageClass'
										) || storageClass.split('/')[0])
								)?.storageClassList[0].volumeType ===
								'LocalPath' ? (
									<div style={{ color: '#d93026' }}>
										选择local-path类型存储时采用动态存储方式，实际存储大小取决于挂载磁盘容量
									</div>
								) : (
									''
								)
							}
						>
							<Select
								placeholder="请选择存储"
								style={{
									marginRight: 8,
									width: '100%'
								}}
								dropdownMatchSelectWidth={false}
								value={storage}
								onChange={(value) => {
									const data: any = storageClassList.find(
										(item) =>
											item.storageClassList[0].name ===
											value
									);
									setStorage(value);
									handleDirectory &&
										handleDirectory(
											data.quota.request -
												data.quota.used || 0
										);
								}}
							>
								{storageClassList.map((item: StorageItem) => {
									return (
										<Select.Option
											key={item.storageClassList[0].name}
											value={
												item.storageClassList[0].name
											}
										>
											<p>
												{item.aliasName}
												<span
													className="available-domain"
													style={{ color: '#52c41a' }}
												>
													{item.storageClassList[0]
														?.volumeType ===
													'LocalPath'
														? 'local-path'
														: item
																.storageClassList[0]
																?.volumeType}
												</span>
											</p>
										</Select.Option>
									);
								})}
							</Select>
						</FormItem>
						<FormItem
							label="存储大小"
							labelAlign="left"
							rules={[
								{
									required: true,
									message: '请输入存储大小'
								}
							]}
							name={current + 'Node-logs_volumeSize'}
							className="ant-form-name"
							initialValue={1}
						>
							<InputNumber
								min={0}
								style={{ width: '180px' }}
								addonAfter="GB"
								placeholder="请输入存储大小"
							/>
						</FormItem>
						<FormItem
							label="宿主机目录"
							labelAlign="left"
							name={current + 'Node-logs_hostPath'}
							className="ant-form-name"
							rules={[
								{
									required: true,
									message: '输入宿主机目录'
								}
							]}
							initialValue={
								current === 'master'
									? '/es/logs'
									: `/es/${current}node/logs`
							}
						>
							<Input
								disabled={disabled}
								style={{ width: '100%' }}
								placeholder="请输入/开头的目录地址"
							/>
						</FormItem>
						<FormItem
							label="容器内目录"
							labelAlign="left"
							rules={[
								{
									required: true,
									message: '请输入容器内目录'
								}
							]}
							name={current + 'Node-logs_mountPath'}
							className="ant-form-name"
							initialValue={
								current === 'master'
									? '/es/logs'
									: `/es/${current}node_logs`
							}
						>
							<Input
								disabled={disabled}
								style={{ width: '100%' }}
								placeholder="请输入/开头的目录地址"
							/>
						</FormItem>
					</Panel>
				</Collapse>
			</div>
		</div>
	);
}

export default DirectoryCard;

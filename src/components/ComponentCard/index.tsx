import React, { useState } from 'react';
import { notification, Modal } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { postComponent, deleteComponent } from '@/services/common';
import InstallForm from './installForm';
import AccessForm from './accessForm';
import LvmInstallForm from './lvmInstallForm';
import MidCard from '../MidCard';
import { StorageItem } from '@/pages/StorageManagement/storageManage';
import './index.less';
import { color, icon, name } from '@/utils/const';

interface ComponentCardProps {
	title: string;
	status: number;
	clusterId: string;
	onRefresh: () => void;
	createTime: string | null;
	seconds: number;
	data: any;
	storageClassList: StorageItem[];
	subMenu: any[];
}
export interface SendDataProps {
	clusterId: string;
	componentName: string;
	type?: string;
	vgName?: string;
	size?: number;
	platformProtocol?: string;
	platformHost?: string;
	platformPort?: string;
}

const { confirm } = Modal;

const ComponentCard = (props: ComponentCardProps) => {
	const {
		title,
		status,
		clusterId,
		onRefresh,
		createTime,
		seconds,
		data,
		storageClassList,
		subMenu
	} = props;
	const [visible, setVisible] = useState<boolean>(false);
	const [accessVisible, setAccessVisible] = useState<boolean>(false);
	const [lvmVisible, setLvmVisible] = useState<boolean>(false);
	const show = (code: string) =>
		subMenu?.find((item: any) => item.name === code);
	const installData = (data: SendDataProps) => {
		postComponent(data).then((res) => {
			if (res.success) {
				notification.success({
					message: '成功',
					description: '组件安装成功'
				});
				onRefresh();
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const installComponent = () => {
		if (title === 'local-path' || title === 'ingress') {
			const content =
				title === 'local-path' ? (
					<p>是否确认安装资源存储组件</p>
				) : (
					<p>是否确认安装负载均衡组件</p>
				);
			confirm({
				title: '确认安装',
				content: content,
				okText: '确定',
				cancelText: '取消',
				onOk: () => {
					const sendData = {
						clusterId,
						componentName: title,
						type: 'simple'
					};
					installData(sendData);
				}
			});
		} else {
			setVisible(true);
		}
	};
	const uninstallComponent = () => {
		const msg = status === 1 ? '取消接入' : '卸载';
		const content =
			status === 1 ? (
				<p>是否确认取消接入该组件</p>
			) : (
				<p>是否确认卸载该组件</p>
			);
		confirm({
			icon: <ExclamationCircleOutlined />,
			title: `确认${msg}`,
			content: content,
			okText: '确定',
			cancelText: '取消',
			onOk: () => {
				const sendData = {
					clusterId,
					componentName: title,
					status
				};
				deleteComponent(sendData).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: `操作成功！${msg}${name[title]}需要一定时间，请留意刷新数据！`
						});
						onRefresh();
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			},
			onCancel: () => console.log('弹窗取消')
		});
	};
	const actionRender = (title: string) => {
		if (
			title !== 'lvm' &&
			title !== 'local-path' &&
			title !== 'minio' &&
			title !== 'middleware-scheduler' &&
			title !== 'middleware-admission-webhook' &&
			title !== 'middlewarebackup-controller' &&
			title !== 'fs-exporter' &&
			title !== 'gossip-operator' &&
			title !== 'replicator'
		) {
			return 2;
		} else {
			return 1;
		}
	};
	const cardRender = () => {
		switch (status) {
			case 0:
				return (
					<MidCard
						color={color[title]}
						icon={icon[title]}
						title={name[title]}
						status={status}
						actionCount={actionRender(title)}
						leftText={
							show('clusterPlatformComponentsAdd') ? '安装' : ''
						}
						rightText={
							show('clusterPlatformComponentsAccess')
								? '接入'
								: ''
						}
						leftClass="link"
						rightClass="link"
						leftHandle={() => {
							title !== 'lvm'
								? installComponent()
								: setLvmVisible(true);
						}}
						rightHandle={() => setAccessVisible(true)}
						centerText={
							show('clusterPlatformComponentsAdd') ? '安装' : ''
						}
						centerClass="link"
						centerHandle={() => {
							title !== 'lvm'
								? installComponent()
								: setLvmVisible(true);
						}}
						createTime={createTime}
						seconds={seconds}
					/>
				);
			case 1:
				return (
					<MidCard
						color={color[title]}
						icon={icon[title]}
						title={name[title]}
						status={status}
						actionCount={2}
						leftText={
							show('clusterPlatformComponentsDelete')
								? '取消接入'
								: ''
						}
						rightText={
							show('clusterPlatformComponentsUpdate')
								? '编辑'
								: ''
						}
						leftClass="danger"
						rightClass="link"
						leftHandle={uninstallComponent}
						rightHandle={() => setAccessVisible(true)}
						createTime={createTime}
						seconds={seconds}
					/>
				);
			case 2:
				return (
					<MidCard
						color={color[title]}
						icon={icon[title]}
						title={name[title]}
						status={status}
						actionCount={1}
						centerText={
							show('clusterPlatformComponentsAdd') ? '安装中' : ''
						}
						createTime={createTime}
						centerStyle={{
							background: '#0064C8',
							color: '#ffffff',
							border: 'none'
						}}
						onRefresh={onRefresh}
						seconds={seconds}
					/>
				);
			case 3:
				return (
					<MidCard
						color={color[title]}
						icon={icon[title]}
						title={name[title]}
						status={status}
						actionCount={actionRender(title)}
						leftText={
							show('clusterPlatformComponentsDelete')
								? '卸载'
								: ''
						}
						leftClass="danger"
						rightText={
							show('clusterPlatformComponentsUpdate')
								? '编辑'
								: ''
						}
						rightClass="link"
						leftHandle={uninstallComponent}
						rightHandle={() => setAccessVisible(true)}
						centerText={
							show('clusterPlatformComponentsDelete')
								? '卸载'
								: ''
						}
						centerClass="danger"
						centerHandle={uninstallComponent}
						createTime={createTime}
						seconds={seconds}
					/>
				);
			case 4:
				return (
					<MidCard
						color={color[title]}
						icon={icon[title]}
						title={name[title]}
						status={status}
						actionCount={actionRender(title)}
						leftText={
							show('clusterPlatformComponentsDelete')
								? '卸载'
								: ''
						}
						leftClass="danger"
						rightText={
							show('clusterPlatformComponentsUpdate')
								? '编辑'
								: ''
						}
						rightClass="link"
						leftHandle={uninstallComponent}
						rightHandle={() => setAccessVisible(true)}
						centerText={
							show('clusterPlatformComponentsDelete')
								? '卸载'
								: ''
						}
						centerClass="danger"
						centerHandle={uninstallComponent}
						createTime={createTime}
						seconds={seconds}
					/>
				);
			case 5:
				return (
					<MidCard
						color={color[title]}
						icon={icon[title]}
						title={name[title]}
						status={status}
						actionCount={1}
						centerText={
							show('clusterPlatformComponentsDelete')
								? '卸载中'
								: ''
						}
						centerStyle={{
							background: '#C80000',
							color: '#ffffff',
							border: 'none'
						}}
						createTime={createTime}
						seconds={seconds}
					/>
				);
			case 6:
				return (
					<MidCard
						color={color[title]}
						icon={icon[title]}
						title={name[title]}
						status={status}
						actionCount={actionRender(title)}
						leftText={
							show('clusterPlatformComponentsDelete')
								? '卸载'
								: ''
						}
						leftClass="danger"
						rightText={
							show('clusterPlatformComponentsUpdate')
								? '编辑'
								: ''
						}
						rightClass="link"
						leftHandle={uninstallComponent}
						rightHandle={() => setAccessVisible(true)}
						centerText={
							show('clusterPlatformComponentsDelete')
								? '卸载'
								: ''
						}
						centerClass="danger"
						centerHandle={uninstallComponent}
						createTime={createTime}
						seconds={seconds}
					/>
				);
			case 7:
				return (
					<MidCard
						color={color[title]}
						icon={icon[title]}
						title={name[title]}
						status={status}
						actionCount={actionRender(title)}
						leftText={
							show('clusterPlatformComponentsDelete')
								? '卸载'
								: ''
						}
						leftClass="danger"
						rightText={
							show('clusterPlatformComponentsUpdate')
								? '编辑'
								: ''
						}
						rightClass="link"
						leftHandle={uninstallComponent}
						rightHandle={() => setAccessVisible(true)}
						centerText={
							show('clusterPlatformComponentsDelete')
								? '卸载'
								: ''
						}
						centerClass="danger"
						centerHandle={uninstallComponent}
						createTime={createTime}
						seconds={seconds}
					/>
				);
			default:
				break;
		}
	};
	return (
		<>
			{cardRender()}
			{visible && (
				<InstallForm
					visible={visible}
					onCancel={() => setVisible(false)}
					onCreate={installData}
					title={title}
					clusterId={clusterId}
					storageClassList={storageClassList}
				/>
			)}
			{accessVisible && (
				<AccessForm
					visible={accessVisible}
					onCancel={() => setAccessVisible(false)}
					status={status}
					title={title}
					clusterId={clusterId}
					onRefresh={onRefresh}
					componentData={data}
				/>
			)}
			{lvmVisible && (
				<LvmInstallForm
					visible={lvmVisible}
					onCancel={() => setLvmVisible(false)}
					onCreate={installData}
					title={title}
					clusterId={clusterId}
					onRefresh={onRefresh}
				/>
			)}
		</>
	);
};
export default ComponentCard;

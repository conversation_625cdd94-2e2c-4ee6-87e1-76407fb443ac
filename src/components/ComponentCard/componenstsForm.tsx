import React, { useEffect, useState } from 'react';
import { Form, Input, Select, Switch, Row, Col, InputNumber } from 'antd';
import { silences, recordTime } from '@/utils/const';
import transUnit from '@/utils/transUnit';
const formItemLayout = {
	labelCol: {
		span: 5
	},
	wrapperCol: {
		span: 19
	}
};
const formItemLayout2 = {
	labelCol: {
		span: 8
	},
	wrapperCol: {
		span: 16
	}
};
const FormItem = Form.Item;
const Option = Select.Option;
export const PrometheusRender = () => {
	return (
		<>
			<FormItem
				{...formItemLayout}
				labelAlign="left"
				label="prometheus地址"
				style={{ marginBottom: 0 }}
				labelCol={{ span: 5 }}
				required
			>
				<Row>
					<Col span={6}>
						<FormItem name="protocolPrometheus" initialValue="http">
							<Select
								style={{
									width: '100%'
								}}
							>
								<Select.Option value="https">
									https
								</Select.Option>
								<Select.Option value="http">http</Select.Option>
							</Select>
						</FormItem>
					</Col>
					<Col span={12}>
						<FormItem
							required
							rules={[
								{ required: true, message: '请输入IP地址' }
							]}
							style={{
								marginLeft: -2
							}}
							name="hostPrometheus"
						>
							<Input type="text" placeholder="请输入IP地址" />
						</FormItem>
					</Col>
					<Col span={6}>
						<FormItem
							required
							rules={[{ required: true, message: '请输入端口' }]}
							style={{
								marginLeft: -2
							}}
							name="portPrometheus"
						>
							<InputNumber
								min={1}
								max={65535}
								placeholder="端口"
								style={{ width: '100%' }}
							/>
						</FormItem>
					</Col>
				</Row>
			</FormItem>
			<FormItem
				{...formItemLayout}
				labelAlign="left"
				label="prometheus鉴权"
				style={{ marginBottom: 0 }}
			>
				<Row gutter={4}>
					<Col>
						<FormItem name="usernamePrometheus">
							<Input placeholder="请输入账户名" />
						</FormItem>
					</Col>
					<Col>
						<FormItem name="passwordPrometheus">
							<Input.Password placeholder="请输入密码" />
						</FormItem>
					</Col>
				</Row>
			</FormItem>
		</>
	);
};
export const AgentRender = () => {
	return (
		<FormItem
			{...formItemLayout}
			labelAlign="left"
			label="客户端控制器地址"
			style={{ marginBottom: 0 }}
			labelCol={{ span: 5 }}
		>
			<Row>
				<Col span={6}>
					<FormItem name="protocolAgent">
						<Select
							style={{
								width: '100%'
							}}
						>
							<Select.Option value="https">https</Select.Option>
							<Select.Option value="http">http</Select.Option>
						</Select>
					</FormItem>
				</Col>
				<Col span={12}>
					<FormItem
						required
						rules={[{ required: true, message: '请输入IP地址' }]}
						style={{
							marginLeft: -2
						}}
						name="hostAgent"
					>
						<Input type="text" placeholder="请输入IP地址" />
					</FormItem>
				</Col>
				<Col span={6}>
					<FormItem
						required
						rules={[{ required: true, message: '请输入端口' }]}
						style={{
							marginLeft: -2
						}}
						name="portAgent"
					>
						<InputNumber
							min={1}
							max={65535}
							placeholder="端口"
							style={{ width: '100%' }}
						/>
					</FormItem>
				</Col>
			</Row>
		</FormItem>
	);
};
export const MiddlewareApiRender = () => (
	<FormItem
		{...formItemLayout}
		labelAlign="left"
		label="Middleware-API地址"
		style={{ marginBottom: 0 }}
	>
		<Row>
			<Col span={6}>
				<FormItem name="protocolAPI">
					<Select
						style={{
							width: '100%'
						}}
					>
						<Select.Option value="https">https</Select.Option>
						<Select.Option value="http">http</Select.Option>
					</Select>
				</FormItem>
			</Col>
			<Col span={12}>
				<FormItem
					required
					rules={[{ required: true, message: '请输入IP地址' }]}
					style={{
						marginLeft: -2
					}}
					name="hostAPI"
				>
					<Input type="text" placeholder="请输入IP地址" />
				</FormItem>
			</Col>
			<Col span={6}>
				<FormItem
					required
					rules={[{ required: true, message: '请输入端口' }]}
					style={{
						marginLeft: -2
					}}
					name="portAPI"
				>
					<InputNumber
						min={1}
						max={65535}
						placeholder="端口"
						style={{ width: '100%' }}
					/>
				</FormItem>
			</Col>
		</Row>
	</FormItem>
);
export const LoggingRender = (props: any) => {
	const { form, data, status } = props;
	const [options] = useState<string[]>([
		'7',
		'30',
		'60',
		'120',
		'180',
		'365',
		'730',
		'1095',
		'ALWAYS'
	]);
	const [logSaveTime, setLogSaveTime] = useState<string>('ALWAYS');
	const [customValue, setCustomValue] = useState<number | null>();
	useEffect(() => {
		if (status !== 0) {
			if (
				!options.includes(transUnit.removeUnit(data.logSaveTime, 'd'))
			) {
				setLogSaveTime('custom');
				setCustomValue(
					Number(transUnit.removeUnit(data.logSaveTime, 'd'))
				);
			} else {
				setLogSaveTime(transUnit.removeUnit(data.logSaveTime, 'd'));
			}
		}
	}, [data]);
	useEffect(() => {
		form.setFieldsValue({
			logSaveTime:
				logSaveTime === 'custom'
					? customValue
						? Math.floor(customValue || 1) + ''
						: ''
					: logSaveTime
		});
	}, [logSaveTime, customValue]);
	return (
		<>
			<FormItem
				{...formItemLayout}
				labelAlign="left"
				label="ES地址"
				style={{ marginBottom: 0 }}
				required
			>
				<Row>
					<Col span={6}>
						<FormItem name="protocolEs">
							<Select
								style={{
									width: '100%'
								}}
							>
								<Select.Option value="https">
									https
								</Select.Option>
								<Select.Option value="http">http</Select.Option>
							</Select>
						</FormItem>
					</Col>
					<Col span={12}>
						<FormItem
							required
							rules={[
								{ required: true, message: '请输入IP地址' }
							]}
							style={{
								marginLeft: -2
							}}
							name="hostEs"
						>
							<Input type="text" placeholder="请输入IP地址" />
						</FormItem>
					</Col>
					<Col span={6}>
						<FormItem
							required
							rules={[{ required: true, message: '请输入端口' }]}
							style={{
								marginLeft: -2
							}}
							name="portEs"
						>
							<InputNumber
								min={1}
								max={65535}
								placeholder="端口"
								style={{ width: '100%' }}
							/>
						</FormItem>
					</Col>
				</Row>
			</FormItem>
			<FormItem
				{...formItemLayout}
				labelAlign="left"
				label="ES鉴权"
				style={{ marginBottom: 0 }}
			>
				<Row gutter={4}>
					<Col>
						<FormItem name="userEs">
							<Input placeholder="请输入用户名" />
						</FormItem>
					</Col>
					<Col>
						<FormItem name="passwordEs">
							<Input.Password placeholder="请输入密码" />
						</FormItem>
					</Col>
				</Row>
			</FormItem>
			<FormItem
				{...formItemLayout}
				labelAlign="left"
				label="ES日志采集工具"
				valuePropName="checked"
				name="logCollect"
			>
				<Switch />
			</FormItem>
			<FormItem
				{...formItemLayout}
				labelAlign="left"
				label="日志保留时间"
				name="logSaveTime"
				rules={[{ required: true, message: '日志保留时间不能为空' }]}
			>
				<Row gutter={4}>
					<Col span={6}>
						<Select
							value={logSaveTime}
							onChange={(value) => setLogSaveTime(value)}
						>
							<Select.Option value="7">7天</Select.Option>
							<Select.Option value="30">30天</Select.Option>
							<Select.Option value="60">60天</Select.Option>
							<Select.Option value="120">120天</Select.Option>
							<Select.Option value="180">180天</Select.Option>
							<Select.Option value="365">1年</Select.Option>
							<Select.Option value="730">2年</Select.Option>
							<Select.Option value="1095">3年</Select.Option>
							<Select.Option value="custom">自定义</Select.Option>
							<Select.Option value="ALWAYS">永久</Select.Option>
						</Select>
					</Col>
					<Col>
						{logSaveTime === 'custom' && (
							<InputNumber
								onChange={(value: number | null) =>
									setCustomValue(value && Math.floor(value))
								}
								min={1}
								value={customValue}
								addonAfter="天"
								step={1}
							/>
						)}
					</Col>
				</Row>
			</FormItem>
		</>
	);
};
export const GrafanaRender = () => {
	return (
		<>
			<FormItem
				{...formItemLayout}
				labelAlign="left"
				label="grafana地址"
				labelCol={{ span: 5 }}
				style={{ marginBottom: 0 }}
				required
			>
				<Row>
					<Col span={6}>
						<FormItem name="protocolGrafana">
							<Select
								style={{
									width: '100%'
								}}
							>
								<Select.Option value="https">
									https
								</Select.Option>
								<Select.Option value="http">http</Select.Option>
							</Select>
						</FormItem>
					</Col>
					<Col span={12}>
						<FormItem
							required
							rules={[
								{ required: true, message: '请输入IP地址' }
							]}
							style={{
								marginLeft: -2
							}}
							name="hostGrafana"
						>
							<Input type="text" placeholder="请输入IP地址" />
						</FormItem>
					</Col>
					<Col span={6}>
						<FormItem
							required
							rules={[{ required: true, message: '请输入端口' }]}
							style={{
								marginLeft: -2
							}}
							name="portGrafana"
						>
							<InputNumber
								min={1}
								max={65535}
								placeholder="端口"
								style={{ width: '100%' }}
							/>
						</FormItem>
					</Col>
				</Row>
			</FormItem>
			<FormItem
				{...formItemLayout}
				labelAlign="left"
				label="grafana鉴权"
				style={{ marginBottom: 0 }}
			>
				<Row gutter={4}>
					<Col>
						<FormItem name="usernameGrafana">
							<Input placeholder="请输入账户名" />
						</FormItem>
					</Col>
					<Col>
						<FormItem name="passwordGrafana">
							<Input.Password placeholder="请输入密码" />
						</FormItem>
					</Col>
				</Row>
			</FormItem>
		</>
	);
};
export const AlertRender = (props: any) => {
	const { data, status } = props;
	const [alertTime, setAlertTime] = useState<string>('1');
	return (
		<>
			<FormItem
				{...formItemLayout}
				labelAlign="left"
				label="监控告警地址"
				style={{ marginBottom: 0 }}
				required
			>
				<Row>
					<Col span={6}>
						<FormItem name="protocolAlert">
							<Select
								style={{
									width: '100%'
								}}
							>
								<Select.Option value="https">
									https
								</Select.Option>
								<Select.Option value="http">http</Select.Option>
							</Select>
						</FormItem>
					</Col>
					<Col span={12}>
						<FormItem
							required
							rules={[
								{ required: true, message: '请输入IP地址' }
							]}
							style={{
								marginLeft: -2
							}}
							name="hostAlert"
						>
							<Input type="text" placeholder="请输入IP地址" />
						</FormItem>
					</Col>
					<Col span={6}>
						<FormItem
							style={{
								marginLeft: -2
							}}
							required
							rules={[{ required: true, message: '请输入端口' }]}
							name="portAlert"
						>
							<InputNumber
								min={1}
								max={65535}
								placeholder="端口"
								style={{ width: '100%' }}
							/>
						</FormItem>
					</Col>
				</Row>
			</FormItem>
			<FormItem
				{...formItemLayout}
				labelAlign="left"
				label="监控告警鉴权"
				style={{ marginBottom: 0 }}
			>
				<Row gutter={4}>
					<Col>
						<FormItem name="usernameAlert">
							<Input placeholder="请输入账户名" />
						</FormItem>
					</Col>
					<Col>
						<FormItem name="passwordAlert">
							<Input.Password placeholder="请输入密码" />
						</FormItem>
					</Col>
				</Row>
			</FormItem>
			{data.status === 0 && (
				<FormItem
					{...formItemLayout}
					labelAlign="left"
					label="Zeus-API地址"
					style={{ marginBottom: 0 }}
					required
				>
					<Row>
						<Col span={6}>
							<FormItem name="platformProtocol">
								<Select
									style={{
										width: '100%'
									}}
								>
									<Select.Option value="https">
										https
									</Select.Option>
									<Select.Option value="http">
										http
									</Select.Option>
								</Select>
							</FormItem>
						</Col>
						<Col span={12}>
							<FormItem
								required
								rules={[
									{ required: true, message: '请输入IP地址' }
								]}
								style={{
									marginLeft: -2
								}}
								name="platformHost"
							>
								<Input type="text" placeholder="请输入IP地址" />
							</FormItem>
						</Col>
						<Col span={6}>
							<FormItem
								style={{
									marginLeft: -2
								}}
								required
								rules={[
									{ required: true, message: '请输入端口' }
								]}
								name="platformPort"
							>
								<InputNumber
									min={1}
									max={65535}
									placeholder="端口"
									style={{ width: '100%' }}
								/>
							</FormItem>
						</Col>
					</Row>
				</FormItem>
			)}
			<FormItem
				{...formItemLayout}
				labelAlign="left"
				label="全局静默时间"
				name="silentTime"
				required
			>
				<Select>
					{silences.map((i) => {
						return (
							<Option key={i.value} value={i.value}>
								{i.text}
							</Option>
						);
					})}
				</Select>
			</FormItem>
			<div className="display-flex">
				<FormItem
					labelCol={{
						span:
							alertTime === 'customize' ||
							(!['-1', '1', '3', '6', '12'].includes(
								data.alertRetentionDuration
							) &&
								status !== 0 &&
								status !== 1)
								? 8
								: 5
					}}
					labelAlign="left"
					label="告警记录保留时间"
					style={{
						marginBottom: 0,
						width:
							alertTime === 'customize' ||
							(!['-1', '1', '3', '6', '12'].includes(
								data.alertRetentionDuration
							) &&
								status !== 0)
								? '62.5%'
								: '100%'
					}}
					name="alertRetentionDuration"
					required
				>
					<Select
						value={alertTime}
						onChange={(value) => setAlertTime(value)}
					>
						{recordTime.map((i) => {
							return (
								<Option key={i.value} value={i.value}>
									{i.text}
								</Option>
							);
						})}
					</Select>
				</FormItem>
				{(alertTime === 'customize' ||
					(!['-1', '1', '3', '6', '12'].includes(
						data.alertRetentionDuration
					) &&
						status === 0)) && (
					<Form.Item
						name="alertTime"
						style={{ width: '37.5%' }}
						wrapperCol={{ span: 24 }}
						rules={[
							{
								required: true,
								message: '请输入告警记录保留时间'
							}
						]}
					>
						<InputNumber
							min={1}
							step={1}
							parser={(value: any) => value.split('.')[0]}
							style={{ width: '100%' }}
							addonAfter="月"
						/>
					</Form.Item>
				)}
			</div>
		</>
	);
};
export const MinioRender = (props: any) => {
	const { form, data } = props;
	const [head, setHead] = useState<string>('http://');
	const [mid, setMid] = useState<string>();
	const [tail, setTail] = useState<number>();
	const { endpoint } = data;
	useEffect(() => {
		form.setFieldsValue({
			endpoint: head + mid + ':' + tail + ''
		});
	}, [head, mid, tail]);
	useEffect(() => {
		if (endpoint) {
			const endpoints = endpoint.split(':');
			setHead(`${endpoints[0]}://`);
			setMid(
				endpoints[1].substring(2) === 'undefined'
					? ''
					: endpoints[1].substring(2)
			);
			setTail(endpoints[2]);
		}
	}, [endpoint]);
	const handleChange = (value: any, type: string) => {
		switch (type) {
			case 'head':
				setHead(value);
				break;
			case 'mid':
				setMid(value);
				break;
			case 'tail':
				setTail(value);
				break;
			default:
				break;
		}
	};
	const select = (
		<Select onChange={(value) => handleChange(value, 'head')} value={head}>
			<Option value="https://">https://</Option>
			<Option value="http://">http://</Option>
		</Select>
	);
	const input = (
		<Input
			type="number"
			onChange={(value) => handleChange(value, 'tail')}
			style={{ width: '80px' }}
			value={tail}
		/>
	);
	return (
		<>
			<FormItem
				{...formItemLayout2}
				label="Access Key ID"
				required
				labelAlign="left"
				name="accessKeyId"
			>
				<Input />
			</FormItem>
			<FormItem
				{...formItemLayout2}
				label="Bucket名称"
				required
				labelAlign="left"
				name="bucketName"
			>
				<Input />
			</FormItem>
			<FormItem
				{...formItemLayout2}
				label="Minio名称"
				required
				labelAlign="left"
				name="minioName"
			>
				<Input />
			</FormItem>
			<FormItem
				{...formItemLayout2}
				label="Minio地址"
				required
				labelAlign="left"
			>
				<Input.Group>
					<Row>
						<Col span={6}>
							<Select
								onChange={(value) =>
									handleChange(value, 'head')
								}
								value={head}
								style={{ width: '100%' }}
							>
								<Option value="https://">https://</Option>
								<Option value="http://">http://</Option>
							</Select>
						</Col>
						<Col span={12}>
							<Input
								style={{ width: '100%' }}
								value={mid}
								onChange={(value) => handleChange(value, 'mid')}
							/>
						</Col>
						<Col span={6}>
							<Input
								type="number"
								onChange={(value) =>
									handleChange(value, 'tail')
								}
								style={{ width: '100%' }}
								value={tail}
							/>
						</Col>
					</Row>
				</Input.Group>
			</FormItem>
			<FormItem
				{...formItemLayout2}
				label="Access Key Secret"
				required
				labelAlign="left"
				name="secretAccessKey"
			>
				<Input />
			</FormItem>
		</>
	);
};
export const LvmRender = () => {
	return (
		<>
			<FormItem
				{...formItemLayout}
				label="名称"
				required
				rules={[{ required: true, message: '请输入名称' }]}
				name="lvmName"
				labelAlign="left"
			>
				<Input type="text" placeholder="请输入名称" />
			</FormItem>
			<FormItem
				{...formItemLayout}
				label="分区"
				required
				rules={[{ required: true, message: '请输入分区' }]}
				name="lvmNamespace"
			>
				<Input type="text" placeholder="请输入分区" />
			</FormItem>
		</>
	);
};
export const LocalPathRender = () => {
	return (
		<>
			<FormItem
				{...formItemLayout}
				label="名称"
				required
				rules={[{ required: true, message: '请输入名称' }]}
				name="localPathName"
			>
				<Input type="text" placeholder="请输入名称" />
			</FormItem>
			<FormItem
				{...formItemLayout}
				label="分区"
				required
				rules={[{ required: true, message: '请输入分区' }]}
				name="localPathNamespace"
			>
				<Input type="text" placeholder="请输入分区" />
			</FormItem>
		</>
	);
};

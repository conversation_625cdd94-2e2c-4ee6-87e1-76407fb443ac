import React, { useEffect } from 'react';
import { Modal, Form, notification } from 'antd';
import { putComponent, cutInComponent, getLogCollect } from '@/services/common';
import {
	PrometheusRender,
	LoggingRender,
	GrafanaRender,
	AlertRender,
	MinioRender,
	LvmRender,
	LocalPathRender,
	MiddlewareApiRender,
	AgentRender
} from './componenstsForm';
import './index.less';
import transUnit from '@/utils/transUnit';
import { StorageItem } from '@/pages/StorageManagement/storageManage';
import { PRIVATE_KEY, PUBLIC_KEY } from '@/utils/const';
import { decrypt, encrypt } from '@/utils/utils';

interface AccessFormProps {
	visible: boolean;
	onCancel: () => void;
	title: string;
	clusterId: string;
	onRefresh: () => void;
	status: number;
	componentData: any;
}

const AccessForm = (props: AccessFormProps) => {
	const {
		visible,
		onCancel,
		title,
		clusterId,
		onRefresh,
		status,
		componentData
	} = props;
	const [form] = Form.useForm();
	useEffect(() => {
		if (componentData.component === 'logging') {
			getLogCollect({ clusterId: componentData.clusterId }).then(
				(res) => {
					if (res.success) {
						form.setFieldsValue({
							logCollect: res.data
						});
					} else {
						form.setFieldsValue({
							logCollect: false
						});
					}
				}
			);
		}
	}, []);
	useEffect(() => {
		if (componentData.component === 'alertmanager')
			form.setFieldsValue({
				protocolAlert: componentData.protocol || 'http',
				hostAlert: componentData.host,
				portAlert: componentData.port,
				platformProtocol: 'http',
				platformHost: 'zeus.zeus',
				platformPort: 8080,
				silentTime:
					status === 0 ? '1h' : componentData.silentTime || '1h',
				alertRetentionDuration:
					status === 0
						? '1'
						: !['-1', '1', '3', '6', '12'].includes(
								componentData.alertRetentionDuration
						  )
						? 'customize'
						: componentData.alertRetentionDuration,
				alertTime: !['-1', '1', '3', '6', '12'].includes(
					componentData.alertRetentionDuration
				)
					? componentData.alertRetentionDuration
					: null,
				usernameAlert: componentData.username,
				passwordAlert: componentData.password
					? decrypt(componentData.password, PRIVATE_KEY)
					: componentData.password
			});
		if (componentData.component === 'grafana')
			form.setFieldsValue({
				protocolGrafana: componentData.protocol || 'http',
				hostGrafana: componentData.host,
				portGrafana: componentData.port,
				storageClassName: componentData.storageClassName,
				storageSize: Number(
					transUnit.removeUnit(componentData.storageSize, 'GB')
				),
				usernameGrafana: componentData.username,
				passwordGrafana: componentData.password
					? decrypt(componentData.password, PRIVATE_KEY)
					: componentData.password
			});
		if (componentData.component === 'prometheus')
			form.setFieldsValue({
				protocolPrometheus: componentData.protocol || 'http',
				hostPrometheus: componentData.host,
				portPrometheus: componentData.port,
				storageClassName: componentData.storageClassName,
				storageSize: Number(
					transUnit.removeUnit(componentData.storageSize, 'GB')
				),
				usernamePrometheus: componentData.username,
				passwordPrometheus: componentData.password
					? decrypt(componentData.password, PRIVATE_KEY)
					: componentData.password
			});
		form.setFieldsValue({
			protocolAgent: componentData.protocol || 'http',
			hostAgent: componentData.host,
			portAgent: componentData.port
		});
		form.setFieldsValue({
			protocolEs: componentData.protocol || 'http',
			hostEs: componentData.host,
			portEs: componentData.port,
			userEs: componentData.username,
			passwordEs: componentData.password
				? decrypt(componentData.password, PRIVATE_KEY)
				: componentData.password,
			storageClassName: componentData.storageClassName,
			storageSize: Number(
				transUnit.removeUnit(componentData.storageSize, 'GB')
			)
		});
		form.setFieldsValue({
			protocolAPI: componentData.protocol || 'http',
			hostAPI: componentData.host,
			portAPI: componentData.port
		});
	}, []);
	const onOk = () => {
		form.validateFields().then((values) => {
			const sendData: any = {
				clusterId,
				componentName: title
			};
			if (title === 'grafana') {
				sendData.host = values.hostGrafana;
				sendData.port = values.portGrafana;
				sendData.protocol = values.protocolGrafana;
				sendData.username = values.usernameGrafana;
				sendData.password =
					encrypt(values.passwordGrafana, PUBLIC_KEY) ||
					values.passwordGrafana;
			} else if (title === 'logging') {
				sendData.protocol = values.protocolEs;
				sendData.host = values.hostEs;
				sendData.port = values.portEs;
				sendData.username = values.userEs;
				sendData.password =
					encrypt(values.passwordEs, PUBLIC_KEY) || values.passwordEs;
				sendData.logCollect = values.logCollect;
				sendData.logSaveTime =
					values.logSaveTime === 'ALWAYS'
						? 'ALWAYS'
						: values.logSaveTime + 'd';
			} else if (title === 'alertmanager') {
				if (status === 0) {
					sendData.platformProtocol = values.platformProtocol;
					sendData.platformHost = values.platformHost;
					sendData.platformPort = values.platformPort;
				}
				sendData.host = values.hostAlert;
				sendData.port = values.portAlert;
				sendData.protocol = values.protocolAlert;
				sendData.silentTime = values.silentTime;
				sendData.alertRetentionDuration =
					values.alertRetentionDuration === 'customize'
						? values.alertTime
						: values.alertRetentionDuration;
				sendData.username = values.usernameAlert;
				sendData.password =
					encrypt(values.passwordAlert, PUBLIC_KEY) ||
					values.passwordAlert;
			} else if (title === 'prometheus') {
				sendData.host = values.hostPrometheus;
				sendData.port = values.portPrometheus;
				sendData.protocol = values.protocolPrometheus;
				sendData.username = values.usernamePrometheus;
				sendData.password =
					encrypt(values.passwordPrometheus, PUBLIC_KEY) ||
					values.passwordPrometheus;
			} else if (title === 'middleware-controller') {
				sendData.host = values.hostAPI;
				sendData.port = values.portAPI;
				sendData.protocol = values.protocolAPI;
			} else if (title === 'extra-middleware-controller') {
				sendData.host = values.hostAgent;
				sendData.port = values.portAgent;
				sendData.protocol = values.protocolAgent;
			}
			if (status === 0) {
				cutInComponent(sendData).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '组件接入成功'
						});
						onCancel();
						onRefresh();
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			} else {
				putComponent(sendData).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: `组件编辑成功`
						});
						onCancel();
						onRefresh();
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			}
		});
	};

	const childrenRender = () => {
		switch (title) {
			case 'minio':
				return <MinioRender data={form.getFieldValue} form={form} />;
			case 'prometheus':
				return <PrometheusRender />;
			case 'alertmanager':
				return <AlertRender data={componentData} status={status} />;
			case 'grafana':
				return <GrafanaRender />;
			case 'logging':
				return (
					<LoggingRender
						data={componentData}
						form={form}
						status={status}
					/>
				);
			case 'lvm':
				return <LvmRender />;
			case 'local-path':
				return <LocalPathRender />;
			case 'middleware-controller':
				return <MiddlewareApiRender />;
			case 'extra-middleware-controller':
				return <AgentRender />;
			default:
				break;
		}
	};
	return (
		<Modal
			title={`工具${status === 0 ? '接入' : '编辑'}`}
			open={visible}
			onCancel={onCancel}
			onOk={onOk}
			width={650}
			okText="确定"
			cancelText="取消"
		>
			<div className="access-title-content">
				<div className="access-title-name">
					完善{status === 0 ? '接入' : '编辑'}信息
				</div>
			</div>
			<p className="access-subtitle">
				若您的集群已经安装了对应工具，可直接
				{status === 0 ? '接入' : '编辑'}
				使用
			</p>
			<div className="access-form-content">
				<Form labelAlign="left" form={form}>
					{childrenRender()}
				</Form>
			</div>
		</Modal>
	);
};
export default AccessForm;

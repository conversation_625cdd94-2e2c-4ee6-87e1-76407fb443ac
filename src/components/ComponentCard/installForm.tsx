import React, { useEffect, useState } from 'react';
import {
	Form,
	Row,
	Col,
	Select,
	Input,
	InputNumber,
	Drawer,
	Divider,
	Space,
	Button,
	Switch,
	AutoComplete,
	Tooltip,
	Tag,
	Checkbox
} from 'antd';
import { ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { IconFont } from '../IconFont';
import { SendDataProps } from './index';
import { AffinityLabelsItem } from '@/pages/ServiceCatalog/catalog';
import { TolerationLabelItem } from '../FormTolerations/formTolerations';
import { AutoCompleteOptionItem } from '@/types/comment';
import { StorageItem } from '@/pages/StorageManagement/storageManage';
import './index.less';
import { labelHigh, labelSimple, PUBLIC_KEY } from '@/utils/const';
import { encrypt } from '@/utils/utils';
import { getNodePort, getNodeTaint } from '@/services/middleware';
const formItemLayout = {
	labelCol: {
		span: 5
	},
	wrapperCol: {
		span: 19
	}
};
const FormItem = Form.Item;
interface installFormProps {
	visible: boolean;
	title: string;
	clusterId: string;
	storageClassList: StorageItem[];
	onCancel: () => void;
	onCreate: (values: SendDataProps) => void;
}
const InstallForm = (props: installFormProps) => {
	const { visible, onCancel, clusterId, title, onCreate, storageClassList } =
		props;
	const [type, setType] = useState<string>('high');
	const [labelList, setLabelList] = useState<AutoCompleteOptionItem[]>([]);
	const [taintList, setTaintList] = useState<AutoCompleteOptionItem[]>([]);
	const [AdvancedChecked, setAdvancedChecked] = useState<boolean>(true);
	const [taintLabel, setTaintLabel] = useState<string>('');
	const [affinityLabels, setAffinityLabels] = useState<AffinityLabelsItem[]>(
		[]
	);
	const [tolerationsLabels, setTolerationsLabels] = useState<
		TolerationLabelItem[]
	>([]);
	const [label, setLabel] = useState<string>('');
	const [required, setRequired] = useState<boolean>(false);
	const [form] = Form.useForm();
	const [storageForm] = Form.useForm();
	const [esForm] = Form.useForm();

	useEffect(() => {
		if (title === 'alertmanager') {
			form.setFieldsValue({
				platformProtocol: 'http',
				platformHost: 'zeus.zeus',
				platformPort: 8080
			});
		}
		getNodePort({ clusterId }).then((res) => {
			if (res.success) {
				const list = res.data.map((item: string) => {
					return {
						value: item,
						label: item
					};
				});
				setLabelList(list);
			}
		});
		getNodeTaint({ clusterid: clusterId }).then((res) => {
			if (res.success) {
				const list = res.data.map((item: string) => {
					return {
						value: item,
						label: item
					};
				});
				setTaintList(list);
			}
		});
	}, []);
	const onOk = async () => {
		let flag = false;
		let esFlag = false;
		try {
			await storageForm.validateFields();
		} catch (error) {
			// 处理 storageForm.validateFields() 函数的错误
			flag = true;
		}
		try {
			await esForm.validateFields();
		} catch (error) {
			// 处理 esForm.validateFields() 函数的错误
			esFlag = true;
		}
		if (title === 'logging') {
			if (flag || esFlag) return;
		} else {
			if (flag) return;
		}
		const values = storageForm.getFieldsValue();
		const esValues = esForm.getFieldsValue();
		const sendData: any = {
			clusterId,
			componentName: title,
			type: type,
			protocol:
				window.location.protocol.toLowerCase() === 'https:'
					? 'https'
					: 'http',
			tolerations: [],
			nodeAffinity: []
		};
		tolerationsLabels.forEach((item) => {
			sendData.tolerations.push(item.label);
		});
		if (title === 'alertmanager') {
			sendData.platformProtocol = form.getFieldValue('platformProtocol');
			sendData.platformPort = form.getFieldValue('platformPort');
			sendData.platformHost = form.getFieldValue('platformHost');
		}
		if (
			title === 'logging' ||
			title === 'grafana' ||
			title === 'prometheus' ||
			title === 'minio'
		) {
			sendData.storageClassName = values.storageClassName;
			sendData.storageSize = values.storageSize + 'GB';
			if (title === 'logging') {
				const password_encrypt =
					encrypt(esValues.password, PUBLIC_KEY) || esValues.password;
				sendData.username = esValues.username;
				sendData.password = password_encrypt;
			}
		}
		if (AdvancedChecked) {
			if (title === 'minio' || title === 'fs-exporter') {
				const nodeSelector: any = {};
				affinityLabels.forEach((item: any) => {
					const label: any = item.label.split('=');
					nodeSelector[label[0]] = label[1];
				});
				sendData.nodeSelector = nodeSelector;
			} else {
				sendData.nodeAffinity = affinityLabels.map((item) => {
					return {
						label: item.label,
						anti: item.anti,
						required
					};
				});
			}
		}
		onCreate(sendData);
		onCancel();
	};
	const onChange = (checked: boolean) => {
		setAdvancedChecked(checked);
	};
	return (
		<Drawer title="工具安装" open={visible} onClose={onCancel} width={600}>
			<div className="install-title-content">
				<div className="install-title-name">选择安装规格</div>
			</div>
			<p className="install-subtitle">
				依据您集群资源的富余情况进行灵活选择
			</p>
			{title === 'alertmanager' && (
				<Form form={form}>
					<FormItem
						{...formItemLayout}
						labelAlign="left"
						label="Zeus-API地址"
						style={{ marginBottom: 0 }}
					>
						<Row>
							<Col span={6}>
								<FormItem name="platformProtocol">
									<Select
										style={{
											width: '100%'
										}}
									>
										<Select.Option value="https">
											https
										</Select.Option>
										<Select.Option value="http">
											http
										</Select.Option>
									</Select>
								</FormItem>
							</Col>
							<Col span={12}>
								<FormItem
									required
									rules={[
										{
											required: true,
											message: '请输入IP地址'
										}
									]}
									style={{
										marginLeft: -2
									}}
									name="platformHost"
								>
									<Input
										type="text"
										placeholder="请输入IP地址"
									/>
								</FormItem>
							</Col>
							<Col span={6}>
								<FormItem
									style={{
										marginLeft: -2
									}}
									required
									rules={[
										{
											required: true,
											message: '请输入端口'
										}
									]}
									name="platformPort"
								>
									<InputNumber
										min={1}
										max={65535}
										placeholder="端口"
										style={{ width: '100%' }}
									/>
								</FormItem>
							</Col>
						</Row>
					</FormItem>
				</Form>
			)}
			<div
				className="install-item"
				style={type === 'simple' ? { border: '1px solid #0064C8' } : {}}
				onClick={() => setType('simple')}
			>
				<div>
					<IconFont
						type="icon-renwushili"
						style={
							type === 'simple'
								? { width: 80, fontSize: 80, color: '#0064C8' }
								: { width: 80, fontSize: 80, color: '#666666' }
						}
					/>
				</div>
				<div className="install-item-info">
					<h2>单实例版</h2>
					<p>
						资源占用少，保证安装后，该工具可用，但是不稳定
						<br />
						所需资源约：{labelSimple[title]}
					</p>
					<IconFont
						type="icon-xuanzhong"
						style={
							type === 'simple'
								? { position: 'absolute', right: 0, bottom: 0 }
								: { visibility: 'hidden' }
						}
					/>
				</div>
			</div>
			<div
				className="install-item"
				style={type === 'high' ? { border: '1px solid #0064C8' } : {}}
				onClick={() => setType('high')}
			>
				<div>
					<IconFont
						type="icon-gaokeyong"
						style={
							type === 'high'
								? { width: 80, fontSize: 80, color: '#0064C8' }
								: { width: 80, fontSize: 80, color: '#666666' }
						}
					/>
				</div>
				<div className="install-item-info">
					<h2>高可用版（推荐）</h2>
					<p>
						资源占用相对多，保证安装后，该工具可用，且稳定
						<br />
						所需资源约{labelHigh[title]}
					</p>
					<IconFont
						type="icon-xuanzhong"
						style={
							type === 'high'
								? { position: 'absolute', right: 0, bottom: 0 }
								: { visibility: 'hidden' }
						}
					/>
				</div>
			</div>
			{(title === 'logging' ||
				title === 'grafana' ||
				title === 'prometheus' ||
				title === 'minio') && (
				<Form form={storageForm}>
					<FormItem
						label="存储选择"
						labelAlign="left"
						{...formItemLayout}
						style={{ marginBottom: 0 }}
					>
						<div className={`form-content display-flex`}>
							<FormItem
								name={'storageClassName'}
								required
								rules={[
									{
										required: true,
										message: '请选择存储'
									}
								]}
								style={{ width: '50%' }}
							>
								<Select
									placeholder="请选择存储"
									style={{ width: '100%' }}
								>
									{storageClassList.map(
										(item: StorageItem) => {
											return (
												<Select.Option
													key={
														item.storageClassList[0]
															.name
													}
													value={
														item.storageClassList[0]
															.name
													}
												>
													{item.aliasName}
													{item.isActiveActive ? (
														<span className="available-domain">
															可用区
														</span>
													) : null}
												</Select.Option>
											);
										}
									)}
								</Select>
							</FormItem>
							<FormItem
								rules={[
									{
										required: true,
										message: '请输入存储配额大小（GB）'
									}
								]}
								name={'storageSize'}
								style={{ width: '50%' }}
							>
								<InputNumber
									style={{ width: '100%' }}
									placeholder="请输入存储配额"
									min={0}
									addonAfter="GB"
								/>
							</FormItem>
						</div>
					</FormItem>
				</Form>
			)}
			{title === 'logging' && (
				<Form form={esForm}>
					<FormItem
						label="ES鉴权"
						labelAlign="left"
						{...formItemLayout}
						style={{ marginBottom: 0 }}
					>
						<div className={`form-content display-flex`}>
							<FormItem
								name="username"
								initialValue="elastic"
								required
								rules={[
									{
										required: true,
										message: '请输入用户名'
									}
								]}
								style={{ width: '50%' }}
							>
								<Input
									disabled
									placeholder="请输入用户名"
									style={{ width: '100%' }}
								/>
							</FormItem>
							<FormItem
								name="password"
								required
								rules={[
									{
										required: true,
										message: '请输入密码'
									}
								]}
								style={{ width: '50%' }}
							>
								<Input.Password
									placeholder="请输入密码"
									style={{ width: '100%' }}
								/>
							</FormItem>
						</div>
					</FormItem>
				</Form>
			)}
			<h2>高级配置</h2>
			<div>
				<FormItem
					label="调度策略"
					labelAlign="left"
					{...formItemLayout}
				>
					<Switch checked={AdvancedChecked} onChange={onChange} />
				</FormItem>
				{AdvancedChecked && (
					<FormItem
						label={
							<span>
								主机亲和{' '}
								<Tooltip title="勾选强制亲和时，服务只会部署在具备相应标签的主机上，若主机资源不足，可能会导致启动失败">
									<ExclamationCircleOutlined
										style={{
											fontSize: 14,
											margin: '0 5px',
											cursor: 'pointer',
											color: '#888888'
										}}
									/>
								</Tooltip>
							</span>
						}
						name="nodeAffinity"
						labelAlign="left"
						{...formItemLayout}
					>
						<div className="display-flex flex-align">
							<Space>
								<AutoComplete
									allowClear
									placeholder="请输入key=value格式的内容"
									value={label}
									style={{ width: 200 }}
									options={labelList}
									onChange={(value) => setLabel(value)}
									status={
										label &&
										!/^[a-zA-Z0-9-./_]+[=]([a-zA-Z0-9-./_]+)?$/.test(
											label
										)
											? 'error'
											: ''
									}
								/>
								<Button
									style={{
										marginLeft: '4px',
										padding: '0 9px'
									}}
									disabled={
										!label ||
										!/^[a-zA-Z0-9-./_]+[=]([a-zA-Z0-9-./_]+)?$/.test(
											label
										)
											? true
											: false
									}
									onClick={() => {
										if (
											!affinityLabels.find(
												(item: any) => {
													return item.label === label;
												}
											)
										) {
											setAffinityLabels([
												...affinityLabels,
												{
													label: label,
													required,
													anti: false,
													id: Math.random()
												}
											]);
										}
									}}
								>
									<PlusOutlined
										style={{
											color: '#005AA5'
										}}
									/>
								</Button>
								<Checkbox
									checked={required}
									onChange={(e) => {
										setRequired(e.target.checked);
									}}
								>
									强制亲和
								</Checkbox>
							</Space>
						</div>
						{label &&
						!/^[a-zA-Z0-9-./_]+[=]([a-zA-Z0-9-./_]+)?$/.test(
							label
						) ? (
							<div
								style={{
									color: '#ff4d4f'
								}}
							>
								请输入key=value格式的内容
							</div>
						) : null}
						{affinityLabels.length > 0 && (
							<div
								className="tag-box"
								style={{ marginLeft: '0px' }}
							>
								<Space wrap>
									{affinityLabels.map((item) => {
										return (
											<Tag
												key={item.label}
												closable
												style={{
													padding: '4px 10px'
												}}
												onClose={() =>
													setAffinityLabels(
														affinityLabels.filter(
															(arr) =>
																arr.id !==
																item.id
														)
													)
												}
											>
												{item.label}
											</Tag>
										);
									})}
								</Space>
							</div>
						)}
					</FormItem>
				)}
				{AdvancedChecked && (
					<FormItem
						label="主机容忍"
						name="tolerations"
						labelAlign="left"
						{...formItemLayout}
					>
						<div className="display-flex flex-align">
							<>
								<Space>
									<AutoComplete
										allowClear
										value={taintLabel}
										style={{ width: 200 }}
										options={taintList}
										onChange={(value) =>
											setTaintLabel(value)
										}
										placeholder="请选择或输入主机容忍"
									/>
									<Button
										style={{
											marginLeft: '4px',
											padding: '0 9px'
										}}
										onClick={() => {
											if (
												!tolerationsLabels.find(
													(item: any) =>
														item.label ===
														taintLabel
												)
											) {
												setTolerationsLabels([
													...tolerationsLabels,
													{
														label: taintLabel,
														id: Math.random()
													}
												]);
											}
										}}
										disabled={!taintLabel ? true : false}
									>
										<PlusOutlined
											style={{
												color: '#005AA5'
											}}
										/>
									</Button>
								</Space>
							</>
						</div>
						{tolerationsLabels.length > 0 && (
							<div
								className="tag-box"
								style={{ marginLeft: '0px' }}
							>
								<Space wrap>
									{tolerationsLabels.map((item) => {
										return (
											<Tag
												key={item.label}
												closable
												style={{
													padding: '4px 10px'
												}}
												onClose={() =>
													setTolerationsLabels(
														tolerationsLabels.filter(
															(arr) =>
																arr.id !==
																item.id
														)
													)
												}
											>
												{item.label}
											</Tag>
										);
									})}
								</Space>
							</div>
						)}
					</FormItem>
				)}
			</div>
			<Divider />
			<Space>
				<Button type="default" onClick={onCancel}>
					取消
				</Button>
				<Button type="primary" onClick={onOk}>
					确定
				</Button>
			</Space>
		</Drawer>
	);
};
export default InstallForm;

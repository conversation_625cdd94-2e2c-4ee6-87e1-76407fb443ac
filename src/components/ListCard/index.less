.zeus-list-card-box {
	width: 100%;
	height: 64px;
	background: #ffffff;
	border: 1px solid rgba(0, 0, 0, 0.15);
	display: flex;
	margin-bottom: @margin-lg;
}
.zeus-list-card-box,
.zeus-list-panel {
	.zeus-list-card-head {
		display: flex;
		width: 200px;
		// * 对于 flex 容器（即父元素），设置 width 属性通常不会生效
		min-width: 200px;
		max-width: 200px;
		padding: 12px;
		margin-left: 24px;
		align-items: center;
		.zeus-list-card-title-content {
			width: 70%;
			.zeus-list-card-title {
				width: 100%;
				height: @line-height-1;
				font-size: @font-1;
				font-weight: @font-weight-sm;
				line-height: @line-height-1;
				.mixin(textEllipsis);
				cursor: pointer;
				& .active {
					color: @primary-color;
				}
			}
			.zeus-list-card-subTitle {
				width: 100%;
				height: @line-height-1;
				font-size: @font-1;
				font-weight: @font-weight-sm;
				line-height: @line-height-1;
				color: rgba(0, 0, 0, 0.45);
				.mixin(textEllipsis);
			}
		}
	}
	.zeus-list-card-content {
		width: 100%;
		display: flex;
		flex-wrap: nowrap;
		justify-content: center;
		column-gap: 50px;
	}
	.zeus-list-card-action {
		min-width: 100px;
		height: 62px;
		line-height: 62px;
		margin-left: auto;
		margin-right: 16px;
	}
}
.zeus-list-card-item-content {
	height: 62px;
	width: 150px;
	padding: 12px;
	display: flex;
	align-items: center;
	.zeus-list-card-item-label,
	.zeus-list-card-item-value {
		width: 100%;
		height: @line-height-1;
		font-size: @font-1;
		font-weight: @font-weight-sm;
		line-height: @line-height-1;
		color: rgba(0, 0, 0, 0.85);
		.mixin(textEllipsis);
	}
	.zeus-list-card-item-label {
		color: rgba(0, 0, 0, 0.45);
	}
}

.ant-collapse {
	margin-bottom: @margin-lg !important;
	.ant-collapse-item.zeus-list-panel {
		.ant-collapse-header {
			width: 100%;
			.ant-collapse-header-text {
				width: 100%;
				.zeus-list-panel-box {
					width: 96%;
					display: flex;
					align-items: center;
					.zeus-list-card-head {
						margin-left: 0px;
					}
				}
			}
			align-items: center;
			padding: 0;
			background: #ffffff;
			.ant-collapse-arrow {
				margin: 0 0 0 12px;
			}
		}
		.zeus-list-card-action {
			margin-right: 40px;
		}
	}
}

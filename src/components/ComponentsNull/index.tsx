import React from 'react';
import { useHistory, useParams } from 'react-router';
import noData from '@/assets/images/nodata.svg';
import { Button } from 'antd';
import storage from '@/utils/storage';
import './index.less';

interface ComponentNullProps {
	title: string;
	onClick?: () => void;
	isAdmin?: boolean;
}
const ComponentNull = (props: ComponentNullProps) => {
	const { title, onClick, isAdmin } = props;
	const params: any = useParams();
	const history = useHistory();
	return (
		<div className="no-data-content">
			<img width={140} height={140} src={noData} />
			<p>{title}</p>
			{!!isAdmin && (
				<Button
					style={{ marginTop: 8 }}
					type="primary"
					onClick={() => {
						if (onClick) {
							onClick();
						} else {
							storage.setSession(
								'cluster-detail-current-tab',
								'component'
							);
							history.push(
								`/platform/clusterManagement/resourcePoolDetail/${params.clusterId}`
							);
						}
					}}
				>
					点击跳转
				</Button>
			)}
		</div>
	);
};
export default ComponentNull;

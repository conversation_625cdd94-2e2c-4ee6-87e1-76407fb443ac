import React, { useEffect, useState } from 'react';
import {
	Popover,
	Switch,
	notification,
	Input,
	Button,
	Row,
	Col,
	Form
} from 'antd';
import { DoubleRightOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import LDAP from '@/assets/images/LDAP.svg';
import { getLDAP, enableLDAP, disableLDAP, checkLDAP } from '@/services/user';
import Email from '@/assets/images/email.svg';
import { connectMail, getMailInfo, setMail } from '@/services/alarm';
import './index.less';
const formItemLayout = {
	labelCol: {
		span: 6
	},
	wrapperCol: {
		span: 18
	}
};

function OpenCenterContent(): JSX.Element {
	const [btnStatus, setBtnStatus] = useState<boolean>(true);
	const [mailBtnStatus, setMailBtnStatus] = useState<boolean>(true);
	const [connect, setConnect] = useState<string>('');
	const [mailConnect, setMailConnect] = useState<string>('');
	const [ldapShow, setLdapShow] = useState<boolean>(true);
	const [emailShow, setEmailShow] = useState<boolean>(true);
	const [formShow, setFormShow] = useState<boolean>(true);
	const [data, setData] = useState();
	const [testMailLoading, setTestMailLoading] = useState<boolean>(false);
	const [mailLoading, setMailLoading] = useState<boolean>(false);
	const [openSSLChecked, setOpenSSLChecked] = useState<boolean>(false);
	const [form] = Form.useForm();
	const [form1] = Form.useForm();

	useEffect(() => {
		getMailInfoData();
	}, []);

	const getMailInfoData = () => {
		getLDAP().then(async (res) => {
			if (!res.data) return;
			await form.setFieldsValue(res.data);
			checkBtn();
			res.data.isOn ? setFormShow(true) : setFormShow(false);
		});
		getMailInfo().then(async (res) => {
			if (!res.data) return;
			await form1.setFieldsValue(res.data);
			setOpenSSLChecked(res.data.openSSL);
			checkMailBtn();
			setData(res.data);
		});
	};

	const submit = () => {
		form.validateFields().then((values) => {
			enableLDAP({ ...values, isOn: '1' }).then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '保存成功'
					});
					getMailInfoData();
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		});
	};
	const mailSubmit = () => {
		form1.validateFields().then((value) => {
			setMailLoading(true);
			setMail(value)
				.then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '邮箱设置成功'
						});
						getMailInfoData();
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				})
				.finally(() => {
					setMailLoading(false);
				});
		});
	};

	const checkBtn = () => {
		const obj: any = {
			ip: null,
			port: null,
			base: null,
			password: null,
			userdn: null,
			objectClass: null,
			searchAttribute: null,
			displayNameAttribute: null,
			...form.getFieldsValue()
		};
		const arr = [];
		for (const key in obj) {
			key !== 'id' && key !== 'isOn' && arr.push(obj[key]);
		}
		arr.every((item) => item) ? setBtnStatus(false) : setBtnStatus(true);
	};
	const checkMailBtn = () => {
		const obj: any = {
			port: null,
			password: null,
			mailServer: null,
			userName: null,
			...form1.getFieldsValue()
		};
		const arr = [];
		console.log(obj);
		for (const key in obj) {
			key !== 'openSSL' &&
				key !== 'time' &&
				key !== 'mailPath' &&
				arr.push(obj[key]);
		}
		arr.every((item) => item)
			? setMailBtnStatus(false)
			: setMailBtnStatus(true);
	};

	const testMail = () => {
		const data: any = form1.getFieldsValue();
		console.log(data);

		const sendData = { ...data };
		setTestMailLoading(true);
		connectMail(sendData)
			.then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '测试完成'
					});
				} else {
					notification.error({
						message: '错误',
						description: res.errorMsg
					});
				}
				res.success ? setMailConnect('good') : setMailConnect('bad');
			})
			.finally(() => {
				setTestMailLoading(false);
			});
	};
	const testLDAP = () => {
		const data: any = form.getFieldsValue();
		delete data.isOn;
		delete data.id;
		checkLDAP(data).then((res) => {
			if (res.data) {
				notification.success({
					message: '成功',
					description: '测试完成'
				});
			} else {
				notification.error({
					message: '失败',
					description: res.errMsg
				});
			}
			res.success ? setConnect('good') : setConnect('bad');
		});
	};

	const changeLDAP = (value: any) => {
		if (formShow) {
			disableLDAP().then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: 'LDAP已关闭'
					});
					setFormShow(value);
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		} else {
			checkBtn();
			form.validateFields()
				.then((values) => {
					enableLDAP({ ...values, isOn: '1' }).then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: 'LDAP已启用'
							});
							setFormShow(value);
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					});
				})
				.catch((errors) => {
					setFormShow(value);
				});
		}
	};

	return (
		<div className="alarm-set">
			<div className="box">
				<div
					className="box-header"
					onClick={() => setLdapShow(!ldapShow)}
				>
					<div className="header-img">
						<img src={LDAP} />
					</div>
					<div className="header-info">
						<div>
							<span className="type">LDAP</span>
							<span
								className={formShow ? 'status' : 'status none'}
							>
								{formShow ? '已启用' : '未启用'}
							</span>
						</div>
						<p>可有效解决多系统账户对接、统一管理问题</p>
					</div>
					<div className="show-box">
						<span className="show-icon">
							<DoubleRightOutlined
								style={{
									color: '#C0C6CC',
									transform: ldapShow
										? 'rotate(90deg)'
										: 'rotate(270deg)'
								}}
							/>
						</span>
					</div>
				</div>
				<div
					className="box-content"
					style={{ display: ldapShow ? 'block' : 'none' }}
				>
					<Row>
						<Col span={13} offset={4}>
							<div
								className={`form-display ${
									!formShow ? 'padding' : ''
								}`}
							>
								<label className="form-name">
									<span style={{ marginRight: 8 }}>
										启用开关
									</span>
									<Popover content="开启LDAP认证会自动禁用系统当前的用户系统，取而代之的是利用对接的LDAP服务器来做用户的登录认证">
										<QuestionCircleOutlined />
									</Popover>
								</label>
								<Switch
									checked={formShow}
									onChange={changeLDAP}
								/>
							</div>
							<Form
								form={form}
								{...formItemLayout}
								style={{
									padding: '24px',
									display: formShow ? 'block' : 'none'
								}}
								labelAlign="left"
							>
								<Form.Item
									label="服务器地址"
									name="ip"
									rules={[
										{
											required: true,
											message: '请输入服务器地址'
										}
									]}
									style={{ position: 'relative' }}
									extra={
										connect && (
											<div className="concat">
												<span
													className={
														connect === 'good'
															? 'good'
															: 'bad'
													}
												>
													{connect === 'good'
														? '可用'
														: '不可用'}
												</span>
											</div>
										)
									}
								>
									<Input
										placeholder="请输入内容"
										onChange={checkBtn}
									/>
								</Form.Item>
								<Form.Item
									name="port"
									rules={[
										{
											required: true,
											message: '请输入端口'
										}
									]}
									label="端口"
								>
									<Input
										placeholder="请输入内容"
										onChange={checkBtn}
									/>
								</Form.Item>
								<Form.Item
									name="base"
									rules={[
										{
											required: true,
											message: '请输入基准DN'
										}
									]}
									label="基准DN"
								>
									<Input
										placeholder="请输入内容"
										onChange={checkBtn}
										onBlur={(e) =>
											form.setFieldsValue({
												mailPath: e.target.value
											})
										}
									/>
								</Form.Item>
								<Form.Item
									name="userdn"
									rules={[
										{
											required: true,
											message: '请输入管理DN'
										}
									]}
									label="管理DN"
								>
									<Input
										placeholder="请输入内容"
										onChange={checkBtn}
									/>
								</Form.Item>
								<Form.Item
									name="password"
									rules={[
										{
											required: true,
											message: '请输入密码'
										}
									]}
									label="密码"
								>
									<Input.Password
										placeholder="请输入内容"
										onChange={checkBtn}
									/>
								</Form.Item>
								<Form.Item
									name="searchAttribute"
									rules={[
										{
											required: true,
											message: '请输入用户属性名'
										}
									]}
									label="用户属性名"
								>
									<Input
										placeholder="请输入内容"
										onChange={checkBtn}
									/>
								</Form.Item>
								<Form.Item
									name="objectClass"
									rules={[
										{
											required: true,
											message: '请输入过滤条件'
										}
									]}
									label="过滤条件"
								>
									<Input
										placeholder="请输入内容"
										onChange={checkBtn}
									/>
								</Form.Item>
								<Form.Item
									name="displayNameAttribute"
									rules={[
										{
											required: true,
											message: '请输入用户姓名的属性名'
										}
									]}
									label="用户姓名的属性名"
								>
									<Input
										placeholder="请输入内容"
										onChange={checkBtn}
									/>
								</Form.Item>
								<div className="btns">
									<Button
										onClick={() => {
											form.resetFields();
											checkBtn();
											setConnect('');
										}}
									>
										重置
									</Button>
									<Button
										className={
											btnStatus
												? 'test normal'
												: 'test error'
										}
										disabled={btnStatus}
										onClick={testLDAP}
										type="primary"
									>
										连接测试
									</Button>
									<Button
										className={
											btnStatus
												? 'save normal'
												: 'save error'
										}
										disabled={btnStatus}
										onClick={submit}
										type="primary"
									>
										保存
									</Button>
								</div>
							</Form>
						</Col>
					</Row>
				</div>
			</div>
			<div className="box">
				<div
					className="box-header"
					onClick={() => setEmailShow(!emailShow)}
				>
					<div className="header-img">
						<img src={Email} />
					</div>
					<div className="header-info">
						<div>
							<span className="type">邮箱</span>
							<span className={data ? 'status' : 'status none'}>
								{data ? '已设置' : '未设置'}
							</span>
						</div>
						<p>
							设置一个能正常收、发邮件到邮件服务器，告警信息第一时间通过邮件告知，及时、高效、规范。
						</p>
					</div>
					<div className="show-box">
						<span className="show-icon">
							<DoubleRightOutlined
								style={{
									color: '#C0C6CC',
									transform: emailShow
										? 'rotate(90deg)'
										: 'rotate(270deg)'
								}}
							/>
						</span>
					</div>
				</div>
				<div
					className="box-content"
					style={{ display: emailShow ? 'block' : 'none' }}
				>
					<Row>
						<Col span={13} offset={4}>
							<Form
								form={form1}
								{...formItemLayout}
								style={{ padding: '24px' }}
							>
								<Form.Item
									rules={[
										{
											required: true,
											message: '请输入邮箱服务器'
										}
									]}
									label="邮箱服务器"
									name="mailServer"
								>
									<Input
										placeholder="请输入邮箱服务器"
										onChange={checkMailBtn}
									/>
								</Form.Item>
								{mailConnect && (
									<div className="concat">
										<span
											className={
												mailConnect === 'good'
													? 'good'
													: 'bad'
											}
										>
											{mailConnect === 'good'
												? '可用'
												: '不可用'}
										</span>
									</div>
								)}
								<Form.Item
									rules={[
										{
											required: true,
											message: '请输入端口'
										}
									]}
									label="端口"
									name="port"
								>
									<Input
										placeholder="请输入端口"
										onChange={checkMailBtn}
									/>
								</Form.Item>
								<Form.Item
									rules={[
										{
											required: true,
											message: '请输入邮箱'
										}
									]}
									label="邮箱"
									name="userName"
								>
									<Input
										placeholder="请输入邮箱"
										onChange={checkMailBtn}
									/>
								</Form.Item>
								<Form.Item
									rules={[
										{
											required: true,
											message: '请输入授权码'
										}
									]}
									label="授权码"
									name="password"
								>
									<Input.Password
										placeholder="请输入授权码"
										onChange={checkMailBtn}
									/>
								</Form.Item>
								<Form.Item
									rules={[
										{
											required: true,
											message: '请选择SSL开关'
										}
									]}
									label="SSL开关"
									name="openSSL"
									initialValue={false}
								>
									<Switch
										checked={openSSLChecked}
										onChange={(checked) =>
											setOpenSSLChecked(checked)
										}
									/>
								</Form.Item>
								<div className="btns">
									<Button
										onClick={() => {
											form1.resetFields();
											checkMailBtn();
											setMailConnect('');
										}}
									>
										重置
									</Button>
									<Button
										className={
											mailBtnStatus
												? 'test normal'
												: 'test error'
										}
										disabled={mailBtnStatus}
										onClick={testMail}
										type="primary"
										loading={testMailLoading}
									>
										连接测试
									</Button>
									<Button
										className={
											mailBtnStatus
												? 'save normal'
												: 'save error'
										}
										disabled={mailBtnStatus}
										onClick={mailSubmit}
										loading={mailLoading}
										type="primary"
									>
										保存
									</Button>
								</div>
							</Form>
						</Col>
					</Row>
				</div>
			</div>
		</div>
	);
}

export default OpenCenterContent;

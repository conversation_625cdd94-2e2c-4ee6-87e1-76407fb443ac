export interface middlewareProps {
	chartName: string;
	chartVersion: string;
	createTime: string | null;
	description: string;
	grafanaId: null;
	id: number;
	image: string | null;
	imagePath: string | null;
	middlewares: null;
	name: string;
	official: true;
	replicas: null;
	vendor?: string;
	replicasStatus: null;
	status: number;
	type: string | null;
	version: string;
	versionStatus: null;
}
export interface middlewareItemProps extends middlewareProps {
	clusterId?: string;
	onRefresh: () => void;
	enable: boolean;
	disabled?: boolean;
	isManage?: boolean;
	withMiddleware?: boolean;
	onEnableChange?: (value: boolean) => void;
}
export interface middlewareListProps {
	[propsName: string]: middlewareProps[];
}
export interface paramsProps {
	type: string;
	clusterId: string;
}

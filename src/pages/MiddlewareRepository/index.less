.middleware-repository-tips {
	width: 100%;
	height: 34px;
	background: #F8F8F9;
	border-radius: @border-radius;
	color: @text-color-title;
	padding: 8px 16px;
}
.middleware-repository-action-layout {
	display: flex;
	justify-content: space-between;
	margin-top: 24px;
	.middleware-repository-right-layout{
		display: flex;
		.middleware-repository-refresh-btn {
			width: 32px;
			height: 32px;
			border-radius: @border-radius;
			border: 1px solid #C0C6CC;
			text-align: center;
			line-height: 32px;
			margin-left: 8px;
			cursor: pointer;
		}
	}
}
.middleware-repository-list-display {
	.middleware-repository-list-item {
		width: 100%;
		height: auto;
		padding: 16px 20px;
		background-color: @white;
		margin-top: 13px;
		box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.09);
		& > p {
			font-size: @font-2;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: @font-weight;
			color: @text-color-title;
			@include lineHeight(24px);
		}
		.middleware-repository-list-content {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			margin-top: 16px;
		}
	}
}
.middleware-version-content{
	margin-top: 16px;
}
.version-status-display{
	height: 22px;
	line-height: 19px;
	text-align: center;
	border: 1px solid;
	display: inline-block;
	padding: 1px 10px;
}

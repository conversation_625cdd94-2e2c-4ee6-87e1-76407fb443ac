import React, { useEffect, useState } from 'react';
import { Modal, Button, notification, Alert } from 'antd';

import { ProPage, ProContent, ProHeader } from '@/components/ProPage';
import { useHistory, useParams } from 'react-router-dom';
import OperatorInstallForm from '@/components/OperatorInstallForm';

import moment from 'moment';

import Actions from '@/components/Actions';
import ProTable from '@/components/ProTable';
import UploadMiddlewareForm from '../ServiceCatalog/components/UploadMiddlewareForm';

import {
	getTypeVersion,
	updateMiddleware,
	shelvesTypeVersion
} from '@/services/repository';
import { paramsProps } from './middleware';
import { getMiddlewareRepository } from '@/services/repository';

import { middlewareProps } from './middleware';
import { iconTypeRender } from '@/utils/utils';
import { versionStatus } from '@/utils/enum';

import './index.less';

const LinkButton = Actions.LinkButton;
function MiddlewareVersion(): JSX.Element {
	const params: paramsProps = useParams();
	const history = useHistory();
	const [originData, setOriginData] = useState<middlewareProps[]>([]);
	const [dataSource, setDataSource] = useState<middlewareProps[]>([]);
	const [visible, setVisible] = useState<boolean>(false);
	const [installVisible, setInstallVisible] = useState<boolean>(false);
	const [record, setRecord] = useState<middlewareProps>();
	const [status, setStatus] = useState<number>();
	const [loading, setLoading] = useState<boolean>(false);

	useEffect(() => {
		async function getAllData() {
			setLoading(true);
			await getData();
			setLoading(false);
		}
		getAllData();
	}, []);
	const getData = async () => {
		const res1 = await getMiddlewareRepository({
			clusterId: params.clusterId
		});
		if (res1.success) {
			setStatus(
				res1.data.find((item: any) => item.chartName === params.type)
					?.status
			);
		}
		const res2 = await getTypeVersion({
			clusterId: params.clusterId,
			type: params.type
		});
		if (res2.success) {
			setOriginData(res2.data);
		} else {
			notification.error({
				message: '失败',
				description: res2.errorMsg
			});
		}
	};
	useEffect(() => {
		setDataSource([...originData]);
	}, [originData]);
	const onCreate = () => {
		setVisible(false);
		getData();
	};
	const Operation = {
		primary: (
			<Button onClick={() => setVisible(true)} type="primary">
				上架新版本
			</Button>
		)
	};
	const versionStatusRender = (value: string) => {
		const color =
			value === 'now'
				? '#00A7FA'
				: value === 'future' || value === 'updating'
				? '#52C41A'
				: '#666666';
		const bgColor =
			value === 'now'
				? '#EBF8FF'
				: value === 'future' || value === 'updating'
				? '#F6FFED'
				: '#F5F5F5';
		return (
			<div
				className="version-status-display"
				style={{
					color: color,
					backgroundColor: bgColor,
					borderColor: color
				}}
			>
				{versionStatus[value]}
			</div>
		);
	};
	const actionRender = (
		value: string,
		record: middlewareProps,
		index: number
	) => {
		return (
			<Actions>
				{record.versionStatus === 'future' && (
					<LinkButton
						disabled={dataSource.some(
							(item) => item.versionStatus === 'updating'
						)}
						onClick={() => {
							if (status === 2) {
								setRecord(record);
								setInstallVisible(true);
							} else {
								installUpdate(record);
							}
						}}
					>
						安装升级
					</LinkButton>
				)}
				{record.versionStatus === 'updating' && (
					<LinkButton>升级中...</LinkButton>
				)}
				{record.versionStatus !== 'now' && (
					<LinkButton
						disabled={record.versionStatus === 'updating'}
						onClick={() => shelves(record)}
					>
						下架
					</LinkButton>
				)}
			</Actions>
		);
	};
	const installUpdate = (record: middlewareProps) => {
		Modal.confirm({
			title: '操作确认',
			content: '是否确认升级到该版本？',
			okText: '确认',
			cancelText: '取消',
			onOk: () => {
				return updateMiddleware({
					clusterId: params.clusterId,
					chartName: record.chartName,
					chartVersion: record.chartVersion
				})
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '已升级到该版本'
							});
						} else {
							Modal.error({
								title: '失败',
								content:
									'升级失败，已维持升级前状态不变，可重试',
								okText: '我知道了'
							});
						}
					})
					.finally(() => {
						getData();
					});
			}
		});
	};
	const shelves = (record: middlewareProps) => {
		Modal.confirm({
			title: '操作确认',
			content: '是否确认下架该版本中间件？',
			okText: '确认',
			cancelText: '取消',
			onOk: () => {
				return shelvesTypeVersion({
					chartName: record.chartName,
					chartVersion: record.chartVersion
				})
					.then((res) => {
						if (res.success) {
							if (dataSource.length === 1)
								history.push('/platform/marketManagement');
							notification.success({
								message: '成功',
								description: '已下架该版本'
							});
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					})
					.finally(() => {
						getData();
					});
			}
		});
	};

	return (
		<ProPage>
			<ProHeader
				title={`${params.type}版本管理`}
				onBack={() => window.history.back()}
			/>
			<ProContent>
				<Alert
					message="本系统范围内其它集群使用过的中间件版本，都可以自主选择是否安装升级到更新版本"
					type="warning"
				/>
				<div className="middleware-version-content">
					<ProTable
						dataSource={dataSource}
						showRefresh
						onRefresh={getData}
						rowKey="chartVersion"
						operation={Operation}
						loading={loading}
					>
						<ProTable.Column
							title="类型"
							dataIndex="chartName"
							render={iconTypeRender}
						/>
						<ProTable.Column
							ellipsis
							title="描述"
							dataIndex="description"
						/>
						<ProTable.Column
							title="版本状态"
							dataIndex="versionStatus"
							render={versionStatusRender}
							filters={[
								{ text: '当前版本', value: 'now' },
								{ text: '可安装升级版本', value: 'future' },
								{ text: '历史版本', value: 'history' },
								{ text: '升级中', value: 'updating' }
							]}
							filterMultiple={false}
							onFilter={(value, record: any) =>
								record.versionStatus === value
							}
							width={200}
						/>
						<ProTable.Column
							title="版本"
							dataIndex="chartVersion"
							width={100}
						/>
						<ProTable.Column
							title="上架时间"
							dataIndex="createTime"
							render={(text: string) => (
								<span>
									{moment(text).format('YYYY-MM-DD HH:mm:ss')}
								</span>
							)}
							width={200}
							sorter={(a: any, b: any) =>
								moment(a.createTime).unix() -
								moment(b.createTime).unix()
							}
						/>
						<ProTable.Column
							title="操作"
							dataIndex="action"
							width={150}
							render={actionRender}
						/>
					</ProTable>
				</div>
			</ProContent>
			{visible && (
				<UploadMiddlewareForm
					visible={visible}
					onCancel={() => setVisible(false)}
					onCreate={onCreate}
					clusterId={params.clusterId}
				/>
			)}
			{installVisible && record && (
				<OperatorInstallForm
					visible={installVisible}
					clusterId={params.clusterId}
					chartName={params.type.toLocaleLowerCase()}
					chartVersion={record.chartVersion}
					onRefresh={getData}
					onCancel={() => setInstallVisible(false)}
				/>
			)}
		</ProPage>
	);
}
export default MiddlewareVersion;

.work-order-detail-content,
.work-order-form-content {
	display: flex;
	.work-order-detail-left,
	.work-order-form-left {
		width: 70%;
	}
	.work-order-detail-right,
	.work-order-form-right {
		width: 30%;
	}
}
.work-order-step-item-content {
	display: flex;
	margin-top: 8px;
	.work-order-step-item-left {
		width: 12px;
		position: relative;
		margin-top: 4px;
		height: inherit;
		.work-order-step-item-circle {
			width: 12px;
			height: 12px;
			border-radius: 6px;
			border: 1px solid rgba(151, 151, 151, 1);
		}
		.work-order-step-item-line {
			position: absolute;
			width: 1px;
			height: 100%;
			border-left: 1px solid rgba(151, 151, 151, 1);
			left: 5px;
		}
	}
	.work-order-step-item-right {
		width: 350px;
		margin-left: 4px;
		.work-order-step-item-title,
		.work-order-step-item-user {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
		.work-order-step-item-title {
			.ant-space-item:nth-of-type(2) {
				width: 100%;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
			.work-order-step-item-role {
				font-size: @font-2;
				font-weight: @font-weight;
			}
		}
		.work-order-step-item-user {
			margin-top: @margin;
			.work-order-more-user {
				display: inline-block;
				width: 32px;
				height: 32px;
				border-radius: 16px;
				line-height: 32px;
				background-color: rgba(34, 110, 231, 0.08);
				text-align: center;
				font-size: 16px;
				cursor: pointer;
				margin-bottom: 22px;
			}
		}
		.work-order-step-item-comment {
			margin-top: 4px;
			width: 100%;
			color: @text-color-secondary;
			& > p:nth-child(1) {
				font-weight: @font-weight-lg;
				color: @text-color-title;
			}
		}
	}
}
.user-transfer,
.role-transfer {
	.transfer-container {
		width: 46%;
		height: 430px;
		border: 1px solid #d7d7d7;
		.transfer-header {
			width: 100%;
			height: 32px;
			padding: 0 12px;
			line-height: 32px;
			border-bottom: 1px solid #d7d7d7;
		}
		.transfer-content {
			height: 276px;
			overflow: auto;
			&.right {
				height: 324px;
			}
			.role-checkbox-list {
				width: 100%;
				.ant-collapse-content-box {
					display: flex;
					flex-direction: column;
					.ant-checkbox-wrapper + .ant-checkbox-wrapper {
						margin-left: 0;
						margin-top: 4px;
					}
				}
			}
			.select-role-list + .select-role-list {
				margin-top: 4px;
			}
		}
	}
}

.role-transfer {
	.transfer-container {
		.transfer-content {
			height: 326px;
		}
	}
}

.progress-manage-name-list {
	max-width: 300px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

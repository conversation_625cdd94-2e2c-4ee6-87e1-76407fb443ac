import * as React from 'react';
import { useState, useEffect } from 'react';
import { <PERSON><PERSON>ontent, ProHeader, ProPage } from '@/components/ProPage';
import { useHistory, useParams } from 'react-router';
import { Button, Radio, RadioChangeEvent, Space, notification } from 'antd';
import UserTransfer from './components/userTransfer';
import RoleTransfer from './components/roleTransfer';
import { editWorkFlow, getWorkFlowDetail } from '@/services/workOrder';
import { workOrderTopic } from '@/utils/const';

const Initiator = () => {
	const params: { uid: string; type: string } = useParams();
	const history = useHistory();
	const [data, setData] = useState<any>();
	const [type, setType] = useState<string>('AnyRole');
	const [userValue, setUserValue] = useState<any>();
	const [roleValue, setRoleValue] = useState<any>();
	const [btnLoading, setBtnLoading] = useState<boolean>(false);

	useEffect(() => {
		getWorkFlowDetail({ uid: params.uid }).then((res) => {
			if (res.success) {
				const linkList = res.data?.linkList.map((item: any) => ({
					...item,
					id: Math.random()
				}));
				setData({ ...res.data, linkList });
				setType(res.data?.linkList?.[0]?.manager?.managerType);
				if (res.data?.linkList?.[0]?.manager) {
					if (
						res.data?.linkList?.[0]?.manager?.managerType ===
						'AnyUser'
					) {
						setUserValue(
							res.data?.linkList?.[0]?.manager?.managerList
						);
						setRoleValue([]);
					} else {
						setUserValue([]);
						setRoleValue(
							res.data?.linkList?.[0]?.manager?.managerList
						);
					}
				}
			} else {
				notification.error({
					message: '失败',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	}, []);

	const onUserChange = (sendData: any) => {
		setUserValue(sendData);
	};

	const onRoleChange = (sendData: any) => {
		setRoleValue(sendData);
	};

	const onOk = () => {
		setBtnLoading(true);
		let manager: any = {};
		if (type === 'AnyRole') {
			manager = {
				managerType: 'AnyRole',
				managerList: roleValue?.map((item: any) => ({
					...item,
					classType: 'role'
				}))
			};
		} else {
			manager = {
				managerType: 'AnyUser',
				managerList: userValue?.map((item: any) => ({
					...item,
					classType: 'user'
				}))
			};
		}
		editWorkFlow({
			uid: params.uid,
			name: workOrderTopic[params.type],
			linkList: [{ manager }]
		}).then((res) => {
			if (res.success) {
				setBtnLoading(false);
				history.goBack();
				notification.success({
					message: '成功',
					description: '修改成功'
				});
			} else {
				notification.error({
					message: '失败',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};

	return (
		<ProPage>
			<ProHeader
				title={`谁能发起（${workOrderTopic[params.type]}）`}
				onBack={() => history.goBack()}
			/>
			<ProContent>
				<h2>选择方式</h2>
				<Radio.Group
					value={type}
					style={{ margin: '0 0 24px 24px' }}
					onChange={(e: RadioChangeEvent) => setType(e.target.value)}
				>
					<Radio value="AnyRole">按角色选择</Radio>
					<Radio value="AnyUser">按用户选择</Radio>
				</Radio.Group>
				<h2>选择目标</h2>
				{type === 'AnyUser' ? (
					<UserTransfer
						userValue={userValue}
						initValue={userValue}
						onUserChange={onUserChange}
					/>
				) : (
					<RoleTransfer
						roleValue={roleValue}
						initValue={roleValue}
						onRoleChange={onRoleChange}
					/>
				)}
				<div style={{ marginTop: 24 }}>
					<Space>
						<Button onClick={() => history.goBack()}>取消</Button>
						<Button
							type="primary"
							onClick={onOk}
							loading={btnLoading}
						>
							确定
						</Button>
					</Space>
				</div>
			</ProContent>
		</ProPage>
	);
};

export default Initiator;

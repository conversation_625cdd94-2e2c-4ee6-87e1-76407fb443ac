import * as React from 'react';
import { useState, useEffect } from 'react';
import { <PERSON><PERSON>ontent, ProHeader, ProPage } from '@/components/ProPage';
import { Button, notification } from 'antd';
import ProTable from '@/components/ProTable';
import Actions from '@/components/Actions';
import { useHistory } from 'react-router';
import { getWorkFlow } from '@/services/workOrder';
import { nullRender } from '@/utils/utils';
import './index.less';

const ProgressManage = () => {
	const history = useHistory();
	const [dataSource, setDataSource] = useState<any[]>([]);

	useEffect(() => {
		getData();
	}, []);

	const getData = () => {
		getWorkFlow().then((res) => {
			if (res.success) {
				setDataSource(res.data);
			} else {
				notification.error({
					message: '失败',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};

	const userRender = (value: any, record: any) => {
		const name = value?.[0]?.manager?.managerList
			.map((item: any) => item.aliasName)
			?.join('、');
		return (
			<div className="display-flex flex-align">
				<div className="progress-manage-name-list">{name}</div>
				<Button
					type="link"
					onClick={() =>
						history.push(
							`/workspace/workOrderManage/progressManage/initiator/${record.type}/${record.uid}`
						)
					}
				>
					修改
				</Button>
			</div>
		);
	};

	const actionRender = (value: unknown, record: any) => {
		return (
			<Actions>
				<Actions.LinkButton
					onClick={() =>
						history.push(
							`/workspace/workOrderManage/progressManage/edit/${record.type}/${record.uid}`
						)
					}
				>
					编辑
				</Actions.LinkButton>
			</Actions>
		);
	};

	return (
		<ProPage>
			<ProHeader
				title="流程管理"
				onBack={() => {
					history.goBack();
				}}
			/>
			<ProContent>
				<ProTable
					rowKey="uid"
					showRefresh
					onRefresh={getData}
					dataSource={dataSource}
				>
					<ProTable.Column title="工单类型" dataIndex="name" />
					<ProTable.Column
						title="谁能发起"
						dataIndex="linkList"
						render={userRender}
					/>
					<ProTable.Column
						title="最后更新时间"
						dataIndex="lastUpdateTime"
						render={nullRender}
					/>
					<ProTable.Column title="操作" render={actionRender} />
				</ProTable>
			</ProContent>
		</ProPage>
	);
};

export default ProgressManage;

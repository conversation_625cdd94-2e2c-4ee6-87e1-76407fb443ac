import React, { useState, useEffect } from 'react';
import { Button, Space, Tabs, notification } from 'antd';
import { useParams, useHistory } from 'react-router';
import { ProContent, ProHeader, ProPage } from '@/components/ProPage';
import { ReloadOutlined } from '@ant-design/icons';
import Pending from './Pending';
import Processed from './Processed';
import Initiated from './Initiated';
import Received from './Received';
import { getWorkFlow } from '@/services/workOrder';
import Auth from '@/components/Auth';

export default function WorkOrderManagement(): JSX.Element {
	const params: { activeKey: string } = useParams();
	const history = useHistory();
	const [activeKey, setActiveKey] = useState<string>(params.activeKey);
	const [refreshKey, setRefreshKey] = useState<number>(0);
	const [visible, setVisible] = useState<boolean>(false);

	useEffect(() => {
		getWorkFlow().then((res) => {
			if (res.success) {
				setVisible(res.data?.length ? true : false);
			} else {
				notification.error({
					message: '失败',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	}, []);

	const onTabClick = (key: string) => {
		setActiveKey(key);
		history.push(`/workspace/workOrderManage/${key}`);
	};
	const onRefresh = () => {
		setRefreshKey((pre) => pre + 1);
	};
	const items = [
		{
			label: '待处理',
			key: 'pending',
			children: <Pending refreshKey={refreshKey} />
		},
		{
			label: '已处理',
			key: 'processed',
			children: <Processed refreshKey={refreshKey} />
		},
		{
			label: '已发起',
			key: 'initiated',
			children: <Initiated refreshKey={refreshKey} />
		},
		{
			label: '我收到的',
			key: 'received',
			children: <Received refreshKey={refreshKey} />
		}
	];
	return (
		<ProPage>
			<ProHeader
				title="工单管理"
				extra={
					<Space>
						{visible ? (
							<Auth code="flowManagement">
								<Button
									type="primary"
									onClick={() =>
										history.push(
											'/workspace/workOrderManage/progressManage/list'
										)
									}
								>
									流程管理
								</Button>
							</Auth>
						) : null}
						<Button onClick={onRefresh} icon={<ReloadOutlined />} />
					</Space>
				}
			/>
			<ProContent>
				<Tabs
					items={items}
					activeKey={activeKey}
					destroyInactiveTabPane
					onTabClick={onTabClick}
				/>
			</ProContent>
		</ProPage>
	);
}

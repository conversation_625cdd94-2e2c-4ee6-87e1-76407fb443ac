import React, { useState } from 'react';
import { Form, Input, Modal, notification } from 'antd';
import { withdrawOrder } from '@/services/workOrder';
import { formItemLayout618 } from '@/utils/const';

export default function WithdrawOrder({
	open,
	onCancel,
	orderId,
	onRefresh
}: {
	open: boolean;
	onCancel: () => void;
	orderId: string;
	onRefresh: () => void;
}): JSX.Element {
	const [form] = Form.useForm();
	const [btnLoading, setBtnLoading] = useState<boolean>(false);
	const onOk = async () => {
		await form.validateFields();
		setBtnLoading(true);
		const values = form.getFieldsValue();
		withdrawOrder({
			orderId,
			comment: values.comment
		}).then((res) => {
			setBtnLoading(false);
			if (res.success) {
				notification.success({
					message: '成功',
					description: '工单撤回成功！'
				});
				onCancel();
				onRefresh();
			} else {
				notification.error({
					message: '失败',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	return (
		<Modal
			title="撤回工单"
			onOk={onOk}
			onCancel={onCancel}
			open={open}
			okButtonProps={{ loading: btnLoading }}
		>
			<Form form={form} {...formItemLayout618} labelAlign="left">
				<Form.Item
					label="撤回理由"
					name="comment"
					requiredMark="optional"
					colon={false}
					rules={[
						{
							required: true,
							message: '请输入长度为1-64的撤回理由'
						},
						{
							type: 'string',
							min: 1,
							max: 64,
							message: '请输入长度为1-64的撤回理由'
						}
					]}
				>
					<Input.TextArea rows={3} placeholder="请输入撤回理由" />
				</Form.Item>
			</Form>
		</Modal>
	);
}

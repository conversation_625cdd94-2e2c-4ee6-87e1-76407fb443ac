import * as React from 'react';
import { useState, useEffect } from 'react';
import { <PERSON><PERSON>ontent, ProHeader, ProPage } from '@/components/ProPage';
import {
	Steps,
	Form,
	Button,
	notification,
	Space,
	Tag,
	Modal,
	Tooltip
} from 'antd';
import { PlusOutlined, QuestionCircleFilled } from '@ant-design/icons';
import { formItemLayout410, workOrderTopic } from '@/utils/const';
import { getWorkFlowDetail, editWorkFlow } from '@/services/workOrder';
import { useHistory, useParams } from 'react-router';
import WorkProgressStep from './components/WorkProgressStep';
import AdvancedConfig from './components/AdvancedConfig';
import UserTransfer from './components/userTransfer';
import storage from '@/utils/storage';

const { Step } = Steps;
const EditWorkOrder = () => {
	const [form] = Form.useForm();
	const history = useHistory();
	const role = JSON.parse(storage.getLocal('role'));
	const params: { uid: string; type: string } = useParams();
	const [data, setData] = useState<any>();
	const [current, setCurrent] = useState<number>(0);
	const [userValue, setUserValue] = useState<any>();
	const [managerList, setManagerList] = useState<any>([]);
	const [visible, setVisible] = useState<boolean>(false);
	const [removeId, setRemoveId] = useState<string | number>();
	const [removeManagerId, setRemoveManagerId] = useState<string | number>();
	const [btnDisabled, setBtnDisabled] = useState<boolean>(false);
	const [btnLoading, setBtnLoading] = useState<boolean>(false);

	useEffect(() => {
		getDetail();
	}, []);

	useEffect(() => {
		const manager = data?.linkList?.[0]?.manager;
		const linkList = data?.linkList || [];
		linkList.splice(0, 1, {
			...linkList?.[0],
			manager: {
				managerList: manager?.managerList.filter(
					(item: any) => item.id !== removeId
				),
				managerType: manager?.managerType
			}
		});
		setManagerList(managerList.filter((item: any) => item.id !== removeId));
		removeId && editWorkFlow({ ...data, linkList: [...linkList] });
	}, [removeId]);

	useEffect(() => {
		const manager = data?.manager;
		setData({
			...data,
			manager: {
				managerList: manager?.managerList.filter(
					(item: any) => item.id !== removeManagerId
				),
				managerType: manager?.managerType
			}
		});
		removeManagerId &&
			editWorkFlow({
				...data,
				manager: {
					managerList: manager?.managerList.filter(
						(item: any) => item.id !== removeManagerId
					),
					managerType: manager?.managerType
				}
			});
	}, [removeManagerId]);

	const getDetail = () => {
		getWorkFlowDetail({ uid: params.uid }).then((res) => {
			if (res.success) {
				const linkList = res.data?.linkList.map((item: any) => ({
					...item,
					id: Math.random()
				}));
				setData({ ...res.data, linkList });
				setUserValue(res.data?.manager?.managerList);
				setManagerList(
					res.data?.linkList?.[0]?.manager?.managerList || []
				);
			} else {
				notification.error({
					message: '失败',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};

	const childrenRender = (index: number) => {
		switch (index) {
			case 0:
				return (
					<Form form={form} labelAlign="left" {...formItemLayout410}>
						<Form.Item
							label="工单类型名称"
							initialValue={workOrderTopic[params.type]}
						>
							{workOrderTopic[params.type]}
						</Form.Item>
						<Form.Item label="谁能发起">
							<Space
								wrap
								style={{
									width: '100%',
									padding: '5px 8px',
									border: '1px solid #d7d7d7'
								}}
							>
								{managerList.map((item: any) => {
									return (
										<Tag
											key={item.name}
											closable
											onClose={(e) => {
												e.preventDefault();
												setRemoveId(item.id);
											}}
										>
											{item.aliasName}
										</Tag>
									);
								})}
								<Button
									type="dashed"
									style={{ height: 22, padding: '0 8px' }}
									icon={
										<PlusOutlined
											style={{ color: '#999' }}
										/>
									}
									onClick={() =>
										history.push(
											`/workspace/workOrderManage/progressManage/initiator/${params.type}/${params.uid}`
										)
									}
								>
									添加
								</Button>
							</Space>
						</Form.Item>
						<Form.Item
							label={
								<Space>
									工单管理员
									<Tooltip title="选中人员拥有此类工单的管理权限">
										<QuestionCircleFilled
											style={{ cursor: 'pointer' }}
										/>
									</Tooltip>
								</Space>
							}
						>
							<Space
								wrap
								style={{
									width: '100%',
									padding: '5px 8px',
									border: '1px solid #d7d7d7'
								}}
							>
								{data?.manager?.managerList?.map(
									(item: any) => {
										return (
											<Tag
												key={item.name}
												closable={role.isAdmin}
												onClose={(e) => {
													e.preventDefault();
													setRemoveManagerId(item.id);
												}}
											>
												{item.aliasName}
											</Tag>
										);
									}
								)}
								<Button
									type="dashed"
									disabled={!role.isAdmin}
									style={{ height: 22, padding: '0 8px' }}
									icon={
										<PlusOutlined
											style={{ color: '#999' }}
										/>
									}
									onClick={() => setVisible(true)}
								>
									添加
								</Button>
							</Space>
						</Form.Item>
					</Form>
				);
			case 1:
				return (
					<WorkProgressStep
						initData={data}
						setBtnDisabled={setBtnDisabled}
					/>
				);
			case 2:
				return (
					<AdvancedConfig
						data={data}
						onChange={(value: any) => setData(value)}
					/>
				);
			default:
				break;
		}
	};

	const goPrev = () => {
		setCurrent(current - 1);
	};

	const goNext = () => {
		setCurrent(current + 1);
	};

	const onCreate = () => {
		setBtnLoading(true);
		editWorkFlow(data).then((res) => {
			if (res.success) {
				history.goBack();
				setBtnLoading(false);
				notification.success({
					message: '成功',
					description: '编辑成功'
				});
			} else {
				notification.error({
					message: '失败',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};

	const onUserChange = (sendData: any) => {
		setUserValue(sendData);
	};

	const onOk = () => {
		let manager: any = {};
		manager = {
			managerType: 'AnyUser',
			managerList: userValue.map((item: any) => ({
				...item,
				classType: 'user'
			}))
		};
		editWorkFlow({
			manager,
			uid: params.uid,
			name: workOrderTopic[params.type]
		}).then((res) => {
			if (res.success) {
				getDetail();
				setVisible(false);
			}
		});
	};

	return (
		<ProPage>
			<ProHeader
				title={`工单编辑（${workOrderTopic[params.type]}）`}
				onBack={() => history.goBack()}
			/>
			<ProContent>
				<Steps current={current}>
					<Step title="基础信息" />
					<Step title="流程设置" />
					<Step title="高级配置" />
				</Steps>
				<div style={{ marginTop: 36 }}></div>
				{childrenRender(current)}
				{(current === 0 || current === 1 || current === 2) && (
					<div className={`zeus-edit-param-summit-btn`}>
						<Button
							danger
							onClick={() => {
								window.history.back();
							}}
						>
							取消
						</Button>
						{current !== 0 && (
							<Button type="default" onClick={goPrev}>
								上一步
							</Button>
						)}
						{current === 2 ? (
							<Button
								type="primary"
								onClick={onCreate}
								loading={btnLoading}
							>
								完成
							</Button>
						) : (
							<Button
								type="primary"
								onClick={goNext}
								disabled={current == 1 && btnDisabled}
							>
								下一步
							</Button>
						)}
					</div>
				)}
			</ProContent>
			{visible && (
				<Modal
					width={1300}
					title="工单管理员"
					open={visible}
					onOk={onOk}
					onCancel={() => setVisible(false)}
				>
					<UserTransfer
						userValue={data?.manager?.managerList}
						initValue={data?.manager?.managerList}
						onUserChange={onUserChange}
						isManager={true}
					/>
				</Modal>
			)}
		</ProPage>
	);
};

export default EditWorkOrder;

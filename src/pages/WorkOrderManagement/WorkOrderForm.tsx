import React, { useEffect, useState } from 'react';
import {
	Button,
	Cascader,
	Divider,
	Form,
	Input,
	Select,
	Space,
	Spin,
	notification,
	DatePicker
} from 'antd';
import type { RangePickerProps } from 'antd/es/date-picker';
import { useHistory, useParams } from 'react-router';
import moment from 'moment';
import { DefaultOptionType } from 'antd/lib/cascader';
import { ProContent, ProHeader, ProPage } from '@/components/ProPage';
import storage from '@/utils/storage';
import {
	CONTROLLED_OPERATION_BASE,
	CONTROLLED_OPERATION_Maintenance,
	CONTROLLED_OPERATION_EXPERT,
	// MIDDLEWARE_DECOMMISSIONING,
	MIDDLEWARE_DEPLOYMENT,
	QUOTA_REQUEST_FOR_NAMESPACE,
	QUOTA_REQUEST_FOR_ORGAN,
	QUOTA_REQUEST_FOR_PROJECT,
	SERVER_INTEGRATE_FOR_PROJECT,
	SERVER_INTEGRATE_FOR_ORGAN,
	formItemLayout614,
	maintenances,
	workOrderTopic,
	NeedThirdPartyOperators
} from '@/utils/const';
import { NamespaceItem } from '../ProjectDetail/projectDetail';
import {
	getMaintenance,
	getProjectNamespace,
	getProjects
} from '@/services/project';
import { getClusters } from '@/services/common';
import ServerList from './components/ServerList';
import QuotaInfo from './components/QuotaInfo';
import './index.less';
import WorkOrderStep from './components/WorkOrderStep';
import { createWorkOrder, getOrderDetail } from '@/services/workOrder';
const { RangePicker } = DatePicker;
const hasClusterIdAndNamespaceTopic = [
	MIDDLEWARE_DEPLOYMENT,
	// MIDDLEWARE_DECOMMISSIONING,
	CONTROLLED_OPERATION_BASE,
	CONTROLLED_OPERATION_Maintenance,
	CONTROLLED_OPERATION_EXPERT
];
const quotaRequestTopic = [
	QUOTA_REQUEST_FOR_NAMESPACE,
	QUOTA_REQUEST_FOR_PROJECT,
	QUOTA_REQUEST_FOR_ORGAN
];
export default function WorkOrderForm(): JSX.Element {
	const params: FormParams = useParams();
	const [form] = Form.useForm();
	const organization = storage.getSession('organization');
	const project = storage.getSession('project');
	const currentService = storage.getSession('currentService');
	const quotaListForm = Form.useWatch('quotaList', form);
	const [options, setOptions] = useState<Option[]>([]);
	const [value, setValue] = useState<string[]>([]);
	const [orgOptions, setOrgOptions] = useState<Option[]>([]);
	const [orgValue, setOrgValue] = useState<string[]>([]);
	const [spinning, setSpinning] = useState<boolean>(false);
	const [loading, setLoading] = useState<boolean>(false);
	const [detail, setDetail] = useState<WorkOrderItem>();
	const [maintenanceByDetail, setMaintenanceByDetail] = useState<
		MaintenanceItem[]
	>([]);
	const [disabled, setDisabled] = useState<boolean>(false);
	const history = useHistory();
	const [open, setOpen] = useState<boolean>(false);
	useEffect(() => {
		if (quotaRequestTopic.includes(params.topic)) {
			if (quotaListForm && quotaListForm.length > 0) {
				setDisabled(false);
			} else {
				setDisabled(true);
			}
		} else {
			setDisabled(false);
		}
	}, [quotaListForm]);
	useEffect(() => {
		async function getAllData() {
			setSpinning(true);
			const res3 = await getMaintenance({
				organId: organization.organId,
				projectId: project.projectId,
				operatorType: ''
			});
			setMaintenanceByDetail(res3.data);
			if (
				params.topic !== QUOTA_REQUEST_FOR_ORGAN &&
				params.topic !== SERVER_INTEGRATE_FOR_ORGAN &&
				params.topic !== SERVER_INTEGRATE_FOR_PROJECT
			) {
				const res = await getClusters({
					organId: organization.organId,
					projectId: project.projectId
				});
				if (!res.success) {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
					return;
				}
				if (quotaRequestTopic.includes(params.topic)) {
					const tl = res.data.map((item: any) => {
						return {
							value: item.id,
							label: item.nickname || item.name,
							isLeaf: false
						};
					});
					setOptions(tl);
				}
				if (hasClusterIdAndNamespaceTopic.includes(params.topic)) {
					const tl = res.data.map((item: any) => {
						return {
							value: item.id,
							label: item.nickname || item.name,
							isLeaf: false
						};
					});
					const res2 = await getProjectNamespace({
						organId: organization.organId,
						projectId: project.projectId,
						clusterId: currentService.clusterId
					});
					if (!res2.success) {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res2.errorMsg}</p>
									<p>{res2.errorDetail}</p>
								</>
							)
						});
						return;
					}
					const ntl = res2.data?.map((item) => {
						return {
							value: item.name,
							label: item.aliasName || item.name,
							isLeaf: true
						};
					});
					tl.map((item: any) => {
						if (item.value === currentService.clusterId) {
							item.children = ntl || [];
						}
						return item;
					});
					setOptions(tl);
					form.setFieldsValue({
						clusterAndNamespace: [
							currentService.clusterId,
							currentService.namespace
						]
					});
					setValue([
						currentService.clusterId,
						currentService.namespace
					]);
				}
			} else {
				const res = await getProjects({
					organId: organization.organId || '',
					key: '',
					manager: true
				});
				if (!res.success) {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
					return;
				}
				const tl = [
					{
						value: organization.organId,
						label: organization.name,
						children: res.data.map((item) => {
							return {
								value: item.projectId,
								label: item.aliasName || item.name,
								isLeaf: true
							};
						})
					}
				];
				if (params.topic === SERVER_INTEGRATE_FOR_PROJECT) {
					setOrgValue([organization.organId, project.projectId]);
					form.setFieldsValue({
						orgAndPro: [organization.organId, project.projectId]
					});
				}
				setOrgOptions(tl);
			}
			setSpinning(false);
		}
		async function getOrderDetailData(orderId: string) {
			setSpinning(true);
			const res = await getOrderDetail({ orderId });
			if (!res.success) {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
			setDetail(res.data);
			const res3 = await getMaintenance({
				organId: res.data.organId,
				projectId: res.data.projectId,
				operatorType: ''
			});
			setMaintenanceByDetail(res3.data);
			const quotaListTemp = [];
			if (res.data.workOrderContentDo?.resourceQuotaDo?.cpu.request) {
				quotaListTemp.push({
					quotaType: 'cpu',
					cpu: res.data.workOrderContentDo.resourceQuotaDo.cpu.request
				});
			}
			if (res.data.workOrderContentDo?.resourceQuotaDo?.memory.request) {
				quotaListTemp.push({
					quotaType: 'memory',
					memory: res.data.workOrderContentDo.resourceQuotaDo.memory
						.request
				});
			}
			const lt =
				res.data.workOrderContentDo?.resourceQuotaDo?.storageList.map(
					(item) => {
						quotaListTemp.push({
							quotaType: 'storage',
							storageId: item.storageId,
							storageQuota: item.storage.request
						});
					}
				);
			if (
				params.topic === QUOTA_REQUEST_FOR_ORGAN ||
				params.topic === SERVER_INTEGRATE_FOR_ORGAN
			) {
				const res2 = await getProjects({
					organId: res.data.organId || '',
					key: '',
					manager: true
				});
				const orgOptionsTemp = [
					{
						value: res.data.organId,
						label: res.data.organName,
						children:
							res2.data?.map((item) => {
								return {
									value: item.projectId,
									label: item.aliasName || item.name,
									isLeaf: true
								};
							}) || []
					}
				];
				setOrgOptions(orgOptionsTemp);
			} else {
				const orgOptionsTemp = [
					{
						value: res.data.organId,
						label: res.data.organName,
						children: [
							{
								value: res.data.projectId,
								label: res.data.projectName
							}
						]
					}
				];
				setOrgOptions(orgOptionsTemp);
			}
			const optionsTemp = [
				{
					value: res.data.workOrderContentDo?.clusterId as string,
					label: res.data.workOrderContentDo
						?.clusterAliasName as string,
					children: [
						{
							value: res.data.workOrderContentDo
								?.namespace as string,
							label: res.data.workOrderContentDo
								?.namespaceAliasName as string
						}
					]
				}
			];
			setOptions(optionsTemp);
			form.setFieldsValue({
				orgAndPro: [res.data.organId, res.data.projectId],
				clusterAndNamespace: [
					res.data.workOrderContentDo.clusterId,
					res.data.workOrderContentDo.namespace
				],
				middlewareType: res.data.workOrderContentDo.middlewareType,
				middlewareName: res.data.workOrderContentDo.middlewareName,
				operatorId: res.data.workOrderContentDo.maintenanceOperatorId,
				clusterId: res.data.workOrderContentDo.clusterId,
				description: res.data.description,
				operatorTime: [
					moment(res.data.workOrderContentDo.operationStartTime),
					moment(res.data.workOrderContentDo.operationEndTime)
				],
				serverList: res.data.workOrderContentDo?.ip?.map((item) => {
					return { ip: item };
				}),
				quotaList: quotaListTemp
			});
			setOrgValue([res.data.organId, res.data.projectId]);
			setSpinning(false);
		}
		if (!params.orderId) {
			getAllData();
		} else {
			// * 再次申请
			getOrderDetailData(params.orderId);
		}
	}, []);
	useEffect(() => {
		if (orgValue && orgValue.length) {
			getClusters({
				organId: orgValue?.[0],
				projectId: orgValue?.[1]
			}).then((res) => {
				const tl = res.data.map((item: any) => {
					return {
						value: item.id,
						label: item.nickname || item.name,
						isLeaf: false,
						children: options?.[0]?.children || []
					};
				});
				setOptions(tl);
			});
		} else {
			setOptions([]);
		}
	}, [orgValue]);
	const range = (start: number, end: number) => {
		const result = [];
		for (let i = start; i < end; i++) {
			result.push(i);
		}
		return result;
	};
	const disabledDate: RangePickerProps['disabledDate'] = (current) => {
		return current && current < moment().startOf('day');
	};
	const disabledRangeTime: RangePickerProps['disabledTime'] = (
		date,
		type
	) => {
		const selectedDay = moment(date).date();
		const day = moment().date();
		const selectedHour = moment(date).hour();
		const hour = moment().hour();
		const min = moment().minute();
		if (type === 'start') {
			if (selectedDay === day) {
				if (selectedHour > hour) {
					return {
						disabledHours: () => range(0, hour)
					};
				}
				return {
					disabledHours: () => range(0, hour),
					disabledMinutes: () => range(0, min + 1)
				};
			} else {
				return {};
			}
		}
		if (selectedDay === day) {
			if (selectedHour > hour) {
				return {
					disabledHours: () => range(0, hour)
				};
			}
			return {
				disabledHours: () => range(0, hour),
				disabledMinutes: () => range(0, min + 1)
			};
		} else {
			return {};
		}
	};
	const onChange = (changedValue: any) => {
		setValue(changedValue);
		const quotaListTemp = [];
		const values = form.getFieldsValue();
		if (values?.quotaList?.find((item: any) => item.quotaType === 'cpu')) {
			quotaListTemp.push({
				quotaType: 'cpu',
				cpu: values.quotaList.find(
					(item: any) => item.quotaType === 'cpu'
				).cpu
			});
		}
		if (
			values?.quotaList?.find((item: any) => item.quotaType === 'memory')
		) {
			quotaListTemp.push({
				quotaType: 'memory',
				memory: values.quotaList.find(
					(item: any) => item.quotaType === 'memory'
				).memory
			});
		}
		const lt = values?.quotaList
			?.filter((item: any) => item.quotaType === 'storage')
			.map((item: any) => {
				quotaListTemp.push({
					quotaType: 'storage',
					storageId: undefined,
					storageQuota: undefined
				});
			});
		form.setFieldsValue({
			quotaList: quotaListTemp
		});
	};
	const onOrgChange = (changedValue: any) => {
		setOrgValue(changedValue);
		const quotaListTemp = [];
		const values = form.getFieldsValue();
		if (values?.quotaList?.find((item: any) => item.quotaType === 'cpu')) {
			quotaListTemp.push({
				quotaType: 'cpu',
				cpu: values.quotaList.find(
					(item: any) => item.quotaType === 'cpu'
				).cpu
			});
		}
		if (
			values?.quotaList?.find((item: any) => item.quotaType === 'memory')
		) {
			quotaListTemp.push({
				quotaType: 'memory',
				memory: values.quotaList.find(
					(item: any) => item.quotaType === 'memory'
				).memory
			});
		}
		const lt = values?.quotaList
			?.filter((item: any) => item.quotaType === 'storage')
			.map((item: any) => {
				quotaListTemp.push({
					quotaType: 'storage',
					storageId: undefined,
					storageQuota: undefined
				});
			});
		form.setFieldsValue({
			clusterAndNamespace: undefined,
			clusterId: undefined,
			quotaList: quotaListTemp
		});
	};
	const loadData = (selectedOptions: DefaultOptionType[]) => {
		const targetOption = selectedOptions[selectedOptions.length - 1];
		targetOption.loading = true;
		return getProjectNamespace({
			organId: orgValue?.[0] || organization.organId,
			projectId: orgValue?.[1] || project.projectId,
			clusterId: targetOption?.value as string,
			withQuota: true
		}).then((res) => {
			if (res.success) {
				const cnl = res.data?.map((item: NamespaceItem) => {
					return {
						value: item.name,
						label: item.aliasName || item.name,
						isLeaf: true
					};
				});
				targetOption.children = cnl;
				setOptions([...options]);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	const topicFormRender = () => {
		switch (params.topic) {
			case MIDDLEWARE_DEPLOYMENT:
				return (
					<>
						<Form.Item
							label="组织/项目"
							name="orgAndPro"
							initialValue={[
								organization.organId,
								project.projectId
							]}
						>
							{detail?.organName || organization.name}/
							{detail?.projectName ||
								project.aliasName ||
								project.name}
						</Form.Item>
						<Form.Item
							label="集群/命名空间"
							name="clusterAndNamespace"
						>
							<Cascader
								disabled={true}
								placeholder="请选择集群/命名空间"
								options={options}
								value={value}
								onChange={onChange}
								loadData={loadData}
								popupClassName="publish-cascader-popup-content"
							/>
						</Form.Item>
						<Form.Item
							label="服务名称"
							name="middlewareName"
							initialValue={params.middlewareName}
						>
							<Input disabled value={params.middlewareName} />
						</Form.Item>
						<Form.Item
							label="服务类型"
							name="middlewareType"
							initialValue={params?.name}
						>
							<Input disabled />
						</Form.Item>
						<Form.Item
							label="描述"
							name="description"
							requiredMark="optional"
							rules={[
								{
									required: true,
									message: '描述内容不能为空'
								}
							]}
						>
							<Input.TextArea
								placeholder="请输入描述内容"
								rows={3}
							/>
						</Form.Item>
					</>
				);
			case SERVER_INTEGRATE_FOR_ORGAN:
			case SERVER_INTEGRATE_FOR_PROJECT:
				return (
					<>
						<Form.Item
							label="组织/项目"
							name="orgAndPro"
							rules={[
								{ required: true, message: '组织/项目不能为空' }
							]}
							requiredMark="optional"
						>
							<Cascader
								disabled={
									params.topic ===
									SERVER_INTEGRATE_FOR_PROJECT
								}
								placeholder="请选择组织/项目"
								options={orgOptions}
								value={orgValue}
								onChange={onOrgChange}
								popupClassName="publish-cascader-popup-content"
							/>
						</Form.Item>
						<Form.Item
							label="集群"
							name="clusterId"
							tooltip="接入服务器时，需要依赖集群的客户端控制器组件实现管理"
							rules={[
								{ required: true, message: '集群不能为空' }
							]}
							requiredMark="optional"
						>
							<Select
								options={options}
								placeholder="请选择集群"
							/>
						</Form.Item>
						<Form.Item
							label="描述"
							name="description"
							requiredMark="optional"
							rules={[
								{
									required: true,
									message: '描述内容不能为空'
								}
							]}
						>
							<Input.TextArea
								placeholder="请输入描述内容"
								rows={3}
							/>
						</Form.Item>
						<ServerList />
					</>
				);
			case CONTROLLED_OPERATION_BASE:
			case CONTROLLED_OPERATION_Maintenance:
			case CONTROLLED_OPERATION_EXPERT:
				return (
					<>
						<Form.Item
							label="组织/项目"
							name="orgAndPro"
							initialValue={[
								organization.organId,
								project.projectId
							]}
						>
							{detail?.organName || organization.name}/
							{detail?.projectName ||
								project.aliasName ||
								project.name}
						</Form.Item>
						{params.middlewareName !== 'null' && (
							<Form.Item
								label="集群/命名空间"
								name="clusterAndNamespace"
							>
								<Cascader
									disabled={true}
									placeholder="请选择集群/命名空间"
									options={options}
									value={value}
									onChange={onChange}
									loadData={loadData}
									popupClassName="publish-cascader-popup-content"
								/>
							</Form.Item>
						)}
						<Form.Item
							label="服务类型"
							name="middlewareType"
							initialValue={params?.name}
						>
							<Input disabled />
						</Form.Item>
						{params.middlewareName !== 'null' && (
							<Form.Item
								label="服务名称"
								name="middlewareName"
								initialValue={params.middlewareName}
							>
								<Input disabled value={params.middlewareName} />
							</Form.Item>
						)}
						<Form.Item
							label="操作名称"
							name="operatorId"
							initialValue={
								detail
									? detail.workOrderContentDo
											.maintenanceOperatorId
									: params.operatorId
							}
						>
							<Select disabled>
								{maintenanceByDetail.map(
									(item: MaintenanceItem) => {
										return (
											<Select.Option
												key={item.operatorId}
											>
												{item.operatorAliasName}
											</Select.Option>
										);
									}
								)}
							</Select>
						</Form.Item>
						<Form.Item
							name="operatorTime"
							label="操作时间"
							rules={[
								{
									required: true,
									message: '操作时间不能为空'
								},
								{
									validator: async (_, value) => {
										if (value) {
											if (!value[0])
												return Promise.reject(
													new Error('请选择开始时间')
												);
											if (!value[1])
												return Promise.reject(
													new Error('请选择结束时间')
												);
											const currentTime = moment().unix();
											const startTime = moment(
												value[0]
											).unix();
											if (currentTime > startTime) {
												return Promise.reject(
													new Error(
														'请选择正确的时间段'
													)
												);
											}
										}
									}
								}
							]}
							requiredMark="optional"
						>
							<RangePicker
								disabledDate={disabledDate}
								disabledTime={disabledRangeTime}
								showTime={
									open
										? {
												hideDisabledOptions: true,
												defaultValue: [
													moment().add(1, 'm'),
													moment()
												]
										  }
										: {
												hideDisabledOptions: true
										  }
								}
								open={open}
								onOpenChange={(open: boolean) => setOpen(open)}
								format="YYYY-MM-DD HH:mm"
							/>
						</Form.Item>
						<Form.Item
							label="描述"
							name="description"
							requiredMark="optional"
							rules={[
								{
									required: true,
									message: '描述内容不能为空'
								}
							]}
						>
							<Input.TextArea
								placeholder="请输入描述内容"
								rows={3}
							/>
						</Form.Item>
					</>
				);
			case QUOTA_REQUEST_FOR_PROJECT:
			case QUOTA_REQUEST_FOR_NAMESPACE:
				return (
					<>
						<Form.Item
							label="组织/项目"
							name="orgAndPro"
							initialValue={[
								organization.organId,
								project.projectId
							]}
						>
							{detail?.organName || organization.name}/
							{detail?.projectName ||
								project.aliasName ||
								project.name}
						</Form.Item>
						<Form.Item
							label="集群/命名空间"
							name="clusterAndNamespace"
							rules={[
								{
									required: true,
									message: '集群/命名空间不能为空'
								}
							]}
							requiredMark="optional"
						>
							<Cascader
								placeholder="请选择集群/命名空间"
								options={options}
								value={value}
								onChange={onChange}
								loadData={loadData}
								popupClassName="publish-cascader-popup-content"
							/>
						</Form.Item>
						<Form.Item
							label="描述"
							name="description"
							requiredMark="optional"
							rules={[
								{
									required: true,
									message: '描述内容不能为空'
								}
							]}
						>
							<Input.TextArea
								placeholder="请输入描述内容"
								rows={3}
							/>
						</Form.Item>
						<h2>配额信息</h2>
						<QuotaInfo />
					</>
				);
			case QUOTA_REQUEST_FOR_ORGAN:
				return (
					<>
						<Form.Item
							label="组织/项目"
							name="orgAndPro"
							rules={[
								{
									required: true,
									message: '组织/项目不能为空'
								}
							]}
							requiredMark="optional"
						>
							<Cascader
								placeholder="请选择组织/项目"
								options={orgOptions}
								value={orgValue}
								onChange={onOrgChange}
								popupClassName="publish-cascader-popup-content"
							/>
						</Form.Item>
						<Form.Item
							label="集群/命名空间"
							name="clusterAndNamespace"
							rules={[
								{
									required: true,
									message: '集群/命名空间不能为空'
								}
							]}
							requiredMark="optional"
						>
							<Cascader
								placeholder="请选择集群/命名空间"
								options={options}
								value={value}
								onChange={onChange}
								loadData={loadData}
								popupClassName="publish-cascader-popup-content"
							/>
						</Form.Item>
						<Form.Item
							label="描述"
							name="description"
							requiredMark="optional"
							rules={[
								{
									required: true,
									message: '描述内容不能为空'
								}
							]}
						>
							<Input.TextArea
								placeholder="请输入描述内容"
								rows={3}
							/>
						</Form.Item>
						<h2>配额信息</h2>
						<QuotaInfo />
					</>
				);
			default:
				return <></>;
		}
	};
	const handleSubmit = async () => {
		await form.validateFields();
		const values = form.getFieldsValue();
		let sendData: any = {};
		let locked;
		if (params.orderId) {
			if (params.orderId.indexOf('UMW') !== -1) {
				locked = false;
			} else {
				locked = true;
			}
		} else {
			if (params.lock === 'true') {
				locked = true;
			} else {
				locked = false;
			}
		}
		const currentOperatorId = detail
			? detail.workOrderContentDo.maintenanceOperatorId
			: params.operatorId;
		const third_party_flag = NeedThirdPartyOperators.map(
			(item) => item.operatorId
		).includes(currentOperatorId || '');
		switch (params.topic) {
			case MIDDLEWARE_DEPLOYMENT:
				sendData = {
					organId: values.orgAndPro?.[0],
					projectId: values.orgAndPro?.[1],
					topic: values.topic,
					lockMiddleware: locked,
					description: values.description,
					needThirdParty: third_party_flag,
					workOrderContentDo: {
						clusterId: values.clusterAndNamespace?.[0],
						namespace: values.clusterAndNamespace?.[1],
						maintenanceOperatorId: maintenances['Service Lock'],
						middlewareName: values.middlewareName,
						middlewareType: detail
							? detail.workOrderContentDo.middlewareType
							: currentService.type,
						deployMod: detail
							? detail.workOrderContentDo.deployMod
							: currentService.deployMod,
						chartName: detail
							? detail.workOrderContentDo.chartName
							: currentService.type,
						chartVersion: detail
							? detail.workOrderContentDo.chartVersion
							: currentService.chartVersion
					}
				};
				break;
			case SERVER_INTEGRATE_FOR_ORGAN:
			case SERVER_INTEGRATE_FOR_PROJECT:
				sendData = {
					organId: values.orgAndPro?.[0],
					projectId: values.orgAndPro?.[1],
					topic: params.topic,
					description: values.description,
					needThirdParty: third_party_flag,
					workOrderContentDo: {
						clusterId: values.clusterId,
						ip: values.serverList.map(
							(item: { ip: string }) => item.ip
						)
					}
				};
				break;
			case QUOTA_REQUEST_FOR_ORGAN:
			case QUOTA_REQUEST_FOR_PROJECT:
			case QUOTA_REQUEST_FOR_NAMESPACE:
				sendData = {
					organId: values.orgAndPro?.[0],
					projectId: values.orgAndPro?.[1],
					topic: values.topic,
					description: values.description,
					needThirdParty: third_party_flag,
					workOrderContentDo: {
						clusterId: values.clusterAndNamespace?.[0],
						namespace: values.clusterAndNamespace?.[1],
						resourceQuotaDo: {
							cpu: {
								request: values.quotaList?.find(
									(item: any) => item.quotaType === 'cpu'
								)?.cpu
							},
							memory: {
								request: values.quotaList?.find(
									(item: any) => item.quotaType === 'memory'
								)?.memory
							},
							storageList: values.quotaList
								?.filter(
									(item: any) => item.quotaType === 'storage'
								)
								.map((item: any) => {
									return {
										storageId: item.storageId,
										storage: {
											request: item.storageQuota
										}
									};
								})
						}
					}
				};
				break;
			case CONTROLLED_OPERATION_BASE:
			case CONTROLLED_OPERATION_Maintenance:
			case CONTROLLED_OPERATION_EXPERT:
				sendData = {
					organId: values.orgAndPro?.[0],
					projectId: values.orgAndPro?.[1],
					topic: values.topic,
					description: values.description,
					needThirdParty: third_party_flag,
					workOrderContentDo: {
						clusterId: values.clusterAndNamespace?.[0],
						namespace: values.clusterAndNamespace?.[1],
						middlewareName: values.middlewareName,
						middlewareType: params.name
							? params.name
							: currentService.type,
						maintenanceOperatorId: values.operatorId,
						operationStartTime: moment(
							values.operatorTime[0]
						).format('YYYY-MM-DD HH:mm:ss'),
						operationEndTime: moment(values.operatorTime[1]).format(
							'YYYY-MM-DD HH:mm:ss'
						)
					}
				};
				break;
			default:
				return;
		}
		// console.log(sendData);
		setLoading(true);
		createWorkOrder(sendData)
			.then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '工单创建成功'
					});
					storage.setSession('urlFrom', 'workOrderForm');
					history.push({
						pathname: `/workspace/workOrderManage/detail/${res.data.orderId}`
					});
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	return (
		<ProPage>
			<ProHeader
				title="工单填写"
				onBack={() => {
					history.goBack();
				}}
			/>
			<ProContent>
				<h2>申请信息</h2>
				<Spin spinning={spinning}>
					<div className="work-order-form-content">
						<div className="work-order-form-left">
							<Form
								{...formItemLayout614}
								labelAlign="left"
								colon={false}
								form={form}
							>
								<Form.Item
									label="工单主题"
									name="topic"
									initialValue={params.topic}
								>
									{workOrderTopic[params.topic]}
								</Form.Item>
								{topicFormRender()}
							</Form>
						</div>
						<div className="work-order-form-right">
							<h2>审批流程</h2>
							<WorkOrderStep
								organId={
									detail ? detail.organId : orgValue?.[0]
								}
								projectId={
									detail ? detail.projectId : orgValue?.[1]
								}
								operatorId={
									detail
										? detail.workOrderContentDo
												.maintenanceOperatorId
										: params.operatorId
								}
							/>
						</div>
					</div>
					<Divider />
					<Space>
						<Button danger onClick={() => history.goBack()}>
							取消
						</Button>
						<Button
							type="primary"
							onClick={handleSubmit}
							disabled={disabled}
							loading={loading}
						>
							确定
						</Button>
					</Space>
				</Spin>
			</ProContent>
		</ProPage>
	);
}

import React, { useEffect, useState } from 'react';
import { useHistory, useParams, useLocation } from 'react-router';
import { Button, Spin, notification } from 'antd';
import { ProContent, ProHeader, ProPage } from '@/components/ProPage';
import { getOrderDetail } from '@/services/workOrder';
import {
	CONTROLLED_OPERATION_BASE,
	CONTROLLED_OPERATION_Maintenance,
	CONTROLLED_OPERATION_EXPERT,
	// MIDDLEWARE_DECOMMISSIONING,
	MIDDLEWARE_DEPLOYMENT,
	QUOTA_REQUEST_FOR_NAMESPACE,
	QUOTA_REQUEST_FOR_ORGAN,
	QUOTA_REQUEST_FOR_PROJECT,
	SERVER_INTEGRATE_FOR_PROJECT,
	SERVER_INTEGRATE_FOR_ORGAN,
	workOrderTopic
} from '@/utils/const';
import DataFields from '@/components/DataFields';
import WorkOrderStep from './components/WorkOrderStep';
import {
	objectRemoveDuplicates<PERSON><PERSON><PERSON><PERSON>,
	workOrderStatusRender
} from '@/utils/utils';
import { ReloadOutlined } from '@ant-design/icons';
import storage from '@/utils/storage';
import ImplementForm from './components/ImplementForm';
import moment from 'moment';
import CompleteForm from './components/CompleteForm';

const item1 = [
	{
		dataIndex: 'title',
		span: 24
	},
	{
		dataIndex: 'description',
		label: '工单描述'
	}
];
const item2 = [
	{
		dataIndex: 'title',
		span: 24
	},
	{
		dataIndex: 'orgAndPro',
		label: '组织/项目'
	},
	{
		dataIndex: 'clusterAndNamespace',
		label: '集群/命名空间'
	},
	{
		dataIndex: 'middlewareName',
		label: '服务名称'
	},
	{
		dataIndex: 'middlewareTypeAliasName',
		label: '服务类型'
	},
	{
		dataIndex: 'operatorAliasName',
		label: '操作名称'
	},
	{
		dataIndex: 'operatorTime',
		label: '操作时间'
	}
];
const item21 = [
	{
		dataIndex: 'title',
		span: 24
	},
	{
		dataIndex: 'orgAndPro',
		label: '组织/项目'
	},
	{
		dataIndex: 'clusterAndNamespace',
		label: '集群/命名空间'
	},
	{
		dataIndex: 'middlewareTypeAliasName',
		label: '服务类型'
	},
	{
		dataIndex: 'middlewareName',
		label: '服务名称'
	}
];
const item22Temp = [
	{
		dataIndex: 'title',
		span: 24
	},
	{
		dataIndex: 'orgAndPro',
		label: '组织/项目'
	},
	{
		dataIndex: 'clusterAndNamespace',
		label: '集群/命名空间'
	}
];
const item23Temp = [
	{
		dataIndex: 'title',
		span: 24
	},
	{
		dataIndex: 'orgAndPro',
		label: '组织/项目'
	},
	{
		dataIndex: 'clusterAliasName',
		label: '集群'
	}
];
const item3 = [
	{
		dataIndex: 'title',
		span: 24
	},
	{
		dataIndex: 'orderId',
		label: '工单ID'
	},
	{
		dataIndex: 'creator',
		label: '创建人'
	},
	{
		dataIndex: 'handler',
		label: '待处理人'
	},
	{
		dataIndex: 'status',
		label: '工单状态',
		render: workOrderStatusRender
	},
	{
		dataIndex: 'updateTime',
		label: '最近更新时间'
	},
	{
		dataIndex: 'time',
		label: '已耗时'
	}
];
export default function OrderDetail(): JSX.Element {
	const { orderId }: { orderId: string } = useParams();
	const history = useHistory();
	const role = JSON.parse(storage.getLocal('role'));
	const [data, setData] = useState<WorkOrderItem>();
	const [spinning, setSpinning] = useState<boolean>(false);
	const [implementOpen, setImplementOpen] = useState<boolean>(false);
	const [completeOpen, setCompleteOpen] = useState<boolean>(false);
	const [item22, setItem22] = useState<any>(item22Temp);
	const [item23, setItem23] = useState<any>(item23Temp);
	const [basicSource, setBasicSource] = useState<{
		title: string;
		description: string;
	}>({ title: '', description: '' });
	// * 受限操作
	const [basicSource2, setBasicSource2] = useState<{
		title: string;
		orgAndPro: string;
		clusterAndNamespace: string;
		middlewareName: string;
		middlewareType: string;
		middlewareTypeAliasName: string;
		operatorAliasName: string;
		operatorTime: string;
	}>({
		title: '',
		orgAndPro: '',
		clusterAndNamespace: '',
		middlewareName: '',
		middlewareType: '',
		middlewareTypeAliasName: '',
		operatorAliasName: '',
		operatorTime: ''
	});
	// * 服务锁定/解锁
	const [basicSource21, setBasicSource21] = useState<{
		title: string;
		orgAndPro: string;
		clusterAndNamespace: string;
		middlewareName: string;
		middlewareType: string;
		middlewareTypeAliasName: string;
	}>({
		title: '',
		orgAndPro: '',
		clusterAndNamespace: '',
		middlewareName: '',
		middlewareType: '',
		middlewareTypeAliasName: ''
	});
	// * 配额管理
	const [basicSource22, setBasicSource22] = useState<{
		title: string;
		orgAndPro: string;
		clusterAndNamespace: string;
		cpu: string;
		memory: string;
		storages: any[];
	}>({
		title: '',
		orgAndPro: '',
		clusterAndNamespace: '',
		cpu: '',
		memory: '',
		storages: []
	});
	// * 服务器接入
	const [basicSource23, setBasicSource23] = useState<{
		title: string;
		orgAndPro: string;
		clusterAliasName: string;
		[props: string]: string;
	}>({
		title: '',
		orgAndPro: '',
		clusterAliasName: ''
	});
	const [basicSource3, setBasicSource3] = useState<{
		title: string;
		orderId: string;
		creator: string;
		handler: string;
		status: string;
		updateTime: string;
		time: string;
	}>({
		title: '',
		orderId: '',
		creator: '',
		handler: '',
		status: '',
		updateTime: '',
		time: ''
	});
	useEffect(() => {
		getData();
		return () => {
			storage.removeSession('urlFrom');
		};
	}, []);
	const getData = () => {
		setSpinning(true);
		getOrderDetail({ orderId })
			.then((res) => {
				if (res.success) {
					setData(res.data);
					setBasicSource({
						title: '',
						description: res.data.description
					});
					setBasicSource2({
						title: '',
						orgAndPro: `${res.data.organName || '-'}/${
							res.data.projectName || '-'
						}`,
						clusterAndNamespace: `${
							res.data.workOrderContentDo.clusterAliasName || '-'
						}/${
							res.data.workOrderContentDo.namespaceAliasName ||
							'-'
						}`,
						middlewareType:
							res.data.workOrderContentDo.middlewareType || '/',
						middlewareTypeAliasName:
							res.data.workOrderContentDo
								.middlewareTypeAliasName || '/',
						middlewareName:
							res.data.workOrderContentDo.middlewareAlisaName ||
							res.data.workOrderContentDo.middlewareName ||
							'/',
						operatorAliasName:
							res.data.workOrderContentDo.maintenanceOperatorName?.replace(
								'#',
								'/'
							) || '',
						operatorTime: `${
							res.data.workOrderContentDo.operationStartTime ||
							'/'
						}-${
							res.data.workOrderContentDo.operationEndTime || '/'
						}`
					});
					setBasicSource21({
						title: '',
						orgAndPro: `${res.data.organName || '-'}/${
							res.data.projectName || '-'
						}`,
						clusterAndNamespace: `${
							res.data.workOrderContentDo.clusterAliasName || '-'
						}/${
							res.data.workOrderContentDo.namespaceAliasName ||
							'-'
						}`,
						middlewareName:
							res.data.workOrderContentDo.middlewareAlisaName ||
							res.data.workOrderContentDo.middlewareName ||
							'/',
						middlewareType:
							res.data.workOrderContentDo.middlewareType || '/',
						middlewareTypeAliasName:
							res.data.workOrderContentDo
								.middlewareTypeAliasName || '/'
					});
					const indexTemp = [];
					if (
						res.data.workOrderContentDo?.resourceQuotaDo?.cpu
							?.request
					) {
						indexTemp.push({
							dataIndex: 'cpu',
							label: 'CPU'
						});
					}
					if (
						res.data.workOrderContentDo?.resourceQuotaDo?.memory
							?.request
					) {
						indexTemp.push({
							dataIndex: 'memory',
							label: '内存'
						});
					}
					if (
						res.data.workOrderContentDo?.resourceQuotaDo
							?.storageList &&
						res.data.workOrderContentDo?.resourceQuotaDo
							?.storageList?.length > 0
					) {
						indexTemp.push({
							dataIndex: 'storages',
							label: '存储',
							render: (values: any) => {
								return (
									<div>
										{values.map((item: any) => (
											<p key={item.storageId}>
												{item.name || '/'}/
												{item.storage.request || '-'} GB
											</p>
										))}
									</div>
								);
							}
						});
					}
					setItem22(
						objectRemoveDuplicatesByKey(
							[...item22, ...indexTemp],
							'dataIndex'
						)
					);
					setBasicSource22({
						title: '',
						orgAndPro: `${res.data.organName || '-'}/${
							res.data.projectName || '-'
						}`,
						clusterAndNamespace: `${
							res.data.workOrderContentDo.clusterAliasName || '-'
						}/${
							res.data.workOrderContentDo.namespaceAliasName ||
							'-'
						}`,
						cpu: `${
							res.data.workOrderContentDo?.resourceQuotaDo?.cpu
								?.request || '-'
						} Core`,
						memory: `${
							res.data.workOrderContentDo?.resourceQuotaDo?.memory
								?.request || '-'
						} Gi`,
						storages:
							res.data.workOrderContentDo?.resourceQuotaDo
								?.storageList || []
					});
					if (
						res.data.workOrderContentDo.ip &&
						res.data.workOrderContentDo?.ip.length > 0
					) {
						const obj: any = {};
						const listTemp = res.data.workOrderContentDo.ip.map(
							(item, index) => {
								obj[`ip${index}`] = item || '-';
								return {
									label: 'IP地址',
									dataIndex: `ip${index}`
								};
							}
						);
						setItem23(
							objectRemoveDuplicatesByKey(
								[...item23, ...listTemp],
								'dataIndex'
							)
						);
						setBasicSource23({
							title: '',
							orgAndPro: `${res.data.organName || '-'}/${
								res.data.projectName || '-'
							}`,
							clusterAliasName:
								res.data.workOrderContentDo.clusterAliasName ||
								res.data.workOrderContentDo.clusterId ||
								'/',
							...obj
						});
					}
					let temp = '';
					const timeTemp =
						moment().unix() - moment(res.data.updateTime).unix();
					if (timeTemp / 60 < 60) {
						temp = Math.ceil(timeTemp / 60) + 'm';
					}
					if (timeTemp / 60 >= 60) {
						temp = Math.ceil(timeTemp / 60 / 60) + 'h';
					}
					setBasicSource3({
						title: '',
						orderId: res.data.orderId,
						creator: res.data.creator || '-',
						handler: res.data.userList?.join(',') || '/',
						status: res.data.status,
						updateTime: res.data.updateTime || '-',
						time:
							res.data.status === 'completed' ||
							res.data.status === 'closed'
								? '/'
								: temp
					});
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setSpinning(false);
			});
	};
	const applyRender = () => {
		if (data) {
			switch (data.topic) {
				case MIDDLEWARE_DEPLOYMENT:
					return (
						<DataFields dataSource={basicSource21} items={item21} />
					);
				// case MIDDLEWARE_DECOMMISSIONING:
				// 	return (
				// 		<DataFields dataSource={basicSource21} items={item21} />
				// 	);
				case CONTROLLED_OPERATION_BASE:
					return (
						<DataFields dataSource={basicSource2} items={item2} />
					);
				case CONTROLLED_OPERATION_Maintenance:
					return (
						<DataFields dataSource={basicSource2} items={item2} />
					);
				case CONTROLLED_OPERATION_EXPERT:
					return (
						<DataFields dataSource={basicSource2} items={item2} />
					);
				case QUOTA_REQUEST_FOR_NAMESPACE:
					return (
						<DataFields dataSource={basicSource22} items={item22} />
					);
				case QUOTA_REQUEST_FOR_PROJECT:
					return (
						<DataFields dataSource={basicSource22} items={item22} />
					);
				case QUOTA_REQUEST_FOR_ORGAN:
					return (
						<DataFields dataSource={basicSource22} items={item22} />
					);
				case SERVER_INTEGRATE_FOR_ORGAN:
				case SERVER_INTEGRATE_FOR_PROJECT:
					return (
						<DataFields dataSource={basicSource23} items={item23} />
					);
				default:
					break;
			}
		}
	};
	const extraRender = () => {
		if (
			(data?.status === 'pending' || data?.status === 'resolving') &&
			data?.userList?.includes(role.aliasName)
		) {
			return [
				<Button
					key={1}
					type="primary"
					onClick={() => setImplementOpen(true)}
				>
					处理工单
				</Button>,
				<Button key={2} onClick={getData} icon={<ReloadOutlined />} />
			];
		}
		if (
			data?.status === 'waitExecuted' &&
			data?.userList?.includes(role.aliasName)
		) {
			return [
				<Button
					key={1}
					type="primary"
					onClick={() => setCompleteOpen(true)}
				>
					完成工单
				</Button>,
				<Button key={2} onClick={getData} icon={<ReloadOutlined />} />
			];
		}
		return <Button onClick={getData} icon={<ReloadOutlined />} />;
	};
	return (
		<ProPage>
			<Spin spinning={spinning}>
				<ProHeader
					title={`${workOrderTopic[data?.topic || '']}(${orderId})`}
					onBack={() => {
						if (
							storage.getSession('urlFrom') &&
							storage.getSession('urlFrom') === 'workOrderForm'
						) {
							history.push(
								'/workspace/workOrderManage/initiated'
							);
						} else {
							history.goBack();
						}
					}}
					extra={extraRender()}
				/>
				<ProContent>
					<div className="work-order-detail-content">
						<div className="work-order-detail-left">
							<h2>工单描述</h2>
							<DataFields
								dataSource={basicSource}
								items={item1}
							/>
							<div className="mb-16"></div>
							<h2>申请信息</h2>
							{applyRender()}
							<div className="mb-16"></div>
							<h2>基本信息</h2>
							<DataFields
								dataSource={basicSource3}
								items={item3}
							/>
						</div>
						<div className="work-order-detail-right">
							<h2>审批流程</h2>
							<WorkOrderStep
								type="detail"
								data={data?.workOrderStepDoList}
								organId={data?.organId}
								projectId={data?.projectId}
							/>
						</div>
					</div>
					{implementOpen && (
						<ImplementForm
							orderId={orderId}
							currentStepId={data?.currentStepId}
							currentStepRule={data?.currentStepRule}
							open={implementOpen}
							onCancel={() => {
								setImplementOpen(false);
							}}
							onRefresh={getData}
						/>
					)}
					{completeOpen && (
						<CompleteForm
							orderId={orderId}
							executed={data?.executed}
							open={completeOpen}
							onCancel={() => {
								setCompleteOpen(false);
							}}
							onRefresh={getData}
						/>
					)}
				</ProContent>
			</Spin>
		</ProPage>
	);
}

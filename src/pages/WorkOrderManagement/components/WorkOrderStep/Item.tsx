import React, { useEffect, useState } from 'react';
import { Space, Tooltip } from 'antd';
import { AgreeUser, DisagreeUser, NormalUser } from './UserItem';
import { workOrderStepTagRender } from '@/utils/utils';
import { SmallDashOutlined } from '@ant-design/icons';
export function StepItem(props: StepItemProps): JSX.Element {
	const {
		stepName,
		stepNameSuffix,
		status,
		targetRole,
		updateTime,
		workOrderStepUserDoList
	} = props;
	const [sorterWorkOrderStepUserDoList, setSorterWorkOrderStepUserDoList] =
		useState<WorkOrderStepUserDoItem[]>([]);
	const [moreTitle, setMoreTitle] = useState<string>('');
	useEffect(() => {
		if (workOrderStepUserDoList) {
			const list = [...workOrderStepUserDoList];
			const sortList: WorkOrderStepUserDoItem[] = [];
			list.map((item: any) => {
				if (item.status === 'refused') {
					sortList.unshift(item);
				} else {
					if (item.status === 'approved') {
						sortList.push(item);
					}
				}
				return sortList;
			});
			sortList.splice(
				sortList.filter(
					(item: WorkOrderStepUserDoItem) => item.status === 'refused'
				)?.length,
				0,
				...list.filter(
					(item: any) =>
						item.status !== 'refused' && item.status !== 'approved'
				)
			);
			const temp = sortList
				?.filter((item, index) => index > 2)
				?.map((item) => item.aliasName)
				?.join(',');
			setMoreTitle(temp);
			sortList.length = sortList.length >= 3 ? 3 : sortList.length;
			setSorterWorkOrderStepUserDoList(sortList);
		}
	}, [workOrderStepUserDoList]);
	return (
		<div className="work-order-step-item-content">
			<div className="work-order-step-item-left">
				<div className="work-order-step-item-circle"></div>
				<div className="work-order-step-item-line"></div>
			</div>
			<div className="work-order-step-item-right">
				<div className="work-order-step-item-title">
					<Space
						style={{
							width: stepNameSuffix
								? 'calc(100% - 124px)'
								: 'calc(100% - 60px'
						}}
					>
						{workOrderStepTagRender(status)}
						<span className="work-order-step-item-role">
							{stepName}
						</span>
					</Space>
					{stepNameSuffix ? (
						<span className="work-order-step-item-role">
							{stepNameSuffix}
						</span>
					) : null}
					{targetRole !== 'own' && (
						<span>
							{workOrderStepUserDoList?.length || 0}人审批
						</span>
					)}
				</div>
				<div className="work-order-step-item-user">
					<Space size="large">
						{sorterWorkOrderStepUserDoList?.map(
							(item: WorkOrderStepUserDoItem) => {
								if (item.status === 'refused') {
									return (
										<DisagreeUser
											name={
												item.aliasName || item.username
											}
											key={item.username}
										/>
									);
								}
								if (item.status === 'approved') {
									return (
										<AgreeUser
											name={
												item.aliasName || item.username
											}
											key={item.username}
										/>
									);
								}
								return (
									<NormalUser
										name={item.aliasName || item.username}
										key={item.username}
									/>
								);
							}
						)}
						{(workOrderStepUserDoList || [])?.length > 3 && (
							<Tooltip title={moreTitle}>
								<div className="work-order-more-user">
									<SmallDashOutlined />
								</div>
							</Tooltip>
						)}
					</Space>
					<span style={{ marginBottom: 22 }}>{updateTime}</span>
				</div>
				{workOrderStepUserDoList?.map(
					(item: WorkOrderStepUserDoItem) => {
						return (
							item.comment && (
								<div
									className="work-order-step-item-comment"
									key={item.username}
								>
									<p>
										{item.aliasName || item.username}添加了
										{item.status === 'withDraw'
											? '撤回理由'
											: '评论'}
										：
									</p>
									<p
										title={item.comment}
										className="text-overflow-2"
									>
										{item.comment}
									</p>
								</div>
							)
						);
					}
				)}
			</div>
		</div>
	);
}
export function StepEndItem(props: StepItemProps): JSX.Element {
	const { status, stepName } = props;
	return (
		<div className="work-order-step-item-content">
			<div className="work-order-step-item-left">
				<div className="work-order-step-item-circle"></div>
			</div>
			<div className="work-order-step-item-right">
				<div className="work-order-step-item-title">
					<Space>
						{workOrderStepTagRender(status)}
						<span className="work-order-step-item-role">
							{stepName}
						</span>
					</Space>
				</div>
			</div>
		</div>
	);
}

import React from 'react';
import { CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons';
import { Badge } from 'antd';
import userIcon from '@/assets/images/work-user.svg';

export const AgreeUser: ({ name }: { name: string }) => JSX.Element = ({
	name
}: {
	name: string;
}) => {
	return (
		<Badge
			style={{ textAlign: 'center' }}
			count={<CheckCircleFilled style={{ color: '#226ee7' }} />}
			offset={[-12, 3]}
		>
			<div style={{ textAlign: 'center', width: '40px' }}>
				<img src={userIcon} />
				<p
					className="text-overflow"
					title={name}
					style={{ marginTop: '6px' }}
				>
					{name}
				</p>
			</div>
		</Badge>
	);
};
export const DisagreeUser: ({ name }: { name: string }) => JSX.Element = ({
	name
}: {
	name: string;
}) => {
	return (
		<Badge
			count={<CloseCircleFilled style={{ color: '#ff4d4f' }} />}
			offset={[-12, 3]}
		>
			<div style={{ textAlign: 'center', width: '40px' }}>
				<img src={userIcon} />
				<p
					className="text-overflow"
					title={name}
					style={{ marginTop: '6px' }}
				>
					{name}
				</p>
			</div>
		</Badge>
	);
};
export const NormalUser: ({ name }: { name: string }) => JSX.Element = ({
	name
}: {
	name: string;
}) => {
	return (
		<Badge count={0}>
			<div style={{ textAlign: 'center', width: '40px' }}>
				<img src={userIcon} />
				<p
					className="text-overflow"
					title={name}
					style={{ marginTop: '6px' }}
				>
					{name}
				</p>
			</div>
		</Badge>
	);
};

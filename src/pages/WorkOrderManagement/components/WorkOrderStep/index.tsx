import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router';
import { getOrderProcess } from '@/services/workOrder';
import { Spin, notification } from 'antd';
import { StepEndItem, StepItem } from './Item';
import storage from '@/utils/storage';
import { NeedThirdPartyOperators } from '@/utils/const';

export default function WorkOrderStep({
	data,
	type,
	organId,
	projectId,
	operatorId
}: {
	type?: string;
	data?: WorkOrderItem['workOrderStepDoList'];
	organId?: string;
	projectId?: string;
	operatorId?: string;
}): JSX.Element {
	const params: FormParams = useParams();
	const [spinning, setSpinning] = useState<boolean>(false);
	const [steps, setSteps] = useState<WorkOrderStepItem[]>([]);
	useEffect(() => {
		if (data) {
			setSteps(data);
		}
	}, [data]);
	useEffect(() => {
		if (!type && !steps.length) {
			setSpinning(true);
			const third_party_flag = NeedThirdPartyOperators.map(
				(item) => item.operatorId
			).includes(operatorId || '');
			getOrderProcess({
				organId: organId || storage.getSession('organization')?.organId,
				projectId:
					projectId || storage.getSession('project')?.projectId,
				topic: params.topic,
				needThirdParty: third_party_flag
			})
				.then((res) => {
					if (res.success) {
						setSteps(res.data);
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				})
				.finally(() => {
					setSpinning(false);
				});
		}
	}, [organId, projectId]);
	return (
		<Spin spinning={spinning}>
			{steps.map((item: WorkOrderStepItem, index: number) => {
				if (index === steps.length - 1) {
					return (
						<StepEndItem
							key={item.orderStepId}
							index={index}
							{...item}
						/>
					);
				}
				return (
					<StepItem key={item.orderStepId} index={index} {...item} />
				);
			})}
		</Spin>
	);
}

import React from 'react';
import { Button, Col, Form, Input, Row } from 'antd';
import { MinusOutlined, PlusOutlined } from '@ant-design/icons';
import { formItemLayout618 } from '@/utils/const';
import pattern from '@/utils/pattern';
export default function ServerList(): JSX.Element {
	return (
		<Row>
			<Col span={6}>服务器信息</Col>
			<Col span={14}>
				<Form.List
					name="serverList"
					rules={[
						{
							validator: async (_, values) => {
								if (!values) {
									return Promise.reject(
										new Error('请输入服务器信息')
									);
								}
							}
						}
					]}
					initialValue={[{ ip: '' }]}
				>
					{(fields: any[], { add, remove }: any, { errors }: any) => {
						return (
							<>
								{fields.map((field: any) => (
									<Row
										className="display-flex"
										key={field.key}
									>
										<Col span={2} style={{ marginTop: 5 }}>
											<Button
												size="small"
												icon={<MinusOutlined />}
												type="default"
												disabled={fields.length === 1}
												onClick={() =>
													remove(field.name)
												}
											/>
										</Col>
										<Col span={22}>
											<Form.Item
												{...formItemLayout618}
												{...field}
												label="IP地址"
												style={{ marginBottom: 12 }}
												name={[field.name, 'ip']}
												rules={[
													{
														required: true,
														message:
															'IP地址不能为空'
													},
													{
														pattern: new RegExp(
															pattern.ip
														),
														message:
															'请输入由数字及特殊字符"."组成的IP地址'
													}
												]}
												requiredMark="optional"
											>
												<Input placeholder="请输入IP地址" />
											</Form.Item>
										</Col>
									</Row>
								))}
								<Form.Item wrapperCol={{ span: 24 }}>
									<Button
										type="dashed"
										onClick={() => add()}
										block
										icon={<PlusOutlined />}
									>
										新增
									</Button>
									<Form.ErrorList errors={errors} />
								</Form.Item>
							</>
						);
					}}
				</Form.List>
			</Col>
		</Row>
	);
}

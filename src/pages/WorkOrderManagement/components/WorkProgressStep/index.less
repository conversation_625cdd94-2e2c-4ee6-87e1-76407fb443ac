.work-progress-set {
	display: flex;
	align-items: center;
	flex-direction: column;
	.item-card {
		width: 272px;
		height: 100px;
		cursor: pointer;
		border-radius: 5px;
		border: 1px solid @black-6;
		.error-text {
			color: @red-5;
		}
		.item-card-header {
			height: 32px;
			line-height: 30px;
			padding: 0 12px;
			display: flex;
			color: @black-10;
			border-radius: 4px 4px 0 0;
			justify-content: space-between;
			background-color: #ff943e;
			&.source {
				background-color: #576a95;
			}
		}
		.item-card-content {
			height: 68px;
			display: flex;
			padding: 0 12px;
			align-items: center;
			justify-content: space-between;
			p {
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
			.approver-info {
				width: 60%;
				display: flex;
				align-items: center;
			}
			.approver {
				overflow: hidden;
				margin-right: 12px;
				white-space: nowrap;
				display: inline-block;
				text-overflow: ellipsis;
			}
		}
	}
	.arrow-line {
		position: relative;
		width: 1px;
		height: 80px;
		background-color: @black-5;
	}

	.arrow-line::before {
		content: '';
		position: absolute;
		left: -5px;
		bottom: 1px;
		width: 0;
		height: 0;
		border-top: 5px solid transparent;
		border-left: 5px solid transparent;
		border-bottom: 5px solid @black-4;
		border-right: 5px solid @black-4;
		transform: rotate(45deg);
	}
}
.approver-drawer {
	.ant-radio-wrapper {
		width: 110px;
		margin-bottom: 12px;
	}
	.block-radio {
		width: 100%;
		display: block;
	}
}

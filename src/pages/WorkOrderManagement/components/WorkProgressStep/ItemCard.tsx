import * as React from 'react';
import { useState, useEffect } from 'react';
import {
	UserOutlined,
	RightOutlined,
	PlusCircleFilled
} from '@ant-design/icons';
import { IconFont } from '@/components/IconFont';
import './index.less';
import { Button } from 'antd';

const ItemCard = (props: any): JSX.Element => {
	const { isSource, data, onClick, onAdd, onRemove, removeDisabled } = props;
	const usersOrRoles = data?.manager?.managerList
		?.map((item: any) => item.aliasName || item.name)
		?.join('、');
	return (
		<>
			<div className="item-card" onClick={onClick && onClick}>
				<div className={`item-card-header ${isSource ? 'source' : ''}`}>
					<p>
						<UserOutlined /> {isSource ? '发起人' : '审批人'}
					</p>
					{!isSource ? (
						<Button
							disabled={removeDisabled}
							style={{ border: 'none', background: 'none' }}
							icon={
								<IconFont
									type="icon-shanchu"
									onClick={(e) => {
										e.stopPropagation();
										onRemove && onRemove();
									}}
									style={{ color: '#fff' }}
								/>
							}
						></Button>
					) : null}
				</div>
				<div className="item-card-content">
					{isSource ? (
						<p>{usersOrRoles}</p>
					) : data?.manager ? (
						<>
							<p className="approver-info">
								<span className="approver">
									{data?.manager?.managerType === 'Initiator'
										? '发起人自己'
										: usersOrRoles}
								</span>
								{data?.approvalMethod ? (
									<span>
										{data?.approvalMethod === 'and'
											? '会签'
											: '或签'}
									</span>
								) : null}
							</p>
							<RightOutlined />
						</>
					) : (
						<>
							<div className="error-text">待配置</div>
							<RightOutlined />
						</>
					)}
				</div>
			</div>
			<div className="arrow-line">
				<PlusCircleFilled
					style={{
						position: 'absolute',
						top: 28,
						left: -12,
						zIndex: 10,
						fontSize: 25,
						cursor: 'pointer'
					}}
					onClick={onAdd}
				/>
			</div>
		</>
	);
};

export default ItemCard;

import * as React from 'react';
import { useState, useEffect } from 'react';
import ItemCard from './ItemCard';
import ApproverConfig from './ApproveConfig';

const WorkProgressStep = ({
	initData,
	setBtnDisabled
}: {
	initData: any;
	setBtnDisabled: (arg: boolean) => void;
}): JSX.Element => {
	const [data, setData] = useState<any>(initData);
	const [currentItem, setCurrentItem] = useState<any>();
	const [visible, setVisible] = useState<boolean>(false);

	useEffect(() => {
		data?.linkList?.some((item: any) => !item.manager)
			? setBtnDisabled(true)
			: setBtnDisabled(false);
	}, [data]);

	const onAdd = (id: number) => {
		const index = data.linkList.findIndex((item: any) => item.id === id);
		data.linkList.splice(index + 1, 0, { id: Math.random() });
		setData({ ...data });
	};

	const onRemove = (id: number) => {
		const index = data.linkList.findIndex((item: any) => item.id === id);
		data.linkList.splice(index, 1);
		setData({ ...data });
	};

	const onOk = (obj: any) => {
		const index = data.linkList.findIndex(
			(item: any) => item.id === obj.id
		);
		data.linkList.splice(index, 1, obj);
		setData({ ...data });
		setVisible(false);
	};

	return (
		<div className="work-progress-set">
			{data?.linkList.map((item: any, index: number, record: any) => {
				return index === 0 ? (
					<ItemCard
						key={item.id}
						isSource
						data={item}
						onAdd={() => onAdd(item.id)}
					/>
				) : (
					<ItemCard
						key={item.id}
						data={item}
						onAdd={() => onAdd(item.id)}
						onRemove={() => onRemove(item.id)}
						removeDisabled={record.length === 2}
						onClick={() => {
							setCurrentItem(item);
							setVisible(true);
						}}
					/>
				);
			})}
			流程结束
			{visible && (
				<ApproverConfig
					onOk={onOk}
					data={currentItem}
					visible={visible}
					onClose={() => setVisible(false)}
				/>
			)}
		</div>
	);
};

export default WorkProgressStep;

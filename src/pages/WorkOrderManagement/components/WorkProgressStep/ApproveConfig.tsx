import * as React from 'react';
import { useState, useEffect } from 'react';
import {
	Drawer,
	Form,
	Radio,
	RadioChangeEvent,
	Tag,
	Button,
	Space,
	Modal,
	Tooltip
} from 'antd';
import { PlusOutlined, QuestionCircleFilled } from '@ant-design/icons';
import RoleTransfer from '../roleTransfer';
import UserTransfer from '../userTransfer';

const ApproverConfig = ({
	onOk,
	data,
	visible,
	onClose
}: {
	data: any;
	onOk: (obj: any) => void;
	visible: boolean;
	onClose: () => void;
}): JSX.Element => {
	const [form] = Form.useForm();
	const [approver, setApprover] = useState<string>('AnyUser');
	const [approvalBlankMethod, setApprovalBlankMethod] =
		useState<string>('and');
	const [approvalMethod, setApprovalMethod] = useState<string>('AutoPass');
	const [userValue, setUserValue] = useState<any>([]);
	const [initUserValue, setInitUserValue] = useState<any>([]);
	const [roleValue, setRoleValue] = useState<any>([]);
	const [initRoleValue, setInitRoleValue] = useState<any>([]);
	const [userVisible, setUserVisible] = useState<boolean>(false);
	const [roleVisible, setRoleVisible] = useState<boolean>(false);
	const [removeUserId, setRemoveUserId] = useState<number>();
	const [removeRoleId, setRemoveRoleId] = useState<number>();
	const [btnDisabled, setBtnDisabled] = useState<boolean>(false);
	const [list, setList] = useState<any>();
	const items = [
		{
			label: '指定成员',
			value: 'AnyUser'
		},
		{
			label: '发起人自己',
			value: 'Initiator'
		},
		{
			label: '指定角色',
			value: 'AnyRole'
		},
		{
			label: '项目管理员',
			value: '2'
		},
		{
			label: '组织管理员',
			value: '5'
		},
		{
			label: '中间件管理员',
			value: '6'
		},
		{
			label: '超级管理员',
			value: '1'
		}
	];

	useEffect(() => {
		if (data) {
			if (data.manager?.managerType === 'AnyUser') {
				setUserValue(data.manager?.managerList);
				setInitUserValue(data.manager?.managerList);
			} else if (data.manager?.managerType === 'AnyRole') {
				setRoleValue(data.manager?.managerList);
				setInitRoleValue(data.manager?.managerList);
			} else {
				setUserValue([]);
				setRoleValue([]);
				setInitUserValue([]);
				setInitRoleValue([]);
			}
			setApprover(
				data.manager?.managerType === 'OneRole'
					? String(data.manager?.managerList[0]?.id)
					: data.manager?.managerType
			);
			form.setFieldsValue({
				managerType:
					data.manager?.managerType === 'OneRole'
						? String(data.manager?.managerList[0]?.id)
						: data.manager?.managerType,
				approvalMethod: data?.approvalMethod,
				approvalBlankMethod: data?.approvalBlankMethod
			});
		}
	}, [data]);

	useEffect(() => {
		if (removeUserId) {
			setList(userValue.filter((res: any) => res.id !== removeUserId));
			setUserValue(
				userValue.filter((res: any) => res.id !== removeUserId)
			);
			setInitUserValue(
				initUserValue.filter((res: any) => res.id !== removeUserId)
			);
		}
	}, [removeUserId]);

	useEffect(() => {
		if (removeRoleId) {
			setList(roleValue.filter((res: any) => res.id !== removeRoleId));
			setRoleValue(
				roleValue.filter((res: any) => res.id !== removeRoleId)
			);
			setInitRoleValue(
				initRoleValue.filter((res: any) => res.id !== removeRoleId)
			);
		}
	}, [removeRoleId]);

	useEffect(() => {
		const values = form.getFieldsValue();
		if (
			values.managerType ||
			values.approvalMethod ||
			values.approvalBlankMethod
		) {
			setBtnDisabled(true);
		}
		if (values.managerType === 'Initiator') {
			setBtnDisabled(false);
		} else if (
			values.managerType === 'AnyUser' ||
			values.managerType === 'AnyRole'
		) {
			if (values.managerType === 'AnyUser') {
				if (
					values.approvalMethod &&
					values.approvalBlankMethod &&
					userValue.length
				) {
					setBtnDisabled(false);
				} else {
					setBtnDisabled(true);
				}
			} else {
				if (
					values.approvalMethod &&
					values.approvalBlankMethod &&
					roleValue.length
				) {
					setBtnDisabled(false);
				} else {
					setBtnDisabled(true);
				}
			}
		} else {
			if (values.approvalMethod && values.approvalBlankMethod) {
				setBtnDisabled(false);
			} else {
				setBtnDisabled(true);
			}
		}
	}, [form.getFieldsValue()]);

	const onUserChange = (sendData: any) => {
		setUserValue(sendData);
	};

	const onRoleChange = (sendData: any) => {
		setRoleValue(sendData);
	};

	const onUserOk = () => {
		setList(userValue);
		setUserVisible(false);
		setInitUserValue(userValue);
	};

	const onRoleOk = () => {
		setList(roleValue);
		setRoleVisible(false);
		setInitRoleValue(roleValue);
	};

	const handleSubmit = () => {
		form.validateFields().then((values) => {
			let managerTypeValue, managerListValue;
			if (
				values.managerType === 'AnyUser' ||
				values.managerType === 'AnyRole'
			) {
				managerTypeValue = values.managerType;
				managerListValue = list || data?.manager?.managerList;
			} else if (values.managerType === 'Initiator') {
				managerTypeValue = values.managerType;
				managerListValue = [];
			} else {
				managerTypeValue = 'OneRole';
				managerListValue = [
					{
						classType: 'role',
						id: Number(values.managerType),
						name: items.find(
							(item: any) => item.value === values.managerType
						)?.label
					}
				];
			}
			const obj: any = {
				...data,
				manager: {
					managerList: managerListValue,
					managerType: managerTypeValue
				},
				approvalMethod: values.approvalMethod,
				approvalBlankMethod: values.approvalBlankMethod
			};
			onOk(obj);
		});
	};

	const footer = (
		<Space>
			<Button onClick={onClose}>取消</Button>
			<Button
				type="primary"
				onClick={handleSubmit}
				disabled={btnDisabled}
			>
				确定
			</Button>
		</Space>
	);

	return (
		<Drawer
			title="审批人"
			width={520}
			forceRender
			open={visible}
			footer={footer}
			onClose={onClose}
			className="approver-drawer"
		>
			<Form form={form}>
				<h2>该节点审批人</h2>
				<Form.Item name="managerType">
					<Radio.Group
						options={items}
						value={approver}
						onChange={(e: RadioChangeEvent) =>
							setApprover(e.target.value)
						}
					/>
				</Form.Item>
				{approver === 'AnyUser' || approver === 'AnyRole' ? (
					<>
						<h2>
							{approver === 'AnyUser' ? '指定成员' : '指定角色'}
						</h2>
						<Form.Item>
							<Space
								wrap
								style={{
									width: '100%',
									padding: '5px 8px',
									border: '1px solid #d7d7d7'
								}}
							>
								{approver === 'AnyUser'
									? userValue?.map((item: any) => {
											return (
												<Tag
													key={item.name}
													closable
													onClose={(e) => {
														e.preventDefault();
														setRemoveUserId(
															item.id
														);
													}}
												>
													{item.aliasName}
												</Tag>
											);
									  })
									: roleValue?.map((item: any) => {
											return (
												<Tag
													key={item.name}
													closable
													onClose={(e) => {
														e.preventDefault();
														setRemoveRoleId(
															item.id
														);
													}}
												>
													{item.name}
												</Tag>
											);
									  })}
								<Button
									type="dashed"
									style={{ height: 22, padding: '0 8px' }}
									icon={
										<PlusOutlined
											style={{ color: '#999' }}
										/>
									}
									onClick={() =>
										approver === 'AnyUser'
											? setUserVisible(true)
											: setRoleVisible(true)
									}
								>
									添加
								</Button>
							</Space>
						</Form.Item>
					</>
				) : null}
				{approver !== 'Initiator' ? (
					<>
						<h2>多人审批时采用的审批方式</h2>
						<Form.Item name="approvalMethod">
							<Radio.Group
								value={approvalMethod}
								onChange={(e: RadioChangeEvent) =>
									setApprovalMethod(e.target.value)
								}
							>
								<Radio value="and" className="block-radio">
									会签（需要所有审批人都同意才可通过）
								</Radio>
								<Radio value="or" className="block-radio">
									或签（其中一名审批人同意或拒绝即可）
								</Radio>
							</Radio.Group>
						</Form.Item>
						<h2>审批人为空时</h2>
						<Form.Item name="approvalBlankMethod">
							<Radio.Group
								value={approvalBlankMethod}
								onChange={(e: RadioChangeEvent) =>
									setApprovalBlankMethod(e.target.value)
								}
							>
								<Radio value="AutoPass" className="block-radio">
									自动通过
									<Tooltip title="当该步骤审批人为空的时候，审批单将自动通过">
										<QuestionCircleFilled
											style={{
												cursor: 'pointer',
												marginLeft: 8
											}}
										/>
									</Tooltip>
								</Radio>
								<Radio
									value="DeliverAdmin"
									className="block-radio"
								>
									自动转交管理员
									<Tooltip title="当该步骤审批人为空时，审批单会自动转交给管理员（默认转交超级管理员）">
										<QuestionCircleFilled
											style={{
												cursor: 'pointer',
												marginLeft: 8
											}}
										/>
									</Tooltip>
								</Radio>
							</Radio.Group>
						</Form.Item>
					</>
				) : null}
			</Form>
			{roleVisible && (
				<Modal
					width={1200}
					title="指定角色"
					open={visible}
					onOk={onRoleOk}
					onCancel={() => {
						setRoleVisible(false);
						setRoleValue(initRoleValue);
					}}
				>
					<RoleTransfer
						roleValue={roleValue}
						initValue={roleValue}
						onRoleChange={onRoleChange}
					/>
				</Modal>
			)}
			{userVisible && (
				<Modal
					width={1300}
					title="指定成员"
					open={visible}
					onOk={onUserOk}
					onCancel={() => {
						setUserVisible(false);
						setUserValue(initUserValue);
					}}
				>
					<UserTransfer
						userValue={userValue}
						initValue={userValue}
						onUserChange={onUserChange}
					/>
				</Modal>
			)}
		</Drawer>
	);
};

export default ApproverConfig;

import React, { useState, useEffect } from 'react';
import { useHistory, useParams } from 'react-router';
import {
	Collapse,
	Table,
	Space,
	Input,
	notification,
	Button,
	Radio,
	RadioChangeEvent
} from 'antd';
import { AlarmContactItem } from '@/pages/AlarmCenter/alarm';
import { MinusOutlined, RightOutlined } from '@ant-design/icons';
import { getRoles } from '@/services/user';
import { getManagers } from '@/services/workOrder';

const { Panel } = Collapse;

const columns = [
	{
		title: '登录账户',
		dataIndex: 'name',
		width: 100,
		ellipsis: true,
		render: (val: string) => val || '/'
	},
	{
		title: '用户名',
		dataIndex: 'aliasName',
		width: 100,
		ellipsis: true,
		render: (val: string) => val || '/'
	},
	{
		title: '邮箱',
		dataIndex: 'email',
		width: 150,
		ellipsis: true,
		render: (val: string) => val || '/'
	},
	{
		title: '手机号',
		dataIndex: 'phone',
		width: 130,
		ellipsis: true,
		render: (val: string) => val || '/'
	}
];
const UserTransfer = ({
	initValue,
	userValue,
	isManager,
	onUserChange
}: {
	initValue: any;
	userValue: any;
	isManager?: boolean;
	onUserChange: (val: any) => void;
}): JSX.Element => {
	const [loading, setLoading] = useState<boolean>(false);
	const [initData, setInitData] = useState<any[]>([]);
	const [dataSource, setDataSource] = useState<any[]>([]);
	const [dataTotal, setDataTotal] = useState<number>(0);
	const [showDataSource, setShowDataSource] = useState<any[]>([]);
	const [selectedRows, setSelectedRows] = useState<any[]>([]);
	const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
	const [itemSelectedRows, setItemSelectedRows] = useState<any[]>([]);
	const [itemSelectedRowKeys, setItemSelectedRowKeys] = useState<any[]>([]);
	const [rightData, setRightData] = useState<any[]>([]);
	const [showRightData, setShowRightData] = useState<any[]>([]);
	const [keyword, setKeyword] = useState<string>('');
	const [rightKeyword, setRightKeyword] = useState<string>('');
	const [type, setType] = useState<string>('user');
	const [roles, setRoles] = useState<any[]>([]);
	const [roleLists, setRoleLists] = useState<any[]>([]);
	const [showRoleLists, setShowRoleLists] = useState<any[]>([]);
	const [activeKey, setActiveKey] = useState<string | string[]>([]);

	useEffect(() => {
		getRoles().then((result) => {
			if (result.success) {
				setRoles(result.data);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{result.errorMsg}</p>
							<p>{result.errorDetail}</p>
						</>
					)
				});
			}
		});
	}, []);

	useEffect(() => {
		setLoading(true);
		getManagers({ isFlow: isManager ? true : false, type: 'user' })
			.then((res) => {
				if (res.success) {
					setDataSource(res.data?.ALL);
					setShowDataSource(res.data?.ALL);
					setDataTotal(res.data?.ALL?.length);
					setInitData(res.data);
					if (roles) {
						const list = Object.keys(res.data).map((id) => ({
							id,
							users: res.data[id],
							name: roles.find((item) => item.id === Number(id))
								?.name
						}));
						setRoleLists(list.filter((item) => item.name));
						setShowRoleLists(list.filter((item) => item.name));
					}
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	}, [roles]);

	useEffect(() => {
		if (initValue && initData) {
			const list = dataSource.filter(
				(item) => !initValue.some((arr: any) => arr.name === item.name)
			);
			const showList = showDataSource.filter(
				(item) => !initValue.some((arr: any) => arr.name === item.name)
			);
			setSelectedRows([]);
			setSelectedRowKeys([]);
			setDataSource(list);
			setShowDataSource(showList);
			setDataTotal(showList?.length);
			!rightData.length && setRightData(initValue);
			!showRightData.length && setShowRightData(initValue);
		}
	}, [initData, initValue]);

	useEffect(() => {
		setKeyword('');
	}, [type]);

	const rowSelection = {
		selectedRows,
		selectedRowKeys,
		preserveSelectedRowKeys: true,
		onChange: (
			selectedRowKeys: React.Key[],
			selectedRows: AlarmContactItem[]
		) => {
			// console.log(
			//  `selectedRowKeys: ${curSelectedRowKeys}`,
			//  'selectedRows: ',
			//  curSelectedRows,
			//  selectedRows
			// );
			setSelectedRows(selectedRows);
			setSelectedRowKeys(selectedRowKeys);
		}
	};

	const itemRowSelection = {
		selectedRows: itemSelectedRows,
		selectedRowKeys: itemSelectedRowKeys,
		preserveSelectedRowKeys: true,
		onSelect: (item: any, selected: boolean) => {
			if (selected) {
				!selectedRows.find((select) => select.id === item.id) &&
					setSelectedRows([...selectedRows, item]);
				!selectedRowKeys.includes(item.id) &&
					setSelectedRowKeys([...selectedRowKeys, item.id]);
			} else {
				setSelectedRows(
					selectedRows.filter((obj) => obj.id !== item.id)
				);
				setSelectedRowKeys(
					selectedRowKeys.filter((str) => str !== item.id)
				);
			}
		},
		onSelectAll: (selected: boolean, rows: any[], changeRows: any[]) => {
			const data = [...selectedRows];
			const dataKey = [...selectedRowKeys];
			if (selected) {
				rows.filter((select) => {
					if (
						select?.id &&
						!selectedRows.some((item) => item.id === select.id)
					) {
						data.push(select);
						dataKey.push(select.id);
					}
				});
				setSelectedRows(data);
				setSelectedRowKeys(dataKey);
			} else {
				data.filter(
					(select) =>
						!changeRows.find((item) => item.id === select.id)
				);
				setSelectedRows(
					data.filter(
						(select) =>
							!changeRows.find((item) => item.id === select.id)
					)
				);
				setSelectedRowKeys(
					dataKey.filter(
						(id) => !changeRows.some((item) => item.id === id)
					)
				);
			}
		},
		onChange: (
			itemSelectedRowKeys: React.Key[],
			itemSelectedRows: AlarmContactItem[]
		) => {
			setItemSelectedRows(itemSelectedRows);
			setItemSelectedRowKeys(itemSelectedRowKeys);
		}
	};
	const onTypeChange = (e: RadioChangeEvent) => {
		setType(e.target.value);
		setItemSelectedRows([...selectedRows]);
		setItemSelectedRowKeys([...selectedRowKeys]);
	};

	const onSearch = (value: string, searchType: string) => {
		if (type === 'user') {
			const data = searchType === 'left' ? showDataSource : showRightData;
			const setData =
				searchType === 'left' ? setDataSource : setRightData;
			const result = data.filter(
				(item: any) =>
					item.aliasName?.includes(value) ||
					item.name?.includes(value) ||
					item.email?.includes(value) ||
					item.phone?.includes(value)
			);
			setData(result);
		} else {
			if (searchType === 'left') {
				const result = showRoleLists.map((item) => ({
					...item,
					users: item.users.filter(
						(res: any) =>
							res.aliasName?.includes(value) ||
							res.name?.includes(value) ||
							res.email?.includes(value) ||
							res.phone?.includes(value)
					)
				}));
				const active = result
					.filter((item: any) => item.users.length)
					?.map((item: any) => item.id);
				setActiveKey(active);
				setRoleLists(result);
			} else {
				const result = showRightData.filter(
					(item: any) =>
						item.aliasName?.includes(value) ||
						item.name?.includes(value) ||
						item.email?.includes(value) ||
						item.phone?.includes(value)
				);
				setRightData(result);
			}
		}
	};
	const toLeft = (value: any) => {
		const list = showRightData.filter((item) => value.name !== item.name);
		const showList = showRightData.filter(
			(item) => value.name !== item.name
		);
		setKeyword('');
		setRightKeyword('');
		setSelectedRows([]);
		setSelectedRowKeys([]);
		setItemSelectedRows([]);
		setItemSelectedRowKeys([]);
		setDataSource([value, ...showDataSource]);
		setShowDataSource([value, ...showDataSource]);
		setRightData(list);
		setShowRightData(showList);
		setDataTotal([value, ...showDataSource].length);
		onUserChange(list);
	};
	const toRight = () => {
		const list = showDataSource.filter(
			(item) => !selectedRows.some((arr: any) => arr.name === item.name)
		);
		const showList = showDataSource.filter(
			(item) => !selectedRows.some((arr: any) => arr.name === item.name)
		);
		setKeyword('');
		setRightKeyword('');
		setSelectedRows([]);
		setSelectedRowKeys([]);
		setItemSelectedRows([]);
		setItemSelectedRowKeys([]);
		setDataSource(list);
		setShowDataSource(showList);
		setDataTotal(showList.length);
		setRightData([...showRightData, ...selectedRows]);
		setShowRightData([...showRightData, ...selectedRows]);
		onUserChange([...showRightData, ...selectedRows]);
	};
	const minus = {
		width: 50,
		render: (value: unknown, record: any) => (
			<Button
				icon={<MinusOutlined style={{ fontSize: 12 }} />}
				style={{
					width: 16,
					height: 16,
					padding: 0
				}}
				onClick={() => toLeft(record)}
			/>
		)
	};
	return (
		<div
			className="user-transfer display-flex flex-space-between"
			style={{ margin: '0 24px' }}
		>
			<div className="transfer-container">
				<div className="transfer-header display-flex flex-space-between">
					<span>
						{selectedRows.length}/{dataTotal}条
					</span>
					<span>源列表</span>
				</div>
				<Space
					direction="vertical"
					style={{
						width: '100%',
						padding: '12px'
					}}
				>
					<Input.Search
						placeholder="请输入搜索内容"
						value={keyword}
						allowClear
						onChange={(e) => setKeyword(e.target.value)}
						onSearch={(value) => onSearch(value, 'left')}
					/>
					<Radio.Group
						onChange={onTypeChange}
						value={type}
						optionType="button"
						style={{ width: '100%' }}
					>
						<Radio.Button
							value="user"
							style={{
								width: '50%',
								height: '40px',
								lineHeight: '40px',
								textAlign: 'center',
								borderRadius: '20px 0 0 20px'
							}}
						>
							用户列表
						</Radio.Button>
						<Radio.Button
							value="role"
							style={{
								width: '50%',
								height: '40px',
								lineHeight: '40px',
								textAlign: 'center',
								borderRadius: '0 20px 20px 0'
							}}
						>
							角色列表
						</Radio.Button>
					</Radio.Group>
					<div className="transfer-content">
						{type === 'user' ? (
							<Table
								rowKey="id"
								rowSelection={{
									type: 'checkbox',
									...rowSelection
								}}
								loading={loading}
								// scroll={{ x: 830, y: 380 }}
								columns={columns}
								dataSource={dataSource}
								pagination={false}
							/>
						) : (
							<Collapse
								activeKey={activeKey}
								onChange={(value) => setActiveKey(value)}
							>
								{roleLists.map((item) => {
									const users =
										item.users.filter(
											(item: any) =>
												!showRightData.some(
													(res) =>
														res.name === item.name
												)
										) || [];
									return (
										<Panel header={item.name} key={item.id}>
											<Table
												rowKey="id"
												rowSelection={{
													type: 'checkbox',
													...itemRowSelection
												}}
												loading={loading}
												// scroll={{ x: 830, y: 380 }}
												columns={columns}
												dataSource={users}
												pagination={false}
											/>
										</Panel>
									);
								})}
							</Collapse>
						)}
					</div>
				</Space>
			</div>
			<Button
				disabled={
					selectedRowKeys.length <= 0 ||
					rightData.length > 20 ||
					selectedRowKeys.length > 20 ||
					rightData.length + selectedRowKeys?.length > 20
				}
				type="primary"
				icon={<RightOutlined />}
				onClick={toRight}
			/>
			<div className="transfer-container">
				<div className="transfer-header display-flex flex-space-between">
					<span>{rightData.length}条</span>
					<span>已选列表</span>
				</div>
				<Space
					direction="vertical"
					style={{
						width: '100%',
						padding: '12px'
					}}
				>
					<Input.Search
						placeholder="请输入搜索内容"
						value={rightKeyword}
						allowClear
						onChange={(e) => setRightKeyword(e.target.value)}
						onSearch={(value) => onSearch(value, 'right')}
					/>
					<div className="transfer-content right">
						<Table
							rowKey="id"
							columns={[minus, ...columns]}
							dataSource={rightData}
							pagination={false}
						/>
					</div>
				</Space>
			</div>
		</div>
	);
};

export default UserTransfer;

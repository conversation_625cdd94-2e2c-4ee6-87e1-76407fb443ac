import { completeOrder } from '@/services/workOrder';
import { formItemLayout618 } from '@/utils/const';
import { Form, Input, Modal, Select, notification } from 'antd';
import React, { useState } from 'react';

export default function CompleteForm({
	open,
	onCancel,
	orderId,
	executed,
	onRefresh
}: {
	open: boolean;
	onCancel: () => void;
	orderId?: string;
	executed?: boolean | null;
	onRefresh: () => void;
}) {
	const [form] = Form.useForm();
	const [loading, setLoading] = useState<boolean>(false);
	const onOk = () => {
		form.validateFields().then((values) => {
			if (orderId) {
				const sendData: CompleteSendData = {
					comment: values.comment,
					orderId: orderId,
					result: executed ? values.result : 'notExecuted'
				};
				setLoading(true);
				completeOrder(sendData)
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '工单完成成功！'
							});
							onRefresh();
							onCancel();
						} else {
							notification.error({
								message: '错误',
								description: (
									<>
										<p>{res.errorMsg}</p>
										<p>{res.errorDetail}</p>
									</>
								)
							});
						}
					})
					.finally(() => {
						setLoading(false);
					});
			} else {
				notification.warning({
					message: '提示',
					description: '未获取到当前工单信息'
				});
				return;
			}
		});
	};
	return (
		<Modal
			destroyOnClose={true}
			title="完成工单"
			open={open}
			onCancel={onCancel}
			onOk={onOk}
			confirmLoading={loading}
		>
			<Form
				preserve={false}
				form={form}
				{...formItemLayout618}
				labelAlign="left"
			>
				<Form.Item label="是否执行" name="isImplement">
					{executed ? '已执行' : '未执行'}
				</Form.Item>
				{executed && (
					<Form.Item
						name="result"
						label="执行结果"
						rules={[
							{
								required: true,
								message: '请选择执行结果'
							}
						]}
						requiredMark="optional"
					>
						<Select
							placeholder="请选择执行结果"
							options={[
								{ value: 'succeed', label: '执行成功' },
								{ value: 'failed', label: '执行失败' }
							]}
						/>
					</Form.Item>
				)}
				<Form.Item
					rules={[
						{
							type: 'string',
							min: 1,
							max: 64,
							message: '请输入长度为1-64的评论'
						}
					]}
					label="评论"
					name="comment"
				>
					<Input.TextArea rows={3} placeholder="请输入评论" />
				</Form.Item>
			</Form>
		</Modal>
	);
}

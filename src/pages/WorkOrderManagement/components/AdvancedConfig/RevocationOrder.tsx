import React, { useState, useEffect } from 'react';
import { Modal, Space, Checkbox } from 'antd';

const RevocationOrder = ({
	data,
	visible,
	onOk,
	onCancel
}: {
	data: any;
	visible: boolean;
	onOk: (res: any) => void;
	onCancel: () => void;
}): JSX.Element => {
	const [checked, setChecked] = useState<boolean>();

	useEffect(() => {
		if (data) {
			setChecked(data?.children?.['/recall/allow']?.beSelect);
		}
	}, [data]);

	const handleSubmit = () => {
		data = {
			...data,
			children: {
				'/recall/allow': {
					...data?.children?.['/recall/allow'],
					beSelect: checked
				}
			}
		};
		onOk(data);
	};

	return (
		<Modal
			title="撤回工单"
			open={visible}
			onOk={handleSubmit}
			onCancel={onCancel}
		>
			<Space>
				<Checkbox
					checked={checked}
					onChange={(e) => setChecked(e.target.checked)}
				/>
				允许提交人撤销审批中的审批单
			</Space>
		</Modal>
	);
};

export default RevocationOrder;

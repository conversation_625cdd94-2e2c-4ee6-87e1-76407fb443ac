import React, { useState, useEffect } from 'react';
import { Modal, Switch, Radio } from 'antd';

const AutoRepeat = ({
	data,
	visible,
	onOk,
	onCancel
}: {
	data: any;
	visible: boolean;
	onOk: (res: any) => void;
	onCancel: () => void;
}): JSX.Element => {
	const [manyTimeValue, setManyTimeValue] = useState<string>();
	const [manyTimeChecked, setManyTimeChecked] = useState<boolean>();
	const [autoPassChecked, setAutoPassChecked] = useState<boolean>();

	useEffect(() => {
		if (data) {
			setManyTimeChecked(data?.children?.['/repeat/manyTime']?.beSelect);
			setAutoPassChecked(data?.children?.['/repeat/autoPass']?.beSelect);
			setManyTimeValue(
				data?.children?.['/repeat/manyTime']?.children?.[
					'/repeat/manyTime/onlyFirst'
				]?.beSelect
					? 'onlyFirst'
					: 'continuous'
			);
		}
	}, [data]);

	const handleSubmit = () => {
		data = {
			...data,
			children: {
				'/repeat/manyTime': {
					...data?.children?.['/repeat/manyTime'],
					beSelect: manyTimeChecked,
					children: {
						'/repeat/manyTime/onlyFirst': {
							...data?.children?.['/repeat/manyTime']?.children?.[
								'/repeat/manyTime/onlyFirst'
							],
							beSelect: manyTimeChecked
								? manyTimeValue === 'onlyFirst'
									? true
									: false
								: false
						},
						'/repeat/manyTime/continuous': {
							...data?.children?.['/repeat/manyTime']?.children?.[
								'/repeat/manyTime/continuous'
							],
							beSelect: manyTimeChecked
								? manyTimeValue === 'continuous'
									? true
									: false
								: false
						}
					}
				},
				'/repeat/autoPass': {
					...data?.children?.['/repeat/autoPass'],
					beSelect: autoPassChecked
				}
			}
		};
		onOk(data);
	};

	return (
		<Modal
			title="自动去重"
			open={visible}
			onOk={handleSubmit}
			onCancel={onCancel}
		>
			<div>
				<div className="display-flex flex-space-between">
					<p>1、同一审批人在流程中需要多次审批时，自动去重</p>
					<Switch
						checked={manyTimeChecked}
						onChange={(checked) => setManyTimeChecked(checked)}
					/>
				</div>
				{manyTimeChecked ? (
					<Radio.Group
						value={manyTimeValue}
						onChange={(e) => setManyTimeValue(e.target.value)}
					>
						<Radio
							style={{ margin: '16px 0 16px 16px' }}
							value="onlyFirst"
						>
							在流程中出现需要多次审批时，仅保留第一个
						</Radio>
						<Radio
							style={{ margin: '0 0 16px 16px' }}
							value="continuous"
						>
							仅在连续出现需要审批时，自动去重
						</Radio>
					</Radio.Group>
				) : null}
			</div>
			<div
				className={`display-flex flex-space-between ${
					manyTimeChecked ? '' : 'mt-16'
				}`}
			>
				<p>2、审批人和发起人是同一个人，审批自动通过</p>
				<Switch
					checked={autoPassChecked}
					onChange={(checked) => setAutoPassChecked(checked)}
				/>
			</div>
		</Modal>
	);
};

export default AutoRepeat;

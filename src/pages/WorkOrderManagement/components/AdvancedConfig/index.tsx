import * as React from 'react';
import { useState, useEffect } from 'react';
import AdvancedConfigCard from './AdvancedConfigCard';
import AutoRepeat from './AutoRepeat';
import RevocationOrder from './RevocationOrder';
import ApproveOpinion from './ApproveOpinion';

const AdvancedConfig = ({
	data,
	onChange
}: {
	data: any;
	onChange: (value: any) => void;
}): JSX.Element => {
	const [sendData, setSendData] = useState<any>(data);
	const [autoVisible, setAutoVisible] = useState<boolean>(false);
	const [orderVisible, setOrderVisible] = useState<boolean>(false);
	const [approvalVisible, setApprovalVisible] = useState<boolean>(false);

	const onAutoOk = (res: any) => {
		const result = {
			...data,
			settings: {
				...data.settings,
				'/repeat': { ...res }
			}
		};
		onChange(result);
		setSendData(result);
		setAutoVisible(false);
	};

	const onOrderOk = (res: any) => {
		const result = {
			...data,
			settings: {
				...data.settings,
				'/recall': { ...res }
			}
		};
		onChange(result);
		setSendData(result);
		setOrderVisible(false);
	};

	const onOpinionOk = (res: any) => {
		const result = {
			...data,
			settings: {
				...data.settings,
				'/opinion': {
					...res
				}
			}
		};
		onChange(result);
		setSendData(result);
		setApprovalVisible(false);
	};

	return (
		<div className="advanced-config">
			<AdvancedConfigCard
				title="自动去重"
				onClick={() => setAutoVisible(true)}
				content="审批流程中审批人重复出现时，只需审批一次其余自动通过"
			/>
			<AdvancedConfigCard
				title="撤回工单"
				onClick={() => setOrderVisible(true)}
				content="开启后发起人可以撤回审批单"
			/>
			<AdvancedConfigCard
				title="审批意见"
				onClick={() => setApprovalVisible(true)}
				content="设置审批功能是否必填、评论提示信息"
			/>
			{autoVisible && (
				<AutoRepeat
					data={sendData?.settings?.['/repeat']}
					visible={autoVisible}
					onOk={onAutoOk}
					onCancel={() => setAutoVisible(false)}
				/>
			)}
			{orderVisible && (
				<RevocationOrder
					data={sendData?.settings?.['/recall']}
					visible={orderVisible}
					onOk={onOrderOk}
					onCancel={() => setOrderVisible(false)}
				/>
			)}
			{approvalVisible && (
				<ApproveOpinion
					data={sendData?.settings?.['/opinion']}
					visible={approvalVisible}
					onOk={onOpinionOk}
					onCancel={() => setApprovalVisible(false)}
				/>
			)}
		</div>
	);
};

export default AdvancedConfig;

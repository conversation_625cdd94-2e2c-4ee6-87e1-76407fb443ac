import React, { useState, useEffect } from 'react';
import { Modal, Space, Checkbox, Input } from 'antd';

const ApproveOpinion = ({
	data,
	visible,
	onOk,
	onCancel
}: {
	data: any;
	visible: boolean;
	onOk: (res: any) => void;
	onCancel: () => void;
}): JSX.Element => {
	const [value, setValue] = useState<string>();
	const [checked, setChecked] = useState<boolean>();

	useEffect(() => {
		if (data) {
			setChecked(data?.children?.['/opinion/required']?.beSelect);
			setValue(
				data?.children?.['/opinion/required']?.children?.[
					'/opinion/required/content'
				]?.content
			);
		}
	}, [data]);

	const handleSubmit = () => {
		data = {
			...data,
			children: {
				'/opinion/required': {
					...data?.children?.['/opinion/required'],
					beSelect: checked,
					children: {
						'/opinion/required/content': {
							...data?.children?.['/opinion/required']
								?.children?.['/opinion/required/content'],
							content: value
						}
					}
				}
			}
		};
		onOk(data);
	};

	return (
		<Modal
			title="审批意见"
			open={visible}
			onOk={handleSubmit}
			onCancel={onCancel}
		>
			<Space>
				<Checkbox
					checked={checked}
					onChange={(e) => setChecked(e.target.checked)}
				/>
				必须填写评论方可提交审批
			</Space>
			<Input
				value={value}
				placeholder="请输入评论"
				style={{ width: '90%', margin: '12px 24px 0' }}
				onChange={(e) => setValue(e.target.value)}
			/>
		</Modal>
	);
};

export default ApproveOpinion;

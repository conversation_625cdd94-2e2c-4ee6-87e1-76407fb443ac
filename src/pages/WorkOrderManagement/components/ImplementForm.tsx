import React, { useState } from 'react';
import { Form, Input, Modal, Select, notification } from 'antd';
import { implementOrder } from '@/services/workOrder';
import { formItemLayout618 } from '@/utils/const';

export default function ImplementForm({
	open,
	onCancel,
	orderId,
	onRefresh,
	currentStepId,
	currentStepRule
}: {
	open: boolean;
	onCancel: () => void;
	orderId?: string;
	onRefresh: () => void;
	currentStepId?: string;
	currentStepRule?: currentStepRuleItem;
}) {
	const [form] = Form.useForm();
	const [loading, setLoading] = useState<boolean>(false);
	const onOk = () => {
		form.validateFields().then((values) => {
			if (orderId && currentStepId) {
				const sendData: ImplementSendData = {
					comment: values.comment,
					orderId: orderId,
					operator: values.operator,
					currentStepId: currentStepId
				};
				setLoading(true);
				implementOrder(sendData)
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '工单处理成功！'
							});
							onRefresh();
							onCancel();
						} else {
							notification.error({
								message: '错误',
								description: (
									<>
										<p>{res.errorMsg}</p>
										<p>{res.errorDetail}</p>
									</>
								)
							});
						}
					})
					.finally(() => {
						setLoading(false);
					});
			} else {
				notification.warning({
					message: '提示',
					description: '未获取到当前工单信息'
				});
				return;
			}
		});
	};
	return (
		<Modal
			destroyOnClose={true}
			title="处理工单"
			open={open}
			onCancel={onCancel}
			onOk={onOk}
			confirmLoading={loading}
		>
			<Form
				preserve={false}
				form={form}
				{...formItemLayout618}
				labelAlign="left"
			>
				<Form.Item
					label="处理意见"
					name="operator"
					rules={[{ required: true, message: '请选择处理意见' }]}
				>
					<Select
						placeholder="请选择处理意见"
						options={[
							{ value: 'approved', label: '同意' },
							{ value: 'refused', label: '驳回' }
						]}
					/>
				</Form.Item>
				<Form.Item
					rules={[
						{
							required: currentStepRule?.needApproveOpinion,
							message: '请输入评论'
						},
						{
							type: 'string',
							min: 1,
							max: 64,
							message: '请输入长度为1-64的评论'
						}
					]}
					label="评论"
					name="comment"
				>
					<Input.TextArea
						rows={3}
						placeholder={
							currentStepRule?.opinionTip
								? currentStepRule.opinionTip
								: '请输入评论'
						}
					/>
				</Form.Item>
			</Form>
		</Modal>
	);
}

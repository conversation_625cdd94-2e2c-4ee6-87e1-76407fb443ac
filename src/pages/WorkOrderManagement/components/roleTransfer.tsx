import React, { useState, useEffect } from 'react';
import {
	Collapse,
	Spin,
	Space,
	Input,
	notification,
	Button,
	Checkbox
} from 'antd';
import { MinusOutlined, RightOutlined, UserOutlined } from '@ant-design/icons';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';
import { getManagers } from '@/services/workOrder';

const { Panel } = Collapse;

const RoleTransfer = ({
	initValue,
	roleValue,
	onRoleChange
}: {
	initValue: any;
	roleValue: any;
	onRoleChange: (val: any) => void;
}): JSX.Element => {
	const [loading, setLoading] = useState<boolean>(false);
	const [dataSource, setDataSource] = useState<any[]>([]);
	const [showDataSource, setShowDataSource] = useState<any[]>([]);
	const [showDataTotal, setShowDataTotal] = useState<number>(0);
	const [selectedRows, setSelectedRows] = useState<any[]>([]);
	const [rightData, setRightData] = useState<any[]>([]);
	const [showRightData, setShowRightData] = useState<any[]>([]);
	const [keyword, setKeyword] = useState<string>('');
	const [rightKeyword, setRightKeyword] = useState<string>('');
	const [activeKey, setActiveKey] = useState<string | string[]>([]);

	useEffect(() => {
		setLoading(true);
		getManagers({ isFlow: false, type: 'role' })
			.then((res) => {
				if (res.success) {
					const list = Object.keys(res.data).map((type) => ({
						type,
						users: res.data[type]
					}));
					const total = list.reduce((accumulator, currentValue) => {
						return accumulator + currentValue.users.length;
					}, 0);
					setDataSource(list);
					setShowDataSource(list);
					setShowDataTotal(total);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	}, []);

	useEffect(() => {
		let selectList: any = [];
		showDataSource.map((item) => {
			selectList = [
				...selectList,
				...item.users
					.filter((user: any) =>
						initValue?.some((res: any) => res.name === user.name)
					)
					.map((res: any) => ({
						id: res.id,
						type: item.type,
						name: res.name
					}))
			];
		});
		const list = showDataSource.map((item) => ({
			type: item.type,
			users: item.users.filter(
				(user: any) =>
					!initValue?.some((res: any) => res.name === user.name)
			)
		}));
		const showList = showDataSource.map((item) => ({
			type: item.type,
			users: item.users.filter(
				(user: any) =>
					!initValue?.some((res: any) => res.name === user.name)
			)
		}));
		const total = showList.reduce(
			(num, current) => (num += current.users.length),
			0
		);
		setSelectedRows([]);
		setDataSource(list);
		setShowDataTotal(total);
		setShowDataSource(showList);
		setRightData([...showRightData, ...selectList]);
		setShowRightData([...showRightData, ...selectList]);
	}, [initValue, loading]);

	const onSearch = (value: string, type: string) => {
		if (type === 'left') {
			const result = showDataSource.map((item) => ({
				...item,
				users: item.users.filter((res: any) =>
					res.name?.includes(value)
				)
			}));
			const showResult = showDataSource.map((item) => ({
				...item,
				users: item.users.map((res: any) =>
					res.name?.includes(value)
						? { ...res, disabled: true }
						: { ...res, disabled: false }
				)
			}));
			const active = result
				.filter((item: any) => item.users.length)
				?.map((item: any) => item.type);
			setActiveKey(active);
			setDataSource(result);
			setShowDataSource(showResult);
		} else {
			const result = showRightData.filter((res: any) =>
				res.name?.includes(value)
			);
			setRightData(result);
		}
	};
	const toLeft = (value: any) => {
		const list = showRightData.filter((item) => value.name !== item.name);
		const showList = showRightData.filter(
			(item) => value.name !== item.name
		);
		const leftList = showDataSource.map((item) => {
			if (item.type === value.type) {
				item.users = [{ name: value.name }, ...item.users];
			}
			return item;
		});
		const leftTotal = leftList.reduce(
			(num, current) => (num += current.users.length),
			0
		);
		setKeyword('');
		setRightKeyword('');
		setSelectedRows([]);
		setShowDataTotal(leftTotal);
		setDataSource(leftList);
		setShowDataSource(leftList);
		setRightData(list);
		setShowRightData(showList);
		onRoleChange(showList);
	};
	const toRight = () => {
		let selectList: any = [];
		showDataSource.map((item) => {
			selectList = [
				...selectList,
				...item.users
					.filter((user: any) => selectedRows.includes(user.name))
					.map((res: any) => ({
						id: res.id,
						name: res.name,
						type: item.type
					}))
			];
		});
		const list = showDataSource.map((item) => ({
			type: item.type,
			users: item.users.filter(
				(user: any) => !selectedRows.includes(user.name)
			)
		}));
		const showList = showDataSource.map((item) => ({
			type: item.type,
			users: item.users.filter(
				(user: any) => !selectedRows.includes(user.name)
			)
		}));
		const total = showList.reduce(
			(num, current) => (num += current.users.length),
			0
		);
		setKeyword('');
		setRightKeyword('');
		setSelectedRows([]);
		setDataSource(list);
		setShowDataTotal(total);
		setShowDataSource(showList);
		setRightData([...showRightData, ...selectList]);
		setShowRightData([...showRightData, ...selectList]);
		onRoleChange([...showRightData, ...selectList]);
	};
	const typeRender = (value: string) => {
		switch (value) {
			case 'manager':
				return '管理类型';
			case 'normal':
				return '普通类型';
			case 'dba':
				return 'DBA类型';
			default:
				return '/';
		}
	};
	return (
		<div
			className="role-transfer display-flex flex-space-between"
			style={{ margin: '0 24px' }}
		>
			<div className="transfer-container">
				<div className="transfer-header display-flex flex-space-between">
					<span>
						{selectedRows.length}/{showDataTotal}条
					</span>
					<span>源列表</span>
				</div>
				<Space
					direction="vertical"
					style={{
						width: '100%',
						padding: '12px'
					}}
				>
					<Input.Search
						placeholder="请输入搜索内容"
						value={keyword}
						allowClear
						onChange={(e) => setKeyword(e.target.value)}
						onSearch={(value) => onSearch(value, 'left')}
					/>
					<Spin spinning={loading}>
						<div className="transfer-content">
							<Checkbox.Group
								value={selectedRows}
								onChange={(
									CheckboxValue: CheckboxValueType[]
								) => setSelectedRows(CheckboxValue)}
								className="role-checkbox-list"
							>
								<Collapse
									activeKey={activeKey}
									onChange={(value) => setActiveKey(value)}
								>
									{showDataSource.map((item) => {
										return (
											<Panel
												header={typeRender(item.type)}
												key={item.type}
												// style={{ width: '100%' }}
											>
												{item.users?.map(
													(user: any) => {
														{
															console.log(user);
														}
														return (
															<Checkbox
																value={
																	user.name
																}
																key={user.name}
																style={{
																	display:
																		!keyword ||
																		user.disabled ||
																		typeof user.disabled ===
																			'undefined'
																			? 'inline-flex'
																			: 'none'
																}}
															>
																{user.name}
															</Checkbox>
														);
													}
												)}
											</Panel>
										);
									})}
								</Collapse>
							</Checkbox.Group>
						</div>
					</Spin>
				</Space>
			</div>
			<Button
				disabled={
					selectedRows.length <= 0 ||
					rightData.length > 5 ||
					selectedRows.length > 5 ||
					rightData.length + selectedRows?.length > 5
				}
				type="primary"
				icon={<RightOutlined />}
				onClick={toRight}
			/>
			<div className="transfer-container">
				<div className="transfer-header display-flex flex-space-between">
					<span>{rightData.length}条</span>
					<span>已选列表</span>
				</div>
				<Space
					direction="vertical"
					style={{
						width: '100%',
						padding: '12px'
					}}
				>
					<Input.Search
						placeholder="请输入搜索内容"
						value={rightKeyword}
						allowClear
						onChange={(e) => setRightKeyword(e.target.value)}
						onSearch={(value) => onSearch(value, 'right')}
					/>
					<div className="transfer-content right">
						{rightData.map((item) => {
							return (
								<div
									key={item.name}
									className="flex-align select-role-list"
								>
									<Button
										icon={
											<MinusOutlined
												style={{ fontSize: 12 }}
											/>
										}
										style={{
											margin: '0 12px 0 8px',
											width: 16,
											height: 16,
											padding: 0
										}}
										onClick={() => toLeft(item)}
									/>
									<UserOutlined style={{ marginRight: 4 }} />
									{item.name}
								</div>
							);
						})}
					</div>
				</Space>
			</div>
		</div>
	);
};

export default RoleTransfer;

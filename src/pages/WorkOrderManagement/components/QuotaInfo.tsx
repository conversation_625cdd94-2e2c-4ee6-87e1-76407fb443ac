import React, { useEffect, useState } from 'react';
import {
	Button,
	Col,
	Form,
	Input,
	InputNumber,
	Row,
	Select,
	Space,
	notification
} from 'antd';
import { MinusOutlined, PlusOutlined } from '@ant-design/icons';
import { StorageItem } from '@/pages/StorageManagement/storageManage';
import { getNamespaceStorages } from '@/services/storage';
import { formItemLayout618 } from '@/utils/const';
export default function QuotaInfo(): JSX.Element {
	const form = Form.useFormInstance();
	const clusterAndNamespace = Form.useWatch('clusterAndNamespace');
	// * 用来监听quotaList的改变，同时仅当该字段变化时重新渲染
	const quotaListForm = Form.useWatch('quotaList');
	const [storages, setStorages] = useState<StorageItem[]>([]);
	useEffect(() => {
		if (clusterAndNamespace) {
			getNamespaceStorages({
				clusterId: clusterAndNamespace?.[0],
				namespace: clusterAndNamespace?.[1]
			}).then((res) => {
				if (res.success) {
					if (res.data.length > 0) {
						setStorages(res.data);
					} else {
						setStorages([]);
					}
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			});
		} else {
			setStorages([]);
		}
	}, [clusterAndNamespace]);
	return (
		<Form.List
			name="quotaList"
			rules={[
				{
					validator: async (_, quotaList) => {
						if (!quotaList || quotaList.length === 0) {
							return Promise.reject(new Error('请添加配额信息'));
						}
					}
				}
			]}
		>
			{(fields, { add, remove }, { errors }) => (
				<>
					{fields.map((field) => (
						<Row className="display-flex" key={field.name}>
							<Col span={1} style={{ marginTop: 5 }}>
								<Button
									size="small"
									icon={<MinusOutlined />}
									type="default"
									onClick={() => remove(field.name)}
								/>
							</Col>
							<Col span={7}>
								<Form.Item
									{...field}
									name={[field.name, 'quotaType']}
									label="配额类型"
									rules={[
										{
											required: true,
											message: '请选择配额类型'
										}
									]}
									requiredMark="optional"
								>
									<Select
										placeholder="请选择配额类型"
										options={[
											{
												value: 'cpu',
												label: 'CPU',
												disabled: form
													.getFieldsValue()
													.quotaList?.find(
														(item: any) =>
															item?.quotaType ===
															'cpu'
													)
											},
											{
												value: 'memory',
												label: '内存',
												disabled: form
													.getFieldsValue()
													.quotaList?.find(
														(item: any) =>
															item?.quotaType ===
															'memory'
													)
											},
											{ value: 'storage', label: '存储' }
										]}
									/>
								</Form.Item>
							</Col>
							<Col span={15}>
								<Form.Item noStyle shouldUpdate>
									{({ getFieldsValue }) => {
										if (
											getFieldsValue().quotaList[
												field.name
											]?.quotaType === 'cpu'
										) {
											return (
												<Form.Item
													{...field}
													name={[field.name, 'cpu']}
													labelCol={{ span: 3 }}
													label="CPU"
													rules={[
														{
															required: true,
															message:
																'CPU不能为空'
														}
													]}
													requiredMark="optional"
												>
													<InputNumber
														min={0.1}
														step={0.1}
														precision={1}
														addonAfter="Core"
														placeholder="请输入CPU配额"
													/>
												</Form.Item>
											);
										}
										if (
											getFieldsValue().quotaList[
												field.name
											]?.quotaType === 'memory'
										) {
											return (
												<Form.Item
													{...field}
													name={[
														field.name,
														'memory'
													]}
													labelCol={{ span: 3 }}
													label="内存"
													rules={[
														{
															required: true,
															message:
																'内存不能为空'
														}
													]}
													requiredMark="optional"
												>
													<InputNumber
														min={0.1}
														step={0.1}
														precision={1}
														addonAfter="Gi"
														placeholder="请输入内存配额"
													/>
												</Form.Item>
											);
										}
										if (
											getFieldsValue().quotaList[
												field.name
											]?.quotaType === 'storage'
										) {
											return (
												<Space.Compact
													block
													key={field.name}
												>
													<Form.Item
														{...field}
														name={[
															field.name,
															'storageId'
														]}
														key={field.name + '1'}
														style={{ width: '50%' }}
														{...formItemLayout618}
														label="名称"
														rules={[
															{
																required: true,
																message:
																	'请选择存储'
															}
														]}
														requiredMark="optional"
													>
														<Select
															placeholder="请选择存储"
															style={{
																width: '95%'
															}}
														>
															{storages.map(
																(
																	item: StorageItem
																) => {
																	return (
																		<Select.Option
																			key={
																				item.storageId
																			}
																			value={
																				item.storageId
																			}
																			disabled={form
																				.getFieldsValue()
																				?.quotaList?.find(
																					(
																						i: any
																					) =>
																						i?.storageId ===
																						item.storageId
																				)}
																		>
																			{
																				item.aliasName
																			}
																			{item.isActiveActive ? (
																				<span className="available-domain">
																					可用区
																				</span>
																			) : null}
																		</Select.Option>
																	);
																}
															)}
														</Select>
													</Form.Item>
													<Form.Item
														{...field}
														name={[
															field.name,
															'storageQuota'
														]}
														key={field.name + '2'}
														style={{ width: '50%' }}
														{...formItemLayout618}
														label="大小"
														rules={[
															{
																required: true,
																message:
																	'存储大小不能为空'
															}
														]}
														requiredMark="optional"
													>
														<InputNumber
															style={{
																width: '100%'
															}}
															min={1}
															step={1}
															precision={0}
															addonAfter="GB"
															placeholder="请输入单个存储大小"
														/>
													</Form.Item>
												</Space.Compact>
											);
										}
									}}
								</Form.Item>
							</Col>
						</Row>
					))}
					<Form.Item wrapperCol={{ span: 24 }}>
						<Button
							type="dashed"
							onClick={() => add()}
							block
							icon={<PlusOutlined />}
						>
							新增
						</Button>
						<Form.ErrorList errors={errors} />
					</Form.Item>
				</>
			)}
		</Form.List>
	);
}

import React, { useEffect, useState } from 'react';
import { notification } from 'antd';
import { useHistory } from 'react-router';
import ProTable from '@/components/ProTable';
import {
	orgAndProRender,
	topicRender,
	workOrderStatusRender
} from '@/utils/utils';
import Actions from '@/components/Actions';
import { getOrders } from '@/services/workOrder';
import ImplementForm from './components/ImplementForm';
import CompleteForm from './components/CompleteForm';
import {
	CONTROLLED_OPERATION_BASE,
	CONTROLLED_OPERATION_Maintenance,
	CONTROLLED_OPERATION_EXPERT,
	// MIDDLEWARE_DECOMMISSIONING,
	MIDDLEWARE_DEPLOYMENT,
	SERVER_INTEGRATE_FOR_PROJECT,
	SERVER_INTEGRATE_FOR_ORGAN,
	QUOTA_REQUEST_FOR_NAMESPACE,
	QUOTA_REQUEST_FOR_PROJECT,
	QUOTA_REQUEST_FOR_ORGAN
} from '@/utils/const';
const { LinkButton } = Actions;
export default function Pending({
	refreshKey
}: {
	refreshKey: number;
}): JSX.Element {
	const history = useHistory();
	const [completeOpen, setCompleteOpen] = useState<boolean>(false);
	const [implementOpen, setImplementOpen] = useState<boolean>(false);
	const [editData, setEditData] = useState<WorkOrderItem>();
	const [loading, setLoading] = useState<boolean>(false);
	const [current, setCurrent] = useState<number>(1);
	const [size, setSize] = useState<number>(10);
	const [orders, setOrders] = useState<WorkOrderItem[]>([]);
	const [keyword, setKeyword] = useState<string>('');
	const [status, setStatus] = useState<string>('');
	const [sorter, setSorter] = useState<string>('desc');
	const [topic, setTopic] = useState<string>('');
	const [total, setTotal] = useState<number>(0);
	useEffect(() => {
		getData(current, size, keyword, status, sorter, topic);
	}, [refreshKey]);
	const getData = async (
		current: number,
		pageSize: number,
		keyword: string,
		status: string,
		sorter: string,
		topic: string
	) => {
		setLoading(true);
		const res = await getOrders({
			tab: 'waiting',
			current,
			size: pageSize,
			keyword: keyword,
			status: status,
			sortByUpdateTime: sorter,
			topic: topic
		});
		if (!res.success) {
			notification.error({
				message: '错误',
				description: (
					<>
						<p>{res.errorMsg}</p>
						<p>{res.errorDetail}</p>
					</>
				)
			});
			return;
		}
		setOrders(res.data.list);
		setCurrent(res.data.pageNum);
		setSize(res.data.pageSize);
		setTotal(res.data.total);
		setLoading(false);
	};
	const onSearch = (value: string) => {
		setKeyword(value);
		getData(1, size, value, status, sorter, topic);
	};
	const actionRender = (value: any, record: WorkOrderItem) => {
		return (
			<Actions>
				{(record.status === 'pending' ||
					record.status === 'resolving') && (
					<LinkButton
						onClick={() => {
							setEditData(record);
							setImplementOpen(true);
						}}
					>
						处理工单
					</LinkButton>
				)}
				{record.status === 'waitExecuted' && (
					<LinkButton
						onClick={() => {
							setEditData(record);
							setCompleteOpen(true);
						}}
					>
						完成工单
					</LinkButton>
				)}
			</Actions>
		);
	};
	const orderIdRender = (value: string) => {
		return (
			<span
				className="name-link"
				onClick={() => {
					history.push(`/workspace/workOrderManage/detail/${value}`);
				}}
			>
				{value}
			</span>
		);
	};
	const onTableChange = (pagination: any, filters: any, sorter: any) => {
		console.log(pagination, filters, sorter);
		let statusTemp = '';
		let sorterTemp = 'desc';
		let topicTemp = '';
		if (sorter?.order) {
			sorterTemp = sorter?.order === 'ascend' ? 'asc' : 'desc';
			setSorter(sorter?.order === 'ascend' ? 'asc' : 'desc');
		}
		if (filters?.[1]) {
			topicTemp = filters[1][0];
			setTopic(filters[1][0]);
		}
		if (filters?.[2]) {
			statusTemp = filters[2][0];
			setStatus(filters[2][0]);
		}
		setCurrent(pagination.current);
		setSize(pagination.pageSize);
		getData(
			pagination.current,
			pagination.pageSize,
			keyword,
			statusTemp,
			sorterTemp,
			topicTemp
		);
	};
	return (
		<>
			<ProTable
				search={{
					placeholder: '请输入关键词搜索',
					onSearch: onSearch
				}}
				dataSource={orders}
				rowKey="orderId"
				loading={loading}
				pagination={{
					total: total,
					current: current,
					pageSize: size
				}}
				onChange={onTableChange}
			>
				<ProTable.Column
					dataIndex="orderId"
					title="工单编号"
					render={orderIdRender}
				/>
				<ProTable.Column
					dataIndex="topic"
					title="主题"
					render={topicRender}
					filters={[
						{ value: MIDDLEWARE_DEPLOYMENT, text: '服务锁定/解锁' },
						// { value: MIDDLEWARE_DECOMMISSIONING, text: '服务解锁' },
						{
							value: CONTROLLED_OPERATION_BASE,
							text: '受限操作-基础操作'
						},
						{
							value: CONTROLLED_OPERATION_Maintenance,
							text: '受限操作-运维操作'
						},
						{
							value: CONTROLLED_OPERATION_EXPERT,
							text: '受限操作-高级操作'
						},
						{
							value: SERVER_INTEGRATE_FOR_PROJECT,
							text: '服务器接入-项目内接入'
						},
						{
							value: SERVER_INTEGRATE_FOR_ORGAN,
							text: '服务器接入-组织内接入'
						},
						{
							value: QUOTA_REQUEST_FOR_NAMESPACE,
							text: '配额申请-命名空间配额'
						},
						{
							value: QUOTA_REQUEST_FOR_PROJECT,
							text: '配额申请-项目配额'
						},
						{
							value: QUOTA_REQUEST_FOR_ORGAN,
							text: '配额申请-组织配额'
						}
					]}
					filterMultiple={false}
				/>
				<ProTable.Column
					dataIndex="status"
					title="状态"
					filters={[
						{ value: 'pending', text: '待处理' },
						{ value: 'waitExecuted', text: '待执行' }
					]}
					onFilter={(value, record: any) => record.status === value}
					filterMultiple={false}
					render={workOrderStatusRender}
				/>
				<ProTable.Column
					dataIndex="org/pro"
					title="组织/项目"
					render={orgAndProRender}
				/>
				<ProTable.Column dataIndex="creator" title="创建人" />
				<ProTable.Column dataIndex="description" title="描述" />
				<ProTable.Column
					dataIndex="updateTime"
					title="最近更新时间"
					sorter={true}
				/>
				<ProTable.Column
					dataIndex="action"
					title="操作"
					render={actionRender}
				/>
			</ProTable>
			{completeOpen && (
				<CompleteForm
					orderId={editData?.orderId}
					executed={editData?.executed}
					open={completeOpen}
					onCancel={() => {
						setEditData(undefined);
						setCompleteOpen(false);
					}}
					onRefresh={() =>
						getData(current, size, keyword, status, sorter, topic)
					}
				/>
			)}
			{implementOpen && (
				<ImplementForm
					orderId={editData?.orderId}
					currentStepId={editData?.currentStepId}
					currentStepRule={editData?.currentStepRule}
					open={implementOpen}
					onCancel={() => {
						setEditData(undefined);
						setImplementOpen(false);
					}}
					onRefresh={() =>
						getData(current, size, keyword, status, sorter, topic)
					}
				/>
			)}
		</>
	);
}

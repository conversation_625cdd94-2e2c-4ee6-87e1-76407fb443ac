import React, { useEffect, useRef, useState } from 'react';
import { Button, Col, Form, Row, notification } from 'antd';
import { useParams, useHistory } from 'react-router';
import { ResultStatusType } from 'antd/lib/result';
import { formItemLayout614 } from '@/utils/const';
import DefaultPage from '../components/DefaultPage';
import BasicInformation from '../components/Basic';
import { NamespaceItem } from '@/pages/ProjectDetail/projectDetail';
import ResultPage from '../components/ResultPage';
import FileLog from '../components/FileLog';
import StandardLog from '../components/StandardLog';
import SchedulingPolicy from '../components/SchedulingPolicy';
import ContainerConfig from '../components/ContainerConfig';
import Password from '../components/Password';
import HostNetwork from '../components/HostNetwork';
import CustomPort from '../components/CustomPort';
import DataPartition from '../components/DataPartition';
import {
	getMiddlewareDetail,
	postMiddleware,
	createMiddlewaresTemplate,
	postMiddlewareBatch
} from '@/services/middleware';
import { getNamespaceStorages } from '@/services/storage';
import { applyBackup } from '@/services/backup';
import { getAspectFrom } from '@/services/common';
import CustomForm from '../components/CustomForm';
import { getCustomFormKeys } from '@/utils/utils';
import storage from '@/utils/storage';
import transUnit from '@/utils/transUnit';
import '../index.less';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';
import TemplateNameModal from '../components/Common/templateName';
import { getProjectNamespace } from '@/services/project';
import TemplateSelect from '../components/TemplateSelect';
import TemplateBasicInformation from '../components/Basic/template';

function RedisPublish(): JSX.Element {
	const project = storage.getSession('project');
	const organization = storage.getSession('organization');
	const [form] = Form.useForm();
	const params: ParamsProps = useParams();
	const history = useHistory();
	const [currentNamespace, setCurrentNamespace] = useState<NamespaceItem>();
	const [currentPassword, setCurrentPassword] = useState<string>('');
	const [currentMode, setCurrentMode] = useState<string>('');
	const [clusterAndNamespace, setClusterAndNamespace] = useState<any>();
	const basicInfo = useRef<any>(null);
	const configData = useRef<any>(null);
	const highData = useRef<any>(null);
	const [loading, setLoading] = useState<boolean>(true);
	const [title, setTitle] = useState<string>('发布中');
	const [status, setStatus] = useState<ResultStatusType>('info');
	const [currentTemplate, setCurrentTemplate] =
		useState<ServiceTemplateItem>();
	const [templateOpen, setTemplateOpen] = useState<boolean>(false);
	// * 备份
	const backupDetail = storage.getLocal('backupDetail');
	// * 判断是否是灾备发布
	const isDisaster = params.isDisaster === 'isDisaster';
	// * 判断是否是根据模板发布
	const isTemplate = params.isTemplate === 'isTemplate';
	// * 判断是否是克隆发布
	const [judgeBackup] = useState<boolean>(
		!!(params.namespace && params.middlewareName && !isDisaster) || false
	);
	// * 动态表单
	const [customForm, setCustomForm] = useState<any>();

	useEffect(() => {
		getAspectFrom().then((res) => {
			if (res.success) {
				setCustomForm(res.data);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	}, []);
	useEffect(() => {
		if (params.clusterId) {
			if (judgeBackup) {
				setForm();
			}
		}
	}, [params.clusterId]);
	const getCurrentNamespace = (value: NamespaceItem) => {
		setCurrentNamespace(value);
	};
	const judgeGoNext = (step: number) => {
		if (step === 1) {
			if (
				form
					.getFieldsError()
					.every((item) => item.errors.length === 0) &&
				form.getFieldValue('judgeStock')
			) {
				return true;
			} else {
				notification.warning({
					message: '提醒',
					description: '当前命名空间可分配的资源不足！'
				});
				return false;
			}
		} else if (step === 2 || step === 3) {
			if (
				form.getFieldsError().every((item) => item.errors.length === 0)
			) {
				return true;
			} else {
				return false;
			}
		}
	};
	const goNext = async (value: number) => {
		if (value === 1) {
			await form.validateFields().catch((error) => {
				form.scrollToField(error.errorFields[0].name[0], {
					block: 'center'
				});
				return Promise.reject();
			});
			basicInfo.current = form.getFieldsValue();
			if (basicInfo.current.mode !== 'cluster') {
				if (
					!basicInfo.current.quota.redis.storageClassQuota ||
					!basicInfo.current.quota.redis.storageId
				) {
					notification.error({
						message: '提醒',
						description: '请输入存储配额！'
					});
					return Promise.reject();
				}
				if (basicInfo.current.quota.redis.storageClassQuota === 0) {
					notification.error({
						message: '提醒',
						description: '请输入存储配额！'
					});
					return Promise.reject();
				}
			}
			setClusterAndNamespace(basicInfo.current.clusterAndNamespace);
			return judgeGoNext(value) ? true : false;
		} else if (value === 2) {
			await form.validateFields();
			configData.current = form.getFieldsValue();
			return judgeGoNext(value) ? true : false;
		} else if (value === 3) {
			await form.validateFields();
			highData.current = form.getFieldsValue();
			setCurrentPassword(configData.current.password);
			setTimeout(() => {
				handleSubmit(highData.current);
			}, 1000);
			return judgeGoNext(value) ? true : false;
		} else {
			return false;
		}
	};
	const getExtra = () => {
		if (loading || status === 'error') {
			return (
				<Button
					type="primary"
					onClick={() => {
						history.push({
							pathname: `/project/${params.type}/${params.name}/${params.aliasName}`
						});
					}}
				>
					返回列表
				</Button>
			);
		}
		if (status === 'success') {
			if (isDisaster) {
				return (
					<Button
						type="primary"
						onClick={() => {
							history.goBack();
						}}
					>
						返回
					</Button>
				);
			}
			return [
				<Button
					key="list"
					type="primary"
					onClick={() => {
						history.push({
							pathname: `/project/${params.type}/${params.name}/${params.aliasName}`
						});
					}}
				>
					返回列表
				</Button>,
				<Button
					key="detail"
					onClick={() => {
						history.push({
							pathname: `/project/${params.type}/${params.name}/${params.aliasName}/container/basicInfo/${basicInfo?.current?.name}/${basicInfo?.current?.chartVersion}/${clusterAndNamespace[0]}/${clusterAndNamespace[1]}`
						});
					}}
				>
					查看详情
				</Button>,
				<Button
					key="template"
					onClick={() => {
						setTemplateOpen(true);
					}}
				>
					保存为模板
				</Button>
			];
		}
	};
	const steps = [
		{
			title: '基本信息',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout614}
				>
					<BasicInformation
						type={params.name}
						projectId={project.projectId}
						organId={organization.organId}
						returnCurNamespace={getCurrentNamespace}
						organizationName={organization.name}
						projectName={project.aliasName || project.name}
						setCurrentMode={setCurrentMode}
						judgeBackup={judgeBackup}
						judgeDisaster={isDisaster}
					/>
				</Form>
			)
		},
		{
			title: '基础配置',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout614}
				>
					<Password type={params.name} judgeBackup={judgeBackup} />
					<FileLog />
					<StandardLog />
				</Form>
			)
		},
		{
			title: '高级配置',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout614}
				>
					<DataPartition
						type={params.name}
						mode={currentMode}
						clusterId={clusterAndNamespace?.[0]}
						namespace={clusterAndNamespace?.[1]}
						isActiveActive={clusterAndNamespace?.[2]}
						judgeBackup={judgeBackup}
					/>
					<HostNetwork />
					<CustomPort type={params.name} />
					<SchedulingPolicy
						clusterAndNamespace={clusterAndNamespace}
					/>
					<ContainerConfig currentNamespace={currentNamespace} />
					<CustomForm
						clusterId={clusterAndNamespace?.[0]}
						namespace={currentNamespace?.name}
						customForm={customForm}
					/>
				</Form>
			)
		},
		{
			title: '发布结果',
			content: (
				<div id="publish-result-page">
					<ResultPage
						loading={loading}
						title={title}
						status={status}
						extra={getExtra()}
					>
						<div className="publish-result-page-content">
							<Row>
								<Col span={8}>
									<div className="publish-result-page-label">
										初始密码
									</div>
								</Col>
								<Col span={16}>
									<div
										className="publish-result-page-text"
										title={currentPassword || '未设置'}
									>
										{currentPassword || '未设置'}
									</div>
								</Col>
							</Row>
						</div>
					</ResultPage>
				</div>
			)
		}
	];
	// * 通过模版创建使用
	const templateSteps = [
		{
			title: '选择模板',
			content: (
				<Form form={form}>
					<TemplateSelect />
				</Form>
			)
		},
		{
			title: '基本信息',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout614}
				>
					<TemplateBasicInformation
						type={params.name}
						projectId={project.projectId}
						organId={organization.organId}
						organizationName={organization.name}
						projectName={project.aliasName || project.name}
						template={currentTemplate}
					/>
				</Form>
			)
		},
		{
			title: '高级配置',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout614}
				>
					<DataPartition
						type={params.name}
						mode={currentMode}
						clusterId={clusterAndNamespace?.[0]}
						namespace={clusterAndNamespace?.[1]}
						isActiveActive={clusterAndNamespace?.[2]}
					/>
					<HostNetwork />
					<CustomPort type={params.name} />
					<SchedulingPolicy
						clusterAndNamespace={clusterAndNamespace}
					/>
					<ContainerConfig currentNamespace={currentNamespace} />
					<CustomForm
						clusterId={clusterAndNamespace?.[0]}
						namespace={currentNamespace?.name}
						customForm={customForm}
					/>
				</Form>
			)
		},
		{
			title: '发布结果',
			content: (
				<div id="publish-result-page">
					<ResultPage
						loading={loading}
						title={title}
						status={status}
						extra={getExtra()}
					/>
				</div>
			)
		}
	];
	const judgeGoNextForTemplate = (step: number) => {
		if (step === 1) {
			const values = form.getFieldsValue();
			if (!values.template) {
				notification.warning({
					message: '提示',
					description: '请选择模板'
				});
				return false;
			}
			setCurrentTemplate(values.template);
			return true;
		} else if (step === 2 || step === 3) {
			if (
				form.getFieldsError().every((item) => item.errors.length === 0)
			) {
				return true;
			} else {
				return false;
			}
		}
	};
	const goNextForTemplate = async (value: number) => {
		if (value === 1) {
			return judgeGoNextForTemplate(value) ? true : false;
		} else if (value === 2) {
			await form.validateFields();
			basicInfo.current = form.getFieldsValue();
			setClusterAndNamespace(basicInfo.current.clusterAndNamespace);
			return judgeGoNextForTemplate(value) ? true : false;
		} else if (value === 3) {
			await form.validateFields();
			highData.current = form.getFieldsValue();
			setTimeout(() => {
				handleBatchSubmit(highData.current);
			}, 1000);
			return judgeGoNextForTemplate(value) ? true : false;
		} else {
			return false;
		}
	};
	// * 使用模版进行批量创建
	const handleBatchSubmit = async (highData: any) => {
		const quotaTemp: any = currentTemplate?.quota;
		quotaTemp.redis.storageId = basicInfo.current.storageId;
		const customVolumesTemp: any = {};
		if (highData.dataPartition) {
			for (const key in highData.customVolumes) {
				if (!highData.customVolumes[key].disabled) {
					if (highData.customVolumes[key].hostPath) {
						if (
							!highData.customVolumes[key].storageClass &&
							highData.customVolumes[key].switch
						) {
							return;
						}
					}
					quotaTemp.redis = {
						num: quotaTemp.redis.num,
						cpu: quotaTemp.redis.cpu,
						memory: quotaTemp.redis.memory
					};
					if (highData.customVolumes[key].switch !== false) {
						customVolumesTemp[key] = {
							...highData.customVolumes[key]
						};
					}
				}
			}
		}
		const sendData: any = {
			templageId: currentTemplate?.id, // ! templageId 拼写错误 待后端修改
			clusterId: clusterAndNamespace[0],
			namespace: clusterAndNamespace[1],
			deployMod: 'container',
			name: basicInfo?.current?.name,
			aliasName: basicInfo?.current?.aliasName,
			containerUID: highData?.containerUID,
			containerGID: highData?.containerGID,
			chartName: params.name,
			chartVersion: basicInfo?.current?.chartVersion,
			type: 'redis',
			labels: highData?.labels,
			annotations: highData?.annotations,
			description: basicInfo?.current?.description,
			deployNum: basicInfo?.current?.deployNum,
			mirrorImageId: basicInfo?.current?.mirrorImageId,
			scheduler: highData?.scheduler || false,
			nodeAffinity: (highData?.nodeAffinity || []).concat(
				highData?.nodeAntiAffinity || []
			),
			tolerations: highData?.tolerations,
			quota: quotaTemp,
			redisParam: {
				hostNetwork: highData?.hostNetwork || false,
				redisPort: highData?.redisPort,
				exporterPort: highData?.exporterPort
			},
			customVolumes: customVolumesTemp,
			readWriteProxy: {
				enabled:
					currentTemplate?.mode === 'readWriteProxy' ||
					currentTemplate?.mode === 'agent'
						? true
						: false
			}
		};
		// * 动态表单相关
		if (customForm) {
			const dynamicValues: any = {};
			let keys: string[] = [];
			for (const i in customForm) {
				const list = getCustomFormKeys(customForm[i]);
				keys = [...list, ...keys];
			}
			keys.forEach((item) => {
				dynamicValues[item] = highData[item];
			});
			sendData.dynamicValues = dynamicValues;
		}
		console.log(sendData);
		await ExecuteOrderFuc();
		postMiddlewareBatch(sendData)
			.then((res) => {
				setLoading(false);
				if (res.success) {
					setTitle('发布成功');
					setStatus('success');
				} else {
					setTitle('发布失败');
					setStatus('error');
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.catch((errors) => {
				setLoading(false);
				setTitle('发布失败');
				setStatus('error');
			});
	};
	// * 保存为模板
	const saveTemplateSubmit = async ({
		templateName
	}: {
		templateName: string;
	}) => {
		await form.validateFields();
		let quotaTemp: any;
		if (basicInfo.current.mode === 'cluster') {
			quotaTemp = {
				redis: {
					num: basicInfo?.current?.num,
					cpu: basicInfo?.current?.cpu,
					memory: basicInfo?.current.memory,
					storageClassQuota: basicInfo?.current?.storageClassQuota
				}
			};
		} else if (basicInfo?.current.mode === 'sentinel') {
			quotaTemp = {
				redis: {
					...basicInfo.current.quota.redis,
					num: basicInfo.current.num
				},
				sentinel: {
					...basicInfo.current.quota.sentinel
				}
			};
		} else if (basicInfo.current.mode === 'agent') {
			quotaTemp = {
				redis: {
					...basicInfo.current.quota.redis,
					num: basicInfo.current.num
				},
				proxy: {
					...basicInfo.current.quota.proxy
				}
			};
		} else {
			quotaTemp = {
				redis: {
					...basicInfo.current.quota.redis,
					num: basicInfo.current.num
				},
				sentinel: {
					...basicInfo.current.quota.sentinel
				},
				proxy: {
					...basicInfo.current.quota.proxy
				}
			};
		}
		const sendData: any = {
			projectId: project.projectId,
			organId: organization.organId,
			name: templateName,
			aliasName: basicInfo?.current?.aliasName,
			chartName: params.name,
			chartVersion: params.chartVersion,
			type: 'redis',
			description: basicInfo?.current?.description,
			version: basicInfo?.current?.version,
			mode:
				basicInfo?.current?.mode === 'cluster' ||
				basicInfo?.current?.mode === 'agent'
					? 'cluster'
					: 'sentinel',
			filelogEnabled: configData?.current?.filelogEnabled || false,
			stdoutEnabled: configData?.current?.stdoutEnabled || false,
			password: configData.current.password,
			quota: quotaTemp,
			redisParam: {
				sentinelPort: basicInfo?.current.quota?.sentinel?.sentinelPort,
				sentinelExporterPort:
					basicInfo?.current?.quota?.sentinel?.sentinelExporterPort,
				predixyPort: basicInfo?.current?.quota?.proxy?.predixyPort,
				predixyExporterPort:
					basicInfo?.current?.quota?.proxy?.predixyExporterPort
			},
			readWriteProxy: {
				enabled:
					basicInfo?.current?.mode === 'readWriteProxy' ||
					basicInfo?.current?.mode === 'agent'
						? true
						: false
			},
			deployMod: 'container'
		};
		createMiddlewaresTemplate(sendData)
			.then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '模板创建成功'
					});
					history.push(
						`/project/${params.type}/${params.name}/${params.aliasName}/templateManage`
					);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.catch((errors) => {
				notification.error({
					message: '失败',
					description: errors.errorMsg
				});
			});
	};
	// * 服务发布
	const handleSubmit = async (highData: any) => {
		let quotaTemp: any;
		if (basicInfo.current.mode === 'cluster') {
			quotaTemp = {
				redis: {
					num: basicInfo?.current?.num,
					cpu: basicInfo?.current?.cpu,
					memory: basicInfo?.current.memory,
					storageId: basicInfo?.current?.storageId,
					storageClassQuota: basicInfo?.current?.storageClassQuota
				}
			};
		} else if (basicInfo?.current.mode === 'sentinel') {
			quotaTemp = {
				redis: {
					...basicInfo.current.quota.redis,
					num: basicInfo.current.num
				},
				sentinel: {
					...basicInfo.current.quota.sentinel
				}
			};
		} else if (basicInfo.current.mode === 'agent') {
			quotaTemp = {
				redis: {
					...basicInfo.current.quota.redis,
					num: basicInfo.current.num
				},
				proxy: {
					...basicInfo.current.quota.proxy
				}
			};
		} else {
			quotaTemp = {
				redis: {
					...basicInfo.current.quota.redis,
					num: basicInfo.current.num
				},
				sentinel: {
					...basicInfo.current.quota.sentinel
				},
				proxy: {
					...basicInfo.current.quota.proxy
				}
			};
		}
		const customVolumesTemp: any = {};
		if (highData.dataPartition) {
			for (const key in highData.customVolumes) {
				if (!highData.customVolumes[key].disabled) {
					if (highData.customVolumes[key].hostPath) {
						if (
							!highData.customVolumes[key].storageClass &&
							highData.customVolumes[key].switch
						) {
							return;
						}
					}
					quotaTemp.redis = {
						num: quotaTemp.redis.num,
						cpu: quotaTemp.redis.cpu,
						memory: quotaTemp.redis.memory
					};
					if (highData.customVolumes[key].switch !== false) {
						customVolumesTemp[key] = {
							...highData.customVolumes[key]
						};
					}
				}
			}
		}
		let sendData: RedisServiceSendData = {
			clusterId: isDisaster
				? form.getFieldValue('clusterAndNamespace')[0]
				: clusterAndNamespace[0],
			namespace: isDisaster
				? form.getFieldValue('clusterAndNamespace')[1]
				: currentNamespace?.name || clusterAndNamespace[1],
			deployMod: 'container',
			name: basicInfo?.current?.name,
			aliasName: basicInfo?.current?.aliasName,
			containerUID: highData?.containerUID,
			containerGID: highData?.containerGID,
			chartName: params.name,
			chartVersion: basicInfo?.current?.chartVersion,
			type: 'redis',
			labels: highData?.labels,
			annotations: highData?.annotations,
			description: basicInfo?.current?.description,
			version: basicInfo?.current?.version,
			mode:
				basicInfo?.current?.mode === 'cluster' ||
				basicInfo?.current?.mode === 'agent'
					? 'cluster'
					: 'sentinel',
			mirrorImageId: basicInfo?.current?.mirrorImageId,
			filelogEnabled: configData?.current?.filelogEnabled || false,
			stdoutEnabled: configData?.current?.stdoutEnabled || false,
			scheduler: highData?.scheduler || false,
			nodeAffinity: (highData?.nodeAffinity || []).concat(
				highData?.nodeAntiAffinity || []
			),
			tolerations: highData?.tolerations,
			password: configData.current.password,
			quota: quotaTemp,
			redisParam: {
				hostNetwork: highData?.hostNetwork || false,
				redisPort: highData?.redisPort,
				exporterPort: highData?.exporterPort,
				sentinelPort: basicInfo?.current.quota?.sentinel?.sentinelPort,
				sentinelExporterPort:
					basicInfo?.current?.quota?.sentinel?.sentinelExporterPort,
				predixyPort: basicInfo?.current?.quota?.proxy?.predixyPort,
				predixyExporterPort:
					basicInfo?.current?.quota?.proxy?.predixyExporterPort
			},
			customVolumes: customVolumesTemp,
			readWriteProxy: {
				enabled:
					basicInfo?.current?.mode === 'readWriteProxy' ||
					basicInfo?.current?.mode === 'agent'
						? true
						: false
			}
		};
		// * 动态表单相关
		if (customForm) {
			const dynamicValues: any = {};
			let keys: string[] = [];
			for (const i in customForm) {
				const list = getCustomFormKeys(customForm[i]);
				keys = [...list, ...keys];
			}
			keys.forEach((item) => {
				dynamicValues[item] = highData[item];
			});
			sendData.dynamicValues = dynamicValues;
		}
		console.log(sendData);
		await ExecuteOrderFuc();
		if (params.middlewareName && params.namespace && judgeBackup) {
			// * 为了解决克隆时，无法克隆参数列表中的数据
			sendData = {
				...sendData,
				isBackup: true,
				relationMiddleware: {
					clusterId: sendData.clusterId,
					namespace: params.namespace,
					name: params.middlewareName,
					type: backupDetail.sourceType
				}
			};
			const result = {
				clusterId: params.clusterId,
				namespace: params.namespace || currentNamespace?.name,
				middlewareName: basicInfo?.current?.name,
				type: storage.getLocal('backupDetail').sourceType,
				backupId: storage.getLocal('backupDetail').backupId,
				activeArea: storage.getLocal('backupDetail').activeArea,
				sourceName: storage.getLocal('backupDetail').sourceName,
				backupName:
					storage.getLocal('backupDetail').recoveryType === 'time'
						? storage.getLocal('backupDetail').backupIncName
						: storage.getLocal('backupDetail').backupRecordName ||
						  storage.getLocal('backupDetail').backupName
			};
			applyBackup(result);
		}
		postMiddleware(sendData)
			.then((res) => {
				setLoading(false);
				if (res.success) {
					setTitle('发布成功');
					setStatus('success');
				} else {
					setTitle('发布失败');
					setStatus('error');
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.catch((errors) => {
				setLoading(false);
				setTitle('发布失败');
				setStatus('error');
			});
	};
	const setForm = async () => {
		const res = await getMiddlewareDetail({
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params.middlewareName,
			type: 'redis'
		});
		const res2 = await getNamespaceStorages({
			clusterId: params.clusterId,
			namespace: params.namespace
		});
		const res3 = await getProjectNamespace({
			organId: organization.organId,
			projectId: project.projectId,
			clusterId: params.clusterId,
			withQuota: true
		});
		// * 判断当前命名空间是否是双活命名空间
		const cur_namespace = res3.data.find(
			(item) => item.name === params.namespace
		);
		if (!res.success) {
			notification.error({
				message: '错误',
				description: (
					<>
						<p>{res.errorMsg}</p>
						<p>{res.errorDetail}</p>
					</>
				)
			});
			return;
		}
		// * 判断调度策略开关是否开启
		let schedulingPolicyTemp = false;
		if (
			(res.data.nodeAffinity || []).length !== 0 ||
			!!res.data.scheduler ||
			(res.data?.tolerations || []).length !== 0
		) {
			schedulingPolicyTemp = true;
		}
		const nodeAffinityTemp =
			res.data.nodeAffinity?.filter(
				(i: AffinityItem) => i.anti === false
			) || [];
		const antiNodeAffinityTemp =
			res.data.nodeAffinity?.filter(
				(i: AffinityItem) => i.anti === true
			) || [];
		// * 判断容器配置开关是否开启
		let containerConfigTemp = false;
		if (
			res.data.containerGID ||
			res.data.containerUID ||
			res.data.labels ||
			res.data.annotations
		) {
			containerConfigTemp = true;
		}
		// * 获取存储总额 storageMax
		let storageMaxTemp = 0;
		const stp = res2.data.find(
			(i: any) => i.storageId === res.data.quota.redis.storageId
		);
		storageMaxTemp = stp?.quota?.request - stp?.quota?.used || 0;
		// * quota
		let quotaTemp: any;
		if (res.data.mode === 'sentinel') {
			if (res.data.readWriteProxy.enabled) {
				quotaTemp = {
					redis: {
						cpu: +res.data.quota.redis.cpu,
						memory: +transUnit.removeUnit(
							res.data.quota.redis.memory,
							'Gi'
						),
						num: res.data.quota.redis.num,
						storageId: res.data.customVolumes
							? null
							: res.data.quota.redis.storageId,
						storageClassNameAndAliasName: res.data.customVolumes
							? null
							: `${res.data.quota.redis.storageClassName}/${res.data.quota.redis.storageClassAliasName}`,
						storageClassQuota: 0,
						instanceSpec: 'Customize',
						title: 'Redis节点',
						specId: '1',
						storageMax: storageMaxTemp
					},
					sentinel: {
						cpu: +res.data.quota.sentinel.cpu,
						memory: +transUnit.removeUnit(
							res.data.quota.sentinel.memory,
							'Gi'
						),
						num: res.data.quota.sentinel.num,
						instanceSpec: 'Customize',
						title: '哨兵节点',
						specId: '1',
						sentinelPort: res.data?.redisParam?.sentinelPort,
						sentinelExporterPort:
							res.data.redisParam?.sentinelExporterPort
					},
					proxy: {
						cpu: +res.data.quota.proxy.cpu,
						memory: +transUnit.removeUnit(
							res.data.quota.proxy.memory,
							'Gi'
						),
						num: res.data.quota.proxy.num,
						instanceSpec: 'Customize',
						title: 'proxy节点',
						specId: '1',
						predixyPort: res.data.redisParam?.predixyPort,
						predixyExporterPort:
							res.data.redisParam?.predixyExporterPort
					}
				};
			} else {
				quotaTemp = {
					redis: {
						cpu: +res.data.quota.redis.cpu,
						memory: +transUnit.removeUnit(
							res.data.quota.redis.memory,
							'Gi'
						),
						num: res.data.quota.redis.num,
						storageId: res.data.customVolumes
							? null
							: res.data.quota.redis.storageId,
						storageClassNameAndAliasName: res.data.customVolumes
							? null
							: `${res.data.quota.redis.storageClassName}/${res.data.quota.redis.storageClassAliasName}`,
						storageClassQuota: 0,
						instanceSpec: 'Customize',
						title: 'Redis节点',
						specId: '1',
						storageMax: storageMaxTemp
					},
					sentinel: {
						cpu: +res.data.quota.sentinel.cpu,
						memory: +transUnit.removeUnit(
							res.data.quota.sentinel.memory,
							'Gi'
						),
						num: res.data.quota.sentinel.num,
						instanceSpec: 'Customize',
						title: '哨兵节点',
						specId: '1',
						sentinelPort: res.data?.redisParam?.sentinelPort,
						sentinelExporterPort:
							res.data.redisParam?.sentinelExporterPort
					}
				};
			}
		} else if (res.data.mode === 'cluster') {
			if (res.data.readWriteProxy.enabled) {
				quotaTemp = {
					redis: {
						cpu: +res.data.quota.redis.cpu,
						memory: +transUnit.removeUnit(
							res.data.quota.redis.memory,
							'Gi'
						),
						num: res.data.quota.redis.num,
						storageId: res.data.customVolumes
							? null
							: res.data.quota.redis.storageId,
						storageClassNameAndAliasName: res.data.customVolumes
							? null
							: `${res.data.quota.redis.storageClassName}/${res.data.quota.redis.storageClassAliasName}`,
						storageClassQuota: 0,
						instanceSpec: 'Customize',
						title: 'Redis节点',
						specId: '1',
						storageMax: storageMaxTemp
					},
					proxy: {
						cpu: +res.data.quota.proxy.cpu,
						memory: +transUnit.removeUnit(
							res.data.quota.proxy.memory,
							'Gi'
						),
						num: res.data.quota.proxy.num,
						instanceSpec: 'Customize',
						title: 'proxy节点',
						specId: '1',
						predixyPort: res.data.redisParam?.predixyPort,
						predixyExporterPort:
							res.data.redisParam?.predixyExporterPort
					}
				};
			} else {
				quotaTemp = {
					redis: {
						cpu: +res.data.quota.redis.cpu,
						memory: +transUnit.removeUnit(
							res.data.quota.redis.memory,
							'Gi'
						),
						num: res.data.quota.redis.num,
						storageId: res.data.customVolumes
							? null
							: res.data.quota.redis.storageId,
						storageClassNameAndAliasName: res.data.customVolumes
							? null
							: `${res.data.quota.redis.storageClassName}/${res.data.quota.redis.storageClassAliasName}`,
						storageClassQuota: 0,
						instanceSpec: 'Customize',
						title: 'Redis节点',
						specId: '1',
						storageMax: storageMaxTemp
					}
				};
			}
		} else {
			quotaTemp = {
				redis: {
					cpu: +res.data.quota.redis.cpu,
					memory: +transUnit.removeUnit(
						res.data.quota.redis.memory,
						'Gi'
					),
					num: res.data.quota.redis.num,
					storageId: res.data.customVolumes
						? null
						: res.data.quota.redis.storageId,
					storageClassNameAndAliasName: res.data.customVolumes
						? null
						: `${res.data.quota.redis.storageClassName}/${res.data.quota.redis.storageClassAliasName}`,
					storageClassQuota: 0,
					instanceSpec: 'Customize',
					title: 'Redis节点',
					specId: '1',
					storageMax: storageMaxTemp
				}
			};
		}
		const modeTemp =
			res.data.mode === 'cluster'
				? res.data.readWriteProxy.enabled
					? 'agent'
					: 'cluster'
				: res.data.readWriteProxy.enabled
				? 'readWriteProxy'
				: 'sentinel';
		form.setFieldsValue({
			clusterAndNamespace: [
				params.clusterId,
				params.namespace,
				cur_namespace?.availableDomain
			],
			name: params.middlewareName + '-backup',
			aliasName: res.data.aliasName,
			description: res.data.description,
			mirrorImageId: res.data.mirrorImageId
				? +res.data.mirrorImageId
				: null,
			version: res.data.version,
			chartVersion: params.chartVersion,
			mode: modeTemp,
			num: res.data.quota.redis.num,
			instanceSpec: 'Customize',
			cpu: +res.data.quota.redis.cpu,
			memory: +transUnit.removeUnit(res.data.quota.redis.memory, 'Gi'),
			storageId: res.data.customVolumes
				? null
				: res.data.quota.redis.storageId,
			storageClassNameAndAliasName: res.data.customVolumes
				? null
				: `${res.data.quota.redis.storageClassName}/${res.data.quota.redis.storageClassAliasName}`,
			password: res.data.password,
			stdoutEnabled: res.data.stdoutEnabled,
			filelogEnabled: res.data.filelogEnabled,
			customPort: true,
			redisPort: res.data.redisParam.redisPort,
			exporterPort: res.data.redisParam.exporterPort,
			hostNetwork: res.data.redisParam.hostNetwork,
			schedulingPolicy: schedulingPolicyTemp,
			scheduler: res.data.scheduler || false,
			nodeAffinitySwitch: nodeAffinityTemp.length !== 0,
			nodeAffinity: nodeAffinityTemp,
			nodeAntiAffinitySwitch: antiNodeAffinityTemp.length !== 0,
			nodeAntiAffinity: antiNodeAffinityTemp,
			tolerationsSwitch: (res.data.tolerations || []).length !== 0,
			tolerations: res.data.tolerations,
			containerConfig: containerConfigTemp,
			containerGID: res.data.containerGID,
			containerUID: res.data.containerUID,
			labels: res.data.labels,
			annotations: res.data.annotations,
			quota: quotaTemp,
			dataPartition: res.data.customVolumes ? true : false,
			dataPartitionDisabled: res.data.customVolumes ? false : true
		});
	};
	return (
		<>
			<DefaultPage
				title="发布Redis服务"
				onBack={() => window.history.back()}
				steps={isTemplate ? templateSteps : steps}
				goNext={isTemplate ? goNextForTemplate : goNext}
			/>
			<TemplateNameModal
				open={templateOpen}
				onCancel={() => setTemplateOpen(false)}
				onCreate={saveTemplateSubmit}
			/>
		</>
	);
}
export default RedisPublish;

import React from 'react';
import { Input, Form } from 'antd';
import pattern from '@/utils/pattern';
import { getMiddlewareNameCheck } from '@/services/middleware';
import { notAllowedName } from '@/utils/const';

const FormItem = Form.Item;
interface MiddlewareNameProps {
	type: string;
}
export default function MiddlewareName(
	props: MiddlewareNameProps
): JSX.Element {
	const formClusterAndNamespace = Form.useWatch('clusterAndNamespace');
	const { type } = props;
	return (
		<FormItem
			name="name"
			label="服务名称"
			labelAlign="left"
			colon={false}
			validateTrigger={['onBlur', 'onSubmit', 'onChange']}
			rules={[
				{
					required: true,
					message: '请输入服务名称'
				},
				{
					pattern: new RegExp(pattern.name),
					message:
						'请输入以小写字母开头，小写字母数字及“-”组成的2-24个字符'
				},
				({ getFieldValue }) => ({
					validateTrigger: ['onBlur', 'onSubmit'],
					async validator(_, value) {
						if (!value) {
							return Promise.resolve();
						}
						if (notAllowedName.includes(value)) {
							return Promise.reject(
								new Error('无法使用保留词进行命名！')
							);
						}
						if (!formClusterAndNamespace) {
							return Promise.reject(
								new Error(
									'请先选择集群，否则无法进行重名校验！'
								)
							);
						}
						const np = formClusterAndNamespace?.[1];
						if (!np) {
							return Promise.reject(
								new Error(
									'请先选择命名空间，否则无法进行重名校验！'
								)
							);
						}
						const reg = new RegExp(pattern.name);
						if (!reg.test(value)) return;
						const res = await getMiddlewareNameCheck({
							clusterId: formClusterAndNamespace?.[0],
							namespace: np,
							type,
							middlewareName: value,
							deployMod: 'container'
						});
						if (res.success) {
							if (res.data) {
								return Promise.reject(
									new Error('当前中间件名称已存在！')
								);
							} else {
								return Promise.resolve();
							}
						} else {
							return Promise.reject(
								new Error('中间件服务名称校验失败！')
							);
						}
					}
				})
			]}
		>
			<Input placeholder="请输入以小写字母开头，小写字母数字及“-”组成的2-24个字符" />
		</FormItem>
	);
}

import React, { useEffect, useState } from 'react';
import { Form, Space } from 'antd';
import { NamespaceItem } from '@/pages/ProjectDetail/projectDetail';
import MidProgress from '@/components/MidProgress';
import { CpuMemoryItem } from '@/pages/OrganizationDetail/organization.detail';

interface StockAssessmentProps {
	replicas: number; // * 当需要特殊计算时，传0
	type: string;
	currentNamespace?: NamespaceItem | CpuMemoryItem;
}
// * cpu/memory/storage 计算方法详见文档：《中间件资源使用预估计算方式》https://zeusharmonycloud.yuque.com/zkdwn1/eai56u/avv4fttftkkcptny
export default function StockAssessment(
	props: StockAssessmentProps
): JSX.Element {
	const { replicas, type, currentNamespace } = props;
	const form = Form.useFormInstance();
	const cpu = Form.useWatch('cpu');
	const memory = Form.useWatch('memory');
	const storageId = Form.useWatch('storageId');
	const storageIsActiveActive = Form.useWatch('storageIsActiveActive');
	const storageClassQuota = Form.useWatch('storageClassQuota');
	const instanceSpec = Form.useWatch('instanceSpec');
	const storageMax = Form.useWatch('storageMax');
	const quota = Form.useWatch('quota');
	const enabled = Form.useWatch(['readWriteProxy', 'enabled']);
	const mode = Form.useWatch('mode');
	const deployNum = Form.useWatch('deployNum');
	const [totalCpu, setTotalCpu] = useState<number>(0);
	const [totalMemory, setTotalMemory] = useState<number>(0);
	const [totalStorage, setTotalStorage] = useState<number>(0);
	const [storageStock, setStorageStock] = useState<number>(0);
	const [namespace, setNamespace] = useState<any>();
	useEffect(() => {
		if (currentNamespace) {
			setNamespace({
				cpu: {
					request: (currentNamespace as NamespaceItem)?.quotas?.cpu
						?.request,
					used: (currentNamespace as NamespaceItem)?.quotas?.cpu?.used
				},
				memory: {
					request: (currentNamespace as NamespaceItem)?.quotas?.memory
						?.request,
					used: (currentNamespace as NamespaceItem)?.quotas?.memory
						?.used
				}
			});
		}
	}, [currentNamespace]);
	useEffect(() => {
		if (quota) {
			let r = 0; // * 总和
			const storageMaxTemp: any = {}; // * 去重使用
			for (const i in quota) {
				if (!quota[i].disabled) {
					storageMaxTemp[quota[i].storageId] =
						quota[i].storageMax || 0;
				}
			}
			for (const i in storageMaxTemp) {
				r += storageMaxTemp[i];
			}
			setStorageStock(r || 0);
		}
	}, [quota]);
	useEffect(() => {
		if (storageMax) {
			if (typeof storageMax === 'number') {
				setStorageStock(storageMax);
			}
		}
	}, [storageMax]);
	useEffect(() => {
		const cncpu =
			(namespace?.cpu?.request || 0) - (namespace?.cpu?.used || 0);
		const cnmemory =
			(namespace?.memory?.request || 0) - (namespace?.memory?.used || 0);
		if (
			totalStorage > storageMax ||
			totalCpu > cncpu ||
			totalMemory > cnmemory
		) {
			form.setFieldsValue({
				judgeStock: false
			});
		} else {
			form.setFieldsValue({
				judgeStock: true
			});
		}
	}, [storageMax, namespace, totalCpu, totalMemory, totalStorage]);
	useEffect(() => {
		if (type === 'kafka') {
			const cpuTemp = cpu * replicas + 0.2 + 0.5;
			const memoryTemp = memory * replicas + 0.5 + 0.5;
			const storageTemp = (storageClassQuota || 0) * replicas;
			setTotalCpu(cpuTemp || 0);
			setTotalMemory(memoryTemp || 0);
			setTotalStorage(storageTemp);
		} else if (type === 'rocketmq') {
			let replicasTemp = replicas;
			if (mode === '2m-noslave') replicasTemp = 2;
			if (mode === '2m-2s') replicasTemp = 4;
			if (mode === '3m-3s') replicasTemp = 6;
			const cpuTemp = cpu * replicasTemp + 0.5 + 1 + 2;
			const memoryTemp = memory * replicasTemp + 1 + 2 + 2;
			const storageTemp = (storageClassQuota || 0) * replicasTemp;
			setTotalCpu(cpuTemp || 0);
			setTotalMemory(memoryTemp || 0);
			setTotalStorage(storageTemp);
		} else if (type === 'elasticsearch') {
			const obj: any = {};
			for (const i in quota) {
				if (!quota[i].disabled) {
					obj[i] = quota[i];
				}
			}
			let cpuTemp = 0.1;
			let memoryTemp = 0.125;
			let storageTemp = 0;
			for (const i in obj) {
				cpuTemp += obj[i].num * obj[i].cpu;
				memoryTemp += obj[i].num * obj[i].memory;
				if (obj[i].storageClassQuota) {
					storageTemp += obj[i].num * obj[i].storageClassQuota;
				}
			}
			setTotalCpu(cpuTemp);
			setTotalMemory(memoryTemp);
			setTotalStorage(storageTemp);
		} else if (type === 'redis') {
			const totalCpuTemp = (cpu + 0.025) * replicas;
			const totalMemoryTemp = (memory + 0.05) * replicas;
			if (
				mode === 'sentinel' ||
				mode === 'readWriteProxy' ||
				mode === 'agent'
			) {
				setTotalStorage(quota?.redis?.storageClassQuota * replicas);
			} else {
				const storageTemp = (storageClassQuota || 0) * replicas;
				setTotalStorage(storageTemp);
			}
			if (mode === 'cluster') {
				setTotalCpu(totalCpuTemp || 0);
				setTotalMemory(totalMemoryTemp || 0);
			} else if (mode === 'agent') {
				const cpuTemp = (quota?.redis?.cpu + 0.025) * replicas;
				const memoryTemp = (quota?.redis?.memory + 0.05) * replicas;
				const proxyReplicas =
					replicas / 2 == 1 ? replicas : replicas / 2;
				const proxyCpu = quota?.proxy?.cpu ?? 1;
				let proxyMemory = quota?.proxy?.memory;
				if (proxyMemory < 0.256) {
					proxyMemory = 0.256;
				} else if (proxyMemory > 2) {
					proxyMemory = 2;
				}
				const totalProxyCpu = proxyCpu * proxyReplicas;
				const totalProxyMemory = proxyMemory * proxyReplicas;
				setTotalCpu(cpuTemp + totalProxyCpu || 0);
				setTotalMemory(memoryTemp + totalProxyMemory || 0);
			} else if (mode === 'sentinel') {
				const cpuTemp = (quota?.redis?.cpu + 0.025) * replicas;
				const memoryTemp = (quota?.redis?.memory + 0.05) * replicas;
				const senCpuTemp = quota?.sentinel?.cpu * quota?.sentinel?.num;
				const senMemoryTemp =
					quota?.sentinel?.memory * quota?.sentinel?.num;
				setTotalCpu(cpuTemp + senCpuTemp || 0);
				setTotalMemory(memoryTemp + senMemoryTemp || 0);
			} else if (mode === 'readWriteProxy') {
				const cpuTemp = (quota?.redis?.cpu + 0.025) * replicas;
				const memoryTemp = (quota?.redis?.memory + 0.05) * replicas;
				const senCpuTemp = quota?.sentinel?.cpu * quota?.sentinel?.num;
				const senMemoryTemp =
					quota?.sentinel?.memory * quota?.sentinel?.num;
				const proxyReplicas =
					replicas / 2 == 1 ? replicas : replicas / 2;
				const proxyCpuTemp = quota?.proxy?.cpu * proxyReplicas;
				const proxyMemory = quota?.proxy?.memory;
				const proxyMemoryTotal = proxyMemory * proxyReplicas;
				setTotalCpu(cpuTemp + senCpuTemp + proxyCpuTemp || 0);
				setTotalMemory(
					memoryTemp + senMemoryTemp + proxyMemoryTotal || 0
				);
			}
		} else if (type === 'postgresql') {
			let replicasTemp = replicas;
			if (isNaN(replicas)) {
				if (mode === '1m-0s') {
					replicasTemp = 1;
				}
				if (mode === '1m-1s') {
					replicasTemp = 2;
				}
				if (mode === '1m-3s') {
					replicasTemp = 4;
				}
			}
			const cpuTemp = (cpu + 0.1) * replicasTemp;
			const memoryTemp = (memory + 0.125) * replicasTemp;
			const storageTemp = (storageClassQuota || 0) * replicasTemp;
			setTotalCpu(cpuTemp || 0);
			setTotalMemory(memoryTemp || 0);
			setTotalStorage(storageTemp);
		} else if (type === 'mysql') {
			if (enabled === true) {
				const cpuTemp = (quota?.mysql?.cpu + 0.2 + 0.2) * replicas;
				const memoryTemp =
					(quota?.mysql?.memory + 0.2 + 0.2) * replicas;
				const proxyCpu = quota?.proxy?.cpu;
				const proxyMemory = quota?.proxy?.memory;
				const proxyCpuTemp = proxyCpu * replicas;
				const proxyMemoryTemp = proxyMemory * replicas;
				const storageTemp = quota?.mysql?.storageClassQuota * replicas;
				setTotalCpu(cpuTemp + proxyCpuTemp || 0);
				setTotalMemory(memoryTemp + proxyMemoryTemp || 0);
				setTotalStorage(storageTemp);
			} else {
				const cpuTemp = (cpu + 0.2 + 0.2) * replicas;
				const memoryTemp = (memory + 0.2 + 0.2) * replicas;
				const storageTemp = (storageClassQuota || 0) * replicas;
				setTotalCpu(cpuTemp || 0);
				setTotalMemory(memoryTemp || 0);
				setTotalStorage(storageTemp);
			}
		} else {
			const cpuTemp = cpu * replicas;
			const memoryTemp = memory * replicas;
			const storageTemp = (storageClassQuota || 0) * replicas;
			setTotalCpu(cpuTemp || 0);
			setTotalMemory(memoryTemp || 0);
			setTotalStorage(storageTemp);
		}
	}, [cpu, memory, storageClassQuota, instanceSpec, replicas, quota, mode]);
	const totalStorageRender = () => {
		const total_storage_GB = storageIsActiveActive
			? totalStorage / 2
			: totalStorage;
		if (deployNum) {
			return total_storage_GB * deployNum;
		} else {
			return total_storage_GB;
		}
	};
	return (
		<>
			<strong>资源评估</strong>
			<Form.Item name="judgeStock" label="当前规格预计使用资源">
				<Space>
					<div
						className="ml-12"
						style={{
							width:
								namespace &&
								(namespace?.cpu?.request || 0) -
									(namespace?.cpu?.used || 0) ===
									0
									? 230
									: 170
						}}
					>
						<label>
							CPU
							{namespace &&
								(namespace.cpu?.request || 0) -
									(namespace.cpu?.used || 0) ===
									0 && (
									<span
										style={{
											color: '#ff4d4f',
											marginLeft: 2
										}}
									>
										(当前CPU无剩余可使用量，请切换！)
									</span>
								)}
						</label>
						<MidProgress
							fromColor="#226EE7"
							toColor="#47A7F5"
							unit="Core"
							used={deployNum ? totalCpu * deployNum : totalCpu}
							total={
								(namespace?.cpu?.request || 0) -
								(namespace?.cpu?.used || 0)
							}
						/>
					</div>
					<div
						className="ml-12"
						style={{
							width:
								namespace &&
								(namespace?.cpu?.request || 0) -
									(namespace?.cpu?.used || 0) ===
									0
									? 230
									: 170
						}}
					>
						<label>
							内存
							{namespace &&
								(namespace.memory?.request || 0) -
									(namespace.memory?.used || 0) ===
									0 && (
									<span
										style={{
											color: '#ff4d4f',
											marginLeft: 2
										}}
									>
										(当前内存无剩余可使用量，请切换！)
									</span>
								)}
						</label>
						<MidProgress
							fromColor="#1AC1C4"
							toColor="#74DDDF"
							unit="GB"
							used={
								deployNum
									? totalMemory * deployNum
									: totalMemory
							}
							total={
								(namespace?.memory?.request || 0) -
								(namespace?.memory?.used || 0)
							}
						/>
					</div>
					<div className="ml-12" style={{ width: 250 }}>
						<label>
							存储
							{storageId && storageMax === 0 ? (
								<span
									style={{ color: '#ff4d4f', marginLeft: 2 }}
								>
									(当前存储剩余可使用量不足，请切换！)
								</span>
							) : null}
						</label>
						<MidProgress
							fromColor="#5C0EDF"
							toColor="#853CFF"
							unit="GB"
							used={totalStorageRender()}
							total={storageStock}
						/>
					</div>
				</Space>
			</Form.Item>
		</>
	);
}

.service-pod-card-content {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	.service-pod-card {
		display: flex;
		align-items: center;
		gap: 8px;
		padding: 8px;
		.service-pod-card-item {
			width: 275px;
			min-height: 160px;
			border: 1px solid @border-color;
			padding: 12px;
			border-radius: @border-radius;
			.service-pod-card-item-title {
				display: flex;
				justify-content: space-between;
				.service-pod-card-icon {
					display: flex;
				}
			}
			.service-pod-card-item-content {
				padding:@padding 7px;
				& > p {
					color: @black-4;
					line-height: @line-height-3;
				}
			}
		}
	}
}

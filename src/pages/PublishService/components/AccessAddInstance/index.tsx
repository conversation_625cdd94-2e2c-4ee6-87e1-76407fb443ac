import React, { useEffect, useState } from 'react';
import { Badge, Button, Form, notification, Modal } from 'antd';
import { useParams } from 'react-router';
import Actions from '@/components/Actions';
import { IconFont } from '@/components/IconFont';
import { ListCardItem, ListPanel } from '@/components/ListCard';
import ProList from '@/components/ProList';
import AccessInstance from '@/pages/ServiceListDetail/AccessBasicInfo/AccessInstance';
import { checkStatus } from '@/services/agent';
import './index.less';
const LinkButton = Actions.LinkButton;
const { confirm } = Modal;
export default function AccessAddInstance({
	agentInfo,
	basicInfo,
	podsInfo,
	clusterAndNamespace
}: {
	agentInfo: any;
	basicInfo: any;
	podsInfo: any;
	clusterAndNamespace: any;
}): JSX.Element {
	console.log(clusterAndNamespace);
	const params: AccessParams = useParams();
	const form = Form.useFormInstance();
	const [open, setOpen] = useState<boolean>(false);
	const [pods, setPods] = useState<AgentInstanceItem[]>([]);
	const [curAgent, setCurAgent] = useState<AgentItem>();
	useEffect(() => {
		if (podsInfo.current) {
			const nameList = agentInfo.current.agentList.map(
				(item: any) => item.name
			);
			if (podsInfo.current.pods) {
				const list = podsInfo.current.pods.filter(
					(item: AgentInstanceItem) =>
						nameList.includes(item.nodeName)
				);
				form.setFieldsValue({
					pods: list
				});
				setPods(list);
			} else {
				setPods([]);
			}
		} else {
			setPods([]);
		}
	}, [podsInfo]);
	const onCreate = (values: any, agent: any) => {
		const podsTemp = pods
			.filter(
				(item: AgentInstanceItem) => item.nodeName === curAgent?.name
			)
			.map((item: AgentInstanceItem) => item.port);
		const namesTemp = pods.map((item: AgentInstanceItem) => item.name);
		if (values.name && namesTemp.includes(values.name)) {
			notification.warning({
				message: '提示',
				description: '该实例名称已重复'
			});
			return true;
		}
		if (
			!values.name &&
			`${basicInfo.current.name}-${pods.length}` &&
			namesTemp?.includes(`${basicInfo.current.name}-${pods.length}`)
		) {
			notification.warning({
				message: '提示',
				description: '该默认实例名称已重复'
			});
			return true;
		}
		if (podsTemp.includes(values.port)) {
			notification.warning({
				message: '提示',
				description: '该端口已被接入'
			});
			return true;
		}
		const sendData = {
			agentName: curAgent?.name,
			clusterId: clusterAndNamespace?.[0],
			namespace: clusterAndNamespace?.[1],
			middleware: params.name,
			middlewareName: basicInfo.current.name,
			name: values.name,
			operate: {
				user: basicInfo.current?.databaseMessage?.user,
				password: basicInfo.current?.databaseMessage?.password
			},
			port: values.port,
			protocol: values?.protocol || ''
		};
		return checkStatus(sendData).then((res) => {
			if (res.success) {
				notification.success({
					message: '成功',
					description: '实例添加成功'
				});
				const temp = {
					name:
						values.name ||
						`${basicInfo.current.name}-${pods.length}`,
					port: values.port,
					nodeName: curAgent?.name || '',
					role: res.data.role,
					status: res.data.status,
					address: agent.address,
					protocol: values?.protocol || '',
					id: Math.random()
				};
				setPods([...pods, temp]);
				form.setFieldsValue({
					pods: [...pods, temp]
				});
				setOpen(false);
				setCurAgent(undefined);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	const roleRender = (value: string) => {
		switch (value) {
			case 'master':
				return '主节点';
			case 'slave':
				return '从节点';
			case 'data':
				return '数据节点';
			case 'client':
				return '协调节点';
			case 'cold':
				return '冷节点';
			case 'kibana':
				return 'Kibana';
			case 'nameserver':
				return 'Nameserver';
			case 'exporter':
				return 'Exporter';
			case 'sentinel':
				return '哨兵';
			case 'proxy':
				return '代理';
			case 'syncslave':
				return '同步节点';
			case 'sync_slave':
				return '同步节点';
			case 'default':
				return '/';
			default:
				return value
					? value.substring(0, 1).toUpperCase() + value.substring(1)
					: '/';
		}
	};
	const statusRender = (value: string) => {
		switch (value) {
			case 'Running':
				return <Badge status="success" text="连接正常" />;
			case 'Error':
				return <Badge status="error" text="连接异常" />;
			case 'Deleted':
				return <Badge status="processing" text="删除中" />;
			default:
				return <Badge status="error" text="连接异常" />;
		}
	};
	const onDelete = (record: AgentInstanceItem) => {
		confirm({
			title: '操作确认',
			content: '移除该实例后需要重新填写内容进行添加，请谨慎操作！',
			onOk: () => {
				const list = pods.filter((item: any) => item.id !== record.id);
				setPods(list);
				form.setFieldsValue({
					pods: list
				});
			}
		});
	};
	return (
		<Form.Item name="pods" noStyle>
			<ProList>
				{agentInfo?.current?.agentList.map((item: AgentItem) => {
					return (
						<React.Fragment key={item.name}>
							<ListPanel
								title={`${item.name}`}
								subTitle="服务器名称"
								icon={
									<IconFont
										type="icon-zhuji"
										style={{
											width: '40px',
											height: '40px',
											fontSize: '24px',
											lineHeight: '43px',
											marginRight: '8px',
											color: 'white'
										}}
									/>
								}
								headStyle={{ width: '500px' }}
								actionRender={
									<Actions>
										<LinkButton
											onClick={() => {
												setCurAgent(item);
												setOpen(true);
											}}
										>
											添加
										</LinkButton>
									</Actions>
								}
								render={
									<div className="service-pod-card-content">
										{pods
											?.filter(
												(i: any) =>
													i.nodeName === item.name
											)
											.map((i: any) => {
												return (
													<div
														className="service-pod-card"
														key={i.name}
													>
														<div className="service-pod-card-item">
															<div className="service-pod-card-item-title">
																<div className="service-pod-card-icon">
																	<IconFont
																		type="icon-xunijijiedian"
																		style={{
																			width: '40px',
																			height: '40px',
																			lineHeight:
																				'40px',
																			fontSize:
																				'30px',
																			paddingTop:
																				'4px'
																		}}
																	/>
																	<span>
																		<p>
																			{
																				i.name
																			}
																		</p>
																		<p>
																			{statusRender(
																				i.status
																			)}
																		</p>
																	</span>
																</div>
																<Button
																	type="link"
																	onClick={() =>
																		onDelete(
																			i
																		)
																	}
																>
																	移除
																</Button>
															</div>
															<div className="service-pod-card-item-content">
																<p>
																	实例IP:{' '}
																	{
																		item.address
																	}
																</p>
																<p>
																	端口：
																	{i.port}
																</p>
																<p>
																	节点类型：
																	{roleRender(
																		i.role
																	)}
																</p>
																{i.protocol && (
																	<p>
																		协议：
																		{
																			i.protocol
																		}
																	</p>
																)}
															</div>
														</div>
													</div>
												);
											})}
									</div>
								}
							>
								<ListCardItem
									label="IP地址"
									value={item.address}
									width={260}
								/>
							</ListPanel>
						</React.Fragment>
					);
				})}
				{open && (
					<AccessInstance
						agent={curAgent}
						open={open}
						onCancel={() => {
							setCurAgent(undefined);
							setOpen(false);
						}}
						onCreate={onCreate}
					/>
				)}
			</ProList>
		</Form.Item>
	);
}

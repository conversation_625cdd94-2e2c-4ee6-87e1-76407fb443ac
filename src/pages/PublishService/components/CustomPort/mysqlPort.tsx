import React from 'react';
import { Form, InputNumber, Switch } from 'antd';
import { formItemLayout614 } from '@/utils/const';

export default function MysqlPort(): JSX.Element {
	const customPort = Form.useWatch('customPort');
	return (
		<>
			<Form.Item
				name="customPort"
				label="自定义端口"
				initialValue={false}
				valuePropName="checked"
			>
				<Switch />
			</Form.Item>
			{customPort && (
				<Form.Item label=" ">
					<Form.Item
						{...formItemLayout614}
						labelAlign="left"
						colon={false}
						label="端口号"
						name="port"
						rules={[
							{
								min: 1,
								max: 65535,
								type: 'number',
								message: '端口范围为1至65535的正整数,默认为3306'
							}
						]}
					>
						<InputNumber
							style={{
								width: '390px'
							}}
							placeholder="请输入MySQL的服务端口号，默认为3306"
						/>
					</Form.Item>
				</Form.Item>
			)}
		</>
	);
}

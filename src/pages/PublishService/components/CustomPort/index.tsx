import storage from '@/utils/storage';
import React, { useState } from 'react';
import ElasticsearchPort from './elasticsearchPort';
import RedisPort from './redisPort';
import PostgreSQLPort from './postgresqlPort';
import MysqlPort from './mysqlPort';

export default function CustomPort({ type }: { type: string }): JSX.Element {
	// * feature 自定义端口是否打开
	const [customPortAPI] = useState<boolean>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'customPort')?.enabled ?? true
	);
	if (customPortAPI) {
		switch (type) {
			case 'elasticsearch':
				return <ElasticsearchPort />;
			case 'redis':
				return <RedisPort />;
			case 'postgresql':
				return <PostgreSQLPort />;
			case 'mysql':
				return <MysqlPort />;
			default:
				return <></>;
		}
	} else {
		return <></>;
	}
}

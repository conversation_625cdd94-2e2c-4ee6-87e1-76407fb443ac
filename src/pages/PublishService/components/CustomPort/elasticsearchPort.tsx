import React from 'react';
import { Switch, Form, InputNumber } from 'antd';
import { formItemLayout614 } from '@/utils/const';

export default function ElasticsearchPort(): JSX.Element {
	const customPort = Form.useWatch('customPort');
	return (
		<>
			<Form.Item
				name="customPort"
				label="自定义端口"
				initialValue={false}
				valuePropName="checked"
			>
				<Switch />
			</Form.Item>
			{customPort && (
				<Form.Item label=" " name="esCustomPort">
					<Form.Item
						{...formItemLayout614}
						labelAlign="left"
						colon={false}
						name="httpPort"
						label="HTTP端口"
						rules={[
							{
								type: 'number',
								min: 1,
								max: 65535,
								message: '端口范围为1至65535的正整数'
							}
						]}
					>
						<InputNumber
							style={{ width: '390px' }}
							placeholder="请输入HTTP端口号，默认为9200"
							step={1}
						/>
					</Form.Item>
					<Form.Item
						{...formItemLayout614}
						labelAlign="left"
						colon={false}
						name="kibanaPort"
						label="Kibana端口"
						rules={[
							{
								type: 'number',
								min: 1,
								max: 65535,
								message: '端口范围为1至65535的正整数'
							}
						]}
					>
						<InputNumber
							style={{ width: '390px' }}
							placeholder="请输入Kibana端口号，默认为5200"
							step={1}
						/>
					</Form.Item>
					<Form.Item
						{...formItemLayout614}
						labelAlign="left"
						colon={false}
						name="tcpPort"
						label="TCP端口"
						rules={[
							{
								type: 'number',
								min: 1,
								max: 65535,
								message: '端口范围为1至65535的正整数'
							}
						]}
					>
						<InputNumber
							style={{ width: '390px' }}
							placeholder="请输入TCP端口号，默认为9300"
							step={1}
						/>
					</Form.Item>
					<Form.Item
						{...formItemLayout614}
						labelAlign="left"
						colon={false}
						name="exporterPort"
						label="Exporter端口"
						rules={[
							{
								type: 'number',
								min: 1,
								max: 65535,
								message: '端口范围为1至65535的正整数'
							}
						]}
					>
						<InputNumber
							style={{ width: '390px' }}
							placeholder="请输入Exporter端口号，默认为19114"
							step={1}
						/>
					</Form.Item>
				</Form.Item>
			)}
		</>
	);
}

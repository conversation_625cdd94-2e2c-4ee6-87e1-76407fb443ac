import React from 'react';
import { Form, InputNumber, Switch } from 'antd';
import { formItemLayout614 } from '@/utils/const';

export default function RedisPort(): JSX.Element {
	const customPort = Form.useWatch('customPort');
	return (
		<>
			<Form.Item
				name="customPort"
				label="自定义端口"
				initialValue={false}
				valuePropName="checked"
			>
				<Switch />
			</Form.Item>
			{customPort && (
				<Form.Item name="redisCustomPort" label=" ">
					<Form.Item
						{...formItemLayout614}
						labelAlign="left"
						colon={false}
						name="redisPort"
						label="Redis端口"
						rules={[
							{
								min: 1,
								max: 65535,
								type: 'number',
								message: '端口范围为1至65535的正整数'
							}
						]}
						style={{ marginBottom: 8 }}
					>
						<InputNumber
							style={{
								width: '380px'
							}}
							step={1}
							placeholder="请输入Redis端口号，默认为6379"
						/>
					</Form.Item>
					<Form.Item
						{...formItemLayout614}
						labelAlign="left"
						colon={false}
						label="Exporter端口"
						name="exporterPort"
						rules={[
							{
								min: 1,
								max: 65535,
								type: 'number',
								message: '端口范围为1至65535的正整数'
							}
						]}
						style={{ marginBottom: 0 }}
					>
						<InputNumber
							style={{
								width: '380px'
							}}
							step={1}
							placeholder="请输入Exporter端口号，默认为9121"
						/>
					</Form.Item>
				</Form.Item>
			)}
		</>
	);
}

import React from 'react';
import { Form, InputNumber, Switch } from 'antd';
import { formItemLayout614 } from '@/utils/const';

export default function PostgreSQLPort(): JSX.Element {
	const customPort = Form.useWatch('customPort');
	return (
		<>
			<Form.Item
				name="customPort"
				label="自定义端口"
				initialValue={false}
				valuePropName="checked"
			>
				<Switch />
			</Form.Item>
			{customPort && (
				<Form.Item label=" " name="pgsqlCustomPort">
					<Form.Item
						{...formItemLayout614}
						labelAlign="left"
						colon={false}
						name="pgPort"
						label="PostgreSQL端口"
						rules={[
							{
								min: 1,
								max: 65535,
								type: 'number',
								message: '端口范围为1至65535的正整数'
							}
						]}
					>
						<InputNumber
							style={{
								width: '390px'
							}}
							step={1}
							placeholder="请输入PostgreSQL端口号，默认为5432"
						/>
					</Form.Item>
					<Form.Item
						{...formItemLayout614}
						labelAlign="left"
						colon={false}
						label="运维端口"
						name="apiPort"
						rules={[
							{
								min: 1,
								max: 65535,
								type: 'number',
								message: '端口范围为1至65535的正整数'
							}
						]}
					>
						<InputNumber
							style={{
								width: '390px'
							}}
							step={1}
							placeholder="请输入运维端口号，默认为8008"
						/>
					</Form.Item>
					<Form.Item
						{...formItemLayout614}
						labelAlign="left"
						colon={false}
						label="Exporter端口"
						name="exporterPort"
						rules={[
							{
								min: 1,
								max: 65535,
								type: 'number',
								message: '端口范围为1至65535的正整数'
							}
						]}
					>
						<InputNumber
							style={{
								width: '380px'
							}}
							step={1}
							placeholder="请输入Exporter端口号，默认为9187"
						/>
					</Form.Item>
					<Form.Item
						{...formItemLayout614}
						labelAlign="left"
						colon={false}
						name="bgMonPort"
						label="进程监控端口"
						rules={[
							{
								min: 1,
								max: 65535,
								type: 'number',
								message: '端口范围为1至65535的正整数'
							}
						]}
					>
						<InputNumber
							style={{
								width: '380px'
							}}
							step={1}
							placeholder="请输入进程监控端口号，默认为8080"
						/>
					</Form.Item>
				</Form.Item>
			)}
		</>
	);
}

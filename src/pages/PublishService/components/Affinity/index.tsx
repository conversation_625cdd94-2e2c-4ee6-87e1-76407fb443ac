import React, { useEffect } from 'react';
import { Form } from 'antd';
import { formItemLayout618 } from '@/utils/const';
import AffinityContent from './affinity';
import './index.less';

export default function Affinity({
	name,
	label,
	clusterId,
	isAnti,
	disabled
}: {
	name: string;
	label: string;
	clusterId?: string;
	isAnti: boolean;
	disabled: boolean;
}): JSX.Element {
	const formSwitch = Form.useWatch(`${name}Switch`);
	const checkAffinity = (_: any, value: any) => {
		if (formSwitch) {
			if (!value) {
				return Promise.reject(new Error(`请添加${label}`));
			} else {
				return Promise.resolve();
			}
		} else {
			return Promise.resolve();
		}
	};
	return (
		<Form.Item
			name={name}
			label={label}
			labelAlign="left"
			colon={false}
			{...formItemLayout618}
			tooltip={`勾选${isAnti ? '强制反亲和' : '强制亲和'}时，服务${
				isAnti ? '不' : '只'
			}会部署在具备相应标签的主机上，若主机资源不足，可能会导致启动失败`}
			rules={[{ validator: checkAffinity }]}
			className="form-affinity"
		>
			<AffinityContent
				name={name}
				isAnti={isAnti}
				disabled={disabled}
				clusterId={clusterId}
			/>
		</Form.Item>
	);
}

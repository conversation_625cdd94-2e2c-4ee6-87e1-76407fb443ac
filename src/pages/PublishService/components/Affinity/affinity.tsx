import React, { useEffect, useState } from 'react';
import { <PERSON>Complete, Button, Checkbox, Form, Switch, Tag } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { AutoCompleteOptionItem } from '@/types/comment';
import pattern from '@/utils/pattern';
import { getNodePort } from '@/services/middleware';
import '../../index.less';

export default function AffinityContent({
	isAnti,
	disabled,
	value,
	onChange,
	clusterId,
	name
}: {
	name: string;
	isAnti: boolean;
	disabled: boolean;
	clusterId?: string;
	value?: AffinityItem[];
	onChange?: (value: AffinityItem[]) => void;
}): JSX.Element {
	const form = Form.useFormInstance();
	const affinitySwitch = Form.useWatch(`${name}Switch`);
	const [label, setLabel] = useState<string>('');
	const [labelList, setLabelList] = useState<AutoCompleteOptionItem[]>([]);
	const [checkedLabels, setCheckedLabels] = useState<AffinityItem[]>(
		value || []
	);
	const [checked, setChecked] = useState<boolean>(false);
	useEffect(() => {
		if (clusterId) {
			getNodePort({ clusterId }).then((res) => {
				if (res.success) {
					const list = res.data.map((item: string) => {
						return {
							value: item,
							label: item
						};
					});
					setLabelList(list);
				}
			});
		}
	}, [clusterId]);
	useEffect(() => {
		if (affinitySwitch === false) {
			setCheckedLabels([]);
			form.setFieldValue(`${name}`, undefined);
			form.validateFields([`${name}`]);
		}
	}, [affinitySwitch]);
	return (
		<>
			<Form.Item
				noStyle
				name={`${name}Switch`}
				initialValue={false}
				valuePropName="checked"
			>
				<Switch />
			</Form.Item>
			{affinitySwitch && (
				<>
					<AutoComplete
						allowClear
						placeholder="请输入key=value格式的内容"
						value={label}
						style={{ width: 260, marginLeft: 8 }}
						options={labelList}
						onChange={(value) => setLabel(value)}
						onBlur={() => {
							if (
								label &&
								new RegExp(pattern.label).test(label) &&
								!checkedLabels.find(
									(item: any) => item.label === label
								)
							) {
								const lt = [
									...checkedLabels,
									{
										label: label,
										required: checked,
										anti: isAnti ? true : false
									}
								];
								setCheckedLabels(lt);
								onChange && onChange(lt);
							}
						}}
						status={
							label && !new RegExp(pattern.label).test(label)
								? 'error'
								: ''
						}
						disabled={disabled}
					/>
					<Button
						style={{
							marginLeft: '4px',
							marginRight: '4px',
							padding: '0 9px'
						}}
						disabled={
							disabled ||
							!label ||
							!new RegExp(pattern.label).test(label)
								? true
								: false
						}
						onClick={() => {
							if (
								!checkedLabels.find(
									(item: any) => item.label === label
								)
							) {
								const lt = [
									...checkedLabels,
									{
										label: label,
										required: checked,
										anti: isAnti ? true : false
									}
								];
								setCheckedLabels(lt);
								onChange && onChange(lt);
							}
						}}
					>
						<PlusOutlined
							style={{
								color: '#005AA5'
							}}
						/>
					</Button>
					<Checkbox
						checked={value?.[0]?.required || checked}
						onChange={(e) => {
							setChecked(e.target.checked);
							const lt = checkedLabels.map((item: any) => {
								return {
									label: item.label,
									anti: isAnti ? true : false,
									required: e.target.checked
								};
							});
							setCheckedLabels(lt);
							onChange && onChange(lt);
						}}
						disabled={disabled}
					>
						{isAnti ? '强制反亲和' : '强制亲和'}
					</Checkbox>
				</>
			)}
			{label && !new RegExp(pattern.label).test(label) ? (
				<div style={{ marginLeft: 52, color: '#ff4d4f' }}>
					请输入key=value格式的内容
				</div>
			) : null}
			{affinitySwitch && checkedLabels.length ? (
				<div className={'affinity-tags-content'}>
					{checkedLabels.map((item: any) => {
						return (
							<Tag
								key={item.label}
								closable={!disabled}
								style={{ padding: '4px 10px' }}
								onClose={(e: React.MouseEvent<HTMLElement>) => {
									e.preventDefault();
									const lt = checkedLabels.filter(
										(arr) => arr.label !== item.label
									);
									setCheckedLabels(lt);
									onChange && onChange(lt);
								}}
							>
								{item.label}
							</Tag>
						);
					})}
				</div>
			) : null}
		</>
	);
}

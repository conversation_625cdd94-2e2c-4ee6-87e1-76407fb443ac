import { Form, InputNumber } from 'antd';
import React from 'react';

export default function TemplateDeployNum(): JSX.Element {
	return (
		<Form.Item
			name="deployNum"
			label="发布数量"
			initialValue={1}
			rules={[{ required: true, message: '发布数量不能为空' }]}
		>
			<InputNumber
				placeholder="请输入发布数量"
				min={1}
				step={1}
				precision={0}
				style={{
					width: 350
				}}
			/>
		</Form.Item>
	);
}

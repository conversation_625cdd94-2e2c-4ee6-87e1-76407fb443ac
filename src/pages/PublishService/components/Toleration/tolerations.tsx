import React, { useEffect, useState } from 'react';
import { <PERSON>C<PERSON>plete, Button, Form, Switch, Tag, notification } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { getNodeTaint } from '@/services/middleware';
import { AutoCompleteOptionItem } from '@/types/comment';
import '../../index.less';

export default function TolerationContent({
	value,
	onChange,
	clusterId,
	name,
	formLabel
}: {
	value?: string[];
	onChange?: (value: any) => void;
	clusterId?: string;
	name: string;
	formLabel: string;
}): JSX.Element {
	const form = Form.useFormInstance();
	const tolerationSwitch = Form.useWatch(`${name}Switch`);
	const [label, setLabel] = useState<string>('');
	const [labelList, setLabelList] = useState<AutoCompleteOptionItem[]>([]);
	const [checkedLabels, setCheckedLabels] = useState<string[]>(value || []);
	useEffect(() => {
		if (clusterId) {
			getNodeTaint({ clusterid: clusterId }).then((res) => {
				if (res.success) {
					const list = res.data.map((item: string) => {
						return {
							value: item,
							label: item
						};
					});
					setLabelList(list);
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		}
	}, [clusterId]);
	useEffect(() => {
		if (tolerationSwitch === false) {
			form.setFieldValue(`${name}`, undefined);
			setCheckedLabels([]);
			form.validateFields([`${name}`]);
		}
	}, [tolerationSwitch]);
	return (
		<>
			<Form.Item
				noStyle
				name={`${name}Switch`}
				valuePropName="checked"
				initialValue={false}
			>
				<Switch style={{ marginRight: 8 }} />
			</Form.Item>
			{tolerationSwitch && (
				<>
					<AutoComplete
						value={label}
						placeholder={`请选择或输入${formLabel}`}
						onChange={(value) => setLabel(value)}
						onBlur={() => {
							if (
								label &&
								!checkedLabels.find(
									(item: any) => item === label
								)
							) {
								const listTemp = checkedLabels.concat([label]);
								setCheckedLabels(listTemp);
								onChange && onChange(listTemp);
							}
						}}
						allowClear={true}
						options={labelList}
						style={{ width: 260 }}
					/>
					<Button
						style={{
							marginLeft: '4px',
							padding: '0 9px'
						}}
						disabled={label ? false : true}
						onClick={() => {
							if (
								!checkedLabels.find(
									(item: any) => item === label
								)
							) {
								const listTemp = checkedLabels.concat([label]);
								setCheckedLabels(listTemp);
								onChange && onChange(listTemp);
							}
						}}
					>
						<PlusOutlined
							style={{
								color: '#005AA5'
							}}
						/>
					</Button>
				</>
			)}
			{tolerationSwitch && checkedLabels.length ? (
				<div className="toleration-tags-content">
					{checkedLabels.map((item: string) => {
						return (
							<Tag
								key={item}
								closable
								style={{
									padding: '4px 10px'
								}}
								onClose={() => {
									const listTemp = checkedLabels.filter(
										(i: string) => i !== item
									);
									setCheckedLabels(listTemp);
									onChange && onChange(listTemp);
								}}
							>
								{item}
							</Tag>
						);
					})}
				</div>
			) : null}
		</>
	);
}

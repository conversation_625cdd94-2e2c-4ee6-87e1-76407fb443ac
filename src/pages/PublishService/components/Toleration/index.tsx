import { Form } from 'antd';
import React from 'react';
import TolerationContent from './tolerations';
import { formItemLayout614 } from '@/utils/const';
import './index.less';

export default function Toleration({
	clusterId,
	name,
	label
}: {
	name: string;
	label: string;
	clusterId: string;
}): JSX.Element {
	const formSwitch = Form.useWatch(`${name}Switch`);
	const checkToleration = (_: any, value: any) => {
		if (formSwitch) {
			if (!value) {
				return Promise.reject(new Error(`请添加${label}`));
			} else {
				return Promise.resolve();
			}
		} else {
			return Promise.resolve();
		}
	};
	return (
		<Form.Item
			label={label}
			name={name}
			labelAlign="left"
			colon={false}
			{...formItemLayout614}
			className="form-tolerations"
			rules={[{ validator: checkToleration }]}
		>
			<TolerationContent
				formLabel={label}
				name={name}
				clusterId={clusterId}
			/>
		</Form.Item>
	);
}

import React, { useEffect } from 'react';
import { Form, Select } from 'antd';
import Decimal from 'decimal.js';
import { NamespaceItem } from '@/pages/ProjectDetail/projectDetail';

interface NamespaceProps {
	namespace: string; // * 全局的namespace
	currentNamespace?: NamespaceItem; // * 当前的namespace
	namespaceList: NamespaceItem[];
	setCurrentNamespace: (value?: NamespaceItem) => void;
}
export default function Namespace(props: NamespaceProps): JSX.Element {
	const { namespace, currentNamespace, namespaceList, setCurrentNamespace } =
		props;
	const form = Form.useFormInstance();

	useEffect(() => {
		if (form.getFieldValue('namespace')) {
			const cn = namespaceList.find(
				(item: NamespaceItem) =>
					item.name === form.getFieldValue('namespace')
			);
			setCurrentNamespace(cn);
		}
	}, [form.getFieldValue('namespace')]);
	const handleNamespaceChange = (value: string) => {
		setCurrentNamespace(
			namespaceList.find((item: NamespaceItem) => item.name === value)
		);
		form.setFieldValue('namespace', value);
	};
	return (
		<>
			<Form.Item
				label="命名空间"
				rules={[
					{
						required: true,
						message: '请选择命名空间'
					}
				]}
				name="namespace"
				help={
					currentNamespace &&
					`当前命名空间剩余可分配配额为：CPU${
						currentNamespace?.quotas !== null
							? new Decimal(
									currentNamespace?.quotas?.cpu?.request
							  )
									.minus(
										new Decimal(
											currentNamespace?.quotas?.cpu?.used
										)
									)
									.toFixed(1)
							: '-'
					}Core，内存${
						currentNamespace?.quotas !== null
							? new Decimal(
									currentNamespace?.quotas?.memory?.request
							  )
									.minus(
										new Decimal(
											currentNamespace?.quotas?.memory?.used
										)
									)
									.toFixed(1)
							: '-'
					}GB`
				}
			>
				<Select
					placeholder="请选择命名空间"
					style={{ width: '390px' }}
					disabled={namespace !== '*'}
					value={currentNamespace?.name}
					onChange={handleNamespaceChange}
					dropdownMatchSelectWidth={false}
				>
					{namespaceList.map((item) => {
						return (
							<Select.Option key={item.name} value={item.name}>
								{item.aliasName || item.name}
							</Select.Option>
						);
					})}
				</Select>
			</Form.Item>
		</>
	);
}

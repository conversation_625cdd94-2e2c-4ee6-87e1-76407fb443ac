import React from 'react';
import { Input, Modal, Form } from 'antd';
import { useParams } from 'react-router';
import { formItemLayout618 } from '@/utils/const';
import { checkTemplateNameExist } from '@/services/middleware';
import storage from '@/utils/storage';

const FormItem = Form.Item;
export default function TemplateNameModal({
	open,
	onCancel,
	onCreate
}: {
	open: boolean;
	onCancel: () => void;
	onCreate: (values: any) => void;
}): JSX.Element {
	const [form] = Form.useForm();
	const params: ParamsProps = useParams();
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const onOk = async () => {
		await form.validateFields();
		const values = form.getFieldsValue();
		onCreate(values);
	};
	return (
		<Modal
			title="保存为模板"
			onCancel={onCancel}
			onOk={onOk}
			open={open}
			width={400}
			destroyOnClose={true}
		>
			<Form
				form={form}
				preserve={false}
				{...formItemLayout618}
				requiredMark={false}
				labelAlign="left"
				colon={false}
			>
				<FormItem
					label="模板名称"
					name="templateName"
					validateTrigger={['onBlur', 'onSubmit']}
					rules={[
						{ required: true, message: '模板名称不能为空' },
						{
							type: 'string',
							min: 1,
							max: 64,
							message: '请输入长度为1-64个字符的模板名称'
						},
						{
							validateTrigger: ['onSubmit', 'onBlur'],
							validator: async (_, value) => {
								if (!value) {
									return Promise.resolve();
								}
								const res = await checkTemplateNameExist({
									name: value,
									organId: organId,
									projectId: projectId,
									type: params.name
								});
								if (res.success) {
									if (res.data) {
										return Promise.reject(
											new Error('当前模板名称已存在！')
										);
									} else {
										return Promise.resolve();
									}
								} else {
									return Promise.reject(
										new Error('中间件模板名称校验失败！')
									);
								}
							}
						}
					]}
				>
					<Input placeholder="请输入模板名称" />
				</FormItem>
			</Form>
		</Modal>
	);
}

import React, { useEffect, useState } from 'react';
import { Form, Table } from 'antd';
import storage from '@/utils/storage';
import { getMiddlewaresTemplate } from '@/services/middleware';
import { ColumnFilterItem } from 'antd/lib/table/interface';
import { useParams } from 'react-router';
import { modeAliasList } from '@/utils/const';
import { objectRemoveDuplicatesByKey } from '@/utils/utils';

export default function TemplateSelect(): JSX.Element {
	const form = Form.useFormInstance();
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const params: ParamsProps = useParams();
	const [templates, setTemplates] = useState<ServiceTemplateItem[]>([]);
	const [total, setTotal] = useState<number>();
	const [current, setCurrent] = useState<number>(1);
	const [pageSize, setPageSize] = useState<number>(10);
	const [versionFilter, setVersionFilter] = useState<ColumnFilterItem[]>();
	const [modeFilter, setModeFilter] = useState<ColumnFilterItem[]>();
	useEffect(() => {
		getData(current, pageSize);
	}, []);
	const rowSelection = {
		onChange: (
			selectedRowKeys: React.Key[],
			selectedRows: ServiceTemplateItem[]
		) => {
			const select_template = selectedRows[0];
			form.setFieldValue('template', select_template);
		}
	};
	const getData = (current_value: number, page_size: number) => {
		const sendData = {
			projectId,
			organId,
			type: params.name,
			current: current_value,
			size: page_size,
			keyword: ''
		};
		getMiddlewaresTemplate(sendData).then((res) => {
			if (res.success) {
				const templates_temp = res.data.records.map(
					(item: ServiceTemplateItem) => {
						if (params.name === 'redis') {
							if (item.mode === 'cluster') {
								if (Object.keys(item.quota).includes('proxy')) {
									item.mode = 'agent';
								} else {
									item.mode = 'cluster';
								}
							} else {
								if (Object.keys(item.quota).includes('proxy')) {
									item.mode = 'readWriteProxy';
								} else {
									item.mode = 'sentinel';
								}
							}
						}
						return item;
					}
				);
				const mode_filters = templates_temp.map(
					(ele: ServiceTemplateItem) => {
						return {
							value: ele.mode,
							text: modeAliasList[ele.mode]
						};
					}
				);
				const version_filters = templates_temp.map(
					(ele: ServiceTemplateItem) => {
						return {
							value: ele.version,
							text: ele.version
						};
					}
				);
				setModeFilter(mode_filters);
				setVersionFilter(
					objectRemoveDuplicatesByKey(version_filters, 'value')
				);
				setTemplates(templates_temp);
				setCurrent(res.data.current);
				setPageSize(res.data.pageSize);
				setTotal(res.data.total);
			}
		});
	};
	const onChange = (page: number, pageSize: number) => {
		setCurrent(page);
		setPageSize(pageSize);
		getData(page, pageSize);
	};
	const resourceRender = (value: any, record: ServiceTemplateItem) => {
		let total_cpu = 0;
		let total_memory = 0;
		let total_storage = 0;
		Object.keys(record.quota).map((item) => {
			total_cpu +=
				Number(record.quota[item].cpu) * Number(record.quota[item].num);
			total_memory +=
				Number(record.quota[item].memory) *
				Number(record.quota[item].num);
			total_storage +=
				Number(record.quota[item].storageClassQuota) *
				Number(record.quota[item].num);
		});
		return `CPU：${total_cpu}C 内存：${total_memory}GB 存储 ${total_storage}GB`;
	};
	const columns = [
		{ dataIndex: 'name', key: 'name', title: '模板名称' },
		{
			dataIndex: 'version',
			key: 'version',
			title: '中间件版本',
			filters: versionFilter,
			onFilter: (value: any, record: ServiceTemplateItem) =>
				record.version === value
		},
		{
			dataIndex: 'mode',
			key: 'mode',
			title: '模式',
			render: (value: any) => modeAliasList[value],
			filters: modeFilter,
			onFilter: (value: any, record: ServiceTemplateItem) =>
				record.mode === value
		},
		{ dataIndex: 'updateTime', key: 'updateTime', title: '最近修改时间' },
		{
			dataIndex: 'resources',
			key: 'resources',
			title: '资源占用情况',
			render: resourceRender
		}
	];
	return (
		<>
			<Table
				rowKey="id"
				rowSelection={{
					type: 'radio',
					...rowSelection
				}}
				columns={columns}
				dataSource={templates}
				pagination={{
					total: total,
					pageSize: pageSize,
					current: current,
					onChange: onChange
				}}
			/>
			<Form.Item noStyle name="template" />
		</>
	);
}

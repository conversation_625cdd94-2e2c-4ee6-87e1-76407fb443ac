import { Form } from 'antd';
import React from 'react';
import InstanceQuotaContent from './instanceQuota';
import InputContent from './inputContent';
import {
	esDataList,
	kafkaDataList,
	minmax,
	mqDataList,
	mysqlDataList,
	redisDataList,
	redisNodeList,
	redisSentinelDataList,
	zkpDataList
} from '@/utils/const';

export default function InstanceQuota({
	type,
	modeType,
	quota,
	isTemplate
}: {
	type: string;
	modeType?: string;
	quota?: any;
	isTemplate?: boolean;
}): JSX.Element {
	const dataListRender = () => {
		const redisListRender = () => {
			if (modeType && modeType === 'redis') {
				return redisNodeList;
			} else if (modeType && modeType === 'sentinel') {
				return redisSentinelDataList;
			} else if (modeType && modeType === 'proxy') {
				const temp = {
					id: '0',
					cpu: `1 Core`,
					memory: `${minmax(
						quota?.['redis'].memory / 4,
						0.256,
						2
					)} Gi`
				};
				return [temp, ...redisNodeList];
			} else {
				return redisDataList;
			}
		};
		const mysqlListRender = () => {
			if (modeType && modeType === 'proxy') {
				const temp = {
					id: '0',
					cpu: `${quota?.['mysql'].cpu / 4} Core`,
					memory: `${minmax(
						quota?.['mysql'].memory / 4,
						0.256,
						0
					)} Gi`
				};
				const listTemp = [temp, ...mysqlDataList].filter(
					(item) => item.id !== '4' && item.id !== '6'
				);
				return listTemp;
			} else {
				return mysqlDataList;
			}
		};
		switch (type) {
			case 'kafka':
				return kafkaDataList;
			case 'rocketmq':
				return mqDataList;
			case 'elasticsearch':
				return esDataList;
			case 'redis':
				return redisListRender();
			case 'mysql':
				return mysqlListRender();
			case 'postgresql':
				return mysqlDataList;
			case 'zookeeper':
				return zkpDataList;
			default:
				return [];
		}
	};
	return (
		<>
			<Form.Item
				label="节点规格"
				name="instanceSpec"
				initialValue="General"
				style={{ marginBottom: 0 }}
			>
				<InstanceQuotaContent
					tableList={dataListRender()}
					isTemplate={isTemplate}
				/>
			</Form.Item>
			<Form.Item name="specId" label=" " initialValue={'1'}>
				<InputContent
					tableList={dataListRender()}
					isTemplate={isTemplate}
				/>
			</Form.Item>
		</>
	);
}

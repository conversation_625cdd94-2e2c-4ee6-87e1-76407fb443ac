import React, { useState } from 'react';
import { Form } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { rocketMQAccount } from '@/components/RocketACLForm/acl';
import UserConfig from '@/components/RocketACLForm/userConfig';
import '../../index.less';

export default function RocketMQAccountListForm(): JSX.Element {
	const form = Form.useFormInstance();
	const [userConfigs, setUserConfigs] = useState<rocketMQAccount[]>([
		{
			id: Number(Math.random() * 100),
			accessKey: '',
			admin: false,
			whiteRemoteAddress: '',
			secretKey: '',
			groupPerms: {
				defaultGroupPerm: 'DENY'
			},
			topicPerms: {
				defaultTopicPerm: 'DENY'
			}
		}
	]);

	const deleteUserConfig = (id: number) => {
		const userConfigTemps = userConfigs.filter((item) => item.id !== id);
		setUserConfigs(userConfigTemps);
	};
	const addUserConfig = () => {
		const userConfigTemps = [
			...userConfigs,
			{
				id: Number(Math.random() * 100),
				accessKey: '',
				admin: false,
				whiteRemoteAddress: '',
				secretKey: '',
				groupPerms: {
					defaultGroupPerm: 'DENY'
				},
				topicPerms: {
					defaultTopicPerm: 'DENY'
				}
			}
		];
		setUserConfigs(userConfigTemps);
	};
	const handleSetUserConfig = (
		values: rocketMQAccount,
		id: number | string
	) => {
		const userConfigsTemp = userConfigs.map((item) => {
			if (item.id === id) {
				item = values;
			}
			return item;
		});
		setUserConfigs(userConfigsTemp);
		const list = userConfigsTemp.map((item) => {
			return {
				accessKey: item.accessKey,
				admin: item.admin,
				whiteRemoteAddress: item.whiteRemoteAddress,
				secretKey: item.secretKey,
				groupPerms: item.groupPerms,
				topicPerms: item.topicPerms
			};
		});
		console.log(list);
		form.setFieldsValue({
			rocketMQAccountList: list
		});
	};
	return (
		<div className="acl-account-list-content">
			{userConfigs.map((item) => {
				return (
					<UserConfig
						key={item.id}
						userConfig={item}
						deleteUserConfigProps={deleteUserConfig}
						setUserConfig={(value) =>
							handleSetUserConfig(value, item.id)
						}
					/>
				);
			})}
			<div className="acl-add-user-config" onClick={addUserConfig}>
				<PlusOutlined />
				<span>添加账户信息配置</span>
			</div>
		</div>
	);
}

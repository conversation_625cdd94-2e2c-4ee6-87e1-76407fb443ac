import React from 'react';
import { Form, Input, Switch } from 'antd';
import RocketMQAccountListForm from './rocketMQAccountListForm';
import { formItemLayout618 } from '@/utils/const';

export default function ACLForm(): JSX.Element {
	const formAclEnable = Form.useWatch('enable');
	return (
		<>
			<Form.Item
				name="enable"
				label="访问权限控制认证"
				valuePropName="checked"
				initialValue={false}
			>
				<Switch />
			</Form.Item>
			{formAclEnable && (
				<>
					<Form.Item
						name="globalWhiteRemoteAddresses"
						label="全局IP白名单"
						tooltip="可为空，表示不设置白名单，该条规则默认返回false。支持使用“*”，表示全部匹配，该条规则直接返回true，将会阻断其他规则的判断，请慎重使用。 支持使用“;”，表式分隔多个IP，如，*************;*************• 支持使用 “” 或“-”表示范围，如192.168.*.1或者192.168.100-200.10-202种表达都可"
						rules={[
							{
								type: 'string',
								max: 200,
								message: '可以输入多个IP，输入字符不能超过200个'
							}
						]}
					>
						<Input
							style={{ width: '375px' }}
							placeholder="请输入全局IP白名单，支持输入多个IP"
							maxLength={200}
						/>
					</Form.Item>
					<Form.Item
						{...formItemLayout618}
						name="rocketMQAccountList"
						label="账户信息配置"
					>
						<RocketMQAccountListForm />
					</Form.Item>
				</>
			)}
		</>
	);
}

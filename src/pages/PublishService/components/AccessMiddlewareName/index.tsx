import { getMiddlewareNameCheck } from '@/services/middleware';
import { notAllowedName } from '@/utils/const';
import pattern from '@/utils/pattern';
import { Form, Input } from 'antd';
import React from 'react';

export default function AccessMiddlewareName({
	type
}: {
	type: string;
}): JSX.Element {
	const clusterAndNamespace = Form.useWatch('clusterAndNamespace');
	return (
		<Form.Item
			name="name"
			label="服务名称"
			validateTrigger={['onBlur', 'onSubmit', 'onChange']}
			rules={[
				{ required: true, message: '请输入服务名称' },
				{
					pattern: new RegExp(pattern.name),
					message:
						'请输入以小写字母开头，小写字母数字及“-”组成的2-24个字符'
				},
				({ getFieldValue }) => ({
					validateTrigger: ['onBlur', 'onSubmit'],
					async validator(_, value) {
						if (!value) {
							return Promise.resolve();
						}
						if (notAllowedName.includes(value)) {
							return Promise.reject(
								new Error('无法使用保留词进行命名！')
							);
						}
						const reg = new RegExp(pattern.name);
						if (!reg.test(value)) return;
						const res = await getMiddlewareNameCheck({
							clusterId: clusterAndNamespace?.[0],
							namespace: clusterAndNamespace?.[1],
							type,
							middlewareName: value,
							deployMod: 'server'
						});
						if (res.success) {
							if (res.data) {
								return Promise.reject(
									new Error('当前中间件名称已存在！')
								);
							} else {
								return Promise.resolve();
							}
						} else {
							return Promise.reject(
								new Error('中间件服务名称校验失败！')
							);
						}
					}
				})
			]}
		>
			<Input placeholder="请输入以小写字母开头，小写字母数字及“-”组成的2-24个字符" />
		</Form.Item>
	);
}

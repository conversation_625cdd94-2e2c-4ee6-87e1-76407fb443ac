import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router';
import { Form, notification } from 'antd';
import ProList from '@/components/ProList';
import OperatorCard from './OperatorCard';
import { getAbilityDefault } from '@/services/agent';

export default function AccessOperatorSelect(): JSX.Element {
	const form = Form.useFormInstance();
	const params: AccessParams = useParams();
	const [abilities, setAbilities] = useState<OperatorItem[]>([]);
	const [selectedAbilities, setSelectedAbilities] = useState<OperatorItem[]>(
		[]
	);
	useEffect(() => {
		getAbilityDefault({ type: params.name }).then((res) => {
			if (res.success) {
				setAbilities(res.data);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	}, []);
	const onChange = (record: OperatorItem) => {
		if (selectedAbilities.find((item) => item.name === record.name)) {
			const list = selectedAbilities.filter(
				(item) => item.name !== record.name
			);
			setSelectedAbilities(list);
			form.setFieldsValue({
				devopsAbilityList: list
			});
		} else {
			const list = [...selectedAbilities, record];
			setSelectedAbilities(list);
			form.setFieldsValue({
				devopsAbilityList: list
			});
		}
	};
	return (
		<Form.Item name="devopsAbilityList" noStyle>
			<ProList>
				<div className="operator-card-content">
					{abilities.map((item: OperatorItem, index: number) => {
						return (
							<OperatorCard
								value={item}
								key={index}
								onChange={onChange}
								checked={
									!!selectedAbilities.find(
										(i) => i.name === item.name
									)
								}
							/>
						);
					})}
				</div>
			</ProList>
		</Form.Item>
	);
}

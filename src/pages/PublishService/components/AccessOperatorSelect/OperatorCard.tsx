import React from 'react';
import './index.less';
import { Space } from 'antd';
import { IconFont } from '@/components/IconFont';

export default function OperatorCard({
	value,
	checked,
	onChange
}: {
	value: OperatorItem;
	checked: boolean;
	onChange: (value: OperatorItem) => void;
}): JSX.Element {
	return (
		<div
			className={`operator-card-item ${
				checked ? 'operator-card-item-active' : ''
			}`}
			onClick={() => onChange(value)}
		>
			<div className="operator-card-title">
				<Space>
					<IconFont
						type="icon-yunweicaozuoicon"
						style={{
							width: '40px',
							height: '40px',
							fontSize: '24px',
							lineHeight: '43px',
							borderRadius: '20px',
							backgroundColor: '#e6a768',
							color: 'white'
						}}
					/>
					{value.name}
				</Space>
			</div>
			<div className="operator-card-item-description">
				描述：{value.describe}
			</div>
			<IconFont
				className="operator-card-item-checked"
				style={{
					visibility: checked ? 'initial' : 'hidden'
				}}
				type="icon-xuanzhong"
			/>
		</div>
	);
}

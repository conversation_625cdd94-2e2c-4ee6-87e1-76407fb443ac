.operator-card-content {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	.operator-card-item {
		width: 345px;
		height: 100px;
		display: flex;
		padding: @padding 0px 0px @padding;
		flex-direction: column;
		justify-content: space-between;
		border-radius: @border-radius;
		border: 1px rgba(216,222,229,.4) solid;
		.operator-card-item-description {
			width: 320px;
			.mixin (textEllipsis)
		}
		.operator-card-item-checked {
			align-self: flex-end;
		}
	}
}
.operator-card-item-active {
	border-color: @primary-color !important;
	background-color: #f9fbfd;
}

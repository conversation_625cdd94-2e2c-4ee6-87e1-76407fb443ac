import React, { useEffect } from 'react';
import { Form, Input } from 'antd';
// ! 发布灾备时，在研发后期砍掉了跨组织/项目的发布方式
// ! 再从中间件市场页发布时进入切换组织/项目 - v2.0.0 去除
export default function OrganAndProject({
	organId,
	projectId,
	organName,
	projectName
}: {
	organId: string;
	projectId: string;
	organName: string;
	projectName: string;
}): JSX.Element {
	const form = Form.useFormInstance();
	useEffect(() => {
		form.setFieldsValue({
			organNameAndProjectName: `${organName} / ${projectName}`,
			organAndProject: [organId, projectId]
		});
	}, [organName, projectName]);
	return (
		<>
			<Form.Item
				name="organNameAndProjectName"
				label="组织/项目"
				required
			>
				<Input disabled />
			</Form.Item>
			<Form.Item noStyle name="organAndProject"></Form.Item>
		</>
	);
}

import React from 'react';
import { Form, Select } from 'antd';

const charSetList = [
	{
		label: 'utf8mb4',
		value: 'utf8mb4'
	},
	{
		label: 'latin1',
		value: 'latin1'
	}
];
export default function CharSet({
	judgeBackup
}: {
	judgeBackup?: boolean;
}): JSX.Element {
	return (
		<Form.Item name="charSet" label="字符集" initialValue="utf8mb4">
			<Select options={charSetList} disabled={!!judgeBackup} />
		</Form.Item>
	);
}

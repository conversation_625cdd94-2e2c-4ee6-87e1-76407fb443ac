import { Input, InputNumber } from 'antd';
import React, { useState } from 'react';

export default function ZookeeperServiceContent({
	value,
	onChange,
	judgeBackup,
	defaultPath
}: {
	value?: kafkaDTO;
	judgeBackup?: boolean;
	defaultPath?: string;
	onChange?: (value: kafkaDTO) => void;
}): JSX.Element {
	const [zkAddress, setZkAddress] = useState<string>();
	const [zkPort, setZkPort] = useState<number | null>();
	const [path, setPath] = useState<string>(defaultPath || '');
	const triggerChange = (changedValue: {
		zkAddress?: string;
		zkPort?: number | null;
		path?: string;
	}) => {
		onChange &&
			onChange({ zkAddress, zkPort, path, ...value, ...changedValue });
	};
	const onAddressChange = (e: any) => {
		setZkAddress(e.target.value);
		triggerChange({ zkAddress: e.target.value });
	};
	const onPortChange = (newPort: number | null) => {
		setZkPort(newPort);
		triggerChange({ zkPort: newPort });
	};
	const onPathChange = (e: any) => {
		setPath(e.target.value);
		triggerChange({ path: e.target.value });
	};
	return (
		<Input.Group compact>
			<Input
				value={value?.zkAddress || zkAddress}
				onChange={onAddressChange}
				style={{ width: '30%' }}
				placeholder="请输入服务地址"
			/>
			<InputNumber
				value={value?.zkPort || zkPort}
				onChange={onPortChange}
				style={{ width: '20%' }}
				placeholder="端口"
			/>
			<Input
				disabled={!!judgeBackup}
				value={value?.path || path}
				onChange={onPathChange}
				style={{ width: '25%' }}
				placeholder="路径 如： /path"
			/>
		</Input.Group>
	);
}

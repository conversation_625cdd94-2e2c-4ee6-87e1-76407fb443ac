import { Form } from 'antd';
import React from 'react';
import ZookeeperServiceContent from './zookeeperService';
import pattern from '@/utils/pattern';

export default function ZookeeperService({
	judgeBackup,
	zookeeperInfo
}: {
	judgeBackup?: boolean;
	zookeeperInfo?: any;
}): JSX.Element {
	const checkService = (_: any, value: kafkaDTO) => {
		if (!value?.zkAddress) {
			return Promise.reject(new Error('请输入服务地址'));
		}
		if (!value?.zkPort) {
			return Promise.reject(new Error('请输入服务端口'));
		}
		if (!value?.path) {
			return Promise.reject(new Error('请输入唯一路径'));
		}
		if (!new RegExp(pattern.zkPath).test(value.path)) {
			return Promise.reject(new Error('路径不符合规则'));
		}
		return Promise.resolve();
	};

	return (
		<Form.Item
			name="kafkaDTO"
			label="Zookeeper服务"
			required
			rules={[{ validator: checkService }]}
			extra={
				judgeBackup ? (
					<p style={{ color: '#ff4d4f' }}>
						请输入源服务绑定的Zookeeper服务的克隆服务的地址信息
					</p>
				) : null
			}
		>
			<ZookeeperServiceContent
				judgeBackup={judgeBackup}
				defaultPath={zookeeperInfo?.path}
			/>
		</Form.Item>
	);
}

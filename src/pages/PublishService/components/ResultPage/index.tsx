import React from 'react';
import { Result } from 'antd';
import { ResultStatusType } from 'antd/lib/result';
import { LoadingOutlined } from '@ant-design/icons';
export default function ResultPage({
	loading,
	title,
	status,
	extra,
	children
}: {
	loading: boolean;
	title: string;
	status: ResultStatusType;
	extra: React.ReactNode;
	children?: JSX.Element;
}): JSX.Element {
	return (
		<Result
			status={status}
			title={title}
			icon={loading ? <LoadingOutlined /> : undefined}
			extra={extra}
		>
			{children && children}
		</Result>
	);
}

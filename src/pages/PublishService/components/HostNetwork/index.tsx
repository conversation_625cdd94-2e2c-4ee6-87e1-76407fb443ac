import React, { useEffect, useState } from 'react';
import { Form } from 'antd';
import storage from '@/utils/storage';
import HostNetworkContent from './hostNetwork';

export default function HostNetwork(): JSX.Element {
	// * feature 主机网络是否打开
	const [hostNetWorkAPI] = useState<boolean>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'hostNetWork')?.enabled ?? true
	);
	if (hostNetWorkAPI) {
		return (
			<Form.Item
				name="hostNetwork"
				label="网络方案"
				valuePropName="checked"
				initialValue={false}
				tooltip={
					<>
						<p>
							主机网络：主机所使用的在节点网络地址范围内的IP地址
						</p>
						<p>
							容器网络：为集群内容器分配在容器网络地址范围内的IP地址
						</p>
					</>
				}
			>
				<HostNetworkContent />
			</Form.Item>
		);
	} else {
		return <></>;
	}
}

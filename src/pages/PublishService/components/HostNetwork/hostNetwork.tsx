import React, { useState } from 'react';
import { Switch, Form } from 'antd';

export default function HostNetworkContent({
	value,
	onChange
}: {
	value?: boolean;
	onChange?: (value: boolean) => void;
}): JSX.Element {
	const hostNetwork = Form.useWatch('hostNetwork');
	const [checked, setChecked] = useState<boolean>(hostNetwork || false);
	return (
		<>
			<strong>容器网络</strong>
			<Switch
				checked={hostNetwork || checked}
				onChange={(checked: boolean) => {
					setChecked(checked);
					onChange && onChange(checked);
				}}
				style={{ margin: '0 8px' }}
			/>
			<strong>主机网络</strong>
		</>
	);
}

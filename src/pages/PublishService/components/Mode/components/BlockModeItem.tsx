import React, { useEffect, useState } from 'react';
import { Form, Space } from 'antd';
import './index.less';
import storage from '@/utils/storage';
import EditQuota from './EditQuota';
import { minmax } from '@/utils/const';
import { useParams } from 'react-router';

const Title: any = {
	master: '主节点',
	data: '数据节点',
	client: '协调节点',
	cold: '冷数据节点',
	kibana: 'Kibana节点',
	redis: 'Redis节点',
	sentinel: '哨兵节点',
	proxy: 'proxy节点',
	mysql: '普通节点'
};
interface BlockModeItemProps {
	modeType: string; // * mode 类型
	type: string; // * 中间件类型
	data: ModeItemData; // * 显示数据
	isTemplate?: boolean;
	onChange: (value: ModeItemData) => void;
}
export default function BlockModeItem(props: BlockModeItemProps): JSX.Element {
	const { type, data, modeType, onChange, isTemplate } = props;
	const params: ParamsProps = useParams();
	const formClusterAndNamespace = Form.useWatch('clusterAndNamespace');
	const quota = Form.useWatch('quota');
	// * feature 自定义端口是否打开
	const [customPortAPI] = useState<boolean>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'customPort')?.enabled ?? true
	);
	// * 修改节点规格的显示弹窗
	const [open, setOpen] = useState<boolean>(false);
	// * 判断proxy节点如何修改的标记
	const [isChangeBySelf, setIsChangeBySelf] = useState<boolean>(
		params.namespace && params.middlewareName ? true : false
	);
	useEffect(() => {
		if (quota) {
			if (modeType === 'proxy') {
				if (!isChangeBySelf) {
					onChange({
						...data,
						cpu: type === 'mysql' ? quota.mysql.cpu / 4 : 1,
						memory: minmax(
							quota[type].memory / 4,
							0.256,
							type === 'redis' ? 2 : 0
						)
					});
				}
				setIsChangeBySelf(false);
			}
		}
	}, [quota]);
	const titleRender = () => {
		switch (type) {
			case 'elasticsearch':
				return (
					<Space>
						{Title[modeType]}
						<div className="block-mode-item-circle">{data.num}</div>
					</Space>
				);
			case 'redis':
				return (
					<Space>
						{Title[modeType]}
						{modeType === 'sentinel' && (
							<div className="block-mode-item-circle">
								{data.num}
							</div>
						)}
					</Space>
				);
			default:
				return Title[modeType];
		}
	};
	const storageRender = () => {
		if (modeType === 'kibana') return <></>;
		if (modeType === 'sentinel') return <></>;
		if (modeType === 'proxy') return <></>;
		if (data.storageId) {
			if (data.storageClassQuota === 0) {
				return (
					<li style={{ color: '#D93026' }}>
						{data.storageClassNameAndAliasName}:{' '}
						{data.storageClassQuota}GB
					</li>
				);
			} else {
				return (
					<li>
						{data.storageClassNameAndAliasName}:{' '}
						{data.storageClassQuota}GB
					</li>
				);
			}
		} else {
			return <li style={{ color: '#D93026' }}>存储配额：未配置</li>;
		}
	};
	const portRender = () => {
		if (customPortAPI) {
			if (modeType === 'sentinel') {
				return (
					<>
						{data?.sentinelPort && (
							<li>哨兵节点端口：{data?.sentinelPort}</li>
						)}
						{data?.sentinelExporterPort && (
							<li>
								哨兵节点Exporter端口：
								{data?.sentinelExporterPort}
							</li>
						)}
					</>
				);
			} else if (type === 'redis' && modeType === 'proxy') {
				return (
					<>
						{data?.predixyPort && (
							<li>代理节点端口：{data?.predixyPort}</li>
						)}
						{data?.predixyExporterPort && (
							<li>
								代理节点Exporter端口：
								{data?.predixyExporterPort}
							</li>
						)}
					</>
				);
			} else {
				return <></>;
			}
		} else {
			return <></>;
		}
	};
	const onCreate = (value: ModeItemData) => {
		if (modeType === 'proxy') {
			setIsChangeBySelf(true);
		}
		if (modeType === 'mysql') {
			setIsChangeBySelf(false);
		}
		onChange(value);
		setOpen(false);
	};
	if (data?.disabled) {
		return (
			<div className="block-mode-item">
				<div className="block-mode-item-title-disabled">
					<span>{Title[modeType]}</span>
				</div>
				<div className="block-mode-item-content-disabled">未启用</div>
			</div>
		);
	}
	return (
		<>
			<div className="block-mode-item" onClick={() => setOpen(true)}>
				<div className="block-mode-item-title">{titleRender()}</div>
				<div className="block-mode-item-content">
					<ul>
						<li>CPU: {data?.cpu}Core</li>
						<li>内存: {data?.memory}Gi</li>
						{storageRender()}
						{portRender()}
					</ul>
				</div>
			</div>
			{open && (
				<EditQuota
					open={open}
					onCancel={() => setOpen(false)}
					onCreate={onCreate}
					modeType={modeType}
					data={data}
					type={type}
					quota={quota}
					isTemplate={isTemplate}
					clusterId={formClusterAndNamespace?.[0]}
					namespace={formClusterAndNamespace?.[1]}
					isActiveActive={formClusterAndNamespace?.[2]}
				/>
			)}
		</>
	);
}

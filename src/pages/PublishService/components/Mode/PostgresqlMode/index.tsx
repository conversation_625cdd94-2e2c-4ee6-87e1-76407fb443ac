import React from 'react';
import { Form, InputNumber } from 'antd';
import ModeContent from './modeContent';

export default function PgsqlMode({
	judgeBackup
}: {
	judgeBackup?: boolean;
}): JSX.Element {
	return (
		<>
			<Form.Item
				name="mode"
				label="模式"
				tooltip="本模式中的主、从节点，特指不同类型实例个数"
			>
				<ModeContent judgeBackup={judgeBackup} />
			</Form.Item>
			<Form.Item noStyle shouldUpdate>
				{({ getFieldValue }) => {
					if (getFieldValue('mode') === '1m-ns') {
						return (
							<Form.Item
								label="从节点数"
								name="num"
								rules={[
									{
										required: true,
										message: '请输入从节点数！'
									}
								]}
								initialValue={2}
							>
								<InputNumber
									step={1}
									precision={0}
									name="从节点数量字段"
									min={2}
									max={6}
									disabled={!!judgeBackup}
								/>
							</Form.Item>
						);
					} else {
						return null;
					}
				}}
			</Form.Item>
		</>
	);
}

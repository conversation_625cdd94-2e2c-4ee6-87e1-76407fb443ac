import React from 'react';
import { Form, InputNumber } from 'antd';
import ModeContent from './modeContent';
import { formItemLayout618 } from '@/utils/const';

export default function RocketMQMode(): JSX.Element {
	return (
		<>
			<Form.Item
				name="mode"
				label="模式"
				{...formItemLayout618}
				tooltip={
					<div>
						<p>
							双主：主实例宕机期间，未被消费的信息在机器未恢复之前不可消费
						</p>
						<p>
							两主两从：主实例宕机期间，从实例仍可以对外提供消息的消费，但不支持写入，从实例无法自动切换为主实例
						</p>
						<p>
							三主三从：主实例宕机期间，从实例仍可以对外提供消息的消费，但不支持写入，从实例无法自动切换为主实例
						</p>
						<p>
							多副本模式：即DLedger模式，主实例宕机期间，自动进行选主，不影响消息的写入和消费
						</p>
					</div>
				}
				initialValue="2m-noslave"
			>
				<ModeContent />
			</Form.Item>
			<Form.Item noStyle shouldUpdate>
				{({ getFieldValue }) => {
					if (getFieldValue('mode') === 'dledger') {
						return (
							<>
								<Form.Item name="group" label="DLedger组数">
									<InputNumber
										step={1}
										precision={0}
										min={1}
									/>
								</Form.Item>
								<Form.Item name="replicas" label="副本数">
									<InputNumber
										step={1}
										precision={0}
										min={3}
									/>
								</Form.Item>
							</>
						);
					} else {
						return null;
					}
				}}
			</Form.Item>
		</>
	);
}

import React, { useEffect, useState } from 'react';
import { Form, Select } from 'antd';
import Sharding from './sharding';
import RedisQuotaContent from './redisQuotaContent';
import { formItemLayout618 } from '@/utils/const';

export default function RedisMode({
	judgeBackup
}: {
	judgeBackup?: boolean;
}): JSX.Element {
	const form = Form.useFormInstance();
	const clusterAndNamespace = Form.useWatch('clusterAndNamespace', form);
	const mode = Form.useWatch('mode', form);
	const [modeList, setModeList] = useState([
		{
			label: '集群模式',
			value: 'cluster'
		},
		{
			label: '哨兵模式',
			value: 'sentinel'
		},
		{
			label: '集群代理模式',
			value: 'agent'
		},
		{
			label: '哨兵代理模式',
			value: 'readWriteProxy'
		}
	]);
	useEffect(() => {
		if (clusterAndNamespace?.[2]) {
			setModeList([
				{
					label: '哨兵模式',
					value: 'sentinel'
				},
				{
					label: '哨兵代理模式',
					value: 'readWriteProxy'
				}
			]);
			if (mode === 'cluster' || mode === 'agent') {
				form.setFieldValue('mode', 'sentinel');
				onModeChange('sentinel');
			}
		} else {
			setModeList([
				{
					label: '集群模式',
					value: 'cluster'
				},
				{
					label: '哨兵模式',
					value: 'sentinel'
				},
				{
					label: '集群代理模式',
					value: 'agent'
				},
				{
					label: '哨兵代理模式',
					value: 'readWriteProxy'
				}
			]);
		}
	}, [clusterAndNamespace]);
	const onModeChange = (value: string) => {
		if (value === 'sentinel') {
			form.setFieldsValue({
				quota: {
					redis: {
						title: 'Redis 节点',
						instanceSpec: 'General',
						num: 2,
						specId: '1',
						cpu: 2,
						memory: 1,
						storageId: undefined,
						storageClassQuota: undefined
					},
					sentinel: {
						title: '哨兵节点',
						instanceSpec: 'General',
						num: 3,
						specId: '1',
						cpu: 0.256,
						memory: 0.512
					}
				},
				num: 2
			});
		} else if (value === 'agent') {
			form.setFieldsValue({
				quota: {
					redis: {
						title: 'Redis 节点',
						instanceSpec: 'General',
						num: 6,
						specId: '1',
						cpu: 2,
						memory: 1,
						storageId: undefined,
						storageClassQuota: undefined
					},
					proxy: {
						title: 'proxy节点',
						instanceSpec: 'General',
						num: 3,
						specId: '0',
						cpu: 1,
						memory: 0.256
					}
				},
				num: 6
			});
		} else if (value === 'readWriteProxy') {
			form.setFieldsValue({
				quota: {
					redis: {
						title: 'Redis 节点',
						instanceSpec: 'General',
						num: 2,
						specId: '1',
						cpu: 2,
						memory: 1,
						storageId: undefined,
						storageClassQuota: undefined
					},
					sentinel: {
						title: '哨兵节点',
						instanceSpec: 'General',
						num: 3,
						specId: '1',
						cpu: 0.256,
						memory: 0.512
					},
					proxy: {
						title: 'proxy节点',
						instanceSpec: 'General',
						num: 3,
						specId: '0',
						cpu: 1,
						memory: 0.256
					}
				},
				num: 2
			});
		} else {
			form.setFieldsValue({
				quota: {
					redis: {
						title: 'Redis 节点',
						instanceSpec: 'General',
						num: 3,
						specId: '1',
						cpu: 2,
						memory: 1,
						storageId: undefined,
						storageClassQuota: undefined
					}
				},
				num: 6
			});
		}
	};
	return (
		<>
			<Form.Item
				name="mode"
				label="模式"
				initialValue="cluster"
				tooltip="本模式中的分片概念，特指一对有主从关系的节点"
			>
				<Select
					disabled={!!judgeBackup}
					options={modeList}
					onChange={onModeChange}
					style={{ width: '182px' }}
				/>
			</Form.Item>
			<Form.Item
				label="分片数"
				{...formItemLayout618}
				name="num"
				initialValue={6}
			>
				<Sharding judgeBackup={judgeBackup} />
			</Form.Item>
			{mode !== 'cluster' && (
				<Form.Item {...formItemLayout618} label=" " name="quota">
					<RedisQuotaContent />
				</Form.Item>
			)}
		</>
	);
}

import React from 'react';
import KafkaMode from './kafkaMode';
import ZooKeeperMode from './zookeeperMode';
import RocketMQMode from './RocketMQMode';
import ElasticsearchMode from './ElasticsearchMode/elasticsearchMode';
import RedisMode from './RedisMode';
import PgsqlMode from './PostgresqlMode';
import MysqlMode from './MysqlMode';
export default function Mode({
	type,
	judgeBackup,
	judgeDisaster
}: {
	type: string;
	judgeBackup?: boolean;
	judgeDisaster?: boolean;
}): JSX.Element {
	if (type === 'kafka') {
		return <KafkaMode judgeBackup={judgeBackup} />;
	} else if (type === 'zookeeper') {
		return <ZooKeeperMode judgeBackup={judgeBackup} />;
	} else if (type === 'rocketmq') {
		return <RocketMQMode />;
	} else if (type === 'elasticsearch') {
		return <ElasticsearchMode judgeBackup={judgeBackup} />;
	} else if (type === 'redis') {
		return <RedisMode judgeBackup={judgeBackup} />;
	} else if (type === 'postgresql') {
		return <PgsqlMode judgeBackup={judgeBackup} />;
	} else if (type === 'mysql') {
		return (
			<MysqlMode
				judgeBackup={judgeBackup}
				judgeDisaster={judgeDisaster}
			/>
		);
	} else {
		return <></>;
	}
}

import React, { useEffect, useState } from 'react';
import { Form, InputNumber } from 'antd';
import SelectBlock from '@/components/SelectBlock';
const modeList = [
	{
		label: '集群模式',
		value: 'cluster'
	}
];
export default function ZooKeeperMode({
	judgeBackup
}: {
	judgeBackup?: boolean;
}): JSX.Element {
	const form = Form.useFormInstance();
	const [mode, setMode] = useState<string>('cluster');
	useEffect(() => {
		form.setFieldsValue({
			mode
		});
	}, [mode]);
	return (
		<>
			<Form.Item
				name="mode"
				label="模式"
				tooltip="集群模式中，具备自动选举leader能力，保证高可用"
			>
				<SelectBlock
					disabled={!!judgeBackup}
					options={modeList}
					currentValue={mode}
					onCallBack={(value) => {
						setMode(value);
					}}
				/>
			</Form.Item>
			<Form.Item
				name="num"
				label="节点数"
				rules={[{ required: true, message: '请输入节点数!' }]}
				initialValue={3}
			>
				<InputNumber
					disabled={!!judgeBackup}
					step={1}
					precision={0}
					min={3}
					max={10}
				/>
			</Form.Item>
		</>
	);
}

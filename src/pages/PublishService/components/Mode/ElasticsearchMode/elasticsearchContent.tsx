import React, { useState } from 'react';
import SelectBlock from '@/components/SelectBlock';
import '../../../index.less';
const modeList = [
	{
		label: 'N主',
		value: 'simple'
	},
	{
		label: 'N主 N数据',
		value: 'regular'
	},
	{
		label: 'N主 N数据 N协调',
		value: 'complex'
	},
	{
		label: 'N主 N数据 N冷',
		value: 'complex-cold'
	},
	{
		label: 'N主 N数据 N冷 N协调',
		value: 'cold-complex'
	}
];
export default function ElasticsearchContent({
	value,
	onChange,
	judgeBackup,
	isTemplate
}: {
	value?: string;
	onChange?: (value: string) => void;
	judgeBackup?: boolean;
	isTemplate?: boolean;
}): JSX.Element {
	const [mode, setMode] = useState<string>('simple');
	return (
		<SelectBlock
			options={modeList}
			currentValue={value || mode}
			onCallBack={(value: any) => {
				setMode(value);
				onChange && onChange(value);
			}}
			disabled={!!judgeBackup || isTemplate}
		/>
	);
}

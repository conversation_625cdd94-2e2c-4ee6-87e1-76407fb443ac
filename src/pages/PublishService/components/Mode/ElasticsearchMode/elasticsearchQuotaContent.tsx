import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router';
import BlockModeItem from '../components/BlockModeItem';
import { Form } from 'antd';

export default function ElasticsearchQuotaContent({
	value,
	onChange,
	isTemplate
}: {
	value?: any;
	onChange?: (value: any) => void;
	isTemplate?: boolean;
}): JSX.Element {
	const form = Form.useFormInstance();
	const mode = Form.useWatch('mode', form);
	const params: ParamsProps = useParams();
	const [quota, setQuota] =
		useState<ElasticsearchServiceSendData['quota']>(value);
	useEffect(() => {
		if (!(params.middlewareName && params.namespace) && !isTemplate) {
			if (mode) {
				const { master, kibana, data, client, cold } = quota;
				if (mode === 'simple') {
					master.disabled = false;
					kibana.disabled = false;
					data.disabled = true;
					client.disabled = true;
					cold.disabled = true;
				} else if (mode === 'regular') {
					master.disabled = false;
					kibana.disabled = false;
					data.disabled = false;
					client.disabled = true;
					cold.disabled = true;
				} else if (mode === 'complex') {
					master.disabled = false;
					kibana.disabled = false;
					data.disabled = false;
					client.disabled = false;
					cold.disabled = true;
				} else if (mode === 'complex-cold') {
					master.disabled = false;
					kibana.disabled = false;
					data.disabled = false;
					client.disabled = true;
					cold.disabled = false;
				} else if (mode === 'cold-complex') {
					master.disabled = false;
					kibana.disabled = false;
					data.disabled = false;
					client.disabled = false;
					cold.disabled = false;
				}
				onChange && onChange({ master, kibana, data, client, cold });
			}
		}
	}, [mode]);
	return (
		<div className="es-quota-content">
			{Object.keys(value || quota).map((item) => {
				return (
					<BlockModeItem
						key={item}
						type="elasticsearch"
						modeType={item}
						isTemplate={isTemplate}
						data={value ? value[item] : quota[item]}
						onChange={(changedValue: ModeItemData) => {
							setQuota({
								...(value ? value : quota),
								[item]: changedValue
							});
							onChange &&
								onChange({
									...(value ? value : quota),
									[item]: changedValue
								});
						}}
					/>
				);
			})}
		</div>
	);
}

import React, { useEffect, useState } from 'react';
import { Form } from 'antd';
import SelectBlock from '@/components/SelectBlock';

export default function ModeContent({
	value,
	onChange,
	judgeBackup
}: {
	value?: string;
	onChange?: (value: string) => void;
	judgeBackup?: boolean;
}): JSX.Element {
	const form = Form.useFormInstance();
	const clusterAndNamespace = Form.useWatch('clusterAndNamespace');
	const [mode, setMode] = useState<string>(value || '1m-1s');
	const [mode2List, setMode2List] = useState([
		{
			value: '1m-1s',
			label: '一主一从'
		},
		{
			value: '1m-ns',
			label: '一主多从'
		}
	]);
	useEffect(() => {
		if (clusterAndNamespace?.[2]) {
			setMode2List([
				{
					value: '1m-1s',
					label: '一主一从'
				},
				{
					value: '1m-3s',
					label: '一主三从'
				}
			]);
		} else {
			setMode2List([
				{
					value: '1m-1s',
					label: '一主一从'
				},
				{
					value: '1m-ns',
					label: '一主多从'
				}
			]);
		}
		onModeChange('1m-1s');
	}, [clusterAndNamespace]);
	useEffect(() => {
		if (mode === '1m-1s') {
			form.setFieldsValue({
				num: 1
			});
		} else if (mode === '1m-3s') {
			form.setFieldsValue({
				num: 3
			});
		} else if (mode === '1m-ns') {
			form.setFieldsValue({
				num: 2
			});
		}
	}, [mode]);
	const onModeChange = (value: any) => {
		setMode(value);
		onChange && onChange(value);
	};
	return (
		<SelectBlock
			options={mode2List}
			currentValue={value || mode}
			onCallBack={onModeChange}
			disabled={!!judgeBackup}
		/>
	);
}

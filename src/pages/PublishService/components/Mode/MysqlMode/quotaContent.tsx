import React, { useState } from 'react';
import BlockModeItem from '../components/BlockModeItem';
import '../../../index.less';

export default function QuotaContent({
	value,
	onChange
}: {
	value?: any;
	onChange?: (value: any) => void;
}): JSX.Element {
	const [quota, setQuota] = useState(value);
	return (
		<div className="mysql-quota-content">
			{Object.keys(quota).map((key) => {
				return (
					<BlockModeItem
						key={key}
						modeType={key}
						type="mysql"
						data={quota[key]}
						onChange={(changedValue: any) => {
							setQuota({
								...quota,
								[key]: changedValue
							});
							onChange &&
								onChange({
									...quota,
									[key]: changedValue
								});
						}}
					/>
				);
			})}
		</div>
	);
}

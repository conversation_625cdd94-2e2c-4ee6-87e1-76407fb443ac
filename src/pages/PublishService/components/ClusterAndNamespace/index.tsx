import React, { useEffect, useState } from 'react';
import { Cascader, Form, notification } from 'antd';
import { useParams } from 'react-router';
import { ValidateStatus } from 'antd/es/form/FormItem';
import { DefaultOptionType } from 'antd/lib/cascader';
import Decimal from 'decimal.js';
import { getProjectNamespace } from '@/services/project';
import { getClusters } from '@/services/common';
import { NamespaceItem } from '@/pages/ProjectDetail/projectDetail';
import { getComponent } from '@/services/common';
import { getCanReleaseMiddleware } from '@/services/middleware';
import { checkLicense } from '@/services/user';
import { operatorStatus } from '@/services/repository';
import '../../index.less';

export default function ClusterAndNamespace({
	setCurrentNamespace,
	judgeBackup,
	judgeDisaster,
	type,
	isTemplate
}: {
	setCurrentNamespace: (value?: NamespaceItem) => void;
	judgeBackup?: boolean;
	judgeDisaster?: boolean;
	isTemplate?: boolean;
	type: string;
}): JSX.Element {
	const form = Form.useFormInstance();
	const params: ParamsProps = useParams();
	const formOrgAndPro = Form.useWatch('organAndProject');
	const formClusterAndNamespace = Form.useWatch('clusterAndNamespace');
	const formName = Form.useWatch('name');
	const [namespaceList, setNamespaceList] = useState<NamespaceItem[]>([]);
	const [curNamespace, setCurNamespace] = useState<NamespaceItem>();
	const [options, setOptions] = useState<Option[]>([]);
	const [value, setValue] = useState<string[]>([]);
	const [validateStatus, setValidateStatus] = useState<ValidateStatus>();
	const [help, setHelp] = useState<React.ReactNode>();
	useEffect(() => {
		if (!formOrgAndPro) return;
		async function getData() {
			const result = await getComponent({
				clusterId: '*',
				componentName: 'middleware-controller'
			});
			const result2 = await operatorStatus({
				type: params.name
			});
			const res = await getClusters({
				organId: formOrgAndPro?.[0],
				projectId: formOrgAndPro?.[1]
			});
			if (!res.success) {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
				return;
			}
			const tl = res.data.map((item: any) => {
				return {
					value: item.id,
					label: item.nickname || item.name,
					isLeaf: false,
					disabled: disabledCluster(item, result.data, result2.data)
				};
			});
			let clusterId_temp = tl?.[0]?.value;
			if (formClusterAndNamespace) {
				clusterId_temp = formClusterAndNamespace[0];
			}
			if (judgeBackup) {
				clusterId_temp = params.clusterId;
			}
			const res2 = await getProjectNamespace({
				organId: formOrgAndPro?.[0],
				projectId: formOrgAndPro?.[1],
				clusterId: clusterId_temp,
				withQuota: true
			});
			if (!res2.success) {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res2.errorMsg}</p>
							<p>{res2.errorDetail}</p>
						</>
					)
				});
				return;
			}
			const rt =
				type === 'mysql' ||
				type === 'redis' ||
				type === 'postgresql' ||
				type === 'rocketmq'
					? res2.data
					: res2.data.filter((item) => item.availableDomain !== true);
			setNamespaceList(rt);
			const ntl = rt?.map((item) => {
				return {
					value: item.name,
					label: item.aliasName || item.name,
					isLeaf: true
				};
			});
			tl.map((item: any) => {
				if (item.value === clusterId_temp) {
					item.children = ntl || [];
				}
				return item;
			});
			setOptions(tl);
		}
		getData();
	}, [formOrgAndPro]);
	useEffect(() => {
		if (
			namespaceList.length !== 0 &&
			options.length !== 0 &&
			formClusterAndNamespace
		) {
			const cn = namespaceList.find(
				(item: NamespaceItem) =>
					item.name === formClusterAndNamespace[1]
			);
			setCurNamespace(cn);
			setCurrentNamespace(cn);
			formName && form.validateFields(['name']);
		}
	}, [formClusterAndNamespace, namespaceList, options]);
	const displayRender = (
		labels: string[],
		selectedOptions?: DefaultOptionType[]
	) => {
		if (labels?.[2]) {
			if (typeof labels[1] === 'string') {
				return (
					<span>
						{labels[0]} / {labels[1]}{' '}
						<span className="available-domain">可用区</span>
					</span>
				);
			} else {
				return (
					<span>
						{labels[0]} / {(labels[1] as any).props?.children[0]}{' '}
						<span className="available-domain">可用区</span>
					</span>
				);
			}
		} else {
			return labels?.map((label, i) => {
				const option = selectedOptions?.[i];
				if (i === 0) {
					return <span key={option?.value}>{label} / </span>;
				}
				if (i === 1) {
					return <span key={option?.value}>{label}</span>;
				}
			});
		}
	};
	const disabledCluster = (
		item: any,
		componentInfo: any,
		operatorInfo: any
	) => {
		const cur = operatorInfo.find((i: any) => i.clusterId === item.id);
		if (cur.status === 1 || cur.status === 4) {
			if (componentInfo[item.id]?.status === 3) {
				return false;
			} else {
				return true;
			}
		} else {
			return true;
		}
	};
	const onChange = (changedValue: any, selectedOptions: any) => {
		if (!changedValue) {
			if (isTemplate) {
				// * 当该组件在通过模板发布中使用时，storageClassQuota是固定的，故不清除该值
				form.setFieldsValue({
					storageClassName: undefined
				});
			} else {
				form.setFieldsValue({
					storageClassName: undefined,
					storageClassQuota: undefined
				});
			}
			setCurNamespace(undefined);
			setCurrentNamespace(undefined);
			formName && form.validateFields(['name']);
			return;
		}
		// * 当命名空间的选择发生变化，则清空存储相关信息
		if (changedValue[1] !== value[1]) {
			if (isTemplate) {
				// * 当该组件在通过模板发布中使用时，storageClassQuota是固定的，故不清除该值
				form.setFieldsValue({
					storageClassName: undefined
				});
			} else {
				form.setFieldsValue({
					storageClassName: undefined,
					storageClassQuota: undefined
				});
			}
		}
		const cn = namespaceList.find(
			(item: NamespaceItem) => item.name === changedValue[1]
		);
		form.setFieldsValue({
			clusterAndNamespace: [
				changedValue[0],
				changedValue[1],
				cn?.availableDomain || false
			]
		});
		setCurNamespace(cn);
		setCurrentNamespace(cn);
		formName && form.validateFields(['name']);
		setValue(changedValue);
	};
	const loadData = (selectedOptions: DefaultOptionType[]) => {
		const targetOption = selectedOptions[selectedOptions.length - 1];
		targetOption.loading = true;
		return getProjectNamespace({
			organId: formOrgAndPro?.[0],
			projectId: formOrgAndPro?.[1],
			clusterId: targetOption?.value as string,
			withQuota: true
		}).then((res) => {
			if (res.success) {
				const tl =
					type === 'mysql' ||
					type === 'redis' ||
					type === 'postgresql' ||
					type === 'rocketmq'
						? res.data
						: res.data.filter(
								(item) => item.availableDomain !== true
						  );
				setNamespaceList(tl);
				const cnl = tl.map((item: NamespaceItem) => {
					const labelTemp = item.availableDomain ? (
						<div className="flex-space-between">
							{item.aliasName || item.name}{' '}
							{item.availableDomain ? (
								<span className="available-domain">可用区</span>
							) : null}
						</div>
					) : (
						item.aliasName || item.name
					);
					return {
						value: item.name,
						label: labelTemp,
						isLeaf: true
					};
				});
				targetOption.children = cnl;
				setOptions([...options]);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	const helpContent = (
		<>
			{curNamespace ? (
				<div>
					{`当前命名空间剩余可分配配额为：CPU${
						curNamespace?.quotas !== null
							? new Decimal(curNamespace?.quotas?.cpu?.request)
									.minus(
										new Decimal(
											curNamespace?.quotas?.cpu?.used
										)
									)
									.toFixed(1)
							: '-'
					}Core，内存${
						curNamespace?.quotas !== null
							? new Decimal(curNamespace?.quotas?.memory?.request)
									.minus(
										new Decimal(
											curNamespace?.quotas?.memory?.used
										)
									)
									.toFixed(1)
							: '-'
					}GB`}
				</div>
			) : null}
			{judgeDisaster &&
			params.clusterId &&
			params.clusterId === value[0] ? (
				<div>
					若有可用的其它集群的情况下，不建议将灾备服务和源服务部署在一个集群
				</div>
			) : null}
		</>
	);
	const checkClusterAndNamespace = async (_: any, valuef: any) => {
		if (!valuef) {
			form.setFieldsValue({
				chartVersion: ''
			});
			setValidateStatus('error');
			setHelp('请选择集群/命名空间');
			return Promise.reject();
		}
		const res = await getCanReleaseMiddleware({
			clusterId: valuef[0],
			type: params.name
		});
		if (res.success) {
			form.setFieldsValue({
				chartVersion: res.data.chartVersion
			});
		} else {
			form.setFieldsValue({
				chartVersion: ''
			});
			setValidateStatus('error');
			setHelp('中间件chart包存在异常！');
			return Promise.reject();
		}
		const res2 = await checkLicense({ license: '', clusterId: valuef[0] });
		if (res2.success) {
			if (!res2.data) {
				setValidateStatus('error');
				setHelp(
					'当前平台可用余额已不足2Core，如果您想继续使用zeus中间件一体化管理平台，请联系我们申请授权码。'
				);
				return Promise.reject();
			}
		} else {
			setValidateStatus('error');
			setHelp('当前授权接口返回错误，请重试！');
			return Promise.reject();
		}
		setValidateStatus('success');
		setHelp(helpContent);
		return Promise.resolve();
	};
	return (
		<>
			<Form.Item
				name="clusterAndNamespace"
				label="集群/命名空间"
				required={true}
				validateStatus={validateStatus}
				help={help}
				rules={[
					{
						validator: checkClusterAndNamespace
					}
				]}
			>
				<Cascader
					placeholder="请选择集群/命名空间"
					options={options}
					value={value}
					onChange={onChange}
					displayRender={displayRender}
					loadData={loadData}
					disabled={!!judgeBackup}
					popupClassName="publish-cascader-popup-content"
				/>
			</Form.Item>
			<Form.Item noStyle name="chartVersion" />
		</>
	);
}

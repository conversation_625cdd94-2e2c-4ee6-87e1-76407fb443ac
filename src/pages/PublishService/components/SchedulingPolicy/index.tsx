import { Form, Switch } from 'antd';
import React, { useEffect } from 'react';
import IntelligentScheduler from '../IntelligentScheduler';
import Toleration from '../Toleration';
import Affinity from '../Affinity';
import { getKey, getTolerations } from '@/services/middleware';
export default function SchedulingPolicy({
	clusterAndNamespace,
	children
}: {
	clusterAndNamespace?: any;
	children?: any;
}): JSX.Element {
	const form = Form.useFormInstance();
	const schedulingPolicy = Form.useWatch('schedulingPolicy');
	useEffect(() => {
		async function getData() {
			if (clusterAndNamespace?.[2]) {
				const res1 = await getKey();
				if (res1.success) {
					if (res1.data?.anti) {
						form.setFieldsValue({
							nodeAntiAffinitySwitch: true,
							nodeAntiAffinity: [
								{
									label: res1.data.label,
									required: res1.data.required,
									anti: true
								}
							]
						});
					} else {
						form.setFieldsValue({
							nodeAffinitySwitch: true,
							nodeAffinity: [
								{
									label: res1.data.label,
									checked: res1.data.required,
									anti: false
								}
							]
						});
					}
				}
				const res2 = await getTolerations();
				if (res2.success) {
					form.setFieldsValue({
						tolerationsSwitch: true,
						tolerations: [res2.data]
					});
				}
				form.setFieldsValue({
					schedulingPolicy: true
				});
			}
		}
		getData();
	}, [clusterAndNamespace]);
	return (
		<>
			<Form.Item
				label="调度策略"
				name="schedulingPolicy"
				valuePropName="checked"
				initialValue={false}
			>
				<Switch />
			</Form.Item>
			{schedulingPolicy && (
				<Form.Item label=" " name="schedulingPolicyContent">
					<IntelligentScheduler />
					<Affinity
						name="nodeAffinity"
						label="主机亲和"
						isAnti={false}
						disabled={false}
						clusterId={clusterAndNamespace?.[0]}
					/>
					<Affinity
						name="nodeAntiAffinity"
						label="主机反亲和"
						isAnti={true}
						disabled={false}
						clusterId={clusterAndNamespace?.[0]}
					/>
					<Toleration
						name="tolerations"
						label="主机容忍"
						clusterId={clusterAndNamespace?.[0]}
					/>
					{children ? children : <></>}
				</Form.Item>
			)}
		</>
	);
}

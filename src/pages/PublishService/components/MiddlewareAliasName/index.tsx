import React from 'react';
import { Form, Input } from 'antd';
import pattern from '@/utils/pattern';

export default function MiddlewareAliasName(): JSX.Element {
	return (
		<Form.Item
			label="显示名称"
			name="aliasName"
			rules={[
				{
					pattern: new RegExp(pattern.nickname),
					message:
						'请输入由汉字、字母、数字及“-”或“.”或“_”组成的2-80个字符'
				}
			]}
		>
			<Input placeholder="请输入由汉字、字母、数字及“-”或“.”或“_”组成的2-80个字符" />
		</Form.Item>
	);
}

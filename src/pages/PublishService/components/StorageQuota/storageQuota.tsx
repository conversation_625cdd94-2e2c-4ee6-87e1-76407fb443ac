import React, { useEffect, useState } from 'react';
import { Form, Input, InputNumber, Select } from 'antd';
import { StorageItem } from '@/pages/StorageManagement/storageManage';
import { getNamespaceStorages } from '@/services/storage';
import { formatNumber } from '@/utils/utils';

interface StorageQuotaContentProps {
	clusterId: string;
	namespace: string;
	defaultStorage?: number;
	isActiveActive?: boolean;
	isTemplate?: boolean;
}
export default function StorageQuotaContent(
	props: StorageQuotaContentProps
): JSX.Element {
	const {
		clusterId,
		defaultStorage = 1,
		isActiveActive,
		isTemplate,
		namespace
	} = props;
	const form = Form.useFormInstance();
	const formStorageId = Form.useWatch('storageId', form);
	const [storageClassList, setStorageClassList] = useState<StorageItem[]>([]);
	const [currentStorage, setCurrentStorage] = useState<number>();
	useEffect(() => {
		if (clusterId && namespace) {
			getNamespaceStorages({
				clusterId,
				namespace
			}).then((res) => {
				if (res.success) {
					if (res.data.length > 0) {
						isActiveActive
							? setStorageClassList(res.data)
							: setStorageClassList(
									res.data.filter(
										(item: any) => !item.isActiveActive
									)
							  );
					} else {
						setStorageClassList([]);
					}
				}
			});
		}
	}, [clusterId, namespace]);
	useEffect(() => {
		if (storageClassList.length !== 0 && formStorageId) {
			const temp = storageClassList.find(
				(item: StorageItem) => item.storageId === formStorageId
			);
			const name_temp = temp?.isActiveActive
				? `${temp.storageClassList[0].name},${temp.storageClassList[1].name}/${temp.aliasName}`
				: `${temp?.storageClassList[0].name}/${temp?.aliasName}`;
			form.setFieldValue(
				'storageMax',
				(temp?.quota?.request || 0) - (temp?.quota?.used || 0)
			);
			form.setFieldValue('storageIsActiveActive', temp?.isActiveActive);
			form.setFieldValue('storageClassNameAndAliasName', name_temp);
		}
	}, [formStorageId, storageClassList]);
	const handleChange = (value: string) => {
		const temp = storageClassList.find(
			(item: StorageItem) => item.storageId === value
		);
		const name_temp = temp?.isActiveActive
			? `${temp.storageClassList[0].name},${temp.storageClassList[1].name}/${temp.aliasName}`
			: `${temp?.storageClassList[0].name}/${temp?.aliasName}`;
		form.setFieldValue(
			'storageMax',
			(temp?.quota?.request || 0) - (temp?.quota?.used || 0)
		);
		form.setFieldValue('storageIsActiveActive', temp?.isActiveActive);
		form.setFieldValue('storageClassNameAndAliasName', name_temp);
	};
	const onInputChange = (value: any) => {
		setCurrentStorage && setCurrentStorage(value);
	};
	return (
		<Input.Group compact>
			<Form.Item
				name="storageId"
				required
				noStyle
				rules={[
					{
						required: true,
						message: '请选择存储'
					}
				]}
			>
				<Select
					placeholder="请选择存储"
					style={{
						width: 150
					}}
					onChange={handleChange}
					dropdownMatchSelectWidth={false}
				>
					{storageClassList.map((item: StorageItem) => {
						return (
							<Select.Option
								key={item.storageId}
								value={item.storageId}
							>
								{item.aliasName}
								{item.isActiveActive ? (
									<span className="available-domain">
										可用区
									</span>
								) : null}
							</Select.Option>
						);
					})}
				</Select>
			</Form.Item>
			<Form.Item
				noStyle
				rules={[
					{
						required: true,
						message: '请输入存储配额大小（GB）'
					},
					{
						type: 'number',
						min: defaultStorage,
						message: `存储配额不能小于${defaultStorage}GB`
					}
				]}
				name="storageClassQuota"
			>
				<InputNumber
					disabled={isTemplate}
					style={{ width: '200px' }}
					placeholder="请输入存储配额大小"
					onChange={onInputChange}
					value={formatNumber(currentStorage || 0)}
					addonAfter="GB"
				/>
			</Form.Item>
			<Form.Item noStyle name="storageIsActiveActive" />
			<Form.Item noStyle name="storageClassNameAndAliasName" />
		</Input.Group>
	);
}

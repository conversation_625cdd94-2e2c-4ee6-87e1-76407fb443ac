import React from 'react';
import { Form } from 'antd';
import StorageQuotaContent from './storageQuota';
interface StorageQuotaProps {
	clusterId?: string;
	namespace?: string;
	defaultStorage?: number;
	isActiveActive?: boolean;
	isTemplate?: boolean;
}
export default function StorageQuota(props: StorageQuotaProps): JSX.Element {
	const { clusterId, namespace, defaultStorage, isActiveActive, isTemplate } =
		props;
	const formClusterAndNamespace = Form.useWatch('clusterAndNamespace');
	return (
		<Form.Item name="storageMax" label="存储配额" required>
			<StorageQuotaContent
				defaultStorage={defaultStorage}
				clusterId={clusterId || formClusterAndNamespace?.[0]}
				namespace={namespace || formClusterAndNamespace?.[1]}
				isActiveActive={isActiveActive || formClusterAndNamespace?.[2]}
				isTemplate={isTemplate}
			/>
		</Form.Item>
	);
}

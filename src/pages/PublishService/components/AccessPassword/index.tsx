import React from 'react';
import { Form } from 'antd';
import AccessPasswordContent from './AccessPasswordContent';

export default function AccessPassword({
	type
}: {
	type: string;
}): JSX.Element {
	const checkDatabaseMessage = (_: any, value: any) => {
		if (type !== 'redis') {
			if (!value || (!value.user && !value.password)) {
				return Promise.reject(new Error('请输入数据库账号密码'));
			}
			if (!value.user) {
				return Promise.reject(new Error('请输入数据库账号'));
			}
			if (!value.password) {
				return Promise.reject(new Error('请输入数据库密码'));
			}
		} else {
			if (!value || !value.user) {
				return Promise.reject(new Error('请输入数据库账号'));
			}
		}
		return Promise.resolve();
	};
	if (
		type === 'mysql' ||
		type === 'postgresql' ||
		type === 'redis' ||
		type === 'elasticsearch' ||
		type === 'nacos' ||
		type === 'rabbitmq'
	) {
		return (
			<Form.Item
				name="databaseMessage"
				label="数据库信息"
				required
				rules={[{ validator: checkDatabaseMessage }]}
			>
				<AccessPasswordContent />
			</Form.Item>
		);
	} else {
		return <></>;
	}
}

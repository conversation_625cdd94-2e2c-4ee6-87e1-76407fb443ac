import { Input, Space } from 'antd';
import React, { useState } from 'react';
import './index.less';

interface databaseAccount {
	user: string;
	password: string;
}
export default function AccessPasswordContent({
	value,
	onChange
}: {
	value?: databaseAccount;
	onChange?: (value: databaseAccount) => void;
}): JSX.Element {
	const [user, setUser] = useState<string>('');
	const [password, setPassword] = useState<string>('');
	return (
		<Space className="access-password-content">
			<Input
				value={value?.user || user}
				onChange={(e: any) => {
					setUser(e.target.value);
					onChange && onChange({ user: e.target.value, password });
				}}
				style={{ width: '100%' }}
				addonBefore="账号"
				placeholder="请输入数据库账号"
			/>
			<Input.Password
				value={value?.password || password}
				onChange={(e: any) => {
					setPassword(e.target.value);
					onChange && onChange({ user, password: e.target.value });
				}}
				style={{ width: '100%' }}
				addonBefore="密码"
				placeholder="请输入数据库密码"
			/>
		</Space>
	);
}

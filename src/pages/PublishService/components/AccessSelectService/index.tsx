import React, { useEffect, useState } from 'react';
import { Form, Input, Spin, Table, Tag, notification } from 'antd';
import { useParams } from 'react-router';
import { getProjectAgentList } from '@/services/agent';
import { agentPhaseRender } from '@/utils/utils';
import { agentFilters } from '@/utils/const';
import './index.less';

export default function AccessSelectService({
	data,
	clusterAndNamespace
}: {
	data: any;
	clusterAndNamespace: any;
}): JSX.Element {
	const params: AccessParams = useParams();
	const form = Form.useFormInstance();
	const { organId, projectId } = params;
	const [spinning, setSpinning] = useState<boolean>(false);
	const [selected, setSelected] = useState<AgentItem[]>([]);
	const [agents, setAgents] = useState<AgentItem[]>([]);
	const [dataSource, setDataSource] = useState<AgentItem[]>([]);
	const [primaryKeys, setPrimaryKeys] = useState<React.Key[]>([]);
	useEffect(() => {
		async function getAllData() {
			setSpinning(true);
			const res1 = await getProjectAgentList({ organId, projectId });
			if (res1.success) {
				setAgents(res1.data);
				setDataSource(res1.data);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res1.errorMsg}</p>
							<p>{res1.errorDetail}</p>
						</>
					)
				});
			}
			setSpinning(false);
		}
		getAllData();
	}, []);
	useEffect(() => {
		if (clusterAndNamespace?.[0] && agents?.length > 0) {
			const list = agents.filter(
				(item) => item.clusterId === clusterAndNamespace?.[0]
			);
			setDataSource(list);
			if (data.current) {
				setSelected(data.current.agentList);
				setPrimaryKeys(
					data.current.agentList.map((item: AgentItem) => item.name)
				);
			} else {
				setSelected([]);
				setPrimaryKeys([]);
			}
		}
	}, [clusterAndNamespace, agents]);
	const onClose = (record: AgentItem, e: React.MouseEvent<HTMLElement>) => {
		const list = selected.filter((item) => item.address !== record.address);
		const listTemp = primaryKeys.filter((item) => item !== record.name);
		setSelected(list);
		setPrimaryKeys(listTemp);
		form.setFieldsValue({
			agentList: list
		});
	};
	const onChange = (
		selectedRowKeys: React.Key[],
		selectedRows: AgentItem[]
	) => {
		setPrimaryKeys(selectedRowKeys);
		setSelected(selectedRows);
		form.setFieldsValue({
			agentList: selectedRows
		});
	};
	const handleSearch = (value: string) => {
		const list = agents.filter(
			(item) => item.clusterId === clusterAndNamespace?.[0]
		);
		const dt = list.filter((item) => item.name.includes(value));
		setDataSource(dt);
	};
	return (
		<Spin spinning={spinning}>
			<Form.Item name="agentList" noStyle>
				<Input.Search
					placeholder="请输入关键字搜索"
					style={{ width: '250px' }}
					onSearch={handleSearch}
					allowClear
				/>
				<div className="select-service-select-master">
					当前已选择的服务器：
					{selected.map((item: any) => (
						<Tag
							key={item.name}
							closable
							onClose={(e) => onClose(item, e)}
						>
							{item.address}
						</Tag>
					))}
				</div>
				<div className="mt-16">
					<Table
						rowKey="name"
						dataSource={dataSource}
						rowSelection={{
							selectedRowKeys: primaryKeys,
							onChange,
							getCheckboxProps: (record) => ({
								disabled: record.phase !== 'Online'
							})
						}}
					>
						<Table.Column title="服务器名称" dataIndex="name" />
						<Table.Column title="IP地址" dataIndex="address" />
						<Table.Column title="操作系统" dataIndex="osType" />
						<Table.Column
							title="客户端状态"
							dataIndex="phase"
							filterMultiple={false}
							filters={agentFilters}
							onFilter={(value, record: AgentItem) =>
								record.phase === value
							}
							render={agentPhaseRender}
						/>
					</Table>
				</div>
			</Form.Item>
		</Spin>
	);
}

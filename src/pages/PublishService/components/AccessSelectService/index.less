.cluster-card-content {
	display: flex;
	align-items: center;
	gap: 8px;
	.cluster-card-item {
		width: 200px;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 8px;
		padding-top: 24px;
		border-radius: @border-radius;
		border: 1px rgba(216,222,229,.4) solid;
		cursor: pointer;
	}
}
.cluster-card-item-active {
	border-color: @primary-color !important;
	background-color: #f9fbfd;
}
.cluster-card-item-checked {
	align-self: flex-end;
}
.select-service-select-master {
	display: flex;
	gap: 8px;
	align-items: center;
	margin-top: @margin;
}

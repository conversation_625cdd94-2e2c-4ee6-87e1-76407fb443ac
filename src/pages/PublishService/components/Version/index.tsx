import React from 'react';
import { Form } from 'antd';
import VersionContent from './version';
import { formItemLayout618, formItemLayout519 } from '@/utils/const';
interface VersionProps {
	type: string;
	judgeBackup?: boolean;
	judgeDisaster?: boolean;
}
export default function Version(props: VersionProps): JSX.Element {
	const { type, judgeBackup, judgeDisaster } = props;
	const chartVersion = Form.useWatch('chartVersion');
	return (
		<Form.Item
			label="版本"
			name="version"
			rules={[{ required: true, message: '请选择服务版本' }]}
			{...(type === 'elasticsearch' || type === 'postgresql'
				? formItemLayout519
				: formItemLayout618)}
		>
			<VersionContent
				type={type}
				chartVersion={chartVersion}
				judgeBackup={judgeBackup}
				judgeDisaster={judgeDisaster}
			/>
		</Form.Item>
	);
}

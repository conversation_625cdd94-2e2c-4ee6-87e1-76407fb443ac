import React, { useEffect, useState } from 'react';
import { notification, Typography } from 'antd';
import SelectBlock from '@/components/SelectBlock';
import { AutoCompleteOptionItem } from '@/types/comment';
import { getMiddlewareVersions } from '@/services/common';
import semver from 'semver';
const { Text } = Typography;
interface VersionContentProps {
	type: string;
	chartVersion?: string;
	value?: string;
	onChange?: (value: string) => void;
	judgeBackup?: boolean;
	judgeDisaster?: boolean;
}
export default function VersionContent(
	props: VersionContentProps
): JSX.Element {
	const { type, chartVersion, value, onChange, judgeBackup, judgeDisaster } =
		props;
	const [versionOriginData, setVersionOriginData] = useState<any>([]);
	const [versionFatherList, setVersionFatherList] = useState<
		AutoCompleteOptionItem[]
	>([]);
	const [fatherVersion, setFatherVersion] = useState<string>('');
	const [versionList, setVersionList] = useState<AutoCompleteOptionItem[]>(
		[]
	);
	const [version, setVersion] = useState<string>('');
	useEffect(() => {
		if (versionFatherList && versionFatherList.length) {
			if (value) {
				setFatherVersion(value?.split('.')[0]);
				setVersionList(
					versionOriginData
						.find(
							(item: any) =>
								item.masterVersion === value?.split('.')[0]
						)
						.slaveVersion.map((item: string) => {
							return { value: item, label: item };
						})
				);
				setVersion(value);
				onChange && onChange(value);
			} else {
				const maxFatherVersion = versionFatherList.reduce(
					(maxValue, current) => {
						const currentValue = Number(current.value);
						const maxValueNum = Number(maxValue);
						return currentValue > maxValueNum
							? current.value
							: maxValue;
					},
					versionFatherList[0].value
				);
				setFatherVersion(maxFatherVersion);
				const childVersionList = versionOriginData.find(
					(item: any) => item.masterVersion === maxFatherVersion
				)?.slaveVersion;
				const maxVersion = childVersionList.reduce(
					(maxValue: string, current: any) => {
						return semver.gt(
							semver.valid(current) || '0.0.0',
							semver.valid(maxValue) || '0.0.0'
						)
							? current.value
							: maxValue;
					},
					childVersionList[0]
				);
				setVersion(maxVersion);
				onChange && onChange(maxVersion);
				setVersionList(
					childVersionList.map((item: string) => {
						return { value: item, label: item };
					})
				);
			}
		}
	}, [value, versionFatherList]);
	useEffect(() => {
		if (chartVersion) {
			getMiddlewareVersions({
				type,
				chartVersion
			}).then((res) => {
				if (res.success) {
					if (res.data) {
						setVersionOriginData(res.data);
						const fatherList = res.data.map((item: any) => {
							return {
								value: item.masterVersion,
								label: item.masterVersion
							};
						});
						setVersionFatherList(fatherList);
					} else {
						notification.error({
							message: '错误',
							description: '没有获取到当前中间件版本'
						});
					}
				}
			});
		}
	}, [chartVersion]);
	if (chartVersion) {
		return (
			<>
				<SelectBlock
					options={versionFatherList}
					currentValue={fatherVersion}
					onCallBack={(value: any) => {
						setFatherVersion(value);
						setVersionList(
							versionOriginData
								.find(
									(item: any) => item.masterVersion === value
								)
								.slaveVersion.map((item: string) => {
									return { value: item, label: item };
								})
						);
						setVersion(
							versionOriginData.find(
								(item: any) => item.masterVersion === value
							)?.slaveVersion[0]
						);
						onChange &&
							onChange(
								versionOriginData.find(
									(item: any) => item.masterVersion === value
								)?.slaveVersion[0]
							);
					}}
					disabled={
						!!judgeBackup || (judgeDisaster && type === 'mysql')
					}
				/>
				<div className="mt-16"></div>
				<SelectBlock
					options={versionList}
					currentValue={value || version}
					onCallBack={(value: any) => {
						setVersion(value);
						onChange && onChange(value);
					}}
					disabled={
						!!judgeBackup || (judgeDisaster && type === 'mysql')
					}
				/>
			</>
		);
	} else {
		return <Text type="danger">请先选择集群后获取版本信息！</Text>;
	}
}

import React from 'react';
import { Form, Input, Select } from 'antd';

const { Option } = Select;

const AccessConsoleAddress = (props: {
	required?: boolean;
	labelHidden?: boolean;
}) => {
	const { required, labelHidden } = props;

	return (
		<Form.Item
			colon={false}
			label={labelHidden ? undefined : '服务控制台地址'}
			name="consoleAddress"
			style={{ marginBottom: 0 }}
		>
			<Input.Group compact>
				<Form.Item
					name={['consoleAddress', 'protocol']}
					rules={[
						{
							required,
							message: '请选择协议'
						}
					]}
					style={{
						width: '30%'
					}}
				>
					<Select
						placeholder="请选择协议"
						style={{
							width: '100%'
						}}
					>
						<Option value="http">http</Option>
						<Option value="https">https</Option>
					</Select>
				</Form.Item>
				<Form.Item
					name={['consoleAddress', 'address']}
					rules={[
						{
							required,
							message: '请输入地址'
						}
					]}
					style={{
						width: '40%'
					}}
				>
					<Input
						placeholder="请输入地址"
						style={{
							width: '100%',
							borderRight: 0
						}}
					/>
				</Form.Item>

				<Form.Item
					name={['consoleAddress', 'port']}
					rules={[
						{
							required,
							message: '请输入端口号'
						}
					]}
					style={{
						width: '30%'
					}}
				>
					<Input
						placeholder="请输入端口号"
						style={{
							width: '100%'
						}}
					/>
				</Form.Item>
			</Input.Group>
		</Form.Item>
	);
};

export default AccessConsoleAddress;

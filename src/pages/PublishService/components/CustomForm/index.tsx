import React, { useEffect, useState } from 'react';
import { Form, notification } from 'antd';
import { getAspectFrom } from '@/services/common';
import { childrenRender } from '@/utils/utils';

export default function CustomForm({
	clusterId,
	namespace,
	customForm
}: {
	clusterId: string;
	namespace?: string;
	customForm: any;
}): JSX.Element {
	const form = Form.useFormInstance();
	return <>{childrenRender(customForm, form, clusterId, namespace)}</>;
}

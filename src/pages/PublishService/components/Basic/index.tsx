import React, { useEffect, useState } from 'react';
import { Form } from 'antd';
import MiddlewareName from '../MiddlewareName';
import MiddlewareAliasName from '../MiddlewareAliasName';
import Description from '../Description';
import MirrorImage from '../MirrorImage';
import Version from '../Version';
import InstanceQuota from '../InstanceQuota';
import StorageQuota from '../StorageQuota';
import StockAssessment from '../StockAssessment';
import { NamespaceItem } from '@/pages/ProjectDetail/projectDetail';
import Mode from '../Mode';
import OrganAndProject from '../OrganAndProject';
import ClusterAndNamespace from '../ClusterAndNamespace';

interface BasicInformationProps {
	organId: string;
	projectId: string;
	type: string;
	organizationName: string;
	projectName: string;
	returnCurNamespace: (value: NamespaceItem) => void;
	setCurrentMode?: (value: string) => void;
	judgeBackup?: boolean;
	judgeDisaster?: boolean;
}
export default function BasicInformation(
	props: BasicInformationProps
): JSX.Element {
	const {
		type,
		projectId,
		organId,
		returnCurNamespace,
		organizationName,
		projectName,
		setCurrentMode,
		judgeBackup,
		judgeDisaster
	} = props;
	const [currentNamespace, setCurrentNamespace] = useState<NamespaceItem>();
	const formMode = Form.useWatch('mode');
	const enabled = Form.useWatch(['readWriteProxy', 'enabled']);
	useEffect(() => {
		if (formMode) {
			setCurrentMode && setCurrentMode(formMode);
		}
	}, [formMode]);
	useEffect(() => {
		if (currentNamespace) {
			returnCurNamespace(currentNamespace);
		}
	}, [currentNamespace]);
	const getNumber = () => {
		if (type === 'kafka') return Form.useWatch('num');
		if (type === 'zookeeper') return Form.useWatch('num');
		if (type === 'rocketmq') {
			return Form.useWatch('group') * Form.useWatch('replicas');
		}
		if (type === 'redis') return Form.useWatch('num');
		if (type === 'postgresql') {
			return Form.useWatch('num') + 1;
		}
		if (type === 'mysql') {
			return Form.useWatch('num') + 1;
		}
		return 0;
	};
	const instanceQuotaRender = () => {
		switch (type) {
			case 'elasticsearch':
				return <></>;
			case 'redis':
				if (formMode === 'cluster') {
					return (
						<>
							<InstanceQuota type={type} />
							<StorageQuota />
						</>
					);
				} else {
					return <></>;
				}
			case 'mysql':
				if (enabled) {
					return <></>;
				} else {
					return (
						<>
							<InstanceQuota type={type} />
							<StorageQuota defaultStorage={5} />
						</>
					);
				}
			default:
				return (
					<>
						<InstanceQuota type={type} />
						<StorageQuota />
					</>
				);
		}
	};
	return (
		<>
			<OrganAndProject
				organId={organId}
				projectId={projectId}
				organName={organizationName}
				projectName={projectName}
			/>
			<ClusterAndNamespace
				setCurrentNamespace={setCurrentNamespace}
				judgeBackup={judgeBackup}
				judgeDisaster={judgeDisaster}
				type={type}
			/>
			<MiddlewareName type={type} />
			<MiddlewareAliasName />
			<Description />
			<MirrorImage />
			<Version
				type={type}
				judgeBackup={judgeBackup}
				judgeDisaster={judgeDisaster}
			/>
			<Mode
				type={type}
				judgeBackup={judgeBackup}
				judgeDisaster={judgeDisaster}
			/>
			{instanceQuotaRender()}
			<StockAssessment
				currentNamespace={currentNamespace}
				replicas={getNumber()}
				type={type}
			/>
		</>
	);
}

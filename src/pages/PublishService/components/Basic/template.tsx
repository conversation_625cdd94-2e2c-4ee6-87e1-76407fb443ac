import React, { useEffect, useState } from 'react';
import { Form } from 'antd';
import MiddlewareName from '../MiddlewareName';
import MiddlewareAliasName from '../MiddlewareAliasName';
import Description from '../Description';
import MirrorImage from '../MirrorImage';
import StockAssessment from '../StockAssessment';
import { NamespaceItem } from '@/pages/ProjectDetail/projectDetail';
import OrganAndProject from '../OrganAndProject';
import ClusterAndNamespace from '../ClusterAndNamespace';
import TemplateDeployNum from '../TemplateDeployNum';
import TemplateStorageSelect from '../TemplateStorageSelect';

export default function TemplateBasicInformation({
	type,
	organId,
	projectId,
	organizationName,
	projectName,
	template
}: {
	type: string;
	organId: string;
	projectId: string;
	organizationName: string;
	projectName: string;
	template?: ServiceTemplateItem;
}): JSX.Element {
	const form = Form.useFormInstance();
	const [currentNamespace, setCurrentNamespace] = useState<NamespaceItem>();
	useEffect(() => {
		if (template) {
			let mode_temp = template.mode;
			if (type === 'redis') {
				const isProxy = Object.keys(template.quota).includes('proxy');
				if (template.mode === 'cluster') {
					if (isProxy) {
						mode_temp = 'agent';
					} else {
						mode_temp = 'cluster';
					}
				} else {
					if (isProxy) {
						mode_temp = 'readWriteProxy';
					} else {
						mode_temp = 'sentinel';
					}
				}
			}
			// * 为了资源评估的计算，将cpu,memory,storageClassQuota 后端传过来的string 转成 number
			const quota_temp: any = {};
			Object.keys(template.quota).map((item) => {
				quota_temp[item] = template.quota[item];
				quota_temp[item].cpu = Number(template.quota[item].cpu);
				quota_temp[item].memory = Number(template.quota[item].memory);
				quota_temp[item].storageClassQuota = Number(
					template.quota[item].storageClassQuota
				);
				if (type === 'elasticsearch') {
					quota_temp[item].disabled =
						elasticsearchQuotaDisabled(item);
				}
			});
			if (type === 'elasticsearch') {
				form.setFieldsValue({
					quota: quota_temp,
					mode: mode_temp
				});
			} else {
				form.setFieldsValue({
					quota: quota_temp,
					cpu: Number(template.quota[type].cpu),
					memory: Number(template.quota[type].memory),
					num: template.quota[type].num,
					mode: mode_temp,
					storageClassQuota: Number(
						template.quota[type].storageClassQuota
					)
				});
			}
		}
	}, [template]);
	const elasticsearchQuotaDisabled = (item: string) => {
		switch (item) {
			case 'master':
				return false;
			case 'kibana':
				return false;
			case 'data':
				if (template?.mode === 'simple') {
					return true;
				} else {
					return false;
				}
			case 'client':
				if (template?.mode === 'complex-cold') {
					return true;
				} else {
					return false;
				}
			case 'cold':
				if (template?.mode === 'complex') {
					return true;
				} else {
					return false;
				}
			default:
				return true;
		}
	};
	const getNumber = () => {
		if (type === 'redis') return Form.useWatch('num');
		if (type === 'postgresql') {
			return Form.useWatch('num') + 1;
		}
		return 0;
	};
	return (
		<>
			<OrganAndProject
				organId={organId}
				projectId={projectId}
				organName={organizationName}
				projectName={projectName}
			/>
			<ClusterAndNamespace
				setCurrentNamespace={setCurrentNamespace}
				type={type}
				isTemplate={true}
			/>
			<MiddlewareName type={type} />
			<MiddlewareAliasName />
			<Description />
			<MirrorImage />
			<TemplateStorageSelect type={type} />
			<TemplateDeployNum />
			<StockAssessment
				currentNamespace={currentNamespace}
				replicas={getNumber()}
				type={type}
			/>
			<Form.Item noStyle name="cpu" />
			<Form.Item noStyle name="memory" />
			<Form.Item noStyle name="quota" />
			<Form.Item noStyle name="num" />
			<Form.Item noStyle name="mode" />
			<Form.Item noStyle name="storageClassQuota" />
		</>
	);
}

import React, { useEffect, useState } from 'react';
import { Form, Input, Tooltip } from 'antd';
import { CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons';
import pattern from '@/utils/pattern';

export default function MysqlPassword({
	judgeBackup
}: {
	judgeBackup?: boolean;
}): JSX.Element {
	const [checks, setChecks] = useState<boolean[]>([false, false]);
	const password = Form.useWatch('password');
	useEffect(() => {
		if (password) {
			const temp = [...checks];
			if (password.length >= 8 && password.length <= 32) {
				temp[0] = true;
			} else {
				temp[0] = false;
			}
			if (new RegExp(pattern.pwd).test(password)) {
				temp[1] = true;
			} else {
				temp[1] = false;
			}
			setChecks(temp);
		}
	}, [password]);
	return (
		<Tooltip
			title={
				<ul>
					<li className="edit-form-icon-style">
						{checks[0] ? (
							<CheckCircleFilled
								style={{
									color: '#68B642',
									marginRight: 4
								}}
							/>
						) : (
							<CloseCircleFilled
								style={{
									color: '#Ef595C',
									marginRight: 4
								}}
							/>
						)}
						<span>(长度需要8-32之间)</span>
					</li>
					<li className="edit-form-icon-style">
						{checks[1] ? (
							<CheckCircleFilled
								style={{
									color: '#68B642',
									marginRight: 4
								}}
							/>
						) : (
							<CloseCircleFilled
								style={{
									color: '#Ef595C',
									marginRight: 4
								}}
							/>
						)}
						<span>
							至少包含以下字符中的三种：大写字母、小写字母、数字和特殊字符～!@%^*-_=+?,()&
						</span>
					</li>
				</ul>
			}
		>
			<Form.Item
				name="password"
				label="root密码"
				rules={
					judgeBackup
						? [
								{
									required: true,
									message: '请输入root密码'
								}
						  ]
						: [
								{
									required: true,
									message: '请输入root密码'
								},
								{
									pattern: new RegExp(pattern.mysqlPwd),
									message: '密码不符合要求'
								}
						  ]
				}
			>
				<Input.Password
					placeholder="请输入root密码"
					disabled={!!judgeBackup}
				/>
			</Form.Item>
		</Tooltip>
	);
}

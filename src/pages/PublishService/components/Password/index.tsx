import React from 'react';
import EsPassword from './esPassword';
import RedisPassword from './redisPassword';
import PgPassword from './pgPassword';
import MysqlPassword from './mysqlPassword';

export default function Password({
	type,
	judgeBackup
}: {
	type: string;
	judgeBackup?: boolean;
}): JSX.Element {
	switch (type) {
		case 'elasticsearch':
			return <EsPassword judgeBackup={judgeBackup} />;
		case 'redis':
			return <RedisPassword judgeBackup={judgeBackup} />;
		case 'postgresql':
			return <PgPassword judgeBackup={judgeBackup} />;
		case 'mysql':
			return <MysqlPassword judgeBackup={judgeBackup} />;
		default:
			return <></>;
	}
}

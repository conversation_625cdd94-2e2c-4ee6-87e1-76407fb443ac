import React, { useEffect, useState } from 'react';
import { Form, Select } from 'antd';
import { getNamespaceStorages } from '@/services/storage';
import { StorageItem } from '@/pages/StorageManagement/storageManage';
import ElasticsearchMode from '../Mode/ElasticsearchMode/elasticsearchMode';

export default function TemplateStorageSelect({
	type
}: {
	type: string;
}): JSX.Element {
	const form = Form.useFormInstance();
	const clusterAndNamespace = Form.useWatch('clusterAndNamespace');
	const storageId = Form.useWatch('storageId');
	const [storages, setStorages] = useState<StorageItem[]>([]);
	useEffect(() => {
		if (clusterAndNamespace?.length > 0) {
			getNamespaceStorages({
				clusterId: clusterAndNamespace?.[0],
				namespace: clusterAndNamespace?.[1]
			}).then((res) => {
				if (res.success) {
					if (res.data.length > 0) {
						clusterAndNamespace?.[2]
							? setStorages(res.data)
							: setStorages(
									res.data.filter(
										(item: any) => !item.isActiveActive
									)
							  );
					} else {
						setStorages([]);
					}
				}
			});
		}
	}, [clusterAndNamespace]);
	useEffect(() => {
		if (storageId && storages.length > 0) {
			const storage_temp = storages.find(
				(item: StorageItem) => item.storageId === storageId
			);
			form.setFieldValue(
				'storageMax',
				(storage_temp?.quota?.request || 0) -
					(storage_temp?.quota?.used || 0)
			);
		}
	}, [storageId, storages]);
	if (type === 'elasticsearch') {
		return <ElasticsearchMode isTemplate={true} />;
	}
	return (
		<>
			<Form.Item
				name="storageId"
				label="存储选择"
				rules={[{ required: true, message: '存储选择不能为空' }]}
			>
				<Select
					placeholder="请选择存储"
					style={{
						width: 350
					}}
					dropdownMatchSelectWidth={false}
				>
					{storages.map((item: StorageItem) => {
						return (
							<Select.Option
								key={item.storageId}
								value={item.storageId}
							>
								{item.aliasName}
								{item.isActiveActive ? (
									<span className="available-domain">
										可用区
									</span>
								) : null}
							</Select.Option>
						);
					})}
				</Select>
			</Form.Item>
			<Form.Item noStyle name="storageMax" />
		</>
	);
}

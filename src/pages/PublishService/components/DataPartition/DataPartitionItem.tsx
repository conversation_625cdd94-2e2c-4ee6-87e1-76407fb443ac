import React, { useEffect, useState } from 'react';
import { Collapse, Form, Input, InputNumber, Select } from 'antd';
import '../../index.less';
import { formItemLayout618 } from '@/utils/const';
import { getNamespaceStorages } from '@/services/storage';
import { StorageItem } from '@/pages/StorageManagement/storageManage';

const { Panel } = Collapse;
const { Option } = Select;
enum DataPartitionTitle {
	master = '主节点',
	data = '数据节点',
	client = '协调节点',
	cold = '冷数据节点'
}
interface DataPartitionItemProps {
	nodeType: string;
	clusterId: string;
	namespace?: string;
	isActiveActive?: boolean;
}
export default function DataPartitionItem(
	props: DataPartitionItemProps
): JSX.Element {
	const { nodeType, clusterId, namespace, isActiveActive } = props;
	const [storageClassList, setStorageClassList] = useState<StorageItem[]>([]);
	useEffect(() => {
		if (clusterId && namespace) {
			getNamespaceStorages({
				clusterId,
				namespace
			}).then((res) => {
				if (res.success) {
					if (res.data.length > 0) {
						isActiveActive
							? setStorageClassList(
									res.data
										.filter(
											(item: any) =>
												item.storageClassList[0]
													.volumeType === 'LocalPath'
										)
										.map((item: any) => {
											item.name =
												item.storageClassList[0].name;
											return item;
										})
							  )
							: setStorageClassList(
									res.data
										.filter(
											(item: any) => !item.isActiveActive
										)
										.filter(
											(item: any) =>
												item.storageClassList[0]
													.volumeType === 'LocalPath'
										)
										.map((item: any) => {
											item.name =
												item.storageClassList[0].name;
											return item;
										})
							  );
					} else {
						setStorageClassList([]);
					}
				}
			});
		}
	}, [clusterId, namespace]);
	return (
		<div id="data-partition-item-content">
			<div className="data-partition-item-title">
				{DataPartitionTitle[nodeType]}
			</div>
			<Collapse className="data-partition-custom-collapse">
				<Panel
					header={
						<>
							数据目录
							{!Form.useWatch(
								`${nodeType}Node-data_storageClass`
							) && (
								<span
									className="ml-8"
									style={{ color: '#d93026' }}
								>
									(未配置)
								</span>
							)}
						</>
					}
					key="1"
					className="data-partition-custom-panel"
				>
					<Form.Item
						label="存储"
						labelAlign="left"
						{...formItemLayout618}
						rules={[
							{
								required: true,
								message: '请选择存储'
							}
						]}
						name={`${nodeType}Node-data_storageClass`}
						help={
							Form.useWatch(
								`${nodeType}Node-data_storageClass`
							) && (
								<span style={{ color: '#d93026' }}>
									选择local-path类型存储时采用动态存储方式，实际存储大小取决于挂载磁盘容量
								</span>
							)
						}
					>
						<Select
							placeholder="请选择存储"
							dropdownMatchSelectWidth={false}
						>
							{storageClassList.map((item: StorageItem) => {
								return (
									<Option
										key={item.storageId}
										value={item.name}
									>
										<p>
											{item.aliasName || item.name}
											<span
												className="available-domain"
												style={{ color: '#52c41a' }}
											>
												{item.storageClassList[0]
													?.volumeType === 'LocalPath'
													? 'local-path'
													: item.storageClassList[0]
															?.volumeType}
											</span>
										</p>
									</Option>
								);
							})}
						</Select>
					</Form.Item>
					<Form.Item
						name={`${nodeType}Node-data_volumeSize`}
						label="存储大小"
						initialValue={1}
						labelAlign="left"
						{...formItemLayout618}
						rules={[
							{
								required: true,
								message: '请输入存储大小'
							}
						]}
					>
						<InputNumber addonAfter="GB" />
					</Form.Item>
					<Form.Item
						name={`${nodeType}Node-data_hostPath`}
						label="宿主机目录"
						initialValue={
							nodeType === 'master'
								? '/es/data'
								: `/es/${nodeType}node/data`
						}
						labelAlign="left"
						{...formItemLayout618}
						rules={[
							{
								required: true,
								message: '请输入宿主机目录'
							}
						]}
					>
						<Input
							style={{ width: '100%' }}
							placeholder="请输入/开头的目录地址"
						/>
					</Form.Item>
					<Form.Item
						name={`${nodeType}Node-data_mountPath`}
						label="容器内目录"
						initialValue={
							nodeType === 'master'
								? '/es/data'
								: `/es/${nodeType}node_data`
						}
						labelAlign="left"
						{...formItemLayout618}
						rules={[
							{
								required: true,
								message: '请输入容器内目录'
							}
						]}
					>
						<Input
							style={{ width: '100%' }}
							placeholder="请输入/开头的目录地址"
						/>
					</Form.Item>
				</Panel>
				<Panel
					header={
						<>
							日志目录
							{!Form.useWatch(
								`${nodeType}Node-logs_storageClass`
							) && (
								<span
									className="ml-8"
									style={{ color: '#d93026' }}
								>
									(未配置)
								</span>
							)}
						</>
					}
					key="2"
					className="data-partition-custom-panel"
				>
					<Form.Item
						label="存储"
						labelAlign="left"
						{...formItemLayout618}
						rules={[
							{
								required: true,
								message: '请选择存储'
							}
						]}
						name={`${nodeType}Node-logs_storageClass`}
						help={
							Form.useWatch(
								`${nodeType}Node-logs_storageClass`
							) && (
								<span style={{ color: '#d93026' }}>
									选择local-path类型存储时采用动态存储方式，实际存储大小取决于挂载磁盘容量
								</span>
							)
						}
					>
						<Select
							placeholder="请选择存储"
							dropdownMatchSelectWidth={false}
						>
							{storageClassList.map((item: StorageItem) => {
								return (
									<Option
										key={item.storageId}
										value={item.name}
									>
										<p>
											{item.aliasName || item.name}
											<span
												className="available-domain"
												style={{ color: '#52c41a' }}
											>
												{item.storageClassList[0]
													?.volumeType === 'LocalPath'
													? 'local-path'
													: item.storageClassList[0]
															?.volumeType}
											</span>
										</p>
									</Option>
								);
							})}
						</Select>
					</Form.Item>
					<Form.Item
						name={`${nodeType}Node-logs_volumeSize`}
						label="存储大小"
						initialValue={1}
						labelAlign="left"
						{...formItemLayout618}
						rules={[
							{
								required: true,
								message: '请输入存储大小'
							}
						]}
					>
						<InputNumber addonAfter="GB" />
					</Form.Item>
					<Form.Item
						name={`${nodeType}Node-logs_hostPath`}
						label="宿主机目录"
						initialValue={
							nodeType === 'master'
								? '/es/logs'
								: `/es/${nodeType}node/logs`
						}
						labelAlign="left"
						{...formItemLayout618}
						rules={[
							{
								required: true,
								message: '请输入宿主机目录'
							}
						]}
					>
						<Input
							style={{ width: '100%' }}
							placeholder="请输入/开头的目录地址"
						/>
					</Form.Item>
					<Form.Item
						name={`${nodeType}Node-logs_mountPath`}
						label="容器内目录"
						initialValue={
							nodeType === 'master'
								? '/es/logs'
								: `/es/${nodeType}node_logs`
						}
						labelAlign="left"
						{...formItemLayout618}
						rules={[
							{
								required: true,
								message: '请输入容器内目录'
							}
						]}
					>
						<Input
							style={{ width: '100%' }}
							placeholder="请输入/开头的目录地址"
						/>
					</Form.Item>
				</Panel>
			</Collapse>
		</div>
	);
}

import React, { useState } from 'react';
import ESDataPartition from './esDataPartition';
import RedisDataPartition from './redisDataPartition';
import PgsqlDataPartition from './pgsqlDataPartitioin';
import storage from '@/utils/storage';

export default function DataPartition({
	type,
	mode,
	clusterId,
	namespace,
	judgeBackup,
	isActiveActive
}: {
	type: string;
	mode: string;
	clusterId: string;
	namespace?: string;
	judgeBackup?: boolean;
	isActiveActive?: boolean;
}): JSX.Element {
	// * feature 目录分盘是否打开
	const [dataPartitionAPI] = useState<boolean>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'dataPartition')?.enabled ??
			true
	);
	if (isActiveActive) {
		return <></>;
	}
	if (dataPartitionAPI) {
		switch (type) {
			case 'elasticsearch':
				return (
					<ESDataPartition
						mode={mode}
						clusterId={clusterId}
						namespace={namespace}
						judgeBackup={judgeBackup}
					/>
				);
			case 'redis':
				return (
					<RedisDataPartition
						mode={mode}
						clusterId={clusterId}
						namespace={namespace}
						judgeBackup={judgeBackup}
					/>
				);
			case 'postgresql':
				return (
					<PgsqlDataPartition
						mode={mode}
						clusterId={clusterId}
						namespace={namespace}
						judgeBackup={judgeBackup}
					/>
				);
			default:
				return <></>;
		}
	} else {
		return <></>;
	}
}

import React, { useEffect, useState } from 'react';
import { Form, Switch, notification } from 'antd';
import DataPartitionItem from './DataPartitionItem';
import '../../index.less';
import {
	filterObject,
	hasNullOrUndefinedValue,
	isOdd,
	transformObject
} from '@/utils/utils';
export default function ESDataPartition({
	mode,
	clusterId,
	namespace,
	judgeBackup
}: {
	mode: string;
	clusterId: string;
	namespace?: string;
	judgeBackup?: boolean;
}): JSX.Element {
	const dataPartition = Form.useWatch('dataPartition');
	const dataPartitionDisabled = Form.useWatch('dataPartitionDisabled');
	const form = Form.useFormInstance();
	const [data, setData] = useState<any>();
	useEffect(() => {
		switch (mode) {
			case 'simple':
				setData({ master: {} });
				return;
			case 'regular':
				setData({ master: {}, data: {} });
				return;
			case 'complex':
				setData({ master: {}, data: {}, client: {} });
				return;
			case 'complex-cold':
				setData({ master: {}, data: {}, cold: {} });
				return;
			case 'cold-complex':
				setData({ master: {}, data: {}, client: {}, cold: {} });
				return;
			default:
				setData({ master: {} });
				return;
		}
	}, [mode]);
	const checkCustomVolumes = (_: any, value: any) => {
		const customTemp = filterObject(transformObject(form.getFieldsValue()));
		const allFieldsValue = form.getFieldsValue();
		if (allFieldsValue.dataPartition) {
			if (JSON.stringify(customTemp) === '{}') {
				notification.warning({
					message: '提示',
					description: '当前数据分盘未配置！'
				});
				return Promise.reject(new Error(''));
			}
			if (hasNullOrUndefinedValue(customTemp)) {
				notification.warning({
					message: '提示',
					description: '当前数据分盘存在未配置的信息！'
				});
				return Promise.reject(new Error(''));
			}
			if (isOdd(Object.keys(customTemp).length)) {
				notification.warning({
					message: '提示',
					description: '当前数据分盘存在未配置的信息！'
				});
				return Promise.reject(new Error(''));
			}
			if (
				Object.keys(data).length * 2 !==
				Object.keys(customTemp).length
			) {
				notification.warning({
					message: '提示',
					description: '当前数据分盘存在未配置的信息！'
				});
				return Promise.reject(new Error(''));
			}
			return Promise.resolve();
		} else {
			return Promise.resolve();
		}
	};
	return (
		<>
			<Form.Item
				name="dataPartition"
				label="数据分盘"
				valuePropName="checked"
				initialValue={false}
				tooltip="开启数据分盘后，需要重新为节点下的各目录选择存储，可能会导致原先资源评估结果改变"
			>
				<Switch disabled={!!judgeBackup && dataPartitionDisabled} />
			</Form.Item>
			{dataPartition && (
				<Form.Item
					name="customVolumes"
					label=" "
					validateTrigger={['onBlur', 'onSubmit']}
					rules={[
						{
							validator: checkCustomVolumes,
							validateTrigger: ['onSubmit']
						}
					]}
				>
					<div className="custom-volumes-content">
						{Object.keys(data).map((key: string) => {
							return (
								<DataPartitionItem
									key={key}
									nodeType={key}
									clusterId={clusterId}
									namespace={namespace}
								/>
							);
						})}
					</div>
				</Form.Item>
			)}
			<Form.Item noStyle name="dataPartitionDisabled" />
		</>
	);
}

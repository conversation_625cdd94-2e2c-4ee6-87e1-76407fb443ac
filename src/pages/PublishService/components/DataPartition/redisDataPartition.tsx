import React from 'react';
import { Form, Switch, notification } from 'antd';
import OtherDataPartitionItem from './otherDataPartitionItem';
import { hasNullOrUndefinedValue } from '@/utils/utils';

export default function RedisDataPartition({
	mode,
	clusterId,
	namespace,
	judgeBackup
}: {
	mode: string;
	clusterId: string;
	namespace?: string;
	judgeBackup?: boolean;
}): JSX.Element {
	const form = Form.useFormInstance();
	const dataPartition = Form.useWatch('dataPartition');
	const dataPartitionDisabled = Form.useWatch('dataPartitionDisabled');
	const checkCustomVolumes = (_: any, value: any) => {
		const allFieldsValue = form.getFieldsValue();
		const valueTemp = {};
		Object.keys(value).map((key: string) => {
			if (value[key].switch) {
				valueTemp[key] = value[key];
			}
		});
		if (allFieldsValue.dataPartition) {
			if (hasNullOrUndefinedValue(valueTemp)) {
				notification.warning({
					message: '提示',
					description: '当前数据分盘未配置！'
				});
				return Promise.reject(new Error(''));
			}
			return Promise.resolve();
		} else {
			return Promise.resolve();
		}
	};
	return (
		<>
			<Form.Item
				name="dataPartition"
				label="数据分盘"
				valuePropName="checked"
				initialValue={false}
				tooltip="开启数据分盘后，需要重新为节点下的各目录选择存储，可能会导致原先资源评估结果改变"
			>
				<Switch disabled={!!judgeBackup && dataPartitionDisabled} />
			</Form.Item>
			{dataPartition && (
				<Form.Item
					label=" "
					name="customVolumes"
					initialValue={{
						'redis-data': {
							title: '数据目录',
							hostPath: '/host/path',
							mountPath: '/redis/data',
							storageClass: null,
							volumeSize: 1,
							switch: true,
							targetContainers: ['redis-cluster']
						},
						'redis-logs': {
							title: '日志目录',
							hostPath: '/redis/logs',
							mountPath: '/redis/logs',
							storageClass: null,
							volumeSize: 1,
							switch: false,
							targetContainers: ['redis-cluster']
						}
					}}
					validateTrigger={['onBlur', 'onSubmit']}
					rules={[
						{
							validateTrigger: ['onSubmit'],
							validator: checkCustomVolumes
						}
					]}
				>
					<OtherDataPartitionItem
						mode={mode}
						clusterId={clusterId}
						namespace={namespace}
					/>
				</Form.Item>
			)}
			<Form.Item noStyle name="dataPartitionDisabled" />
		</>
	);
}

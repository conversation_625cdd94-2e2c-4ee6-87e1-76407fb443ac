import DirectoryItem from '@/components/DirectoryItem';
import React, { useState } from 'react';

export default function OtherDataPartitionItem({
	value,
	onChange,
	mode,
	clusterId,
	namespace
}: {
	value?: any;
	onChange?: (value: any) => void;
	mode: string;
	clusterId: string;
	namespace?: string;
}): JSX.Element {
	const [pathObj, setPathObj] = useState(value);
	return (
		<div className={`display-flex mode-content`}>
			{Object.keys(value).map((key) => (
				<DirectoryItem
					key={key}
					type={key}
					data={pathObj[key]}
					clusterId={clusterId}
					mode={mode}
					namespace={namespace}
					onChange={(values) => {
						setPathObj({
							...pathObj,
							[key]: values
						});
						onChange &&
							onChange({
								...pathObj,
								[key]: values
							});
					}}
				/>
			))}
		</div>
	);
}

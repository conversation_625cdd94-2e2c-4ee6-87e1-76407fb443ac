import { NamespaceItem } from '@/pages/ProjectDetail/projectDetail';
import GIDSet from '@/pages/ServiceCatalog/components/GIDSet';
import UIDSet from '@/pages/ServiceCatalog/components/UIDSet';
import { formItemLayout420, formItemLayout614 } from '@/utils/const';
import pattern from '@/utils/pattern';
import { Form, Input, Switch } from 'antd';
import React from 'react';

interface ContainerConfigProps {
	currentNamespace?: NamespaceItem;
	template?: boolean;
}
export default function ContainerConfig(
	props: ContainerConfigProps
): JSX.Element {
	const { currentNamespace, template } = props;
	const containerConfig = Form.useWatch('containerConfig');
	return (
		<>
			<Form.Item
				label="容器配置"
				name="containerConfig"
				valuePropName="checked"
				initialValue={false}
			>
				<Switch />
			</Form.Item>
			{containerConfig && (
				<Form.Item label=" " name="containerConfigContent">
					<Form.Item
						label="注解"
						name="annotations"
						rules={[
							{
								pattern: new RegExp(pattern.labels),
								message:
									'请输入key=value格式的注解，多个注解以英文逗号分隔'
							}
						]}
						labelAlign="left"
						colon={false}
						{...formItemLayout614}
					>
						<Input
							style={{ width: '390px' }}
							placeholder="请输入key=value格式的注解，多个注解以英文逗号分隔"
						/>
					</Form.Item>
					<Form.Item
						label="标签"
						name="labels"
						rules={[
							{
								pattern: new RegExp(pattern.labels),
								message:
									'请输入key=value格式的标签，多个标签以英文逗号分隔'
							}
						]}
						labelAlign="left"
						colon={false}
						{...formItemLayout614}
					>
						<Input
							style={{ width: '390px' }}
							placeholder="请输入key=value格式的标签，多个标签以英文逗号分隔"
						/>
					</Form.Item>
					{template ? (
						''
					) : (
						<>
							<UIDSet namespace={currentNamespace} />
							<GIDSet namespace={currentNamespace} />
						</>
					)}
				</Form.Item>
			)}
		</>
	);
}

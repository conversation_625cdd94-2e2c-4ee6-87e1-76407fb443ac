import React from 'react';
import { DatePicker, Form } from 'antd';
import moment from 'moment';
import storage from '@/utils/storage';

export default function RecoveryConfig({
	middlewareName,
	namespace
}: {
	middlewareName?: string;
	namespace?: string;
}): JSX.Element {
	// * 备份
	const backupDetail = storage.getLocal('backupDetail');
	const disabledDate = (current: any) => {
		// Can not select days before today and today
		return (
			current < moment(new Date(backupDetail?.startTime)) ||
			current >
				moment(
					new Date(new Date(backupDetail?.endTime).getTime() + 1000)
				)
		);
	};
	if (
		middlewareName &&
		namespace &&
		backupDetail.recoveryType === 'time' &&
		backupDetail.backupMode === 'period'
	) {
		return (
			<Form.Item
				rules={[
					{
						required: true,
						message: '请选择恢复的时间点'
					}
				]}
				name="restoreTime"
				label="选择恢复的时间点"
				help={`可恢复的时间范围: ${
					backupDetail
						? (backupDetail?.startTime || '--') +
						  '-' +
						  (backupDetail?.endTime || '--')
						: '--'
				}`}
			>
				<DatePicker
					showTime
					showNow={false}
					disabledDate={disabledDate}
				/>
			</Form.Item>
		);
	} else {
		return <></>;
	}
}

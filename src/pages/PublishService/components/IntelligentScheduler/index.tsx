import React, { useState } from 'react';
import storage from '@/utils/storage';
import { Form, Switch } from 'antd';
import { formItemLayout618 } from '@/utils/const';

// * 所有中间件都具备的智能调度开关，默认关闭
export default function IntelligentScheduler(): JSX.Element {
	// * 智能调度 feature 功能
	const [extendSchedulerAPI] = useState<boolean>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'extendScheduler')?.enabled ??
			true
	);
	if (extendSchedulerAPI) {
		return (
			<Form.Item
				labelAlign="left"
				colon={false}
				{...formItemLayout618}
				label="智能调度"
				name="scheduler"
				valuePropName="checked"
				tooltip="当某个节点的存储不够的时候，pod将不会往该节点上调度（仅限lvm存储可以使用）"
			>
				<Switch />
			</Form.Item>
		);
	} else {
		return <></>;
	}
}

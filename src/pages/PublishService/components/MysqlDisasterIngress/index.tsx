import React from 'react';
import { Form, InputNumber, Switch } from 'antd';
import { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { StoreState } from '@/types';
import { Select, Tag, Row, Col, notification } from 'antd';
import { IngressItemProps } from '@/pages/ResourcePoolManagement/resource.pool';
import { AvailablePortItem } from '@/pages/ServiceListDetail/detail';
import {
	getIngresses,
	getNodePort,
	getIngressTCPPort
} from '@/services/common';
import { getAvailablePorts } from '@/services/ingress';
import { useParams } from 'react-router';

function MysqlDisasterIngress(props: any): JSX.Element {
	const params: any = useParams();
	const { clusterId, namespace, middlewareName } = props;
	const form = Form.useFormInstance();
	const [masterExposeType, setMasterExposeType] = useState<string>('Ingress');
	const [slaveExposeType, setSlaveExposeType] = useState<string>('Ingress');
	const [masterIngresses, setMasterIngresses] = useState<IngressItemProps[]>(
		[]
	);
	const [slaveIngresses, setSlaveIngresses] = useState<IngressItemProps[]>(
		[]
	);
	const [exposePort, setExposePort] = useState<number | null>();
	const [masterNodePortArray, setMasterNodePortArray] = useState<string[]>(
		[]
	);
	const [slaveNodePortArray, setSlaveNodePortArray] = useState<string[]>([]);
	const [ingressPortArray, setIngressPortArray] = useState<string[]>([]);
	const [masterIngressClassName, setMasterIngressClassName] = useState<{
		value: string;
		type: string;
		traefikPortList: any[];
	}>({
		value: '',
		type: '',
		traefikPortList: []
	});
	const [slaveIngressClassName, setSlaveIngressClassName] = useState<{
		value: string;
		type: string;
		traefikPortList: any[];
	}>({
		value: '',
		type: '',
		traefikPortList: []
	});
	const [availablePorts, setAvailablePorts] = useState<AvailablePortItem[]>(
		[]
	);
	const [slaveAvailablePorts, setSlaveAvailablePorts] = useState<
		AvailablePortItem[]
	>([]);

	useEffect(() => {
		getNodePort().then((res) => {
			if (res.success) {
				setMasterNodePortArray(res.data.split('-'));
				setSlaveNodePortArray(res.data.split('-'));
			}
		});
		getIngressTCPPort().then((res) => {
			if (res.success) {
				setIngressPortArray(res.data.split('-'));
			}
		});
	}, []);

	useEffect(() => {
		setMasterExposeType(form.getFieldsValue().masterExposeType);
		setSlaveExposeType(form.getFieldsValue().slaveExposeType);
	}, [form.getFieldsValue()]);

	useEffect(() => {
		getIngresses({ clusterId: params.clusterId }).then((res) => {
			if (res.success) {
				setMasterIngresses(res.data);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
		getAvailablePorts({
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params.disasterOriginName
		}).then((res) => {
			if (res.success) {
				setAvailablePorts(res.data);
			}
		});
	}, [params.clusterId]);

	useEffect(() => {
		getIngresses({ clusterId }).then((res) => {
			if (res.success) {
				setSlaveIngresses(res.data);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
		getAvailablePorts({
			clusterId,
			namespace,
			middlewareName
		}).then((res) => {
			if (res.success) {
				setSlaveAvailablePorts(res.data);
			}
		});
	}, [clusterId]);

	const handleIngressChange = (value: string, isMaster: boolean) => {
		if (isMaster) {
			const cur = masterIngresses.find(
				(item) => item.ingressClassName === value
			);
			setMasterIngressClassName({
				value: value,
				type: cur?.type as string,
				traefikPortList: cur?.traefikPortList || []
			});

			const curPortItem = availablePorts.find(
				(item: AvailablePortItem) => item.ingressClassName === value
			);
			form.setFieldsValue({
				masterIngresType: cur?.type as string,
				masterExposePort: curPortItem?.portList[0]
			});
		} else {
			const cur = slaveIngresses.find(
				(item) => item.ingressClassName === value
			);
			setSlaveIngressClassName({
				value: value,
				type: cur?.type as string,
				traefikPortList: cur?.traefikPortList || []
			});
			const curPortItem = slaveAvailablePorts.find(
				(item: AvailablePortItem) => item.ingressClassName === value
			);
			form.setFieldsValue({
				slaveIngresType: cur?.type as string,
				slaveExposePort: curPortItem?.portList[0]
			});
		}
	};

	const onExposeTypeChange = (value: string, isMaster: boolean) => {
		if (isMaster) {
			setMasterExposeType(value);
			if (value === 'NodePort') {
				const curPortItem = availablePorts.find(
					(item: AvailablePortItem) => item.exposeType === 'NodePort'
				);
				form.setFieldsValue({
					masterExposePort: curPortItem?.portList[0]
				});
			} else {
				const curPortItem = availablePorts.find(
					(item: AvailablePortItem) => {
						if (
							item.ingressClassName ===
							masterIngressClassName.value
						) {
							return item;
						}
					}
				);
				form.setFieldsValue({
					masterExposePort: curPortItem?.portList[0]
				});
			}
		} else {
			setSlaveExposeType(value);
			if (value === 'NodePort') {
				const curPortItem = slaveAvailablePorts.find(
					(item: AvailablePortItem) => item.exposeType === 'NodePort'
				);
				form.setFieldsValue({
					slaveExposePort: curPortItem?.portList[0]
				});
			} else {
				const curPortItem = slaveAvailablePorts.find(
					(item: AvailablePortItem) => {
						if (
							item.ingressClassName ===
							slaveIngressClassName.value
						) {
							return item;
						}
					}
				);
				form.setFieldsValue({
					slaveExposePort: curPortItem?.portList[0]
				});
			}
		}
	};

	return (
		<>
			<Row>
				<Col span={6}>
					<span className="ne-required">主服务故障恢复端口</span>
				</Col>
				<Col span={18}>
					<Form.Item
						required
						label="暴露方式"
						name="masterExposeType"
						initialValue={masterExposeType}
					>
						<Select
							value={masterExposeType}
							onChange={(value) =>
								onExposeTypeChange(value, true)
							}
							placeholder="请选择服务暴露"
						>
							<Select.Option value="NodePort">
								NodePort
							</Select.Option>
							<Select.Option value="Ingress">
								Ingress
							</Select.Option>
						</Select>
					</Form.Item>
					{masterExposeType === 'Ingress' && (
						<Form.Item
							name="masterIngressClassName"
							required
							label="负载均衡选择"
							rules={[
								{
									required: true,
									message: '请选择负载均衡'
								}
							]}
						>
							<Select
								value={masterIngressClassName?.value}
								placeholder="请选择负载均衡"
								dropdownMatchSelectWidth={false}
								onChange={(value) =>
									handleIngressChange(value, true)
								}
							>
								{masterIngresses.map(
									(item: IngressItemProps) => {
										return (
											<Select.Option
												key={item.ingressClassName}
												value={item.ingressClassName}
											>
												<div className="flex-space-between">
													{item.ingressClassName}
													<Tag
														color={
															item.type ===
															'nginx'
																? 'cyan'
																: 'green'
														}
													>
														{item.type}
													</Tag>
												</div>
											</Select.Option>
										);
									}
								)}
							</Select>
						</Form.Item>
					)}
					<Form.Item
						label="对外端口配置"
						name="masterExposePort"
						rules={[
							{
								required: true,
								message: '请输入对外端口配置'
							}
							// {
							// 	validator(rule, value, callback) {
							// 		const masterPort =
							// 			form.getFieldValue('slaveExposePort');
							// 		if (value && value === masterPort) {
							// 			return Promise.reject(
							// 				new Error(
							// 					'对外配置端口和灾备服务重复'
							// 				)
							// 			);
							// 		} else {
							// 			return Promise.resolve();
							// 		}
							// 	}
							// }
						]}
					>
						<InputNumber
							value={exposePort}
							onChange={(value: number | null) =>
								setExposePort(value)
							}
							placeholder={`请输入规定范围以内的端口`}
							style={{ width: '100%' }}
						/>
					</Form.Item>
					{masterExposeType === 'Ingress' &&
						masterIngressClassName?.type !== 'traefik' && (
							<Row>
								<Col span={6}></Col>
								<Col span={18}>
									<div>
										当前负载均衡相关端口组为
										{ingressPortArray.join('-')}
										请在端口组范围内选择端口
									</div>
								</Col>
							</Row>
						)}
					{masterExposeType === 'Ingress' &&
						masterIngressClassName?.type === 'traefik' && (
							<Row>
								<Col span={6}></Col>
								<Col span={18}>
									<div>
										当前负载均衡相关端口组为
										{masterIngressClassName.traefikPortList
											.map(
												(item) =>
													`${item.startPort}-${item.endPort}`
											)
											.join(',')}
										,请在端口组范围内选择端口
									</div>
								</Col>
							</Row>
						)}
					{masterExposeType === 'NodePort' && (
						<Row>
							<Col span={6}></Col>
							<Col span={18}>
								<div>
									当前端口组为
									{masterNodePortArray.join('-')}
									,请在端口组范围内选择端口
								</div>
							</Col>
						</Row>
					)}
				</Col>
			</Row>
			<Row style={{ marginTop: 24 }}>
				<Col span={6}>
					<span className="ne-required">灾备服务恢复端口</span>
				</Col>
				<Col span={18}>
					<Form.Item
						required
						label="暴露方式"
						name="slaveExposeType"
						initialValue={slaveExposeType}
					>
						<Select
							value={slaveExposeType}
							onChange={(value) =>
								onExposeTypeChange(value, false)
							}
							placeholder="请选择服务暴露"
						>
							<Select.Option value="NodePort">
								NodePort
							</Select.Option>
							<Select.Option value="Ingress">
								Ingress
							</Select.Option>
						</Select>
					</Form.Item>
					{slaveExposeType === 'Ingress' && (
						<Form.Item
							name="slaveIngressClassName"
							required
							label="负载均衡选择"
							rules={[
								{
									required: true,
									message: '请选择负载均衡'
								}
							]}
						>
							<Select
								value={slaveIngressClassName?.value}
								placeholder="请选择负载均衡"
								dropdownMatchSelectWidth={false}
								onChange={(value) =>
									handleIngressChange(value, false)
								}
							>
								{slaveIngresses.map(
									(item: IngressItemProps) => {
										return (
											<Select.Option
												key={item.ingressClassName}
												value={item.ingressClassName}
											>
												<div className="flex-space-between">
													{item.ingressClassName}
													<Tag
														color={
															item.type ===
															'nginx'
																? 'cyan'
																: 'green'
														}
													>
														{item.type}
													</Tag>
												</div>
											</Select.Option>
										);
									}
								)}
							</Select>
						</Form.Item>
					)}
					<Form.Item
						label="对外端口配置"
						name="slaveExposePort"
						rules={[
							{
								required: true,
								message: '请输入对外端口配置'
							}
							// {
							// 	validator(rule, value, callback) {
							// 		const masterPort =
							// 			form.getFieldValue('masterExposePort');
							// 		if (value && value === masterPort) {
							// 			return Promise.reject(
							// 				new Error(
							// 					'对外配置端口和源服务重复'
							// 				)
							// 			);
							// 		} else {
							// 			return Promise.resolve();
							// 		}
							// 	}
							// }
						]}
					>
						<InputNumber
							value={exposePort}
							onChange={(value: number | null) =>
								setExposePort(value)
							}
							placeholder={`请输入规定范围以内的端口`}
							style={{ width: 250 }}
						/>
					</Form.Item>
					{slaveExposeType === 'Ingress' &&
						slaveIngressClassName?.type === 'traefik' && (
							<Row>
								<Col span={6}></Col>
								<Col span={18}>
									<div>
										当前负载均衡相关端口组为
										{slaveIngressClassName.traefikPortList
											.map(
												(item) =>
													`${item.startPort}-${item.endPort}`
											)
											.join(',')}
										,请在端口组范围内选择端口
									</div>
								</Col>
							</Row>
						)}
					{slaveExposeType === 'NodePort' && (
						<Row>
							<Col span={6}></Col>
							<Col span={18}>
								<div>
									当前端口组为
									{slaveNodePortArray.join('-')}
									,请在端口组范围内选择端口
								</div>
							</Col>
						</Row>
					)}
				</Col>
			</Row>
		</>
	);
}

// const mapStateToProps = (state: StoreState) => ({
// 	globalVar: state.globalVar
// });
// export default connect(mapStateToProps)(MysqlDisasterIngress);
export default MysqlDisasterIngress;

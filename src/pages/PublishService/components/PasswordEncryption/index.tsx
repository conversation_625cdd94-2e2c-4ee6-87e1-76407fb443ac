import React from 'react';
import { Form, Select } from 'antd';

export default function PasswordEncryption({
	judgeBackup
}: {
	judgeBackup?: boolean;
}): JSX.Element {
	return (
		<Form.Item
			label="密码认证方式"
			name="passwordEncryption"
			initialValue="md5"
		>
			<Select disabled={!!judgeBackup} placeholder="请选择认证方式">
				<Select.Option value="scram-sha-256">
					scram-sha-256
				</Select.Option>
				<Select.Option value="md5">md5</Select.Option>
			</Select>
		</Form.Item>
	);
}

import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, ProHeader, ProPage } from '@/components/ProPage';
import { Steps, Button, Divider, Space } from 'antd';
import '../../index.less';
interface DefaultPageProps {
	title: string;
	onBack: () => void;
	steps: any;
	goNext: (value: number) => Promise<boolean>;
	goPrev?: (value: number) => void;
}
export default function DefaultPage(props: DefaultPageProps): JSX.Element {
	const { title, onBack, steps, goNext, goPrev } = props;
	const [current, setCurrent] = useState<number>(0);
	const items = steps.map((item: any) => ({
		key: item.title,
		title: item.title
	}));
	const next = async () => {
		const res = await goNext(current + 1);
		if (res) {
			setCurrent(current + 1);
		}
	};
	const prev = () => {
		goPrev && goPrev(current - 1);
		setCurrent(current - 1);
	};
	return (
		<ProPage>
			<ProHeader title={title} onBack={onBack} />
			<ProContent>
				<Steps items={items} current={current} />
				<div className="default-page-content">
					{steps?.[current]?.content}
				</div>
				{current !== steps.length - 1 && <Divider />}
				<Space>
					{current !== steps.length - 1 && (
						<Button danger onClick={onBack}>
							取消
						</Button>
					)}
					{current > 0 && current < steps.length - 1 && (
						<Button onClick={prev}>上一步</Button>
					)}
					{current < steps.length - 1 && (
						<Button htmlType="submit" type="primary" onClick={next}>
							{current === steps.length - 2 ? '提交' : '下一步'}
						</Button>
					)}
				</Space>
			</ProContent>
		</ProPage>
	);
}

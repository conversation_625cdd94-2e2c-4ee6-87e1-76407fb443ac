import React, { useEffect, useState } from 'react';
import { Cascader, Form, notification } from 'antd';
import { useParams } from 'react-router';
import { DefaultOptionType } from 'antd/lib/cascader';
import { getClusters, getComponent } from '@/services/common';
import { operatorStatus } from '@/services/repository';
import { getProjectNamespace } from '@/services/project';
import { NamespaceItem } from '@/pages/ProjectDetail/projectDetail';
export default function AccessClusterAndNamespace(): JSX.Element {
	const form = Form.useFormInstance();
	const formName = Form.useWatch('name');
	const params: AccessParams = useParams();
	const formOrgAndPro = Form.useWatch('organAndProject');
	const [options, setOptions] = useState<Option[]>([]);
	const [value, setValue] = useState<string[]>([]);
	useEffect(() => {
		async function getData() {
			const result = await getComponent({
				clusterId: '*',
				componentName: 'middleware-controller'
			});
			const result2 = await operatorStatus({
				type: params.name
			});
			const res = await getClusters({
				organId: formOrgAndPro?.[0],
				projectId: formOrgAndPro?.[1]
			});
			if (!res.success) {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
				return;
			}
			const tl = res.data.map((item: any) => {
				return {
					value: item.id,
					label: item.nickname || item.name,
					isLeaf: false,
					disabled: disabledCluster(item, result.data, result2.data)
				};
			});
			const res2 = await getProjectNamespace({
				organId: formOrgAndPro?.[0],
				projectId: formOrgAndPro?.[1],
				clusterId: tl?.[0].value,
				withQuota: true
			});
			if (!res2.success) {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res2.errorMsg}</p>
							<p>{res2.errorDetail}</p>
						</>
					)
				});
				return;
			}
			const ntl = res2.data
				?.filter((item) => item.availableDomain !== true)
				?.map((item) => {
					return {
						value: item.name,
						label: item.aliasName || item.name,
						isLeaf: true
					};
				});
			tl.map((item: any) => {
				if (item.value === tl[0].value) {
					item.children = ntl || [];
				}
				return item;
			});
			setOptions(tl);
		}
		if (!formOrgAndPro) return;
		getData();
	}, [formOrgAndPro]);
	const disabledCluster = (
		item: any,
		componentInfo: any,
		operatorInfo: any
	) => {
		const cur = operatorInfo.find((i: any) => i.clusterId === item.id);
		if (cur.status === 1 || cur.status === 2 || cur.status === 4) {
			if (componentInfo[item.id]?.status === 3) {
				return false;
			} else {
				return true;
			}
		} else {
			return true;
		}
	};
	const onChange = (changedValue: any, selectedOptions: any) => {
		setValue(changedValue);
		formName && form.validateFields(['name']);
	};
	const displayRender = (
		labels: string[],
		selectedOptions?: DefaultOptionType[]
	) => {
		if (labels?.[2]) {
			if (typeof labels[1] === 'string') {
				return (
					<span>
						{labels[0]} / {labels[1]}{' '}
						<span className="available-domain">可用区</span>
					</span>
				);
			} else {
				return (
					<span>
						{labels[0]} / {(labels[1] as any).props?.children[0]}{' '}
						<span className="available-domain">可用区</span>
					</span>
				);
			}
		} else {
			return labels?.map((label, i) => {
				const option = selectedOptions?.[i];
				if (i === 0) {
					return <span key={option?.value}>{label} / </span>;
				}
				if (i === 1) {
					return <span key={option?.value}>{label}</span>;
				}
			});
		}
	};
	const loadData = (selectedOptions: DefaultOptionType[]) => {
		const targetOption = selectedOptions[selectedOptions.length - 1];
		targetOption.loading = true;
		return getProjectNamespace({
			organId: formOrgAndPro?.[0],
			projectId: formOrgAndPro?.[1],
			clusterId: targetOption?.value as string,
			withQuota: true
		}).then((res) => {
			if (res.success) {
				const cnl = res.data
					?.filter((item) => item.availableDomain !== true)
					?.map((item: NamespaceItem) => {
						return {
							value: item.name,
							label: item.aliasName || item.name,
							isLeaf: true
						};
					});
				targetOption.children = cnl;
				setOptions([...options]);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	return (
		<Form.Item
			label="集群/命名空间"
			name="clusterAndNamespace"
			rules={[{ required: true, message: '请选择集群/命名空间' }]}
		>
			<Cascader
				placeholder="请选择集群/命名空间"
				options={options}
				value={value}
				onChange={onChange}
				displayRender={displayRender}
				loadData={loadData}
				popupClassName="publish-cascader-popup-content"
			/>
		</Form.Item>
	);
}

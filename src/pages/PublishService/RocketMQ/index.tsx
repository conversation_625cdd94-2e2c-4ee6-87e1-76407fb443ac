import React, { useEffect, useRef, useState } from 'react';
import { Button, Form, notification } from 'antd';
import { useParams, useHistory } from 'react-router';
import { ResultStatusType } from 'antd/lib/result';
import { formItemLayout614 } from '@/utils/const';
import DefaultPage from '../components/DefaultPage';
import BasicInformation from '../components/Basic';
import FileLog from '../components/FileLog';
import StandardLog from '../components/StandardLog';
import { postMiddleware } from '@/services/middleware';
import { NamespaceItem } from '@/pages/ProjectDetail/projectDetail';
import SchedulingPolicy from '../components/SchedulingPolicy';
import Toleration from '../components/Toleration';
import Affinity from '../components/Affinity';
import ContainerConfig from '../components/ContainerConfig';
import ResultPage from '../components/ResultPage';
import TopicForm from '../components/TopicForm';
// import ACLForm from '../components/ACLForm';
import { getCustomFormKeys } from '@/utils/utils';
import { getAspectFrom } from '@/services/common';
import CustomForm from '../components/CustomForm';
import storage from '@/utils/storage';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';

function RocketMQPublish(): JSX.Element {
	const project = storage.getSession('project');
	const organization = storage.getSession('organization');
	const [form] = Form.useForm();
	const params: ParamsProps = useParams();
	const history = useHistory();
	const [currentNamespace, setCurrentNamespace] = useState<NamespaceItem>();
	const [clusterAndNamespace, setClusterAndNamespace] = useState<any>();
	const basicInfo = useRef<any>(null);
	const configData = useRef<any>(null);
	const highData = useRef<any>(null);
	const [loading, setLoading] = useState<boolean>(true);
	const [title, setTitle] = useState<string>('发布中');
	const [status, setStatus] = useState<ResultStatusType>('info');
	// * 动态表单
	const [customForm, setCustomForm] = useState<any>();
	useEffect(() => {
		getAspectFrom().then((res) => {
			if (res.success) {
				setCustomForm(res.data);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	}, []);
	const getCurrentNamespace = (value: NamespaceItem) => {
		setCurrentNamespace(value);
	};
	const judgeGoNext = (step: number) => {
		if (step === 1) {
			if (
				form
					.getFieldsError()
					.every((item) => item.errors.length === 0) &&
				form.getFieldValue('judgeStock')
			) {
				return true;
			} else {
				notification.warning({
					message: '提醒',
					description: '当前命名空间可分配的资源不足！'
				});
				return false;
			}
		} else if (step === 2 || step === 3) {
			if (
				form.getFieldsError().every((item) => item.errors.length === 0)
			) {
				return true;
			} else {
				return false;
			}
		}
	};
	const goNext = async (value: number) => {
		if (value === 1) {
			await form.validateFields().catch((error) => {
				form.scrollToField(error.errorFields[0].name[0], {
					block: 'center'
				});
				return Promise.reject();
			});
			basicInfo.current = form.getFieldsValue();
			setClusterAndNamespace(basicInfo.current.clusterAndNamespace);
			return judgeGoNext(value) ? true : false;
		} else if (value === 2) {
			await form.validateFields();
			configData.current = form.getFieldsValue();
			return judgeGoNext(value) ? true : false;
		} else if (value === 3) {
			await form.validateFields();
			highData.current = form.getFieldsValue();
			setTimeout(() => {
				handleSubmit(highData.current);
			}, 1000);
			return judgeGoNext(value) ? true : false;
		} else {
			return false;
		}
	};
	const getExtra = () => {
		if (loading || status === 'error') {
			return (
				<Button
					type="primary"
					onClick={() => {
						history.push({
							pathname: `/project/${params.type}/${params.name}/${params.aliasName}`
						});
					}}
				>
					返回列表
				</Button>
			);
		}
		if (status === 'success') {
			return [
				<Button
					key="list"
					type="primary"
					onClick={() => {
						history.push({
							pathname: `/project/${params.type}/${params.name}/${params.aliasName}`
						});
					}}
				>
					返回列表
				</Button>,
				<Button
					key="detail"
					onClick={() => {
						history.push({
							pathname: `/project/${params.type}/${params.name}/${params.aliasName}/container/basicInfo/${basicInfo?.current?.name}/${basicInfo?.current?.chartVersion}/${clusterAndNamespace[0]}/${clusterAndNamespace[1]}`
						});
					}}
				>
					查看详情
				</Button>
			];
		}
	};
	const steps = [
		{
			title: '基本信息',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout614}
				>
					<BasicInformation
						type={params.name}
						projectId={project.projectId}
						organId={organization.organId}
						returnCurNamespace={getCurrentNamespace}
						organizationName={organization.name}
						projectName={project.aliasName || project.name}
					/>
				</Form>
			)
		},
		{
			title: '基础配置',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout614}
				>
					{/* <ACLForm /> */}
					<TopicForm />
					<FileLog />
					<StandardLog />
				</Form>
			)
		},
		{
			title: '高级配置',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout614}
				>
					<SchedulingPolicy clusterAndNamespace={clusterAndNamespace}>
						<Affinity
							name="exporterNodeAffinity"
							label="监控采集节点亲和"
							isAnti={false}
							disabled={false}
							clusterId={clusterAndNamespace?.[0]}
						/>
						<Toleration
							name="exporterTolerations"
							label="监控采集污点容忍"
							clusterId={clusterAndNamespace?.[0]}
						/>
						<Affinity
							name="consoleNodeAffinity"
							label="控制台节点亲和"
							isAnti={false}
							disabled={false}
							clusterId={clusterAndNamespace?.[0]}
						/>
						<Toleration
							name="consoleTolerations"
							label="控制台污点容忍"
							clusterId={clusterAndNamespace?.[0]}
						/>
					</SchedulingPolicy>
					<ContainerConfig currentNamespace={currentNamespace} />
					<CustomForm
						clusterId={clusterAndNamespace?.[0]}
						namespace={currentNamespace?.name}
						customForm={customForm}
					/>
				</Form>
			)
		},
		{
			title: '发布结果',
			content: (
				<ResultPage
					loading={loading}
					title={title}
					status={status}
					extra={getExtra()}
				/>
			)
		}
	];
	const handleSubmit = async (highData: any) => {
		const sendData: RocketMQServiceSendData = {
			clusterId: clusterAndNamespace[0],
			deployMod: 'container',
			namespace: currentNamespace?.name || clusterAndNamespace[1],
			name: basicInfo?.current?.name,
			aliasName: basicInfo?.current?.aliasName,
			chartName: params.name,
			chartVersion: basicInfo?.current?.chartVersion,
			type: 'rocketmq',
			labels: highData?.labels,
			annotations: highData?.annotations,
			description: basicInfo?.current?.description,
			version: basicInfo?.current?.version,
			mode: basicInfo?.current?.mode,
			mirrorImageId: basicInfo?.current?.mirrorImageId,
			filelogEnabled: configData?.current?.filelogEnabled,
			stdoutEnabled: configData?.current?.stdoutEnabled,
			scheduler: highData?.scheduler,
			nodeAffinity: (highData?.nodeAffinity || []).concat(
				highData?.nodeAntiAffinity || []
			),
			tolerations: highData?.tolerations,
			rocketMQParam: {
				acl: {
					enable: false
					// enable: configData?.current.enable,
					// globalWhiteRemoteAddresses:
					// 	configData.current.globalWhiteRemoteAddresses,
					// rocketMQAccountList:
					// 	configData?.current?.rocketMQAccountList
				},
				autoCreateTopicEnable:
					configData?.current?.autoCreateTopicEnable,
				exporterNodeAffinity: highData.exporterNodeAffinity,
				exporterTolerations: highData.exporterTolerations,
				consoleNodeAffinity: highData.consoleNodeAffinity,
				consoleTolerations: highData.consoleTolerations,
				group: basicInfo?.current.group,
				replicas: basicInfo?.current.replicas
			},
			quota: {
				rocketmq: {
					cpu: basicInfo?.current?.cpu,
					memory: basicInfo?.current.memory,
					storageId: basicInfo?.current?.storageId,
					storageClassQuota: basicInfo?.current?.storageClassQuota
				}
			}
		};
		// * 动态表单相关
		if (customForm) {
			const dynamicValues: any = {};
			let keys: string[] = [];
			for (const i in customForm) {
				const list = getCustomFormKeys(customForm[i]);
				keys = [...list, ...keys];
			}
			keys.forEach((item) => {
				dynamicValues[item] = highData[item];
			});
			sendData.dynamicValues = dynamicValues;
		}
		await ExecuteOrderFuc();
		postMiddleware(sendData)
			.then((res) => {
				setLoading(false);
				if (res.success) {
					setTitle('发布成功');
					setStatus('success');
				} else {
					setTitle('发布失败');
					setStatus('error');
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.catch((errors) => {
				setLoading(false);
				setTitle('发布失败');
				setStatus('error');
			});
	};
	return (
		<DefaultPage
			title="发布RocketMQ服务"
			onBack={() => window.history.back()}
			steps={steps}
			goNext={goNext}
		/>
	);
}
export default RocketMQPublish;

import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, Col, Form, Row, notification } from 'antd';
import { useParams, useHistory } from 'react-router';
import { ResultStatusType } from 'antd/lib/result';
import { formItemLayout516, formItemLayout614 } from '@/utils/const';
import DefaultPage from '../components/DefaultPage';
import BasicInformation from '../components/Basic';
import { NamespaceItem } from '@/pages/ProjectDetail/projectDetail';
import {
	getMiddlewareDetail,
	postMiddleware,
	createMiddlewaresTemplate,
	postMiddlewareBatch
} from '@/services/middleware';
import FileLog from '../components/FileLog';
import StandardLog from '../components/StandardLog';
import SchedulingPolicy from '../components/SchedulingPolicy';
import ContainerConfig from '../components/ContainerConfig';
import ResultPage from '../components/ResultPage';
import CustomPort from '../components/CustomPort';
import HostNetwork from '../components/HostNetwork';
import Password from '../components/Password';
import DataPartition from '../components/DataPartition';
import AuditLog from '../components/AuditLog';
import { getAspectFrom } from '@/services/common';
import CustomForm from '../components/CustomForm';
import { getCustomFormKeys } from '@/utils/utils';
import transUnit from '@/utils/transUnit';
import { applyBackup } from '@/services/backup';
import storage from '@/utils/storage';
import moment from 'moment';
import RecoveryConfig from '../components/RecoveryConfig';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';
import PasswordEncryption from '../components/PasswordEncryption';
import TemplateNameModal from '../components/Common/templateName';
import { getProjectNamespace } from '@/services/project';
import TemplateSelect from '../components/TemplateSelect';
import TemplateBasicInformation from '../components/Basic/template';

function PostgreSQLPublish(): JSX.Element {
	const project = storage.getSession('project');
	const organization = storage.getSession('organization');
	const [form] = Form.useForm();
	const params: ParamsProps = useParams();
	const history = useHistory();
	const [currentNamespace, setCurrentNamespace] = useState<NamespaceItem>();
	const [currentPassword, setCurrentPassword] = useState<string>('');
	const [clusterAndNamespace, setClusterAndNamespace] = useState<any>();
	const [currentMode, setCurrentMode] = useState<string>('');
	const basicInfo = useRef<any>(null);
	const configData = useRef<any>(null);
	const highData = useRef<any>(null);
	const [loading, setLoading] = useState<boolean>(true);
	const [title, setTitle] = useState<string>('发布中');
	const [status, setStatus] = useState<ResultStatusType>('info');
	const [templateOpen, setTemplateOpen] = useState<boolean>(false);
	// * 备份
	const backupDetail = storage.getLocal('backupDetail');
	// * 判断是否是灾备发布
	const isDisaster = params.isDisaster === 'isDisaster';
	// * 判断是否通过模板进行批量发布
	const isTemplate = params.isTemplate === 'isTemplate';
	const [currentTemplate, setCurrentTemplate] =
		useState<ServiceTemplateItem>();
	// * 判断是否是克隆发布
	const [judgeBackup] = useState<boolean>(
		!!(params.namespace && params.middlewareName && !isDisaster) || false
	);
	// * 动态表单
	const [customForm, setCustomForm] = useState<any>();
	useEffect(() => {
		getAspectFrom().then((res) => {
			if (res.success) {
				setCustomForm(res.data);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	}, []);
	useEffect(() => {
		if (params.clusterId) {
			if (judgeBackup) {
				setForm();
			}
		}
	}, [params.clusterId]);
	const getCurrentNamespace = (value: NamespaceItem) => {
		setCurrentNamespace(value);
	};
	const judgeGoNext = (step: number) => {
		if (step === 1) {
			if (
				form
					.getFieldsError()
					.every((item) => item.errors.length === 0) &&
				form.getFieldValue('judgeStock')
			) {
				return true;
			} else {
				notification.warning({
					message: '提醒',
					description: '当前命名空间可分配的资源不足！'
				});
				return false;
			}
		} else if (step === 2 || step === 3) {
			if (
				form.getFieldsError().every((item) => item.errors.length === 0)
			) {
				return true;
			} else {
				return false;
			}
		}
	};
	const goNext = async (value: number) => {
		if (value === 1) {
			await form.validateFields().catch((error) => {
				form.scrollToField(error.errorFields[0].name[0], {
					block: 'center'
				});
				return Promise.reject();
			});
			basicInfo.current = form.getFieldsValue();
			setClusterAndNamespace(basicInfo.current.clusterAndNamespace);
			return judgeGoNext(value) ? true : false;
		} else if (value === 2) {
			await form.validateFields();
			configData.current = form.getFieldsValue();
			return judgeGoNext(value) ? true : false;
		} else if (value === 3) {
			await form.validateFields();
			highData.current = form.getFieldsValue();
			setCurrentPassword(configData.current.password);
			setTimeout(() => {
				handleSubmit(highData.current);
			}, 1000);
			return judgeGoNext(value) ? true : false;
		} else {
			return false;
		}
	};
	const getExtra = () => {
		if (loading || status === 'error') {
			return (
				<Button
					type="primary"
					onClick={() => {
						history.push({
							pathname: `/project/${params.type}/${params.name}/${params.aliasName}`
						});
					}}
				>
					返回列表
				</Button>
			);
		}
		if (status === 'success') {
			if (isDisaster) {
				return (
					<Button
						type="primary"
						onClick={() => {
							history.goBack();
						}}
					>
						返回
					</Button>
				);
			}
			return [
				<Button
					key="list"
					type="primary"
					onClick={() => {
						history.push({
							pathname: `/project/${params.type}/${params.name}/${params.aliasName}`
						});
					}}
				>
					返回列表
				</Button>,
				<Button
					key="detail"
					onClick={() => {
						history.push({
							pathname: `/project/${params.type}/${params.name}/${params.aliasName}/container/basicInfo/${basicInfo?.current?.name}/${basicInfo?.current?.chartVersion}/${clusterAndNamespace[0]}/${clusterAndNamespace[1]}`
						});
					}}
				>
					查看详情
				</Button>,
				<Button
					key="template"
					onClick={() => {
						setTemplateOpen(true);
					}}
				>
					保存为模板
				</Button>
			];
		}
	};
	const steps = [
		{
			title: '基本信息',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout516}
				>
					<BasicInformation
						type={params.name}
						projectId={project.projectId}
						organId={organization.organId}
						returnCurNamespace={getCurrentNamespace}
						organizationName={organization.name}
						projectName={project.aliasName || project.name}
						setCurrentMode={setCurrentMode}
						judgeBackup={judgeBackup}
						judgeDisaster={isDisaster}
					/>
				</Form>
			)
		},
		{
			title: '基础配置',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout516}
				>
					<Password type={params.name} judgeBackup={judgeBackup} />
					<PasswordEncryption judgeBackup={judgeBackup} />
					<FileLog />
					<StandardLog />
					<AuditLog />
				</Form>
			)
		},
		{
			title: '高级配置',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout516}
				>
					<DataPartition
						type={params.name}
						mode={currentMode}
						clusterId={clusterAndNamespace?.[0]}
						namespace={clusterAndNamespace?.[1]}
						isActiveActive={clusterAndNamespace?.[2]}
						judgeBackup={judgeBackup}
					/>
					<HostNetwork />
					<CustomPort type={params.name} />
					<SchedulingPolicy
						clusterAndNamespace={clusterAndNamespace}
					/>
					<ContainerConfig currentNamespace={currentNamespace} />
					<RecoveryConfig
						middlewareName={params.middlewareName}
						namespace={params.namespace}
					/>
					<CustomForm
						clusterId={clusterAndNamespace?.[0]}
						namespace={currentNamespace?.name}
						customForm={customForm}
					/>
				</Form>
			)
		},
		{
			title: '发布结果',
			content: (
				<div id="publish-result-page">
					<ResultPage
						loading={loading}
						title={title}
						status={status}
						extra={getExtra()}
					>
						<div className="publish-result-page-content">
							<Row>
								<Col span={8}>
									<div className="publish-result-page-label">
										postgres密码
									</div>
								</Col>
								<Col span={16}>
									<div
										className="publish-result-page-text"
										title={currentPassword}
									>
										{currentPassword}
									</div>
								</Col>
							</Row>
						</div>
					</ResultPage>
				</div>
			)
		}
	];
	// * 通过模版创建使用
	const templateSteps = [
		{
			title: '选择模板',
			content: (
				<Form form={form}>
					<TemplateSelect />
				</Form>
			)
		},
		{
			title: '基本信息',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout614}
				>
					<TemplateBasicInformation
						type={params.name}
						projectId={project.projectId}
						organId={organization.organId}
						organizationName={organization.name}
						projectName={project.aliasName || project.name}
						template={currentTemplate}
					/>
				</Form>
			)
		},
		{
			title: '高级配置',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout614}
				>
					<DataPartition
						type={params.name}
						mode={currentMode}
						clusterId={clusterAndNamespace?.[0]}
						namespace={clusterAndNamespace?.[1]}
						isActiveActive={clusterAndNamespace?.[2]}
						judgeBackup={judgeBackup}
					/>
					<HostNetwork />
					<CustomPort type={params.name} />
					<SchedulingPolicy
						clusterAndNamespace={clusterAndNamespace}
					/>
					<ContainerConfig currentNamespace={currentNamespace} />
					<RecoveryConfig
						middlewareName={params.middlewareName}
						namespace={params.namespace}
					/>
					<CustomForm
						clusterId={clusterAndNamespace?.[0]}
						namespace={currentNamespace?.name}
						customForm={customForm}
					/>
				</Form>
			)
		},
		{
			title: '发布结果',
			content: (
				<div id="publish-result-page">
					<ResultPage
						loading={loading}
						title={title}
						status={status}
						extra={getExtra()}
					/>
				</div>
			)
		}
	];
	const judgeGoNextForTemplate = (step: number) => {
		if (step === 1) {
			const values = form.getFieldsValue();
			if (!values.template) {
				notification.warning({
					message: '提示',
					description: '请选择模板'
				});
				return false;
			}
			setCurrentTemplate(values.template);
			return true;
		} else if (step === 2 || step === 3) {
			if (
				form.getFieldsError().every((item) => item.errors.length === 0)
			) {
				return true;
			} else {
				return false;
			}
		}
	};
	const goNextForTemplate = async (value: number) => {
		if (value === 1) {
			return judgeGoNextForTemplate(value) ? true : false;
		} else if (value === 2) {
			await form.validateFields();
			basicInfo.current = form.getFieldsValue();
			setClusterAndNamespace(basicInfo.current.clusterAndNamespace);
			return judgeGoNextForTemplate(value) ? true : false;
		} else if (value === 3) {
			await form.validateFields();
			highData.current = form.getFieldsValue();
			setTimeout(() => {
				handleBatchSubmit(highData.current);
			}, 1000);
			return judgeGoNextForTemplate(value) ? true : false;
		} else {
			return false;
		}
	};
	// * 使用模版进行批量创建
	const handleBatchSubmit = async (highData: any) => {
		const customVolumesTemp: any = {};
		// * 开启数据分盘
		if (highData.dataPartition) {
			for (const key in highData.customVolumes) {
				if (!highData.customVolumes[key].disabled) {
					if (highData.customVolumes[key].hostPath) {
						if (
							!highData.customVolumes[key].storageClass &&
							highData.customVolumes[key].switch
						) {
							return;
						}
					}
					if (highData.customVolumes[key].switch !== false) {
						customVolumesTemp[key] = highData.customVolumes[key];
					}
				}
			}
		}
		const sendData: any = {
			templageId: currentTemplate?.id, // ! templageId 拼写错误 待后端修改
			clusterId: clusterAndNamespace[0],
			namespace: clusterAndNamespace[1],
			deployMod: 'container',
			name: basicInfo?.current?.name,
			aliasName: basicInfo?.current?.aliasName,
			containerUID: highData?.containerUID,
			containerGID: highData?.containerGID,
			chartName: params.name,
			chartVersion: basicInfo?.current?.chartVersion,
			type: 'postgresql',
			labels: highData?.labels,
			annotations: highData?.annotations,
			description: basicInfo?.current?.description,
			mirrorImageId: basicInfo?.current?.mirrorImageId,
			scheduler: highData?.scheduler || false,
			nodeAffinity: (highData?.nodeAffinity || []).concat(
				highData?.nodeAntiAffinity || []
			),
			tolerations: highData?.tolerations,
			quota: {
				postgresql: {
					num: currentTemplate?.quota?.postgresql.num,
					cpu: currentTemplate?.quota?.postgresql.cpu,
					memory: currentTemplate?.quota.postgresql.memory,
					storageId: highData.dataPartition
						? undefined
						: basicInfo?.current?.storageId,
					storageClassQuota: highData.dataPartition
						? undefined
						: currentTemplate?.quota?.postgresql.storageClassQuota
				}
			},
			postgresqlParam: {
				hostNetwork: highData.hostNetwork || false,
				pgPort: highData.pgPort,
				apiPort: highData.apiPort,
				exporterPort: highData.exporterPort,
				bgMonPort: highData.bgMonPort
			},
			customVolumes: customVolumesTemp
		};
		// * 动态表单相关
		if (customForm) {
			const dynamicValues: any = {};
			let keys: string[] = [];
			for (const i in customForm) {
				const list = getCustomFormKeys(customForm[i]);
				keys = [...list, ...keys];
			}
			keys.forEach((item) => {
				dynamicValues[item] = highData[item];
			});
			sendData.dynamicValues = dynamicValues;
		}
		console.log(sendData);
		await ExecuteOrderFuc();
		postMiddlewareBatch(sendData)
			.then((res) => {
				setLoading(false);
				if (res.success) {
					setTitle('发布成功');
					setStatus('success');
				} else {
					setTitle('发布失败');
					setStatus('error');
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.catch((errors) => {
				setLoading(false);
				setTitle('发布失败');
				setStatus('error');
			});
	};
	// * 保存为模板
	const saveTemplateSubmit = async ({
		templateName
	}: {
		templateName: string;
	}) => {
		await form.validateFields();
		const sendData: any = {
			deployMod: 'container',
			projectId: project.projectId,
			organId: organization.organId,
			name: templateName,
			aliasName: basicInfo?.current?.aliasName,
			chartName: params.name,
			chartVersion: params.chartVersion,
			type: 'postgresql',
			description: basicInfo?.current?.description,
			version: basicInfo?.current?.version,
			mode: basicInfo?.current?.mode,
			filelogEnabled: configData?.current?.filelogEnabled,
			stdoutEnabled: configData?.current?.stdoutEnabled,
			audit: configData?.current.audit || false,
			password: configData.current.password,
			quota: {
				postgresql: {
					num:
						basicInfo.current?.mode === '1m-ns'
							? basicInfo?.current?.num
							: Number(basicInfo.current?.mode.charAt(3)),
					cpu: basicInfo?.current?.cpu,
					memory: basicInfo?.current.memory,
					storageId: basicInfo.current.storageId
				}
			},
			postgresqlParam: {
				passwordEncryption: configData.current.passwordEncryption
			}
		};
		createMiddlewaresTemplate(sendData)
			.then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '模板创建成功'
					});
					history.push(
						`/project/${params.type}/${params.name}/${params.aliasName}/templateManage`
					);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.catch((res) => {
				notification.error({
					message: '失败',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			});
	};
	const handleSubmit = async (highData: any) => {
		const customVolumesTemp: any = {};
		// * 开启数据分盘
		if (highData.dataPartition) {
			for (const key in highData.customVolumes) {
				if (!highData.customVolumes[key].disabled) {
					if (highData.customVolumes[key].hostPath) {
						if (
							!highData.customVolumes[key].storageClass &&
							highData.customVolumes[key].switch
						) {
							return;
						}
					}
					if (highData.customVolumes[key].switch !== false) {
						customVolumesTemp[key] = highData.customVolumes[key];
					}
				}
			}
		}
		let sendData: PostgreSQLServiceSendData = {
			clusterId: clusterAndNamespace[0],
			namespace: currentNamespace?.name || clusterAndNamespace[1],
			deployMod: 'container',
			name: basicInfo?.current?.name,
			aliasName: basicInfo?.current?.aliasName,
			containerUID: highData?.containerUID,
			containerGID: highData?.containerGID,
			chartName: params.name,
			chartVersion: basicInfo?.current?.chartVersion,
			type: 'postgresql',
			labels: highData?.labels,
			annotations: highData?.annotations,
			description: basicInfo?.current?.description,
			version: basicInfo?.current?.version,
			mode: basicInfo?.current?.mode,
			mirrorImageId: basicInfo?.current?.mirrorImageId,
			filelogEnabled: configData?.current?.filelogEnabled,
			stdoutEnabled: configData?.current?.stdoutEnabled,
			audit: configData?.current.audit || false,
			scheduler: highData?.scheduler || false,
			nodeAffinity: (highData?.nodeAffinity || []).concat(
				highData?.nodeAntiAffinity || []
			),
			tolerations: highData?.tolerations,
			password: configData.current.password,
			quota: {
				postgresql: {
					num:
						basicInfo.current?.mode === '1m-ns'
							? basicInfo?.current?.num
							: Number(basicInfo.current?.mode.charAt(3)),
					cpu: basicInfo?.current?.cpu,
					memory: basicInfo?.current.memory,
					storageId: highData.dataPartition
						? undefined
						: basicInfo?.current?.storageId,
					storageClassQuota: highData.dataPartition
						? undefined
						: basicInfo?.current?.storageClassQuota
				}
			},
			postgresqlParam: {
				hostNetwork: highData.hostNetwork || false,
				pgPort: highData.pgPort,
				apiPort: highData.apiPort,
				exporterPort: highData.exporterPort,
				bgMonPort: highData.bgMonPort,
				passwordEncryption: configData.current.passwordEncryption
			},
			customVolumes: customVolumesTemp
		};
		// * 动态表单相关
		if (customForm) {
			const dynamicValues: any = {};
			let keys: string[] = [];
			for (const i in customForm) {
				const list = getCustomFormKeys(customForm[i]);
				keys = [...list, ...keys];
			}
			keys.forEach((item) => {
				dynamicValues[item] = highData[item];
			});
			sendData.dynamicValues = dynamicValues;
		}
		await ExecuteOrderFuc();
		if (params.namespace && params.middlewareName && judgeBackup) {
			// * 为了解决克隆时，无法克隆参数列表中的数据
			sendData = {
				...sendData,
				isBackup: true,
				relationMiddleware: {
					clusterId: sendData.clusterId,
					namespace: params.namespace,
					name: params.middlewareName,
					type: backupDetail.sourceType
				}
			};
			const result = {
				clusterId: params.clusterId,
				namespace: params.namespace,
				middlewareName: basicInfo.current.name,
				type: backupDetail.sourceType,
				backupId: backupDetail.backupId,
				activeArea: backupDetail.activeArea,
				sourceName: backupDetail.sourceName,
				restoreTime:
					backupDetail.recoveryType === 'time'
						? moment(highData.restoreTime).format(
								'YYYY-MM-DD HH:mm:ss'
						  )
						: '',
				backupName:
					backupDetail.recoveryType === 'time'
						? backupDetail.backupIncName
						: backupDetail.backupRecordName ||
						  backupDetail.backupName
			};
			applyBackup(result);
		}
		postMiddleware(sendData)
			.then((res) => {
				setLoading(false);
				if (res.success) {
					setTitle('发布成功');
					setStatus('success');
				} else {
					setTitle('发布失败');
					setStatus('error');
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.catch((errors) => {
				setLoading(false);
				setTitle('发布失败');
				setStatus('error');
			});
	};
	const setForm = async () => {
		const res = await getMiddlewareDetail({
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params.middlewareName,
			type: 'postgresql'
		});
		if (!res.success) {
			notification.error({
				message: '错误',
				description: (
					<>
						<p>{res.errorMsg}</p>
						<p>{res.errorDetail}</p>
					</>
				)
			});
			return;
		}
		const res3 = await getProjectNamespace({
			organId: organization.organId,
			projectId: project.projectId,
			clusterId: params.clusterId,
			withQuota: true
		});
		// * 判断当前命名空间是否是双活命名空间
		const cur_namespace = res3.data.find(
			(item) => item.name === params.namespace
		);
		// * 判断调度策略开关是否开启
		let schedulingPolicyTemp = false;
		if (
			(res.data.nodeAffinity || []).length !== 0 ||
			!!res.data.scheduler ||
			(res.data.tolerations || []).length !== 0
		) {
			schedulingPolicyTemp = true;
		}
		const nodeAffinityTemp =
			res.data.nodeAffinity?.filter(
				(i: AffinityItem) => i.anti === false
			) || [];
		const antiNodeAffinityTemp =
			res.data.nodeAffinity?.filter(
				(i: AffinityItem) => i.anti === true
			) || [];
		// * 判断容器配置开关是否开启
		let containerConfigTemp = false;
		if (
			res.data.containerGID ||
			res.data.containerUID ||
			res.data.labels ||
			res.data.annotations
		) {
			containerConfigTemp = true;
		}
		form.setFieldsValue({
			clusterAndNamespace: [
				params.clusterId,
				params.namespace,
				cur_namespace?.availableDomain
			],
			name: params.middlewareName + '-backup',
			aliasName: res.data.aliasName,
			description: res.data.description,
			mirrorImageId: res.data.mirrorImageId
				? +res.data.mirrorImageId
				: null,
			version: res.data.version,
			chartVersion: params.chartVersion,
			mode: res.data.mode,
			num: res.data.quota.postgresql.num,
			instanceSpec: 'Customize',
			cpu: +res.data.quota.postgresql.cpu,
			memory: +transUnit.removeUnit(
				res.data.quota.postgresql.memory,
				'Gi'
			),
			storageId: res.data.customVolumes
				? null
				: res.data.quota.postgresql.storageId,
			password: res.data.password,
			stdoutEnabled: res.data.stdoutEnabled,
			filelogEnabled: res.data.filelogEnabled,
			slowSql: res.data.slowSql || false,
			audit: res.data.audit || false,
			customPort: true,
			apiPort: res.data.postgresqlParam.apiPort,
			bgMonPort: res.data.postgresqlParam.bgMonPort,
			exporterPort: res.data.postgresqlParam.exporterPort,
			hostNetwork: res.data.postgresqlParam.hostNetwork,
			pgPort: res.data.postgresqlParam.pgPort,
			passwordEncryption: res.data.postgresqlParam.passwordEncryption,
			schedulingPolicy: schedulingPolicyTemp,
			nodeAffinitySwitch: nodeAffinityTemp.length !== 0,
			nodeAffinity: nodeAffinityTemp,
			scheduler: res.data.scheduler || false,
			nodeAntiAffinitySwitch: antiNodeAffinityTemp.length !== 0,
			nodeAntiAffinity: antiNodeAffinityTemp,
			tolerationsSwitch: (res.data.tolerations || []).length !== 0,
			tolerations: res.data.tolerations,
			containerConfig: containerConfigTemp,
			containerGID: res.data.containerGID,
			containerUID: res.data.containerUID,
			labels: res.data.labels,
			annotations: res.data.annotations,
			dataPartition: res.data.customVolumes ? true : false,
			dataPartitionDisabled: res.data.customVolumes ? false : true
		});
	};
	return (
		<>
			<DefaultPage
				title="发布PostgreSQL服务"
				onBack={() => window.history.back()}
				steps={isTemplate ? templateSteps : steps}
				goNext={isTemplate ? goNextForTemplate : goNext}
			/>
			<TemplateNameModal
				open={templateOpen}
				onCancel={() => setTemplateOpen(false)}
				onCreate={saveTemplateSubmit}
			/>
		</>
	);
}
export default PostgreSQLPublish;

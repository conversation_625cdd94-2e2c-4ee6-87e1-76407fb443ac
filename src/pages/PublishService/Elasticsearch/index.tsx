import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, Col, Form, Row, notification } from 'antd';
import { useParams, useHistory } from 'react-router';
import { ResultStatusType } from 'antd/lib/result';
import DefaultPage from '../components/DefaultPage';
import {
	getMiddlewareDetail,
	postMiddleware,
	createMiddlewaresTemplate,
	postMiddlewareBatch
} from '@/services/middleware';
import ResultPage from '../components/ResultPage';
import SchedulingPolicy from '../components/SchedulingPolicy';
import { formItemLayout516, formItemLayout519 } from '@/utils/const';
import Toleration from '../components/Toleration';
import Affinity from '../components/Affinity';
import ContainerConfig from '../components/ContainerConfig';
import StandardLog from '../components/StandardLog';
import FileLog from '../components/FileLog';
import BasicInformation from '../components/Basic';
import { NamespaceItem } from '@/pages/ProjectDetail/projectDetail';
import Password from '../components/Password';
import HostNetwork from '../components/HostNetwork';
import CustomPort from '../components/CustomPort';
import DataPartition from '../components/DataPartition';
import {
	filterObject,
	getCustomFormKeys,
	transformObject
} from '@/utils/utils';
import { getAspectFrom } from '@/services/common';
import CustomForm from '../components/CustomForm';
import storage from '@/utils/storage';
import transUnit from '@/utils/transUnit';
import { getNamespaceStorages } from '@/services/storage';
import { applyBackup } from '@/services/backup';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';
import TemplateNameModal from '../components/Common/templateName';
import TemplateSelect from '../components/TemplateSelect';
import TemplateBasicInformation from '../components/Basic/template';

function ElasticsearchPublish(): JSX.Element {
	const project = storage.getSession('project');
	const organization = storage.getSession('organization');
	const [form] = Form.useForm();
	const params: ParamsProps = useParams();
	const history = useHistory();
	const [currentNamespace, setCurrentNamespace] = useState<NamespaceItem>();
	const [currentPassword, setCurrentPassword] = useState<string>('');
	const [currentMode, setCurrentMode] = useState<string>('');
	const [clusterAndNamespace, setClusterAndNamespace] = useState<any>();
	const basicInfo = useRef<any>(null);
	const configData = useRef<any>(null);
	const highData = useRef<any>(null);
	const [loading, setLoading] = useState<boolean>(true);
	const [title, setTitle] = useState<string>('发布中');
	const [status, setStatus] = useState<ResultStatusType>('info');
	const [templateOpen, setTemplateOpen] = useState<boolean>(false);
	// * 备份
	const backupDetail = storage.getLocal('backupDetail');
	// * 判断是否是灾备发布
	const isDisaster = params.isDisaster === 'isDisaster';
	// * 判断是否通过模板进行批量发布
	const isTemplate = params.isTemplate === 'isTemplate';
	const [currentTemplate, setCurrentTemplate] =
		useState<ServiceTemplateItem>();
	// * 判断是否是克隆发布
	const [judgeBackup] = useState<boolean>(
		!!(params.namespace && params.middlewareName && !isDisaster) || false
	);
	// * 动态表单
	const [customForm, setCustomForm] = useState<any>();
	useEffect(() => {
		getAspectFrom().then((res) => {
			if (res.success) {
				setCustomForm(res.data);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	}, []);
	useEffect(() => {
		if (params.clusterId) {
			if (judgeBackup) {
				setForm();
			}
		}
	}, [params.clusterId]);
	const getCurrentNamespace = (value: NamespaceItem) => {
		setCurrentNamespace(value);
	};
	const judgeGoNext = (step: number) => {
		if (step === 1) {
			if (
				form
					.getFieldsError()
					.every((item) => item.errors.length === 0) &&
				form.getFieldValue('judgeStock')
			) {
				return true;
			} else {
				notification.warning({
					message: '提醒',
					description: '当前命名空间可分配的资源不足！'
				});
				return false;
			}
		} else if (step === 2 || step === 3) {
			if (
				form.getFieldsError().every((item) => item.errors.length === 0)
			) {
				return true;
			} else {
				return false;
			}
		}
	};
	const goNext = async (value: number) => {
		if (value === 1) {
			await form.validateFields().catch((error) => {
				form.scrollToField(error.errorFields[0].name[0], {
					block: 'center'
				});
				return Promise.reject();
			});
			basicInfo.current = form.getFieldsValue();
			for (const i in basicInfo.current.quota) {
				if (
					i !== 'kibana' &&
					basicInfo.current.quota[i].disabled === false &&
					(!basicInfo.current.quota[i]?.storageId ||
						!basicInfo.current.quota[i]?.storageClassQuota ||
						basicInfo.current.quota[i]?.storageClassQuota === 0)
				) {
					notification.error({
						message: '提醒',
						description: `请输入存储配额！`
					});
					return Promise.reject();
				}
			}
			setClusterAndNamespace(basicInfo.current.clusterAndNamespace);
			return judgeGoNext(value) ? true : false;
		} else if (value === 2) {
			await form.validateFields();
			configData.current = form.getFieldsValue();
			return judgeGoNext(value) ? true : false;
		} else if (value === 3) {
			await form.validateFields();
			highData.current = form.getFieldsValue();
			setCurrentPassword(configData.current.password);
			setTimeout(() => {
				handleSubmit(highData.current);
			}, 1000);
			return judgeGoNext(value) ? true : false;
		} else {
			return false;
		}
	};
	const getExtra = () => {
		if (loading || status === 'error') {
			return (
				<Button
					type="primary"
					onClick={() => {
						history.push({
							pathname: `/project/${params.type}/${params.name}/${params.aliasName}`
						});
					}}
				>
					返回列表
				</Button>
			);
		}
		if (status === 'success') {
			if (isDisaster) {
				return (
					<Button
						type="primary"
						onClick={() => {
							history.goBack();
						}}
					>
						返回
					</Button>
				);
			}
			return [
				<Button
					key="list"
					type="primary"
					onClick={() => {
						history.push({
							pathname: `/project/${params.type}/${params.name}/${params.aliasName}`
						});
					}}
				>
					返回列表
				</Button>,
				<Button
					key="detail"
					onClick={() => {
						history.push({
							pathname: `/project/${params.type}/${params.name}/${params.aliasName}/container/basicInfo/${basicInfo?.current?.name}/${basicInfo?.current?.chartVersion}/${clusterAndNamespace[0]}/${clusterAndNamespace[1]}`
						});
					}}
				>
					查看详情
				</Button>,
				<Button
					key="template"
					onClick={() => {
						setTemplateOpen(true);
					}}
				>
					保存为模板
				</Button>
			];
		}
	};
	const steps = [
		{
			title: '基本信息',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout516}
				>
					<BasicInformation
						type={params.name}
						projectId={project.projectId}
						organId={organization.organId}
						returnCurNamespace={getCurrentNamespace}
						organizationName={organization.name}
						projectName={project.aliasName || project.name}
						setCurrentMode={setCurrentMode}
						judgeBackup={judgeBackup}
						judgeDisaster={isDisaster}
					/>
				</Form>
			)
		},
		{
			title: '基础配置',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout516}
				>
					<Password type={params.name} judgeBackup={judgeBackup} />
					<FileLog />
					<StandardLog />
				</Form>
			)
		},
		{
			title: '高级配置',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout519}
				>
					<DataPartition
						type={params.name}
						mode={currentMode}
						clusterId={clusterAndNamespace?.[0]}
						namespace={currentNamespace?.name}
						judgeBackup={judgeBackup}
					/>
					<HostNetwork />
					<CustomPort type={params.name} />
					<SchedulingPolicy clusterAndNamespace={clusterAndNamespace}>
						<Affinity
							name="exporterNodeAffinity"
							label="监控采集节点亲和"
							isAnti={false}
							disabled={false}
							clusterId={clusterAndNamespace?.[0]}
						/>
						<Toleration
							name="exporterTolerations"
							label="监控采集污点容忍"
							clusterId={clusterAndNamespace?.[0]}
						/>
						<Affinity
							name="kibanaNodeAffinity"
							label="kibana节点亲和"
							isAnti={false}
							disabled={false}
							clusterId={clusterAndNamespace?.[0]}
						/>
						<Toleration
							name="kibanaTolerations"
							label="kibana污点容忍"
							clusterId={clusterAndNamespace?.[0]}
						/>
					</SchedulingPolicy>
					<ContainerConfig currentNamespace={currentNamespace} />
					<CustomForm
						clusterId={clusterAndNamespace?.[0]}
						namespace={currentNamespace?.name}
						customForm={customForm}
					/>
				</Form>
			)
		},
		{
			title: '发布结果',
			content: (
				<div id="publish-result-page">
					<ResultPage
						loading={loading}
						title={title}
						status={status}
						extra={getExtra()}
					>
						<div className="publish-result-page-content">
							<Row>
								<Col span={8}>
									<div className="publish-result-page-label">
										初始密码
									</div>
								</Col>
								<Col span={16}>
									<div
										className="publish-result-page-text"
										title={currentPassword || '未设置'}
									>
										{currentPassword || '未设置'}
									</div>
								</Col>
							</Row>
						</div>
					</ResultPage>
				</div>
			)
		}
	];
	// * 通过模版创建使用
	const templateSteps = [
		{
			title: '选择模板',
			content: (
				<Form form={form}>
					<TemplateSelect />
				</Form>
			)
		},
		{
			title: '基本信息',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout516}
				>
					<TemplateBasicInformation
						type={params.name}
						projectId={project.projectId}
						organId={organization.organId}
						organizationName={organization.name}
						projectName={project.aliasName || project.name}
						template={currentTemplate}
					/>
				</Form>
			)
		},
		{
			title: '高级配置',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout519}
				>
					<DataPartition
						type={params.name}
						mode={currentMode}
						clusterId={clusterAndNamespace?.[0]}
						namespace={currentNamespace?.name}
						judgeBackup={judgeBackup}
					/>
					<HostNetwork />
					<CustomPort type={params.name} />
					<SchedulingPolicy clusterAndNamespace={clusterAndNamespace}>
						<Affinity
							name="exporterNodeAffinity"
							label="监控采集节点亲和"
							isAnti={false}
							disabled={false}
							clusterId={clusterAndNamespace?.[0]}
						/>
						<Toleration
							name="exporterTolerations"
							label="监控采集污点容忍"
							clusterId={clusterAndNamespace?.[0]}
						/>
						<Affinity
							name="kibanaNodeAffinity"
							label="kibana节点亲和"
							isAnti={false}
							disabled={false}
							clusterId={clusterAndNamespace?.[0]}
						/>
						<Toleration
							name="kibanaTolerations"
							label="kibana污点容忍"
							clusterId={clusterAndNamespace?.[0]}
						/>
					</SchedulingPolicy>
					<ContainerConfig currentNamespace={currentNamespace} />
					<CustomForm
						clusterId={clusterAndNamespace?.[0]}
						namespace={currentNamespace?.name}
						customForm={customForm}
					/>
				</Form>
			)
		},
		{
			title: '发布结果',
			content: (
				<div id="publish-result-page">
					<ResultPage
						loading={loading}
						title={title}
						status={status}
						extra={getExtra()}
					/>
				</div>
			)
		}
	];
	const judgeGoNextForTemplate = (step: number) => {
		if (step === 1) {
			const values = form.getFieldsValue();
			if (!values.template) {
				notification.warning({
					message: '提示',
					description: '请选择模板'
				});
				return false;
			}
			setCurrentTemplate(values.template);
			return true;
		} else if (step === 2 || step === 3) {
			if (
				form.getFieldsError().every((item) => item.errors.length === 0)
			) {
				return true;
			} else {
				return false;
			}
		}
	};
	const goNextForTemplate = async (value: number) => {
		if (value === 1) {
			return judgeGoNextForTemplate(value) ? true : false;
		} else if (value === 2) {
			await form.validateFields();
			basicInfo.current = form.getFieldsValue();
			setClusterAndNamespace(basicInfo.current.clusterAndNamespace);
			return judgeGoNextForTemplate(value) ? true : false;
		} else if (value === 3) {
			await form.validateFields();
			highData.current = form.getFieldsValue();
			setTimeout(() => {
				handleBatchSubmit(highData.current);
			}, 1000);
			return judgeGoNextForTemplate(value) ? true : false;
		} else {
			return false;
		}
	};
	// * 使用模版进行批量创建
	const handleBatchSubmit = async (highData: any) => {
		const esQuota: any = {};
		for (const key in basicInfo?.current?.quota) {
			if (!basicInfo?.current?.quota[key].disabled) {
				esQuota[key] = {
					...basicInfo?.current?.quota[key],
					storageId: basicInfo?.current?.quota[key].storageId,
					storageClassQuota:
						basicInfo?.current?.quota[key].storageClassQuota
				};
			}
		}
		const esCustomVolumes = filterObject(transformObject(highData));
		const sendData: any = {
			templageId: currentTemplate?.id, // ! templageId 拼写错误 待后端修改
			clusterId: clusterAndNamespace[0],
			namespace: clusterAndNamespace[1],
			deployMod: 'container',
			name: basicInfo?.current?.name,
			aliasName: basicInfo?.current?.aliasName,
			chartName: params.name,
			chartVersion: basicInfo?.current?.chartVersion,
			type: 'elasticsearch',
			labels: highData?.labels,
			annotations: highData?.annotations,
			description: basicInfo?.current?.description,
			version: basicInfo?.current?.version,
			mode: basicInfo?.current?.mode,
			mirrorImageId: basicInfo?.current?.mirrorImageId,
			scheduler: highData?.scheduler,
			nodeAffinity: (highData?.nodeAffinity || []).concat(
				highData?.nodeAntiAffinity || []
			),
			tolerations: highData?.tolerations,
			esParam: {
				hostNetwork: highData?.hostNetwork,
				httpPort: highData?.httpPort,
				tcpPort: highData?.tcpPort,
				kibanaPort: highData?.kibanaPort,
				exporterPort: highData?.exporterPort,
				exporterNodeAffinity: highData?.exporterNodeAffinity,
				exporterTolerations: highData?.exporterTolerations,
				kibanaNodeAffinity: highData?.kibanaNodeAffinity,
				kibanaTolerations: highData?.kibanaTolerations
			},
			quota: esQuota,
			customVolumes: esCustomVolumes
		};
		// * 动态表单相关
		if (customForm) {
			const dynamicValues: any = {};
			let keys: string[] = [];
			for (const i in customForm) {
				const list = getCustomFormKeys(customForm[i]);
				keys = [...list, ...keys];
			}
			keys.forEach((item) => {
				dynamicValues[item] = highData[item];
			});
			sendData.dynamicValues = dynamicValues;
		}
		await ExecuteOrderFuc();
		postMiddlewareBatch(sendData)
			.then((res) => {
				setLoading(false);
				if (res.success) {
					setTitle('发布成功');
					setStatus('success');
				} else {
					setTitle('发布失败');
					setStatus('error');
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.catch((errors) => {
				setLoading(false);
				setTitle('发布失败');
				setStatus('error');
			});
	};
	// * 保存为模板
	const saveTemplateSubmit = async ({
		templateName
	}: {
		templateName: string;
	}) => {
		await form.validateFields();
		const esQuota: any = {};
		for (const key in basicInfo?.current?.quota) {
			if (!basicInfo?.current?.quota[key].disabled) {
				esQuota[key] = {
					...basicInfo?.current?.quota[key],
					storageClassQuota:
						basicInfo?.current?.quota[key].storageClassQuota
				};
			}
		}
		const sendData: any = {
			deployMod: 'container',
			projectId: project.projectId,
			organId: organization.organId,
			name: templateName,
			aliasName: basicInfo?.current?.aliasName,
			chartName: params.name,
			chartVersion: params.chartVersion,
			type: 'elasticsearch',
			description: basicInfo?.current?.description,
			version: basicInfo?.current?.version,
			mode: basicInfo?.current?.mode,
			filelogEnabled: configData?.current?.filelogEnabled,
			stdoutEnabled: configData?.current?.stdoutEnabled,
			password: configData?.current.password,
			quota: esQuota
		};
		createMiddlewaresTemplate(sendData)
			.then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '模板创建成功'
					});
					history.push(
						`/project/${params.type}/${params.name}/${params.aliasName}/templateManage/${params.chartVersion}/${params.name}/${params.aliasName}`
					);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.catch((errors) => {
				notification.error({
					message: '失败',
					description: errors.errorMsg
				});
			});
	};
	const handleSubmit = async (highData: any) => {
		const esQuota: any = {};
		for (const key in basicInfo?.current?.quota) {
			if (!basicInfo?.current?.quota[key].disabled) {
				esQuota[key] = {
					...basicInfo?.current?.quota[key],
					storageId: basicInfo?.current?.quota[key].storageId,
					storageClassQuota:
						basicInfo?.current?.quota[key].storageClassQuota
				};
			}
		}
		const esCustomVolumes = filterObject(transformObject(highData));
		let sendData: ElasticsearchServiceSendData = {
			clusterId: clusterAndNamespace[0],
			namespace: currentNamespace?.name || clusterAndNamespace[1],
			deployMod: 'container',
			name: basicInfo?.current?.name,
			aliasName: basicInfo?.current?.aliasName,
			chartName: params.name,
			chartVersion: basicInfo?.current?.chartVersion,
			type: 'elasticsearch',
			labels: highData?.labels,
			annotations: highData?.annotations,
			description: basicInfo?.current?.description,
			version: basicInfo?.current?.version,
			mode: basicInfo?.current?.mode,
			password: configData?.current.password,
			mirrorImageId: basicInfo?.current?.mirrorImageId,
			filelogEnabled: configData?.current?.filelogEnabled,
			stdoutEnabled: configData?.current?.stdoutEnabled,
			scheduler: highData?.scheduler,
			nodeAffinity: (highData?.nodeAffinity || []).concat(
				highData?.nodeAntiAffinity || []
			),
			tolerations: highData?.tolerations,
			esParam: {
				hostNetwork: highData?.hostNetwork,
				httpPort: highData?.httpPort,
				tcpPort: highData?.tcpPort,
				kibanaPort: highData?.kibanaPort,
				exporterPort: highData?.exporterPort,
				exporterNodeAffinity: highData?.exporterNodeAffinity,
				exporterTolerations: highData?.exporterTolerations,
				kibanaNodeAffinity: highData?.kibanaNodeAffinity,
				kibanaTolerations: highData?.kibanaTolerations
			},
			quota: esQuota,
			customVolumes: esCustomVolumes
		};
		// * 动态表单相关
		if (customForm) {
			const dynamicValues: any = {};
			let keys: string[] = [];
			for (const i in customForm) {
				const list = getCustomFormKeys(customForm[i]);
				keys = [...list, ...keys];
			}
			keys.forEach((item) => {
				dynamicValues[item] = highData[item];
			});
			sendData.dynamicValues = dynamicValues;
		}
		await ExecuteOrderFuc();
		if (params.middlewareName && params.namespace && judgeBackup) {
			// * 为了解决克隆时，无法克隆参数列表中的数据
			sendData = {
				...sendData,
				isBackup: true,
				relationMiddleware: {
					clusterId: sendData.clusterId,
					namespace: params.namespace,
					name: params.middlewareName,
					type: backupDetail.sourceType
				}
			};
			const result = {
				clusterId: clusterAndNamespace[0],
				namespace: params.namespace,
				middlewareName: basicInfo.current.name,
				type: backupDetail.sourceType,
				backupId: backupDetail.backupId,
				activeArea: backupDetail.activeArea,
				sourceName: backupDetail.sourceName,
				backupName:
					backupDetail.recoveryType === 'time'
						? backupDetail.backupIncName
						: backupDetail.backupRecordName ||
						  backupDetail.backupName
			};
			applyBackup(result);
		}
		postMiddleware(sendData)
			.then((res) => {
				setLoading(false);
				if (res.success) {
					setTitle('发布成功');
					setStatus('success');
				} else {
					setTitle('发布失败');
					setStatus('error');
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.catch((errors) => {
				setLoading(false);
				setTitle('发布失败');
				setStatus('error');
			});
	};
	const setForm = async () => {
		const res = await getMiddlewareDetail({
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params.middlewareName,
			type: 'elasticsearch'
		});
		const res2 = await getNamespaceStorages({
			clusterId: params.clusterId || '',
			namespace:
				params.namespace ||
				currentNamespace?.name ||
				clusterAndNamespace[1]
		});
		if (!res.success) {
			notification.error({
				message: '错误',
				description: (
					<>
						<p>{res.errorMsg}</p>
						<p>{res.errorDetail}</p>
					</>
				)
			});
			return;
		}
		if (!res2.success) {
			notification.error({
				message: '错误',
				description: (
					<>
						<p>{res.errorMsg}</p>
						<p>{res.errorDetail}</p>
					</>
				)
			});
			return;
		}
		// * 判断调度策略开关是否开启
		let schedulingPolicyTemp = false;
		if (
			(res.data.nodeAffinity || []).length !== 0 ||
			!!res.data.scheduler ||
			(res.data.tolerations || []).length !== 0 ||
			(res.data.esParam.exporterTolerations || []).length !== 0 ||
			(res.data.esParam.exporterNodeAffinity || []).length !== 0 ||
			(res.data.esParam.kibanaTolerations || []).length !== 0 ||
			(res.data.esParam.kibanaNodeAffinity || []).length !== 0
		) {
			schedulingPolicyTemp = true;
		}
		const nodeAffinityTemp =
			res.data.nodeAffinity?.filter(
				(i: AffinityItem) => i.anti === false
			) || [];
		const antiNodeAffinityTemp =
			res.data.nodeAffinity?.filter(
				(i: AffinityItem) => i.anti === true
			) || [];
		// * 判断容器配置开关是否开启
		let containerConfigTemp = false;
		if (
			res.data.containerGID ||
			res.data.containerUID ||
			res.data.labels ||
			res.data.annotations
		) {
			containerConfigTemp = true;
		}
		// * storageMaxTemp
		const storageMaxTemp: any = {};
		for (const k in res.data.quota) {
			if (res.data.quota[k].num !== 0) {
				const stp = res2.data.find(
					(i: any) => i.storageId === res.data.quota[k]?.storageId
				);
				storageMaxTemp[k] = stp?.quota?.request - stp?.quota?.used || 0;
			}
		}
		// * quota
		const quotaTemp = {
			master: {
				...res.data.quota.master,
				cpu: +res.data.quota.master.cpu,
				memory: +transUnit.removeUnit(
					res.data.quota.master.memory,
					'Gi'
				),
				storageId: res.data.customVolumes
					? null
					: res.data.quota.master.storageId,
				storageClassNameAndAliasName: res.data.customVolumes
					? null
					: `${res.data.quota.master.storageClassName}/${res.data.quota.master.storageClassAliasName}`,
				storageClassQuota: 0,
				disabled: res.data.quota.master.num === 0 ? true : false,
				storageMax: storageMaxTemp?.master,
				instanceSpec: 'Customize',
				specId: '1'
			},
			kibana: {
				...res.data.quota.kibana,
				cpu: +res.data.quota.kibana.cpu,
				memory: +transUnit.removeUnit(
					res.data.quota.kibana.memory,
					'Gi'
				),
				disabled: res.data.quota.kibana.num === 0 ? true : false,
				instanceSpec: 'Customize',
				specId: '1'
			},
			data: {
				...res.data.quota.data,
				cpu: +res.data.quota.data.cpu,
				memory: +transUnit.removeUnit(res.data.quota.data.memory, 'Gi'),
				storageId: res.data.customVolumes
					? null
					: res.data.quota.data.storageId,
				storageClassNameAndAliasName: res.data.customVolumes
					? null
					: `${res.data.quota.data.storageClassName}/${res.data.quota.data.storageClassAliasName}`,
				storageClassQuota: 0,
				disabled: res.data.quota.data.num === 0 ? true : false,
				storageMax: storageMaxTemp?.data,
				instanceSpec: 'Customize',
				specId: '1'
			},
			client: {
				...res.data.quota.client,
				cpu: +res.data.quota.client.cpu,
				memory: +transUnit.removeUnit(
					res.data.quota.client.memory,
					'Gi'
				),
				storageId: res.data.customVolumes
					? null
					: res.data.quota.client.storageId,
				storageClassNameAndAliasName: res.data.customVolumes
					? null
					: `${res.data.quota.client.storageClassName}/${res.data.quota.client.storageClassAliasName}`,
				storageClassQuota: 0,
				disabled: res.data.quota.client.num === 0 ? true : false,
				storageMax: storageMaxTemp?.client,
				instanceSpec: 'Customize',
				specId: '1'
			},
			cold: {
				...res.data.quota.cold,
				cpu: +res.data.quota.cold.cpu,
				memory: +transUnit.removeUnit(res.data.quota.cold.memory, 'Gi'),
				storageId: res.data.customVolumes
					? null
					: res.data.quota.cold.storageId,
				storageClassNameAndAliasName: res.data.customVolumes
					? null
					: `${res.data.quota.cold.storageClassName}/${res.data.quota.cold.storageClassAliasName}`,
				storageClassQuota: 0,
				disabled: res.data.quota.cold.num === 0 ? true : false,
				storageMax: storageMaxTemp?.cold,
				instanceSpec: 'Customize',
				specId: '1'
			}
		};
		form.setFieldsValue({
			clusterAndNamespace: [params.clusterId, params.namespace],
			name: params.middlewareName + '-backup',
			aliasName: res.data.aliasName,
			description: res.data.description,
			mirrorImageId: res.data.mirrorImageId
				? +res.data.mirrorImageId
				: null,
			version: res.data.version,
			chartVersion: params.chartVersion,
			mode: res.data.mode,
			password: res.data.password,
			stdoutEnabled: res.data.stdoutEnabled,
			filelogEnabled: res.data.filelogEnabled,
			customPort: true,
			httpPort: res.data.esParam.httpPort,
			kibanaPort: res.data.esParam.kibanaPort,
			exporterPort: res.data.esParam.exporterPort,
			tcpPort: res.data.esParam.tcpPort,
			hostNetwork: res.data.esParam.hostNetwork,
			schedulingPolicy: schedulingPolicyTemp,
			nodeAffinitySwitch: nodeAffinityTemp.length !== 0,
			nodeAffinity: nodeAffinityTemp,
			scheduler: res.data.scheduler || false,
			nodeAntiAffinitySwitch: antiNodeAffinityTemp.length !== 0,
			nodeAntiAffinity: antiNodeAffinityTemp,
			tolerationsSwitch: (res.data.totolerations || []).length !== 0,
			tolerations: res.data.tolerations,
			exporterTolerationsSwitch:
				(res.data.esParam.exporterTolerations || []).length !== 0,
			exporterTolerations: res.data.esParam.exporterTolerations,
			exporterNodeAffinitySwitch:
				(res.data.esParam.exporterNodeAffinity || []).length !== 0,
			exporterNodeAffinity: res.data.esParam.esporterNodeAffinity,
			kibanaNodeAffinitySwitch:
				(res.data.esParam.kibanaNodeAffinity || []).length !== 0,
			kibanaNodeAffinity: res.data.esParam.kibanaNodeAffinity,
			kibanaTolerationsSwitch:
				(res.data.esParam.kibanaTolerations || []).length !== 0,
			kibanaTolerations: res.data.esParam.kibanaTolerations,
			containerConfig: containerConfigTemp,
			containerGID: res.data.containerGID,
			containerUID: res.data.containerUID,
			labels: res.data.labels,
			annotations: res.data.annotations,
			quota: quotaTemp,
			dataPartition: res.data.customVolumes ? true : false,
			dataPartitionDisabled: res.data.customVolumes ? false : true
		});
	};
	return (
		<>
			<DefaultPage
				title="发布Elasticsearch服务"
				onBack={() => window.history.back()}
				steps={isTemplate ? templateSteps : steps}
				goNext={isTemplate ? goNextForTemplate : goNext}
			/>
			<TemplateNameModal
				open={templateOpen}
				onCancel={() => setTemplateOpen(false)}
				onCreate={saveTemplateSubmit}
			/>
		</>
	);
}
export default ElasticsearchPublish;

import React, { useEffect, useRef, useState } from 'react';
import DefaultPage from './components/DefaultPage';
import { useHistory, useParams } from 'react-router';
import ResultPage from './components/ResultPage';
import { Button, Form, notification } from 'antd';
import { formItemLayout614 } from '@/utils/const';
import { ResultStatusType } from 'antd/lib/result';
import AccessMiddlewareName from './components/AccessMiddlewareName';
import MiddlewareAliasName from './components/MiddlewareAliasName';
import AccessVersion from './components/AccessVersion';
import AccessPassword from './components/AccessPassword';
import Description from './components/Description';
import AccessSelectService from './components/AccessSelectService';
import AccessAddInstance from './components/AccessAddInstance';
import AccessOperatorSelect from './components/AccessOperatorSelect';
import { postMiddleware } from '@/services/middleware';
import OrganAndProject from './components/OrganAndProject';
import storage from '@/utils/storage';
import AccessClusterAndNamespace from './components/AccessClusterAndNamespace';
import MonitorAddressFormItem from '@/components/MonitorAddressFormItem';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';
import { getAbilityDefault } from '@/services/agent';
import AccessConsoleAddress from './components/AccessConsoleAddress';

export default function AccessService(): JSX.Element {
	const params: AccessParams = useParams();
	const project = storage.getSession('project');
	const organization = storage.getSession('organization');
	const history = useHistory();
	const [form] = Form.useForm();
	const { name, aliasName } = params;
	const [loading, setLoading] = useState<boolean>(false);
	const [title, setTitle] = useState<string>('接入中');
	const [status, setStatus] = useState<ResultStatusType>('info');
	const [clusterAndNamespace, setClusterAndNamespace] = useState<any>();
	const basicInfo = useRef<any>(null);
	const agentInfo = useRef<any>(null);
	const podsInfo = useRef<any>(null);
	const operateInfo = useRef<any>(null);
	const [isWithoutOperator, setIsWithoutOperator] = useState<boolean>(false);
	// * 接入的服务 配置文件 feature功能
	const [extraMiddlewareConfigAPI] = useState<any>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'extraMiddlewareMonitoring')
			?.enabled ?? true
	);
	const getExtra = () => {
		if (loading || status === 'error') {
			return (
				<Button
					type="primary"
					onClick={() => {
						history.push({
							pathname: `/project/${params.type}/${params.name}/${params.aliasName}`
						});
					}}
				>
					返回列表
				</Button>
			);
		}
		if (status === 'success') {
			return [
				<Button
					key="list"
					type="primary"
					onClick={() => {
						history.push({
							pathname: `/project/${params.type}/${params.name}/${params.aliasName}`
						});
					}}
				>
					返回列表
				</Button>,
				<Button
					key="detail"
					onClick={() => {
						history.push(
							`/project/${params.type}/${params.name}/${params.aliasName}/server/basicInfo/${basicInfo?.current?.name}/${clusterAndNamespace?.[0]}/${clusterAndNamespace?.[1]}`
						);
					}}
				>
					查看详情
				</Button>
			];
		}
	};
	const steps_origin = [
		{
			title: '基本信息',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout614}
				>
					<OrganAndProject
						organId={organization.organId}
						projectId={project.projectId}
						organName={organization.name}
						projectName={project.name}
					/>
					<AccessClusterAndNamespace />
					<AccessMiddlewareName type={name} />
					<MiddlewareAliasName />
					<AccessVersion />
					<AccessPassword type={name} />
					{name === 'nacos' ||
					name === 'rabbitmq' ||
					name === 'elasticsearch' ||
					name === 'kibana' ||
					name === 'skywalking' ? (
						<AccessConsoleAddress />
					) : null}
					{extraMiddlewareConfigAPI ? (
						<MonitorAddressFormItem layout={6} />
					) : null}
					<Description />
				</Form>
			)
		},
		{
			title: '选择服务器',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout614}
				>
					<AccessSelectService
						clusterAndNamespace={clusterAndNamespace}
						data={agentInfo}
					/>
				</Form>
			)
		},
		{
			title: '添加实例',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout614}
				>
					<AccessAddInstance
						clusterAndNamespace={clusterAndNamespace}
						podsInfo={podsInfo}
						agentInfo={agentInfo}
						basicInfo={basicInfo}
					/>
				</Form>
			)
		},
		{
			title: '选择运维能力',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout614}
				>
					<AccessOperatorSelect />
				</Form>
			)
		},
		{
			title: '接入结果',
			content: (
				<ResultPage
					loading={loading}
					title={title}
					status={status}
					extra={getExtra()}
				/>
			)
		}
	];
	const step_without_operator = steps_origin.filter(
		(item) => item.title !== '选择运维能力'
	);
	useEffect(() => {
		getAbilityDefault({ type: params.name }).then((res) => {
			if (!res.success) {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
				return;
			}
			if (res.data.length === 0) {
				setIsWithoutOperator(true);
			} else {
				setIsWithoutOperator(false);
			}
		});
	}, []);
	const judgeGoNext = (step: number) => {
		if (step === 1) {
			if (
				form.getFieldsError().every((item) => item.errors.length === 0)
			) {
				return true;
			} else {
				return false;
			}
		} else if (step === 2) {
			if (form.getFieldsValue().agentList?.length > 0) {
				return true;
			} else {
				notification.warning({
					message: '提示',
					description: '请选择服务器！'
				});
				return false;
			}
		} else if (step === 3) {
			if (form.getFieldsValue().pods?.length > 0) {
				return true;
			} else {
				notification.warning({
					message: '提示',
					description: '请添加实例！'
				});
				return false;
			}
		} else {
			return true;
		}
	};
	const goPrev = (value: number) => {
		if (value === 1) {
			podsInfo.current = form.getFieldsValue();
		}
	};
	const goNext = async (value: number) => {
		if (value === 1) {
			await form.validateFields();
			basicInfo.current = form.getFieldsValue();
			setClusterAndNamespace(basicInfo.current.clusterAndNamespace);
			return judgeGoNext(value) ? true : false;
		} else if (value === 2) {
			agentInfo.current = form.getFieldsValue();
			return judgeGoNext(value) ? true : false;
		} else if (value === 3) {
			podsInfo.current = form.getFieldsValue();
			console.log(isWithoutOperator);

			if (isWithoutOperator) {
				if (form.getFieldsValue().pods?.length > 0) {
					handleSubmit([]);
				}
			}
			return judgeGoNext(value) ? true : false;
		} else if (value === 4) {
			if (isWithoutOperator) {
				return true;
			}
			operateInfo.current = form.getFieldsValue();
			handleSubmit(operateInfo.current);
			return judgeGoNext(value) ? true : false;
		} else {
			return false;
		}
	};
	const handleSubmit = async (value: any) => {
		const podsTemp = podsInfo.current.pods.map((item: any) => {
			return {
				podName: item.name,
				port: item.port,
				nodeName: item.nodeName,
				protocol: item?.protocol || ''
			};
		});
		const devopsAbilityListTemp = value?.devopsAbilityList?.map(
			(item: OperatorItem) => {
				return {
					name: item.name,
					cmd: item.exec,
					description: item.describe,
					operationScope: item.operationScope,
					aliasName: item.aliasName,
					describe: item.describe,
					exec: item.exec
				};
			}
		);
		let sendData: any = {
			deployMod: 'server',
			clusterId: clusterAndNamespace?.[0],
			namespace: clusterAndNamespace?.[1],
			name: basicInfo.current.name,
			aliasName: basicInfo.current.aliasName,
			version: basicInfo.current.version,
			username: basicInfo.current?.databaseMessage?.user,
			password: basicInfo.current?.databaseMessage?.password,
			description: basicInfo.current.description,
			pods: podsTemp,
			devOpsAbilities: devopsAbilityListTemp,
			type: params.name,
			organId: params.organId,
			projectId: params.projectId,
			extraMiddlewareMonitorInfoList:
				basicInfo.current.extraMiddlewareMonitorInfoList
		};
		if (basicInfo.current?.consoleAddress) {
			sendData = {
				...sendData,
				managementUrl: {
					protocol: basicInfo.current?.consoleAddress?.protocol,
					url: basicInfo.current?.consoleAddress?.address,
					port: Number(basicInfo.current?.consoleAddress?.port)
				}
			};
		}
		await ExecuteOrderFuc();
		postMiddleware(sendData)
			.then((res) => {
				setLoading(false);
				if (res.success) {
					setTitle('接入成功');
					setStatus('success');
				} else {
					setTitle('接入失败');
					setStatus('error');
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.catch((errors) => {
				setLoading(false);
				setTitle('接入失败');
				setStatus('error');
			});
	};
	return (
		<DefaultPage
			title={`接入${aliasName}服务`}
			onBack={() => window.history.back()}
			steps={isWithoutOperator ? step_without_operator : steps_origin}
			goNext={goNext}
			goPrev={goPrev}
		/>
	);
}

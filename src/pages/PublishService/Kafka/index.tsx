import React, { useEffect, useRef, useState } from 'react';
import { Button, Form, notification } from 'antd';
import { useParams, useHistory } from 'react-router';
import { ResultStatusType } from 'antd/lib/result';
import { formItemLayout614 } from '@/utils/const';
import DefaultPage from '../components/DefaultPage';
import BasicInformation from '../components/Basic';
import ZookeeperService from '../components/ZookeeperService';
import { NamespaceItem } from '@/pages/ProjectDetail/projectDetail';
import ContainerConfig from '../components/ContainerConfig';
import SchedulingPolicy from '../components/SchedulingPolicy';
import Toleration from '../components/Toleration';
import Affinity from '../components/Affinity';
import ResultPage from '../components/ResultPage';
import { getMiddlewareDetail, postMiddleware } from '@/services/middleware';
import { applyBackup } from '@/services/backup';
import { getAspectFrom } from '@/services/common';
import CustomForm from '../components/CustomForm';
import { getCustomFormKeys } from '@/utils/utils';
import storage from '@/utils/storage';
import FileLog from '../components/FileLog';
import StandardLog from '../components/StandardLog';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';
import transUnit from '@/utils/transUnit';

function KafkaPublish(): JSX.Element {
	const project = storage.getSession('project');
	const organization = storage.getSession('organization');
	const [form] = Form.useForm();
	const params: ParamsProps = useParams();
	const history = useHistory();
	const [currentNamespace, setCurrentNamespace] = useState<NamespaceItem>();
	const [clusterAndNamespace, setClusterAndNamespace] = useState<any>();
	const basicInfo = useRef<any>(null);
	const configData = useRef<any>(null);
	const highData = useRef<any>(null);
	const [loading, setLoading] = useState<boolean>(true);
	const [title, setTitle] = useState<string>('发布中');
	const [status, setStatus] = useState<ResultStatusType>('info');
	const [zookeeperInfo, setZookeeperInfo] = useState<any>();
	// * 动态表单
	const [customForm, setCustomForm] = useState<any>();
	// * 备份
	const backupDetail = storage.getLocal('backupDetail');
	// * 判断是否是灾备发布
	const isDisaster = params.isDisaster === 'isDisaster';
	// * 判断是否是克隆发布
	const [judgeBackup] = useState<boolean>(
		!!(params.namespace && params.middlewareName && !isDisaster) || false
	);

	useEffect(() => {
		getAspectFrom().then((res) => {
			if (res.success) {
				setCustomForm(res.data);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	}, []);
	useEffect(() => {
		if (params.clusterId) {
			if (judgeBackup) {
				setForm();
			}
		}
	}, [params.clusterId]);
	const getCurrentNamespace = (value: NamespaceItem) => {
		setCurrentNamespace(value);
	};
	const judgeGoNext = (step: number) => {
		if (step === 1) {
			if (
				form
					.getFieldsError()
					.every((item) => item.errors.length === 0) &&
				form.getFieldValue('judgeStock')
			) {
				return true;
			} else {
				notification.warning({
					message: '提醒',
					description: '当前命名空间可分配的资源不足！'
				});
				return false;
			}
		} else if (step === 2 || step === 3) {
			if (
				form.getFieldsError().every((item) => item.errors.length === 0)
			) {
				return true;
			} else {
				return false;
			}
		}
	};
	const goNext = async (value: number) => {
		if (value === 1) {
			await form.validateFields().catch((error) => {
				form.scrollToField(error.errorFields[0].name[0], {
					block: 'center'
				});
				return Promise.reject();
			});
			basicInfo.current = form.getFieldsValue();
			setClusterAndNamespace(basicInfo.current.clusterAndNamespace);
			return judgeGoNext(value) ? true : false;
		} else if (value === 2) {
			await form.validateFields();
			configData.current = form.getFieldsValue();
			return judgeGoNext(value) ? true : false;
		} else if (value === 3) {
			await form.validateFields();
			highData.current = form.getFieldsValue();
			setTimeout(() => {
				handleSubmit(highData.current);
			}, 1000);
			return judgeGoNext(value) ? true : false;
		} else {
			return false;
		}
	};
	const getExtra = () => {
		if (loading || status === 'error') {
			return (
				<Button
					type="primary"
					onClick={() => {
						history.push({
							pathname: `/project/${params.type}/${params.name}/${params.aliasName}`
						});
					}}
				>
					返回列表
				</Button>
			);
		}
		if (status === 'success') {
			if (isDisaster) {
				return (
					<Button
						type="primary"
						onClick={() => {
							history.goBack();
						}}
					>
						返回
					</Button>
				);
			}
			return [
				<Button
					key="list"
					type="primary"
					onClick={() => {
						history.push({
							pathname: `/project/${params.type}/${params.name}/${params.aliasName}`
						});
					}}
				>
					返回列表
				</Button>,
				<Button
					key="detail"
					onClick={() => {
						history.push({
							pathname: `/project/${params.type}/${params.name}/${params.aliasName}/container/basicInfo/${basicInfo?.current?.name}/${basicInfo?.current?.chartVersion}/${clusterAndNamespace[0]}/${clusterAndNamespace[1]}`
						});
					}}
				>
					查看详情
				</Button>
			];
		}
	};
	const steps = [
		{
			title: '基本信息',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout614}
				>
					<BasicInformation
						type={params.name}
						projectId={project.projectId}
						organId={organization.organId}
						returnCurNamespace={getCurrentNamespace}
						organizationName={organization.name}
						projectName={project.aliasName || project.name}
						judgeDisaster={isDisaster}
						judgeBackup={judgeBackup}
					/>
				</Form>
			)
		},
		{
			title: '基础配置',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout614}
				>
					<ZookeeperService
						judgeBackup={judgeBackup}
						zookeeperInfo={zookeeperInfo}
					/>
					<FileLog />
					<StandardLog />
				</Form>
			)
		},
		{
			title: '高级配置',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout614}
				>
					<SchedulingPolicy clusterAndNamespace={clusterAndNamespace}>
						<Affinity
							name="exporterNodeAffinity"
							label="监控采集节点亲和"
							isAnti={false}
							disabled={false}
							clusterId={clusterAndNamespace?.[0]}
						/>
						<Toleration
							name="exporterTolerations"
							label="监控采集污点容忍"
							clusterId={clusterAndNamespace?.[0]}
						/>
						<Affinity
							name="managerNodeAffinity"
							label="控制台节点亲和"
							isAnti={false}
							disabled={false}
							clusterId={clusterAndNamespace?.[0]}
						/>
						<Toleration
							name="managerTolerations"
							label="控制台污点容忍"
							clusterId={clusterAndNamespace?.[0]}
						/>
					</SchedulingPolicy>
					<ContainerConfig currentNamespace={currentNamespace} />
					<CustomForm
						clusterId={clusterAndNamespace?.[0]}
						namespace={currentNamespace?.name}
						customForm={customForm}
					/>
				</Form>
			)
		},
		{
			title: '发布结果',
			content: (
				<ResultPage
					loading={loading}
					title={title}
					status={status}
					extra={getExtra()}
				/>
			)
		}
	];
	const handleSubmit = async (highData: any) => {
		let sendData: KafkaServiceSendData = {
			clusterId: isDisaster
				? form.getFieldValue('clusterAndNamespace')[0]
				: clusterAndNamespace[0],
			namespace: isDisaster
				? form.getFieldValue('clusterAndNamespace')[1]
				: currentNamespace?.name || clusterAndNamespace[1],
			deployMod: 'container',
			name: basicInfo?.current?.name,
			aliasName: basicInfo?.current?.aliasName,
			chartName: params.name,
			chartVersion: basicInfo?.current?.chartVersion,
			type: 'kafka',
			labels: highData?.labels,
			annotations: highData?.annotations,
			description: basicInfo?.current?.description,
			version: basicInfo?.current?.version,
			mode: basicInfo?.current?.mode || 'cluster',
			mirrorImageId: basicInfo?.current?.mirrorImageId,
			scheduler: highData?.scheduler,
			nodeAffinity: (highData?.nodeAffinity || []).concat(
				highData?.nodeAntiAffinity || []
			),
			tolerations: highData?.tolerations,
			filelogEnabled: configData?.current?.filelogEnabled,
			stdoutEnabled: configData?.current?.stdoutEnabled,
			kafkaDTO: {
				...configData?.current?.kafkaDTO,
				exporterNodeAffinity: highData.exporterNodeAffinity,
				exporterTolerations: highData.exporterTolerations,
				managerNodeAffinity: highData.managerNodeAffinity,
				managerTolerations: highData.managerTolerations
			},
			quota: {
				kafka: {
					num: basicInfo?.current?.num,
					cpu: basicInfo?.current?.cpu,
					memory: basicInfo?.current.memory,
					storageId: basicInfo?.current.storageId,
					storageClassQuota: basicInfo?.current?.storageClassQuota
				}
			}
		};
		// * 动态表单相关
		if (customForm) {
			const dynamicValues: any = {};
			let keys: string[] = [];
			for (const i in customForm) {
				const list = getCustomFormKeys(customForm[i]);
				keys = [...list, ...keys];
			}
			keys.forEach((item) => {
				dynamicValues[item] = highData[item];
			});
			sendData.dynamicValues = dynamicValues;
		}
		await ExecuteOrderFuc();
		if (params.middlewareName && params.namespace) {
			// * 为了解决克隆时，无法克隆参数列表中的数据
			sendData = {
				...sendData,
				isBackup: true,
				relationMiddleware: {
					clusterId: sendData.clusterId,
					namespace: params.namespace,
					name: params.middlewareName,
					type: backupDetail.sourceType
				}
			};
			const result = {
				clusterId: params.clusterId,
				namespace: params.namespace,
				middlewareName: basicInfo.current.name,
				type: backupDetail.sourceType,
				backupId: backupDetail.backupId,
				activeArea: backupDetail.activeArea,
				sourceName: backupDetail.sourceName,
				backupName:
					backupDetail.recoveryType === 'time'
						? backupDetail.backupIncName
						: backupDetail.backupRecordName ||
						  backupDetail.backupName
			};
			applyBackup(result);
		}
		postMiddleware(sendData)
			.then((res) => {
				setLoading(false);
				if (res.success) {
					setTitle('发布成功');
					setStatus('success');
				} else {
					setTitle('发布失败');
					setStatus('error');
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.catch((errors) => {
				setLoading(false);
				setTitle('发布失败');
				setStatus('error');
			});
	};
	const setForm = async () => {
		const res = await getMiddlewareDetail({
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params.middlewareName,
			type: 'kafka'
		});
		if (!res.success) {
			notification.error({
				message: '错误',
				description: (
					<>
						<p>{res.errorMsg}</p>
						<p>{res.errorDetail}</p>
					</>
				)
			});
			return;
		}
		setZookeeperInfo(res.data?.kafkaDTO);
		// * 判断调度策略开关是否开启
		let schedulingPolicyTemp = false;
		if (
			res.data.nodeAffinity?.length !== 0 ||
			!!res.data.scheduler ||
			res.data.tolerations?.length !== 0
		) {
			schedulingPolicyTemp = true;
		}
		const nodeAffinityTemp =
			res.data.nodeAffinity?.filter(
				(i: AffinityItem) => i.anti === false
			) || [];
		const antiNodeAffinityTemp =
			res.data.nodeAffinity?.filter(
				(i: AffinityItem) => i.anti === true
			) || [];
		// * 判断容器配置开关是否开启
		let containerConfigTemp = false;
		if (
			res.data.containerGID ||
			res.data.containerUID ||
			res.data.labels ||
			res.data.annotations
		) {
			containerConfigTemp = true;
		}
		form.setFieldsValue({
			clusterAndNamespace: [params.clusterId, params.namespace],
			name: params.middlewareName + '-backup',
			aliasName: res.data.aliasName,
			description: res.data.description,
			mirrorImageId: res.data.mirrorImageId
				? +res.data.mirrorImageId
				: null,
			version: res.data.version,
			chartVersion: params.chartVersion,
			mode: res.data.mode,
			num: res.data.quota.kafka.num,
			instanceSpec: 'Customize',
			cpu: +res.data.quota.kafka.cpu,
			memory: +transUnit.removeUnit(res.data.quota.kafka.memory, 'Gi'),
			storageId: res.data.quota.kafka.storageId,
			customVolumes: res.data.customVolumes,
			password: res.data.password,
			stdoutEnabled: res.data.stdoutEnabled,
			filelogEnabled: res.data.filelogEnabled,
			slowSql: res.data.slowSql || false,
			audit: res.data.audit || false,
			customPort: true,
			schedulingPolicy: schedulingPolicyTemp,
			nodeAffinitySwitch: nodeAffinityTemp.length !== 0,
			nodeAffinity: nodeAffinityTemp,
			scheduler: res.data.scheduler || false,
			nodeAntiAffinitySwitch: antiNodeAffinityTemp.length !== 0,
			nodeAntiAffinity: antiNodeAffinityTemp,
			tolerationsSwitch: (res.data.tolerations || []).length !== 0,
			tolerations: res.data.tolerations,
			containerConfig: containerConfigTemp,
			containerGID: res.data.containerGID,
			containerUID: res.data.containerUID,
			labels: res.data.labels,
			annotations: res.data.annotations
		});
	};
	return (
		<DefaultPage
			title="发布Kafka服务"
			onBack={() => window.history.back()}
			steps={steps}
			goNext={goNext}
		/>
	);
}

export default KafkaPublish;

import React, { useEffect, useRef, useState } from 'react';
import { But<PERSON>, Col, Form, Row, notification } from 'antd';
import { useParams, useHistory } from 'react-router';
import { ResultStatusType } from 'antd/lib/result';
import DefaultPage from '../components/DefaultPage';
import {
	getMiddlewareDetail,
	postMiddleware,
	addDisasterIns
} from '@/services/middleware';
import ResultPage from '../components/ResultPage';
import SchedulingPolicy from '../components/SchedulingPolicy';
import { formItemLayout614 } from '@/utils/const';
import { NamespaceItem } from '@/pages/ProjectDetail/projectDetail';
import BasicInformation from '../components/Basic';
import Password from '../components/Password';
import FileLog from '../components/FileLog';
import StandardLog from '../components/StandardLog';
import ContainerConfig from '../components/ContainerConfig';
import SlowLog from '../components/SlowLog';
import AuditLog from '../components/AuditLog';
import CharSet from '../components/CharSet';
import '../index.less';
import CustomForm from '../components/CustomForm';
import { getCustomFormKeys } from '@/utils/utils';
import { getAspectFrom } from '@/services/common';
import storage from '@/utils/storage';
import transUnit from '@/utils/transUnit';
import { applyBackup } from '@/services/backup';
import moment from 'moment';
import RecoveryConfig from '../components/RecoveryConfig';
import { getNamespaceStorages } from '@/services/storage';
import CustomPort from '../components/CustomPort';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';
import MysqlDisasterIngress from '../components/MysqlDisasterIngress';
import { getProjectNamespace } from '@/services/project';

function MysqlPublish(): JSX.Element {
	const project = storage.getSession('project');
	const organization = storage.getSession('organization');
	const [form] = Form.useForm();
	const params: ParamsProps = useParams();
	const history = useHistory();
	const [currentNamespace, setCurrentNamespace] = useState<NamespaceItem>();
	const [currentPassword, setCurrentPassword] = useState<string>('');
	const [clusterAndNamespace, setClusterAndNamespace] = useState<any>();
	const [originData, setOriginData] = useState<any>();
	const basicInfo = useRef<any>(null);
	const configData = useRef<any>(null);
	const highData = useRef<any>(null);
	const [loading, setLoading] = useState<boolean>(true);
	const [title, setTitle] = useState<string>('发布中');
	const [status, setStatus] = useState<ResultStatusType>('info');
	// * 备份
	const backupDetail = storage.getLocal('backupDetail');
	// * 判断是否是克隆发布
	const [judgeBackup] = useState<boolean>(
		!!(params.namespace && params.middlewareName) || false
	);
	// * 判断是否是灾备发布
	const [judgeDisaster] = useState<boolean>(
		!!(params.namespace && params.disasterOriginName) || false
	);
	// * 动态表单
	const [customForm, setCustomForm] = useState<any>();
	useEffect(() => {
		getAspectFrom().then((res) => {
			if (res.success) {
				setCustomForm(res.data);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	}, []);
	useEffect(() => {
		if (params.clusterId) {
			if (params.namespace && params.middlewareName) {
				setForm();
			}
			if (params.namespace && params.disasterOriginName) {
				setForm();
			}
		}
	}, [params.clusterId]);
	const getCurrentNamespace = (value: NamespaceItem) => {
		setCurrentNamespace(value);
	};
	const judgeGoNext = (step: number) => {
		if (step === 1) {
			if (
				form
					.getFieldsError()
					.every((item) => item.errors.length === 0) &&
				form.getFieldValue('judgeStock')
			) {
				return true;
			} else {
				notification.warning({
					message: '提醒',
					description: '当前命名空间可分配的资源不足！'
				});
				return false;
			}
		} else if (step === 2 || step === 3) {
			if (
				form.getFieldsError().every((item) => item.errors.length === 0)
			) {
				return true;
			} else {
				return false;
			}
		}
	};
	const goNext = async (value: number) => {
		if (value === 1) {
			await form.validateFields().catch((error) => {
				form.scrollToField(error.errorFields[0].name[0], {
					block: 'center'
				});
				return Promise.reject();
			});
			basicInfo.current = form.getFieldsValue();
			if (basicInfo.current.pattern === 'readWriteProxy') {
				if (
					!basicInfo.current.quota.mysql.storageId ||
					!basicInfo.current.quota.mysql.storageClassQuota
				) {
					notification.error({
						message: '提醒',
						description: '请输入存储配额！'
					});
					return Promise.reject();
				}
				if (basicInfo.current.quota.mysql.storageClassQuota === 0) {
					notification.error({
						message: '提醒',
						description: '请输入存储配额！'
					});
					return Promise.reject();
				}
			}
			setClusterAndNamespace(basicInfo.current.clusterAndNamespace);
			return judgeGoNext(value) ? true : false;
		} else if (value === 2) {
			await form.validateFields();
			configData.current = form.getFieldsValue();
			return judgeGoNext(value) ? true : false;
		} else if (value === 3) {
			await form.validateFields();
			highData.current = form.getFieldsValue();
			setCurrentPassword(configData.current.password);
			setTimeout(() => {
				handleSubmit(highData.current);
			}, 1000);
			return judgeGoNext(value) ? true : false;
		} else {
			return false;
		}
	};
	const getExtra = () => {
		if (loading || status === 'error') {
			return (
				<Button
					type="primary"
					onClick={() => {
						history.push({
							pathname: `/project/${params.type}/${params.name}/${params.aliasName}`
						});
					}}
				>
					返回列表
				</Button>
			);
		}
		if (status === 'success') {
			if (judgeDisaster) {
				return (
					<Button
						type="primary"
						onClick={() => {
							history.goBack();
						}}
					>
						返回
					</Button>
				);
			}
			return [
				<Button
					key="list"
					type="primary"
					onClick={() => {
						history.push({
							pathname: `/project/${params.type}/${params.name}/${params.aliasName}`
						});
					}}
				>
					返回列表
				</Button>,
				<Button
					key="detail"
					onClick={() => {
						history.push({
							pathname: `/project/${params.type}/${params.name}/${params.aliasName}/container/basicInfo/${basicInfo?.current?.name}/${basicInfo?.current?.chartVersion}/${clusterAndNamespace[0]}/${clusterAndNamespace[1]}`
						});
					}}
				>
					查看详情
				</Button>
			];
		}
	};
	const steps = [
		{
			title: '基本信息',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					scrollToFirstError={true}
					{...formItemLayout614}
				>
					<BasicInformation
						type={params.name}
						projectId={project.projectId}
						organId={organization.organId}
						returnCurNamespace={getCurrentNamespace}
						organizationName={organization.name}
						projectName={project.aliasName || project.name}
						judgeBackup={judgeBackup}
						judgeDisaster={judgeDisaster}
					/>
				</Form>
			)
		},
		{
			title: '基础配置',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout614}
				>
					<CharSet judgeBackup={judgeBackup} />
					<Password type={params.name} judgeBackup={judgeBackup} />
					<FileLog />
					<StandardLog />
					<SlowLog />
					<AuditLog />
				</Form>
			)
		},
		{
			title: '高级配置',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout614}
				>
					<CustomPort type={params.name} />
					<SchedulingPolicy
						clusterAndNamespace={clusterAndNamespace}
					/>
					<ContainerConfig currentNamespace={currentNamespace} />
					<RecoveryConfig
						middlewareName={params.middlewareName}
						namespace={params.namespace}
					/>
					<CustomForm
						clusterId={clusterAndNamespace?.[0]}
						namespace={currentNamespace?.name}
						customForm={customForm}
					/>
					{judgeDisaster ? (
						<MysqlDisasterIngress
							clusterId={clusterAndNamespace?.[0]}
							namespace={currentNamespace?.name}
							middlewareName={basicInfo?.current?.name}
						/>
					) : null}
				</Form>
			)
		},
		{
			title: '发布结果',
			content: (
				<div id="publish-result-page">
					<ResultPage
						loading={loading}
						title={title}
						status={status}
						extra={getExtra()}
					>
						<div className="publish-result-page-content">
							<Row>
								<Col span={8}>
									<div className="publish-result-page-label">
										root密码
									</div>
								</Col>
								<Col span={16}>
									<div
										className="publish-result-page-text"
										title={currentPassword}
									>
										{currentPassword}
									</div>
								</Col>
							</Row>
						</div>
					</ResultPage>
				</div>
			)
		}
	];
	const handleSubmit = async (highData: any) => {
		let quotaTemp: any;
		if (basicInfo.current.pattern === 'readWriteProxy') {
			quotaTemp = {
				mysql: {
					...basicInfo.current.quota.mysql,
					memory: basicInfo.current.quota.mysql.memory + 'Gi',
					num:
						basicInfo.current?.num || basicInfo.current.num === 0
							? basicInfo?.current?.num
							: Number(basicInfo?.current?.mode.charAt(3)),
					storageId: basicInfo.current.quota.mysql.storageId
				},
				proxy: {
					...basicInfo.current.quota.proxy,
					memory: basicInfo.current.quota.proxy.memory + 'Gi'
				}
			};
		} else {
			quotaTemp = {
				mysql: {
					storageId: basicInfo.current.storageId,
					storageClassQuota: basicInfo.current.storageClassQuota,
					cpu: basicInfo.current.cpu,
					memory: basicInfo.current.memory + 'Gi',
					num:
						basicInfo.current?.num || basicInfo.current.num === 0
							? basicInfo?.current?.num
							: Number(basicInfo?.current?.mode.charAt(3))
				}
			};
		}
		let sendData: MysqlServiceSendData = {
			clusterId: clusterAndNamespace[0],
			namespace: clusterAndNamespace[1],
			deployMod: 'container',
			name: basicInfo?.current?.name,
			aliasName: basicInfo?.current?.aliasName,
			containerUID: highData?.containerUID,
			containerGID: highData?.containerGID,
			chartName: params.name,
			chartVersion: basicInfo?.current?.chartVersion,
			charSet: configData?.current.charSet,
			type: 'mysql',
			labels: highData?.labels,
			annotations: highData?.annotations,
			description: basicInfo?.current?.description,
			version: basicInfo?.current?.version,
			mode: basicInfo?.current?.mode || '1m-0s',
			password: configData?.current.password,
			mirrorImageId: basicInfo?.current?.mirrorImageId,
			filelogEnabled: configData?.current?.filelogEnabled,
			stdoutEnabled: configData?.current?.stdoutEnabled,
			scheduler: highData?.scheduler,
			nodeAffinity: (highData?.nodeAffinity || []).concat(
				highData?.nodeAntiAffinity || []
			),
			tolerations: highData?.tolerations,
			audit: configData?.current?.audit,
			slowSql: configData?.current?.slowSql,
			readWriteProxy: {
				enabled:
					basicInfo.current.pattern === 'readWriteProxy'
						? true
						: false
			},
			quota: quotaTemp,
			mysqlDTO: {
				openDisasterRecoveryMode: false
			},
			port: highData?.port
		};
		// * 动态表单相关
		if (customForm) {
			const dynamicValues: any = {};
			let keys: string[] = [];
			for (const i in customForm) {
				const list = getCustomFormKeys(customForm[i]);
				keys = [...list, ...keys];
			}
			keys.forEach((item) => {
				dynamicValues[item] = highData[item];
			});
			sendData.dynamicValues = dynamicValues;
		}
		console.log(sendData);
		await ExecuteOrderFuc();
		if (params.middlewareName && params.namespace && judgeBackup) {
			// * 为了解决克隆时，无法克隆参数列表中的数据
			sendData = {
				...sendData,
				isBackup: true,
				relationMiddleware: {
					clusterId: sendData.clusterId,
					namespace: params.namespace,
					type: backupDetail.sourceType,
					name: params.middlewareName
				}
			};
			const result = {
				clusterId: clusterAndNamespace[0],
				namespace: params.namespace || currentNamespace?.name,
				middlewareName: basicInfo?.current?.name,
				backupId: backupDetail.backupId,
				type: backupDetail.sourceType,
				activeArea: backupDetail.activeArea,
				sourceName: backupDetail.sourceName,
				restoreTime:
					backupDetail.recoveryType === 'time'
						? moment(highData.restoreTime).format(
								'YYYY-MM-DD HH:mm:ss'
						  )
						: '',
				backupName:
					backupDetail.recoveryType === 'time'
						? backupDetail.backupIncName
						: backupDetail.backupRecordName ||
						  backupDetail.backupName
			};
			applyBackup(result);
		}
		if (params.namespace && params.disasterOriginName) {
			const disasterData: any = {
				...(originData ? originData : {}),
				middlewareName: params.disasterOriginName,
				chartName: 'mysql',
				mysqlDTO: {
					...(originData?.mysqlDTO || {}),
					openDisasterRecoveryMode: true,
					isSource: true
				},
				mysqlDisasterExposeDtoList: [
					{
						chartVersion: params.chartVersion,
						exposeType: '',
						ingressClassName: highData.masterIngressClassName,
						middlewareType: 'mysql',
						protocol: highData.protocol ? highData.protocol : 'TCP',
						tcpExposeType:
							highData.masterExposeType === 'Ingress'
								? highData.masterIngresType === 'nginx'
									? 'nginx'
									: 'traefik'
								: 'nodePort',
						serviceDTOList: [
							{
								exposePort: highData.masterExposePort
							}
						]
					}
				],
				relationMiddleware: {
					...sendData,
					mysqlDisasterExposeDtoList: [
						{
							chartVersion: params.chartVersion,
							exposeType: '',
							ingressClassName: highData.slaveIngressClassName,
							middlewareType: 'mysql',
							protocol: highData.protocol
								? highData.protocol
								: 'TCP',
							tcpExposeType:
								highData.slaveExposeType === 'Ingress'
									? highData.slaveIngresType === 'nginx'
										? 'nginx'
										: 'traefik'
									: 'nodePort',
							serviceDTOList: [
								{
									exposePort: highData.slaveExposePort
								}
							]
						}
					]
				}
			};
			// * 发布灾备
			addDisasterIns(disasterData)
				.then((res) => {
					setLoading(false);
					if (res.success) {
						setTitle('发布成功');
						setStatus('success');
					} else {
						setTitle('发布失败');
						setStatus('error');
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				})
				.catch((errors) => {
					setLoading(false);
					setTitle('发布失败');
					setStatus('error');
				});
		} else {
			postMiddleware(sendData)
				.then((res) => {
					setLoading(false);
					if (res.success) {
						setTitle('发布成功');
						setStatus('success');
					} else {
						setTitle('发布失败');
						setStatus('error');
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				})
				.catch((errors) => {
					setLoading(false);
					setTitle('发布失败');
					setStatus('error');
				});
		}
	};
	const setForm = async () => {
		const res = await getMiddlewareDetail({
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params.middlewareName || params.disasterOriginName,
			type: 'mysql'
		});
		if (params.namespace && params.disasterOriginName) {
			setOriginData(res.data);
			// * 发布灾备服务版本要一致且不能修改
			form.setFieldValue('version', res.data.version);
			// * 去除之前复用源服务的功能
			return;
		}
		if (!res.success) {
			notification.error({
				message: '错误',
				description: (
					<>
						<p>{res.errorMsg}</p>
						<p>{res.errorDetail}</p>
					</>
				)
			});
			return;
		}
		const res2 = await getNamespaceStorages({
			clusterId: params.clusterId || '',
			namespace: params.namespace
		});
		const res3 = await getProjectNamespace({
			organId: organization.organId,
			projectId: project.projectId,
			clusterId: params.clusterId,
			withQuota: true
		});
		// * 判断当前命名空间是否是双活命名空间
		const cur_namespace = res3.data.find(
			(item) => item.name === params.namespace
		);
		// * 判断调度策略开关是否开启
		let schedulingPolicyTemp = false;
		if (
			(res.data.nodeAffinity || []).length !== 0 ||
			!!res.data.scheduler ||
			(res.data?.tolerations || []).length !== 0
		) {
			schedulingPolicyTemp = true;
		}
		const nodeAffinityTemp =
			res.data.nodeAffinity?.filter(
				(i: AffinityItem) => i.anti === false
			) || [];
		const antiNodeAffinityTemp =
			res.data.nodeAffinity?.filter(
				(i: AffinityItem) => i.anti === true
			) || [];
		// * 判断容器配置开关是否开启
		let containerConfigTemp = false;
		if (
			res.data.containerGID ||
			res.data.containerUID ||
			res.data.labels ||
			res.data.annotations
		) {
			containerConfigTemp = true;
		}
		// * 获取存储总额 storageMax
		let storageMaxTemp = 0;
		const stp = res2.data.find(
			(i: any) => i.storageId === res.data.quota.mysql.storageId
		);
		storageMaxTemp = stp?.quota?.request - stp?.quota?.used || 0;
		// * quota
		let quotaTemp: any;
		if (res.data.readWriteProxy.enabled) {
			quotaTemp = {
				mysql: {
					cpu: +res.data.quota.mysql.cpu,
					memory: +transUnit.removeUnit(
						res.data.quota.mysql.memory,
						'Gi'
					),
					num: res.data.quota.mysql.num,
					storageClassNameAndAliasName: `${res.data.quota.mysql.storageClassName}/${res.data.quota.mysql.storageClassAliasName}`,
					storageId: res.data.quota.mysql.storageId,
					storageClassQuota: 0,
					instanceSpec: 'Customize',
					title: '普通节点',
					specId: '1',
					storageMax: storageMaxTemp
				},
				proxy: {
					cpu: +res.data.quota?.proxy?.cpu,
					memory: +transUnit.removeUnit(
						res.data.quota?.proxy?.memory,
						'Gi'
					),
					num: res.data.quota.proxy?.num,
					instanceSpec: 'Customize',
					title: 'proxy节点',
					specId: '1'
				}
			};
		} else {
			quotaTemp = {
				mysql: {
					cpu: +res.data.quota.mysql.cpu,
					memory: +transUnit.removeUnit(
						res.data.quota.mysql.memory,
						'Gi'
					),
					num: res.data.quota.mysql.num,
					storageClassNameAndAliasName: `${res.data.quota.mysql.storageClassName}/${res.data.quota.mysql.storageClassAliasName}`,
					storageId: res.data.quota.mysql.storageId,
					storageClassQuota: 0,
					instanceSpec: 'Customize',
					title: '普通节点',
					specId: '1'
				}
			};
		}
		form.setFieldsValue({
			clusterAndNamespace: [
				params.clusterId,
				params.namespace,
				cur_namespace?.availableDomain
			],
			name: params.middlewareName + '-backup',
			aliasName: res.data.aliasName,
			description: res.data.description,
			mirrorImageId: res.data.mirrorImageId
				? +res.data.mirrorImageId
				: null,
			version: res.data.version,
			chartVersion: params.chartVersion,
			pattern: res.data.readWriteProxy.enabled
				? 'readWriteProxy'
				: res.data.mode === '1m-0s'
				? '1m-0s'
				: 'high',
			mode: res.data.mode,
			num: res.data.quota.mysql.num,
			instanceSpec: 'Customize',
			cpu: +res.data.quota.mysql.cpu,
			memory: +transUnit.removeUnit(res.data.quota.mysql.memory, 'Gi'),
			storageId: res.data.quota.mysql.storageId,
			customVolumes: res.data.customVolumes,
			password: res.data.password,
			stdoutEnabled: res.data.stdoutEnabled,
			filelogEnabled: res.data.filelogEnabled,
			slowSql: res.data.slowSql || false,
			audit: res.data.audit || false,
			schedulingPolicy: schedulingPolicyTemp,
			nodeAffinitySwitch: nodeAffinityTemp.length !== 0,
			nodeAffinity: nodeAffinityTemp,
			scheduler: res.data.scheduler || false,
			nodeAntiAffinitySwitch: antiNodeAffinityTemp.length !== 0,
			nodeAntiAffinity: antiNodeAffinityTemp,
			tolerationsSwitch: (res.data.tolerations || []).length !== 0,
			tolerations: res.data.tolerations,
			containerConfig: containerConfigTemp,
			containerGID: res.data.containerGID,
			containerUID: res.data.containerUID,
			labels: res.data.labels,
			annotations: res.data.annotations,
			quota: quotaTemp,
			port: res.data.port
		});
	};
	return (
		<DefaultPage
			title="发布MySQL服务"
			onBack={() => window.history.back()}
			steps={steps}
			goNext={goNext}
		/>
	);
}
export default MysqlPublish;

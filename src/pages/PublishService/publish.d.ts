declare interface ParamsProps {
	type: string;
	name: string;
	aliasName: string;
	chartName?: string;
	middlewareName?: string;
	namespace: string;
	isDisaster?: string;
	isTemplate?: string;
	disasterOriginName?: string;
	clusterId: string;
	chartVersion: string;
}
declare interface AccessParams {
	type: string;
	name: string;
	aliasName: string;
	organId: string;
	projectId: string;
}
declare interface DefaultPublishProps {
	cluster: clusterType;
	namespace: namespaceType;
	organization: OrganizationItem;
	project: ProjectItem;
}
declare interface MirrorItem {
	address: string;
	clusterId: string;
	createTime: string;
	description: string | null;
	hostAddress: string;
	id: number;
	isDefault: null;
	password: string;
	port: number | null;
	project: string;
	protocol: string;
	updateTime: string;
	username: string;
}
declare interface kafkaDTO {
	zkAddress?: string | null;
	zkPort?: number | null;
	path?: string | null;
	exporterTolerations?: string[];
	exporterNodeAffinity?: AffinityItem[];
	managerTolerations?: string[];
	managerNodeAffinity?: AffinityItem[];
}
declare interface AffinityItem {
	label: string;
	required: boolean;
	anti: boolean;
}

declare interface ServiceCommonSendData {
	organId?: string;
	projectId?: string;
	clusterId: string;
	namespace: string;
	name: string;
	aliasName: string;
	chartName: string;
	chartVersion: string;
	type: string;
	labels: string;
	annotations: string;
	description: string;
	version: string;
	mode: string;
	mirrorImageId: string;
	scheduler: boolean;
	nodeAffinity?: AffinityItem[];
	tolerations?: string[];
	dynamicValues?: any;
	deployMod: string;
	password?: string;
}
declare interface KafkaServiceSendData extends ServiceCommonSendData {
	kafkaDTO: KafkaDTO;
	filelogEnabled: boolean;
	stdoutEnabled: boolean;
	quota: {
		kafka: {
			num?: number;
			cpu?: number;
			memory?: string;
			storageId?: string;
			storageClassName?: string;
			storageClassQuota?: string;
		};
	};
	isBackup?: boolean;
	relationMiddleware?: RelationMiddlewareData;
}
declare interface ZooKeeperServiceSendData extends ServiceCommonSendData {
	filelogEnabled: boolean;
	stdoutEnabled: boolean;
	quota: {
		zookeeper: {
			num?: number;
			cpu?: number;
			memory?: number;
			storageId?: string;
			storageClassName?: string;
			storageClassQuota?: string;
		};
	};
	isBackup?: boolean;
	relationMiddleware?: RelationMiddlewareData;
}
declare interface RocketMQServiceSendData extends ServiceCommonSendData {
	filelogEnabled: boolean;
	stdoutEnabled: boolean;
	quota: {
		rocketmq: {
			storageId?: string;
			storageClassName?: string;
			storageClassQuota: string;
			cpu?: number;
			memory?: string;
		};
	};
	rocketMQParam: {
		acl: {
			enable: boolean;
			globalWhiteRemoteAddresses?: string;
			rocketMQAccountList?: rocketMQAccount[];
		};
		replicas?: number;
		group?: number;
		autoCreateTopicEnable: boolean;
		exporterTolerations?: string[];
		exporterNodeAffinity?: AffinityItem[];
		consoleTolerations?: string[];
		consoleNodeAffinity?: AffinityItem[];
	};
}
declare interface CustomVolumesItem {
	hostPath: string;
	mountPath: string;
	storageClass: string;
	targetContainers?: string[];
	volumeSize: number;
}
declare interface ElasticsearchServiceSendData extends ServiceCommonSendData {
	filelogEnabled: boolean;
	stdoutEnabled: boolean;
	esParam: {
		hostNetwork: boolean;
		httpPort?: number;
		tcpPort?: number;
		kibanaPort?: number;
		exporterPort?: number;
		exporterTolerations?: string[];
		exporterNodeAffinity?: AffinityItem[];
		kibanaTolerations?: string[];
		kibanaNodeAffinity?: AffinityItem[];
	};
	customVolumes?: {
		[propsName: string]: CustomVolumesItem;
	};
	quota: {
		[propsName: string]: ModeItemData;
	};
	isBackup?: boolean;
	relationMiddleware?: RelationMiddlewareData;
}
declare interface RedisServiceSendData extends ServiceCommonSendData {
	filelogEnabled: boolean;
	stdoutEnabled: boolean;
	password: string;
	containerUID: number | null;
	containerGID: number | null;
	quota: {
		redis?: {
			title?: string;
			instanceSpec?: string;
			specId?: string;
			num?: number;
			cpu?: number;
			memory?: number;
			storageClassName?: string;
			storageClassQuota?: string;
		};
		sentinel?: {
			title?: string;
			instanceSpec?: string;
			specId?: string;
			num?: number;
			cpu?: number;
			memory?: number;
			storageClassName?: string;
			storageClassQuota?: string;
		};
		proxy?: {
			title?: string;
			instanceSpec?: string;
			specId?: string;
			num?: number;
			cpu?: number;
			memory?: number;
			storageClassName?: string;
			storageClassQuota?: string;
		};
	};
	redisParam: {
		hostNetwork: boolean;
		redisPort?: number;
		sentinelPort?: number;
		predixyPort?: number;
		exporterPort?: number;
		predixyExporterPort?: number;
		sentinelExporterPort?: number;
	};
	customVolumes?: any;
	readWriteProxy: {
		enabled: boolean;
	};
	isBackup?: boolean;
	relationMiddleware?: RelationMiddlewareData;
}
declare interface PostgreSQLServiceSendData extends ServiceCommonSendData {
	filelogEnabled: boolean;
	stdoutEnabled: boolean;
	audit: boolean;
	containerUID: number | null;
	containerGID: number | null;
	quota: {
		postgresql: {
			cpu?: number;
			memory?: string;
			storageId?: string;
			storageClassName?: string;
			storageClassQuota?: string;
			num?: number;
		};
	};
	password: string;
	customVolumes?: any;
	postgresqlParam: {
		hostNetwork: boolean;
		pgPort: number;
		apiPort: number;
		exporterPort: number;
		bgMonPort: number;
		passwordEncryption: string;
	};
	isBackup?: boolean;
	relationMiddleware?: RelationMiddlewareData;
}
declare interface RelationMiddlewareData {
	clusterId: string;
	namespace: string;
	type: string;
	name: string;
}
declare interface MysqlServiceSendData extends ServiceCommonSendData {
	filelogEnabled: boolean;
	stdoutEnabled: boolean;
	containerUID: number | null;
	containerGID: number | null;
	audit: boolean;
	slowSql: boolean;
	charSet: string;
	readWriteProxy: {
		enabled: boolean;
	};
	password: string;
	quota: {
		mysql: {
			storageClassName: string;
			storageClassQuota: string;
			cpu?: number;
			memory?: string;
			num: number;
		};
	};
	mysqlDTO: {
		openDisasterRecoveryMode: boolean;
		type?: string;
		relationName?: string;
		relationAliasName?: string;
		relationClusterId?: string;
		relationNamespace?: string;
		isSource?: boolean;
	};
	port?: number;
	isBackup?: boolean;
	relationMiddleware?: RelationMiddlewareData;
}
declare interface ModeItemData {
	cpu: number;
	disabled: boolean;
	memory: number;
	num: number;
	specId: string;
	storageId: string;
	storageClassNameAndAliasName?: string;
	storageClassName?: string;
	storageClassQuota?: number;
	title: string;
	instanceSpec?: string;
	sentinelPort?: number;
	predixyPort?: number;
	predixyExporterPort?: number;
	sentinelExporterPort?: number;
}
declare interface Option {
	value: string;
	label: string;
	children?: Option[];
	isLeaf?: boolean;
	loading?: boolean;
	disabled?: boolean;
}
declare interface QuotaTableItem {
	id: string;
	cpu: string;
	memory: string;
}

import React, { useState, useEffect } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { Button, Modal, notification } from 'antd';
// --- 自定义组件
import { ProPage, ProContent, ProHeader } from '@/components/ProPage';
import Actions from '@/components/Actions';
import {
	getMiddlewaresTemplate,
	delMiddlewaresTemplate
} from '@/services/middleware';
import ProTable from '@/components/ProTable';
import moment from 'moment';
import storage from '@/utils/storage';
import { modeAliasList } from '@/utils/const';
import { ColumnFilterItem } from 'antd/lib/table/interface';
import { objectRemoveDuplicatesByKey } from '@/utils/utils';

const LinkButton = Actions.LinkButton;
const ServiceTemplateManage = () => {
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const history = useHistory();
	const params: TemplateCreateDetailParams = useParams();
	const [keyword, setKeyword] = useState<string>('');
	const { name, aliasName, type } = params;
	const [templates, setTemplates] = useState<ServiceTemplateItem[]>([]);
	const [total, setTotal] = useState<number>();
	const [current, setCurrent] = useState<number>(1);
	const [pageSize, setPageSize] = useState<number>(10);
	const [versionFilter, setVersionFilter] = useState<ColumnFilterItem[]>();
	const [modeFilter, setModeFilter] = useState<ColumnFilterItem[]>();

	const delTemplate = (record: any) => {
		delMiddlewaresTemplate({
			id: record.id
		}).then((res) => {
			if (res.success) {
				notification.success({
					message: '成功',
					description: '删除成功'
				});
				getList(current, pageSize, keyword);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const createTemplate = () => {
		switch (name) {
			case 'oscar':
			case 'aas':
				history.push(
					`/project/${type}/${name}/${aliasName}/dynamicFormTemplateCreate`
				);
				break;
			default:
				history.push(
					`/project/${type}/${name}/${aliasName}/templateCreate`
				);
				break;
		}
	};

	const getList = (
		current_value: number,
		page_size_value: number,
		keyword_value: string
	) => {
		const sendData = {
			projectId,
			organId,
			type: name,
			current: current_value,
			size: page_size_value,
			keyword: keyword_value
		};
		getMiddlewaresTemplate(sendData).then((res) => {
			if (res.success) {
				const templates_temp = res.data.records.map(
					(item: ServiceTemplateItem) => {
						if (params.name === 'redis') {
							if (item.mode === 'cluster') {
								if (Object.keys(item.quota).includes('proxy')) {
									item.mode = 'agent';
								} else {
									item.mode = 'cluster';
								}
							} else {
								if (Object.keys(item.quota).includes('proxy')) {
									item.mode = 'readWriteProxy';
								} else {
									item.mode = 'sentinel';
								}
							}
						}
						return item;
					}
				);
				const mode_filters = templates_temp.map(
					(ele: ServiceTemplateItem) => {
						return {
							value: ele.mode,
							text: modeAliasList[ele.mode]
						};
					}
				);
				const version_filters = templates_temp.map(
					(ele: ServiceTemplateItem) => {
						return {
							value: ele.version,
							text: ele.version
						};
					}
				);
				setModeFilter(mode_filters);
				setVersionFilter(
					objectRemoveDuplicatesByKey(version_filters, 'value')
				);
				setTemplates(templates_temp);
				setCurrent(res.data.current);
				setPageSize(res.data.pageSize);
				setTotal(res.data.total);
			}
		});
	};
	const delTemplateNew = (record: any) => {
		Modal.confirm({
			title: '操作确认',
			content: (
				<div>
					删除模板后将无法恢复，已发布的服务不受影响
					<br />
					是否确定删除模板？
				</div>
			),
			okText: '确定',
			cancelText: '取消',
			onOk: () => {
				delTemplate(record);
			}
		});
	};
	useEffect(() => {
		getList(current, pageSize, keyword || '');
	}, [organId, projectId]);
	const onTableChange = (page: number, pageSize: number) => {
		setCurrent(page);
		setPageSize(pageSize);
		getList(page, pageSize, keyword);
	};
	const onSearch = (value: string) => {
		setKeyword(value);
		getList(current, pageSize, value);
	};
	const onBack = () => {
		history.push(
			`/project/${params.type}/${params.name}/${params.aliasName}`
		);
	};
	const handleChange: (e: any) => void = (e: any) => {
		setKeyword(e.target.value);
	};
	const resourceRender = (value: any, record: ServiceTemplateItem) => {
		let total_cpu = 0;
		let total_memory = 0;
		let total_storage = 0;
		Object.keys(record.quota).map((item) => {
			total_cpu +=
				Number(record.quota[item].cpu) * Number(record.quota[item].num);
			total_memory +=
				Number(record.quota[item].memory) *
				Number(record.quota[item].num);
			total_storage +=
				Number(record.quota[item].storageClassQuota) *
				Number(record.quota[item].num);
		});
		return `CPU：${total_cpu}C 内存：${total_memory}GB 存储 ${total_storage}GB`;
	};
	return (
		<ProPage>
			<ProHeader title={`${aliasName || ''}模板管理`} onBack={onBack} />
			<ProContent>
				<ProTable
					operation={{
						primary: (
							<Button
								type="primary"
								onClick={() => {
									createTemplate();
								}}
							>
								新增
							</Button>
						)
					}}
					rowKey="id"
					showRefresh
					onRefresh={() => getList(current, pageSize, keyword)}
					dataSource={templates}
					pagination={{
						total,
						current,
						pageSize,
						onChange: onTableChange
					}}
					search={{
						value: keyword,
						onChange: handleChange,
						onSearch: onSearch,
						placeholder: '请输入关键字搜索'
					}}
				>
					<ProTable.Column title="模板名称" dataIndex="name" />
					<ProTable.Column
						title="中间件版本"
						dataIndex="version"
						filters={versionFilter}
						onFilter={(
							value: string | number | boolean,
							record: any
						) => {
							return record.version === value;
						}}
					/>
					<ProTable.Column
						title="控制器版本"
						dataIndex="chartVersion"
					/>
					<ProTable.Column
						title="模式"
						dataIndex="mode"
						filters={modeFilter}
						onFilter={(value: any, record: ServiceTemplateItem) => {
							console.log(value, record);
							return record.mode === value;
						}}
						render={(value: any) => modeAliasList[value]}
					/>
					<ProTable.Column
						title="资源占用情况"
						dataIndex="resource"
						render={resourceRender}
					/>
					<ProTable.Column
						title="创建时间"
						dataIndex="updateTime"
						key="updateTime"
						width={180}
						sorter={(a: any, b: any) =>
							moment(a.updateTime).unix() -
							moment(b.updateTime).unix()
						}
					/>
					<ProTable.Column
						title="操作"
						dataIndex="action"
						render={(_text: any, record: any) => (
							<div>
								<Actions>
									<LinkButton
										onClick={() => {
											delTemplateNew(record);
										}}
									>
										删除
									</LinkButton>
								</Actions>
							</div>
						)}
					/>
				</ProTable>
			</ProContent>
		</ProPage>
	);
};
export default ServiceTemplateManage;

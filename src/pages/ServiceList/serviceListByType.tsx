import React, { useState, useEffect, ReactNode, ReactElement } from 'react';
import moment from 'moment';
import { connect } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';
import {
	Button,
	notification,
	Modal,
	Checkbox,
	Dropdown,
	Space,
	Cascader
} from 'antd';
// --- 自定义组件
import { ProPage, ProContent, ProHeader } from '@/components/ProPage';
import ProTable from '@/components/ProTable';
import Actions from '@/components/Actions';
// --- 方法
import {
	getList,
	deleteMiddlewareStorage,
	recoveryMiddleware,
	ParamsProps,
	getPlatformAdd
} from '@/services/serviceList';
import {
	deleteMiddleware,
	getDisasterService,
	lockService
} from '@/services/middleware';
import { getClusters } from '@/services/common';
import { licenseFeatures } from '@/services/user';
// --- 类型定义 / 常量 / 工具
import {
	serviceListItemProps,
	serviceProps,
	ShowDataSourceParams
} from './service.list';
import { StoreState, User } from '@/types/index';
import storage from '@/utils/storage';
import {
	FIXED_MIDDLEWARES,
	maintenances,
	// MIDDLEWARE_DECOMMISSIONING,
	MIDDLEWARE_DEPLOYMENT,
	serviceDetailMenu,
	states
} from '@/utils/const';
import {
	serviceListStatusRender,
	timeRender,
	nullRender,
	serviceOnOrOffLineTag,
	controlledOperationDisabled
} from '@/utils/utils';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { DownOutlined } from '@ant-design/icons';
import { NamespaceItem } from '../ProjectDetail/projectDetail';
import { getProjectMarket, getProjectNamespace } from '@/services/project';
import { DefaultOptionType } from 'antd/lib/cascader';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
// --- css样式

const { confirm, info } = Modal;
const LinkButton = Actions.LinkButton;
// ! Creating 启动中
// ! Recover 恢复中
// ! Running 运行正常
// ! Failed 运行异常
// ! RunningError  运行异常
// ! Preparing 创建中
// ! failed 创建失败
// ! Deleted 已删除
// ! Deleting 数据删除中
// ! GracefulRestart 重启中

const ServiceListByType = ({ buttonList }: { buttonList: any }) => {
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const [dataSource, setDataSource] = useState<serviceListItemProps>();
	const [showDataSource, setShowDataSource] =
		useState<ShowDataSourceParams>();
	const [backupCheck, setBackupCheck] = useState<boolean>(false);
	const [keyword, setKeyword] = useState<string>('');
	const history = useHistory();
	const params: any = useParams();
	const { name, aliasName, type } = params;
	const [loadingVisible, setLoadingVisible] = useState<boolean>(true);
	// * 手动操作排序、筛选、分页
	const [filteredInfo, setFilteredInfo] = useState<any>({});
	const [sortedInfo, setSortedInfo] = useState<any>({});
	// * 角色权限
	const [role, setRole] = useState<User>();
	const [curClusterId, setCurClusterId] = useState<string>();
	const [curNamespace, setCurNamespace] = useState<any>();
	const [options, setOptions] = useState<any>([]);
	const [value, setValue] = useState<string[]>([]);
	// * 运维面板 feature功能
	const [maintenanceDashboardAPI] = useState<any>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'maintenanceDashboard')
			?.enabled ?? true
	);
	// * 获取进入运维面板的operatorId
	const intoMaintenanceOperatorId =
		maintenances['Enter Operations Panel/Service Console'];
	// * 获取服务删除的operatorId
	const deleteServiceOperatorId = maintenances['Service Deletion'];
	// * 获取服务恢复的operatorId
	const recoveryServiceOperatorId = maintenances['Service Recovery'];
	// * 获取服务发布的operatorId
	const servicePublishOperatorId = maintenances['Service Publishing'];
	// * 获取服务接入的operatorId
	const serviceAccessOperatorId = maintenances['Service Access'];
	// * 获取二次删除的operatorId
	const forceDeleteOperatorId = maintenances['Secondary Deletion'];
	// * 灾备是否开启判断
	const [disasterOpen, setDisasterOpen] = useState<boolean>(false);
	// * 灾备服务信息
	const [disaster, setDisaster] = useState<any[]>([]);
	const [projectMarkets, setProjectMarkets] = useState<any>([]);
	// * 判断当前中间件是官方上架的中间件还是用户自定义上架的中间件
	const [middlewareInfo, setMiddlewareInfo] = useState<any>();
	const [items, setItems] = useState<any>(
		name === 'redis' || name === 'postgresql' || name === 'elasticsearch'
			? [
					{
						label: '发布服务',
						key: 'publish',
						disabled: true,
						title: '中间件信息加载中...'
					},
					{ label: '接入服务', key: 'access' },
					{
						label: '选择模板发布',
						key: 'templatePublish',
						disabled: true,
						title: '中间件信息加载中...'
					}
			  ]
			: [
					{
						label: '发布服务',
						key: 'publish',
						disabled: true,
						title: '中间件信息加载中...'
					},
					{ label: '接入服务', key: 'access' }
			  ]
	);

	useEffect(() => {
		async function getAllData() {
			const res1 = await getClusters({ organId, projectId });
			if (!res1.success) {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res1.errorMsg}</p>
							<p>{res1.errorDetail}</p>
						</>
					)
				});
				return;
			}
			setCurClusterId('all');
			const tl = res1.data.map((item: any) => {
				return {
					value: item.id,
					label: item.nickname || item.name,
					isLeaf: false
				};
			});
			setOptions([
				{
					value: 'all',
					label: '全部',
					isLeaf: false,
					children: [
						{
							value: '*',
							label: '全部',
							isLeaf: true,
							availableDomain: false
						}
					]
				},
				...tl
			]);
			setCurNamespace({
				value: '*',
				label: '全部',
				isLeaf: true,
				availableDomain: false
			});
			setValue(['all', '*']);
			const res3 = await getProjectMarket({
				organId,
				projectId,
				keyword: ''
			});
			if (!res3.success) {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res3.errorMsg}</p>
							<p>{res3.errorDetail}</p>
						</>
					)
				});
				return;
			}
			setProjectMarkets(res3.data);
		}
		if (organId && projectId) {
			getAllData();
		}
	}, [organId, projectId]);
	useEffect(() => {
		if (projectMarkets?.length > 0) {
			if (
				name === 'redis' ||
				name === 'postgresql' ||
				name === 'elasticsearch'
			) {
				const list = [
					{
						label: '发布服务',
						key: 'publish',
						disabled: true,
						title: '中间件信息加载中...'
					},
					{ label: '接入服务', key: 'access' },
					{
						label: '选择模板发布',
						key: 'templatePublish',
						disabled: true,
						title: '中间件信息加载中...'
					}
				];
				const lt1 = projectMarkets.find(
					(item: any) => item.groupCode === type
				);
				const lt2 = lt1?.middlewareInfoDTOS.find(
					(item: any) => item.chartName === name
				);
				setMiddlewareInfo(lt2);
				const itemsTemp = list.map((item: any) => {
					if (
						item.key === 'publish' ||
						item.key === 'templatePublish'
					) {
						item.disabled = false;
						item.title = '';
					}
					return item;
				});
				setItems(itemsTemp);
			} else {
				const list = [
					{
						label: '发布服务',
						key: 'publish',
						disabled: true,
						title: '中间件信息加载中...'
					},
					{ label: '接入服务', key: 'access' }
				];
				const lt1 = projectMarkets.find(
					(item: any) => item.groupCode === type
				);
				const lt2 = lt1?.middlewareInfoDTOS.find(
					(item: any) => item.chartName === name
				);
				setMiddlewareInfo(lt2);
				const itemsTemp = list.map((item: any) => {
					if (
						item.key === 'publish' ||
						item.key === 'templatePublish'
					) {
						item.disabled = false;
						item.title = '';
					}
					return item;
				});
				setItems(itemsTemp);
			}
		}
	}, [projectMarkets, name, type]);
	useEffect(() => {
		licenseFeatures().then((res) => {
			if (res.success) {
				setDisasterOpen(res.data.disasterRecoveryEnable);
			}
		});
	}, []);
	useEffect(() => {
		if (storage.getLocal('role')) {
			const roleT = JSON.parse(storage.getLocal('role'));
			setRole(roleT);
		}
	}, [storage.getLocal('role')]);
	useEffect(() => {
		// * 切换菜单/组织/项目后刷新
		let mounted = true;
		if (organId && projectId && curClusterId && curNamespace) {
			if (mounted) {
				setDataSource(undefined);
				setLoadingVisible(true);
				setShowDataSource({ '': [] });
				setKeyword('');
				getList({
					organId: organId,
					projectId: projectId,
					clusterId:
						curClusterId === 'all' ? '*' : curClusterId || '*',
					namespace: curNamespace.value,
					keyword: '',
					type: name,
					filterServerMod: false
				})
					.then((res) => {
						if (res.success) {
							if (res.data?.length > 0) {
								setDataSource(res.data);
								setShowDataSource({
									[name]: res.data.map((item: any) => {
										item.description =
											item.description || '/';
										return item;
									})
								});
							} else {
								setDataSource(undefined);
								setShowDataSource({ '': [] });
							}
						} else {
							if (
								`${res.code}${res.errorMsg}` !== '400007无权限'
							) {
								notification.error({
									message: '失败',
									description: res.errorMsg
								});
							}
						}
					})
					.finally(() => {
						setLoadingVisible(false);
					});
				getDisasterService({
					organId: organId,
					projectId: projectId,
					clusterId:
						curClusterId === 'all' ? '*' : curClusterId || '*',
					namespace: '*',
					middlewareName: '*',
					type: name
				}).then((res) => {
					if (res.success) {
						setDisaster(res.data);
					}
				});
			}
		}
		return () => {
			setShowDataSource({ '': [] });
			mounted = false;
		};
	}, [organId, projectId, curClusterId, curNamespace, name]);
	useEffect(() => {
		setFilteredInfo({});
		setSortedInfo({});
	}, [name]);
	const getData = (value: string) => {
		setLoadingVisible(true);
		setDataSource(undefined);
		setShowDataSource({ '': [] });
		getList({
			organId: organId,
			projectId: projectId,
			clusterId: curClusterId === 'all' ? '*' : curClusterId || '*',
			namespace: curNamespace?.value || '*',
			keyword: value,
			type: name,
			filterServerMod: false
		})
			.then((res) => {
				if (res.success) {
					if (res.data.length > 0) {
						setDataSource(res.data);
						let list_temp = res.data;
						if (backupCheck) {
							list_temp = res.data.filter(
								(item: serviceProps) =>
									item?.mysqlDTO?.openDisasterRecoveryMode ===
										true ||
									!!disaster.find(
										(_item) =>
											_item?.master.name === item.name ||
											_item?.slave.name === item.name
									)
							);
						}
						setShowDataSource({
							[name]: list_temp
						});
					} else {
						setDataSource(undefined);
						setShowDataSource({ '': [] });
					}
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			})
			.finally(() => {
				setLoadingVisible(false);
			});
		getDisasterService({
			organId: organId,
			projectId: projectId,
			clusterId: curClusterId === 'all' ? '*' : curClusterId || '*',
			namespace: '*',
			middlewareName: '*',
			type: name
		}).then((res) => {
			if (res.success) {
				setDisaster(res.data);
			}
		});
	};
	const loadData = (selectedOptions: any[]) => {
		const targetOption = selectedOptions[selectedOptions.length - 1];
		targetOption.loading = true;
		return getProjectNamespace({
			organId,
			clusterId: targetOption.value,
			projectId,
			withQuota: false
		}).then((res) => {
			if (res.success) {
				const tl =
					name === 'mysql' ||
					name === 'redis' ||
					name === 'postgresql' ||
					name === 'rocketmq'
						? res.data
						: res.data.filter(
								(item) => item.availableDomain !== true
						  );
				const cnl = tl.map((item: NamespaceItem) => {
					const labelTemp = item.availableDomain ? (
						<div className="flex-space-between">
							{item.aliasName || item.name}{' '}
							{item.availableDomain ? (
								<span className="available-domain">可用区</span>
							) : null}
						</div>
					) : (
						item.aliasName || item.name
					);
					return {
						value: item.name,
						label: labelTemp,
						isLeaf: true
					};
				});
				targetOption.children = [
					{
						value: '*',
						label: '全部',
						isLeaf: true,
						availableDomain: false
					},
					...cnl
				];
				setOptions([...options]);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	const handleChange: (e: any) => void = (e: any) => {
		setKeyword(e.target.value);
	};
	const handleSearch = (value: string) => {
		setKeyword(value);
		getData(value);
	};
	const deleteFn = (record: serviceProps) => {
		const notRecover =
			record.type === 'redis' &&
			record.mode === 'cluster' &&
			!record.readWriteProxy?.enabled;
		confirm({
			title: '提示',
			content:
				record.deployMod === 'server'
					? '删除接入的服务后将无法恢复，请谨慎操作! '
					: notRecover
					? '确定删除该服务?（Redis集群模式暂不支持删除后恢复服务，请谨慎操作！）'
					: '确定删除该服务？',
			onOk: async () => {
				await ExecuteOrderFuc();
				return deleteMiddleware({
					clusterId: record.clusterId,
					namespace: record.namespace,
					middlewareName: record.name,
					type: record.type,
					deployMod: record.deployMod
				})
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '删除成功'
							});
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					})
					.finally(() => {
						getData(keyword);
					});
			}
		});
	};
	const handleFilterBackup = (e: CheckboxChangeEvent) => {
		setBackupCheck(e.target.checked);
		let list: any = dataSource?.serviceList || [];
		if (e.target.checked) {
			list =
				showDataSource?.[name].filter(
					(item) =>
						item?.mysqlDTO?.openDisasterRecoveryMode === true ||
						!!disaster.find(
							(_item) =>
								_item?.master.name === item.name ||
								_item?.slave.name === item.name
						)
				) || [];
			setShowDataSource({ [name]: list });
		} else {
			list = dataSource;
			setShowDataSource({ [name]: list });
		}
	};
	const releaseMiddleware = () => {
		if (FIXED_MIDDLEWARES.includes(name)) {
			history.push(`/project/${type}/${name}/${aliasName}/${name}Create`);
		} else {
			history.push(`/project/${type}/${name}/${aliasName}/dynamicForm`);
		}
	};
	const releaseMiddlewareTemplate = () => {
		history.push(
			`/project/${type}/${name}/${aliasName}/${name}CreateByTemplate/isTemplate`
		);
	};
	const recoveryService = (record: serviceProps) => {
		const sendData: ParamsProps = {
			clusterId: record.clusterId,
			namespace: record.namespace,
			chartName: record.type,
			chartVersion: record.chartVersion || null,
			middlewareName: record.name,
			type: record.type
		};
		confirm({
			title: '操作确认',
			content: '请确认是否恢复该服务！',
			onOk: async () => {
				await ExecuteOrderFuc();
				return recoveryMiddleware(sendData)
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '该服务已恢复,3秒后刷新'
							});
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					})
					.finally(() => {
						setTimeout(() => {
							getData(keyword);
						}, 3000);
					});
			}
		});
	};
	const deleteStorage = (record: serviceProps) => {
		const sendData: ParamsProps = {
			clusterId: record.clusterId,
			namespace: record.namespace,
			middlewareName: record.name,
			type: record.type
		};
		confirm({
			title: '操作确认',
			content: '删除后无法恢复该服务，请谨慎操作！',
			onOk: async () => {
				await ExecuteOrderFuc();
				return deleteMiddlewareStorage(sendData)
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '该服务已彻底删除'
							});
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					})
					.finally(() => {
						getData(keyword);
					});
			}
		});
	};
	const lockedService = (lock: boolean, record: serviceProps) => {
		const contentTemp = lock ? (
			'服务锁定后，部分操作将会受到限制，需要经过审批后方可进行，是否确定锁定该服务？'
		) : (
			<>
				<p>服务解锁后，部分操作将不会再受到限制</p>
				<p>是否确定解锁该服务？</p>
			</>
		);
		confirm({
			title: '操作确认',
			content: contentTemp,
			onOk: () => {
				return lockService({
					clusterId: record.clusterId,
					namespace: record.namespace,
					middlewareName: record.name,
					type: record.type,
					chartVersion: record.chartVersion || '',
					deployMod: record.deployMod,
					lock
				})
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: `服务${lock ? '锁定' : '解锁'}成功`
							});
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					})
					.finally(() => {
						getData(keyword);
					});
			}
		});
	};
	const handleMenuClick = (e: any) => {
		switch (e.key) {
			case 'publish':
				WorkOrderFuc(
					releaseMiddleware,
					'locked',
					'null',
					servicePublishOperatorId,
					history,
					type,
					name,
					aliasName
				);
				break;
			case 'templatePublish':
				WorkOrderFuc(
					releaseMiddlewareTemplate,
					'locked',
					'null',
					servicePublishOperatorId,
					history,
					type,
					name,
					aliasName
				);
				break;
			case 'access':
				WorkOrderFuc(
					() => {
						history.push(
							`/project/${type}/${name}/${aliasName}/server/${organId}/${projectId}`
						);
					},
					'locked',
					'null',
					serviceAccessOperatorId,
					history,
					type,
					name,
					aliasName
				);
				break;
			default:
				break;
		}
	};
	const onChange = (value: any, selectedOptions: any) => {
		console.log(value, selectedOptions);
		setValue(value);
		setCurClusterId(value[0]);
		setCurNamespace(selectedOptions[1]);
	};
	const displayRender = (
		labels: string[],
		selectedOptions?: DefaultOptionType[]
	) => {
		return labels?.map((label, i) => {
			const option = selectedOptions?.[i];
			if (i === 0) {
				return <span key={option?.value}>{label} / </span>;
			}
			if (i === 1) {
				if (typeof label === 'string') {
					return <span key={option?.value}>{label}</span>;
				} else {
					return (
						<span key={option?.value}>
							{(label as ReactElement)?.props?.children[0]}{' '}
							<span className="available-domain">可用区</span>
						</span>
					);
				}
			}
		});
	};
	const isInitMiddleware = () => {
		if (middlewareInfo?.official) {
			switch (name) {
				case 'mysql':
					return true;
				case 'redis':
					return true;
				case 'postgresql':
					return true;
				case 'rocketmq':
					return true;
				case 'kafka':
					return true;
				case 'zookeeper':
					return true;
				case 'elasticsearch':
					return true;
				case 'nacos':
					return true;
				case 'skywalking':
					return true;
				case 'kibana':
					return true;
				case 'logstash':
					return true;
				case 'rabbitmq':
					return true;
				default:
					return false;
			}
		} else {
			switch (name) {
				case 'nacos':
					return true;
				case 'skywalking':
					return true;
				default:
					return false;
			}
		}
	};
	const goTemplateManage = () => {
		history.push(`/project/${type}/${name}/${aliasName}/templateManage`);
	};
	const operation = () => {
		if (
			(name === 'mysql' ||
				name === 'redis' ||
				name === 'kafka' ||
				name === 'postgresql' ||
				name === 'elasticsearch') &&
			disasterOpen
		) {
			return {
				primary: (
					<Space>
						{isInitMiddleware() ? (
							<>
								<Dropdown
									disabled={controlledOperationDisabled(
										'base'
									)}
									menu={{ items, onClick: handleMenuClick }}
								>
									<Button type="primary">
										<Space>
											发布/接入服务
											<DownOutlined />
										</Space>
									</Button>
								</Dropdown>
								{FIXED_MIDDLEWARES.includes(name) ? (
									<Button
										disabled={
											controlledOperationDisabled(
												'base'
											) ||
											(middlewareInfo.status &&
												middlewareInfo.status !== 1)
										}
										style={{ marginLeft: '5px' }}
										onClick={() => {
											goTemplateManage();
										}}
									>
										模板管理
									</Button>
								) : (
									''
								)}
							</>
						) : (
							<Button
								type="primary"
								onClick={() => {
									WorkOrderFuc(
										releaseMiddleware,
										'locked',
										'null',
										servicePublishOperatorId,
										history,
										type,
										name,
										aliasName
									);
								}}
								disabled={controlledOperationDisabled('base')}
								title={
									items.find(
										(item: any) =>
											item.key === 'publish' ||
											item.key === 'templatePublish'
									).title
								}
							>
								发布服务
							</Button>
						)}
						<Cascader
							style={{ width: '210px' }}
							allowClear={false}
							loadData={loadData}
							value={value}
							options={options}
							onChange={onChange}
							displayRender={displayRender}
						/>
					</Space>
				),
				secondary: (
					<Checkbox
						checked={backupCheck}
						onChange={handleFilterBackup}
					>
						灾备服务
					</Checkbox>
				)
			};
		} else {
			return {
				primary: (
					<Space>
						{isInitMiddleware() ? (
							<>
								<Dropdown
									disabled={controlledOperationDisabled(
										'base'
									)}
									menu={{ items, onClick: handleMenuClick }}
								>
									<Button type="primary">
										<Space>
											发布/接入服务
											<DownOutlined />
										</Space>
									</Button>
								</Dropdown>
								{FIXED_MIDDLEWARES.includes(name) ? (
									<Button
										disabled={
											middlewareInfo.status &&
											middlewareInfo.status !== 1
										}
										style={{ marginLeft: '5px' }}
										onClick={() => {
											goTemplateManage();
										}}
									>
										模板管理
									</Button>
								) : (
									''
								)}
							</>
						) : (
							<Button
								type="primary"
								onClick={releaseMiddleware}
								disabled={controlledOperationDisabled('base')}
								title={
									items.find(
										(item: any) =>
											item.key === 'publish' ||
											item.key === 'templatePublish'
									).title
								}
							>
								发布服务
							</Button>
						)}
						<Cascader
							style={{ width: '210px' }}
							allowClear={false}
							loadData={loadData}
							value={value}
							options={options}
							onChange={onChange}
							displayRender={displayRender}
						/>
					</Space>
				)
			};
		}
	};
	const toServiceConsole = (record: serviceProps) => {
		const sendData = {
			clusterId: record.clusterId,
			namespace: record.namespace,
			middlewareName: record.name,
			type: record.type,
			deployMode: record.deployMod
		};
		getPlatformAdd(sendData).then((res) => {
			if (res.success) {
				if (res.data) {
					ExecuteOrderFuc(() => {
						const protocol_flag =
							(res.data as string).startsWith('http') ||
							(res.data as string).startsWith('https');
						if (protocol_flag) {
							window.open(res.data, '_blank');
						} else {
							window.open(
								`${window.location.protocol.toLowerCase()}//${
									res.data
								}`,
								'_blank'
							);
						}
					});
				} else {
					notification.error({
						message: '失败',
						description:
							record.deployMod === 'container'
								? `请前往服务暴露页面暴露管理页面服务`
								: '当前服务未配置服务控制台地址，无法跳转对应服务控制台页面'
					});
				}
			} else {
				notification.info({
					message: '提醒',
					description: res.errorMsg
				});
			}
		});
	};
	const toOnOrOffline = (
		record: serviceProps,
		topic: string,
		locked: boolean
	) => {
		if (role?.isAdmin) {
			lockedService(locked, record);
			return;
		}
		const curProjectAuth = role?.userRoleList.find(
			(item) => item.projectId === projectId
		);
		if (curProjectAuth) {
			if (curProjectAuth.weight === 3 || curProjectAuth.weight === 2) {
				// * 判断当前项目下用户的权限为组织管理员或者项目管理员，可以直接上下线
				lockedService(locked, record);
				return;
			} else {
				// * 当该服务处于审批中时，提示弹窗
				if (record.lock === 'locking' || record.lock === 'unlocking') {
					info({
						title: '工单已存在',
						content: '当前已存在正在审批的相同类型工单，请稍后重试'
					});
					return;
				}
				// * 判断为运维人员后跳转工单审批页面
				storage.setSession('currentService', record);
				history.push(
					`/project/${type}/${name}/${aliasName}/workOrder/${topic}/${record.name}/null/${locked}`
				);
				return;
			}
		} else {
			if (role?.dba) {
				// * 当前用户为dba类型的角色时找不到当前项目的权限，但是是运维人员
				if (record.lock === 'locking' || record.lock === 'unlocking') {
					info({
						title: '工单已存在',
						content: '您已存在正在进行的工单，请耐心等待审批！'
					});
					return;
				}
				storage.setSession('currentService', record);
				history.push(
					`/project/${type}/${name}/${aliasName}/workOrder/${topic}/${record.name}/null/${locked}`
				);
				return;
			}
			notification.warning({
				message: '提醒',
				description: '未找到该用户下当前项目的权限，请刷新页面！'
			});
			return;
		}
	};
	const actionRender = (
		value: string,
		record: serviceProps,
		index: number
	) => {
		const notRecover =
			record.type === 'redis' &&
			record.mode === 'cluster' &&
			!record.readWriteProxy?.enabled;
		// * 创建中和创建失败状态的中间件只可以进行删除操作
		if (record.status === 'Preparing' || record.status === 'failed') {
			return (
				<Actions>
					<LinkButton
						disabled={controlledOperationDisabled(
							'base',
							record.lock
						)}
						onClick={() => {
							storage.setSession('currentService', record);
							WorkOrderFuc(
								() => {
									deleteFn(record);
								},
								record.lock,
								record.name,
								deleteServiceOperatorId,
								history,
								type,
								name,
								aliasName,
								record.clusterId,
								record.namespace
							);
						}}
					>
						删除
					</LinkButton>
				</Actions>
			);
		}
		// * 已删除和数据删除中状态显示，数据删除中时，操作置灰
		if (record.status === 'Deleted' || record.status === 'Deleting') {
			return (
				<Actions>
					<LinkButton
						disabled={
							record.status === 'Deleting' ||
							notRecover ||
							controlledOperationDisabled('base', record.lock)
						}
						onClick={() => {
							storage.setSession('currentService', record);
							WorkOrderFuc(
								() => {
									recoveryService(record);
								},
								record.lock,
								record.name,
								recoveryServiceOperatorId,
								history,
								type,
								name,
								aliasName,
								record.clusterId,
								record.namespace
							);
						}}
					>
						恢复服务
					</LinkButton>
					<LinkButton
						disabled={
							record.status === 'Deleting' ||
							controlledOperationDisabled('base', record.lock)
						}
						onClick={() => {
							storage.setSession('currentService', record);
							WorkOrderFuc(
								() => {
									deleteStorage(record);
								},
								record.lock,
								record.name,
								forceDeleteOperatorId,
								history,
								type,
								name,
								aliasName,
								record.clusterId,
								record.namespace
							);
						}}
					>
						彻底删除
					</LinkButton>
				</Actions>
			);
		}
		return (
			<Actions>
				{maintenanceDashboardAPI &&
					(name === 'kafka' ||
						name === 'rocketmq' ||
						name === 'elasticsearch' ||
						name === 'nacos' ||
						name === 'rabbitmq' ||
						name === 'kibana' ||
						name === 'skywalking') && (
						<LinkButton
							disabled={controlledOperationDisabled(
								'expert',
								record.lock
							)}
							onClick={() => {
								// window.open(
								// 	`${window.location.origin}/#/nacostest`
								// );
								WorkOrderFuc(
									() => {
										toServiceConsole(record);
									},
									record.lock,
									record.name,
									intoMaintenanceOperatorId,
									history,
									type,
									name,
									aliasName,
									record.clusterId,
									record.namespace
								);
							}}
						>
							服务控制台
						</LinkButton>
					)}
				{maintenanceDashboardAPI &&
					(name === 'mysql' ||
						name === 'redis' ||
						name === 'postgresql') && (
						<LinkButton
							disabled={
								// !roleFlag.operateFlag ||
								record.status === 'Creating' ||
								controlledOperationDisabled(
									'expert',
									record.lock
								)
							}
							onClick={() => {
								storage.setSession('currentService', record);
								WorkOrderFuc(
									() => {
										window.open(
											`#/operationalPanel/sqlConsole/${organId}/${projectId}/${record.clusterId}/${record.namespace}/${name}/${record.name}/${record?.version}/${record.mode}`,
											'_blank'
										);
									},
									record.lock,
									record.name,
									intoMaintenanceOperatorId,
									history,
									type,
									name,
									aliasName,
									record.clusterId,
									record.namespace
								);
							}}
						>
							运维面板(beta)
						</LinkButton>
					)}
				{(record.lock === 'unlocked' || record.lock === 'locking') && (
					<LinkButton
						onClick={() =>
							toOnOrOffline(record, MIDDLEWARE_DEPLOYMENT, true)
						}
						disabled={
							record.status !== 'Running' ||
							controlledOperationDisabled('lock')
						}
						title={
							controlledOperationDisabled('lock')
								? '当前用户无该操作的权限'
								: ''
						}
					>
						锁定
					</LinkButton>
				)}
				{(record.lock === 'locked' || record.lock === 'unlocking') && (
					<LinkButton
						onClick={() =>
							toOnOrOffline(record, MIDDLEWARE_DEPLOYMENT, false)
						}
						disabled={controlledOperationDisabled('lock')}
						title={
							controlledOperationDisabled('lock')
								? '当前用户无该操作的权限'
								: ''
						}
					>
						解锁
					</LinkButton>
				)}
				<LinkButton
					disabled={controlledOperationDisabled('base', record.lock)}
					onClick={() => {
						storage.setSession('currentService', record);
						WorkOrderFuc(
							() => {
								deleteFn(record);
							},
							record.lock,
							record.name,
							deleteServiceOperatorId,
							history,
							type,
							name,
							aliasName,
							record.clusterId,
							record.namespace
						);
					}}
				>
					删除
				</LinkButton>
			</Actions>
		);
	};
	const nameRender = (value: string, record: serviceProps, index: number) => {
		if (record.status === 'Deleted' || record.status === 'Deleting') {
			return (
				<div className="flex-space-between">
					<div style={{ maxWidth: '120px' }}>
						<div
							title={record.name}
							className="displayed-name text-overflow"
						>
							{record.name}
						</div>
						<div
							title={record.aliasName || ''}
							className="text-overflow"
						>
							{record.aliasName}
						</div>
					</div>
					{serviceOnOrOffLineTag(record.lock)}
				</div>
			);
		}
		if (record.status === 'Preparing' || record.status === 'failed') {
			return (
				<div style={{ maxWidth: '120px' }}>
					<div
						className="displayed-name text-overflow"
						title={
							record.status === 'Preparing'
								? '服务创建中，无法操作'
								: '服务创建失败，无法操作'
						}
					>
						{record.name}
					</div>
					<div className="text-overflow">{record.aliasName}</div>
				</div>
			);
		}
		const isDisaster =
			disaster.find((item: any) => item?.master.name === record.name) ||
			disaster.find((item: any) => item?.slave.name === record.name);
		let canDisaster = false;
		const menuList =
			record.deployMod === 'container'
				? buttonList?.filter(
						(item: any) =>
							(item.module === 'container_middleware' ||
								item.module === 'middleware') &&
							item.name !== 'serviceManagement'
				  ) || []
				: buttonList?.filter(
						(item: any) =>
							(item.module === 'extra_middleware' ||
								item.module === 'middleware') &&
							item.name !== 'serviceManagement'
				  ) || [];
		const currentTab =
			serviceDetailMenu.find(
				(item: any) => item.code === menuList?.[0]?.name
			)?.key || 'basicInfo';
		if (
			record.type === 'redis' ||
			record.type === 'mysql' ||
			record.type === 'kafka'
		) {
			if (
				record.type === 'redis' &&
				record?.version?.split('.')[0] !== '6'
			) {
				canDisaster = false;
			} else if (record.type === 'mysql' && record?.mode === '1m-0s') {
				canDisaster = false;
			} else if (
				record.type === 'mysql' &&
				record?.readWriteProxy?.enabled === true
			) {
				canDisaster = false;
			} else {
				canDisaster = true;
			}
		} else {
			canDisaster = false;
		}
		return (
			<div className="flex-space-between">
				<div className="display-flex flex-center">
					{(record?.mysqlDTO &&
						record?.mysqlDTO?.openDisasterRecoveryMode) ||
					isDisaster ? (
						(!record?.mysqlDTO?.isSource &&
							record.type === 'mysql') ||
						isDisaster?.slave.name === record.name ? (
							<div className="gray-circle">备</div>
						) : (
							<div className="blue-circle">源</div>
						)
					) : null}
					<div
						style={{
							maxWidth:
								record?.mysqlDTO?.openDisasterRecoveryMode &&
								!record?.mysqlDTO?.isSource
									? '120px'
									: '140px'
						}}
					>
						<div
							className={`${
								(menuList?.length &&
									menuList?.[0]?.name !== 'disasterBackup') ||
								(menuList?.[0]?.name === 'disasterBackup' &&
									canDisaster)
									? 'name-link'
									: ''
							} text-overflow`}
							onClick={() => {
								if (
									(menuList?.length &&
										menuList?.[0]?.name !==
											'disasterBackup') ||
									(menuList?.[0]?.name === 'disasterBackup' &&
										canDisaster)
								) {
									if (record.deployMod === 'container') {
										history.push(
											`/project/${type}/${name}/${aliasName}/container/${currentTab}/${record.name}/${record.chartVersion}/${record.clusterId}/${record.namespace}`
										);
									} else {
										history.push(
											`/project/${type}/${name}/${aliasName}/server/${currentTab}/${record.name}/${record.clusterId}/${record.namespace}`
										);
									}
								}
							}}
							title={record.name}
						>
							{record.name}
						</div>
						<div
							title={record.aliasName || ''}
							className="text-overflow"
						>
							{record.aliasName}
						</div>
					</div>
				</div>
				{serviceOnOrOffLineTag(record.lock)}
			</div>
		);
	};
	const deployRender = (value: string) => {
		return value === 'server' ? '服务器部署' : '容器部署';
	};
	const podRender = (value: string, record: serviceProps, index: number) => {
		const canLink =
			(record.deployMod === 'container' &&
				buttonList?.find(
					(item: any) => item.code === 'InstanceDetail'
				)) ||
			(record.deployMod === 'server' &&
				buttonList?.find((item: any) => item.code === 'baseInfo'));
		if (record.status === 'Deleted' || record.status === 'Deleting')
			return '--';
		return (
			<span
				className={canLink ? 'name-link' : ''}
				onClick={() => {
					if (
						record.deployMod === 'container' &&
						buttonList?.find(
							(item: any) => item.code === 'InstanceDetail'
						)
					) {
						history.push(
							`/project/${type}/${name}/${aliasName}/${record.deployMod}/highAvailability/${record.name}/${record.chartVersion}/${record.clusterId}/${record.namespace}`
						);
					}
					if (
						record.deployMod === 'server' &&
						buttonList?.find(
							(item: any) => item.code === 'baseInfo'
						)
					) {
						history.push(
							`/project/${type}/${name}/${aliasName}/server/basicInfo/${record.name}/${record.clusterId}/${record.namespace}`
						);
					}
				}}
			>
				{value || '--'}
			</span>
		);
	};
	const handleTableChange = (pagination: any, filters: any, sorter: any) => {
		setFilteredInfo(filters);
		setSortedInfo(sorter);
	};
	return (
		<ProPage>
			<ProHeader
				title={`${aliasName || ''}服务列表`}
				subTitle="已发布中间件服务管理列表"
			/>
			<ProContent>
				<ProTable
					dataSource={showDataSource?.[name] || []}
					showRefresh
					onRefresh={() => getData(keyword)}
					rowKey={(record) =>
						record.clusterId + record.namespace + record.name
					}
					operation={operation()}
					loading={loadingVisible}
					search={{
						value: keyword,
						onChange: handleChange,
						onSearch: handleSearch,
						placeholder: '请输入搜索内容'
					}}
					rowClassName={(record) => {
						if (
							record.status === 'Deleted' ||
							record.status === 'Deleting'
						) {
							return 'table-row-delete';
						}
						return '';
					}}
					onChange={handleTableChange}
				>
					<ProTable.Column
						title="服务名称/中文别名"
						dataIndex="name"
						width={200}
						render={nameRender}
						fixed="left"
					/>
					<ProTable.Column
						title="状态"
						dataIndex="status"
						key="status"
						width={120}
						render={serviceListStatusRender}
						filters={states}
						filterMultiple={false}
						filteredValue={filteredInfo[1] || null}
						onFilter={(
							value: string | number | boolean,
							record: serviceProps
						) => {
							if (value === 'Other') {
								return (
									record.status !== 'Creating' &&
									record.status !== 'Running' &&
									record.status !== 'Preparing' &&
									record.status !== 'failed' &&
									record.status !== 'Deleted' &&
									record.status !== 'Deleting' &&
									record.status !== 'Switching' &&
									record.status !== 'Upgrading' &&
									record.status !== 'GracefulRestart'
								);
							}
							return record.status === value;
						}}
					/>
					<ProTable.Column
						title="实例数"
						dataIndex="podNum"
						render={podRender}
						width={50}
					/>
					<ProTable.Column
						title="备注"
						dataIndex="description"
						width={150}
						render={nullRender}
					/>
					<ProTable.Column
						title="部署方式"
						dataIndex="deployMod"
						width={100}
						filterMultiple={false}
						filteredValue={filteredInfo[4] || null}
						filters={[
							{
								text: '服务器部署',
								value: 'server'
							},
							{
								text: '容器部署',
								value: 'container'
							}
						]}
						onFilter={(
							value: string | number | boolean,
							record: serviceProps
						) => {
							console.log(
								value,
								record,
								record.deployMod === value
							);
							return record.deployMod === value;
						}}
						render={deployRender}
					/>
					<ProTable.Column
						title="版本"
						dataIndex="version"
						width={50}
						render={(value) => value || '/'}
					/>
					<ProTable.Column
						title="创建时间"
						key="createTime"
						dataIndex="createTime"
						width={180}
						sortOrder={
							sortedInfo.field === 'createTime'
								? sortedInfo.order
								: null
						}
						sorter={(a: serviceProps, b: serviceProps) =>
							moment(a.createTime).unix() -
							moment(b.createTime).unix()
						}
						render={timeRender}
					/>
					<ProTable.Column
						title="操作"
						dataIndex="action"
						render={actionRender}
						width={180}
					/>
				</ProTable>
			</ProContent>
		</ProPage>
	);
};
export default connect((state: StoreState) => ({
	buttonList: state.auth.buttonList
}))(ServiceListByType);

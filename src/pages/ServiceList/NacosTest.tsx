import React, { useEffect } from 'react';

export default function NacosTest(): JSX.Element {
	useEffect(() => {
		const iframe: any = document.getElementById('nacosIframe');
		iframe.addEventListener('load', function () {
			console.log('in');
			const iframeUrl = iframe.contentWindow.location;
			console.log(iframeUrl);
		});
		// const http = new XMLHttpRequest();
		// // nacos 登录接口
		// const url = 'http://************:32398/nacos/v1/auth/users/login';
		// const formData = new window.FormData();
		// formData.append('username', 'nacos');
		// formData.append('password', 'nacos');
		// http.open('POST', url, true);
		// http.onreadystatechange = function () {
		// 	if (http.readyState == 4 && http.status == 200) {
		// 		console.log(JSON.parse(http.responseText));
		// 		const iframe: any = document.getElementById('nacosIframe');
		// 		// iframe.onload = function () {
		// 		// 	iframe.contentWindow.postMessage('a', 'a');
		// 		// };
		// 		// localStorage.setItem(
		// 		// 	'token',
		// 		// 	JSON.stringify(JSON.parse(http.responseText))
		// 		// );
		// 		// 登录成功后打开 nacos 控制台页面
		// 		// window.location.href = 'http://************:32398/nacos/#/';
		// 	}
		// };
		// http.send(formData);
	}, []);
	return (
		<iframe
			id="nacosIframe"
			style={{
				width: '100%',
				height: '100%'
			}}
			src="https://************:30611/login?next=%2F"
			// sandbox="allow-same-origin allow-scripts allow-popups allow-forms allow-storage-access-by-user-activation"
		></iframe>
	);
}

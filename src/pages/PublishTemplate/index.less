.default-page-content {
	margin: 32px 12px 16px 12px;
	width: 100%;
}
.instance-quota-customize-content {
	width: 400px;
	margin-top: 8px;
	padding: 24px 30px 1px 30px;
	background: #efefef;
}
.affinity-tags-content,
.toleration-tags-content {
	width: 500px;
	margin-top: 12px;
	padding: 10px 24px;
	background: #f1f1f2;
	display: flex;
	flex-wrap: wrap;
	column-gap: 8px;
	gap: 8px;
}
.acl-account-list-content {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.mysql-quota-content,
.redis-quota-content {
	display: flex;
}
.es-quota-content {
	display: flex;
	margin-top: 12px;
}
.custom-volumes-content {
	display: flex;
	flex-wrap: wrap;
}
#data-partition-item-content {
	width: 380px;
	margin: 0 16px 16px 0;
	border: 1px solid @border-color;
	.data-partition-item-title {
		color: @black-1;
		padding: 4px 16px;
		font-size: @font-2;
		line-height: @line-height-2;
	}
	.data-partition-custom-collapse {
		border-left: none;
		border-right: none;
		background: @black-10;
		.data-partition-custom-panel {
			margin-bottom: 24px;
			border-top: 1px solid @border-color;
			&:first-child {
				border-top: none;
			}
			&:last-child {
				margin-bottom: 0px;
				border-bottom: none;
			}
		}
	}
	.ant-collapse {
		margin-bottom: 0px !important;
		border-bottom: none;
	}
}
#publish-result-page {
	.ant-result {
		.ant-result-content {
			background-color: white !important;
			padding: 0px !important;
			display: flex;
			justify-content: center;
			.publish-result-page-content {
				width: 500px;
				background-color: #fafafa;
				padding: 20px 24px;
				.publish-result-page-label {
					font-size: 14px;
					font-weight: 600;
				}
				.publish-result-page-text {
					width: 300px;
					.mixin(textEllipsis);
				}
			}
		}
	}
}
.publish-cascader-popup-content {
	.ant-cascader-menus {
		.ant-cascader-menu {
			min-width: 250px !important;
		}
	}
}

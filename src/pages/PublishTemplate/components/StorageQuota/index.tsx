import React from 'react';
import { Form } from 'antd';
import StorageQuotaContent from './storageQuota';
import { formItemLayout516, formItemLayout614 } from '@/utils/const';
interface StorageQuotaProps {
	clusterId?: string;
	namespace?: string;
	defaultStorage?: number;
	type?: string;
}
export default function StorageQuota(props: StorageQuotaProps): JSX.Element {
	const { clusterId, namespace, defaultStorage, type } = props;
	const formClusterAndNamespace = Form.useWatch('clusterAndNamespace');
	return (
		<Form.Item
			name="storageMax"
			label="存储配额"
			required
			{...(type === 'postgresql' ? formItemLayout516 : formItemLayout614)}
		>
			<StorageQuotaContent
				defaultStorage={defaultStorage}
				clusterId={clusterId || formClusterAndNamespace?.[0]}
				namespace={namespace || formClusterAndNamespace?.[1]}
				isActiveActive={formClusterAndNamespace?.[2]}
			/>
		</Form.Item>
	);
}

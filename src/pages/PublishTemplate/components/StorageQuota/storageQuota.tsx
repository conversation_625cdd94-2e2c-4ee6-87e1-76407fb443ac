import React from 'react';
import { Form, InputNumber } from 'antd';
interface StorageQuotaContentProps {
	clusterId: string;
	namespace: string;
	defaultStorage?: number;
	chartName?: string;
	isActiveActive?: boolean;
}
export default function StorageQuotaContent(
	props: StorageQuotaContentProps
): JSX.Element {
	const { defaultStorage = 1 } = props;
	return (
		<Form.Item
			noStyle
			rules={[
				{
					required: true,
					message: '请输入存储配额大小（GB）'
				},
				{
					type: 'number',
					min: defaultStorage,
					message: `存储配额不能小于${defaultStorage}GB`
				}
			]}
			name="storageClassQuota"
		>
			<InputNumber
				style={{ width: '100%' }}
				placeholder="请输入存储配额大小"
				addonAfter="GB"
			/>
		</Form.Item>
	);
}

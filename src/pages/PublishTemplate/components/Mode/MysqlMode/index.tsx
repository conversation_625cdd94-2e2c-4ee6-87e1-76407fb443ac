import { Form, InputNumber, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import ModeContent from './modeContent';
import QuotaContent from './quotaContent';

export default function MysqlMode({
	judgeBackup
}: {
	judgeBackup?: boolean;
}): JSX.Element {
	const clusterAndNamespace = Form.useWatch('clusterAndNamespace');
	const pattern = Form.useWatch('pattern');
	const mode = Form.useWatch('mode');
	const form = Form.useFormInstance();
	const [patternList, setPatternList] = useState([
		{
			value: 'high',
			label: '高可用模式'
		},
		{
			value: 'readWriteProxy',
			label: '读写分离模式'
		},
		{
			value: '1m-0s',
			label: '单实例模式'
		}
	]);
	useEffect(() => {
		if (clusterAndNamespace?.[2]) {
			setPatternList([
				{
					value: 'high',
					label: '高可用模式'
				},
				{
					value: 'readWriteProxy',
					label: '读写分离模式'
				}
			]);
		} else {
			setPatternList([
				{
					value: 'high',
					label: '高可用模式'
				},
				{
					value: 'readWriteProxy',
					label: '读写分离模式'
				},
				{
					value: '1m-0s',
					label: '单实例模式'
				}
			]);
		}
	}, [clusterAndNamespace]);
	useEffect(() => {
		if (pattern === 'readWriteProxy') {
			form.setFieldsValue({
				readWriteProxy: {
					enabled: true
				}
			});
		} else {
			form.setFieldsValue({
				readWriteProxy: {
					enabled: false
				}
			});
		}
		if (pattern === '1m-0s') {
			form.setFieldsValue({
				num: 0
			});
		}
	}, [pattern]);
	return (
		<>
			<Form.Item
				name="pattern"
				label="模式"
				tooltip="本模式中的主、从节点，特指不同类型实例个数"
				initialValue="high"
			>
				<Select
					disabled={!!judgeBackup}
					options={patternList}
					style={{ width: '182px' }}
				/>
			</Form.Item>
			{pattern !== '1m-0s' && (
				<Form.Item name="mode" label=" " initialValue="1m-1s">
					<ModeContent judgeBackup={judgeBackup} />
				</Form.Item>
			)}
			{mode === '1m-ns' && (
				<Form.Item
					name="num"
					label="从节点数"
					initialValue={2}
					rules={[
						{
							required: true,
							message: '请输入从节点数！'
						}
					]}
				>
					<InputNumber
						max={6}
						min={2}
						step={1}
						precision={0}
						disabled={!!judgeBackup}
					/>
				</Form.Item>
			)}
			{pattern === 'readWriteProxy' && (
				<Form.Item
					name="quota"
					label=" "
					initialValue={{
						mysql: {
							disabled: false,
							title: '普通节点',
							num: 1,
							specId: '1',
							cpu: 2,
							memory: 4,
							storageClassName: undefined,
							storageClassQuota: undefined
						},
						proxy: {
							disabled: false,
							title: 'proxy节点',
							num: 3,
							specId: '0',
							cpu: 0.512,
							memory: 0.512
						}
					}}
				>
					<QuotaContent />
				</Form.Item>
			)}
			{mode !== '1m-ns' && <Form.Item noStyle name="num"></Form.Item>}
			<Form.Item noStyle name={['readWriteProxy', 'enabled']}></Form.Item>
		</>
	);
}

import React from 'react';
import { Form } from 'antd';
import ElasticsearchContent from './elasticsearchContent';
import { formItemLayout519 } from '@/utils/const';
import ElasticsearchQuotaContent from './elasticsearchQuotaContent';

export default function ElasticsearchMode({
	judgeBackup
}: {
	judgeBackup?: boolean;
}): JSX.Element {
	return (
		<>
			<Form.Item
				name="mode"
				label="模式"
				style={{ marginBottom: 0 }}
				{...formItemLayout519}
				initialValue={'simple'}
				tooltip={
					<div>
						<p>主节点负责集群管理相关操作</p>
						<p>数据节点负责数据存储</p>
						<p>协调节点负责负载均衡，路由分发</p>
						<p>冷节点负责低优先级数据存储</p>
					</div>
				}
			>
				<ElasticsearchContent judgeBackup={judgeBackup} />
			</Form.Item>
			<Form.Item
				label=" "
				{...formItemLayout519}
				name="quota"
				initialValue={{
					master: {
						disabled: false,
						title: '主节点',
						instanceSpec: 'General',
						num: 3,
						specId: '1',
						cpu: 2,
						memory: 4,
						storageClassName: undefined,
						storageClassQuota: undefined
					},
					kibana: {
						disabled: false,
						title: 'Kibana节点',
						instanceSpec: 'General',
						num: 1,
						specId: '1',
						cpu: 2,
						memory: 4
					},
					data: {
						disabled: true,
						title: '数据节点',
						instanceSpec: 'General',
						num: 3,
						specId: '1',
						cpu: 2,
						memory: 4,
						storageClassName: undefined,
						storageClassQuota: undefined
					},
					client: {
						disabled: true,
						title: '协调节点',
						instanceSpec: 'General',
						num: 2,
						specId: '1',
						cpu: 2,
						memory: 4,
						storageClassName: undefined,
						storageClassQuota: undefined
					},
					cold: {
						disabled: true,
						title: '冷数据节点',
						instanceSpec: 'General',
						num: 3,
						specId: '1',
						cpu: 2,
						memory: 4,
						storageClassName: undefined,
						storageClassQuota: undefined
					}
				}}
			>
				<ElasticsearchQuotaContent />
			</Form.Item>
		</>
	);
}

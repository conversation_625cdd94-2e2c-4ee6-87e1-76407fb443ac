.block-mode-item {
	width: 182px;
	height: 170px;
	border-top: 1px solid #b2d4ef;
	border-right: 1px solid #b2d4ef;
	border-bottom: 1px solid #b2d4ef;
	cursor: pointer;
	&:first-child {
		border-left: 1px solid #b2d4ef;
	}
	.block-mode-item-title-disabled,
	.block-mode-item-title {
		width: 100%;
		height: 30px;
		border-bottom: 1px solid #b2d4ef;
		line-height: 30px;
		display: flex;
		justify-content: center;
		align-items: center;
		.block-mode-item-circle {
			margin-left: 4px;
			height: 16px;
			line-height: 16px;
			padding: 0 4px;
			background-color: #c3d6f0;
			border-radius: @border-radius-lg * 2;
		}
	}
	.block-mode-item-title-disabled {
		background-color: #efefef;
	}
	.block-mode-item-content-disabled,
	.block-mode-item-content {
		width: 100%;
		height: 138px;
		padding: 16px;
	}
	.block-mode-item-content-disabled {
		width: 100%;
		height: 138px;
		padding: 16px;
		background-color: #efefef;
	}
}

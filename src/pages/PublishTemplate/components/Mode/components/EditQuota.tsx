import React, { useEffect } from 'react';
import { Form, InputNumber, Modal } from 'antd';
import { formItemLayout618 } from '@/utils/const';
import InstanceQuota from '../../InstanceQuota';
import StorageQuota from '../../StorageQuota';

interface EditQuotaProps {
	open: boolean;
	onCancel: () => void;
	onCreate: (value: ModeItemData) => void;
	modeType: string; // * mode 类型
	data: ModeItemData; // * 显示数据
	type: string; // * 中间件类型
	clusterId: string; // * 集群id
	namespace: string; // * 命名空间名称
	quota: any; // * 整个quota
}
export default function EditQuota(props: EditQuotaProps): JSX.Element {
	const {
		open,
		onCancel,
		onCreate,
		modeType,
		data,
		type,
		clusterId,
		namespace,
		quota
	} = props;
	const [form1] = Form.useForm();
	useEffect(() => {
		if (data) {
			form1.setFieldsValue({ ...data });
		}
	}, [data]);
	const numRender = () => {
		if (modeType === 'redis') return <></>;
		if (modeType === 'proxy') return <></>;
		if (modeType === 'mysql') return <></>;
		return (
			<Form.Item
				label="节点数量"
				name="num"
				rules={[
					{
						type: 'number',
						min:
							modeType === 'master' || modeType === 'data'
								? 3
								: 1,
						message: `${data.title}数量最小值为${
							modeType === 'master' || modeType === 'data' ? 3 : 1
						}`
					}
				]}
				initialValue={data.num}
			>
				<InputNumber
					value={data.num}
					precision={0}
					style={{ width: '150px' }}
				/>
			</Form.Item>
		);
	};
	const storageRender = () => {
		if (modeType === 'kibana') return <></>;
		if (modeType === 'sentinel') return <></>;
		if (modeType === 'proxy') return <></>;
		return (
			<StorageQuota
				clusterId={clusterId}
				namespace={namespace}
				defaultStorage={modeType === 'mysql' ? 5 : 1}
			/>
		);
	};
	const portRender = () => {
		if (type === 'redis' && modeType === 'sentinel') {
			return (
				<>
					<Form.Item
						label="哨兵节点端口"
						name="sentinelPort"
						initialValue={data.sentinelPort}
						rules={[
							{
								min: 1,
								max: 65535,
								type: 'number',
								message: '端口范围为1至65535的正整数'
							}
						]}
					>
						<InputNumber
							style={{ width: '390px' }}
							placeholder="请输入哨兵节点端口号，默认为26379"
						/>
					</Form.Item>
					<Form.Item
						label="哨兵节点Exporter端口"
						name="sentinelExporterPort"
						initialValue={data.sentinelExporterPort}
						rules={[
							{
								min: 1,
								max: 65535,
								type: 'number',
								message: '端口范围为1至65535的正整数'
							}
						]}
					>
						<InputNumber
							style={{ width: '390px' }}
							placeholder="请输入哨兵节点Exporter端口号，默认为9121"
						/>
					</Form.Item>
				</>
			);
		} else if (type === 'redis' && modeType === 'proxy') {
			return (
				<>
					<Form.Item
						label="代理节点端口"
						name="predixyPort"
						initialValue={data.predixyPort}
						rules={[
							{
								min: 1,
								max: 65535,
								type: 'number',
								message: '端口范围为1至65535的正整数'
							}
						]}
					>
						<InputNumber
							style={{ width: '390px' }}
							placeholder="请输入代理节点端口号，默认为7617"
						/>
					</Form.Item>
					<Form.Item
						label="代理节点Exporter端口"
						name="predixyExporterPort"
						initialValue={data.predixyExporterPort}
						rules={[
							{
								min: 1,
								max: 65535,
								type: 'number',
								message: '端口范围为1至65535的正整数'
							}
						]}
					>
						<InputNumber
							style={{ width: '390px' }}
							placeholder="请输入代理节点Exporter端口号，默认为9121"
						/>
					</Form.Item>
				</>
			);
		} else {
			return <></>;
		}
	};
	const onOk = async () => {
		await form1.validateFields();
		onCreate({ ...data, ...form1.getFieldsValue() });
	};
	return (
		<Modal
			title="实例配置"
			open={open}
			onCancel={onCancel}
			onOk={onOk}
			width={600}
			centered
		>
			<Form
				form={form1}
				{...formItemLayout618}
				colon={false}
				labelAlign="left"
			>
				{numRender()}
				<InstanceQuota quota={quota} type={type} modeType={modeType} />
				{storageRender()}
				{portRender()}
			</Form>
		</Modal>
	);
}

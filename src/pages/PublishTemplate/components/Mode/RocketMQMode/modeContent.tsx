import SelectBlock from '@/components/SelectBlock';
import { Form } from 'antd';
import React, { useState } from 'react';
const modeList = [
	{
		label: '双主',
		value: '2m-noslave'
	},
	{
		label: '两主两从',
		value: '2m-2s'
	},
	{
		label: '三主三从',
		value: '3m-3s'
	},
	{
		label: 'DLedger模式',
		value: 'dledger'
	}
];
export default function ModeContent({
	value,
	onChange
}: {
	value?: string;
	onChange?: (value: string) => void;
}): JSX.Element {
	const form = Form.useFormInstance();
	const [mode, setMode] = useState<string>(value || '2m-noslave');
	const onModeChange = (value: any) => {
		if (value === 'dledger') {
			form.setFieldsValue({
				group: 2,
				replicas: 3
			});
		}
		setMode(value);
		onChange && onChange(value);
	};
	return (
		<SelectBlock
			currentValue={mode}
			onCallBack={onModeChange}
			options={modeList}
		/>
	);
}

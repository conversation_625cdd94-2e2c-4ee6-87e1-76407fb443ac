import React, { useEffect, useState } from 'react';
import SelectBlock from '@/components/SelectBlock';
import { Form } from 'antd';

export default function ModeContent({
	value,
	onChange,
	judgeBackup
}: {
	value?: string;
	onChange?: (value: string) => void;
	judgeBackup?: boolean;
}): JSX.Element {
	const clusterAndNamespace = Form.useWatch('clusterAndNamespace');
	const [mode, setMode] = useState<string>(value || '1m-1s');
	const [modeList, setModeList] = useState([
		{
			label: '一主一从',
			value: '1m-1s'
		},
		{
			label: '一主多从',
			value: '1m-ns'
		},
		{
			label: '单实例',
			value: '1m-0s'
		}
	]);
	useEffect(() => {
		if (clusterAndNamespace?.[2]) {
			setModeList([
				{
					value: '1m-1s',
					label: '一主一从'
				},
				{
					value: '1m-3s',
					label: '一主三从'
				}
			]);
		} else {
			setModeList([
				{
					label: '一主一从',
					value: '1m-1s'
				},
				{
					label: '一主多从',
					value: '1m-ns'
				},
				{
					label: '单实例',
					value: '1m-0s'
				}
			]);
		}
	}, [clusterAndNamespace]);
	const onModeChange = (value: any) => {
		setMode(value);
		onChange && onChange(value);
	};
	return (
		<SelectBlock
			onCallBack={onModeChange}
			currentValue={value || mode}
			options={modeList}
			disabled={!!judgeBackup}
		/>
	);
}

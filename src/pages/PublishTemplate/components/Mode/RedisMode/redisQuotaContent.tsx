import React, { useState } from 'react';
import BlockModeItem from '../components/BlockModeItem';
import '../../../index.less';
import { Form } from 'antd';

export default function RedisQuotaContent({
	value,
	onChange
}: {
	value?: any;
	onChange?: (value: any) => void;
}): JSX.Element {
	const mode = Form.useWatch('mode');
	const [quota, setQuota] = useState<RedisServiceSendData['quota']>({
		redis: {
			title: 'Redis 节点',
			instanceSpec: 'General',
			num: 2,
			specId: '1',
			cpu: 2,
			memory: 1,
			storageClassName: undefined,
			storageClassQuota: undefined
		},
		sentinel: {
			title: '哨兵节点',
			instanceSpec: 'General',
			num: 3,
			specId: '1',
			cpu: 0.256,
			memory: 0.512
		}
	});
	return (
		<div className="redis-quota-content">
			{Object.keys(value || quota).map((key: string) => {
				if (mode === 'sentinel' && key === 'proxy')
					return <React.Fragment key={key}></React.Fragment>;
				if (mode === 'agent' && key === 'sentinel')
					return <React.Fragment key={key}></React.Fragment>;
				return (
					<BlockModeItem
						key={key}
						modeType={key}
						type="redis"
						data={value ? value[key] : quota[key]}
						onChange={(changedValue: any) => {
							setQuota({
								...(value ? value : quota),
								[key]: changedValue
							});
							onChange &&
								onChange({
									...(value ? value : quota),
									[key]: changedValue
								});
						}}
					/>
				);
			})}
		</div>
	);
}

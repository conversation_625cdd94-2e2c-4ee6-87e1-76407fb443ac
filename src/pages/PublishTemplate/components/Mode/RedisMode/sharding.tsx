import SelectBlock from '@/components/SelectBlock';
import { Form } from 'antd';
import React, { useEffect, useState } from 'react';

const clusterShardingList = [
	{
		label: '三分片',
		value: 6
	},
	{
		label: '五分片',
		value: 10
	},
	{
		label: '七分片',
		value: 14
	},
	{
		label: '九分片',
		value: 18
	}
];
const sentinelShardingList = [
	{
		label: '单分片',
		value: 2
	},
	{
		label: '双分片',
		value: 4
	},
	{
		label: '四分片',
		value: 8
	},
	{
		label: '八分片',
		value: 16
	}
];
export default function Sharding({
	value,
	onChange
}: {
	value?: any;
	onChange?: (value: any) => void;
}): JSX.Element {
	const mode = Form.useWatch('mode');
	const [sharding, setSharding] = useState<number>(value || 6);
	const optionsRender = () => {
		switch (mode) {
			case 'cluster':
				return clusterShardingList;
			case 'sentinel':
				return [sentinelShardingList[0]];
			case 'agent':
				return clusterShardingList;
			case 'readWriteProxy':
				return sentinelShardingList;
			default:
				return clusterShardingList;
		}
	};
	return (
		<SelectBlock
			options={optionsRender()}
			currentValue={value || sharding}
			onCallBack={(value: any) => {
				setSharding(value);
				onChange && onChange(value);
			}}
		/>
	);
}

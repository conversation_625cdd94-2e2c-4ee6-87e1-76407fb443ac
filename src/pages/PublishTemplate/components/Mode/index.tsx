import React from 'react';
import KafkaMode from './kafkaMode';
import ZooKeeperMode from './zookeeperMode';
import RocketMQMode from './RocketMQMode';
import ElasticsearchMode from './ElasticsearchMode/elasticsearchMode';
import RedisMode from './RedisMode';
import PgsqlMode from './PostgresqlMode';
import MysqlMode from './MysqlMode';
export default function Mode({
	type,
	judgeBackup
}: {
	type: string;
	judgeBackup?: boolean;
}): JSX.Element {
	if (type === 'kafka') {
		return <KafkaMode />;
	} else if (type === 'zookeeper') {
		return <ZooKeeperMode />;
	} else if (type === 'rocketmq') {
		return <RocketMQMode />;
	} else if (type === 'elasticsearch') {
		return <ElasticsearchMode judgeBackup={judgeBackup} />;
	} else if (type === 'redis') {
		return <RedisMode judgeBackup={judgeBackup} />;
	} else if (type === 'postgresql') {
		return <PgsqlMode judgeBackup={judgeBackup} />;
	} else if (type === 'mysql') {
		return <MysqlMode judgeBackup={judgeBackup} />;
	} else {
		return <></>;
	}
}

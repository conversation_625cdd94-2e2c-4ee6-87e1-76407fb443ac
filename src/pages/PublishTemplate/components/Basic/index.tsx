import React from 'react';
import { Form } from 'antd';
import TemplateName from '../TemplateName';
import Version from '../Version';
import InstanceQuota from '../InstanceQuota';
import StorageQuota from '../StorageQuota';
import Mode from '../Mode';

interface BasicInformationProps {
	type: string;
	chartVersion: string;
}
export default function BasicInformation(
	props: BasicInformationProps
): JSX.Element {
	const { type, chartVersion } = props;
	const formMode = Form.useWatch('mode');
	const instanceQuotaRender = () => {
		switch (type) {
			case 'elasticsearch':
				return <></>;
			case 'redis':
				if (formMode === 'cluster') {
					return (
						<>
							<InstanceQuota type={type} />
							<StorageQuota />
						</>
					);
				} else {
					return <></>;
				}
			case 'mysql':
				return (
					<>
						<InstanceQuota type={type} />
						<StorageQuota defaultStorage={5} />
					</>
				);
			default:
				return (
					<>
						<InstanceQuota type={type} />
						<StorageQuota type={type} />
					</>
				);
		}
	};
	return (
		<>
			<TemplateName />
			<Version type={type} chartVersion={chartVersion} />
			<Mode type={type} />
			{instanceQuotaRender()}
		</>
	);
}

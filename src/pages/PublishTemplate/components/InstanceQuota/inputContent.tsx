import React, { useState } from 'react';
import { Form, InputNumber } from 'antd';
import TableRadio from '@/pages/ServiceCatalog/components/TableRadio';
import { formItemLayout618 } from '@/utils/const';
import transUnit from '@/utils/transUnit';
import '../../index.less';

export default function InputContent({
	value,
	onChange,
	tableList
}: {
	value?: any;
	onChange?: (value: any) => void;
	tableList: QuotaTableItem[];
}): JSX.Element {
	const form = Form.useFormInstance();
	const formInstanceSpec = Form.useWatch('instanceSpec');
	const [specId, setSpecId] = useState<string>('1');
	const [initCpu] = useState(
		tableList.find((item) => item.id === value)?.cpu
	);
	const [initMemory] = useState(
		tableList.find((item) => item.id === value)?.memory
	);
	if (formInstanceSpec === 'General') {
		return (
			<>
				<Form.Item
					noStyle
					name="cpu"
					initialValue={
						initCpu && +transUnit.removeUnit(initCpu, ' Core')
					}
				></Form.Item>
				<Form.Item
					noStyle
					name="memory"
					initialValue={
						initMemory && +transUnit.removeUnit(initMemory, ' Gi')
					}
				></Form.Item>
				<div
					style={{
						marginTop: 16
					}}
				>
					<TableRadio
						id={value || specId}
						onCallBack={(value: any) => {
							setSpecId(value);
							onChange && onChange(value);
							const t = tableList.find((i) => i.id === value);
							if (t) {
								form.setFieldsValue({
									cpu: +transUnit.removeUnit(t.cpu, ' Core'),
									memory: +transUnit.removeUnit(
										t.memory,
										' Gi'
									)
								});
							} else {
								throw new Error('通用规格列表数据错误');
							}
						}}
						dataList={tableList}
					/>
				</div>
			</>
		);
	} else {
		return (
			<div className="instance-quota-customize-content">
				<Form.Item
					label="CPU"
					name="cpu"
					{...formItemLayout618}
					rules={[
						{
							required: true,
							message: '请输入自定义CPU配额，单位为Core'
						},
						{
							min: 0.1,
							type: 'number',
							message: `最小为0.1`
						}
					]}
				>
					<InputNumber
						style={{
							width: '100%'
						}}
						step={0.1}
						placeholder="请输入自定义CPU配额，单位为Core"
					/>
				</Form.Item>
				<Form.Item
					label="内存"
					{...formItemLayout618}
					rules={[
						{
							required: true,
							message: '请输入自定义内存配额，单位为Gi'
						},
						{
							min: 0.1,
							type: 'number',
							message: `最小为0.1`
						}
					]}
					name="memory"
				>
					<InputNumber
						style={{
							width: '100%'
						}}
						step={0.1}
						placeholder="请输入自定义内存配额，单位为Gi"
					/>
				</Form.Item>
			</div>
		);
	}
}

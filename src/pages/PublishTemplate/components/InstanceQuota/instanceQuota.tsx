import React, { useState } from 'react';
import { useParams } from 'react-router';
import { Form } from 'antd';
import SelectBlock from '@/components/SelectBlock';
import { instanceSpecList } from '@/utils/const';
import transUnit from '@/utils/transUnit';

interface InstanceQuotaContentProps {
	value?: string;
	tableList: QuotaTableItem[];
	onChange?: (value: string) => void;
}
export default function InstanceQuotaContent(
	props: InstanceQuotaContentProps
): JSX.Element {
	const params: ParamsProps = useParams();
	const { value, onChange, tableList } = props;
	const form = Form.useFormInstance();
	const specId = Form.useWatch('specId');
	const [instanceSpec, setInstanceSpec] = useState<string>('General');
	const onCallBack = (value: any) => {
		if (value === 'Customize') {
			// if (!(params.middlewareName && params.namespace)) {
			form.setFieldsValue({
				cpu: undefined,
				memory: undefined
			});
			// }
		} else {
			const temp = tableList.find((item) => item.id === specId);
			temp &&
				form.setFieldsValue({
					cpu: +transUnit.removeUnit(temp.cpu, ' Core'),
					memory: +transUnit.removeUnit(temp.memory, ' Gi')
				});
		}
		setInstanceSpec(value);
		onChange && onChange(value);
	};
	return (
		<SelectBlock
			options={instanceSpecList}
			currentValue={value || instanceSpec}
			onCallBack={onCallBack}
		/>
	);
}

import React from 'react';
import { Input, Form } from 'antd';
import { formItemLayout614, formItemLayout516 } from '@/utils/const';
import { checkTemplateNameExist } from '@/services/middleware';
import { useParams } from 'react-router';
import storage from '@/utils/storage';

const FormItem = Form.Item;

export default function TemplateName(): JSX.Element {
	const params: TemplateCreateDetailParams = useParams();
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	return (
		<FormItem
			{...(params.name === 'elasticsearch' || params.name === 'postgresql'
				? formItemLayout516
				: formItemLayout614)}
			name="name"
			label="模板名称"
			labelAlign="left"
			colon={false}
			validateTrigger={['onBlur', 'onSubmit', 'onChange']}
			rules={[
				{
					required: true,
					message: '模板名称不能为空'
				},
				{
					type: 'string',
					min: 1,
					max: 64,
					message: '请输入长度为1-64个字符的模板名称'
				},
				({ getFieldValue }) => ({
					validateTrigger: ['onBlur', 'onSubmit'],
					async validator(_, value) {
						if (!value) {
							return Promise.resolve();
						}
						const res = await checkTemplateNameExist({
							name: value,
							organId: organId,
							projectId: projectId,
							type: params.name
						});
						if (res.success) {
							if (res.data) {
								return Promise.reject(
									new Error('当前模板名称已存在！')
								);
							} else {
								return Promise.resolve();
							}
						} else {
							return Promise.reject(
								new Error('中间件模板名称校验失败！')
							);
						}
					}
				})
			]}
		>
			<Input placeholder="请输入模板名称" />
		</FormItem>
	);
}

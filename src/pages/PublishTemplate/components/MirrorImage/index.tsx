import React from 'react';
import { useState, useEffect } from 'react';
import { Form, Select, notification } from 'antd';
import { getMirror } from '@/services/common';

function MirrorImage(): JSX.Element {
	const formClusterAndNamespace = Form.useWatch('clusterAndNamespace');
	const [mirrorList, setMirrorList] = useState<MirrorItem[]>([]);
	useEffect(() => {
		if (formClusterAndNamespace?.[0]) {
			getData();
		}
	}, [formClusterAndNamespace?.[0]]);
	const getData = () =>
		getMirror({
			clusterId: formClusterAndNamespace[0]
		}).then((res) => {
			if (res.success) {
				setMirrorList(res.data.list);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	return (
		<Form.Item
			name="mirrorImageId"
			required
			label="镜像仓库"
			rules={[
				{
					required: true,
					message: '请选择镜像仓库'
				}
			]}
		>
			<Select
				placeholder="请选择镜像仓库"
				options={mirrorList.map((item: any) => {
					return {
						value: item.id,
						label: item.address
					};
				})}
			/>
		</Form.Item>
	);
}

export default MirrorImage;

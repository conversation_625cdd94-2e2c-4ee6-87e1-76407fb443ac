import React, { useEffect, useState } from 'react';
import SelectBlock from '@/components/SelectBlock';
import { AutoCompleteOptionItem } from '@/types/comment';
import { getMiddlewareVersions } from '@/services/common';
import { notification } from 'antd';

interface VersionContentProps {
	type: string;
	chartVersion: string;
	value?: string;
	onChange?: (value: string) => void;
	judgeBackup?: boolean;
	judgeDisaster?: boolean;
}
export default function VersionContent(
	props: VersionContentProps
): JSX.Element {
	const { type, chartVersion, value, onChange, judgeDisaster } = props;
	const [versionOriginData, setVersionOriginData] = useState<any>([]);
	const [versionFatherList, setVersionFatherList] = useState<
		AutoCompleteOptionItem[]
	>([]);
	const [fatherVersion, setFatherVersion] = useState<string>('');
	const [versionList, setVersionList] = useState<AutoCompleteOptionItem[]>(
		[]
	);
	const [version, setVersion] = useState<string>('');
	useEffect(() => {
		if (versionFatherList && versionFatherList.length) {
			if (value) {
				setFatherVersion(value?.split('.')[0]);
				setVersionList(
					versionOriginData
						.find(
							(item: any) =>
								item.masterVersion === value?.split('.')[0]
						)
						.slaveVersion.map((item: string) => {
							return { value: item, label: item };
						})
				);
				setVersion(value);
				onChange && onChange(value);
			} else {
				setFatherVersion(versionFatherList[0].value);
				setVersion(
					versionOriginData.find(
						(item: any) =>
							item.masterVersion === versionFatherList[0].value
					)?.slaveVersion[0]
				);
				onChange &&
					onChange(
						versionOriginData.find(
							(item: any) =>
								item.masterVersion ===
								versionFatherList[0].value
						)?.slaveVersion[0]
					);
				setVersionList(
					versionOriginData[0].slaveVersion.map((item: string) => {
						return { value: item, label: item };
					})
				);
			}
		}
	}, [value, versionFatherList]);
	useEffect(() => {
		getMiddlewareVersions({
			type,
			chartVersion: chartVersion || '1.8.7-beta.4'
		}).then((res) => {
			if (res.success) {
				if (res.data) {
					setVersionOriginData(res.data);
					const fatherList = res.data.map((item: any) => {
						return {
							value: item.masterVersion,
							label: item.masterVersion
						};
					});
					setVersionFatherList(fatherList);
				} else {
					notification.error({
						message: '错误',
						description: '没有获取到当前中间件版本'
					});
				}
			}
		});
	}, []);
	return (
		<>
			<SelectBlock
				options={versionFatherList}
				currentValue={fatherVersion}
				onCallBack={(value: any) => {
					setFatherVersion(value);
					setVersionList(
						versionOriginData
							.find((item: any) => item.masterVersion === value)
							.slaveVersion.map((item: string) => {
								return { value: item, label: item };
							})
					);
					setVersion(
						versionOriginData.find(
							(item: any) => item.masterVersion === value
						)?.slaveVersion[0]
					);
					onChange &&
						onChange(
							versionOriginData.find(
								(item: any) => item.masterVersion === value
							)?.slaveVersion[0]
						);
				}}
				disabled={judgeDisaster && type === 'mysql'}
			/>
			<div className="mt-16"></div>
			<SelectBlock
				options={versionList}
				currentValue={value || version}
				onCallBack={(value: any) => {
					setVersion(value);
					onChange && onChange(value);
				}}
				disabled={judgeDisaster && type === 'mysql'}
			/>
		</>
	);
}

import React, { useEffect, useState } from 'react';
import { ProP<PERSON>, ProContent, ProHeader } from '@/components/ProPage';
import { useHistory, useParams } from 'react-router-dom';
import { connect } from 'react-redux';
import Decimal from 'decimal.js';
import { Input, Select, Button, Form, Result, notification } from 'antd';
import FormBlock from '@/components/FormBlock';
import { renderFormItem } from '@/components/renderFormItem';
import {
	getDynamicFormData,
	getMiddlewareNameCheck
} from '@/services/middleware';
import { postMiddleware } from '@/services/middleware';
import pattern from '@/utils/pattern';
import { getMirror } from '@/services/common';

import {
	DynamicSendDataParams,
	DynamicCreateValueParams
} from '../../ServiceCatalog/catalog';
import { NamespaceItem } from '@/pages/ProjectDetail/projectDetail';
import { getProjectNamespace } from '@/services/project';
import { middlewareDetailProps } from '@/types/comment';
import SchedulerForm from '../../ServiceCatalog/components/SchedulerForm';
import MiddlewareName from '../../ServiceCatalog/components/MiddlewareName';
import storage from '@/utils/storage';
const { Item: FormItem } = Form;

interface ParamsProps {
	chartName: string;
	chartVersion: string;
	aliasName: string;
	version: string;
	clusterId: string;
	namespace: string;
	name: string;
	type: string;
}
function DynamicFormTemplate(): JSX.Element {
	const project = storage.getSession('project');
	const organization = storage.getSession('organization');
	const params: ParamsProps = useParams();
	const {
		chartName,
		chartVersion,
		aliasName,
		version,
		clusterId,
		namespace
	} = params;
	const [dataSource, setDataSource] = useState<any>();
	const [capabilities, setCapabilities] = useState<string[]>([]);
	// * 是否点击提交跳转至结果页
	const [commitFlag, setCommitFlag] = useState<boolean>(false);
	// * 发布成功
	const [successFlag, setSuccessFlag] = useState<boolean>(false);
	// * 发布失败
	const [errorFlag, setErrorFlag] = useState<boolean>(false);
	// * 创建返回的服务名称
	const [createData, setCreateData] = useState<middlewareDetailProps>();
	// * 创建失败返回的失败信息
	const [errorData, setErrorData] = useState<string>('');
	const [mirrorList, setMirrorList] = useState<any[]>([]);
	// * 命名空间
	const [namespaceList, setNamespaceList] = useState<NamespaceItem[]>([]);
	const [currentNamespace, setCurrentNamespace] = useState<NamespaceItem>();
	// * 调度策略
	const [scheduler, setScheduler] = useState<boolean>(false);
	const history = useHistory();
	const [form] = Form.useForm();
	const clusterAndNamespace = form.getFieldValue('clusterAndNamespace');

	useEffect(() => {
		if (clusterId && namespace) {
			const sendData = {
				clusterId: clusterId,
				chartName: chartName,
				chartVersion: chartVersion
			};
			getDynamicFormData(sendData).then((res) => {
				if (res.success) {
					const formatData = processData(res.data.questions);
					setDataSource(formatData);
					setCapabilities(res.data.capabilities);
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
			getMirror({
				clusterId: clusterId
			}).then((res) => {
				if (res.success) {
					setMirrorList(res.data.list);
				}
			});
		}
	}, [clusterId, namespace]);

	useEffect(() => {
		if (JSON.stringify(project) !== '{}') {
			getProjectNamespace({
				organId: organization.organId,
				projectId: project.projectId,
				clusterId: clusterId,
				withQuota: true
			}).then((res) => {
				if (res.success) {
					const list = res.data.filter(
						(item: NamespaceItem) => item.availableDomain !== true
					);
					setNamespaceList(list);
					if (namespace !== '*') {
						setCurrentNamespace(
							list.find(
								(item: NamespaceItem) => item.name === namespace
							)
						);
						form.setFieldsValue({
							namespace: namespace
						});
					}
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		}
	}, [project]);

	const processData = (array: any) => {
		const obj: any = {};
		array.forEach((item: any) => {
			obj[item.group] = [];
		});
		array.forEach((item: any) => {
			if (Object.keys(obj).includes(item.group)) {
				obj[item.group].push(item);
			}
		});
		return obj;
	};
	const handleChange = (value: string) => {
		setCurrentNamespace(
			namespaceList.find((item: NamespaceItem) => item.name === value)
		);
	};
	const childrenRender = (values: any) => {
		if (values) {
			const keys = Object.keys(values);
			return (
				<div>
					{keys.map((item) => {
						return (
							<FormBlock key={item} title={item}>
								<div className="w-50">
									<ul className="form-layout">
										{values[item].map((formItem: any) => {
											return (
												<React.Fragment
													key={formItem.variable}
												>
													{renderFormItem(
														formItem,
														form,
														clusterAndNamespace?.[0],
														clusterAndNamespace?.[1]
													)}
												</React.Fragment>
											);
										})}
									</ul>
								</div>
							</FormBlock>
						);
					})}
				</div>
			);
		}
	};

	const handleSubmit = async () => {
		await form.validateFields();
		const res = await getMiddlewareNameCheck({
			clusterId: clusterId,
			namespace: currentNamespace?.name || namespace,
			type: chartName,
			middlewareName: form.getFieldValue('name'),
			deployMod: 'container'
		});
		if (res.success) {
			if (res.data) {
				notification.warning({
					message: '提示',
					description: '当前中间件名称已存在！'
				});
				return;
			}
		} else {
			notification.error({
				message: '错误',
				description: res.errorMsg
			});
			return;
		}
		form.validateFields().then((values: DynamicCreateValueParams) => {
			const sendData: DynamicSendDataParams = {
				clusterId: clusterId,
				namespace: namespace === '*' ? values.namespace : namespace,
				type: chartName,
				chartName: chartName,
				chartVersion: chartVersion,
				version: values.version || version,
				name: values.name,
				aliasName: values.aliasName,
				description: values.description,
				annotations: values.annotations,
				labels: values.labels,
				scheduler: scheduler,
				capabilities: capabilities,
				deployMod: 'container'
			};
			// * 主机亲和特殊处理
			if (values.nodeAffinity) {
				if (values.nodeAffinity.length) {
					sendData.nodeAffinity = values.nodeAffinity.map((item) => {
						return {
							label: item.label,
							required: item.required,
							namespace: namespace
						};
					});
				} else {
					notification.error({
						message: '失败',
						description: '请选择主机亲和。'
					});
				}
			}
			// * 删除动态表单中多余的主机亲和相关的值
			const dynamicValues: any = {};
			for (const index in values) {
				if (
					index !== 'nodeAffinityLabel' &&
					index !== 'nodeAffinityForce' &&
					index !== 'nodeAffinity' &&
					index !== 'name' &&
					index !== 'aliasName' &&
					index !== 'annotations' &&
					index !== 'tolerations' &&
					index !== 'tolerationsLabels'
				) {
					if (index === 'image.repository') {
						dynamicValues['mirrorImageId'] = mirrorList.find(
							(item) => item.address === values[index]
						)
							? mirrorList
									.find(
										(item) => item.address === values[index]
									)
									.id.toString()
							: values[index];
					}
					dynamicValues[index] = values[index];
					if (index === 'storageClassName') {
						dynamicValues['storageClassName'] =
							values['storageClassName'].split('/')[0];
					}
				}
			}
			sendData.dynamicValues = dynamicValues;
			// * 主机容忍特殊处理
			if (values.tolerations) {
				if (values.tolerations.length) {
					sendData.tolerations = values.tolerations.map((item) => {
						return item.label;
					});
				} else {
					notification.error({
						message: '失败',
						description: '请选择主机容忍。'
					});
					return;
				}
			}
			setCommitFlag(true);
			postMiddleware(sendData).then((res) => {
				if (res.success) {
					setCreateData(res.data);
					setSuccessFlag(true);
					setErrorFlag(false);
					setCommitFlag(false);
				} else {
					setErrorData(res.errorMsg);
					setSuccessFlag(false);
					setErrorFlag(true);
					setCommitFlag(false);
				}
			});
		});
	};
	// * 结果页相关
	if (commitFlag) {
		return (
			<ProPage>
				<ProHeader />
				<ProContent>
					<Result
						title="发布中"
						extra={
							<Button
								type="primary"
								onClick={() => {
									history.push({
										pathname: `/project/${params.type}/${params.name}/${params.aliasName}`
									});
								}}
							>
								返回列表
							</Button>
						}
					/>
				</ProContent>
			</ProPage>
		);
	}
	if (successFlag) {
		return (
			<ProPage>
				<ProHeader />
				<ProContent>
					<Result
						status="success"
						title="发布成功"
						extra={[
							<Button
								key="list"
								type="primary"
								onClick={() => {
									history.push({
										pathname: `/project/${params.type}/${params.name}/${params.aliasName}`
									});
								}}
							>
								返回列表
							</Button>,
							<Button
								key="detail"
								onClick={() => {
									history.push({
										pathname: `/project/${params.type}/${chartName}/${aliasName}/basicInfo/${createData?.name}/${chartName}/${chartVersion}/${createData?.namespace}/${createData?.clusterId}`
									});
								}}
							>
								查看详情
							</Button>
						]}
					/>
				</ProContent>
			</ProPage>
		);
	}

	if (errorFlag) {
		return (
			<ProPage>
				<ProHeader />
				<ProContent>
					<Result
						status="error"
						title="发布失败"
						subTitle={errorData}
						extra={
							<Button
								type="primary"
								onClick={() => {
									history.push({
										pathname: `/project/${params.type}/${params.name}/${params.aliasName}`
									});
								}}
							>
								返回列表
							</Button>
						}
					/>
				</ProContent>
			</ProPage>
		);
	}
	return (
		<ProPage>
			<ProHeader
				title={`发布${chartName}服务`}
				onBack={() => window.history.back()}
			/>
			<ProContent>
				<Form form={form}>
					<FormBlock title="选择命名空间">
						<div className="w-50">
							<ul className="form-layout">
								<li className="display-flex flex-column">
									<label
										className="dynamic-form-name"
										style={{ paddingLeft: 8 }}
									>
										<span className="ne-required">
											命名空间
										</span>
									</label>
									<div className="form-content">
										<FormItem
											rules={[
												{
													required: true,
													message: '请选择命名空间'
												}
											]}
											name="namespace"
										>
											<Select
												placeholder="请选择命名空间"
												style={{ width: '390px' }}
												dropdownMatchSelectWidth={false}
												value={currentNamespace?.name}
												onChange={handleChange}
												disabled={namespace !== '*'}
											>
												{namespaceList.map((item) => {
													return (
														<Select.Option
															key={item.name}
															value={item.name}
														>
															{item.aliasName}
														</Select.Option>
													);
												})}
											</Select>
										</FormItem>
										{currentNamespace && (
											<span>{`当前命名空间剩余可分配配额为：CPU${
												currentNamespace?.quotas !==
												null
													? new Decimal(
															currentNamespace?.quotas?.cpu?.request
													  )
															.minus(
																new Decimal(
																	currentNamespace?.quotas?.cpu?.used
																)
															)
															.toFixed(1)
													: '-'
											}Core，内存${
												currentNamespace?.quotas !==
												null
													? new Decimal(
															currentNamespace?.quotas?.memory?.request
													  )
															.minus(
																new Decimal(
																	currentNamespace?.quotas?.memory?.used
																)
															)
															.toFixed(1)
													: '-'
											}GB`}</span>
										)}
									</div>
								</li>
							</ul>
						</div>
					</FormBlock>
					<FormBlock title="基本信息">
						<div className="w-50">
							<ul className="form-layout">
								<MiddlewareName
									clusterId={clusterId}
									namespace={currentNamespace?.name}
									type={chartName}
									form={form}
									isDynamic={true}
								/>
								<li className="display-flex flex-column">
									<label className="dynamic-form-name">
										<span>显示名称</span>
									</label>
									<div className="form-content">
										<FormItem
											rules={[
												{
													pattern: new RegExp(
														pattern.nickname
													),
													message:
														'请输入由汉字、字母、数字及“-”或“.”或“_”组成的2-80个字符'
												}
											]}
											name="aliasName"
										>
											<Input
												style={{ width: '390px' }}
												placeholder="请输入由汉字、字母、数字及“-”或“.”或“_”组成的2-80个字符"
											/>
										</FormItem>
									</div>
								</li>
								<li className="display-flex  flex-column">
									<label className="dynamic-form-name">
										<span>标签</span>
									</label>
									<div className="form-content">
										<FormItem
											rules={[
												{
													pattern: new RegExp(
														pattern.labels
													),
													message:
														'请输入key=value格式的标签，多个标签以英文逗号分隔'
												}
											]}
											name="labels"
										>
											<Input
												style={{ width: '390px' }}
												placeholder="请输入key=value格式的标签，多个标签以英文逗号分隔"
											/>
										</FormItem>
									</div>
								</li>
								<li className="display-flex  flex-column">
									<label className="dynamic-form-name">
										<span>备注</span>
									</label>
									<div className="form-content">
										<FormItem name="description">
											<Input.TextArea
												style={{ width: '390px' }}
												name="description"
												placeholder="请输入备注信息"
											/>
										</FormItem>
									</div>
								</li>
								<li className="display-flex  flex-column">
									<label className="dynamic-form-name">
										<span>注解</span>
									</label>
									<div className="form-content">
										<FormItem
											name="annotations"
											rules={[
												{
													pattern: new RegExp(
														pattern.labels
													),
													message:
														'请输入key=value格式的注解，多个注解以英文逗号分隔'
												}
											]}
										>
											<Input
												style={{ width: '390px' }}
												placeholder="请输入key=value格式的注解，多个注解以英文逗号分隔"
											/>
										</FormItem>
									</div>
								</li>
							</ul>
						</div>
					</FormBlock>
					<FormBlock title="调度策略">
						<div className="w-50">
							<ul className="form-layout">
								<SchedulerForm
									scheduler={scheduler}
									setScheduler={setScheduler}
								/>
							</ul>
						</div>
					</FormBlock>
					{childrenRender(dataSource)}
					<div className="dynamic-summit-box">
						<Button
							type="primary"
							style={{ marginRight: 8 }}
							onClick={handleSubmit}
						>
							提交
						</Button>
						<Button onClick={() => window.history.back()}>
							取消
						</Button>
					</div>
				</Form>
			</ProContent>
		</ProPage>
	);
}

export default DynamicFormTemplate;

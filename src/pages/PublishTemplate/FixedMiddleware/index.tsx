import React, { useRef, useState } from 'react';
import { Button, Form, notification } from 'antd';
import { useParams, useHistory } from 'react-router';
import { ResultStatusType } from 'antd/lib/result';
import { formItemLayout614, formItemLayout516 } from '@/utils/const';
import { createMiddlewaresTemplate } from '@/services/middleware';
import storage from '@/utils/storage';
import BasicInformation from '../components/Basic';
import DefaultPage from '../components/DefaultPage';
import Password from '../../PublishService/components/Password';
import FileLog from '../../PublishService/components/FileLog';
import StandardLog from '../../PublishService/components/StandardLog';
import ResultPage from '../../PublishService/components/ResultPage';
import AuditLog from '../../PublishService/components/AuditLog';
import PasswordEncryption from '../../PublishService/components/PasswordEncryption';
import '../index.less';

function PublishTemplate(): JSX.Element {
	const projectId = storage.getSession('projectId');
	const organId = storage.getSession('organId');
	const [form] = Form.useForm();
	const params: ParamsProps = useParams();
	const { name, aliasName, type } = params;
	const history = useHistory();
	const basicInfo = useRef<any>(null);
	const configData = useRef<any>(null);
	const [loading, setLoading] = useState<boolean>(true);
	const [title, setTitle] = useState<string>('创建中');
	const [status, setStatus] = useState<ResultStatusType>('info');

	const judgeGoNext = (step: number) => {
		if (step === 1) {
			if (
				form.getFieldsError().every((item) => item.errors.length === 0)
			) {
				return true;
			}
		} else if (step === 2 || step === 3) {
			if (
				form.getFieldsError().every((item) => item.errors.length === 0)
			) {
				return true;
			} else {
				return false;
			}
		}
	};
	const goNext = async (value: number) => {
		if (value === 1) {
			await form.validateFields();
			basicInfo.current = form.getFieldsValue();
			if (name === 'redis' && basicInfo.current.mode !== 'cluster') {
				if (!basicInfo.current.quota.redis.storageClassQuota) {
					notification.error({
						message: '提醒',
						description: '请输入存储配额！'
					});
					return Promise.reject();
				}
				if (basicInfo.current.quota.redis.storageClassQuota === 0) {
					notification.error({
						message: '提醒',
						description: '请输入存储配额！'
					});
					return Promise.reject();
				}
			}
			if (name === 'elasticsearch') {
				for (const i in basicInfo.current.quota) {
					if (
						i !== 'kibana' &&
						basicInfo.current.quota[i].disabled === false &&
						(!basicInfo.current.quota[i]?.storageClassQuota ||
							basicInfo.current.quota[i]?.storageClassQuota === 0)
					) {
						notification.error({
							message: '提醒',
							description: `请输入存储配额！`
						});
						return Promise.reject();
					}
				}
			}
			return judgeGoNext(value) ? true : false;
		} else if (value === 2) {
			await form.validateFields();
			configData.current = form.getFieldsValue();
			handleSubmit(configData.current);
			return judgeGoNext(value) ? true : false;
		} else {
			return false;
		}
	};

	const getExtra = () => {
		return (
			<Button
				type="primary"
				onClick={() => {
					history.push(
						`/project/${type}/${name}/${aliasName}/templateManage`
					);
				}}
			>
				返回列表
			</Button>
		);
	};
	const baseConfig = () => {
		switch (name) {
			case 'redis':
				return (
					<>
						<Password type={name} />
						<FileLog />
						<StandardLog />
					</>
				);
			case 'elasticsearch':
				return (
					<>
						<Password type={name} />
						<FileLog />
						<StandardLog />
					</>
				);
			case 'postgresql':
				return (
					<>
						<Password type={name} />
						<PasswordEncryption />
						<FileLog />
						<StandardLog />
						<AuditLog />
					</>
				);
		}
	};
	const steps = [
		{
			title: '基本信息',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...formItemLayout614}
				>
					<BasicInformation
						type={params.name}
						chartVersion={params.chartVersion}
					/>
				</Form>
			)
		},
		{
			title: '基础配置',
			content: (
				<Form
					form={form}
					labelAlign="left"
					colon={false}
					{...(name === 'postgresql'
						? formItemLayout516
						: formItemLayout614)}
				>
					{baseConfig()}
				</Form>
			)
		},
		{
			title: '创建结果',
			content: (
				<div id="publish-result-page">
					<ResultPage
						loading={loading}
						title={title}
						status={status}
						extra={getExtra()}
					/>
				</div>
			)
		}
	];
	const createTemplate = async (sendData: any) => {
		await createMiddlewaresTemplate(sendData)
			.then((res) => {
				setLoading(false);
				if (res.success) {
					setTitle('创建成功');
					setStatus('success');
				} else {
					setTitle('创建失败');
					setStatus('error');
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.catch(() => {
				setLoading(false);
				setTitle('创建失败');
				setStatus('error');
			});
	};
	const middlewareSpecialProperties = () => {
		let quotaTemp: any = {};
		switch (params.name) {
			case 'elasticsearch':
				for (const key in basicInfo?.current?.quota) {
					if (!basicInfo?.current?.quota[key].disabled) {
						quotaTemp[key] = {
							...basicInfo?.current?.quota[key],
							storageClassQuota:
								basicInfo?.current?.quota[key].storageClassQuota
						};
					}
				}
				return {
					quota: quotaTemp
				};
			case 'postgresql':
				return {
					quota: {
						postgresql: {
							num:
								basicInfo.current?.mode === '1m-ns'
									? basicInfo?.current?.num
									: Number(basicInfo.current?.mode.charAt(3)),
							cpu: basicInfo?.current?.cpu,
							memory: basicInfo?.current.memory,
							storageClassQuota:
								basicInfo?.current.storageClassQuota
						}
					},
					postgresqlParam: {
						passwordEncryption:
							configData?.current.passwordEncryption
					}
				};
			case 'redis':
				if (basicInfo.current.mode === 'cluster') {
					quotaTemp = {
						redis: {
							num: basicInfo?.current?.num,
							cpu: basicInfo?.current?.cpu,
							memory: basicInfo?.current.memory,
							storageClassQuota:
								basicInfo?.current?.storageClassQuota
						}
					};
				} else if (basicInfo?.current.mode === 'sentinel') {
					quotaTemp = {
						redis: {
							...basicInfo.current.quota.redis,
							num: basicInfo.current.num,
							storageClassQuota:
								basicInfo?.current?.storageClassQuota
						},
						sentinel: {
							...basicInfo.current.quota.sentinel
						}
					};
				} else if (basicInfo.current.mode === 'agent') {
					quotaTemp = {
						redis: {
							...basicInfo.current.quota.redis,
							num: basicInfo.current.num,
							storageClassQuota:
								basicInfo?.current?.storageClassQuota
						},
						proxy: {
							...basicInfo.current.quota.proxy
						}
					};
				} else {
					quotaTemp = {
						redis: {
							...basicInfo.current.quota.redis,
							num: basicInfo.current.num,
							storageClassQuota:
								basicInfo?.current?.storageClassQuota
						},
						sentinel: {
							...basicInfo.current.quota.sentinel
						},
						proxy: {
							...basicInfo.current.quota.proxy
						}
					};
				}
				return {
					quota: quotaTemp,
					redisParam: {
						sentinelPort:
							basicInfo?.current.quota?.sentinel?.sentinelPort,
						sentinelExporterPort:
							basicInfo?.current?.quota?.sentinel
								?.sentinelExporterPort,
						predixyPort:
							basicInfo?.current?.quota?.proxy?.predixyPort,
						predixyExporterPort:
							basicInfo?.current?.quota?.proxy
								?.predixyExporterPort
					},
					readWriteProxy: {
						enabled:
							basicInfo?.current?.mode === 'readWriteProxy' ||
							basicInfo?.current?.mode === 'agent'
								? true
								: false
					},
					mode:
						basicInfo?.current?.mode === 'cluster' ||
						basicInfo?.current?.mode === 'agent'
							? 'cluster'
							: 'sentinel'
				};
			default:
				return {
					quota: {
						[params.name]: {
							num: basicInfo?.current?.num,
							cpu: basicInfo?.current?.cpu,
							memory: basicInfo?.current.memory,
							storageClassQuota:
								basicInfo?.current.storageClassQuota
						}
					}
				};
		}
	};
	const handleSubmit = async (configData: any) => {
		await form.validateFields();
		const sendData: any = {
			projectId: projectId,
			organId: organId,
			name: basicInfo?.current?.name,
			chartName: params.name,
			chartVersion: basicInfo?.current?.chartVersion,
			type: params.name,
			version: basicInfo?.current?.version,
			mode: basicInfo?.current?.mode,
			filelogEnabled: configData?.filelogEnabled,
			stdoutEnabled: configData?.stdoutEnabled,
			audit: configData?.audit || false,
			password: configData?.password,
			deployMod: 'container',
			...middlewareSpecialProperties()
		};
		await createTemplate(sendData);
	};
	return (
		<DefaultPage
			title={`创建${aliasName}发布模板`}
			onBack={() => window.history.back()}
			steps={steps}
			goNext={goNext}
		/>
	);
}
export default PublishTemplate;

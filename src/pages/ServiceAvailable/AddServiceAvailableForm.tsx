import React, { useState, useEffect } from 'react';
import { useHistory, useParams } from 'react-router';
import {
	Form,
	Select,
	Input,
	Button,
	InputNumber,
	notification,
	Tag
} from 'antd';
import { ProPage, ProContent, ProHeader } from '@/components/ProPage';
import { PlusOutlined, MinusOutlined } from '@ant-design/icons';
import { IconFont } from '@/components/IconFont';

import {
	checkPort,
	createServiceIngress,
	getServices,
	updateServiceIngress
} from '@/services/ingress';
import pattern from '@/utils/pattern';
import {
	getIngresses,
	getIngressTCPPort,
	getNodePort
} from '@/services/common';
import { IngressItemProps } from '@/pages/ResourcePoolManagement/resource.pool';
import storage from '@/utils/storage';

import './index.less';

const FormItem = Form.Item;
const { Option } = Select;

function AddServiceAvailableForm(): JSX.Element {
	const [exposedWay, setExposedWay] = useState('Ingress');
	const [protocol, setProtocol] = useState(
		exposedWay === 'Ingress' ? 'HTTP' : 'TCP'
	);
	const [services, setServices] = useState<any>([]);
	const [selectedService, setSelectedService] = useState({
		serviceName: '',
		portDetailDtoList: []
	});
	const {
		middlewareName,
		clusterId,
		namespace,
		name,
		chartVersion,
		exposeId
	}: any = useParams();
	const [ingresses, setIngresses] = useState<IngressItemProps[]>([]);
	const [httpList, setHttpList] = useState<any[]>([
		{
			serviceName: '',
			servicePort: '',
			domain: '',
			path: '',
			portList: [],
			id: Math.random()
		}
	]);
	const [form] = Form.useForm();
	const record = storage.getLocal('availableRecord');
	const [ingressClassName, setIngressClassName] = useState<any>();
	const [ingressPortArray, setIngressPortArray] = useState<string[]>([]);
	const [nodePortArray, setNodePortArray] = useState<string[]>([]);
	const history = useHistory();

	useEffect(() => {
		if (middlewareName && !record) {
			getIngresses({ clusterId: clusterId }).then((res) => {
				if (res.success) {
					setIngresses(res.data);
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
			getExposedService();
			form.setFieldsValue({
				serve: middlewareName,
				exposeType: 'Ingress',
				protocol: 'HTTP'
			});
		} else if (record) {
			setExposedWay(
				record.tcpExposeType === 'traefik' ||
					record.tcpExposeType === 'nginx'
					? 'Ingress'
					: 'NodePort'
			);
			setProtocol(record.protocol);
			form.setFieldsValue({
				serve: record.middlewareName,
				exposeType:
					record.tcpExposeType === 'traefik' ||
					record.tcpExposeType === 'nginx'
						? 'Ingress'
						: 'NodePort',
				protocol: record.protocol
			});
			getIngresses({ clusterId: clusterId }).then((res) => {
				if (res.success) {
					setIngresses(res.data);
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
			getExposedService();
			if (record.protocol !== 'HTTP') {
				form.setFieldsValue({
					servicePort: record.serviceDTOList[0].servicePort,
					exposePort: Number(record.serviceDTOList[0].exposePort),
					ingressClassName: record.ingressClassName
				});
			} else {
				form.setFieldsValue({
					ingressClassName: record.ingressClassName
				});
				setHttpList(
					record.rules.map((item: any) => {
						return {
							id: Math.random(),
							domain: item.host,
							serviceName: item.ingressHttpPaths[0].serviceName,
							servicePort: item.ingressHttpPaths[0].servicePort,
							path: item.ingressHttpPaths[0].path
						};
					})
				);
				record.rules.map((item: any, index: number) => {
					form.setFieldsValue({
						['domain' + index]: item.host,
						['serviceName' + index]:
							item.ingressHttpPaths[0].serviceName,
						['servicePort' + index]:
							item.ingressHttpPaths[0].servicePort,
						['path' + index]: item.ingressHttpPaths[0].path
					});
				});
			}
		}
	}, []);

	useEffect(() => {
		getIngressTCPPort().then((res) => {
			if (res.success) {
				setIngressPortArray(res.data.split('-'));
			}
		});
		getNodePort().then((res) => {
			if (res.success) {
				setNodePortArray(res.data.split('-'));
			}
		});
		return () => {
			storage.getLocal('availableRecord') &&
				storage.removeLocal('availableRecord');
		};
	}, []);
	const onChange = (value: string) => {
		setExposedWay(value);
		if (value === 'NodePort') {
			setProtocol('TCP');
			if (form.getFieldValue('serviceName')) {
				const cur_service = services.find(
					(item: any) =>
						item.serviceName === form.getFieldValue('serviceName')
				);
				setSelectedService(cur_service);
			}
		} else {
			setProtocol('HTTP');
		}
	};

	const addHttpList = () => {
		setHttpList([...httpList, { id: Math.random() }]);
	};
	const copyHttpList = (index: number) => {
		const addItem = httpList[index];
		const list = [
			...httpList,
			{
				...addItem,
				id: Math.random() * 100,
				servicePort: '',
				portList: [],
				path: ''
			}
		];
		setHttpList(list);
		list.map((item: any, index: number) => {
			item.serviceName &&
				form.setFieldsValue({
					['serviceName' + index]: item.serviceName
				});
			item.domain &&
				form.setFieldsValue({
					['domain' + index]: item.domain
				});
			item.serviceName &&
				item.domain &&
				form.setFieldsValue({
					['serviceName' + index]: item.serviceName,
					['domain' + index]: item.domain
				});
		});
	};
	const deleteHttpList = (i: number) => {
		const list = httpList.filter((item) => item.id !== i);
		setHttpList(list);
	};

	const onChangeHttp = (
		value: string,
		record: any,
		type: string,
		index?: number
	) => {
		let portList: any = [];
		if (type === 'serviceName') {
			form.setFieldValue(`servicePort${index}`, undefined);
			onServiceChange(value);
			const cur_service = services.find(
				(item: any) => item.serviceName === value
			);
			portList = cur_service.portDetailDtoList;
		}
		const list = httpList.map((item) => {
			if (item.id === record.id) {
				if (type === 'serviceName') {
					item.portList = portList;
				}
				item[type] = value;
				return item;
			} else {
				return item;
			}
		});
		setHttpList(list);
	};
	const handleIngressChange = (value: string) => {
		const cur = ingresses.find((item) => item.ingressClassName === value);
		setIngressClassName(cur);
	};
	const onCreate = (values: any) => {
		let old = {};
		if (record.exposeType === 'Ingress' && record.protocol === 'TCP')
			old = {
				oldServicePort: record.serviceList[0].servicePort,
				oldExposePort: record.serviceList[0].exposePort,
				oldServiceName: record.serviceList[0].serviceName
			};
		const curIngressClass = ingresses.find(
			(item) => item.ingressClassName === values.ingressClassName
		);
		const tcpExposeTypeTemp =
			exposedWay === 'Ingress'
				? curIngressClass?.type === 'nginx'
					? 'nginx'
					: 'traefik'
				: 'nodePort';
		const serviceDTOList = [
			{
				exposePort: values.exposePort,
				serviceName: values.serviceName,
				servicePort: values.servicePort
			}
		];
		let rules = null;
		if (values.protocol === 'HTTP') {
			rules = [
				{
					host: httpList?.[0].domain,
					ingressHttpPaths: httpList
				}
			];
		}
		const sendData: any = {
			clusterId,
			namespace,
			middlewareName,
			chartVersion,
			middlewareType: name,
			exposeType: '',
			protocol: values.protocol ? values.protocol : 'TCP',
			ingressClassName: values.ingressClassName,
			tcpExposeType: tcpExposeTypeTemp,
			serviceDTOList: serviceDTOList,
			rules: rules
		};
		if (record) {
			sendData.exposeId = exposeId;
			updateServiceIngress(sendData).then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '对外路由修改成功'
					});
					window.history.back();
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		} else {
			createServiceIngress(sendData).then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '对外路由添加成功'
					});
					window.history.back();
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		}
	};

	const onProtocolChange = (value: string) => {
		setProtocol(value);
	};

	const getExposedService = () => {
		const sendData = {
			clusterId,
			namespace,
			middlewareName: middlewareName,
			middlewareType: name
		};
		getServices(sendData).then((res) => {
			if (res.success) {
				setServices(res.data);
				if (record?.exposeType === 'TCP' && res.data) {
					if (
						res.data.find(
							(item: any) =>
								item.serviceName ===
								record.serviceDTOList[0].serviceName
						)
					) {
						setSelectedService(
							res.data.find(
								(item: any) =>
									item.serviceName ===
									record.serviceDTOList[0].serviceName
							)
						);
						form.setFieldsValue({
							serviceName: res.data.find(
								(item: any) =>
									item.serviceName ===
									record.serviceDTOList[0].serviceName
							).serviceName
						});
					} else {
						setSelectedService(res.data[0]);
						form.setFieldsValue({
							serviceName: res.data[0].serviceName
						});
					}
				} else {
					if (
						res.data.find(
							(item: any) =>
								item.serviceName ===
								record?.serviceDTOList?.[0].serviceName
						)
					) {
						setSelectedService(
							res.data.find(
								(item: any) =>
									item.serviceName ===
									record?.serviceDTOList?.[0].serviceName
							)
						);
						form.setFieldsValue({
							serviceName: res.data.find(
								(item: any) =>
									item.serviceName ===
									record.serviceDTOList[0].serviceName
							).serviceName
						});
					} else {
						res.data && setSelectedService(res.data[0]);
						form.setFieldsValue({
							serviceName: res.data[0].serviceName
						});
					}
				}
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const onServiceChange = (value: string) => {
		const list = services.filter((item: any) => item.serviceName === value);
		form.setFieldValue('servicePort', undefined);
		setSelectedService(list[0]);
	};
	const onOk = () => {
		form.validateFields().then((data) => {
			const value = {
				...data,
				selectedService
			};

			onCreate(value);
		});
	};

	return (
		<ProPage className="add-service">
			<ProHeader
				title={record ? '编辑服务暴露' : '新增服务暴露'}
				onBack={() => history.goBack()}
			/>
			<ProContent>
				<Form form={form} validateTrigger={['onBlur', 'onSubmit']}>
					<h2>服务选择</h2>
					<FormItem
						label="选择服务"
						labelAlign="left"
						rules={[
							{ required: true, message: '请选择服务名称！' }
						]}
						style={{ margin: '8px 0  16px 16px' }}
						name="serve"
					>
						<Input
							disabled
							value={middlewareName}
							style={{ width: '340px' }}
						/>
					</FormItem>
					<h2>选择暴露方式及对象</h2>
					<FormItem
						label="暴露方式"
						labelAlign="left"
						required
						style={{ margin: '8px 0  16px 16px' }}
					>
						<FormItem
							name="exposeType"
							rules={[
								{ required: true, message: '请选择暴露方式！' }
							]}
						>
							<Select
								onChange={onChange}
								style={{ width: '120px' }}
								value={exposedWay}
								disabled={!!record}
							>
								<Option value="Ingress" key="Ingress">
									Ingress
								</Option>
								<Option value="NodePort" key="NodePort">
									NodePort
								</Option>
							</Select>
						</FormItem>
						{exposedWay === 'Ingress' && (
							<FormItem
								name="ingressClassName"
								rules={[
									{
										required: true,
										message: '请选择Ingress！'
									}
								]}
							>
								<Select
									placeholder="请选择一个ingress"
									style={{ width: '250px' }}
									disabled={
										record && record.protocol === 'TCP'
									}
									value={ingressClassName?.name}
									onChange={handleIngressChange}
									dropdownMatchSelectWidth={false}
								>
									{ingresses.map(
										(item: IngressItemProps, index) => {
											return (
												<Option
													key={index}
													value={
														item.ingressClassName
													}
												>
													<div className="flex-space-between">
														{item.ingressClassName}
														<Tag
															color={
																item.type ===
																'nginx'
																	? 'cyan'
																	: 'green'
															}
														>
															{item.type}
														</Tag>
													</div>
												</Option>
											);
										}
									)}
								</Select>
							</FormItem>
						)}
						{exposedWay === 'Ingress' && (
							<FormItem name="protocol">
								<Select
									onChange={onProtocolChange}
									style={{ width: '120px' }}
									value={protocol}
									disabled={!!record}
								>
									<Option value="HTTP">HTTP</Option>
									<Option value="TCP">TCP</Option>
								</Select>
							</FormItem>
						)}
						{protocol === 'TCP' && (
							<FormItem
								name="exposePort"
								rules={[
									{
										required:
											exposedWay === 'Ingress'
												? true
												: false,
										message: '对外端口不能为空！'
									},
									({ getFieldValue }) => ({
										validateTrigger: ['onBlur'],
										async validator(_, value) {
											if (exposedWay === 'Ingress') {
												if (
													ingressClassName?.type ===
													'traefik'
												) {
													if (
														ingressClassName.traefikPortList.some(
															(item: any) => {
																if (
																	value >=
																		item.startPort &&
																	value <=
																		item.endPort
																) {
																	return true;
																}
															}
														)
													) {
														const res =
															await checkPort({
																clusterId,
																namespace,
																middlewareName,
																middlewareType:
																	name,
																chartVersion,
																startPort:
																	value + '',
																endPort:
																	value + ''
															});
														if (res.success) {
															if (
																res.data ===
																'[]'
															) {
																return Promise.resolve();
															} else {
																return Promise.reject(
																	new Error(
																		`${res.data}端口冲突`
																	)
																);
															}
														} else {
															return Promise.reject(
																new Error(
																	'端口校验失败'
																)
															);
														}
													} else {
														const error_string =
															ingressClassName.traefikPortList
																.map(
																	(
																		item: any
																	) =>
																		`${item.startPort}-${item.endPort}`
																)
																.join(',');
														return Promise.reject(
															new Error(
																`当前端口范围为：${error_string}`
															)
														);
													}
												} else {
													if (
														value >=
															Number(
																ingressPortArray[0]
															) &&
														value <=
															Number(
																ingressPortArray[1]
															)
													) {
														const res =
															await checkPort({
																clusterId,
																namespace,
																middlewareName,
																middlewareType:
																	name,
																chartVersion,
																startPort:
																	value + '',
																endPort:
																	value + ''
															});
														if (res.success) {
															if (
																res.data ===
																'[]'
															) {
																return Promise.resolve();
															} else {
																return Promise.reject(
																	new Error(
																		`${res.data}端口冲突`
																	)
																);
															}
														} else {
															return Promise.reject(
																'端口校验失败'
															);
														}
													} else {
														return Promise.reject(
															new Error(
																`当前端口范围为${ingressPortArray[0]}-${ingressPortArray[1]}`
															)
														);
													}
												}
											} else {
												if (
													value >=
													Number(
														nodePortArray[0] &&
															value <=
																Number(
																	nodePortArray[1]
																)
													)
												) {
													const res = await checkPort(
														{
															clusterId,
															namespace,
															middlewareName,
															middlewareType:
																name,
															chartVersion,
															startPort:
																value + '',
															endPort: value + ''
														}
													);
													if (res.success) {
														if (res.data === '[]') {
															return Promise.resolve();
														} else {
															return Promise.reject(
																new Error(
																	`${res.data}端口冲突`
																)
															);
														}
													} else {
														return Promise.reject(
															'端口校验失败'
														);
													}
												} else {
													return Promise.reject(
														new Error(
															`当前端口范围为${nodePortArray[0]}-${nodePortArray[1]}`
														)
													);
												}
											}
										}
									})
								]}
								labelAlign="left"
							>
								<InputNumber
									placeholder="请输入对外端口"
									style={{ width: '200px' }}
								/>
							</FormItem>
						)}
					</FormItem>
					{protocol !== 'HTTP' && (
						<FormItem
							label="选择暴露对象"
							required
							labelAlign="left"
						>
							<FormItem
								name="serviceName"
								rules={[
									{
										required: true,
										message: '请选择暴露对象'
									}
								]}
							>
								<Select
									dropdownMatchSelectWidth={false}
									onChange={onServiceChange}
									style={{ width: '100%' }}
									value={selectedService.serviceName}
									placeholder="请选择Service"
									disabled={!!record}
								>
									{services &&
										services.map((item: any) => {
											return (
												<Option
													key={item.serviceName}
													value={item.serviceName}
												>
													{item.serviceName}
												</Option>
											);
										})}
								</Select>
							</FormItem>
							<FormItem
								name="servicePort"
								rules={[
									{
										required: true,
										message: '请选择暴露端口'
									}
								]}
							>
								<Select
									style={{ width: '100%' }}
									placeholder="请选择端口"
									dropdownMatchSelectWidth={false}
									disabled={!!record}
								>
									{selectedService.portDetailDtoList &&
										selectedService.portDetailDtoList.map(
											(item: any) => {
												return (
													<Option
														key={item.port}
														value={item.port}
													>
														{item.port}
													</Option>
												);
											}
										)}
								</Select>
							</FormItem>
						</FormItem>
					)}
					{protocol === 'HTTP' &&
						httpList.map((item, index) => {
							return (
								<div className="http-form" key={index}>
									<FormItem
										label="选择暴露对象"
										required
										labelAlign="left"
									>
										<FormItem
											name={'serviceName' + index}
											rules={[
												{
													required: true,
													message: '请选择暴露对象'
												}
											]}
										>
											<Select
												onChange={(value) =>
													onChangeHttp(
														value,
														item,
														'serviceName',
														index
													)
												}
												value={item.serviceName}
												style={{ width: '200px' }}
												placeholder="请选择Service"
												dropdownMatchSelectWidth={false}
												disabled={!!record}
											>
												{services &&
													services.map(
														(item: any) => {
															return (
																<Option
																	key={
																		item.serviceName
																	}
																	value={
																		item.serviceName
																	}
																>
																	{
																		item.serviceName
																	}
																</Option>
															);
														}
													)}
											</Select>
										</FormItem>
										<FormItem
											name={'servicePort' + index}
											rules={[
												{
													required: true,
													message: '请选择暴露端口'
												}
											]}
										>
											<Select
												style={{ width: '120px' }}
												placeholder="请选择端口"
												value={item.servicePort}
												onChange={(value) =>
													onChangeHttp(
														value,
														item,
														'servicePort'
													)
												}
												disabled={!!record}
												dropdownMatchSelectWidth={false}
											>
												{item.portList &&
													item.portList.map(
														(item: any) => {
															return (
																<Option
																	key={
																		item.port
																	}
																	value={
																		item.port
																	}
																>
																	{item.port}
																</Option>
															);
														}
													)}
											</Select>
										</FormItem>
									</FormItem>
									<FormItem
										label="域名及路径"
										labelAlign="left"
										required
									>
										<FormItem
											name={'domain' + index}
											rules={[
												{
													required: true,
													message: '请选择暴露对象'
												},
												{
													pattern: new RegExp(
														pattern.domain,
														'i'
													),
													message:
														'请输入正确的域名格式！'
												}
											]}
										>
											<Input
												placeholder="请输入域名"
												value={item.domain}
												onChange={(e) =>
													onChangeHttp(
														e.target.value,
														item,
														'domain'
													)
												}
												disabled={!!record}
											/>
										</FormItem>
										<FormItem
											name={'path' + index}
											rules={[
												{
													required: true,
													message: '路径不能为空！'
												},
												{
													pattern: new RegExp(
														pattern.path,
														'i'
													),
													message:
														'请填写正确的URL映射格式，如：/path'
												}
											]}
										>
											<Input
												placeholder="请输入域名后的路径"
												value={item.path}
												onChange={(e) =>
													onChangeHttp(
														e.target.value,
														item,
														'path'
													)
												}
												disabled={!!record}
											/>
										</FormItem>
									</FormItem>
									<div className="http-btn">
										<Button
											style={{ marginLeft: '8px' }}
											onClick={() => copyHttpList(index)}
											disabled={httpList.length >= 10}
											icon={
												<IconFont
													type="icon-fuzhi1"
													style={{ color: '#0064C8' }}
												/>
											}
										></Button>
										<Button
											style={{ marginLeft: '8px' }}
											onClick={() => addHttpList()}
											disabled={httpList.length >= 10}
											icon={
												<PlusOutlined
													style={{ color: '#0064C8' }}
												/>
											}
										></Button>
										<Button
											style={{ marginLeft: '8px' }}
											onClick={() =>
												deleteHttpList(item.id)
											}
											disabled={httpList.length === 1}
											icon={
												<MinusOutlined
													style={{ color: '#0064C8' }}
												/>
											}
										></Button>
									</div>
								</div>
							);
						})}
				</Form>
				<div className="alarm-bottom">
					<Button
						onClick={onOk}
						type="primary"
						style={{ marginRight: '9px' }}
					>
						确认
					</Button>
					<Button
						onClick={() => {
							window.history.back();
						}}
					>
						取消
					</Button>
				</div>
			</ProContent>
		</ProPage>
	);
}

export default AddServiceAvailableForm;

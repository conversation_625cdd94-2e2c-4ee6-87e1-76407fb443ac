.add-service {
	h2 {
		font-size: @font-2;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: @font-weight;
		color: @text-color-title;
		line-height: 22px;
		margin: 16px 0;
		display: flex;
		align-items: center;
		&::before {
			content: '';
			display: inline-block;
			width: 1px;
			height: 14px;
			border: 1px solid #0064c8;
			margin-right: 5px;
		}
	}

	.ant-form-item {
		margin: 0 0 16px 16px;
		display: flex;
		.ant-form-item-control-input-content {
			display: flex;
			.ant-form-item-control {
				display: block;
			}
			.ant-form-item {
				&:first-of-type {
					margin: 0;
				}
				margin-bottom: 0;
			}
		}
		.ant-form-item-label {
			width: 100px;
		}
	}
	.http-form {
		display: flex;
		line-height: 30px;
		.ant-btn {
			width: 24px;
			height: 24px;
		}
	}
}
.address {
	max-width: 200px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.middleware-name {
	& > div {
		max-width: 100px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
}

import React, { useEffect, useState } from 'react';
import { Drawer, Table, Modal, Form, Input, notification, Button } from 'antd';
import { useHistory } from 'react-router';
import { connect } from 'react-redux';
import { StoreState } from '@/types';
import { ProHeader, ProPage, ProContent } from '@/components/ProPage';
import ProTable from '@/components/ProTable';
import Actions from '@/components/Actions';
import { createArea, deleteArea, getAreas, updateArea } from '@/services/area';
import { formItemLayout618 } from '@/utils/const';
import Auth from '@/components/Auth';
import storage from '@/utils/storage';
const { confirm } = Modal;
const LinkButton = Actions.LinkButton;

function AreaManagement({ buttonList }: { buttonList: any[] }): JSX.Element {
	const history = useHistory();
	const [open, setOpen] = useState<boolean>(false);
	const [formOpen, setFormOpen] = useState<boolean>(false);
	const [editData, setEditData] = useState<AreaItem>();
	const [areas, setAreas] = useState<AreaItem[]>([]);
	const [dataSource, setDataSource] = useState<AreaItem[]>([]);
	const [loading, setLoading] = useState<boolean>(false);
	const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
	const [keyword, setKeyword] = useState<string>('');
	const [form] = Form.useForm();
	useEffect(() => {
		getData();
	}, []);
	const columns = [
		{
			dataIndex: 'nickname',
			title: '集群名称',
			render: (value: any, record: AreaClusterItem) => {
				const canJump = buttonList?.find(
					(item: any) => item.name === 'areaManagementJumpCluster'
				);
				return (
					<span
						className={canJump ? 'name-link' : ''}
						onClick={() => {
							if (!canJump) return;
							history.push(
								`/platform/clusterManagement/resourcePoolDetail/${record.clusterId}`
							);
							storage.setSession(
								'cluster-detail-current-tab',
								'overview'
							);
						}}
					>
						{value}
					</span>
				);
			}
		},
		{
			dataIndex: 'nsNumber',
			title: '命名空间数',
			render: (value: any, record: AreaClusterItem) => {
				const canJump = buttonList?.find(
					(item: any) => item.name === 'areaManagementJumpNamespace'
				);
				return (
					<span
						className={canJump ? 'name-link' : ''}
						onClick={() => {
							if (!canJump) return;
							history.push(
								`/platform/clusterManagement/resourcePoolDetail/${record.clusterId}`
							);
							storage.setSession(
								'cluster-detail-current-tab',
								'namespace'
							);
						}}
					>
						{value}
					</span>
				);
			}
		},
		{
			dataIndex: 'createTime',
			title: '创建时间'
		}
	];
	const getData = () => {
		setLoading(true);
		getAreas()
			.then((res) => {
				if (res.success) {
					setAreas(res.data);
					setDataSource(
						res.data.filter(
							(item) =>
								item.areaId?.includes(keyword) ||
								item.areaName?.includes(keyword) ||
								item.description?.includes(keyword)
						)
					);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const Operation = {
		primary: (
			<Auth code="areaManagementAdd">
				<Button
					type="primary"
					onClick={() => {
						setEditData(undefined);
						setFormOpen(true);
					}}
				>
					新增
				</Button>
			</Auth>
		)
	};
	const deleteAreaFunc = (record: AreaItem) => {
		confirm({
			title: '操作确认',
			content: (
				<>
					<p>删除区域后无法恢复</p>
					<p>是否确定删除？</p>
				</>
			),
			onOk: async () => {
				await deleteArea({ areaId: record.areaId }).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '区域删除成功'
						});
						getData();
					} else {
						notification.error({
							message: '失败',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};
	const onSearch = (value: string) => {
		const lt = areas.filter(
			(item) =>
				item.areaId?.includes(value) ||
				item.areaName?.includes(value) ||
				item.description?.includes(value)
		);
		setDataSource(lt);
	};
	const onOk = async () => {
		await form.validateFields();
		const values = form.getFieldsValue();
		setConfirmLoading(true);
		if (editData) {
			const res = await updateArea({
				...values,
				areaId: editData.areaId
			});
			if (res.success) {
				notification.success({
					message: '成功',
					description: '区域编辑成功'
				});
				getData();
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		} else {
			const res = await createArea(values);
			if (res.success) {
				notification.success({
					message: '成功',
					description: '区域新增成功'
				});
				getData();
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		}
		setConfirmLoading(false);
		setFormOpen(false);
	};
	const actionRender = (value: any, record: AreaItem) => {
		return (
			<Actions>
				<LinkButton
					code="areaManagementUpdate"
					onClick={() => {
						setEditData(record);
						setFormOpen(true);
					}}
				>
					编辑
				</LinkButton>
				<LinkButton
					code="areaManagementDelete"
					title={
						record.clusters && record.clusters?.length !== 0
							? '当前区域内仍存在使用中的集群，无法删除'
							: ''
					}
					disabled={
						record.clusters ? record.clusters.length !== 0 : false
					}
					onClick={() => deleteAreaFunc(record)}
				>
					删除
				</LinkButton>
			</Actions>
		);
	};
	const countRender = (value: any, record: AreaItem) => {
		return (
			<div
				className="name-link"
				onClick={() => {
					setEditData(record);
					setOpen(true);
				}}
			>
				{record.clusters?.length || 0}
			</div>
		);
	};
	return (
		<ProPage>
			<ProHeader title="区域管理" />
			<ProContent>
				<ProTable
					rowKey="areaId"
					dataSource={dataSource}
					operation={Operation}
					search={{
						placeholder: '请输入搜索关键字',
						onSearch: onSearch,
						value: keyword,
						onChange: (e) => setKeyword(e.target.value)
					}}
					showRefresh
					onRefresh={getData}
					loading={loading}
				>
					<ProTable.Column
						dataIndex="areaName"
						title="区域名称"
						ellipsis={true}
						width={300}
					/>
					<ProTable.Column
						dataIndex="areaId"
						title="区域ID"
						width={200}
					/>
					<ProTable.Column
						dataIndex="description"
						title="区域描述"
						render={(value) => value ?? '/'}
						ellipsis={true}
					/>
					<ProTable.Column
						dataIndex="count"
						title="绑定集群数"
						width={100}
						render={countRender}
					/>
					<ProTable.Column
						dataIndex="action"
						title="操作"
						width={100}
						render={actionRender}
					/>
				</ProTable>
				<Drawer
					open={open}
					onClose={() => {
						setEditData(undefined);
						setOpen(false);
					}}
					destroyOnClose={true}
					width={600}
					title="绑定集群列表"
				>
					<Table
						rowKey="clusterId"
						columns={columns}
						dataSource={editData?.clusters ?? []}
					/>
				</Drawer>
				<Modal
					open={formOpen}
					title={editData ? '编辑区域' : '新增区域'}
					onCancel={() => setFormOpen(false)}
					destroyOnClose={true}
					onOk={onOk}
					confirmLoading={confirmLoading}
				>
					<Form
						form={form}
						preserve={false}
						{...formItemLayout618}
						colon={false}
						labelAlign="left"
					>
						<Form.Item
							name="areaName"
							label="区域名称"
							rules={[
								{ required: true, message: '请输入区域名称' },
								{
									type: 'string',
									min: 1,
									max: 64,
									message: '请输入长度为1-64个字符的区域名称'
								}
							]}
							initialValue={editData && editData.areaName}
						>
							<Input placeholder="请输入区域名称" />
						</Form.Item>
						<Form.Item
							name="description"
							label="区域描述"
							initialValue={editData && editData.description}
						>
							<Input.TextArea
								rows={3}
								placeholder="请输入区域描述"
							/>
						</Form.Item>
					</Form>
				</Modal>
			</ProContent>
		</ProPage>
	);
}

export default connect((state: StoreState) => ({
	buttonList: state.auth.buttonList
}))(AreaManagement);

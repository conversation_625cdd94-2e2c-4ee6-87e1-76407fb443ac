import React, { useEffect, useState } from 'react';
import { Checkbox, Row, Col, Space } from 'antd';
import './index.less';
import { useParams } from 'react-router';

function CheckboxGroup(props: any): JSX.Element {
	const { list, allcheckedList, onChange, totalCheckedList } = props;
	const [options, setOptions] = useState([...list]);
	const [indeterminate, setIndeterminate] = useState(false);
	const [checkAll, setCheckAll] = useState(false);
	const { isEdit }: { isEdit: string } = useParams();

	useEffect(() => {
		setIndeterminate(
			!!allcheckedList?.length &&
				[...new Set(allcheckedList)].length < options.length
		);
		if (
			allcheckedList &&
			list &&
			[...new Set(allcheckedList)].length >= list.length
		) {
			setCheckAll(true);
		} else {
			setCheckAll(false);
		}
	}, [allcheckedList, list]);

	const onCheckAllChange = (e: any) => {
		const newOptions = [...options];
		let newCheckedList: any = [];
		newOptions.forEach((item) => {
			item.checked = e.target.checked;
			if (e.target.checked) {
				newCheckedList.push(item.id);
			} else {
				newCheckedList = [];
			}
		});
		setOptions(newOptions);
		onChange(newCheckedList, newOptions, {
			name: 'all',
			value: e.target.value,
			checked: e.target.checked,
			module: options?.[0].module,
			middlewareType: options?.[0].middlewareType
		});
	};

	const onItemChange = (e: any, item: any) => {
		let types = [...new Set(allcheckedList)];
		const checked = e.target.checked;
		const itemValue = e.target.value;
		const newOptions = [...options];
		if (!checked && item?.name?.includes('Query')) {
			types = [];
			newOptions.forEach((item) => {
				item.checked = false;
			});
		} else {
			if (checked) {
				if (!allcheckedList?.length) {
					const id = options?.find((item) =>
						item.name?.includes('Query')
					)?.id;
					types.push(id);
				}

				if (!(allcheckedList || []).includes(itemValue)) {
					types.push(itemValue);
				}
			} else {
				types = types.filter((item) => item !== itemValue);
			}
			newOptions.forEach((item) => {
				item.checked = types.includes(item.id);
			});
		}
		setOptions(newOptions);
		onChange(types, newOptions, item);
	};

	return (
		<>
			{options.length !== 1 && options[0].aliasName !== '全选' ? (
				<Checkbox
					className={'checkBox-group'}
					indeterminate={indeterminate}
					onChange={onCheckAllChange}
					checked={checkAll}
					disabled={
						isEdit === 'false' ||
						options.some((item: any) => item.defaultMenu)
					}
				>
					<label className="ml-8">全选</label>
				</Checkbox>
			) : null}
			<Row>
				{options.map((item) => (
					<Col key={item.id} span={24}>
						<Space>
							<Checkbox
								value={item.id}
								style={{ width: '100%' }}
								disabled={
									item.defaultMenu || isEdit === 'false'
								}
								checked={totalCheckedList?.includes(item.id)}
								onChange={(e) => onItemChange(e, item)}
							>
								<label className="ml-8">
									{item.aliasName}
									{item.comment ? `(${item.comment})` : ''}
								</label>
							</Checkbox>
						</Space>
					</Col>
				))}
			</Row>
		</>
	);
}

export default CheckboxGroup;

import { <PERSON><PERSON><PERSON>nt, ProHeader, ProPage } from '@/components/ProPage';
import type { DataNode } from 'antd/es/tree';
import { useHistory } from 'react-router';
import { Button, Space, Spin, Tree, notification } from 'antd';
import React, { useEffect, useState } from 'react';
import {
	getRoleMenus,
	getRoleMenusByRoleId,
	updateRole
} from '@/services/role';
import storage from '@/utils/storage';

import './index.less';
export default function AllotMenu(): JSX.Element {
	const history = useHistory();
	const [treeData, setTreeData] = useState<DataNode[]>([]);
	const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
	const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
	const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);
	const [spinning, setSpinning] = useState<boolean>(false);
	const [loading, setLoading] = useState<boolean>(false);
	useEffect(() => {
		setSpinning(true);
		async function getData() {
			const res = await getRoleMenus();
			const haveChildren: number[] = [];
			if (res.success) {
				const expandedKeysTemp: any = [];
				const list = res.data.map((item: any) => {
					expandedKeysTemp.push(item.id);
					item.subMenu && haveChildren.push(item.id);
					const result: any = {};
					result.title = item.aliasName;
					result.key = item.id;
					result.weight = item.weight;
					result.parentId = item.parentId;
					const childrenTemp = item.subMenu?.map((i: any) => {
						expandedKeysTemp.push(i.id);
						return {
							title: i.aliasName,
							key: i.id,
							weight: i.weight,
							parentId: i.parentId
						};
					});
					result.children = childrenTemp;
					return result;
				});
				setTreeData(list);
				setExpandedKeys(expandedKeysTemp);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
			const role = JSON.parse(storage.getSession('rolePower'));
			const res2 = await getRoleMenusByRoleId({ roleId: role.id });
			if (res2.success) {
				const checkedKeysTemp: any = [];
				const list = res2.data.map((item: any) => {
					if (item.own === true && !haveChildren.includes(item.id)) {
						checkedKeysTemp.push(item.id);
					}
					const childrenTemp = item.subMenu?.map((i: any) => {
						if (i.own === true) {
							checkedKeysTemp.push(i.id);
						}
						return { title: i.aliasName, key: i.id };
					});
				});
				setCheckedKeys(checkedKeysTemp);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
			setSpinning(false);
		}
		getData();
	}, []);
	const onExpand = (expandedKeysValue: React.Key[]) => {
		// console.log('onExpand', expandedKeysValue);
		setExpandedKeys(expandedKeysValue);
		setAutoExpandParent(true);
	};

	const onCheck = (checkedKeysValue: any) => {
		// console.log('onCheck', checkedKeysValue);
		setCheckedKeys(checkedKeysValue);
	};

	const handleSubmit = () => {
		const treeLists: any = [];
		treeData.map((item: any) => {
			if (item.children) {
				item.children.map((i: any) => {
					treeLists.push(i);
				});
			}
			treeLists.push(item);
		});
		const role = JSON.parse(storage.getSession('rolePower'));
		let resourceMenuDtoListTemp = treeLists.map((item: any) => {
			const result: any = {};
			result.id = item.key;
			if (checkedKeys.includes(item.key)) {
				result.own = true;
			} else {
				result.own = false;
			}
			return result;
		});
		// * 当二级菜单非全选时，需要将父级菜单的own也设置为true
		for (let i = 0; i < checkedKeys.length; i++) {
			const lt = treeLists.find(
				(item: any) => item.key === checkedKeys[i]
			);
			if (lt && lt.parentId !== 0) {
				const pt = treeLists.find(
					(item: any) => item.weight === lt.parentId
				);
				resourceMenuDtoListTemp = resourceMenuDtoListTemp.map(
					(tt: any) => {
						if (tt.id === pt?.key) {
							tt.own = true;
						}
						return tt;
					}
				);
			}
		}
		setLoading(true);
		const sendData = {
			roleId: role.id,
			id: role.id,
			name: role.name,
			description: role.description,
			power: role.power,
			resourceMenuDtoList: resourceMenuDtoListTemp
		};
		updateRole(sendData)
			.then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '角色权限修改成功'
					});
					history.push('/platform/roleManagement');
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	return (
		<ProPage>
			<ProHeader
				title="分配角色权限"
				onBack={() => window.history.back()}
				subTitle="赋予平台角色自定义中间件及菜单权限"
			/>
			<ProContent>
				<h2>菜单权限</h2>
				<Spin spinning={spinning}>
					<div className="allot-menu-content">
						<Tree
							checkable
							onExpand={onExpand}
							expandedKeys={expandedKeys}
							autoExpandParent={autoExpandParent}
							onCheck={onCheck}
							checkedKeys={checkedKeys}
							treeData={treeData}
						/>
					</div>
				</Spin>
				<Space>
					<Button
						loading={loading}
						type="primary"
						onClick={handleSubmit}
						disabled={spinning}
					>
						确认
					</Button>
					<Button onClick={() => history.goBack()}>取消</Button>
				</Space>
			</ProContent>
		</ProPage>
	);
}

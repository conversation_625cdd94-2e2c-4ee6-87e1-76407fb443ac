import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, notification, Select } from 'antd';
import { createNewRole, createRole, updateRole } from '@/services/role';
import { roleProps } from './role';
import pattern from '@/utils/pattern';

const FormItem = Form.Item;
const TextArea = Input.TextArea;
const formItemLayout = {
	labelCol: {
		span: 6
	},
	wrapperCol: {
		span: 18
	}
};

interface RoleFormProps {
	visible: true;
	onRefresh: () => void;
	onCancel: () => void;
	data: roleProps | null | undefined;
}

function RoleForm(props: RoleFormProps): JSX.Element {
	const { visible, onCancel, onRefresh, data } = props;
	const [form] = Form.useForm();
	const [loading, setLoading] = useState<boolean>(false);
	useEffect(() => {
		if (data) {
			form.setFieldsValue({
				name: data.name,
				description: data.description,
				createTime: data.createTime,
				roleId: data.id,
				menu: data.menu,
				clusterList: data.clusterList,
				type: data.type
			});
		}
	}, [data]);
	const onOk: () => void = () => {
		form.validateFields().then((values) => {
			const sendData = {
				...(values as unknown as roleProps)
			};
			setLoading(true);
			if (data) {
				// * 修改角色
				sendData.roleId = data.id;
				updateRole(sendData)
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '角色修改成功'
							});
							onCancel();
							onRefresh();
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					})
					.finally(() => {
						setLoading(false);
					});
			} else {
				// * 创建角色
				// createRole(sendData)
				// 	.then((res) => {
				// 		if (res.success) {
				// 			notification.success({
				// 				message: '成功',
				// 				description: '角色创建成功'
				// 			});
				// 			onCancel();
				// 			onRefresh();
				// 		} else {
				// 			notification.error({
				// 				message: '失败',
				// 				description: res.errorMsg
				// 			});
				// 		}
				// 	})
				// 	.finally(() => {
				// 		setLoading(false);
				// 	});
				createNewRole(sendData)
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '角色创建成功'
							});
							onCancel();
							onRefresh();
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					})
					.finally(() => {
						setLoading(false);
					});
			}
		});
	};

	return (
		<Modal
			title={!data ? '新增角色' : '编辑角色'}
			open={visible}
			onCancel={onCancel}
			onOk={onOk}
			width={550}
			okText="确定"
			okButtonProps={{ loading: loading }}
			cancelText="取消"
		>
			<Form labelAlign="left" form={form} {...formItemLayout}>
				<FormItem
					label="角色名称"
					required
					rules={[
						{ required: true, message: '请输入角色名称' },
						{
							pattern: new RegExp(pattern.roleName),
							message:
								'角色名称支持中文、大小字母、数字和特殊字符_.-，长度不可超过10个字符'
						}
					]}
					name="name"
				>
					<Input placeholder="请输入角色名称" />
				</FormItem>
				<FormItem
					label="角色类型"
					required
					rules={[{ required: true, message: '请选择角色类型' }]}
					name="type"
				>
					<Select disabled={!!data} placeholder="请选择角色类型">
						<Select.Option value="manager">管理类型</Select.Option>
						<Select.Option value="dba">DBA类型</Select.Option>
						<Select.Option value="normal">普通类型</Select.Option>
					</Select>
				</FormItem>
				<FormItem
					label="角色描述"
					required
					name="description"
					rules={[
						{ required: true, message: '请输入角色描述' },
						{
							max: 100,
							message: '角色描述长度不可超过100字符'
						}
					]}
				>
					<TextArea
						placeholder="请输入角色描述"
						maxLength={100}
						showCount
					/>
				</FormItem>
			</Form>
		</Modal>
	);
}

export default RoleForm;

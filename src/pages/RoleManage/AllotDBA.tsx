import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Divider, Space, Spin, notification } from 'antd';
import { useHistory } from 'react-router';
import { ProContent, ProHeader, ProPage } from '@/components/ProPage';
import { getMiddlewareRepository } from '@/services/repository';
import storage from '@/utils/storage';
import { api } from '@/api.json';
import { updateRole } from '@/services/role';

export default function AllotDBA(): JSX.Element {
	const history = useHistory();
	const role = JSON.parse(storage.getSession('rolePower'));
	const [data, setData] = useState<MiddlewareInfo[]>([]);
	const [loading, setLoading] = useState<boolean>(false);
	useEffect(() => {
		setLoading(true);
		getMiddlewareRepository()
			.then((res) => {
				if (res.success) {
					const list = res.data.map((item: MiddlewareInfo) => {
						let ct = false;
						if (role.power[item.chartName] === '1111') {
							ct = true;
						}
						item.checked = ct;
						return item;
					});
					setData(list);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	}, []);
	const handleSummit = () => {
		const dbaTemp = {};
		data.map((item: MiddlewareInfo) => {
			if (item.checked) {
				dbaTemp[item.chartName] = '1111';
			} else {
				dbaTemp[item.chartName] = '0000';
			}
		});
		const sendData = {
			roleId: role.id,
			id: role.id,
			name: role.name,
			description: role.description,
			power: dbaTemp,
			resourceMenuDtoList: role.resourceMenuDtoList
		};
		console.log(sendData);
		updateRole(sendData)
			.then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '角色权限修改成功'
					});
					history.push('/platform/roleManagement');
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	return (
		<ProPage>
			<ProHeader
				title="分配角色权限"
				onBack={() => window.history.back()}
				subTitle="赋予平台角色自定义中间件及菜单权限"
			/>
			<ProContent>
				<Spin spinning={loading}>
					<div className="cards">
						{data?.map((item: MiddlewareInfo) => {
							return (
								<div key={item.id} className="card-box">
									<div
										className={`card ${
											item.checked ? 'active' : ''
										}`}
										onClick={() => {
											const listTemp = data.map((i) => {
												if (i.id === item.id) {
													i.checked = !item.checked;
												}
												return i;
											});
											console.log(listTemp);
											setData(listTemp);
										}}
									>
										<img
											src={`${api}/images/middleware/${item.imagePath}`}
										/>
									</div>
									<div className="card-title">
										{item.name}
									</div>
								</div>
							);
						})}
					</div>
				</Spin>
				<Divider />
				<Space>
					<Button
						onClick={handleSummit}
						type="primary"
						loading={loading}
					>
						确定
					</Button>
					<Button
						onClick={() => {
							history.push(`/platform/roleManagement`);
						}}
					>
						取消
					</Button>
				</Space>
			</ProContent>
		</ProPage>
	);
}

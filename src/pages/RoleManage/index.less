.role-modal {
	width: 540px;
	height: 430px;
	.next-dialog-body {
		height: 320px;
		overflow-y: auto;
		padding: 0px 24px 16px 24px !important;
		& > p {
			line-height: @line-height-2 * 2;
		}
	}
}
.role-key-modal {
	width: 223px;
	.next-dialog-body {
		max-height: 200px;
		overflow-y: auto;
		& > p {
			line-height: @line-height-2 * 2;
		}
	}
}
.role-management-role-content {
	border: 1px solid #dedede;
	padding: 10px;
}
.role-management-content {
	display: flex;
	.role-management-namespace,
	.role-management-cluster {
		height: 200px;
		width: 243px;
		border: 1px solid #dedede;
		border-right: none;
		.role-management-title {
			height: 32px;
			line-height: @line-height-5;
			padding-left: 8px;
			border-bottom: 1px solid #dedede;
		}
		.role-management-check-content {
			padding: 6px 8px;
			overflow-y: auto;
			max-height: 166px;
			.role-management-label {
				height: 16px;
				line-height: 14px;
			}
			.role-management-checkout-content {
				padding-left: 24px;
				.next-checkbox-wrapper {
					display: block;
					margin-right: 0;
				}
			}
		}
	}
	.role-management-namespace {
		border-right: 1px solid #dedede;
	}
	.role-management-check-server {
		width: 100%;
		height: 200px;
		overflow-y: auto;
		padding: 6px 8px;
		border: 1px solid #dedede;
	}
}
.zeus-allot-role-btn-content {
	margin-top: @margin;
	padding: 16px 16px 16px 0px;
	background: @white;
	border-top: 1px solid @border-color;
	& > button {
		margin-right: @margin;
	}
}
.allot-menu-content {
	width: 100%;
	height: 550px;
	border: 1px solid @border-color;
	padding: @padding;
	margin-bottom: @margin;
}
.privilege-menus {
	border: 1px solid #d5d5d5;
	border-radius: 4px;
	margin-bottom: 12px;
	.ant-checkbox-wrapper {
		padding: 5px 0;
	}
	.ant-checkbox-wrapper + .ant-checkbox-wrapper {
		margin-left: 0;
	}
}
.tableTree {
	width: 100%;
	.treeBox {
		display: flex;
		align-items: stretch;
		height: 100%;
		.main {
			width: 100%;
			height: 100%;
			overflow: hidden;
		}
		.level-1 {
			background-color: #fafafa;
			border-right: 1px solid #ebebeb;
			width: 200px;
			height: 100%;
			overflow: hidden;
			p {
				height: 80px;
				font-weight: 500;
				cursor: pointer;
				display: flex;
				align-items: center;
				justify-content: center;
				span {
					display: flex;
				}
				.hc-icon {
					margin-right: 8px;
					margin-top: 2px;
				}
				&.active {
					position: relative;
					background: #fff;
					&:after {
						content: '';
						position: absolute;
						height: 100%;
						width: 1px;
						background: #fff;
						right: -1px;
						top: 0;
					}
				}
			}
		}
		.sub-menus {
			width: 100%;
			overflow: hidden;
		}
	}

	div.leaf {
		display: flex;
		align-items: center;
		padding-left: 20px;
		& > .checkBox-group {
			margin-right: 80px;
		}
	}
	div.child {
		display: flex;
		position: relative;
		& > div {
			flex: 1;
			&.empty {
				flex: none;
				display: flex;
				height: 100%;
				position: relative;
				justify-content: center;
				align-items: center;
				overflow: hidden;
				&::after {
					content: '';
					position: absolute;
					right: 0;
					width: 1px;
					height: 100%;
					background: #ebebeb;
				}
			}
		}
		&::after {
			content: '';
			position: absolute;
			bottom: 0;
			width: 100%;
			height: 1px;
			background: #ebebeb;
		}
		p {
			width: 210px;
			min-height: 50px;
			display: flex;
			align-items: center;
			justify-content: center;
			border-right: 1px solid #ebebeb;
		}
	}
}
.tableHeader {
	width: 100%;
	height: 42px;
	line-height: 42px;
	font-weight: 400;
	color: #555555;
	display: flex;
	background: #fafafa;
	border-bottom: 1px solid #ebebeb;
	p {
		padding-left: 16px;
		width: 210px;
		&:first-child {
			// border-right: 1px solid #EBEBEB;
			width: 181px;
		}
	}
	// 圆角
	border-top-left-radius: 4px;
	border-top-right-radius: 4px;
}
.modal-table-header {
	width: 100%;
	height: 36px;
	line-height: 36px;
	font-weight: 400;
	color: #555555;
	display: flex;
	background: #fafafa;
	border-bottom: 1px solid #ebebeb;
	p {
		padding-left: 16px;
		width: 210px;
		&:first-child {
			width: 181px;
			// border-right: 1px solid #EBEBEB;
		}
	}
}

.checkBox-group {
	min-width: 60px;
}

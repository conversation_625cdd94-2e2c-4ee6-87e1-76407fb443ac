import * as React from 'react';
import { useState, useEffect } from 'react';
import { useHistory, useParams } from 'react-router';
import { ProPage, ProHeader, ProContent } from '@/components/ProPage';
import { Scrollbars } from 'react-custom-scrollbars';
import CheckboxGroup from './CheckboxGroup';
import { Space, Button, notification } from 'antd';
import { getNewRoleMenus, updateRoleMenu } from '@/services/role';
import storage from '@/utils/storage';
import { flattenObjectArray, getPermissionList } from '@/utils/utils';
import { middlewareTypes } from '@/utils/const';

// TODO 未细分权限按钮内容（name、id等）为前端生成，后续改关联按钮需要优化
const timerTransition: any = { 1: '一', 2: '二', 3: '三', 4: '四', 5: '五' };
const AllotMenuAndBtn = (props: any): JSX.Element => {
	const history = useHistory();
	const params: { isEdit: string } = useParams();
	const { record, space, toChangeDisable } = props;
	const [checkedList, setCheckedList] = useState<any>({});
	const [deep, setDeep] = useState(5);
	const [defaultCheckedList, setDefaultCheckedList] = useState<any[]>([]);
	const [treeData, setTreeData] = useState([]);
	const [tableTreeHeight, setHeight] = useState(500);
	const [curIndex, setCurIndex] = useState(space === 'system' ? 1 : 0);
	const role = JSON.parse(storage.getSession('rolePower'));
	const [loading, setLoading] = useState<boolean>(false);

	useEffect(() => {
		getCheckBoxData();
		setHeight(document.body.clientHeight - 250);
	}, []);

	const getCheckBoxData = async () => {
		// 获取权限树
		const response = await getNewRoleMenus({ roleId: role.id });
		const treeData = response.data;
		const tree = setLevel(treeData, 1);
		const checkedList = tileTree(tree);
		setDefaultCheckedList(checkedList);
		filterTree(tree);
		setTreeData(tree);
	};

	const filterTree = (tree: any, parent?: any) => {
		const _tree = JSON.parse(JSON.stringify(tree));
		tree.forEach((item: any) => {
			if (item.subMenu) {
				filterTree(item.subMenu, item);
			}
		});
		if (
			parent &&
			tree.every(
				(item: any) => !item.subMenu || item.subMenu.length === 0
			)
		) {
			parent.subMenu = _tree;
		}
	};

	const tileTree = (tree: any) => {
		const _tree = JSON.parse(JSON.stringify(tree));
		//用于存默认值
		const treeList: any = [];
		//用来构建默认的checkedList
		const checkedList: any = {};
		function getTreeList(tree: any, parent?: any) {
			tree.forEach((item: any) => {
				if (item.subMenu) {
					getTreeList(item.subMenu, item);
				} else if (
					!item.subMenu &&
					item.resourceType === 'button' &&
					item.own
				) {
					treeList.push(item.id);
					checkedList[parent.id] = [
						...(checkedList[parent.id] || []),
						item.id
					];
				}
			});
		}
		getTreeList(_tree);
		setCheckedList(checkedList);
		return treeList;
	};

	let deepTemp = 1;
	const setLevel = (tree: any, level: any, rootIndex?: any) => {
		tree.forEach((child: any, index: number) => {
			const pId = -1;
			if (child.parentId === pId) {
				rootIndex = index;
			}
			child.level = level;
			child.rootIndex = rootIndex;
			if (child.subMenu && child.subMenu.length) {
				deepTemp = deepTemp <= level ? level + 1 : deepTemp;
				setLevel(child.subMenu, level + 1, rootIndex);
			}
			// * 本期不做的内容，自己菜单暂时先加一个全选按钮
			if (!child.subMenu && child.resourceType === 'menu') {
				if (level === 2) {
					child.subMenu = [
						{
							resourceType: 'button',
							aliasName: '全选',
							id: child.id + 'x',
							parentId: child.id,
							name: child.name + 'Button',
							middlewareType: child.middlewareType,
							module: child.module,
							defaultMenu: child.defaultMenu,
							own: child.own,
							comment: child.comment,
							level: 3,
							rootIndex
						}
					];
				}
				if (level === 3) {
					child.subMenu = [
						{
							resourceType: 'button',
							aliasName: '全选',
							id: child.id + 'x',
							parentId: child.id,
							name: child.name + 'Button',
							middlewareType: child.middlewareType,
							module: child.module,
							defaultMenu: child.defaultMenu,
							own: child.own,
							comment: child.comment,
							level: 4,
							rootIndex
						}
					];
				}
				if (level === 4) {
					child.subMenu = [
						{
							resourceType: 'button',
							aliasName: '全选',
							id: child.id + 'x',
							parentId: child.id,
							name: child.name + 'Button',
							middlewareType: child.middlewareType,
							module: child.module,
							defaultMenu: child.defaultMenu,
							own: child.own,
							comment: child.comment,
							level: 5,
							rootIndex
						}
					];
				}
			}
		});
		setDeep(deepTemp);
		return tree;
	};

	const renderHeader = () => {
		return (
			<>
				{new Array(deep).fill(undefined).map((_, index) => {
					if (index < deep - 1) {
						return (
							<p key={index} className="module">{`${
								timerTransition[index + 1]
							}级模块`}</p>
						);
					} else {
						return <p key={index}>功能权限</p>;
					}
				})}
			</>
		);
	};

	const renderLevel1 = (tree: any) => {
		return (
			<>
				{tree.map((child: any, index: number) => {
					return (
						<p
							key={index}
							className={index === curIndex ? 'active' : ''}
							onClick={() => {
								setCurIndex(index);
							}}
						>
							<span>{child.aliasName}</span>
						</p>
					);
				})}
			</>
		);
	};

	const onChangeCheckBox = (
		currentCheckList: any,
		children: any,
		label: any,
		current: any
	) => {
		const obj = flattenObjectArray(treeData, 'subMenu');
		const findItem = (code: string, type?: string) =>
			obj?.find((item) =>
				type
					? item.name === code && item.middlewareType === type
					: item.name === code
			);
		// * 筛选改分类下所有按钮的父菜单（中间件的module区分通用、接入和发布）
		const filterModuleMenu = (code: string, type?: string) =>
			obj?.filter((item) =>
				type
					? item.module?.includes(code) &&
					  item.resourceType === 'menu' &&
					  item.middlewareType === type
					: item.module === code && item.resourceType === 'menu'
			);
		const checkedItem = children?.filter((item: any) => item.checked);
		setDefaultCheckedList(currentCheckList);
		const _checkedList = JSON.parse(JSON.stringify(checkedList));
		_checkedList[label] = currentCheckList;
		const flattenedArray = Object.values(_checkedList).reduce(
			(acc: any[], curr) => acc.concat(curr),
			[]
		);
		console.log(
			currentCheckList,
			children,
			flattenedArray,
			_checkedList,
			label
		);
		// * 跳转市场管理
		if (current.checked && current.name === 'overviewJumpMarket') {
			_checkedList[findItem('marketManagementButton')?.parentId] = [
				findItem('marketManagementButton')?.id
			];
		}
		if (!current.checked && current.name === 'marketManagementButton') {
			const list =
				_checkedList[findItem('overviewJumpMarket')?.parentId] || [];
			_checkedList[findItem('overviewJumpMarket')?.parentId] = [
				...list.filter((item: any) => {
					return item !== findItem('overviewJumpMarket')?.id;
				})
			];
		}
		// 跳转系统告警
		if (current.checked && current.name === 'overviewJumpAlarm') {
			_checkedList[findItem('alarmCenterSystemButton')?.parentId] = [
				findItem('alarmCenterSystemButton')?.id
			];
		}
		if (!current.checked && current.name === 'alarmCenterSystemButton') {
			const list =
				_checkedList[findItem('overviewJumpAlarm')?.parentId] || [];
			_checkedList[findItem('overviewJumpAlarm')?.parentId] = [
				...list.filter((item: any) => {
					return item !== findItem('overviewJumpAlarm')?.id;
				})
			];
		}
		// 跳转集群告警
		if (current.checked && current.name === 'overviewJumpClusterAlarm') {
			_checkedList[findItem('alarmCenterClusterButton')?.parentId] = [
				findItem('alarmCenterClusterButton')?.id
			];
		}
		if (!current.checked && current.name === 'alarmCenterClusterButton') {
			const list =
				_checkedList[findItem('overviewJumpClusterAlarm')?.parentId] ||
				[];
			_checkedList[findItem('overviewJumpClusterAlarm')?.parentId] = [
				...list.filter((item: any) => {
					return item !== findItem('overviewJumpClusterAlarm')?.id;
				})
			];
		}
		// 跳转集群详情关联
		if (
			checkedItem?.find(
				(item: any) => item.name === 'areaManagementJumpCluster'
			)
		) {
			_checkedList[findItem('clusterOverviewQuery')?.parentId] = [
				findItem('clusterOverviewQuery')?.id
			];
			_checkedList[findItem('clusterListQuery')?.parentId] = [
				findItem('clusterListQuery')?.id,
				..._checkedList[findItem('clusterListQuery')?.parentId]
			];
		}
		if (!current.checked && current.name === 'clusterOverviewQuery') {
			const list =
				_checkedList[findItem('areaManagementJumpCluster')?.parentId] ||
				[];
			_checkedList[findItem('areaManagementJumpCluster')?.parentId] = [
				...list.filter((item: any) => {
					return item !== findItem('areaManagementJumpCluster')?.id;
				})
			];
		}
		// 跳转集群命名空间列表关联
		if (
			checkedItem?.find(
				(item: any) => item.name === 'areaManagementJumpNamespace'
			)
		) {
			_checkedList[findItem('clusterListQuery')?.parentId] = [
				findItem('clusterListQuery')?.id,
				..._checkedList[findItem('clusterListQuery')?.parentId]
			];
			_checkedList[findItem('clusterNameSpaceQuery')?.parentId] = [
				...(_checkedList[findItem('clusterNameSpaceQuery')?.parentId] ||
					[]),
				findItem('clusterNameSpaceQuery')?.id
			];
		}
		if (!current.checked && current.name === 'clusterNameSpaceQuery') {
			const list =
				_checkedList[
					findItem('areaManagementJumpNamespace')?.parentId
				] || [];
			_checkedList[findItem('areaManagementJumpNamespace')?.parentId] = [
				...list.filter((item: any) => {
					return item !== findItem('areaManagementJumpNamespace')?.id;
				})
			];
		}
		// * 集群管理选择除集群列表外所有操作集群列表页面可见及查询（不用current是因为点击的可能是全选按钮）
		if (
			current.name !== 'all' &&
			current?.module === 'cluster' &&
			current?.name !== 'clusterListQuery'
		) {
			_checkedList[findItem('clusterListQuery')?.parentId] = [
				...(_checkedList[findItem('clusterListQuery')?.parentId] || []),
				findItem('clusterListQuery')?.id
			];
		}
		// * 集群管理-集群列表取消勾选-集群管理所有内容取消勾选, 跳转集群的操作也要取消勾选
		if (!current.checked && current.name === 'clusterListQuery') {
			const menus = filterModuleMenu('cluster');
			menus.map((item: any) => {
				_checkedList[item.id] = [];
			});
			const list =
				_checkedList[findItem('areaManagementJumpCluster')?.parentId] ||
				[];
			_checkedList[findItem('areaManagementJumpCluster')?.parentId] = [
				...list.filter((item: any) => {
					return (
						item !== findItem('areaManagementJumpCluster')?.id &&
						item !== findItem('areaManagementJumpNamespace')?.id
					);
				})
			];
		}
		// * 组织管理-资源概览-影响客户端管理
		if (current.checked && current.name === 'resourceDisplayButton') {
			_checkedList[findItem('agentButton')?.parentId] = [
				findItem('agentButton')?.id
			];
		}
		if (!current.checked && current.name === 'agentButton') {
			const list =
				_checkedList[findItem('resourceDisplayButton')?.parentId] || [];
			_checkedList[findItem('resourceDisplayButton')?.parentId] = [
				...list.filter((item: any) => {
					return item !== findItem('resourceDisplayButton')?.id;
				})
			];
		}
		// * 组织管理选择除集群列表外所有操作组织列表 页面可见及查询
		if (
			current?.module === 'organization' &&
			current?.name !== 'organizationListButton'
		) {
			_checkedList[findItem('organizationListButton')?.parentId] = [
				...(_checkedList[
					findItem('organizationListButton')?.parentId
				] || []),
				findItem('organizationListButton')?.id
			];
		}
		// * 组织管理-组织列表取消勾选-组织管理所有内容取消勾选
		if (!current.checked && current.name === 'organizationListButton') {
			const menus = filterModuleMenu('organization');
			menus.map((item: any) => {
				_checkedList[item.id] = [];
			});
		}
		// * 所有中间件选择除服务管理外所有操作勾选该中间件服务管理
		if (
			current?.module.includes('middleware') &&
			current?.name !== 'serviceManagementButton'
		) {
			_checkedList[
				findItem(
					'serviceManagementButton',
					current?.middlewareType
				)?.parentId
			] = [
				...(_checkedList[
					findItem('serviceManagementButton', current?.middlewareType)
						?.parentId
				] || []),
				findItem('serviceManagementButton', current?.middlewareType)?.id
			];
		}
		// * 当前中间件取消服务管理其他所有操作取消勾选
		if (!current.checked && current.name === 'serviceManagementButton') {
			const menus = filterModuleMenu(
				'middleware',
				current.middlewareType
			);
			menus.map((item: any) => {
				_checkedList[item.id] = [];
			});
		}
		// * 工作台页面 - 告警跳转
		if (current.checked && current.name === 'workspaceDateJump') {
			// * 勾选所有中间件的基本信息和服务管理
			middlewareTypes.map((item: string) => {
				_checkedList[findItem('baseInfoButton', item)?.parentId] = [
					findItem('baseInfoButton', item)?.id
				];
				_checkedList[
					findItem('serviceManagementButton', item)?.parentId
				] = [findItem('serviceManagementButton', item)?.id];
			});
		}
		if (
			!current.checked &&
			(current.name === 'baseInfoButton' ||
				current.name === 'serviceManagementButton')
		) {
			const list =
				_checkedList[findItem('workspaceDateJump')?.parentId] || [];
			_checkedList[findItem('workspaceDateJump')?.parentId] = [
				...list.filter((item: any) => {
					return item !== findItem('workspaceDateJump')?.id;
				})
			];
		}
		// * 配置文件-文件编辑/修改历史-影响文件详情查看
		if (
			checkedItem.find(
				(item: any) =>
					item.name === 'confFileEditUpload' ||
					item.name === 'confFileEditUpdate' ||
					item.name === 'confFileHistoryDetail' ||
					item.name === 'confFileHistoryRoleBack'
			)
		) {
			_checkedList[
				findItem(
					'confFileDetailQuery',
					current?.middlewareType
				)?.parentId
			] = [
				findItem('confFileDetailQuery', current?.middlewareType)?.id,
				..._checkedList[
					findItem('confFileDetailQuery', current?.middlewareType)
						?.parentId
				]
			];
			if (current.checked && current.name === 'confFileEditUpdate') {
				_checkedList[
					findItem(
						'confFileEditUpload',
						current?.middlewareType
					)?.parentId
				] = [
					findItem('confFileEditUpload', current?.middlewareType)?.id,
					...(_checkedList[
						findItem('confFileEditUpload', current?.middlewareType)
							?.parentId
					] || [])
				];
			}
			if (current.checked && current.name === 'confFileHistoryRoleBack') {
				_checkedList[
					findItem(
						'confFileHistoryDetail',
						current?.middlewareType
					)?.parentId
				] = [
					findItem('confFileHistoryDetail', current?.middlewareType)
						?.id,
					...(_checkedList[
						findItem(
							'confFileHistoryDetail',
							current?.middlewareType
						)?.parentId
					] || [])
				];
			}
		}
		if (!current.checked && current.name === 'confFileDetailQuery') {
			const files = [
				'confFileEditUpload',
				'confFileEditUpdate',
				'confFileHistoryDetail',
				'confFileHistoryRoleBack'
			];
			files.map((item: string) => {
				const list =
					_checkedList[
						findItem(item, current.middlewareType)?.parentId
					] || [];
				_checkedList[findItem(item, current.middlewareType)?.parentId] =
					[
						...list.filter((id: any) => {
							return (
								id !==
								findItem(item, current.middlewareType)?.id
							);
						})
					];
			});
		}
		setCheckedList(_checkedList);
		const preObj = _checkedList;
		let perArr: any[] = [];
		Object.keys(preObj).map((item) => {
			perArr = [...perArr, ...preObj[item]];
		});
		toChangeDisable && toChangeDisable(perArr.length === 0);
	};

	const renderCheckBox = (child: any, index: number, parentLevel?: any) => {
		const totalCheckedList = Object.values(checkedList).reduce(
			(acc: any[], curr) => acc.concat(curr),
			[]
		);
		if (child.rootIndex === curIndex) {
			return (
				<div className={'child'} key={`${child.level}_${index}`}>
					{parentLevel || child.level === 1 ? (
						''
					) : (
						<p>{child.aliasName}</p>
					)}
					<div className="child">
						<div
							className="empty"
							style={{
								width: 210 * (deep - child.level - 1)
							}}
						>
							/
						</div>
						<div className="leaf">
							<CheckboxGroup
								list={child.subMenu}
								totalCheckedList={totalCheckedList}
								defaultCheckedList={defaultCheckedList}
								allcheckedList={checkedList[child.id]}
								onChange={(
									checkedList: any,
									children: any,
									item: any
								) =>
									onChangeCheckBox(
										checkedList,
										children,
										child.id,
										item
									)
								}
							></CheckboxGroup>
						</div>
					</div>
				</div>
			);
		}
	};

	const renderMenu = (child: any, index: number, permission?: any) => {
		const isLeafContainer =
			child.level === deep - 1 && child.resourceType === 'button';
		if (child.rootIndex === curIndex) {
			return (
				<div className="child" key={`${child.level}_${index}`}>
					{child.level !== 1 && <p>{child.aliasName}</p>}
					{child.subMenu && child.subMenu.length >= 0 && (
						<div className={isLeafContainer ? 'leaf' : ''}>
							{renderItem(child.subMenu)}
							{permission
								? renderCheckBox(permission, index, 1)
								: ''}
						</div>
					)}
				</div>
			);
		}
	};

	const renderItem = (tree: any) => {
		return (
			<>
				{tree.map((child: any, index: number) => {
					if (
						child.resourceType === 'menu' &&
						child.subMenu &&
						child.subMenu.length &&
						child.subMenu.every(
							(item: any) => item.resourceType === 'button'
						)
					) {
						return renderCheckBox(child, index);
					} else if (
						child.resourceType === 'menu' &&
						child.subMenu &&
						child.subMenu.length &&
						child.subMenu.some(
							(item: any) => item.resourceType === 'button'
						)
					) {
						//处理菜单下有权限又有菜单的情况
						const menu = child.subMenu.filter(
							(item: any) => item.resourceType === 'menu'
						);
						const _menuChild = JSON.parse(JSON.stringify(child));
						_menuChild.subMenu = menu;
						const permission = child.subMenu.filter(
							(item: any) => item.resourceType === 'button'
						);
						const _permissionChild = JSON.parse(
							JSON.stringify(child)
						);
						_permissionChild.subMenu = permission;
						return [
							renderMenu(_menuChild, index, _permissionChild)
						];
					} else if (child.resourceType === 'menu') {
						return renderMenu(child, index);
					}
				})}
			</>
		);
	};

	const handleSubmit = async () => {
		// setLoading(true);
		const role = JSON.parse(storage.getSession('rolePower'));
		// * 过滤掉自定义的按钮，id为小于1随机数
		const data = getPermissionList(treeData, checkedList).filter(
			(item: any) => item.id >= 1
		);
		try {
			await updateRoleMenu({
				id: role.id,
				name: role.name,
				description: role.description,
				resourceMenuDtoList: data
			});
			setLoading(false);
			history.goBack();
			notification.success({
				message: '成功',
				description: '编辑成功'
			});
		} catch (error) {
			setLoading(false);
		}
	};

	return (
		<ProPage>
			<ProHeader
				onBack={() => history.goBack()}
				title={
					params.isEdit === 'true' ? '分配角色权限' : '查看角色权限'
				}
				subTitle={
					params.isEdit === 'true'
						? '赋予平台角色自定义中间件及菜单权限'
						: undefined
				}
			/>
			<ProContent>
				<h2>权限配置</h2>
				<div className="privilege-menus">
					<div
						className={
							record ? 'modal-table-header' : 'tableHeader'
						}
					>
						{renderHeader()}
					</div>
					<div
						className="tableTree"
						style={{ height: tableTreeHeight }}
					>
						<div className="treeBox">
							<div className="level-1">
								<Scrollbars className="main" autoHide>
									{renderLevel1(treeData)}
								</Scrollbars>
							</div>
							<div className="sub-menus">
								<Scrollbars className="main" autoHide>
									{renderItem(treeData)}
								</Scrollbars>
							</div>
						</div>
					</div>
				</div>
				{params.isEdit === 'true' && (
					<Space>
						<Button
							loading={loading}
							type="primary"
							onClick={handleSubmit}
						>
							确认
						</Button>
						<Button onClick={() => history.goBack()}>取消</Button>
					</Space>
				)}
			</ProContent>
		</ProPage>
	);
};

export default AllotMenuAndBtn;

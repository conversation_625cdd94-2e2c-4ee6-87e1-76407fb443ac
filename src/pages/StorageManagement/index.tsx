import React, { useEffect, useState } from 'react';
import { Space, Button, Select, notification, Modal } from 'antd';
import { useHistory, useParams } from 'react-router';
import { ListCard, ListCardItem } from '@/components/ListCard';
import Actions from '@/components/Actions';
import ProList from '@/components/ProList';
import storageIcon from '@/assets/images/storage-manage.svg';
import {
	getLists,
	getTypes,
	deleteStorage,
	getMonitor
} from '@/services/storage';
import { GetParams, StorageItem, DeleteParams } from './storageManage';
import useRefresh from '@/utils/useRefresh';
import Auth from '@/components/Auth';

const LinkButton = Actions.LinkButton;
const { Option } = Select;
const { confirm } = Modal;
export default function StorageManagement(): JSX.Element {
	const history = useHistory();
	const params: any = useParams();
	const [storages, setStorages] = useState<StorageItem[]>([]);
	const [typeList, setTypeList] = useState<string[]>([]);
	const [selectedType, setSelectedType] = useState<string>('');
	const [key, setKey] = useState<string>('');
	const [resourceQuota, setResourceQuota] = useState<any>();
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		getTypes({ clusterId: '*' }).then((res) => {
			if (res.success) {
				setTypeList(res.data);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
		getData(selectedType, key);
	}, [refreshKey]);
	const getData = (type: string, keyword: string) => {
		const sendData: GetParams = {
			all: false,
			clusterId: params.id,
			type: type || '',
			key: keyword || ''
		};
		getLists(sendData).then((res) => {
			if (res.success) {
				setStorages(res.data);
			} else {
				setStorages([]);
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
		getMonitor({ clusterId: params.id }).then((res) => {
			if (res.success) {
				setResourceQuota(res.data);
			}
		});
	};
	const handleSearch = (value: string) => {
		getData(selectedType, value);
	};
	const handleChange = (value: string) => {
		setStorages([]);
		setSelectedType(value);
		getData(value, key);
	};
	const Operation = {
		primary: (
			<Space>
				<Auth code="clusterStorageManagementAdd">
					<Button
						type="primary"
						onClick={() => {
							history.push(
								`/platform/clusterManagement/resourcePoolDetail/${params.id}/storage/create`
							);
						}}
					>
						新增
					</Button>
				</Auth>
				<label>类型:</label>
				<Select
					defaultValue=""
					style={{ width: 120 }}
					dropdownMatchSelectWidth={false}
					onChange={handleChange}
				>
					<Option value="">全部</Option>
					{typeList.map((item: string) => {
						return (
							<Option key={item} value={item}>
								{item}
							</Option>
						);
					})}
				</Select>
			</Space>
		)
	};
	const quotaRender = (record: StorageItem) => {
		if (resourceQuota && resourceQuota[record.clusterId]) {
			return `${
				resourceQuota[record.clusterId][record.aliasName]?.used || 0
			}GB`;
		} else {
			return '0GB';
		}
	};
	const applyRender = (record: StorageItem) => {
		if (resourceQuota && resourceQuota[record.clusterId]) {
			return `${
				resourceQuota[record.clusterId][record.aliasName]?.request || 0
			}GB`;
		} else {
			return '0GB';
		}
	};
	return (
		<ProList
			operation={Operation}
			search={{
				value: key,
				onChange: (e: any) => setKey(e.target.value),
				onSearch: handleSearch,
				placeholder: '请输入存储服务名称搜索'
			}}
			// showRefresh
			onRefresh={() => {
				setStorages([]);
				getData(selectedType, key);
			}}
		>
			{storages.map((item: StorageItem, index: number) => {
				return (
					<ListCard
						key={`${item.aliasName}-${item.clusterId}`}
						title={item.aliasName}
						subTitle="存储项名称"
						icon={
							<img
								src={storageIcon}
								style={{
									marginLeft: 13,
									marginRight: 16
								}}
							/>
						}
						actionRender={
							<Actions>
								<LinkButton
									code="clusterStorageManagementUpdate"
									onClick={() => {
										history.push(
											`/platform/clusterManagement/resourcePoolDetail/${params.id}/storage/edit/${item.storageId}/${item.storageClassList[0].name}`
										);
									}}
								>
									编辑
								</LinkButton>
								<LinkButton
									code="clusterStorageManagementDelete"
									onClick={() => {
										confirm({
											title: '操作确认',
											content: '是否确认删除该存储?',
											onOk: () => {
												const sendData: DeleteParams = {
													clusterId: item.clusterId,
													storageId:
														item.storageId as string
												};
												return deleteStorage(sendData)
													.then((res) => {
														if (res.success) {
															notification.success(
																{
																	message:
																		'成功',
																	description:
																		'存储删除成功'
																}
															);
														} else {
															notification.error({
																message: '失败',
																description:
																	res.errorMsg
															});
														}
													})
													.finally(() => {
														getData(
															selectedType,
															key
														);
													});
											}
										});
									}}
								>
									删除
								</LinkButton>
							</Actions>
						}
						titleClick={() => {
							history.push(
								`/platform/clusterManagement/resourcePoolDetail/${params.id}/storage/${item.storageClassList[0].name}/${item.aliasName}`
							);
						}}
					>
						<ListCardItem
							label={
								item.isActiveActive
									? 'storageClass (可用区A)'
									: 'storageClass'
							}
							value={
								<p
									className="text-hidden"
									style={{ width: 170 }}
									title={`${item.storageClassList[0].name} (${item.storageClassList[0].volumeType})`}
								>{`${item.storageClassList[0].name} (${item.storageClassList[0].volumeType})`}</p>
							}
							width={item.isActiveActive ? 170 : 390}
						/>
						{item.isActiveActive && (
							<ListCardItem
								label={
									item.isActiveActive
										? 'storageClass (可用区B)'
										: 'storageClass'
								}
								value={
									<p
										className="text-hidden"
										style={{ width: 170 }}
										title={`${item.storageClassList[0].name} (${item.storageClassList[0].volumeType})`}
									>{`${item.storageClassList[1].name} (${item.storageClassList[1].volumeType})`}</p>
								}
								width={170}
							/>
						)}
						<ListCardItem
							label="存储使用量"
							value={quotaRender(item)}
							width={100}
						/>
						<ListCardItem
							label="存储申请量"
							value={applyRender(item)}
							width={100}
						/>
					</ListCard>
				);
			})}
		</ProList>
	);
}

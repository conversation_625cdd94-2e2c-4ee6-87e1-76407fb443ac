import React, { useEffect, useState } from 'react';
import { useParams, useHistory } from 'react-router';
import {
	Button,
	Space,
	Form,
	notification,
	Input,
	Select,
	Radio,
	InputNumber
} from 'antd';
import storageIcon from '@/assets/images/storage-manage.svg';
import { ProHeader, ProPage, ProContent } from '@/components/ProPage';
import {
	AddParams,
	EditStorageParams,
	StorageItem,
	GetParams,
	GetDetailParams
} from './storageManage';

import { licenseFeatures } from '@/services/user';
import {
	getLists,
	addStorage,
	getStorageDetail,
	updateStorage
} from '@/services/storage';
import { formItemLayout614 } from '@/utils/const';
import pattern from '@/utils/pattern';
import storage from '@/utils/storage';

const FormItem = Form.Item;
const Option = Select.Option;
export default function AddStorage(): JSX.Element {
	const params: EditStorageParams = useParams();
	const history = useHistory();
	const [form] = Form.useForm();
	const [storages, setStorages] = useState<StorageItem[]>([]);
	const [volumeType, setVolumeType] = useState<string>('');
	const [storageType, setStorageType] = useState<string>('normal');
	const [detail, setDetail] = useState<StorageItem>();
	const [activeOpen, setActiveOpen] = useState<boolean>(false);

	useEffect(() => {
		licenseFeatures().then((res) => {
			if (res.success) {
				setActiveOpen(res.data.activeActiveEnable);
			}
		});
		if (params.name) {
			const sendData: GetDetailParams = {
				clusterId: params.id,
				storageName: params.name
			};
			getStorageDetail(sendData).then((res) => {
				if (res.success) {
					form.setFieldsValue({
						name: params.name,
						aliasName: res.data.aliasName,
						clusterId: params.id,
						volumeType: res.data.storageClassList[0].volumeType,
						vgName: res.data.storageClassList[0].vgName,
						totalStorage: res.data.totalStorage,
						isActiveActive: res.data.isActiveActive
							? 'shanghuo'
							: 'normal',
						nameA: res.data.storageClassList.find(
							(item: StorageItem) => item.activeZone === 'zoneA'
						)?.name,
						volumeTypeA: res.data.storageClassList.find(
							(item: StorageItem) => item.activeZone === 'zoneA'
						)?.volumeType,
						nameB: res.data.storageClassList.find(
							(item: StorageItem) => item.activeZone === 'zoneB'
						)?.name,
						volumeTypeB: res.data.storageClassList.find(
							(item: StorageItem) => item.activeZone === 'zoneB'
						)?.volumeType
					});
					setDetail(res.data);
					// setCurClusterId(params.id);
					setVolumeType(res.data.storageClassList[0].volumeType);
					setStorageType(
						res.data.isActiveActive ? 'shanghuo' : 'normal'
					);
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		}
	}, []);
	useEffect(() => {
		if (params.id) {
			getData(params.id);
		}
	}, [params.id]);
	const getData = (clusterId: string) => {
		const sendData: GetParams = {
			all: true,
			clusterId: clusterId
		};
		getLists(sendData).then((res) => {
			if (res.success) {
				const list = res.data
					.map((item) => item.storageClassList)
					.flat(1);
				setStorages(list);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const handleSubmit = () => {
		form.validateFields().then((values) => {
			const sendData: AddParams = {
				name: values.name,
				aliasName: values.aliasName,
				clusterId: params.id,
				vgName: values.vgName,
				volumeType: values.volumeType,
				requestQuota: values.requestQuota,
				storageName: params.name,
				totalStorage: values.totalStorage,
				isActiveActive:
					values.isActiveActive === 'normal' ? false : true,
				storageClassList:
					values.isActiveActive === 'normal'
						? [
								storages.find(
									(item: StorageItem) =>
										item.name === values.name
								)
						  ]
						: [
								storages.find(
									(item: StorageItem) =>
										item.name === values.nameA
								),
								storages.find(
									(item: StorageItem) =>
										item.name === values.nameB
								)
						  ]
			};
			if (params.storageId) {
				sendData.storageClassList = detail?.storageClassList;
				sendData.storageName = values.aliasName;
				sendData.storageId = params.storageId;
			}
			if (params.storageId) {
				updateStorage(sendData).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '存储编辑成功'
						});
						storage.setSession(
							'cluster-detail-current-tab',
							'storage'
						);
						history.goBack();
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			} else {
				addStorage(sendData).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '存储添加成功'
						});
						storage.setSession(
							'cluster-detail-current-tab',
							'storage'
						);
						history.goBack();
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			}
		});
	};
	const handleChange = (value: any, name?: string) => {
		const cur = storages.find((item: StorageItem) => item.name === value);

		form.setFieldsValue({
			...form.getFieldsValue(),
			volumeType: cur?.volumeType,
			requestQuota: cur?.requestQuota,
			vgName: cur?.vgName
		});
		name === 'nameA' &&
			form.setFieldsValue({
				...form.getFieldsValue(),
				volumeTypeA: cur?.volumeType
			});
		name === 'nameB' &&
			form.setFieldsValue({
				...form.getFieldsValue(),
				volumeTypeB: cur?.volumeType
			});
		setVolumeType(cur?.volumeType || '');
	};
	return (
		<ProPage>
			<ProHeader
				onBack={() => {
					storage.setSession('cluster-detail-current-tab', 'storage');
					history.goBack();
				}}
				avatar={{
					children: <img src={storageIcon} />,
					shape: 'square',
					size: 48,
					style: { background: '#F5F5F5' }
				}}
				title={`${params.name ? '编辑' : '新增'}存储管理`}
			/>
			<ProContent>
				<Form
					{...formItemLayout614}
					form={form}
					labelAlign="left"
					style={{ width: '60%' }}
				>
					<h2>类型选择</h2>
					<FormItem
						required
						name="isActiveActive"
						initialValue="normal"
						label="类型选择"
					>
						<Radio.Group
							value={storageType}
							disabled={params.name ? true : false}
							onChange={(e) => {
								setStorageType(e.target.value);
								setVolumeType('');
							}}
						>
							<Radio key="normal" value="normal">
								普通存储项
							</Radio>
							{activeOpen && (
								<Radio key="shanghuo" value="shanghuo">
									双活存储项
								</Radio>
							)}
						</Radio.Group>
					</FormItem>
					<h2>基础信息</h2>
					<FormItem
						label="中文名称"
						required
						rules={[
							{ required: true, message: '请填写中文名称' },
							{
								pattern: new RegExp(pattern.storageName),
								message:
									'存储中文名称由中文、大写字母、小写字母、数字以及“./-”组成，长度不超过32个字符'
							}
						]}
						name="aliasName"
					>
						<Input placeholder="请输入中文名称" />
					</FormItem>
					{storageType === 'normal' ? (
						<>
							<FormItem
								label="StorageClass名称"
								required
								rules={[
									{
										required: true,
										message: '请选择StorageClass名称'
									}
								]}
								name="name"
							>
								<Select
									disabled={params.name ? true : false}
									onChange={(value) => handleChange(value)}
									placeholder="请选择"
									dropdownMatchSelectWidth={false}
								>
									{storages.filter((item) => !item.activeZone)
										.length > 0 &&
										storages
											.filter((item) => !item.activeZone)
											.map((item: StorageItem) => {
												return (
													<Option
														key={item.name}
														value={item.name}
													>
														{item.name}
													</Option>
												);
											})}
								</Select>
							</FormItem>
							<FormItem
								label="类型"
								name="volumeType"
								rules={[
									{ required: true, message: '类型不能为空' }
								]}
							>
								<Input disabled />
							</FormItem>
						</>
					) : (
						<>
							<div style={{ display: 'flex' }}>
								<div style={{ width: '25%' }}>可用区A存储</div>
								<div style={{ width: 500 }}>
									<FormItem
										label="StorageClass名称"
										required
										rules={[
											{
												required: true,
												message:
													'请选择StorageClass名称'
											}
										]}
										name="nameA"
									>
										<Select
											disabled={
												params.name ? true : false
											}
											placeholder="请选择"
											onChange={(value) =>
												handleChange(value, 'nameA')
											}
											dropdownMatchSelectWidth={false}
										>
											{storages.filter(
												(item) =>
													item.activeZone === 'zoneA'
											).length > 0 &&
												storages
													.filter(
														(item) =>
															item.activeZone ===
															'zoneA'
													)
													.map(
														(item: StorageItem) => {
															return (
																<Option
																	key={
																		item.name
																	}
																	value={
																		item.name
																	}
																>
																	{item.name}
																</Option>
															);
														}
													)}
										</Select>
									</FormItem>
									<FormItem
										label="类型"
										name="volumeTypeA"
										rules={[
											{
												required: true,
												message: '类型不能为空'
											}
										]}
									>
										<Input disabled />
									</FormItem>
								</div>
							</div>
							<div style={{ display: 'flex' }}>
								<div style={{ width: '25%' }}>可用区B存储</div>
								<div style={{ width: 500 }}>
									<FormItem
										label="StorageClass名称"
										required
										rules={[
											{
												required: true,
												message:
													'请选择StorageClass名称'
											}
										]}
										name="nameB"
									>
										<Select
											disabled={
												params.name ? true : false
											}
											placeholder="请选择"
											onChange={(value) =>
												handleChange(value, 'nameB')
											}
											dropdownMatchSelectWidth={false}
										>
											{storages.filter(
												(item) =>
													item.activeZone === 'zoneB'
											).length > 0 &&
												storages
													.filter(
														(item) =>
															item.activeZone ===
															'zoneB'
													)
													.map(
														(item: StorageItem) => {
															return (
																<Option
																	key={
																		item.name
																	}
																	value={
																		item.name
																	}
																>
																	{item.name}
																</Option>
															);
														}
													)}
										</Select>
									</FormItem>
									<FormItem
										label="类型"
										name="volumeTypeB"
										rules={[
											{
												required: true,
												message: '类型不能为空'
											}
										]}
									>
										<Input disabled />
									</FormItem>
								</div>
							</div>
						</>
					)}
					<FormItem
						label="存储总额"
						rules={[{ required: true, message: '请输入存储总额' }]}
						name="totalStorage"
					>
						<InputNumber
							placeholder="请输入"
							min={0}
							style={{ width: '100%' }}
							addonAfter="GB"
						/>
					</FormItem>
					{volumeType === 'CSI-LVM' && (
						<FormItem
							label="VG名称"
							required
							rules={[
								{
									required: true,
									message: '请填写VG名称'
								}
							]}
							name="vgName"
						>
							<Input disabled />
						</FormItem>
					)}
				</Form>
				{/* </FormBlock> */}
				<Space>
					<Button type="primary" onClick={handleSubmit}>
						确认
					</Button>
					<Button
						onClick={() => {
							storage.setSession(
								'cluster-detail-current-tab',
								'storage'
							);
							history.goBack();
						}}
					>
						取消
					</Button>
				</Space>
			</ProContent>
		</ProPage>
	);
}

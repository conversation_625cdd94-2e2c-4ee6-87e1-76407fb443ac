declare interface resProps {
	readonly code?: number;
	data: any;
	errorMsg?: string | null;
	errorDetail?: string | null;
	success?: boolean;
	count?: number | null;
	errorStack?: StackTraceElement[] | null;
}
declare interface AgentItem {
	address: string;
	arch: string;
	controller: string;
	date: string | null;
	name: string;
	namespace: string;
	osType: string;
	osVersion: string | null;
	phase: string;
	port: number;
	version: string;
	clusterId: string;
	relationMiddlewareNum: number;
}
declare interface GetAgentsRes extends resProps {
	data: AgentItem[];
}

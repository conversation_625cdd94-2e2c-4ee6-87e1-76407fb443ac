import React, { useEffect, useState } from 'react';
import {
	<PERSON><PERSON>,
	<PERSON>,
	Divider,
	Drawer,
	Form,
	Input,
	InputNumber,
	Radio,
	Select,
	Spin,
	notification,
	Modal
} from 'antd';
import moment from 'moment';
import Actions from '@/components/Actions';
import ProTable from '@/components/ProTable';
import AgentImg from '@/assets/images/agent.png';
// * E charts v5
import ReactEChartsCore from 'echarts-for-react/lib/core';
import * as echarts from 'echarts/core';
import { PieChart } from 'echarts/charts';
import {
	GridComponent,
	TooltipComponent,
	TitleComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { formItemLayout618 } from '@/utils/const';
import { CopyOutlined } from '@ant-design/icons';
import { agentPhaseRender, copyValue } from '@/utils/utils';
import {
	deleteAgent,
	getAgentControllers,
	getAgentOrder,
	getAgents,
	getOrganAgentList
} from '@/services/agent';
import './index.less';
import storage from '@/utils/storage';
import { useLocation } from 'react-router';
import { FiltersProps } from '@/types/comment';
import { objectRemoveDuplicatesByKey } from '@/utils/utils';
import pattern from '@/utils/pattern';
// Register the required components
echarts.use([
	TitleComponent,
	TooltipComponent,
	GridComponent,
	PieChart,
	CanvasRenderer
]);
const { confirm } = Modal;
function getAgentOptions(value: any, title?: string) {
	const option = {
		title: {
			text: title || '服务器客户端状态分布',
			textStyle: {
				fontSize: 12
			},
			top: 10,
			left: 10
		},
		tooltip: {
			trigger: 'item'
		},
		legend: {
			orient: 'vertical',
			right: 50,
			top: 'center'
		},
		series: [
			{
				type: 'pie',
				right: 200,
				radius: ['40%', '70%'],
				label: {
					show: false,
					position: 'center'
				},
				emphasis: {
					label: {
						show: true,
						fontSize: 25,
						fontWeight: 'bold'
					}
				},
				labelLine: {
					show: false
				},
				data: value
			}
		]
	};
	return option;
}
const LinkButton = Actions.LinkButton;
export default function AgentContent(props: { refresh: boolean }): JSX.Element {
	const { refresh } = props;
	const [form] = Form.useForm();
	const location = useLocation();
	const organId = storage.getSession('organId');
	const [open, setOpen] = useState<boolean>(false);
	const [installOrder, setInstallOrder] = useState<string>('');
	const [runOrder, setRunOrder] = useState<string>('');
	const [spinning, setSpinning] = useState<boolean>(false);
	const [dataSource, setDataSource] = useState<AgentItem[]>([]);
	const [showDataSource, setShowDataSource] = useState<AgentItem[]>([]);
	const [controllerFilters, setControllerFilters] = useState<FiltersProps[]>(
		[]
	);
	const [option1, setOption1] = useState<any>(
		getAgentOptions([
			{ value: 0, name: '运行中' },
			{ value: 0, name: '离线' },
			{ value: 0, name: '卸载中' }
		])
	);
	const [option2, setOption2] = useState<any>(getAgentOptions([]));
	const [controllers, setControllers] = useState([]);
	const [loading, setLoading] = useState<boolean>(false);
	useEffect(() => {
		async function getAllData() {
			try {
				setSpinning(true);
				if (location.pathname === '/platform/agentManagement') {
					await getData();
				} else {
					if (organId) {
						await getOrganData();
					}
				}
				if (location.pathname === '/platform/agentManagement') {
					const res = await getAgentControllers();
					if (res.success) {
						setControllers(res.data);
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				} else {
					if (organId) {
						const res = await getAgentControllers({ organId });
						if (res.success) {
							setControllers(res.data);
						} else {
							notification.error({
								message: '错误',
								description: (
									<>
										<p>{res.errorMsg}</p>
										<p>{res.errorDetail}</p>
									</>
								)
							});
						}
					}
				}
				setSpinning(false);
			} catch {
				setSpinning(false);
			}
		}
		let mounted = true;
		if (mounted) {
			getAllData();
		}
		return () => {
			mounted = false;
		};
	}, [organId, refresh]);
	const getData = async () => {
		const res = await getAgents();
		if (res.success) {
			const controllerList = res.data?.map((item) => ({
				text: item.controller,
				value: item.clusterId
			}));
			setDataSource(res.data);
			setShowDataSource(res.data);
			setControllerFilters(
				objectRemoveDuplicatesByKey(controllerList, 'value')
			);
			const onlineTemp = res.data.filter(
				(item) => item.phase === 'Online'
			).length;
			const offlineTemp = res.data.filter(
				(item) => item.phase === 'Offline'
			).length;
			const terminatingTemp = res.data.filter(
				(item) => item.phase === 'Terminating'
			).length;
			setOption1(
				getAgentOptions([
					{ value: onlineTemp, name: '运行中' },
					{ value: offlineTemp, name: '离线' },
					{ value: terminatingTemp, name: '卸载中' }
				])
			);
			const l = res.data.map((item) => item.controller);
			let lt: any = Array.from(new Set(l));
			lt = lt.map((i: string) => {
				const it = res.data.filter((p) => p.controller === i).length;
				return {
					value: it,
					name: i
				};
			});
			setOption2(getAgentOptions(lt, '服务器客户端控制器分布'));
		} else {
			notification.error({
				message: '错误',
				description: (
					<>
						<p>{res.errorMsg}</p>
						<p>{res.errorDetail}</p>
					</>
				)
			});
		}
	};
	const getOrganData = async () => {
		const res = await getOrganAgentList({ organId });
		if (res.success) {
			const controllerList = res.data?.map((item) => ({
				text: item.controller,
				value: item.clusterId
			}));
			setDataSource(res.data);
			setShowDataSource(res.data);
			setControllerFilters(
				objectRemoveDuplicatesByKey(controllerList, 'value')
			);
			const onlineTemp = res.data.filter(
				(item) => item.phase === 'Online'
			).length;
			const offlineTemp = res.data.filter(
				(item) => item.phase === 'Offline'
			).length;
			const terminatingTemp = res.data.filter(
				(item) => item.phase === 'Terminating'
			).length;
			setOption1(
				getAgentOptions([
					{ value: onlineTemp, name: '运行中' },
					{ value: offlineTemp, name: '离线' },
					{ value: terminatingTemp, name: '卸载中' }
				])
			);
			const l = res.data.map((item) => item.controller);
			let lt: any = Array.from(new Set(l));
			lt = lt.map((i: string) => {
				const it = res.data.filter((p) => p.controller === i).length;
				return {
					value: it,
					name: i
				};
			});
			setOption2(getAgentOptions(lt, '服务器客户端控制器分布'));
		} else {
			notification.error({
				message: '错误',
				description: (
					<>
						<p>{res.errorMsg}</p>
						<p>{res.errorDetail}</p>
					</>
				)
			});
		}
	};
	const generate = async () => {
		await form.validateFields();
		const values = form.getFieldsValue();
		setLoading(true);
		if (location.pathname === '/organization/agent') {
			values.organ = organId;
		}
		const res = await getAgentOrder(values);
		if (res.success) {
			setInstallOrder(res.data.installCmd);
			setRunOrder(res.data.runCmd);
		} else {
			notification.error({
				message: '错误',
				description: (
					<>
						<p>{res.errorMsg}</p>
						<p>{res.errorDetail}</p>
					</>
				)
			});
		}
		setLoading(false);
	};
	const Operation = {
		primary: (
			<Button type="primary" onClick={() => setOpen(true)}>
				安装主机客户端
			</Button>
		)
	};
	const handleSearch = (value: string) => {
		const lt = dataSource.filter((item) => item.name.includes(value));
		setShowDataSource(lt);
	};
	const actionRender = (value: any, record: AgentItem) => {
		return (
			<Actions>
				<LinkButton
					disabled={record.phase === 'Terminating'}
					onClick={() => {
						if (
							record.relationMiddlewareNum &&
							record.relationMiddlewareNum !== 0
						) {
							notification.warning({
								message: '提示',
								description:
									'该客户端存在已接入的服务，无法卸载！'
							});
							return;
						}
						confirm({
							title: '操作提示',
							content: '请确定是否卸载该客户端？',
							onOk() {
								return deleteAgent({
									clusterId: record.clusterId,
									name: record.name
								})
									.then((res) => {
										if (res.success) {
											notification.success({
												message: '成功',
												description: '卸载成功!'
											});
										} else {
											notification.error({
												message: '错误',
												description: (
													<>
														<p>{res.errorMsg}</p>
														<p>{res.errorDetail}</p>
													</>
												)
											});
										}
									})
									.finally(() => {
										if (
											location.pathname ===
											'/platform/agentManagement'
										) {
											getData();
										} else {
											getOrganData();
										}
									});
							}
						});
					}}
				>
					卸载
				</LinkButton>
			</Actions>
		);
	};
	return (
		<Spin spinning={spinning}>
			<h2>资源信息</h2>
			<div className="agent-management-content">
				<div className="agent-management-box">
					<ReactEChartsCore
						echarts={echarts}
						option={option1}
						notMerge={true}
						lazyUpdate={true}
						style={{
							height: '200px'
						}}
						showLoading={spinning}
					/>
				</div>
				<div className="agent-management-box">
					<ReactEChartsCore
						echarts={echarts}
						option={option2}
						notMerge={true}
						lazyUpdate={true}
						style={{
							height: '200px'
						}}
						showLoading={spinning}
					/>
				</div>
			</div>
			<h2>服务器及客户端列表</h2>
			<ProTable
				rowKey={(record) => record.name + Math.random()}
				dataSource={showDataSource}
				operation={Operation}
				search={{
					onSearch: handleSearch,
					placeholder: '请输入主机名称'
				}}
			>
				<ProTable.Column title="主机名称" dataIndex="name" />
				<ProTable.Column title="IP地址" dataIndex="address" />
				<ProTable.Column
					title="操作系统"
					dataIndex="osType"
					filterMultiple={false}
					filters={[
						{ text: 'linux', value: 'linux' },
						{ text: 'darwin', value: 'darwin' }
					]}
					onFilter={(value: any, record: AgentItem) =>
						record.osType === value
					}
				/>
				<ProTable.Column
					title="客户端状态"
					dataIndex="phase"
					render={agentPhaseRender}
					filterMultiple={false}
					filters={[
						{ text: '运行中', value: 'Online' },
						{ text: '离线', value: 'Offline' },
						{ text: '卸载中', value: 'Terminating' },
						{ text: '未知', value: 'unknown' }
					]}
					onFilter={(value: any, record: AgentItem) =>
						record.phase === value
					}
				/>
				<ProTable.Column title="客户端端口" dataIndex="port" />
				<ProTable.Column
					title="管控控制器"
					dataIndex="controller"
					filterMultiple={false}
					filters={controllerFilters}
					onFilter={(value: any, record: AgentItem) =>
						record.clusterId === value
					}
				/>
				<ProTable.Column
					title="更新时间"
					dataIndex="date"
					sorter={(a: AgentItem, b: AgentItem) =>
						moment(a.date).unix() - moment(b.date).unix()
					}
				/>
				<ProTable.Column
					title="操作"
					dataIndex="action"
					render={actionRender}
				/>
			</ProTable>
			<Drawer
				width={660}
				title="安装主机中间件客户端"
				placement="right"
				onClose={() => setOpen(false)}
				destroyOnClose={true}
				open={open}
			>
				<h2>安装指引</h2>
				<Card>
					<div className="agent-drawer-title">选择合适的安装方式</div>
					<Form
						{...formItemLayout618}
						labelAlign="left"
						colon={false}
						form={form}
						requiredMark="optional"
					>
						<Form.Item
							label="服务器系统"
							name="osType"
							required
							initialValue="linux"
						>
							<Radio.Group
								options={[{ label: 'Linux', value: 'linux' }]}
								optionType="button"
							/>
						</Form.Item>
						<Form.Item
							label="服务器架构"
							name="arch"
							required
							initialValue="x86"
						>
							<Radio.Group
								options={[
									{ label: 'x86', value: 'x86' },
									{ label: 'ARM', value: 'arm' }
								]}
								optionType="button"
							/>
						</Form.Item>
						<Form.Item
							label="受控控制器"
							name="clusterId"
							rules={[
								{ required: true, message: '请选择控制器' }
							]}
						>
							<Select
								placeholder="请选择控制器"
								style={{ width: '300px' }}
							>
								{controllers.map((item: any) => {
									return (
										<Select.Option
											key={item.id}
											value={item.id}
										>
											{item.name}
										</Select.Option>
									);
								})}
							</Select>
						</Form.Item>
						<Form.Item
							label="客户端端口"
							name="agentPort"
							rules={[
								{ required: true, message: '请输入端口号' }
							]}
						>
							<InputNumber
								placeholder="请输入端口号"
								style={{ width: '300px' }}
							/>
						</Form.Item>
						<Form.Item
							label="安装目录"
							name="workDir"
							rules={[
								{ required: true, message: '请输入安装目录' },
								{
									pattern: new RegExp(pattern.agentPath),
									message:
										'请输入以/开头，由字母、数字及特殊字符“/.-_\'"”组成的安装目录信息'
								}
							]}
						>
							<Input
								placeholder="请输入安装目录"
								style={{ width: '300px' }}
							/>
						</Form.Item>
						<Button
							type="primary"
							onClick={generate}
							loading={loading}
						>
							生成
						</Button>
					</Form>
					<div className="mt-16 mb-8">客户端安装命令</div>
					<div>
						<Input
							placeholder="请输入"
							value={installOrder}
							addonAfter={
								<CopyOutlined
									onClick={() => copyValue(installOrder)}
								/>
							}
						/>
					</div>
					<div className="mt-16 mb-8">客户端运行命令</div>
					<div>
						<Input
							placeholder="请输入"
							value={runOrder}
							addonAfter={
								<CopyOutlined
									onClick={() => copyValue(runOrder)}
								/>
							}
						/>
					</div>
					<Divider />
					<div className="agent-drawer-title">判断是否安装成功</div>
					<div className="mb-8">
						执行命令：ps -ef | grep
						agent查看进程是否有运行，有运行则安装正常
					</div>
					<img width={555} src={AgentImg} />
					<div className="mt-8" style={{ color: '#d6d6d6' }}>
						注：若进程没有起来，可使用root用户手动执行命令，启动程序。命令为：/etc/extra-middleware/agent
					</div>
				</Card>
			</Drawer>
		</Spin>
	);
}

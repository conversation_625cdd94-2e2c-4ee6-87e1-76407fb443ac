import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, ProHeader, ProPage } from '@/components/ProPage';
import { Button } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import AgentContent from './content';
import { debounce } from '@/utils/utils';
import './index.less';

export default function AgentManagement(): JSX.Element {
	const [refresh, setRefresh] = useState<boolean>(false);

	return (
		<ProPage>
			<ProHeader
				title="客户端管理"
				extra={
					<Button
						onClick={debounce(() => setRefresh(!refresh))}
						id="detailRefresh"
						icon={<ReloadOutlined id="detailRefresh" />}
					/>
				}
			/>
			<ProContent>
				<AgentContent refresh={refresh} />
			</ProContent>
		</ProPage>
	);
}

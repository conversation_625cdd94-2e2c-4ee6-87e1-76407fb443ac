import React, { useEffect, useState } from 'react';
import {
	Radio,
	Input,
	RadioChangeEvent,
	Select,
	notification,
	Space
} from 'antd';
import { useHistory } from 'react-router';
import ProTable from '@/components/ProTable';
import { nullRender } from '@/utils/utils';
import { getProjectMiddleware } from '@/services/project';
import { ServiceListProps } from './projectDetail';
import { AutoCompleteOptionItem } from '@/types/comment';
import { connect } from 'react-redux';
import { StoreState } from '@/types';

const Search = Input.Search;
const RadioGroup = Radio.Group;
type SelectOption = AutoCompleteOptionItem;
function ServiceList(props: ServiceListProps): JSX.Element {
	const { organId, projectId, menu = [] } = props;
	const history = useHistory();
	const [tableDataSource, setTableDataSource] = useState<any[]>([]);
	const [dataSource, setDataSource] = useState<any[]>([]);
	const [tableType, setTableType] = useState<string>('cpu');
	const [loadingVisible, setLoadingVisible] = useState<boolean>(false);
	const [typeOptions, setTypeOptions] = useState<SelectOption[]>([]);
	const [selectType, setSelectType] = useState<string>('');
	useEffect(() => {
		organId && projectId && getData();
	}, [projectId, organId]);
	useEffect(() => {
		if (selectType) {
			setDataSource(
				tableDataSource.filter(
					(item: any) => item.name === selectType
				)[0].middlewareResourceInfoList
			);
		}
	}, [selectType]);
	const getData = () => {
		setLoadingVisible(true);
		getProjectMiddleware({ projectId, organId })
			.then((res) => {
				if (res.success) {
					setTableDataSource(res.data);
					if (res.data?.length > 0) {
						const lt = res.data?.map((item: any) => {
							return {
								value: item.name,
								label: item.aliasName
							};
						});
						setTypeOptions(lt);
						setSelectType(lt[0].value);
					} else {
						setTypeOptions([]);
						setSelectType('');
					}
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			})
			.finally(() => {
				setLoadingVisible(false);
			});
	};
	const handleSearch = (value: string) => {
		const list = tableDataSource
			.filter((item) => item.name === selectType)[0]
			.middlewareResourceInfoList.filter((i: any) =>
				i.name.includes(value)
			);
		setDataSource(list);
	};
	const onChange = (value: string) => {
		setSelectType(value);
	};
	const Operation = {
		primary: (
			<div
				style={{
					cursor: 'pointer',
					display: 'flex',
					alignItems: 'center',
					height: 32
				}}
			>
				<Space>
					<div>服务类型</div>
					<Select
						value={selectType}
						onChange={onChange}
						style={{ width: 120 }}
						options={typeOptions}
					/>
				</Space>
			</div>
		),
		secondary: (
			<>
				<Search
					placeholder="请输入服务名称搜索"
					onSearch={handleSearch}
					allowClear={true}
					style={{ width: '260px', marginRight: 8 }}
				/>
				<RadioGroup
					value={tableType}
					onChange={(e: RadioChangeEvent) =>
						setTableType(e.target.value)
					}
				>
					<Radio.Button id="cpu" value="cpu">
						CPU
					</Radio.Button>
					<Radio.Button id="memory" value="memory">
						内存
					</Radio.Button>
					<Radio.Button id="storage" value="storage">
						存储
					</Radio.Button>
				</RadioGroup>
			</>
		)
	};
	const nameRender = (value: string, record: any, index: number) => {
		const subMenu =
			menu?.find((item) => item.name === record.type)?.subMenu || [];
		const isLink = subMenu.find(
			(item: MenuResItem) => item.name === 'baseInfo'
		);

		return (
			<div style={{ maxWidth: '160px' }}>
				<div
					title={record.name}
					className={`${isLink ? 'name-link' : ''} text-overflow`}
					onClick={() => {
						if (!isLink) return;
						const list = tableDataSource.find(
							(item) => item.name === selectType
						);
						history.push(
							`/project/${list.type}/${list.name}/${list.aliasName}/container/basicInfo/${record.name}/${record.chartVersion}/${record.clusterId}/${record.namespace}`
						);
					}}
				>
					{record.name}
				</div>
				<div title={record.aliasName} className="text-overflow">
					{record.aliasName}
				</div>
			</div>
		);
	};
	return (
		<ProTable
			loading={loadingVisible}
			dataSource={dataSource}
			rowKey="name"
			operation={Operation}
		>
			<ProTable.Column
				title="服务名称/中文别名"
				dataIndex="name"
				render={nameRender}
				width={180}
			/>
			{tableType === 'cpu' && (
				<ProTable.Column
					title="CPU配额（核）"
					dataIndex="requestCpu"
					render={nullRender}
					width={200}
					sorter={(a: any, b: any) =>
						(a.requestCpu || 0) - (b.requestCpu || 0)
					}
				/>
			)}
			{tableType === 'cpu' && (
				<ProTable.Column
					title="近5min平均使用额（核）"
					dataIndex="per5MinCpu"
					render={nullRender}
					width={200}
					sorter={(a: any, b: any) =>
						(a.per5MinCpu || 0) - (b.per5MinCpu || 0)
					}
				/>
			)}
			{tableType === 'cpu' && (
				<ProTable.Column
					title="CPU使用率（%）"
					dataIndex="cpuRate"
					render={nullRender}
					width={200}
					sorter={(a: any, b: any) =>
						(a.cpuRate || 0) - (b.cpuRate || 0)
					}
				/>
			)}
			{tableType === 'memory' && (
				<ProTable.Column
					title="内存配额（GB）"
					dataIndex="requestMemory"
					render={nullRender}
					width={200}
					sorter={(a: any, b: any) =>
						(a.requestMemory || 0) - (b.requestMemory || 0)
					}
				/>
			)}
			{tableType === 'memory' && (
				<ProTable.Column
					title="近5min平均使用额（GB）"
					dataIndex="per5MinMemory"
					render={nullRender}
					width={200}
					sorter={(a: any, b: any) =>
						(a.per5MinMemory || 0) - (b.per5MinMemory || 0)
					}
				/>
			)}
			{tableType === 'memory' && (
				<ProTable.Column
					title="内存使用率（%）"
					dataIndex="memoryRate"
					render={nullRender}
					width={200}
					sorter={(a: any, b: any) =>
						(a.memoryRate || 0) - (b.memoryRate || 0)
					}
				/>
			)}
			{tableType === 'storage' && (
				<ProTable.Column
					title="存储配额（GB）"
					dataIndex="requestStorage"
					render={nullRender}
					width={200}
					sorter={(a: any, b: any) =>
						(a.requestStorage || 0) - (b.requestStorage || 0)
					}
				/>
			)}
			{tableType === 'storage' && (
				<ProTable.Column
					title="近5min平均使用额（GB）"
					dataIndex="per5MinStorage"
					render={nullRender}
					width={200}
					sorter={(a: any, b: any) =>
						(a.per5MinStorage || 0) - (b.per5MinStorage || 0)
					}
				/>
			)}
			{tableType === 'storage' && (
				<ProTable.Column
					title="存储使用率（%）"
					dataIndex="storageRate"
					render={nullRender}
					width={200}
					sorter={(a: any, b: any) =>
						(a.storageRate || 0) - (b.storageRate || 0)
					}
				/>
			)}
		</ProTable>
	);
}
export default connect((state: StoreState) => ({
	menu: state.auth.menu
}))(ServiceList);

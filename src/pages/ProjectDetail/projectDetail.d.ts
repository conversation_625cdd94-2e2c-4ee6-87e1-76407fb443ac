import { resProps } from '@/types/comment';
import { ProjectItem } from '../ProjectManage/project';
import { StorageItem } from '../ResourcePoolManagement/resource.pool';
export interface DetailParams {
	projectId: string;
	projectName: string;
	activeKey: string;
	organId: string;
}
export interface AddNamespaceProps {
	open: boolean;
	projectId: string;
	organId: string;
	onCancel: () => void;
	onRefresh: () => void;
	data?: NamespaceItem;
}
export interface AddNamespaceFieldValues {
	clusterId: string;
	source: string;
	namespace: string;
	projectId: string;
	aliasName: string;
	name: string;
}
export interface NamespaceItem {
	aliasName: string;
	availableDomain: boolean;
	clusterAliasName: string;
	clusterId: string;
	createTime: null;
	middlewareReplicas: null;
	name: string;
	organId: string;
	phase: null;
	projectId: string;
	projectName: string;
	containerUIDRange: {
		min: null | number;
		max: null | number;
	};
	containerGIDRange: {
		min: null | number;
		max: null | number;
	};
	quotas: {
		quotas: any;
		used: number;
		clusterId: string;
		cpu: {
			allocatable: number;
			request: number;
			total: number;
			usable: number;
			usage: number;
			used: number;
		};
		memory: {
			allocatable: number;
			request: number;
			total: number;
			usable: number;
			usage: number;
			used: number;
		};
		storageList: StorageItem[];
	};
	registered: boolean;
}
export interface getProjectNamespacesRes extends resProps {
	data: NamespaceItem[];
}
export interface AddMemberProps {
	visible: boolean;
	projectId: string;
	organId: string;
	onCancel: () => void;
	onRefresh: () => void;
}
export interface UserRoleItem {
	organId: string;
	power: any;
	projectId: null | string;
	projectName: string | null;
	roleId: null | number;
	roleName: string;
	userName: string;
}
export interface UserItem {
	aliasName: string;
	createTime: null | string;
	email: null | string;
	id: number;
	isAdmin: boolean;
	password: null | string;
	passwordTime: null;
	phone: null | string;
	power: any;
	roleId: null | number | string;
	roleName: null | string;
	userName: string;
	userRoleList: UserRoleItem[];
}
export interface EditMemberProps {
	visible: boolean;
	organId: string;
	onCancel: () => void;
	onRefresh: () => void;
	data: UserItem;
	projectId: string;
	isAccess: boolean;
}
export interface EditMemberFieldValues {
	userName: string;
	aliasName: string;
	roleId: number;
}
export interface ProjectDetailProps {
	project: ProjectItem;
}
export interface NamespaceProps {
	clusterList: clusterType[];
	setRefreshCluster: (flag: boolean) => void;
}
export interface ServiceListProps {
	organId: string;
	projectId: string;
	menu: any[];
}
export interface ProjectMiddlewareRes extends resProps {
	data: MiddlewareTableItem[];
}
export interface namespaceQuotaParams {
	organId: string;
	projectId: string;
	clusterId: string;
	projectName: string;
	namespace: string;
	namespaceAlias: string;
}
export interface NewNamespaceProps {
	setRefreshOrganization: (flag: boolean) => void;
	isAccess: boolean;
}

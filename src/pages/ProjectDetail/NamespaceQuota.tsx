import { Pro<PERSON>ontent, ProHeader, ProPage } from '@/components/ProPage';
import React, { useEffect, useState } from 'react';
import {
	Collapse,
	Form,
	Input,
	InputNumber,
	Tooltip,
	Empty,
	notification,
	Row,
	Col,
	Divider,
	Space,
	Button
} from 'antd';
import Decimal from 'decimal.js';
import { formItemLayout618 } from '@/utils/const';
import { useParams, useHistory } from 'react-router';
import { namespaceQuotaParams } from './projectDetail';
import { IconFont } from '@/components/IconFont';
import { CheckOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { formatNumber } from '@/utils/utils';
import {
	quotaItem,
	StorageAllotItem
} from '../OrganizationManagement/organization';
import {
	getNamespaceCpuMemory,
	getProjectCpuAndMemory,
	getProjectStorage
} from '@/services/project';
import {
	CpuMemoryItem,
	StorageListTableItem
} from '../OrganizationDetail/organization.detail';
import { regNamespace } from '@/services/common';
import { getNamespaceStorages } from '@/services/storage';
import MidSlider from '@/components/MidSlider';
import ClusterNumberDisplay from '@/components/ClusterNumberDisplay';
const { Panel } = Collapse;
function NamespaceQuota(): JSX.Element {
	const params: namespaceQuotaParams = useParams();
	const history = useHistory();
	const [quotaCpu, setQuotaCpu] = useState<quotaItem>({
		allocatable: 0,
		request: 0,
		total: 0,
		usable: 0,
		usage: 0,
		used: 0,
		oused: 0
	});
	const [quotaMemory, setQuotaMemory] = useState<quotaItem>({
		allocatable: 0,
		request: 0,
		total: 0,
		usable: 0,
		usage: 0,
		used: 0,
		oused: 0
	});
	const [cpuMemory, setCpuMemory] = useState<CpuMemoryItem>(); // * 当前命名空间所分配的集群的配额
	const [projectCpuMemory, setProjectCpuMemory] = useState<CpuMemoryItem>(); // * 当前项目下的集群配额
	const [storages, setStorages] = useState<StorageListTableItem[]>([]);
	const [orgSelectStorages, setOrgSelectStorages] = useState<
		StorageAllotItem[]
	>([]);
	const [selectStorages, setSelectStorages] = useState<StorageAllotItem[]>(
		[]
	);
	const [originStorage, setOriginStorage] = useState<CpuMemoryItem[]>();
	useEffect(() => {
		if (originStorage && selectStorages) {
			const cc = originStorage.find(
				(item) => item.clusterId === params.clusterId
			);
			const bt = cc?.storageList.map((item) => {
				const result: any = { ...item };
				result.clusterId = cc.clusterId;
				result.clusterNickName = cc.clusterNickName;
				result.storage = {
					...item.storage,
					oused: item.storage.used,
					used: selectStorages.find(
						(i) => i.storageId === item.storageId
					)?.used
				};
				return result;
			});
			setStorages(bt || []);
		}
	}, [originStorage, selectStorages]);
	useEffect(() => {
		// * 查询项目下的所有存储（显示）
		getProjectStorage({
			organId: params.organId,
			projectId: params.projectId,
			detail: true
		}).then((res) => {
			if (res.success) {
				setOriginStorage(res.data);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
		// * 获取分区下所有已分配的存户次（回显）
		getNamespaceStorages({
			clusterId: params.clusterId,
			namespace: params.namespace
		}).then((res) => {
			if (res.success) {
				if (res.data?.length > 0) {
					const list = res.data.map((item: any) => {
						return {
							clusterId: item.clusterId,
							storageId: item.storageId,
							request: item.quota.request,
							allocatable: item.quota?.request,
							used: item.quota?.used
						};
					});
					setOrgSelectStorages(list);
					setSelectStorages(list);
				} else {
					setSelectStorages([]);
				}
			}
		});
	}, []);
	useEffect(() => {
		if (cpuMemory && projectCpuMemory) {
			setQuotaCpu({
				...quotaCpu,
				used: cpuMemory.cpu.used,
				request:
					projectCpuMemory.cpu.request -
					projectCpuMemory.cpu.used +
					cpuMemory.cpu.request,
				allocatable: cpuMemory.cpu.request
			});
			setQuotaMemory({
				...quotaMemory,
				used: cpuMemory.memory.used,
				request:
					projectCpuMemory.memory.request -
					projectCpuMemory.memory.used +
					cpuMemory.memory.request,
				allocatable: cpuMemory.memory.request
			});
		}
	}, [cpuMemory, projectCpuMemory]);
	useEffect(() => {
		getProjectCpuAndMemory({
			organId: params.organId,
			projectId: params.projectId,
			detail: true
		}).then((res) => {
			if (res.success) {
				const cc = res.data.find(
					(item) => item.clusterId === params.clusterId
				);
				setProjectCpuMemory(cc);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
		getNamespaceCpuMemory({
			clusterId: params.clusterId,
			namespace: params.namespace
		}).then((res) => {
			if (res.success) {
				setCpuMemory(res.data);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	}, []);
	const onChange = (
		newValue: number | null,
		record: StorageListTableItem
	) => {
		// * 存储超分情况
		if (storageMaxCompute(record) < 0) {
			// * 当拖动/输入的存储不是非选中的存储的情况下
			if (
				selectStorages.findIndex(
					(item) => item.storageId === record.storageId
				) < 0
			) {
				const st = {
					storageId: record.storageId as string,
					allocatable:
						(newValue as number) <= storageMaxCompute(record)
							? (newValue as number)
							: storageMaxCompute(record),
					clusterId: record.clusterId,
					used: record.storage.used
				};
				setSelectStorages([st, ...selectStorages]);
			} else {
				const list = selectStorages.map((item) => {
					const result: any = { ...item };
					if (item.storageId === record.storageId) {
						result.allocatable =
							(newValue as number) <= storageMaxCompute(record)
								? newValue
								: storageMaxCompute(record);
					}
					return result;
				});
				setSelectStorages(list);
			}
		} else {
			// * 当拖动/输入的存储不是非选中的存储的情况下
			if (
				selectStorages.findIndex(
					(item) => item.storageId === record.storageId
				) < 0
			) {
				const st = {
					storageId: record.storageId as string,
					allocatable: newValue as number,
					clusterId: record.clusterId,
					used: record.storage.used
				};
				setSelectStorages([st, ...selectStorages]);
			} else {
				const list = selectStorages.map((item) => {
					const result: any = { ...item };
					if (item.storageId === record.storageId) {
						result.allocatable = newValue;
					}
					return result;
				});
				setSelectStorages(list);
			}
		}
	};
	const handleChange = (value: number | null, type: string) => {
		if (type === 'cpu') {
			setQuotaCpu({
				...quotaCpu,
				allocatable: value || 0
			});
		} else {
			setQuotaMemory({
				...quotaMemory,
				allocatable: value || 0
			});
		}
	};
	const handleCheckStorage = (record: StorageListTableItem) => {
		if (
			selectStorages.find((item) => item.storageId === record.storageId)
		) {
			const st = selectStorages.find(
				(item) => item.storageId === record.storageId
			);
			if (st?.used && st.used !== 0) {
				notification.error({
					message: '错误',
					description: '当前存储已被使用，无法取消分配！'
				});
				return;
			}
			const list = selectStorages.filter(
				(item: StorageAllotItem) => item.storageId !== record.storageId
			);
			setSelectStorages(list);
		} else {
			if (
				orgSelectStorages.find(
					(item) => item.storageId === record.storageId
				)
			) {
				const temp = orgSelectStorages.find(
					(item) => item.storageId === record.storageId
				);
				setSelectStorages([
					...selectStorages,
					temp as StorageAllotItem
				]);
			} else {
				setSelectStorages([
					...selectStorages,
					{
						storageId: record.storageId as string,
						allocatable: record.storage.allocatable as number,
						clusterId: record.clusterId,
						used: record.storage.used
					}
				]);
			}
		}
	};
	const storageMaxCompute: (record: StorageListTableItem) => number = (
		record: StorageListTableItem
	) => {
		const st = orgSelectStorages.find(
			(i) => i.storageId === record.storageId
		);
		const result =
			record.storage.request -
			(record.storage.oused || 0) +
			(st?.request || 0);
		return result;
	};
	const handleSubmit = () => {
		if (quotaCpu.allocatable === 0 || !quotaCpu.allocatable) {
			notification.error({
				message: '错误',
				description: '请分配CPU配额！'
			});
			return;
		}
		if (quotaMemory.allocatable === 0 || !quotaMemory.allocatable) {
			notification.error({
				message: '错误',
				description: '请分配内存配额！'
			});
			return;
		}
		if (quotaCpu.allocatable < quotaCpu.used) {
			notification.error({
				message: '错误',
				description: '当前CPU分配配额小于已使用，请重新分配！'
			});
			return;
		}
		if (quotaMemory.allocatable < quotaMemory.used) {
			notification.error({
				message: '错误',
				description: '当前内存分配配额小于已使用，请重新分配！'
			});
			return;
		}
		if (quotaCpu.allocatable > quotaCpu.request) {
			notification.error({
				message: '错误',
				description: '当前CPU分配配额超过可分配配额！'
			});
			return;
		}
		if (quotaMemory.allocatable > quotaMemory.request) {
			notification.error({
				message: '错误',
				description: '当前内存分配配额超过可分配配额！'
			});
			return;
		}
		if (
			selectStorages.some(
				(item) => item.used && item.allocatable < item.used
			)
		) {
			notification.error({
				message: '错误',
				description: '当前存储配额小于已使用值！'
			});
			return;
		}
		if (
			selectStorages.some(
				(item) => item.allocatable === 0 || !item.allocatable
			)
		) {
			notification.error({
				message: '错误',
				description: '请分配存储配额'
			});
			return;
		}
		const sl = selectStorages.map((item) => {
			const result: any = {};
			result.storageId = item.storageId;
			result.storage = {};
			result.storage.request = item.allocatable;
			return result;
		});
		const sendData = {
			clusterId: params.clusterId,
			name: params.namespace,
			aliasName: params.namespaceAlias,
			quotas: {
				clusterId: params.clusterId,
				cpu: {
					request: quotaCpu.allocatable
				},
				memory: {
					request: quotaMemory.allocatable
				},
				storageList: sl
			}
		};
		regNamespace(sendData).then((res) => {
			if (res.success) {
				notification.success({
					message: '成功',
					description: '命名空间分配配额成功'
				});
				history.goBack();
			} else {
				notification.error({
					message: '错误',
					description: res.errorMsg
				});
			}
		});
	};
	return (
		<ProPage>
			<ProHeader
				title="管理命名空间配额"
				onBack={() => {
					history.goBack();
				}}
			/>
			<ProContent>
				<h2>选择命名空间</h2>
				<Form.Item
					{...formItemLayout618}
					labelAlign="left"
					style={{ width: '585px' }}
				>
					<Input disabled value={params.namespaceAlias} />
				</Form.Item>
				<h2>分配配额</h2>
				<div className="cluster-quota-display-content">
					<div className="cluster-quota-cpu-display">
						<div className="cluster-quota-cpu-title">
							<IconFont
								type="icon-quota"
								style={{ fontSize: 46 }}
							/>
							<h3>CPU(单位：核)</h3>
						</div>
						<div className="cluster-quota-cpu-content">
							<div className="cluster-quota-cpu-item">
								<span>
									当前分配总量
									<Tooltip title="当前集群下项目分配到命名空间的特定资源对象（包含CPU、内存、存储、备份服务器）">
										<QuestionCircleOutlined
											style={{ marginLeft: 4 }}
										/>
									</Tooltip>{' '}
									：
								</span>
								<ClusterNumberDisplay
									value={quotaCpu?.allocatable || 0}
								/>
							</div>
							<div className="cluster-quota-cpu-item">
								<span>
									当前可缩容量
									<Tooltip title="当前命名空间资源可缩容量=当前分配总量-当前使用总量">
										<QuestionCircleOutlined
											style={{ marginLeft: 4 }}
										/>
									</Tooltip>{' '}
									：
								</span>
								<ClusterNumberDisplay
									value={new Decimal(
										quotaCpu?.allocatable || 0
									)
										.minus(new Decimal(quotaCpu?.used || 0))
										.toNumber()}
								/>
							</div>
							<div className="cluster-quota-cpu-item">
								<span>
									当前使用总量
									<Tooltip title="当前命名空间已分配得到的总量中，有部分资源已被该命名空间下的服务使用">
										<QuestionCircleOutlined
											style={{ marginLeft: 4 }}
										/>
									</Tooltip>{' '}
									：
								</span>
								<ClusterNumberDisplay
									value={quotaCpu?.used || 0}
								/>
							</div>
							<div className="cluster-quota-cpu-item">
								<span>
									当前可扩容量
									<Tooltip title="当前命名空间资源可扩容量=该集群下项目获得的该资源对象（包含CPU、内存、存储、备份服务器）总配额 - 已分配给其他命名空间的资源配额-当前分配总量">
										<QuestionCircleOutlined
											style={{ marginLeft: 4 }}
										/>
									</Tooltip>{' '}
									：
								</span>
								<ClusterNumberDisplay
									value={new Decimal(quotaCpu?.request)
										.minus(
											new Decimal(
												quotaCpu?.allocatable || 0
											)
										)
										.toNumber()}
								/>
							</div>
						</div>
					</div>
					<div className="cluster-quota-memory-display">
						<div className="cluster-quota-memory-title">
							<IconFont
								type="icon-quota"
								style={{ fontSize: 46 }}
							/>
							<h3>内存(单位：GB)</h3>
						</div>
						<div className="cluster-quota-memory-content">
							<div className="cluster-quota-memory-item">
								<span>
									当前分配总量
									<Tooltip title="当前集群下项目分配到命名空间的特定资源对象（包含CPU、内存、存储、备份服务器）">
										<QuestionCircleOutlined
											style={{ marginLeft: 4 }}
										/>
									</Tooltip>{' '}
									：
								</span>
								<ClusterNumberDisplay
									value={quotaMemory?.allocatable || 0}
								/>
							</div>
							<div className="cluster-quota-memory-item">
								<span>
									当前可缩容量
									<Tooltip title="当前命名空间资源可缩容量=当前分配总量-当前使用总量">
										<QuestionCircleOutlined
											style={{ marginLeft: 4 }}
										/>
									</Tooltip>{' '}
									：
								</span>
								<ClusterNumberDisplay
									value={new Decimal(
										quotaMemory?.allocatable || 0
									)
										.minus(
											new Decimal(quotaMemory?.used || 0)
										)
										.toNumber()}
								/>
							</div>
							<div className="cluster-quota-memory-item">
								<span>
									当前使用总量
									<Tooltip title="当前命名空间已分配得到的总量中，有部分资源已被该命名空间下的服务使用">
										<QuestionCircleOutlined
											style={{ marginLeft: 4 }}
										/>
									</Tooltip>{' '}
									：
								</span>
								<ClusterNumberDisplay
									value={quotaMemory?.used || 0}
								/>
							</div>
							<div className="cluster-quota-memory-item">
								<span>
									当前可扩容量
									<Tooltip title="当前命名空间资源可扩容量=该集群下项目获得的该资源对象（包含CPU、内存、存储、备份服务器）总配额 - 已分配给其他命名空间的资源配额-当前分配总量">
										<QuestionCircleOutlined
											style={{ marginLeft: 4 }}
										/>
									</Tooltip>{' '}
									：
								</span>
								<ClusterNumberDisplay
									value={new Decimal(quotaMemory?.request)
										.minus(
											new Decimal(
												quotaMemory?.allocatable || 0
											)
										)
										.toNumber()}
								/>
							</div>
						</div>
					</div>
				</div>
				<div className="cluster-slider">
					<div className="cluster-slider-action">
						<div className="cluster-slider-title">
							<div className="cluster-namespace-form-label">
								CPU（单位：Core）
							</div>
						</div>
						<div className="cluster-slider-input-number-content">
							<InputNumber
								min={0}
								max={quotaCpu.request}
								style={{ width: '120px' }}
								addonAfter="Core"
								step={0.1}
								precision={1}
								value={quotaCpu?.allocatable}
								onChange={(value: number | null) =>
									handleChange(value, 'cpu')
								}
								onBlur={() => {
									if (
										(quotaCpu.allocatable || 0) <
										quotaCpu.used
									) {
										setQuotaCpu({
											...quotaCpu,
											allocatable: quotaCpu.used
										});
									}
								}}
							/>
						</div>
					</div>
					<MidSlider
						min={0}
						max={quotaCpu.request}
						value={quotaCpu.allocatable}
						used={quotaCpu.used || 0}
						setValue={(value) => {
							setQuotaCpu({
								...quotaCpu,
								allocatable: value
							});
						}}
						step={0.1}
						tooltip={`已使用${formatNumber(quotaCpu.used, 0)}核`}
						type="cpu"
					/>
				</div>
				<div className="cluster-slider">
					<div className="cluster-slider-action">
						<div className="cluster-slider-title">
							<div className="cluster-namespace-form-label">
								内存（单位：GB）
							</div>
						</div>
						<div className="cluster-slider-input-number-content">
							<InputNumber
								min={0}
								max={quotaMemory.request}
								style={{ width: '120px' }}
								addonAfter="GB"
								step={0.1}
								precision={1}
								value={quotaMemory?.allocatable}
								onChange={(value: number | null) =>
									handleChange(value, 'memory')
								}
								onBlur={() => {
									if (
										(quotaMemory.allocatable || 0) <
										quotaMemory.used
									) {
										setQuotaMemory({
											...quotaMemory,
											allocatable: quotaMemory.used
										});
									}
								}}
							/>
						</div>
					</div>
					<MidSlider
						min={0}
						max={quotaMemory.request}
						used={quotaMemory.used || 0}
						step={0.1}
						tooltip={`已使用${quotaMemory.used}GB`}
						type="memory"
						value={quotaMemory.allocatable}
						setValue={(value) => {
							setQuotaMemory({
								...quotaMemory,
								allocatable: value
							});
						}}
					/>
				</div>
				<h2>存储配额</h2>
				<Collapse expandIconPosition="end" defaultActiveKey={['1']}>
					<Panel key="1" header="存储配额分配概览">
						<div className="organization-quota-storage-content">
							{storages.length === 0 && (
								<Empty
									style={{
										width: '100%'
									}}
									image={Empty.PRESENTED_IMAGE_SIMPLE}
									description={<div>当前无数据</div>}
								/>
							)}
							{storages.length !== 0 && (
								<div className="organization-quota-storage">
									{storages.map(
										(
											item: StorageListTableItem,
											index: number
										) => {
											return (
												<div
													className="organization-quota-storage-item"
													key={index}
													style={
														selectStorages.find(
															(
																i: StorageAllotItem
															) =>
																i.storageId ===
																item.storageId
														)
															? {
																	borderColor:
																		'#226ee7'
															  }
															: {}
													}
												>
													<div
														className="organization-quota-storage-title"
														onClick={() =>
															handleCheckStorage(
																item
															)
														}
													>
														<div className="organization-quota-storage-title-content">
															<IconFont
																type="icon-quota"
																style={{
																	fontSize: 28,
																	marginRight: 16
																}}
															/>
															<div className="storage-title-content">
																<p>
																	(
																	{
																		item
																			?.storageType?.[0]
																	}
																	)
																	{item?.name}
																	(GB)
																	{item
																		?.storageType
																		.length >
																		1 && (
																		<span className="available-domain">
																			可用区
																		</span>
																	)}
																</p>
																<p>
																	{
																		item?.clusterNickName
																	}
																</p>
															</div>
														</div>
														{selectStorages.find(
															(
																i: StorageAllotItem
															) =>
																i?.storageId ===
																item?.storageId
														) && (
															<div>
																<CheckOutlined
																	style={{
																		fontSize: 17,
																		color: '#226ee7'
																	}}
																/>
															</div>
														)}
													</div>
													<Row>
														<Col span={20}>
															<MidSlider
																max={storageMaxCompute(
																	item
																)}
																value={
																	selectStorages.find(
																		(i) =>
																			i.storageId ===
																			item.storageId
																	)
																		?.allocatable
																}
																used={
																	item.storage
																		.used ||
																	0
																}
																type="storage"
																setValue={(
																	value
																) =>
																	onChange(
																		value,
																		item
																	)
																}
																tooltip={`已使用${formatNumber(
																	item.storage
																		.used
																)}GB`}
																style={{
																	width: '99%'
																}}
																step={1}
															/>
														</Col>
														<Col span={4}>
															<InputNumber
																min={0}
																max={storageMaxCompute(
																	item
																)}
																value={
																	selectStorages.find(
																		(i) =>
																			i.storageId ===
																			item.storageId
																	)
																		?.allocatable ||
																	0
																}
																onChange={(
																	value:
																		| number
																		| null
																) =>
																	onChange(
																		value,
																		item
																	)
																}
																step={1}
																precision={0}
																addonAfter="GB"
															/>
														</Col>
													</Row>
												</div>
											);
										}
									)}
								</div>
							)}
						</div>
					</Panel>
				</Collapse>
				<Divider />
				<Space>
					<Button type="primary" onClick={handleSubmit}>
						确定
					</Button>
					<Button onClick={() => window.history.back()}>取消</Button>
				</Space>
			</ProContent>
		</ProPage>
	);
}
export default NamespaceQuota;

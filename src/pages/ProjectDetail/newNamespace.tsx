import React, { useState, useEffect } from 'react';
import { useHistory } from 'react-router';
import { ColumnFilterItem } from 'antd/lib/table/interface';
import { Button, notification, Modal } from 'antd';
import ProTable from '@/components/ProTable';
import MidProgress from '@/components/MidProgress';
import { NamespaceItem } from './projectDetail';
import { getProjectNamespace } from '@/services/project';
import { nullRender, objectRemoveDuplicatesByKey } from '@/utils/utils';
import Actions from '@/components/Actions';
import AddNamespace from './addNamespace';
import { deleteNamespace } from '@/services/common';
import storage from '@/utils/storage';
import './index.less';

const { confirm } = Modal;
const LinkButton = Actions.LinkButton;
function NewNamespace(): JSX.Element {
	// * 是否接入了观云台
	const isAccess = storage.getLocal('isAccessGYT');
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const project = storage.getSession('project');
	const history = useHistory();
	const [namespaces, setNamespaces] = useState<NamespaceItem[]>([]);
	const [dataSource, setDataSource] = useState<NamespaceItem[]>([]);
	const [editNamespace, setEditNamespace] = useState<NamespaceItem>();
	const [open, setOpen] = useState<boolean>(false);
	const [clusterFilters, setClusterFilters] = useState<ColumnFilterItem[]>(
		[]
	);
	useEffect(() => {
		let mounted = true;
		if (mounted) {
			getData();
		}
		return () => {
			mounted = false;
		};
	}, [projectId]);
	const Operation = {
		primary: (
			<Button
				type="primary"
				onClick={() => {
					setEditNamespace(undefined);
					setOpen(true);
				}}
				disabled={isAccess}
				title={isAccess ? '平台已接入观云台，请联系观云台管理员' : ''}
			>
				新增
			</Button>
		)
	};
	const getData = () => {
		getProjectNamespace({
			organId,
			projectId,
			withQuota: true,
			withMiddleware: true
		}).then((res) => {
			if (res.success) {
				setNamespaces(res.data);
				setDataSource(res.data);
				const list = res.data.map((item) => {
					return {
						value: item.clusterAliasName,
						text: item.clusterAliasName
					};
				});
				setClusterFilters(objectRemoveDuplicatesByKey(list, 'value'));
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const handleSearch = (value: string) => {
		const list = namespaces.filter((item: NamespaceItem) =>
			item.aliasName.includes(value)
		);
		setDataSource(list);
	};
	const memoryRender = (value: any, record: NamespaceItem, index: number) => {
		return (
			<MidProgress
				fromColor="#1AC1C4"
				toColor="#74DDDF"
				unit="GB"
				used={record.quotas?.memory.used}
				total={record.quotas?.memory.request}
			/>
		);
	};
	const cpuRender = (value: any, record: NamespaceItem, index: number) => {
		return (
			<MidProgress
				fromColor="#5C0EDF"
				toColor="#853CFF"
				unit="Core"
				used={record.quotas?.cpu.used}
				total={record.quotas?.cpu.request}
			/>
		);
	};
	const actionRender = (value: any, record: NamespaceItem, index: number) => {
		return (
			<Actions>
				<LinkButton
					disabled={isAccess}
					title={
						isAccess ? '平台已接入观云台，请联系观云台管理员' : ''
					}
					onClick={() => {
						history.push(
							`/project/projectManagement/namespace/${organId}/${projectId}/namespace/${project.name}/namespaceQuota/${record.clusterId}/${record.name}/${record.aliasName}`
						);
					}}
				>
					管理配额
				</LinkButton>
				<LinkButton
					onClick={() => {
						setEditNamespace(record);
						setOpen(true);
					}}
					disabled={isAccess}
					title={
						isAccess ? '平台已接入观云台，请联系观云台管理员' : ''
					}
				>
					编辑
				</LinkButton>
				<LinkButton
					disabled={isAccess}
					title={
						isAccess ? '平台已接入观云台，请联系观云台管理员' : ''
					}
					onClick={() => {
						confirm({
							title: '操作确认',
							content: '请确认是否要删除该命名空间？',
							onOk: () => {
								return deleteNamespace({
									clusterId: record.clusterId,
									name: record.name
								}).then((res) => {
									if (res.success) {
										notification.success({
											message: '成功',
											description: '命名空间删除成功'
										});
										getData();
									} else {
										notification.error({
											message: '失败',
											description: (
												<>
													<p>{res.errorMsg}</p>
													<p>{res.errorDetail}</p>
												</>
											)
										});
									}
								});
							}
						});
					}}
				>
					删除
				</LinkButton>
			</Actions>
		);
	};
	return (
		<div className="mt-8">
			<ProTable
				showRefresh
				onRefresh={getData}
				operation={Operation}
				search={{
					onSearch: handleSearch,
					placeholder: '请输入命名空间名称搜索'
				}}
				dataSource={dataSource}
				pagination={{
					hideOnSinglePage: true
				}}
				rowKey={(record) => record.name + record.clusterId}
			>
				<ProTable.Column
					dataIndex="aliasName"
					title="命名空间名称"
					width={200}
					ellipsis={true}
				/>
				<ProTable.Column
					dataIndex="name"
					title="命名空间英文名"
					width={200}
					ellipsis={true}
				/>
				<ProTable.Column
					dataIndex="cpu"
					title="CPU配额(核)"
					render={cpuRender}
					sorter={(a: NamespaceItem, b: NamespaceItem) => {
						const aPer = a.quotas
							? a.quotas.cpu.used / a.quotas.cpu.request
							: 0;
						const bPer = b.quotas
							? b.quotas.cpu.used / b.quotas.cpu.request
							: 0;
						return aPer - bPer;
					}}
				/>
				<ProTable.Column
					dataIndex="memory"
					title="内存配额(GB)"
					render={memoryRender}
					sorter={(a: NamespaceItem, b: NamespaceItem) => {
						const aPer = a.quotas
							? a.quotas.memory.used / a.quotas.memory.request
							: 0;
						const bPer = b.quotas
							? b.quotas.memory.used / b.quotas.memory.request
							: 0;
						console.log(aPer, bPer);
						return aPer - bPer;
					}}
				/>
				<ProTable.Column
					dataIndex="middlewareReplicas"
					title="已发布服务"
					render={nullRender}
					width={100}
					sorter={(a: NamespaceItem, b: NamespaceItem) =>
						(a.middlewareReplicas || 0) -
						(b.middlewareReplicas || 0)
					}
				/>
				<ProTable.Column
					dataIndex="clusterAliasName"
					title="所属集群"
					filters={clusterFilters}
					filterMultiple={false}
					onFilter={(value, record: NamespaceItem) =>
						record.clusterAliasName === value
					}
				/>
				<ProTable.Column
					dataIndex="action"
					title="操作"
					render={actionRender}
				/>
			</ProTable>
			{open && (
				<AddNamespace
					projectId={projectId}
					organId={organId}
					onRefresh={() => {
						getData();
					}}
					open={open}
					onCancel={() => setOpen(false)}
					data={editNamespace}
				/>
			)}
		</div>
	);
}
export default NewNamespace;

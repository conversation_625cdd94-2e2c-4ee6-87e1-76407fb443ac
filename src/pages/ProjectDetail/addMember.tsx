import React, { useEffect, useState } from 'react';
import { Modal, Select, notification } from 'antd';
import ProTable from '@/components/ProTable';
import { getProjectMember } from '@/services/project';
import { getRoleList } from '@/services/role';
import { bindProjectMember } from '@/services/project';
import { nullRender } from '@/utils/utils';

import { roleProps } from '../RoleManage/role';
import { userProps } from '../UserManage/user';
import { AddMemberProps } from './projectDetail';
import storage from '@/utils/storage';

const Option = Select.Option;
export default function AddMember(props: AddMemberProps): JSX.Element {
	const { visible, onCancel, onRefresh, projectId, organId } = props;
	const [dataSource, setDataSource] = useState<userProps[]>([]);
	const [showDataSource, setShowDataSource] = useState<userProps[]>([]);
	const [primaryKeys, setPrimaryKeys] = useState<string[]>([]);
	const [roles, setRoles] = useState<roleProps[]>([]);
	const [role] = useState(JSON.parse(storage.getLocal('role')));
	const userRoleId = role?.userRoleList?.find(
		(item: any) => item.projectId === projectId
	)?.roleId;

	useEffect(() => {
		getData();
		getRoleList({ key: '' }).then((res) => {
			if (res.success) {
				setRoles(res.data);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	}, []);
	const getData = () => {
		getProjectMember({
			organId,
			projectId,
			allocatable: true
		}).then((res) => {
			if (res.success) {
				setDataSource(res.data);
				setShowDataSource(res.data);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const handleSearch = (value: string) => {
		const list = dataSource.filter((item: userProps) =>
			item.userName.includes(value)
		);
		setShowDataSource(list);
	};
	const onChange = (selectedRowKeys: any) => {
		setPrimaryKeys(selectedRowKeys);
	};
	const roleChange = (value: number, record: userProps) => {
		record.roleId = value;
	};
	const onOk = () => {
		if (primaryKeys.length === 0) {
			notification.error({
				message: '失败',
				description: '请选择新增的成员'
			});
			return;
		}
		const list: userProps[] = [];
		primaryKeys.forEach((item: any) => {
			dataSource.forEach((i: userProps) => {
				if (i.id === item) {
					list.push(i);
				}
			});
		});
		const rl = roles
			.filter(
				(item) =>
					(item.type !== 'manager' && item.type !== 'dba') ||
					item.weight === 3
			)
			.map((item) => item.id);
		if (
			list.some(
				(item: userProps) =>
					item.roleId === null || !rl.includes(item.roleId)
			)
		) {
			notification.warning({
				message: '失败',
				description: '请选择成员的角色权限'
			});
			return;
		}
		const sendData = {
			organId,
			projectId,
			userDtoList: list
		};
		onCancel();
		bindProjectMember(sendData)
			.then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '成员新增成功'
					});
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			})
			.finally(() => {
				onRefresh();
			});
	};
	const roleRender = (value: string, record: userProps, index: number) => {
		return (
			<Select
				onChange={(value: any) => roleChange(value, record)}
				style={{ width: '100%' }}
				dropdownMatchSelectWidth={false}
			>
				{roles.map((item: roleProps) => {
					if (
						(item.type !== 'manager' && item.type !== 'dba') ||
						item.weight === 3
					) {
						return (
							<Option
								key={item.id}
								value={item.id}
								disabled={userRoleId === 2 && item.id === 2}
							>
								{item.name}
							</Option>
						);
					}
				})}
			</Select>
		);
	};
	return (
		<Modal
			title="新增"
			open={visible}
			onCancel={onCancel}
			onOk={onOk}
			okButtonProps={{ disabled: primaryKeys?.length === 0 }}
			width={840}
			okText="确定"
			cancelText="取消"
		>
			<ProTable
				dataSource={showDataSource}
				search={{
					onSearch: handleSearch,
					placeholder: '请输入关键字搜索'
				}}
				rowSelection={{
					onChange,
					selectedRowKeys: primaryKeys
				}}
				rowKey="id"
			>
				<ProTable.Column
					title="登录账户"
					dataIndex="userName"
					ellipsis={true}
				/>
				<ProTable.Column
					title="用户名"
					dataIndex="aliasName"
					ellipsis={true}
				/>
				<ProTable.Column
					title="邮箱"
					dataIndex="email"
					render={nullRender}
					ellipsis={true}
				/>
				<ProTable.Column
					title="创建时间"
					dataIndex="createTime"
					render={nullRender}
				/>
				<ProTable.Column
					title="角色"
					dataIndex="role"
					width={200}
					render={roleRender}
				/>
			</ProTable>
		</Modal>
	);
}

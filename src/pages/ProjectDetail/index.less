.collapse-panel {
	.ant-collapse-content {
		& > .ant-collapse-content-box {
			padding: 0;
			.ant-collapse {
				margin-bottom: 0 !important;
				.ant-collapse-header {
					padding-left: 57px;
					background: #f9f9f9;
				}
			}
		}
	}
}
.project-detail-namespace {
	border: none;
	.project-detail-namespace-echarts-content {
		display: flex;
		justify-content: space-around;
		.project-detail-namespace-echarts-item {
			text-align: center;
			position: relative;
			.project-detail-namespace-echarts-item-title {
				position: absolute;
				bottom: 0px;
				left: 69px;
				.project-detail-namespace-echarts-item-line {
					color: #226ee7;
					opacity: 0.3;
				}
				.project-detail-namespace-echarts-item-label {
					font-size: @font-1;
					font-weight: @font-weight;
					color: #333333;
					line-height: @line-height-1;
				}
			}
		}
	}
}
.panel-icon {
	height: 62px;
	display: flex;
	align-items: center;
	background: #f9f9f9;
	justify-content: center;
}

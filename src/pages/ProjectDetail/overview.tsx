import FormBlock from '@/components/FormBlock';
import React, { useEffect, useState } from 'react';
import ReactEChartsCore from 'echarts-for-react/lib/core';
import * as echarts from 'echarts/core';
import { GaugeChart } from 'echarts/charts';
import {
	GridComponent,
	TooltipComponent,
	TitleComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { getGaugeOption } from '@/utils/echartsOption';
import ProTable from '@/components/ProTable';
import {
	CpuMemoryItem,
	OrganizationBackupServerItem,
	StorageListItem,
	StorageListTableItem
} from '../OrganizationDetail/organization.detail';
import Actions from '@/components/Actions';
import MidProgress from '@/components/MidProgress';
import ServiceList from './serviceList';
import {
	deleteProBackupServer,
	deleteProStorage,
	getProjectBackupServer,
	getProjectCpuAndMemory,
	getProjectStorage
} from '@/services/project';
import { notification, Modal, Statistic, Spin, Button } from 'antd';
import {
	agentPhaseRender,
	formatNumber,
	objectRemoveDuplicatesByKey
} from '@/utils/utils';
import { ColumnFilterItem } from 'antd/lib/table/interface';
import { deleteProjectAgent, getProjectAgentList } from '@/services/agent';
import storage from '@/utils/storage';
import { FiltersProps } from '@/types/comment';
import { useHistory } from 'react-router';
import { SERVER_INTEGRATE_FOR_PROJECT } from '@/utils/const';
const LinkButton = Actions.LinkButton;
const { confirm } = Modal;
echarts.use([
	TitleComponent,
	TooltipComponent,
	GridComponent,
	GaugeChart,
	CanvasRenderer
]);

export default function Overview(props: { refresh: boolean }): JSX.Element {
	const { refresh } = props;
	const history = useHistory();
	// * 是否接入了观云台
	const isAccess = storage.getLocal('isAccessGYT');
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const [option1, setOption1] = useState(getGaugeOption(0, 'CPU(核)'));
	const [option2, setOption2] = useState(getGaugeOption(0, '内存(GB)'));
	const [backupServers, setBackupServers] = useState<
		OrganizationBackupServerItem[]
	>([]);
	const [storages, setStorages] = useState<StorageListTableItem[]>([]);
	const [clusterStorageFilters, setClusterStorageFilters] = useState<
		ColumnFilterItem[]
	>([]);
	const [clusterBackupServerFilters, setClusterBackupServerFilters] =
		useState<ColumnFilterItem[]>([]);
	const [cpuMemory, setCpuMemory] = useState<{
		cpuUsed: number;
		cpuRequest: number;
		cpuOccupy: number;
		memoryUsed: number;
		memoryRequest: number;
		memoryOccupy: number;
	}>();
	const [agents, setAgents] = useState<AgentItem[]>([]);
	const [agentLoading, setAgentLoading] = useState<boolean>(false);
	const [offline, setOffline] = useState<number>();
	const [online, setOnline] = useState<number>();
	const [controllerFilters, setControllerFilters] = useState<FiltersProps[]>(
		[]
	);
	const role = JSON.parse(storage.getLocal('role'));
	const myTopic = storage.getLocal('myTopic');
	const [quotaRequest, setQuotaRequest] = useState<boolean>(false);
	const [actionAuth, setActionAuth] = useState<boolean>(false);
	useEffect(() => {
		if (role) {
			const temp = role.userRoleList.find(
				(item: any) => item.projectId === projectId
			);
			if (role.isAdmin) {
				setQuotaRequest(false);
				setActionAuth(true);
				return;
			}
			if (temp?.weight === 2) {
				setQuotaRequest(false);
				setActionAuth(true);
				return;
			}
			if (temp?.weight === 3) {
				setActionAuth(true);
				setQuotaRequest(true);
				return;
			}
			if (role?.dba) {
				setQuotaRequest(true);
				return;
			}
			if (temp?.weight === 5) {
				setQuotaRequest(true);
				return;
			}
			if (!temp?.weight && temp?.power) {
				// * 当前项目下，当前用户（自定义）的power存在 运维权限的，即可以申请配额
				const listTemp = Object.entries<any>(temp?.power).map(
					([key, value]) => {
						const get = parseInt(value.charAt(0));
						const update = parseInt(value.charAt(1));
						const create = parseInt(value.charAt(2));
						const del = parseInt(value.charAt(3));
						return {
							technology: key,
							get,
							update,
							create,
							delete: del
						};
					}
				);
				if (listTemp.some((item) => item.update === 1)) {
					setQuotaRequest(true);
				} else {
					setQuotaRequest(false);
				}
				return;
			}
		}
	}, [role]);
	useEffect(() => {
		getProjectCpuAndMemory({
			organId,
			projectId,
			detail: true
		}).then((res) => {
			if (res.success) {
				const useT = res.data.reduce((per, cur) => {
					return per + cur.cpu.used;
				}, 0);
				const useR = res.data.reduce((per, cur) => {
					return per + cur.cpu.request;
				}, 0);
				const useS = res.data.reduce((per, cur) => {
					return per + cur.cpu.occupy;
				}, 0);
				const memoryT = res.data.reduce((per, cur) => {
					return per + cur.memory.used;
				}, 0);
				const memoryR = res.data.reduce((per, cur) => {
					return per + cur.memory.request;
				}, 0);
				const memoryS = res.data.reduce((per, cur) => {
					return per + cur.memory.occupy;
				}, 0);
				setCpuMemory({
					cpuUsed: useT,
					cpuRequest: useR,
					cpuOccupy: useS,
					memoryUsed: memoryT,
					memoryRequest: memoryR,
					memoryOccupy: memoryS
				});
				const cpuRate = useT / useR;
				const option1Temp = getGaugeOption(cpuRate || 0, 'CPU(核)');
				setOption1(option1Temp);
				const memoryRate = memoryT / memoryR;
				const option2Temp = getGaugeOption(memoryRate || 0, '内存(GB)');
				setOption2(option2Temp);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
		getStorages();
		getBackupServers();
		getProjectAgents();
	}, [organId, projectId, refresh]);
	const getStorages = () => {
		getProjectStorage({
			organId,
			projectId,
			detail: true
		}).then((res) => {
			if (res.success) {
				const lt: StorageListTableItem[] = [];
				const filters = res.data.map((item: CpuMemoryItem) => {
					item.storageList.map((i: StorageListItem) => {
						lt.push({
							...i,
							clusterId: item.clusterId,
							clusterNickName:
								item.clusterNickName || item.clusterId
						});
					});
					return {
						value: item.clusterId,
						text: item.clusterNickName || item.clusterId
					};
				});
				setClusterStorageFilters(
					objectRemoveDuplicatesByKey(filters, 'value')
				);
				setStorages(lt);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	const getBackupServers = () => {
		getProjectBackupServer({
			organId,
			projectId,
			detail: true
		}).then((res) => {
			if (res.success) {
				setBackupServers(res.data);
				const list = res.data.map((item) => {
					return {
						value: item.clusterId,
						text: item.clusterNickName || item.clusterId
					};
				});
				setClusterBackupServerFilters(
					objectRemoveDuplicatesByKey(list, 'value')
				);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	const getProjectAgents = () => {
		setAgentLoading(true);
		getProjectAgentList({
			organId,
			projectId
		})
			.then((res) => {
				if (res.success) {
					const controllerList = res.data?.map((item) => ({
						text: item.controller,
						value: item.clusterId
					}));
					setAgents(res.data);
					setControllerFilters(
						objectRemoveDuplicatesByKey(controllerList, 'value')
					);
					setOnline(
						res.data?.filter(
							(item: AgentItem) => item.phase === 'Online'
						).length
					);
					setOffline(
						res.data?.filter(
							(item: AgentItem) => item.phase === 'Offline'
						).length
					);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setAgentLoading(false);
			});
	};
	const actionRender = (
		value: string,
		record: OrganizationBackupServerItem,
		index: number
	) => {
		return (
			<Actions>
				<LinkButton
					onClick={() => {
						confirm({
							title: '操作',
							content: '请确认是否移除该项目下的此备份服务器？',
							onOk: () => {
								if (record.using === true) {
									notification.error({
										message: '错误',
										description:
											'当前备份服务器已被使用，无法移除！'
									});
									return;
								}
								return deleteProBackupServer({
									clusterId: record.clusterId,
									projectId,
									organId,
									backupServerId: record.id + ''
								}).then((res) => {
									if (res.success) {
										notification.success({
											message: '成功',
											description:
												'当前备份服务器移除成功!'
										});
										getBackupServers();
									} else {
										notification.error({
											message: '错误',
											description: (
												<>
													<p>{res.errorMsg}</p>
													<p>{res.errorDetail}</p>
												</>
											)
										});
									}
								});
							}
						});
					}}
				>
					删除
				</LinkButton>
			</Actions>
		);
	};
	const quotaRender = (
		value: any,
		record: StorageListTableItem,
		index: number
	) => {
		return (
			<MidProgress
				fromColor="#5C0EDF"
				toColor="#853CFF"
				unit="GB"
				toFixed={0}
				used={record.storage.used || 0}
				total={record.storage.request || 0}
			/>
		);
	};
	const clusterNickNameRender = (
		value: string,
		record: StorageListTableItem | OrganizationBackupServerItem,
		index: number
	) => {
		return record.clusterNickName ?? record.clusterId;
	};
	const actionStorageRender = (
		value: string,
		record: StorageListTableItem,
		index: number
	) => {
		return (
			<Actions>
				<LinkButton
					disabled={isAccess}
					title={
						isAccess ? '平台已接入观云台，请联系观云台管理员' : ''
					}
					onClick={() => {
						if (record.storage.used === 0 || !record.storage.used) {
							confirm({
								title: '操作确认',
								content: '请确认是否移除该项目下的此存储？',
								onOk: () => {
									return deleteProStorage({
										clusterId: record.clusterId,
										projectId,
										organId,
										storageId: record.storageId
									}).then((res) => {
										if (res.success) {
											notification.success({
												message: '成功',
												description: '当前存储移除成功!'
											});
											getStorages();
										} else {
											notification.error({
												message: '错误',
												description: (
													<>
														<p>{res.errorMsg}</p>
														<p>{res.errorDetail}</p>
													</>
												)
											});
										}
									});
								}
							});
						} else {
							notification.error({
								message: '错误',
								description: '当前存储已在使用，无法移除!'
							});
						}
					}}
				>
					删除
				</LinkButton>
			</Actions>
		);
	};
	const actionAgentRender = (value: any, record: AgentItem) => {
		return (
			<Actions>
				<LinkButton
					disabled={!!record.relationMiddlewareNum}
					onClick={() => {
						confirm({
							title: '操作确认',
							content: '请确认是否移除该项目下的此客户端？',
							onOk: () => {
								return deleteProjectAgent({
									clusterId: record.clusterId,
									projectId,
									organId,
									agentName: record.name
								}).then((res) => {
									if (res.success) {
										notification.success({
											message: '成功',
											description: '当前客户端移除成功！'
										});
										getProjectAgents();
									} else {
										notification.error({
											message: '错误',
											description: (
												<>
													<p>{res.errorMsg}</p>
													<p>{res.errorDetail}</p>
												</>
											)
										});
									}
								});
							}
						});
					}}
				>
					删除
				</LinkButton>
			</Actions>
		);
	};
	return (
		<>
			<FormBlock title="配额信息" className="resource-pool-info">
				<div className="resource-pool-gauge-content">
					<div className="resource-pool-gauge-item">
						<ReactEChartsCore
							echarts={echarts}
							option={option1}
							notMerge={true}
							lazyUpdate={true}
							style={{
								height: '100%',
								width: 'calc(100% - 360px)'
							}}
						/>
						<div className="resource-pool-gauge-info">
							总配额：
							{formatNumber(
								Number(cpuMemory?.cpuRequest) || 0,
								1
							)}{' '}
							核 | 剩余配额：
							{formatNumber(
								(Number(cpuMemory?.cpuRequest) || 0) -
									Number(cpuMemory?.cpuUsed) || 0,
								1
							)}
							核 <br />
							已分配：
							{formatNumber(Number(cpuMemory?.cpuUsed) || 0, 1)}核
							| 已使用：
							{formatNumber(
								Number(cpuMemory?.cpuOccupy) || 0,
								1
							)}{' '}
							核
						</div>
					</div>
					<div className="resource-pool-gauge-item">
						<ReactEChartsCore
							echarts={echarts}
							option={option2}
							notMerge={true}
							lazyUpdate={true}
							style={{
								height: '100%',
								width: 'calc(100% - 360px)'
							}}
						/>
						<div className="resource-pool-gauge-info">
							总配额：
							{formatNumber(
								Number(cpuMemory?.memoryRequest) || 0,
								1
							)}
							GB | 剩余配额：
							{formatNumber(
								(Number(cpuMemory?.memoryRequest) || 0) -
									Number(cpuMemory?.memoryUsed) || 0,
								1
							)}
							GB <br />
							已分配：
							{formatNumber(
								Number(cpuMemory?.memoryUsed) || 0,
								1
							)}
							GB | 已使用：
							{formatNumber(
								Number(cpuMemory?.memoryOccupy) || 0,
								1
							)}
							GB
						</div>
					</div>
				</div>
			</FormBlock>
			<h2 className="mt-16">存储信息</h2>
			<ProTable
				dataSource={storages}
				rowKey={(record) => record.name + Math.random()}
			>
				<ProTable.Column dataIndex="name" title="存储项名称" />
				<ProTable.Column
					dataIndex="clusterNickName"
					title="所属集群"
					filters={clusterStorageFilters}
					onFilter={(value: any, record: StorageListTableItem) =>
						record.clusterId === value
					}
				/>
				<ProTable.Column
					dataIndex="quota"
					title="存储配额"
					render={quotaRender}
				/>
				{actionAuth && (
					<ProTable.Column
						dataIndex="action"
						title="操作"
						render={actionStorageRender}
					/>
				)}
			</ProTable>
			<h2 className="mt-16">备份服务器信息</h2>
			<ProTable dataSource={backupServers} rowKey="id">
				<ProTable.Column dataIndex="name" title="备份服务器名称" />
				<ProTable.Column
					dataIndex="clusterNickName"
					title="所属集群"
					render={clusterNickNameRender}
					filters={clusterBackupServerFilters}
					onFilter={(
						value: any,
						record: OrganizationBackupServerItem
					) => record.clusterId === value}
				/>
				{actionAuth && (
					<ProTable.Column
						dataIndex="action"
						title="操作"
						render={actionRender}
					/>
				)}
			</ProTable>
			<h2 className="mt-16">服务信息</h2>
			<ServiceList organId={organId} projectId={projectId} />
			<h2 className="mt-16">客户端信息</h2>
			<div className="organ-overview-agent-content">
				<div className="organ-overview-agent-content-item">
					{quotaRequest && (
						<Button
							type="primary"
							disabled={
								!myTopic?.includes('ServerIntegrateForProject')
							}
							onClick={() => {
								history.push(
									`/project/display/work/order/${SERVER_INTEGRATE_FOR_PROJECT}`
								);
							}}
							style={{ marginBottom: 8 }}
						>
							申请接入
						</Button>
					)}
					<ProTable
						scroll={{ y: 160 }}
						dataSource={agents}
						rowKey={(record) => record.name + Math.random()}
						loading={agentLoading}
					>
						<ProTable.Column dataIndex="name" title="主机名称" />
						<ProTable.Column dataIndex="address" title="IP地址" />
						<ProTable.Column
							dataIndex="osType"
							title="操作系统"
							filterMultiple={false}
							filters={[
								{ text: 'linux', value: 'linux' },
								{ text: 'darwin', value: 'darwin' }
							]}
							onFilter={(value: any, record: AgentItem) =>
								record.osType === value
							}
						/>
						<ProTable.Column
							dataIndex="controller"
							title="管控控制器"
							filterMultiple={false}
							filters={controllerFilters}
							onFilter={(value: any, record: AgentItem) =>
								record.clusterId === value
							}
						/>
						<ProTable.Column
							dataIndex="phase"
							title="客户端状态"
							render={agentPhaseRender}
							filterMultiple={false}
							filters={[
								{ text: '运行中', value: 'Online' },
								{ text: '离线', value: 'Offline' },
								{ text: '卸载中', value: 'Terminating' },
								{ text: '未知', value: 'unknown' }
							]}
							onFilter={(value: any, record: AgentItem) =>
								record.phase === value
							}
						/>
						{actionAuth && (
							<ProTable.Column
								dataIndex="action"
								title="操作"
								render={actionAgentRender}
							/>
						)}
					</ProTable>
				</div>
				<div className="organ-overview-agent-content-item-2">
					<div className="organ-overview-agent-content-item-title">
						服务器客户端概览
					</div>
					<Spin spinning={agentLoading}>
						<div className="organ-overview-agent-box">
							<div className="organ-overview-agent-card">
								<Statistic
									title="在线客户端"
									value={online}
									precision={0}
								/>
							</div>
							<div className="organ-overview-agent-card">
								<Statistic
									title="离线客户端"
									value={offline}
									precision={0}
								/>
							</div>
						</div>
					</Spin>
				</div>
			</div>
		</>
	);
}

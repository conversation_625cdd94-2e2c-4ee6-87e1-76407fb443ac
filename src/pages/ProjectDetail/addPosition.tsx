import * as React from 'react';
import { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, notification } from 'antd';
import { addPosition, editPosition, getServer } from '@/services/backup';
import { formItemLayout618 } from '@/utils/const';
import pattern from '@/utils/pattern';

export function AddPosition(props: any): JSX.Element {
	const [form] = Form.useForm();
	const { onCancel, visible, organId, projectId, record } = props;
	const [serverList, setServerList] = useState<any>([]);

	useEffect(() => {
		getServer({ clusterId: '', withDetail: false }).then((res) => {
			if (res.success) {
				setServerList(res.data);
			}
		});
		if (record) {
			form.setFieldsValue({
				name: record.name,
				backupServerId: record.backupServerId,
				backupPosition: record.backupPosition
			});
		}
	}, [record]);
	const onCreate = () => {
		form.validateFields().then((values) => {
			if (record.name) {
				editPosition({
					organId,
					projectId,
					...values,
					id: record.id
				}).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '修改成功'
						});
						onCancel();
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			} else {
				addPosition({
					organId,
					projectId,
					...values
				}).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '新增成功'
						});
						onCancel();
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			}
		});
	};

	return (
		<Modal
			title={record.name ? '编辑备份位置' : '新增备份位置'}
			open={visible}
			onOk={onCreate}
			onCancel={onCancel}
		>
			<Form form={form} labelAlign="left" {...formItemLayout618}>
				<Form.Item
					label="备份位置名称"
					name="name"
					rules={[
						{
							required: true,
							message: '请输入备份位置名称'
						},
						{
							max: 64,
							type: 'string',
							message: '备份位置名称长度不能超过64'
						}
					]}
				>
					<Input placeholder="请输入备份位置名称" />
				</Form.Item>
				<Form.Item
					label="服务器选择"
					name="backupServerId"
					rules={[
						{
							required: true,
							message: '请选择服务器'
						}
					]}
				>
					<Select placeholder="请选择服务器" disabled>
						{serverList.map((item: any) => (
							<Select.Option key={item.id} value={item.id}>
								{item.name}
							</Select.Option>
						))}
					</Select>
				</Form.Item>
				<Form.Item
					label="Bucket名称"
					name="backupPosition"
					rules={[
						{
							required: true,
							message: '请输入Bucket名称'
						},
						{
							pattern: new RegExp(pattern.path),
							message: '请输入正确格式的Bucket路径'
						}
					]}
				>
					<Input placeholder="请输入Bucket名称" />
				</Form.Item>
			</Form>
		</Modal>
	);
}

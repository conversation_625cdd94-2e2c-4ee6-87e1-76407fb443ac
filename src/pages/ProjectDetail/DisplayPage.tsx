import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Modal } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import { useHistory } from 'react-router';
import { ProContent, ProHeader, ProPage } from '@/components/ProPage';
import Overview from './overview';
import {
	QUOTA_REQUEST_FOR_NAMESPACE,
	QUOTA_REQUEST_FOR_PROJECT
} from '@/utils/const';
import storage from '@/utils/storage';
import { debounce } from '@/utils/utils';
const { confirm } = Modal;
export default function ProjectDisplayPage(): JSX.Element {
	const history = useHistory();
	const projectId = storage.getSession('projectId');
	const role = JSON.parse(storage.getLocal('role'));
	const myTopic = storage.getLocal('myTopic');
	const isProject = role.userRoleList.find(
		(item: any) => item.projectId === projectId
	);
	const [quotaRequest, setQuotaRequest] = useState<boolean>(false);
	const [isProjectManager, setIsProjectManager] = useState<boolean>(false);
	const [refresh, setRefresh] = useState<boolean>(false);

	useEffect(() => {
		if (role) {
			const temp = role.userRoleList.find(
				(item: any) => item.projectId === projectId
			);
			if (role.isAdmin) {
				setQuotaRequest(false);
				return;
			}
			if (temp?.weight === 2) {
				setQuotaRequest(false);
				return;
			}
			if (temp?.weight === 3) {
				setQuotaRequest(true);
				setIsProjectManager(true);
				return;
			}
			if (role?.dba) {
				setQuotaRequest(true);
				return;
			}
			// 修改运维人员的weight
			if (temp?.weight === 5) {
				setQuotaRequest(true);
				return;
			}
			if (!temp?.weight && temp?.power) {
				// * 当前项目下，当前用户（自定义）的power存在 运维权限的，即可以申请配额
				const listTemp = Object.entries<any>(temp?.power).map(
					([key, value]) => {
						const get = parseInt(value.charAt(0));
						const update = parseInt(value.charAt(1));
						const create = parseInt(value.charAt(2));
						const del = parseInt(value.charAt(3));
						return {
							technology: key,
							get,
							update,
							create,
							delete: del
						};
					}
				);
				if (listTemp.some((item) => item.update === 1)) {
					setQuotaRequest(true);
				} else {
					setQuotaRequest(false);
				}
				return;
			}
		}
	}, [role]);

	return (
		<ProPage>
			<ProHeader
				title="资源概览"
				extra={
					<>
						{quotaRequest && (
							<Button
								type="primary"
								disabled={
									isProject?.roleId === 2
										? !myTopic?.includes(
												'QuotaRequestForProject'
										  )
										: !myTopic?.includes(
												'QuotaRequestForNamespace'
										  )
								}
								onClick={() => {
									if (isProjectManager) {
										history.push(
											`/project/display/work/order/${QUOTA_REQUEST_FOR_PROJECT}`
										);
									} else {
										history.push(
											`/project/display/work/order/${QUOTA_REQUEST_FOR_NAMESPACE}`
										);
									}
								}}
							>
								申请配额
							</Button>
						)}
						<Button
							onClick={debounce(() => setRefresh(!refresh))}
							id="detailRefresh"
							icon={<ReloadOutlined id="detailRefresh" />}
						/>
					</>
				}
			/>
			<ProContent>
				<Overview refresh={refresh} />
			</ProContent>
		</ProPage>
	);
}

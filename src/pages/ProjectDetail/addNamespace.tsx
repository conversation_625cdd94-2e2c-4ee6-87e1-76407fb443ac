import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, Select, notification } from 'antd';
import { createProNamespace, getProjectCpuAndMemory } from '@/services/project';
import { formItemLayout618 } from '@/utils/const';
import { AddNamespaceProps } from './projectDetail';
import { CpuMemoryItem } from '../OrganizationDetail/organization.detail';
import { regNamespace } from '@/services/common';

const FormItem = Form.Item;
const Option = Select.Option;
function AddNamespace(props: AddNamespaceProps): JSX.Element {
	const { open, onCancel, onRefresh, projectId, organId, data } = props;
	const [clusterList, setClusterList] = useState<CpuMemoryItem[]>([]);
	const [currentCluster, setCurrentCluster] = useState<CpuMemoryItem>();
	const [loading, setLoading] = useState<boolean>(false);
	const [form] = Form.useForm();
	useEffect(() => {
		if (data) {
			form.setFieldsValue({
				name: data.name,
				aliasName: data.aliasName,
				clusterId: data.clusterId
			});
		}
	}, [data]);
	useEffect(() => {
		getProjectCpuAndMemory({
			organId,
			projectId,
			detail: true
		}).then((res) => {
			if (res.success) {
				setClusterList(res.data);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	}, []);
	const onOk = () => {
		form.validateFields().then((values) => {
			setLoading(true);
			if (data) {
				// * 编辑分区
				regNamespace({
					projectId: projectId,
					organId: organId,
					...values
				})
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '命名空间编辑成功!'
							});
							onCancel();
							onRefresh();
						} else {
							notification.error({
								message: '失败',
								description: (
									<>
										<p>{res.errorMsg}</p>
										<p>{res.errorDetail}</p>
									</>
								)
							});
						}
					})
					.finally(() => {
						setLoading(false);
					});
			} else {
				// * 创建分区
				createProNamespace({
					projectId: projectId,
					organId: organId,
					...values
				})
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '命名空间添加成功!'
							});
							onCancel();
							onRefresh();
						} else {
							notification.error({
								message: '失败',
								description: (
									<>
										<p>{res.errorMsg}</p>
										<p>{res.errorDetail}</p>
									</>
								)
							});
						}
					})
					.finally(() => {
						setLoading(false);
					});
			}
		});
	};
	const handleChange = (value: string) => {
		const t = clusterList.find(
			(item: CpuMemoryItem) => item.clusterId === value
		);
		setCurrentCluster(t);
	};
	return (
		<Modal
			open={open}
			onCancel={onCancel}
			onOk={onOk}
			okButtonProps={{
				loading: loading
			}}
			title={data ? '编辑' : '新增'}
			width={550}
			okText="确定"
			cancelText="取消"
		>
			<Form
				{...formItemLayout618}
				labelAlign="left"
				form={form}
				requiredMark={false}
			>
				<FormItem
					label="命名空间名称"
					name="aliasName"
					required
					rules={[
						{
							required: true,
							message: '请输入命名空间名称'
						},
						{
							max: 64,
							message: '请输入1-64个字符'
						}
					]}
				>
					<Input placeholder="请输入命名空间名称" />
				</FormItem>
				<FormItem
					label="命名空间英文名"
					required
					name="name"
					rules={[
						{
							required: true,
							message: '请输入命名空间英文名'
						},
						{
							pattern: new RegExp(
								'^[a-z][a-z0-9-]{0,38}[a-z0-9]$'
							),
							message:
								'命名空间是由小写字母数字及“-”组成，且以小写字母开头和结尾，不能以“-”结尾的2-40个字符'
						}
					]}
				>
					<Input
						disabled={!!data}
						placeholder="请输入命名空间英文名"
					/>
				</FormItem>
				<FormItem
					label="绑定集群"
					required
					name="clusterId"
					rules={[
						{
							required: true,
							message: '请选择集群'
						}
					]}
				>
					<Select
						value={currentCluster?.clusterId}
						onChange={handleChange}
						style={{ width: '100%' }}
						dropdownMatchSelectWidth={false}
						disabled={!!data}
						placeholder="请选择集群"
					>
						{clusterList.map((item: CpuMemoryItem) => {
							return (
								<Option
									key={item.clusterId}
									value={item.clusterId}
								>
									{item.clusterNickName}
								</Option>
							);
						})}
					</Select>
				</FormItem>
			</Form>
		</Modal>
	);
}
export default AddNamespace;

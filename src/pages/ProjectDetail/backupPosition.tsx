import React, { useState, useEffect } from 'react';
import { notification, Modal, Spin } from 'antd';
import { DoubleRightOutlined, PlusOutlined } from '@ant-design/icons';
import Backup from '@/assets/images/backup.svg';
import { ListPanel, ListCardItem } from '@/components/ListCard';
import Actions from '@/components/Actions';
import { removePosition } from '@/services/backup';
import DefaultPicture from '@/components/DefaultPicture';
import { AddPosition } from './addPosition';
import { getProjectBackupServer } from '@/services/project';
import storage from '@/utils/storage';
import './index.less';

const LinkButton = Actions.LinkButton;
export default function BackupPosition(): JSX.Element {
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const [addressList, setAddressList] = useState<any[]>([]);
	const [spinning, setSpinning] = useState<boolean>(false);
	const [visible, setVisible] = useState<boolean>(false);
	const [record, setRecord] = useState<any>();
	const [activeKey, setActive] = useState<any>([]);

	useEffect(() => {
		getData();
	}, []);

	const getData = () => {
		setSpinning(true);
		getProjectBackupServer({
			organId,
			projectId,
			position: true,
			detail: true
		})
			.then((res) => {
				if (res.success) {
					setAddressList(res.data);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setSpinning(false);
			});
	};

	const handleDelete = (item: any) => {
		Modal.confirm({
			title: '删除确认',
			content: '确认要删除该备份位置？',
			onOk: () => {
				removePosition({
					organId,
					projectId,
					backupServerId: item.id,
					backupPositionId: item.positionList[0].id
				}).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '删除成功'
						});
						getData();
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			}
		});
	};

	return (
		<div className="mt-8">
			{!spinning ? (
				addressList.length > 0 ? (
					addressList.map((item: any) => {
						return (
							<ListPanel
								title={item.name}
								subTitle={item.serverType}
								icon={
									<img
										src={Backup}
										style={{
											marginLeft: 13,
											marginRight: 16
										}}
									/>
								}
								key={item.id}
								activeKey={{
									activeKey: activeKey.find(
										(num: number) => num === item.id
									)
										? item.id
										: ''
								}}
								onChange={(x: number[]) => {
									x.length
										? setActive([...activeKey, item.id])
										: setActive(
												activeKey.filter(
													(num: string) =>
														num !== item.id
												)
										  );
								}}
								expandIcon={(panel: any) => (
									<DoubleRightOutlined
										style={{
											transform: panel.isActive
												? 'rotate(90deg)'
												: 'rotate(0)'
										}}
										onClick={() =>
											!panel.isActive
												? setActive([
														...activeKey,
														item.id
												  ])
												: setActive(
														activeKey.filter(
															(num: string) =>
																num !== item.id
														)
												  )
										}
									/>
								)}
								className="collapse-panel"
								expandIconPosition="end"
								render={
									item.positionList?.length ? (
										<ListPanel
											title={item.positionList[0].name}
											subTitle="备份位置名称"
											icon={null}
											key={item.id}
											bordered={false}
											showArrow={false}
											actionRender={
												<Actions>
													<LinkButton
														// onClick={() =>
														// 	history.push(
														// 		`/backupService/backupPosition/addBackupPosition/${item.id}`
														// 	)
														// }
														onClick={() => {
															setRecord(
																item
																	.positionList[0]
															);
															setVisible(true);
														}}
													>
														编辑
													</LinkButton>
													<LinkButton
														// disabled={
														// 	addressList.length ===
														// 	1
														// }
														onClick={() =>
															handleDelete(item)
														}
													>
														删除
													</LinkButton>
												</Actions>
											}
										>
											<ListCardItem
												label="备份路径"
												value={
													item.positionList[0]
														.backupPosition
												}
												width={260}
											/>
											<ListCardItem
												label="所引用备份数"
												value={
													item.positionList[0]
														.backupTaskNum || 0
												}
												width={280}
											/>
										</ListPanel>
									) : (
										<div className="panel-icon">
											<PlusOutlined
												style={{
													fontSize: '28px',
													cursor: 'pointer'
												}}
												onClick={() => {
													setRecord({
														backupServerId: item.id
													});
													setVisible(true);
												}}
											/>
										</div>
									)
								}
							>
								<ListCardItem
									label="所属集群"
									value={item.clusterId || '/'}
									width={350}
								/>
								<ListCardItem
									render={
										<div>
											{item.serverDetailList.map(
												(value: any) => {
													return (
														<p key={value.id}>
															{`${value.protocol}://${value.host}:${value.port}`}
														</p>
													);
												}
											)}
											<p className="zeus-list-card-item-label">
												备份服务器地址
											</p>
										</div>
									}
									width={360}
								/>
							</ListPanel>
						);
					})
				) : (
					<DefaultPicture title="当前列表无数据" />
				)
			) : (
				<Spin>
					<DefaultPicture title="当前列表无数据" />
				</Spin>
			)}
			{visible && (
				<AddPosition
					visible={visible}
					record={record}
					projectId={projectId}
					organId={organId}
					onCancel={() => {
						getData();
						setVisible(false);
					}}
				/>
			)}
		</div>
	);
}

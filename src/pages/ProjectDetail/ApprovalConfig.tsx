import React, { useEffect, useState } from 'react';
import {
	<PERSON><PERSON>,
	Button,
	Checkbox,
	Col,
	Row,
	Space,
	Tabs,
	Modal,
	notification,
	Empty,
	Spin
} from 'antd';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';
import { ProContent, ProHeader, ProPage } from '@/components/ProPage';
import storage from '@/utils/storage';
import { getMaintenance, updateMaintenance } from '@/services/project';
import { ReloadOutlined } from '@ant-design/icons';
const { confirm } = Modal;
const Content = ({
	activeKey,
	refreshKey
}: {
	activeKey: string;
	refreshKey: number;
}) => {
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const [actions, setActions] = useState<MaintenanceItem[]>([]);
	const [isEdit, setIsEdit] = useState<boolean>(false);
	const [value, setValue] = useState<CheckboxValueType[]>([]);
	const [spinning, setSpinning] = useState<boolean>(false);
	useEffect(() => {
		if (refreshKey > 0) {
			getData();
		}
	}, [refreshKey]);
	useEffect(() => {
		getData();
	}, []);
	const onChange = (checkedValues: CheckboxValueType[]) => {
		setValue(checkedValues);
	};
	const onSave = () => {
		confirm({
			title: '操作确认',
			content: (
				<>
					<p>勾选的操作在已锁定的服务下需要审批才能执行</p>
					<p>是否确定保存？</p>
				</>
			),
			onOk: () => {
				const temp = actions.map((item) => {
					if (value.includes(item.operatorId)) {
						item.available = true;
					} else {
						item.available = false;
					}
					return item;
				});
				const sendData: UpdateMaintenanceSendData = {
					organId,
					projectId,
					projectMaintenanceAuditDtoList: temp
				};
				return updateMaintenance(sendData)
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '审批配置修改成功！'
							});
						} else {
							notification.error({
								message: '失败',
								description: (
									<>
										<p>{res.errorMsg}</p>
										<p>{res.errorDetail}</p>
									</>
								)
							});
						}
					})
					.finally(() => {
						getData();
					});
			},
			onCancel: () => {
				getData();
			}
		});
	};
	const getData = () => {
		setSpinning(true);
		setIsEdit(false);
		getMaintenance({ organId, projectId, operatorType: activeKey })
			.then((res) => {
				if (res.success) {
					const temp = res.data
						.filter((item) => item.available === true)
						.map((item) => item.operatorId);
					setValue(temp);
					setActions(res.data);
				} else {
					notification.error({
						message: '失败',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setSpinning(false);
			});
	};
	const onCancel = () => {
		const lt = actions
			.filter((item: MaintenanceItem) => item.available === true)
			.map((item) => item.operatorId);
		setValue(lt);
		setIsEdit(false);
	};
	const saveDisabledJudge = () => {
		const lt = actions
			.filter((item: MaintenanceItem) => item.available === true)
			.map((item) => item.operatorId);
		const sortLt = lt?.slice()?.sort();
		const sortValue = value?.slice()?.sort();
		return JSON.stringify(sortLt) === JSON.stringify(sortValue);
	};
	return (
		<div>
			<Space>
				{!isEdit && (
					<Button type="primary" onClick={() => setIsEdit(true)}>
						编辑
					</Button>
				)}
				{isEdit && (
					<>
						<Button
							type="primary"
							onClick={onSave}
							disabled={saveDisabledJudge()}
						>
							保存
						</Button>
						<Button danger onClick={onCancel}>
							取消
						</Button>
					</>
				)}
			</Space>
			<Spin spinning={spinning}>
				{actions.length === 0 && (
					<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
				)}
				{actions.length > 0 && (
					<Checkbox.Group
						style={{ width: '100%', margin: '0 12px' }}
						onChange={onChange}
						value={value}
					>
						<Row>
							{actions.map((item) => {
								return (
									<Col
										style={{
											height: '54px',
											borderBottom: '1px solid #f0f0f0',
											display: 'flex',
											alignItems: 'center'
										}}
										key={item.operatorId}
										span={6}
									>
										<Checkbox
											disabled={!isEdit}
											value={item.operatorId}
										/>
										<div
											className="ml-12"
											title={item.operatorAliasName}
										>
											{item.operatorAliasName}
										</div>
									</Col>
								);
							})}
						</Row>
					</Checkbox.Group>
				)}
			</Spin>
		</div>
	);
};
export default function ApprovalConfig(): JSX.Element {
	const approvalTab = storage.getSession('approval-tab');
	const [activeKey, setActiveKeys] = useState<string>(approvalTab || 'basic');
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useEffect(() => {
		return () => {
			storage.removeSession('approval-tab');
		};
	}, []);
	useEffect(() => {
		if (activeKey) {
			setRefreshKey(0);
		}
	}, [activeKey]);

	const items = [
		{
			label: '基础操作',
			key: 'basic',
			children: <Content activeKey={activeKey} refreshKey={refreshKey} />
		},
		{
			label: '运维操作',
			key: 'maintenance',
			children: <Content activeKey={activeKey} refreshKey={refreshKey} />
		},
		{
			label: '高级操作',
			key: 'advanced',
			children: <Content activeKey={activeKey} refreshKey={refreshKey} />
		}
	];

	const onTabClick = (value: string) => {
		setActiveKeys(value);
		storage.setSession('approval-tab', value);
	};
	return (
		<ProPage>
			<ProHeader
				title="审批配置"
				subTitle="自定义配置需要审批的操作"
				extra={
					<Button
						onClick={() => {
							setRefreshKey((pre) => pre + 1);
						}}
						icon={<ReloadOutlined />}
					/>
				}
			/>
			<ProContent>
				<Alert
					closable
					type="info"
					message="审批配置仅针对已锁定的服务，不影响未锁定服务的操作调试。"
					style={{ marginBottom: 8 }}
				/>
				<Tabs
					items={items}
					activeKey={activeKey}
					onTabClick={onTabClick}
					destroyInactiveTabPane={true}
				/>
			</ProContent>
		</ProPage>
	);
}

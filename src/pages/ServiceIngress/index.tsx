import React, { useEffect, useState } from 'react';
import {
	Drawer,
	Modal,
	notification,
	Select,
	Space,
	Table,
	Tag,
	Tooltip
} from 'antd';
import { ProHeader, ProPage, ProContent } from '@/components/ProPage';
import ProTable from '@/components/ProTable';
import { deleteExposes, getExposes } from '@/services/ingress';
import { ServiceIngressItem } from './serviceIngress';
import Actions from '@/components/Actions';
import { FiltersProps } from '@/types/comment';
import { api } from '@/api.json';
import nodata from '@/assets/images/nodata.svg';
import { copyValue, objectRemoveDuplicatesByKey } from '@/utils/utils';
import storage from '@/utils/storage';
import { getClusters } from '@/services/common';
import { clusterType } from '@/types';

const LinkButton = Actions.LinkButton;
function ServiceIngress(): JSX.Element {
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const [searchText, setSearchText] = useState<string>('');
	const [dataSource, setDataSource] = useState<ServiceIngressItem[]>([]);
	const [visible, setVisible] = useState<boolean>(false);
	const [curIngress, setCurIngress] = useState<ServiceIngressItem>();
	const [typeFilter, setTypeFilter] = useState<FiltersProps[]>([]);
	const [clusters, setClusters] = useState<clusterType[]>([]);
	const [loadingVisible, setLoadingVisible] = useState<boolean>(false);
	// * 获取用户当前角色
	const role = JSON.parse(storage.getLocal('role'));
	// * 是否具有删除功能
	const [actionAuth, setActionAuth] = useState<boolean>(true);
	const [curClusterId, setCurClusterId] = useState<string>('all');
	useEffect(() => {
		let mounted = true;
		async function getAllData() {
			setLoadingVisible(true);
			try {
				const res1 = await getClusters({ organId, projectId });
				if (res1.success) {
					setClusters(res1.data);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res1.errorMsg}</p>
								<p>{res1.errorDetail}</p>
							</>
						)
					});
				}
				const res2 = await getExposes({
					projectId,
					organId,
					keyword: ''
				});
				if (res2.success) {
					setDataSource(res2.data);
					const list = res2.data.map((item: ServiceIngressItem) => {
						return {
							value: item.middlewareType,
							text: item.middlewareType
						};
					});
					setTypeFilter(objectRemoveDuplicatesByKey(list, 'value'));
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res2.errorMsg}</p>
								<p>{res2.errorDetail}</p>
							</>
						)
					});
				}
				setLoadingVisible(false);
			} catch {
				setLoadingVisible(false);
			}
		}
		if (organId && projectId) {
			if (mounted) {
				getAllData();
			}
		}
		return () => {
			mounted = false;
		};
	}, [organId, projectId]);
	useEffect(() => {
		if (JSON.stringify(role) !== '{}') {
			if (role.isAdmin) {
				setActionAuth(true);
				return;
			}
			const curRole = role.userRoleList.find(
				(item: any) => item.projectId === projectId
			);
			const keys = curRole?.power ? Object.keys(curRole?.power) : [];
			const flag = keys.every((item) => {
				if (curRole.power[item][1] === '0') {
					return false;
				} else {
					return true;
				}
			});
			setActionAuth(flag);
		}
	}, [role]);
	const getData = (keyword: string, clusterId?: string) => {
		setLoadingVisible(true);
		getExposes({
			projectId,
			organId,
			clusterId: clusterId === 'all' ? '' : clusterId,
			keyword: keyword
		})
			.then((res) => {
				if (res.success) {
					setDataSource(res.data);
				}
			})
			.finally(() => {
				setLoadingVisible(false);
			});
	};
	const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		setSearchText(event.target.value);
	};
	const handleSelectChange = (value: string) => {
		setCurClusterId(value);
		getData(searchText, value);
	};
	const handleSearch = (value: string) => {
		getData(value);
	};
	const Operation = {
		primary: (
			<Space>
				<label>集群:</label>
				<Select
					style={{ width: 120 }}
					dropdownMatchSelectWidth={false}
					value={curClusterId}
					onChange={handleSelectChange}
				>
					<Select.Option value="all">全部</Select.Option>
					{clusters.map((item) => {
						return (
							<Select.Option key={item.id} value={item.id}>
								{item.name}
							</Select.Option>
						);
					})}
				</Select>
			</Space>
		)
	};
	const handleDelete = (record: ServiceIngressItem) => {
		Modal.confirm({
			title: '操作确认',
			content:
				'删除对外路由会影响当前正在通过对外地址访问中间件的服务，请确认执行',
			okText: '确认',
			cancelText: '取消',
			onOk: () => {
				return deleteExposes(record)
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '对外路由删除成功'
							});
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					})
					.finally(() => {
						getData(searchText);
					});
			}
		});
	};
	const actionRender = (_: string, record: ServiceIngressItem) => {
		return (
			<Actions>
				{(record.tcpExposeType === 'traefik' ||
					record.tcpExposeType === 'nginx') &&
					record.protocol === 'TCP' && (
						<LinkButton
							onClick={() => {
								const list = record.ingressIpList?.map(
									(item: string) => {
										return {
											podIp: item,
											podName: record.ingressClassName
										};
									}
								);
								record.ingressComponentDtoList = list;
								setCurIngress(record);
								setVisible(true);
							}}
						>
							查看详情
						</LinkButton>
					)}
				<LinkButton
					title={judgeInit(record).message}
					disabled={judgeInit(record).flag}
					onClick={() => handleDelete(record)}
				>
					删除
				</LinkButton>
			</Actions>
		);
	};
	const ipRender = (value: string, record: ServiceIngressItem) => {
		if (record.protocol === 'HTTP') {
			return (
				<Tag
					style={{ cursor: 'pointer' }}
					onClick={() =>
						copyValue(
							record.rules?.[0].host +
								record.rules?.[0].ingressHttpPaths?.[0].path +
								''
						)
					}
				>
					{record.rules?.[0].host +
						record.rules?.[0].ingressHttpPaths?.[0].path +
						''}
				</Tag>
			);
		}
		return (
			<>
				<Tag
					style={{ cursor: 'pointer' }}
					onClick={() =>
						copyValue(
							record.ingressIpList?.[0] +
								':' +
								record.serviceDTOList?.[0].exposePort
						)
					}
				>
					{record.ingressIpList?.[0] +
						':' +
						record.serviceDTOList?.[0].exposePort}
				</Tag>
				<Tooltip
					color="#fff"
					overlayStyle={{ maxWidth: '330px' }}
					title={
						<Space wrap>
							{record.serviceDTOList?.length &&
								record.serviceDTOList.map((item: any) => {
									return (
										<Tag
											key={item?.exposePort}
											style={{ cursor: 'pointer' }}
											onClick={() =>
												copyValue(
													record.ingressIpList?.[0] +
														':' +
														item.exposePort
												)
											}
										>
											{record.ingressIpList?.[0] +
												':' +
												item.exposePort}
										</Tag>
									);
								})}
						</Space>
					}
				>
					{record.serviceDTOList?.length > 1 ? (
						<Tag
							style={{ borderRadius: '10px', cursor: 'pointer' }}
						>
							+{record.serviceDTOList?.length}
						</Tag>
					) : null}
				</Tooltip>
			</>
		);
		// if (record.protocol === 'HTTP') return record.rules?.[0].domain;
		// if (record.tcpExposeType === 'nodePort')
		// 	return record.ingressIpList?.[0];
		// if (record.tcpExposeType === 'nginx')
		// 	return record.serviceDTOList?.[0].exposePort;
		// if (record.tcpExposeType === 'traefik')
		// 	return record.serviceDTOList?.[0].exposePort;
	};
	const exposeTypeRender = (value: string, record: ServiceIngressItem) => {
		return record.protocol;
	};
	const nameRender = (value: string, record: ServiceIngressItem) => {
		return record.aliasExposeType;
	};
	const judgeInit = (record: any) => {
		if (!actionAuth)
			return { flag: true, message: '当前用户没有运维权限，无法删除' };
		if (record.exposeType === 'external') {
			return { flag: true, message: '该服务暴露为集群外访问，无法删除' };
		} else {
			return { flag: false, message: '' };
		}
	};

	return (
		<ProPage>
			<ProHeader
				title="服务暴露"
				subTitle="通过Nginx-Ingress/NodePort等多种方式对外暴露已发布的不同类型中间件服务"
			/>
			<ProContent>
				<ProTable
					rowKey="name"
					dataSource={dataSource}
					search={{
						value: searchText,
						onChange: handleChange,
						onSearch: handleSearch,
						placeholder: '请输入服务名称、暴露服务搜索',
						style: { width: '350px' }
					}}
					showRefresh
					onRefresh={() => getData(searchText, curClusterId)}
					operation={Operation}
					loading={loadingVisible}
				>
					<ProTable.Column
						dataIndex="middlewareName"
						title="服务名称"
					/>
					<ProTable.Column
						dataIndex="middlewareType"
						title="服务类型"
						filters={typeFilter}
						onFilter={(value: any, record: ServiceIngressItem) =>
							record.middlewareType === value
						}
					/>
					<ProTable.Column
						dataIndex="tcpExposeType"
						title="暴露方式"
						render={exposeTypeRender}
						filters={[
							{ value: 'HTTP', text: 'HTTP' },
							{ value: 'TCP', text: 'TCP' }
						]}
						filterMultiple={false}
						onFilter={(value: any, record: ServiceIngressItem) => {
							return record.protocol === value;
						}}
					/>
					<ProTable.Column
						dataIndex="name"
						title="暴露服务"
						render={nameRender}
					/>
					<ProTable.Column
						dataIndex="ip"
						title="暴露IP/域名/端口"
						render={ipRender}
					/>
					{/* v2.1.0 去除 */}
					{/* <ProTable.Column
						dataIndex="action"
						title="操作"
						render={actionRender}
					/> */}
				</ProTable>
				<Drawer
					title={
						<div className="icon-type-content">
							<img
								width={14}
								height={14}
								src={
									curIngress?.imagePath
										? `${api}/images/middleware/${curIngress?.imagePath}`
										: nodata
								}
							/>
							<div style={{ marginLeft: 8 }}>
								{curIngress?.middlewareNickName ||
									curIngress?.middlewareName}
							</div>
						</div>
					}
					placement="right"
					onClose={() => setVisible(false)}
					open={visible}
					width={500}
				>
					<Table
						dataSource={curIngress?.ingressComponentDtoList || []}
					>
						<Table.Column dataIndex="podIp" title="IP" />
						<Table.Column dataIndex="podName" title="Ingress名称" />
					</Table>
				</Drawer>
			</ProContent>
		</ProPage>
	);
}
export default ServiceIngress;

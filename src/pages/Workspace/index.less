.zeus-workspace-content {
	width: 100%;
	height: 100%;
	padding: 20px;
	display: flex;
	.zeus-workspace-left-content {
		width: 65%;
		height: 100%;
	}
	.zeus-workspace-right-content {
		width: 35%;
		height: 100%;
	}
}
#my-trend,
#my-alarm,
#my-info,
#my-org-and-pro,
#my-resource {
	padding: 12px;
	box-shadow: 0px 2px 16px 0px rgba(34, 110, 231, 0.1);
	border-radius: @border-radius;
	border: 1px rgba(216, 222, 229, 0.4) solid;
	margin-bottom: 12px;
	&:hover {
		box-shadow: 4px 4px 12px 2px rgba(34, 110, 231, 0.14);
	}
	.my-title {
		display: flex;
		align-items: center;
		font-size: @font-3;
		font-weight: @font-weight;
		font-family: PingFang SC;
		line-height: @line-height-3;
		&::before {
			display: inline-block;
			content: '';
			width: 1px;
			height: 14px;
			margin-right: 8px;
			border: 2px solid @primary-color;
		}
	}
}
#my-resource {
	min-height: 190px;
	.my-resource-title-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		.my-resource-title {
			font-size: @font-1;
			font-weight: @font-weight;
		}
	}
	.my-resource-value-content {
		display: flex;
		flex-wrap: wrap;
		gap: 8px;
		margin-top: 12px;
		.my-resource-empty-card {
			width: 100%;
			height: 120px;
		}
		.my-resource-value-card {
			width: 200px;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			margin-bottom: 8px;
			& > img {
				padding: 4px;
				// background-color: #f5f5f5;
				text-align: center;
				height: 50px;
				width: 50px;
				font-size: 43px;
			}
			.my-resource-card-content-title {
				height: 27px;
				line-height: 27px;
				font-size: @font-1;
				margin-bottom: 4px;
				font-weight: @font-weight;
			}
		}
	}
}
#my-org-and-pro {
	height: 510px;
	overflow-y: auto;
	.my-title {
		margin-bottom: 16px;
	}
	.organization-list-content {
		margin-bottom: 12px;
		border: 1px solid @border-color;
	}
	.project-list-title,
	.organization-list-title {
		display: flex;
		align-items: center;
		// color: @primary-color;
		font-size: @font-1;
		padding: 8px 12px;
		font-weight: @font-weight;
		font-family: PingFang SC;
		line-height: @line-height-1;
		border-bottom: 1px solid @border-color;
		& > span {
			cursor: pointer;
		}
		.project-statistic,
		.organization-statistic {
			font-size: @font-4;
			font-weight: @font-weight;
			line-height: @line-height-4 + 2px;
		}
		.project-count {
			margin-left: 16px;
			padding: 1px 4px;
			border-radius: 2px;
			border: 1px solid #d6e4ff;
			background: #f0f5ff;
			color: #85a5ff;
			font-family: PingFang SC;
			font-size: @font-1;
			font-style: normal;
			font-weight: 400;
			line-height: @line-height-1;
		}
	}
	.project-list-content {
		padding: 12px;
		display: grid;
		grid-template-columns: repeat(auto-fill, 23%);
		&.none {
			display: none;
		}
		&.no-data {
			grid-template-columns: auto;
		}
		.project-list-content-value-card {
			display: flex;
			align-items: center;
			cursor: pointer;
			border-radius: 5px;
			padding: 12px 16px;
			background-color: #fafafa;
			border: 1px solid #efefef;
			div {
				font-size: @font-1;
				line-height: @line-height-1;
				span {
					display: block;
					width: 107px;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					color: rgba(0, 0, 0, 0.85);
				}
				p {
					color: rgba(0, 0, 0, 0.45);
				}
			}
			.project-list-content-card-icon {
				width: 40px;
				height: 100%;
				display: flex;
				margin-right: 8px;
				align-items: center;
				box-sizing: border-box;
				border-top-left-radius: 5px;
				border-bottom-left-radius: 5px;
			}
			&.active:hover {
				border-color: @primary-color;
				.project-list-content-card-icon {
					span {
						color: @primary-color;
					}
				}
			}
		}
	}
}
#my-info {
	width: 100%;
	height: 112px;
	padding: 30px;
	margin-left: 12px;
	background: url(../../assets/images/user-bg.png) no-repeat 100% 0 #ffffff;
	display: flex;
	align-items: center;
	.my-info-user-name {
		width: 210px;
		font-size: @font-2;
		font-weight: @font-weight;
		margin-left: 24px;
		.mixin(textEllipsis);
	}
}
#my-alarm {
	width: 100%;
	margin-left: 12px;
	.my-alarm-empty-content {
		height: 100%;
	}
}
#my-trend {
	width: 100%;
	height: 250px;
	margin-left: 12px;
	.my-trend-title {
		font-size: @font-1;
		font-weight: @font-weight;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	.my-trend-empty-content {
		height: 100%;
		width: 100%;
	}
}

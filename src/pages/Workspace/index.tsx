import React, { useEffect, useState } from 'react';
import './index.less';
import MyResource from './components/myResource';
import MyOrgAndPro from './components/myOrgAndPro';
import { getOrganizations } from '@/services/organization';
import { OrganizationItem } from '../OrganizationManagement/organization';
import { notification } from 'antd';
import MyInfo from './components/myInfo';
import MyAlarm from './components/myAlarm';
import MyTrend from './components/myTrend';

export default function Workspace(): JSX.Element {
	const [resourceHeight, setResourceHeight] = useState<number>(190);
	const [organizations, setOrganizations] = useState<OrganizationItem[]>([]);
	useEffect(() => {
		async function getAllData() {
			const res = await getOrganizations({ keyword: '' });
			if (res.success) {
				setOrganizations(res.data.filter((item) => item.projectCount));
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		}
		getAllData();
	}, []);
	return (
		<div className="zeus-workspace-content">
			<div className="zeus-workspace-left-content">
				<MyResource
					organizations={organizations}
					setResourceHeight={(data: number) =>
						setResourceHeight(data)
					}
				/>
				<MyOrgAndPro />
			</div>
			<div className="zeus-workspace-right-content">
				<MyInfo />
				<MyAlarm resourceHeight={resourceHeight} />
				<MyTrend />
			</div>
		</div>
	);
}

import React, { useEffect, useState } from 'react';
import { ReloadOutlined } from '@ant-design/icons';
import { Button, Select, Space } from 'antd';
import EChartsReact from 'echarts-for-react';
import { getLineOption } from '@/utils/echartsOption';
import { trendAlarm } from '@/utils/const';
import { getWorkspaceALertTrend } from '@/services/workspace';

export default function MyTrend(): JSX.Element {
	const [time, setTime] = useState<string>('12h');
	const [lineOption, setLineOption] = useState<any>({});

	const onChange = (value: string) => {
		setTime(value);
	};

	const getData = () => {
		getWorkspaceALertTrend({ time }).then((res) => {
			if (res.success) {
				setLineOption(
					getLineOption({
						...res.data.alertSummary,
						x: res.data.alertSummary?.infoList
					})
				);
			}
		});
	};

	useEffect(() => {
		getData();
	}, [time]);
	return (
		<div id="my-trend">
			<div className="my-trend-title">
				<div className="my-title">告警总量趋势</div>
				<Space>
					<Select
						style={{ width: '100px' }}
						options={trendAlarm}
						value={time}
						onChange={onChange}
					/>
					<Button icon={<ReloadOutlined />} onClick={getData} />
				</Space>
			</div>
			<div className="my-trend-empty-content">
				<EChartsReact
					option={lineOption}
					style={{
						height: 'calc(100% - 22px)',
						width: '100%'
					}}
				/>
			</div>
		</div>
	);
}

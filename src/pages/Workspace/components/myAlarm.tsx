import * as React from 'react';
import { useState, useEffect } from 'react';
import HomeCard from '@/components/HomeCard';
import { Radio, Pagination } from 'antd';
import AlarmTimeLine from '@/components/AlarmTimeline';

import { getWorkspaceAlertRecord } from '@/services/workspace';
import { eventDataProps } from '@/types/comment';
import { radioList } from '@/utils/const';
const RadioGroup = Radio.Group;
const MyAlarm = ({
	resourceHeight
}: {
	resourceHeight: number;
}): JSX.Element => {
	const alarmHeight = resourceHeight + 510 - 112 - 250 - 12;
	const [level, setLevel] = useState<string>('');
	const [current, setCurrent] = useState<number>(1);
	const [total, setTotal] = useState<number>(10); // 总数
	const [eventData, setEventData] = useState<eventDataProps[]>([]);

	const getData = (sendData: any) => {
		getWorkspaceAlertRecord(sendData).then((res) => {
			if (res.success) {
				setCurrent(
					res.data
						? res.data?.list?.length === 0
							? 0
							: res.data?.pageNum
						: 1
				);
				setEventData(res.data ? res.data?.list : []);
				setTotal(res.data ? res.data?.total : 0);
			}
		});
	};

	const paginationChange = (current: number) => {
		setCurrent(current);
		const alertData = {
			current,
			level
		};
		getData(alertData);
	};

	useEffect(() => {
		const alertData = {
			current,
			level
		};
		getData(alertData);
	}, [level]);

	return (
		<div id="my-alarm" style={{ height: alarmHeight }}>
			<div className="my-title">实时告警</div>
			<div className="my-alarm-empty-content">
				<RadioGroup
					options={radioList}
					onChange={(e) => setLevel(e.target.value)}
					value={level}
					optionType="button"
					size="middle"
					style={{ marginTop: 16 }}
				/>
				<AlarmTimeLine
					list={eventData}
					style={{
						marginTop: 16,
						height: 'calc(100% - 120px)'
					}}
					type="workspace"
				/>
				{eventData && eventData.length ? (
					<div
						style={{
							display: 'flex',
							alignItems: 'center',
							justifyContent: 'space-between',
							marginTop: '8px'
						}}
					>
						<span>总数：{total}</span>
						<Pagination
							style={{ float: 'right' }}
							current={current}
							size="small"
							onChange={paginationChange}
							total={total}
							simple
						/>
					</div>
				) : null}
			</div>
		</div>
	);
};

export default MyAlarm;

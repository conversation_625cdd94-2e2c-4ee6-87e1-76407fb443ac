import React, { useEffect, useRef, useState } from 'react';
import { Badge, Cascader, Empty, Space, Spin, notification } from 'antd';
import { DefaultOptionType } from 'antd/lib/cascader';
import { getProjects } from '@/services/project';
import { ProjectItem } from '@/types';
import { api } from '@/api.json';
import otherColor from '@/assets/images/nodata.svg';
import { OrganizationItem } from '@/pages/OrganizationManagement/organization';
import { getWorkspaceMiddleware } from '@/services/workspace';

export default function MyResource({
	organizations,
	setResourceHeight
}: {
	organizations: OrganizationItem[];
	setResourceHeight: (data: number) => void;
}): JSX.Element {
	const ref = useRef<HTMLDivElement>(null);
	const [options, setOptions] = useState<Option[]>([]);
	const [value, setValue] = useState<string[]>([]);
	const [resources, setResources] = useState<any>([]);
	const [spinning, setSpinning] = useState<boolean>(false);
	useEffect(() => {
		getData();
	}, []);

	useEffect(() => {
		if (organizations?.length > 0) {
			const lt = organizations.map((item) => {
				return {
					value: item.organId,
					label: item.name,
					key: item.organId,
					isLeaf: false
				};
			});
			setOptions(lt);
		}
	}, [organizations]);
	useEffect(() => {
		setResourceHeight(ref.current?.offsetHeight as number);
	}, [resources]);
	const getData = (sendData?: string[]) => {
		setSpinning(true);
		getWorkspaceMiddleware({
			organId: sendData?.[0] || '',
			projectId: sendData?.[1] || ''
		}).then((res) => {
			setSpinning(false);
			if (res.success) {
				setResources(res.data);
			}
		});
	};
	const onChange = (value: any) => {
		console.log(value);
		setValue(value);
		getData(value);
	};
	const loadData = (selectedOptions: DefaultOptionType[]) => {
		const targetOption = selectedOptions[selectedOptions.length - 1];
		targetOption.loading = true;
		return getProjects({
			key: '',
			organId: selectedOptions[0]?.value as string
		}).then((res) => {
			if (res.success) {
				const clt = res.data.map((item: ProjectItem) => {
					return {
						value: item.projectId,
						label: item.aliasName || item.name,
						key: item.projectId,
						isLeaf: true
					};
				});
				targetOption.loading = false;
				targetOption.children = clt;
				setOptions([...options]);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	return (
		<div id="my-resource" ref={ref}>
			<div className="my-resource-title-content">
				<div className="my-title">我的资源</div>
				<div>
					<Cascader
						placeholder="全部"
						allowClear
						onChange={onChange}
						options={options}
						value={value}
						loadData={loadData}
					/>
				</div>
			</div>
			<Spin spinning={spinning} style={{ height: '100%' }}>
				<div
					className="my-resource-value-content"
					style={{
						justifyContent:
							resources.length <= 4 ? 'center' : 'start'
					}}
				>
					{resources.length === 0 && (
						<div className="my-resource-empty-card">
							<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
						</div>
					)}
					{resources.length !== 0 &&
						resources.map((item: any) => {
							return (
								<div
									key={item.type}
									className="my-resource-value-card"
								>
									<img
										height={40}
										width={40}
										src={
											item.imagePath
												? `${api}/images/middleware/${item.imagePath}`
												: otherColor
										}
									/>
									<div className="my-resource-card-content-title">
										{item.type}
									</div>
									<Space>
										{item.running ? (
											<Badge
												status="success"
												text={item.running + '个'}
											/>
										) : null}
										{item.warning ? (
											<Badge
												status="warning"
												text={item.warning + '个'}
											/>
										) : null}
										{item.error ? (
											<Badge
												status="error"
												text={item.error + '个'}
											/>
										) : null}
									</Space>
								</div>
							);
						})}
				</div>
			</Spin>
		</div>
	);
}

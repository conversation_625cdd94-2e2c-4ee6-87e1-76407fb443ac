import { IconFont } from '@/components/IconFont';
import storage from '@/utils/storage';
import React from 'react';

export default function MyInfo(): JSX.Element {
	const role = JSON.parse(storage.getLocal('role') || '{}');

	return (
		<div id="my-info">
			<IconFont
				type="icon-user-circle"
				style={{
					fontSize: '50px'
				}}
			/>
			<div className="my-info-user-name">{role?.userName}</div>
		</div>
	);
}

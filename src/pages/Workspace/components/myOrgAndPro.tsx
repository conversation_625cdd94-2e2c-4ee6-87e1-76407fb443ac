import React, { useEffect, useState } from 'react';
import { Empty, notification, Space } from 'antd';
import { UpOutlined } from '@ant-design/icons';
import { useHistory, useLocation } from 'react-router';
import { ProjectItem, StoreState } from '@/types';
import { OrganizationItem } from '@/pages/OrganizationManagement/organization';
import storage from '@/utils/storage';
import { getOrganAndProject } from '@/services/workspace';
import { getMyTopic } from '@/services/workOrder';
import OrganImage from '@/assets/images/workspace-organ.png';
import { ReactComponent as OrganIcon } from '@/assets/images/organ-manage.svg';
import { ReactComponent as ProjectIcon } from '@/assets/images/project-manage.svg';
import { ReactComponent as ProjectHoverIcon } from '@/assets/images/project-manage-hover.svg';
import { connect } from 'react-redux';

function MyOrgAndPro({ allMenu }: { allMenu: any[] }): JSX.Element {
	const history = useHistory();
	const location = useLocation();
	const [list, setList] = useState<OrganizationItem[]>([]);
	const [foldOrgans, setFoldOrgans] = useState<string[]>([]);
	const [itemHoverId, setItemHoverId] = useState<string>('');
	const [showDetail, setShowDetail] = useState<boolean>(false);

	useEffect(() => {
		async function getData() {
			const res = await getOrganAndProject();
			if (res.success) {
				setList(res.data);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		}

		getData();
	}, []);

	useEffect(() => {
		const workspace =
			allMenu?.find((item: MenuResItem) => item.name === 'workspace')
				?.subMenu || [];
		const res = workspace?.filter(
			(item: MenuResItem) => item.name !== 'workspacePage'
		)?.length;
		setShowDetail(res);
	}, [allMenu]);

	const toBusiness = (record: OrganizationItem, project: ProjectItem) => {
		getMyTopic({
			organId: record?.organId,
			projectId: project?.projectId
		}).then((res) => {
			if (res.success) {
				storage.setLocal('myTopic', res.data);
			}
		});
		storage.setSession('organization', record);
		storage.setSession('organId', record?.organId);
		storage.setSession('project', project);
		storage.setSession('projectId', project?.projectId);
		storage.setSession('preUrl', location.pathname);
		history.push('/project');
	};
	return (
		<div id="my-org-and-pro">
			<div className="my-title">我参与的</div>
			{list.length > 0 &&
				list.map((item: OrganizationItem) => {
					if (item.projectCount) {
						return (
							<div
								className="organization-list-content"
								key={item.organId}
							>
								<div
									className="organization-list-title"
									style={{
										position: 'relative',
										backgroundColor: '#F4F5F8',

										borderBottom: foldOrgans.includes(
											item.organId
										)
											? 'none'
											: '1px solid #d7d7d7'
									}}
								>
									<div
										style={{
											position: 'absolute',
											top: 0,
											right: 0,
											width: 186,
											height: '100%',
											background: `url(${OrganImage}) no-repeat`
										}}
									/>
									<UpOutlined
										style={{
											marginRight: 16,
											color: foldOrgans.includes(
												item.organId
											)
												? ''
												: '#226EE7',
											transform: `rotate(${
												foldOrgans.includes(
													item.organId
												)
													? '180deg'
													: '0deg'
											})`
										}}
										onClick={() =>
											foldOrgans.includes(item.organId)
												? setFoldOrgans(
														foldOrgans.filter(
															(str) =>
																str !==
																item.organId
														)
												  )
												: setFoldOrgans([
														...foldOrgans,
														item.organId
												  ])
										}
									/>
									<OrganIcon
										style={{
											width: 26,
											height: 26,
											marginRight: 16
										}}
									/>
									<span
										onClick={() =>
											foldOrgans.includes(item.organId)
												? setFoldOrgans(
														foldOrgans.filter(
															(str) =>
																str !==
																item.organId
														)
												  )
												: setFoldOrgans([
														...foldOrgans,
														item.organId
												  ])
										}
									>
										{item.name}
									</span>
									{item.projectCount ? (
										<span className="project-count">
											{item.projectCount}个项目
										</span>
									) : null}
								</div>
								<Space
									wrap
									size={16}
									className={`project-list-content ${
										foldOrgans.includes(item.organId)
											? 'none'
											: ''
									} ${
										!item.projectDtoList ||
										item.projectDtoList.length === 0
											? 'no-data'
											: ''
									}`}
								>
									{(!item.projectDtoList ||
										item.projectDtoList.length === 0) && (
										<div className="my-resource-empty-card">
											<Empty
												image={
													Empty.PRESENTED_IMAGE_SIMPLE
												}
												style={{ margin: '0px' }}
											/>
										</div>
									)}
									{item?.projectDtoList &&
										item?.projectDtoList.length > 0 &&
										item?.projectDtoList.map(
											(projectItem: ProjectItem) => {
												return (
													<div
														key={
															projectItem.projectId
														}
														onClick={() => {
															if (showDetail) {
																toBusiness(
																	item,
																	projectItem
																);
															}
														}}
														className={`${
															showDetail
																? 'active'
																: ''
														} project-list-content-value-card`}
														onMouseEnter={() =>
															setItemHoverId(
																projectItem.projectId
															)
														}
														onMouseLeave={() =>
															setItemHoverId('')
														}
													>
														<div className="project-list-content-card-icon">
															{itemHoverId ===
															projectItem.projectId ? (
																<ProjectHoverIcon />
															) : (
																<ProjectIcon />
															)}
														</div>
														<div>
															<span>
																{projectItem.aliasName ||
																	projectItem.name}
															</span>
															<p>项目</p>
														</div>
													</div>
												);
											}
										)}
								</Space>
							</div>
						);
					}
				})}
		</div>
	);
}

export default connect((state: StoreState) => ({
	allMenu: state.auth.allMenu
}))(MyOrgAndPro);

import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router';
import { connect } from 'react-redux';
import { Menu } from 'antd';
import { MenuInfo } from '@/types/comment';
import DefaultPicture from '@/components/DefaultPicture';
import MasterSlaveSwitch from './MasterSlaveSwitch/MasterSlaveSwitch';
import DeployConfig from './DeployConfig';
import InstanceScale from './InstanceScale/InstanceScale';
import Console from './Console';
import YamlCheck from './YamlCheck';
import DatabaseMag from './DatabaseMag';
import StorageList from './StorageList/StorageList';
import { DetailParams, OperatorAbilityProps } from '../detail';
import AuthControl from './AuthControl/AuthControl';
import ServiceVersion from './ServiceVersion';
import NodeMigration from './NodeMigration';
import storage from '@/utils/storage';
import SecondContent from '@/components/SecondContent';
import MiddlewareVersion from './MiddlewareVersion';
import AccountManage from './AccountManage';
import AgentAuthentication from './AgentAuthentication';
const officialMiddleware = [
	'mysql',
	'redis',
	'elasticsearch',
	'postgresql',
	'rocketmq',
	'kafka',
	'zookeeper'
];
function OperatorAbility(props: OperatorAbilityProps): JSX.Element {
	const { onRefresh, data, subMenu } = props;
	const params: DetailParams = useParams();
	const hasWebpage = window.location.href.includes('webpage');
	const [podMigrateAPI] = useState<boolean>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'podMigrate')?.enabled ?? true
	);
	const [deploymentConfigurationAPI] = useState<boolean>(
		storage
			.getLocal('featureAPI')
			.find((item: any) => item.name === 'deploymentConfiguration')
			?.enabled ?? true
	);
	const [middlewareVersionManagementAPI] = useState<boolean>(
		storage
			.getLocal('featureAPI')
			.find((item: any) => item.name === 'middlewareVersionManagement')
			?.enabled ?? true
	);
	const [containerConsoleAPI] = useState<boolean>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'containerConsole')?.enabled ??
			true
	);
	const [items, setItems] = useState([
		{ label: '存储列表', key: 'storageList', code: 'opsStorageList' },
		{ label: '节点迁移', key: 'nodeMigration', code: 'opsNodeMigration' },
		{ label: '控制台', key: 'console', code: 'opsConsole' },
		{ label: '部署配置', key: 'deployConfig', code: 'opsConf' },
		{ label: '查看YAML', key: 'yamlCheck', code: 'opsYaml' },
		{ label: '实例扩缩容', key: 'instanceScale', code: 'opsScale' },
		{
			label: '服务版本管理',
			key: 'serviceVersion',
			code: 'opsServiceVersion'
		},
		{
			label: '中间件版本管理',
			key: 'middlewareVersion',
			code: 'opsMiddlewareVersion'
		}
	]);
	const [selectedKey, setSelectedKey] = useState<string[]>([
		storage.getSession('operatorTab') || 'storageList'
	]);
	const setWrapperItems = (values: any) => {
		if (!hasWebpage) {
			setItems(values);
		}
	};
	useEffect(() => {
		if (params.name === 'rocketmq') {
			let lt = [
				...items
				// { label: '访问权限控制', key: 'authControl', code: 'opsAccess' }
			];
			if (!podMigrateAPI) {
				lt = lt.filter((item) => item.key !== 'nodeMigration');
			}
			if (!deploymentConfigurationAPI) {
				lt = lt.filter((item) => item.key !== 'deployConfig');
			}
			if (!middlewareVersionManagementAPI) {
				lt = lt.filter((item) => item.key !== 'serviceVersion');
			}
			if (!containerConsoleAPI) {
				lt = lt.filter((item) => item.key !== 'console');
			}
			const _items = lt.filter((item: any) =>
				subMenu.find((menu: any) => menu.name === item.code)
			);
			setWrapperItems(_items);
			return;
		} else if (
			params.name === 'mysql' &&
			(data.mode === '1m-1s' ||
				data.mode === '1m-3s' ||
				data.mode === '1m-ns')
		) {
			let lt = [...items];
			lt.splice(5, 0, {
				label: '主从切换',
				code: 'opsSwitch',
				key: 'masterSlaveSwitch'
			});
			if (!podMigrateAPI) {
				lt = lt.filter((item) => item.key !== 'nodeMigration');
			}
			if (!deploymentConfigurationAPI) {
				lt = lt.filter((item) => item.key !== 'deployConfig');
			}
			if (!middlewareVersionManagementAPI) {
				lt = lt.filter((item) => item.key !== 'serviceVersion');
			}
			if (!containerConsoleAPI) {
				lt = lt.filter((item) => item.key !== 'console');
			}
			const _item2 = lt.filter((item: any) =>
				subMenu.find((menu: any) => menu.name === item.code)
			);
			setWrapperItems(_item2);

			return;
		} else if (params.name === 'postgresql' && data.mode !== '1m-0s') {
			let lt = [...items];
			if (data.mode !== '1m-0s') {
				lt.splice(5, 0, {
					label: '主从切换',
					code: 'opsSwitch',
					key: 'masterSlaveSwitch'
				});
				if (!podMigrateAPI) {
					lt = lt.filter((item) => item.key !== 'nodeMigration');
				}
				if (!deploymentConfigurationAPI) {
					lt = lt.filter((item) => item.key !== 'deployConfig');
				}
				if (!middlewareVersionManagementAPI) {
					lt = lt.filter((item) => item.key !== 'serviceVersion');
				}
			}
			lt.push({
				label: '客户端认证',
				key: 'agentAuthentication',
				code: 'pgHba'
			});
			if (!containerConsoleAPI) {
				lt = lt.filter((item) => item.key !== 'console');
			}
			const _item3 = lt.filter((item: any) =>
				subMenu.find((menu: any) => menu.name === item.code)
			);
			setWrapperItems(_item3);
			return;
		} else if (params.name === 'redis') {
			let lt = [...items];
			if (data.mode === 'cluster') {
				lt.splice(5, 0, {
					label: '主从切换',
					code: 'opsSwitch',
					key: 'masterSlaveSwitch'
				});
				if (!data.version.startsWith('5')) {
					lt.splice(6, 0, {
						label: '账户管理',
						key: 'accountManage',
						code: 'opsAccountManagement'
					});
				}
			} else {
				lt.splice(5, 0, {
					label: '账户管理',
					key: 'accountManage',
					code: 'opsAccountManagement'
				});
			}
			if (!podMigrateAPI) {
				lt = lt.filter((item) => item.key !== 'nodeMigration');
			}
			if (!deploymentConfigurationAPI) {
				lt = lt.filter((item) => item.key !== 'deployConfig');
			}
			if (!middlewareVersionManagementAPI) {
				lt = lt.filter((item) => item.key !== 'serviceVersion');
			}
			if (!containerConsoleAPI) {
				lt = lt.filter((item) => item.key !== 'console');
			}
			const _item4 = lt.filter((item: any) =>
				subMenu.find((menu: any) => menu.name === item.code)
			);
			setWrapperItems(_item4);

			return;
		} else if (
			params.name === 'nacos' ||
			params.name === 'elasticsearch' ||
			params.name === 'rabbitmq'
		) {
			let lt = [...items];
			lt.splice(5, 0, {
				label: '账户管理',
				key: 'accountManage',
				code: 'opsAccountManagement'
			});
			if (!podMigrateAPI) {
				lt = lt.filter((item) => item.key !== 'nodeMigration');
			}
			if (!deploymentConfigurationAPI) {
				lt = lt.filter((item) => item.key !== 'deployConfig');
			}
			if (!middlewareVersionManagementAPI) {
				lt = lt.filter((item) => item.key !== 'serviceVersion');
			}
			if (!containerConsoleAPI) {
				lt = lt.filter((item) => item.key !== 'console');
			}
			const _item5 = lt.filter((item: any) =>
				subMenu.find((menu: any) => menu.name === item.code)
			);
			setWrapperItems(_item5);

			return;
		} else {
			let lt = [...items];
			if (!podMigrateAPI) {
				lt = lt.filter((item) => item.key !== 'nodeMigration');
			}
			if (!deploymentConfigurationAPI) {
				lt = lt.filter((item) => item.key !== 'deployConfig');
			}
			if (!middlewareVersionManagementAPI) {
				lt = lt.filter((item) => item.key !== 'serviceVersion');
			}
			if (!officialMiddleware.includes(params.name)) {
				lt = lt.filter((item) => item.key !== 'middlewareVersion');
			}
			if (!containerConsoleAPI) {
				lt = lt.filter((item) => item.key !== 'console');
			}
			const _item6 = lt.filter((item: any) =>
				subMenu.find((menu: any) => menu.name === item.code)
			);
			setWrapperItems(_item6);
			return;
		}
	}, [params.name]);
	useEffect(() => {
		(!storage.getSession('operatorTab') ||
			!items?.some(
				(item) => item.key === storage.getSession('operatorTab')
			)) &&
			setSelectedKey([items[0].key]);
	}, [items]);
	const menuSelect = (info: MenuInfo) => {
		setSelectedKey(info.keyPath);
		storage.setSession('operatorTab', info.key);
	};
	const ConsoleMenu = () => (
		<Menu
			selectedKeys={selectedKey}
			onClick={menuSelect}
			style={{ height: '100%' }}
			items={items}
			mode="inline"
			className="serve-alarm-menu"
		/>
	);
	const childrenRender = (sk: string) => {
		switch (sk) {
			case 'masterSlaveSwitch':
				return (
					<MasterSlaveSwitch
						mode={data.mode}
						serverStatus={data.status}
						serviceRefresh={onRefresh}
						lock={data.lock}
					/>
				);
			case 'deployConfig':
				return <DeployConfig lock={data.lock} />;
			case 'instanceScale':
				return (
					<InstanceScale
						lock={data.lock}
						mode={data.mode}
						quota={data.quota}
						status={data.status}
						serviceRefresh={onRefresh}
						enableScale={data.enableScale}
						readWriteProxy={data.readWriteProxy}
						typeParam={data?.[`${params.aliasName}Param`] || null}
					/>
				);
			case 'console':
				return <Console lock={data.lock} />;
			case 'yamlCheck':
				return <YamlCheck />;
			case 'databaseMag':
				return <DatabaseMag />;
			case 'storageList':
				return <StorageList lock={data.lock} />;
			case 'authControl':
				return (
					<AuthControl
						lock={data.lock}
						serviceRefresh={onRefresh}
						acl={data.rocketMQParam.acl}
					/>
				);
			case 'serviceVersion':
				return (
					<ServiceVersion
						serviceRefresh={onRefresh}
						lock={data.lock}
					/>
				);
			case 'nodeMigration':
				return <NodeMigration lock={data.lock} />;
			case 'middlewareVersion':
				return (
					<MiddlewareVersion
						lock={data.lock}
						serviceRefresh={onRefresh}
					/>
				);
			case 'accountManage':
				return (
					<AccountManage
						lock={data.lock}
						devAccount={data.devAccount}
						serviceRefresh={onRefresh}
					/>
				);
			case 'agentAuthentication':
				return (
					<AgentAuthentication
						lock={data.lock}
						serviceRefresh={onRefresh}
					/>
				);
			default:
				return <DefaultPicture title="当前运维功能暂不支持" />;
		}
	};
	return (
		<SecondContent menu={<ConsoleMenu />} style={{ margin: 0, padding: 0 }}>
			{childrenRender(selectedKey[0])}
		</SecondContent>
	);
}

export default OperatorAbility;

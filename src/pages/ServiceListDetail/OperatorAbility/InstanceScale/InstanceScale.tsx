import React, { useEffect, useState } from 'react';
import { Badge, Button, notification, Modal, Tooltip, Switch } from 'antd';
import { PresetStatusColorType } from 'antd/es/_util/colors';
import { useHistory, useParams } from 'react-router';
import { ReloadOutlined, InfoCircleFilled } from '@ant-design/icons';
import ServiceCard from '@/components/ServiceCard';
import transUnit from '@/utils/transUnit';
import { DetailParams, InstanceScaleProps, podGroupStatus } from '../../detail';
import VerticalExpansion from './VerticalExpansion';
import {
	getPodGroupsStatus,
	getReBalanceStatus,
	updateMiddleware,
	reBalance,
	checkScaleByBackup,
	updateScaleByBackup
} from '@/services/middleware';
import LateralExpansion from './LateralExpansion';
import useRefresh from '@/utils/useRefresh';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';
import { controlledOperationDisabled } from '@/utils/utils';
const confirm = Modal.confirm;
const roleMap: any = {
	kibana: 'Kibana',
	client: '协调节点',
	master: '主节点',
	data: '数据节点',
	cold: '冷节点',
	redis: 'Redis节点',
	sentinel: '哨兵节点',
	proxy: '代理节点'
};
const rebalanceStatus: any = {
	processing: '进行中',
	success: '执行完成',
	error: '执行失败',
	default: '无执行记录'
};
export default function InstanceScale(props: InstanceScaleProps): JSX.Element {
	const {
		mode,
		quota,
		status,
		readWriteProxy,
		serviceRefresh,
		typeParam,
		lock
	} = props;
	const {
		clusterId,
		namespace,
		name,
		middlewareName,
		aliasName,
		chartVersion,
		type
	}: DetailParams = useParams();
	const history = useHistory();
	const [scaleType, setScaleType] = useState<any>();
	const [scaleOpen, setScaleOpen] = useState<boolean>(false);
	const [lateralExpansionOpen, setLateralExpansionOpen] =
		useState<boolean>(false);
	const [podGroups, setPodGroups] = useState<podGroupStatus[]>([]);
	const [balanceStatus, setBalanceStatus] =
		useState<PresetStatusColorType>('default');
	const [loading, setLoading] = useState<boolean>(false);
	const [refreshKey, setRefreshKey] = useState<number>(0);
	const verticalExpansionOperatorId = maintenances['Modify Specification'];
	const lateralExpansionOperatorId = maintenances['Horizontal Scaling'];
	const kafkaReBalanceOperatorId = maintenances['【Kafka】Rehash'];
	const PostgresqlBasedOnBackupOperationOperatorId =
		maintenances['【Postgresql】Based On Backup Operation Switch'];
	const [baseBackupSwitch, setBaseBackupSwitch] = useState<boolean>(false);
	const [baseBackupDisabled, setBaseBackupDisabled] =
		useState<boolean>(false);
	const [tip, setTip] = useState<string>();

	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		getPodStatus();
		name === 'kafka' && getKafkaBalanceStatus();
		name === 'postgresql' && getPostgresqlSwitchStatus();
	}, [refreshKey]);
	const getPodStatus = () => {
		if (name === 'redis' || name === 'elasticsearch') {
			getPodGroupsStatus({
				type: name,
				clusterId,
				namespace,
				middlewareName
			}).then((res) => {
				if (res.success) {
					setPodGroups(res.data);
				} else {
					setPodGroups([]);
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			});
		}
	};
	const getKafkaBalanceStatus = () => {
		setLoading(true);
		getReBalanceStatus({
			clusterId,
			namespace,
			name: middlewareName
		})
			.then((res) => {
				if (res.success) {
					switch (res.data?.status) {
						case 'Listen':
							setBalanceStatus('processing');
							break;
						case 'Executing':
							setBalanceStatus('processing');
							break;
						case 'Active':
							setBalanceStatus('processing');
							break;
						case 'Completed':
							setBalanceStatus('success');
							break;
						case 'CompletedWithError':
							setBalanceStatus('error');
							break;
						default:
							setBalanceStatus('default');
							break;
					}
				} else {
					setBalanceStatus('default');
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const getPostgresqlSwitchStatus = () => {
		const sendData = {
			clusterId: clusterId,
			namespace: namespace,
			middlewareName: middlewareName
		};
		checkScaleByBackup(sendData).then((res) => {
			if (res.success) {
				if (res.data === 1) {
					setBaseBackupSwitch(true);
					setBaseBackupDisabled(false);
				} else if (res.data === 2) {
					setBaseBackupSwitch(false);
					setBaseBackupDisabled(false);
				} else {
					setBaseBackupDisabled(true);
					setTip(res.errorMsg || '');
				}
			} else {
				setTip(res.errorMsg || '');
			}
		});
	};
	const childrenRender = () => {
		if (name === 'postgresql') {
			return (
				<ServiceCard
					lock={lock}
					type={name}
					middlewareName={middlewareName}
					status={status}
					cpu={quota[name].cpu}
					memory={Number(
						transUnit.removeUnit(quota[name].memory, 'Gi')
					)}
					num={quota[name].num ?? '/'}
					onMidClick={() => {
						WorkOrderFuc(
							() => {
								setLateralExpansionOpen(true);
							},
							lock,
							middlewareName,
							lateralExpansionOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
					onActionClick={() => {
						WorkOrderFuc(
							() => {
								setScaleOpen(true);
							},
							lock,
							middlewareName,
							verticalExpansionOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
					aliasName={aliasName}
					// enableScale={enableScale}
				/>
			);
		}
		if (name === 'elasticsearch') {
			const list = [];
			for (const i in quota) {
				if (quota[i].num !== 0) {
					list.push({
						...quota[i],
						title: i,
						status:
							podGroups.find((p) => p.role === i)?.status ||
							'error'
					});
				}
			}
			return list.map((item) => {
				return (
					<ServiceCard
						lock={lock}
						key={item.title}
						type={roleMap[item.title]}
						middlewareName={roleMap[item.title]}
						status={item.status}
						cpu={Number(item.cpu)}
						memory={Number(transUnit.removeUnit(item.memory, 'Gi'))}
						num={item.num}
						onMidClick={() => {
							WorkOrderFuc(
								() => {
									setScaleType(item);
									setLateralExpansionOpen(true);
								},
								lock,
								middlewareName,
								lateralExpansionOperatorId,
								history,
								type,
								name,
								aliasName,
								clusterId,
								namespace
							);
						}}
						podLabel="数量"
						onActionClick={() => {
							WorkOrderFuc(
								() => {
									setScaleType(item);
									setScaleOpen(true);
								},
								lock,
								middlewareName,
								verticalExpansionOperatorId,
								history,
								type,
								name,
								aliasName,
								clusterId,
								namespace
							);
						}}
						aliasName={aliasName}
						isPod={true}
						color="#226ee7"
					/>
				);
			});
		}
		if (name === 'redis') {
			const list = [];
			for (const i in quota) {
				list.push({
					...quota[i],
					title: i,
					status:
						podGroups.find((p) => p.role === i)?.status || 'error'
				});
			}
			return list.map((item) => {
				const onMidClick = () => {
					WorkOrderFuc(
						() => {
							setScaleType(item);
							setLateralExpansionOpen(true);
						},
						lock,
						middlewareName,
						lateralExpansionOperatorId,
						history,
						type,
						name,
						aliasName,
						clusterId,
						namespace
					);
				};
				const onActionClick = () => {
					WorkOrderFuc(
						() => {
							setScaleType(item);
							setScaleOpen(true);
						},
						lock,
						middlewareName,
						verticalExpansionOperatorId,
						history,
						type,
						name,
						aliasName,
						clusterId,
						namespace
					);
				};
				return (
					<ServiceCard
						lock={lock}
						key={item.title}
						type={roleMap[item.title]}
						middlewareName={roleMap[item.title]}
						status={item.status}
						cpu={Number(item.cpu)}
						memory={Number(transUnit.removeUnit(item.memory, 'Gi'))}
						num={
							item.title === 'redis'
								? Number(quota.redis.num) / 2
								: item.num
						}
						onMidClick={
							(item.title === 'redis' && mode !== 'cluster') ||
							item.title === 'proxy'
								? undefined
								: onMidClick
						}
						podLabel={item.title === 'redis' ? '分片数量' : '数量'}
						onActionClick={
							item.title === 'proxy' ? undefined : onActionClick
						}
						aliasName={aliasName}
						isPod={true}
						color={
							item.title === 'redis' &&
							mode === 'cluster' &&
							!readWriteProxy.enabled
								? '#fa8c16'
								: '#226ee7'
						}
						// enableScale={enableScale}
					/>
				);
			});
		}
		if (name === 'kafka') {
			return (
				<ServiceCard
					lock={lock}
					type={name}
					middlewareName={middlewareName}
					status={status}
					cpu={quota[name].cpu}
					memory={Number(
						transUnit.removeUnit(quota[name].memory, 'Gi')
					)}
					num={quota[name].num ?? '/'}
					podLabel="集群实例数量"
					onActionClick={() => {
						WorkOrderFuc(
							() => {
								setScaleOpen(true);
							},
							lock,
							middlewareName,
							verticalExpansionOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
					onMidClick={() => {
						WorkOrderFuc(
							() => {
								setLateralExpansionOpen(true);
							},
							lock,
							middlewareName,
							lateralExpansionOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
					aliasName={aliasName}
				/>
			);
		}
		if (name === 'zookeeper') {
			return (
				<ServiceCard
					lock={lock}
					type={name}
					middlewareName={middlewareName}
					status={status}
					cpu={quota[name].cpu}
					memory={Number(
						transUnit.removeUnit(quota[name].memory, 'Gi')
					)}
					podLabel="节点数"
					num={quota[name].num ?? '/'}
					onActionClick={() => {
						WorkOrderFuc(
							() => {
								setScaleOpen(true);
							},
							lock,
							middlewareName,
							verticalExpansionOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
					onMidClick={() => {
						WorkOrderFuc(
							() => {
								setLateralExpansionOpen(true);
							},
							lock,
							middlewareName,
							lateralExpansionOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
					aliasName={aliasName}
				/>
			);
		}
		return (
			<ServiceCard
				lock={lock}
				type={name}
				middlewareName={middlewareName}
				status={status}
				cpu={quota?.[name].cpu}
				memory={Number(
					transUnit.removeUnit(quota?.[name].memory, 'Gi')
				)}
				num={quota?.[name].num ?? '/'}
				onActionClick={() => {
					WorkOrderFuc(
						() => {
							setScaleOpen(true);
						},
						lock,
						middlewareName,
						verticalExpansionOperatorId,
						history,
						type,
						name,
						aliasName,
						clusterId,
						namespace
					);
				}}
				aliasName={aliasName}
			/>
		);
	};

	// * 修改节点规格
	const updateMid = (sendData: any, actionType: string) => {
		setScaleOpen(false);
		setLateralExpansionOpen(false);
		updateMiddleware(sendData).then((res) => {
			if (res.success) {
				notification.success({
					message: '成功',
					description: '修改中, 3s 后获取数据'
				});
				setTimeout(function () {
					serviceRefresh();
					getPodStatus();
				}, 3000);
				if (actionType === 'lateral') {
					name === 'kafka' &&
						Modal.info({
							title: '提醒',
							content:
								'为了保证扩容后Kafka服务的整体性能，需要等待服务运行正常后进行重新散列操作。'
						});
				}
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const balanceConfirm = () => {
		confirm({
			title: '操作确认',
			content:
				'此操作会短暂影响Kafka服务的整体性能，请在合适的时间谨慎操作！是否继续？',
			onOk: async () => {
				await ExecuteOrderFuc();
				return reBalance({
					clusterId,
					namespace,
					name: middlewareName
				}).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '执行成功！'
						});
						getKafkaBalanceStatus();
					} else {
						notification.error({
							message: '失败',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};

	const onChange = (checked: boolean) => {
		confirm({
			title: '操作确认',
			content: (
				<>
					{checked ? '开启' : '关闭'}
					基于备份操作能力后，服务会自动重启
					<br />
					是否确定{checked ? '开启' : '关闭'}该能力？
				</>
			),
			onOk: async () => {
				await ExecuteOrderFuc();
				return updateScaleByBackup({
					clusterId,
					namespace,
					middlewareName,
					chartName: name,
					chartVersion: chartVersion,
					enable: checked
				}).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: `基于备份操作${
								checked ? '开启' : '关闭'
							}成功`
						});
						getPostgresqlSwitchStatus();
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};

	const titleRender = () => {
		if (name === 'kafka') {
			return (
				<h2>
					当前配置
					<Button
						type="primary"
						className="ml-12 mr-12"
						onClick={() => {
							WorkOrderFuc(
								balanceConfirm,
								lock,
								middlewareName,
								kafkaReBalanceOperatorId,
								history,
								type,
								name,
								aliasName,
								clusterId,
								namespace
							);
						}}
						disabled={
							(status !== 'Running' && status !== 'running') ||
							controlledOperationDisabled('maintenance', lock)
						}
					>
						重新散列
					</Button>
					<strong>散列执行情况：</strong>
					<Badge
						status={balanceStatus}
						text={rebalanceStatus[balanceStatus]}
					/>
					<Button
						type="link"
						icon={<ReloadOutlined />}
						onClick={getKafkaBalanceStatus}
						loading={loading}
					/>
				</h2>
			);
		} else if (name === 'postgresql') {
			return (
				<>
					<h2>当前配置</h2>
					<p>
						<span>基于备份操作</span>{' '}
						<Tooltip
							title={
								<div style={{ color: 'black' }}>
									开启后会配置从节点通过备份进行数据拉取，用于横向扩容和备库重搭。
								</div>
							}
							color={'#fff'}
						>
							<InfoCircleFilled
								style={{
									paddingLeft: '3px',
									paddingRight: '10px'
								}}
							/>
						</Tooltip>
						{baseBackupDisabled ? (
							<Tooltip title={tip}>
								<Switch checked={baseBackupSwitch} disabled />
							</Tooltip>
						) : (
							<Switch
								checked={baseBackupSwitch}
								onChange={(checked: boolean) => {
									WorkOrderFuc(
										() => {
											onChange(checked);
										},
										lock,
										middlewareName,
										PostgresqlBasedOnBackupOperationOperatorId,
										history,
										type,
										name,
										aliasName,
										clusterId,
										namespace
									);
								}}
							/>
						)}
					</p>
				</>
			);
		} else {
			return <h2>当前配置</h2>;
		}
	};
	return (
		<>
			{titleRender()}
			<div className="instance-scale-content">{childrenRender()}</div>
			{/* 修改规格 纵向 */}
			{scaleOpen && quota && (
				<VerticalExpansion
					type={name}
					middlewareName={middlewareName}
					clusterId={clusterId}
					namespace={namespace}
					chartVersion={chartVersion}
					chartName={name}
					open={scaleOpen}
					mode={mode}
					onCancel={() => setScaleOpen(false)}
					updateMid={updateMid}
					quota={quota}
					scaleType={scaleType}
					groupCount={typeParam?.group}
					groupReplicas={typeParam?.replicas}
				/>
			)}
			{/* 修改实例数 横向 */}
			{lateralExpansionOpen && (
				<LateralExpansion
					type={name}
					middlewareName={middlewareName}
					clusterId={clusterId}
					namespace={namespace}
					chartVersion={chartVersion}
					chartName={name}
					open={lateralExpansionOpen}
					mode={mode}
					onCancel={() => setLateralExpansionOpen(false)}
					updateMid={updateMid}
					quota={quota}
					scaleType={scaleType}
					readWriteProxy={readWriteProxy}
					groupCount={typeParam?.group}
					groupReplicas={typeParam?.replicas}
				/>
			)}
		</>
	);
}

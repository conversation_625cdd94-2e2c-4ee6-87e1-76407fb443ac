import SelectBlock from '@/components/SelectBlock';
import { Button } from 'antd';
import { CpuMemoryItem } from '@/pages/OrganizationDetail/organization.detail';
import StockAssessment from '@/pages/ServiceCatalog/components/StockAssessment';
import TableRadio from '@/pages/ServiceCatalog/components/TableRadio';
import { getNamespaceCpuMemory } from '@/services/project';
import {
	esDataList,
	formItemLayout618,
	instanceSpecList,
	kafkaDataList,
	mqDataList,
	mysqlDataList,
	redisDataList,
	redisSentinelDataList,
	zkpDataList
} from '@/utils/const';
import transUnit from '@/utils/transUnit';
import { Modal, Form, InputNumber, Alert, notification } from 'antd';
import React, { useEffect, useState } from 'react';
import { VerticalExpansionProps } from '../../detail';
const FormItem = Form.Item;
// ! 修改规格
export default function VerticalExpansion(
	props: VerticalExpansionProps
): JSX.Element {
	const {
		open,
		onCancel,
		middlewareName,
		type,
		mode,
		updateMid,
		clusterId,
		namespace,
		chartName,
		chartVersion,
		quota,
		scaleType,
		groupCount, // * mq dledger模式下的组数
		groupReplicas // * mq dledger模式下的副本数
	} = props;
	const [form] = Form.useForm();
	const [instanceSpec, setInstanceSpec] = useState<string>('');
	const [specId, setSpecId] = useState<string>('');
	const [cpu, setCpu] = useState<number>(0);
	const [memory, setMemory] = useState<number>(0);
	const [num, setNum] = useState<number>(0);
	const [cpuMemory, setCpuMemory] = useState<CpuMemoryItem>();
	const [computeQuota, setComputeQuota] = useState<{
		cpu: number;
		memory: number;
	}>({ cpu: 0, memory: 0 });
	useEffect(() => {
		if (quota) {
			if (
				type === 'elasticsearch' ||
				(type === 'redis' && mode !== 'cluster')
			) {
				setCpu(scaleType.cpu);
				setMemory(scaleType.memory);
				setNum(scaleType.num);
				form.setFieldsValue({
					cpu: Number(scaleType.cpu),
					memory: Number(transUnit.removeUnit(scaleType.memory, 'Gi'))
				});
				const specId = dataListRender().find(
					(item) =>
						transUnit.removeUnit(item.cpu, ' Core') ===
							scaleType.cpu &&
						transUnit.removeUnit(item.memory, ' Gi') ===
							transUnit.removeUnit(scaleType.memory, 'Gi')
				)?.id;
				!!specId && setInstanceSpec('General');
				!!specId && setSpecId(specId);
				!specId && setInstanceSpec('Customize');
			} else {
				setCpu(Number(quota[type]['cpu']));
				setMemory(Number(quota[type]['memory']));
				if (type === 'rocketmq' && mode === 'dledger') {
					setNum(groupReplicas as number);
				} else {
					setNum(quota[type]['num']);
				}
				form.setFieldsValue({
					cpu: Number(quota[type]['cpu']),
					memory: Number(
						transUnit.removeUnit(quota[type]['memory'], 'Gi')
					)
				});
				const specIdTemp = dataListRender().find(
					(item) =>
						transUnit.removeUnit(item.cpu, ' Core') ===
							quota[type]['cpu'] &&
						transUnit.removeUnit(item.memory, ' Gi') ===
							transUnit.removeUnit(quota[type]['memory'], 'Gi')
				)?.id;
				!!specIdTemp && setInstanceSpec('General');
				!!specIdTemp && setSpecId(specIdTemp);
				!specIdTemp && setInstanceSpec('Customize');
			}
		}
	}, [quota]);
	useEffect(() => {
		getNamespaceCpuMemory({
			clusterId,
			namespace
		}).then((res) => {
			if (res.success) {
				setCpuMemory(res.data);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	}, []);
	useEffect(() => {
		if (instanceSpec === 'General') {
			onTableRadioChange(specId);
		} else {
			setCpu(form.getFieldValue('cpu'));
			setMemory(form.getFieldValue('memory'));
		}
	}, [instanceSpec]);
	const getNum = () => {
		if (type === 'mysql') {
			let replicasTemp = 0;
			if (mode === '1m-0s') replicasTemp = 1;
			else if (mode === '1m-1s') replicasTemp = 2;
			else if (mode === '1m-3s') replicasTemp = 4;
			else replicasTemp = num + 1;
			return replicasTemp;
		} else if (type === 'postgresql') {
			let replicasTemp = 0;
			if (mode === '1m-0s') replicasTemp = 1;
			else if (mode === '1m-1s') replicasTemp = 2;
			else if (mode === '1m-3s') replicasTemp = 4;
			else replicasTemp = num + 1;
			return replicasTemp;
		} else if (type === 'rocketmq') {
			let replicasTemp = 0;
			if (mode === '2m-noslave') replicasTemp = 2;
			else if (mode === '2m-2s') replicasTemp = 4;
			else if (mode === '3m-3s') replicasTemp = 6;
			else if (mode === 'dledger')
				replicasTemp = (groupCount as number) * num;
			return replicasTemp;
		} else {
			return num;
		}
	};
	const getCpu = () => {
		if (type === 'kafka') {
			let cpuTemp = Number(quota?.[type].cpu ?? 0) || 0;
			cpuTemp = cpuTemp * getNum() + 0.2 + 0.5;
			return cpuTemp;
		} else if (type === 'postgresql') {
			let cpuTemp = Number(quota?.[type].cpu ?? 0) || 0;
			cpuTemp = (cpuTemp + 0.1) * getNum();
			return cpuTemp;
		} else if (type === 'rocketmq') {
			let cpuTemp = Number(quota?.[type].cpu ?? 0) || 0;
			cpuTemp = cpuTemp * getNum() + 0.5 + 1 + 2;
			return cpuTemp;
		} else if (type === 'mysql') {
			let cpuTemp = Number(quota?.[type].cpu ?? 0) || 0;
			cpuTemp = (cpuTemp + 0.2 + 0.2) * getNum();
			return cpuTemp;
		} else if (type === 'redis') {
			if (mode === 'cluster') {
				let cpuTemp = Number(quota?.[type].cpu ?? 0) || 0;
				cpuTemp = (cpuTemp + 0.025) * getNum();
				return cpuTemp;
			} else {
				let cpuTemp = Number(scaleType.cpu ?? 0) || 0;
				cpuTemp = (cpuTemp + 0.025) * scaleType?.num;
				return cpuTemp;
			}
		} else if (type === 'elasticsearch') {
			let cpuTemp = Number(scaleType.cpu ?? 0) || 0;
			cpuTemp = cpuTemp * scaleType?.num + 0.1;
			return cpuTemp;
		} else {
			let cpuTemp = Number(quota?.[type].cpu ?? 0) || 0;
			cpuTemp = cpuTemp * getNum();
			return cpuTemp;
		}
	};
	const getMemory = () => {
		if (type === 'kafka') {
			let memoryTemp =
				Number(transUnit.removeUnit(quota?.[type].memory, 'Gi') ?? 0) ||
				0;
			memoryTemp = memoryTemp * getNum() + 0.5 + 0.5;
			return memoryTemp;
		} else if (type === 'postgresql') {
			let memoryTemp =
				Number(transUnit.removeUnit(quota?.[type].memory, 'Gi') ?? 0) ||
				0;
			memoryTemp = (memoryTemp + 0.125) * getNum();
			return memoryTemp;
		} else if (type === 'rocketmq') {
			let memoryTemp =
				Number(transUnit.removeUnit(quota?.[type].memory, 'Gi') ?? 0) ||
				0;
			memoryTemp = memoryTemp * getNum() + 1 + 2 + 2;
			return memoryTemp;
		} else if (type === 'mysql') {
			let memoryTemp =
				Number(transUnit.removeUnit(quota?.[type].memory, 'Gi') ?? 0) ||
				0;
			memoryTemp = (memoryTemp + 0.2 + 0.2) * getNum();
			return memoryTemp;
		} else if (type === 'redis') {
			if (mode === 'cluster') {
				let memoryTemp =
					Number(
						transUnit.removeUnit(quota?.[type].memory, 'Gi') ?? 0
					) || 0;
				memoryTemp = (memoryTemp + 0.025) * getNum();
				return memoryTemp;
			} else {
				let memoryTemp =
					Number(transUnit.removeUnit(scaleType.memory, 'Gi') ?? 0) ||
					0;
				memoryTemp = (memoryTemp + 0.05) * scaleType?.num;
				return memoryTemp;
			}
		} else if (type === 'elasticsearch') {
			let memoryTemp =
				Number(transUnit.removeUnit(scaleType.memory, 'Gi') ?? 0) || 0;
			memoryTemp = memoryTemp * scaleType?.num + 0.125;
			return memoryTemp;
		} else {
			let memoryTemp =
				Number(transUnit.removeUnit(quota?.[type].memory, 'Gi') ?? 0) ||
				0;
			memoryTemp = memoryTemp * getNum();
			return memoryTemp;
		}
	};
	const dataListRender = () => {
		switch (type) {
			case 'kafka':
				return kafkaDataList;
			case 'rocketmq':
				return mqDataList;
			case 'elasticsearch':
				return esDataList;
			case 'redis':
				return mode === 'cluster'
					? redisDataList
					: scaleType.title === 'sentinel'
					? [redisSentinelDataList[0]]
					: scaleType.title === 'redis'
					? redisDataList
					: redisSentinelDataList;
			case 'mysql':
				return mysqlDataList;
			case 'postgresql':
				return mysqlDataList;
			case 'zookeeper':
				return zkpDataList;
			default:
				return [];
		}
	};
	const onTableRadioChange = (value: any) => {
		const cpuMemory = dataListRender().find(
			(item: any) => item.id === value
		);
		setSpecId(value);
		const cpu =
			cpuMemory && Number(transUnit.removeUnit(cpuMemory?.cpu, ' Core'));
		const memory =
			cpuMemory && Number(transUnit.removeUnit(cpuMemory.memory, 'Gi'));
		setCpu(cpu || 0);
		setMemory(memory || 0);
	};
	const onValuesChange = (changeValue: any, allValue: any) => {
		const keys = Object.keys(changeValue);
		if (keys[0] === 'cpu') setCpu(changeValue.cpu);
		if (keys[0] === 'memory') setMemory(changeValue.memory);
	};
	const onChange = (cpu: number, memory: number, storage: number) => {
		setComputeQuota({
			...computeQuota,
			cpu,
			memory
		});
	};
	const onOk = () => {
		form.validateFields().then((values) => {
			const quotaCpu = getCpu();
			const quotaMemory = getMemory();
			const sendCpu = instanceSpec === 'General' ? cpu : values.cpu;
			const sendMemory =
				instanceSpec === 'General' ? memory : values.memory;
			const requestCpu =
				(cpuMemory?.cpu.request || 0) -
				(cpuMemory?.cpu.used || 0) +
				quotaCpu;
			const requestMemory =
				(cpuMemory?.memory.request || 0) -
				(cpuMemory?.memory.used || 0) +
				quotaMemory;
			if (computeQuota.cpu > Number(requestCpu.toFixed(1))) {
				notification.error({
					message: '失败',
					description: '当前命名空间可分配CPU不足！'
				});
				return;
			}
			if (computeQuota.memory > Number(requestMemory.toFixed(1))) {
				notification.error({
					message: '失败',
					description: '当前命名空间可分配内存不足！'
				});
				return;
			}
			if (
				type === 'mysql' ||
				type === 'kafka' ||
				type === 'rocketmq' ||
				type === 'zookeeper'
			) {
				const sendData = {
					clusterId,
					namespace,
					middlewareName,
					chartName,
					chartVersion,
					type,
					quota: {
						[type]: {
							cpu: sendCpu,
							memory: sendMemory,
							num: quota[type]['num'],
							limitCpu: sendCpu,
							limitMemory: sendMemory + 'Gi'
						}
					}
				};
				updateMid(sendData, 'vertical');
				return;
			}
			if (type === 'postgresql') {
				const sendData = {
					clusterId,
					namespace,
					middlewareName,
					chartName,
					chartVersion,
					type,
					quota: {
						postgresql: {
							cpu: sendCpu,
							memory: sendMemory,
							num: quota['postgresql'].num,
							limitCpu: sendCpu,
							limitMemory: sendMemory + 'Gi'
						}
					}
				};
				updateMid(sendData, 'vertical');
				return;
			}
			if (type === 'elasticsearch') {
				const sendData = {
					clusterId,
					namespace,
					middlewareName,
					chartName,
					chartVersion,
					type,
					quota: {
						...quota,
						[scaleType.title]: {
							...scaleType,
							cpu: sendCpu,
							memory: sendMemory,
							limitCpu: sendCpu,
							limitMemory: sendMemory + 'Gi'
						}
					}
				};
				updateMid(sendData, 'vertical');
				return;
			}
			if (type === 'redis') {
				if (mode === 'cluster') {
					const sendData = {
						clusterId,
						namespace,
						middlewareName,
						chartName,
						chartVersion,
						type,
						quota: {
							redis: {
								cpu: sendCpu,
								memory: sendMemory,
								num: quota['redis'].num,
								limitCpu: sendCpu,
								limitMemory: sendMemory + 'Gi'
							}
						}
					};
					updateMid(sendData, 'vertical');
				} else {
					const sendData = {
						clusterId,
						namespace,
						middlewareName,
						chartName,
						chartVersion,
						type,
						quota: {
							...quota,
							[scaleType.title]: {
								...scaleType,
								cpu: sendCpu,
								memory: sendMemory,
								limitCpu: sendCpu,
								limitMemory: sendMemory + 'Gi'
							}
						}
					};
					updateMid(sendData, 'vertical');
				}
				return;
			}
			const sendData = {
				clusterId,
				namespace,
				middlewareName,
				chartName: chartName,
				chartVersion: chartVersion,
				type,
				quota: {
					variable: quota.variable,
					[type]: {
						cpu: sendCpu + '',
						memory: sendMemory + ''
					}
				}
			};
			updateMid(sendData, 'vertical');
			return;
		});
	};
	return (
		<Modal
			title={`修改规格(${middlewareName}${
				scaleType ? `:${scaleType.title}` : ''
			})`}
			open={open}
			onCancel={onCancel}
			onOk={onOk}
			width={700}
			footer={
				<>
					<Button onClick={onCancel}>取消</Button>
					<Button
						onClick={onOk}
						type="primary"
						disabled={instanceSpec === 'General' && !specId}
					>
						确定
					</Button>
				</>
			}
		>
			<Alert
				showIcon
				style={{ marginBottom: 24 }}
				type="warning"
				message="修改节点规格会在自动重启节点后生效，由此可能导致服务短暂中断，请谨慎操作。"
			/>
			<Form
				form={form}
				labelAlign="left"
				{...formItemLayout618}
				onValuesChange={onValuesChange}
			>
				<FormItem name="quota" label="节点规格">
					<SelectBlock
						options={instanceSpecList}
						currentValue={instanceSpec}
						onCallBack={(value: any) => setInstanceSpec(value)}
					/>
					{instanceSpec === 'General' && (
						<div
							style={{
								marginTop: 16
							}}
						>
							<TableRadio
								id={specId}
								onCallBack={onTableRadioChange}
								dataList={dataListRender()}
							/>
						</div>
					)}
					{instanceSpec === 'Customize' && (
						<div className="scale-form-custom-form">
							<FormItem
								label="CPU"
								name="cpu"
								labelAlign="left"
								{...formItemLayout618}
								rules={[
									{
										required: true,
										message:
											'请输入自定义CPU配额，单位为Core'
									},
									{
										type: 'number',
										min: 0.1,
										message: `最小为0.1`
									}
								]}
							>
								<InputNumber
									style={{ width: '100%' }}
									placeholder="请输入自定义CPU配额，单位为Core"
								/>
							</FormItem>
							<FormItem
								label="内存"
								labelAlign="left"
								name="memory"
								{...formItemLayout618}
								style={{ marginBottom: 0 }}
								rules={[
									{
										required: true,
										message:
											'请输入自定义内存配额，单位为Gi'
									},
									{
										type: 'number',
										min: 0.1,
										message: `最小为0.1`
									}
								]}
							>
								<InputNumber
									style={{ width: '100%' }}
									placeholder="请输入自定义内存配额，单位为Gi"
								/>
							</FormItem>
						</div>
					)}
				</FormItem>
				{(type === 'mysql' ||
					type === 'redis' ||
					type === 'postgresql' ||
					type === 'elasticsearch' ||
					type === 'kafka' ||
					type === 'zookeeper' ||
					type === 'rocketmq') && (
					<StockAssessment
						cpu={cpu}
						memory={memory}
						replicas={num}
						type={type}
						isUpdate={true}
						namespace={cpuMemory}
						onChange={onChange}
						quota={
							type === 'elasticsearch' ||
							(type === 'redis' && mode !== 'cluster')
								? scaleType
								: quota
						}
						mode={mode}
					/>
				)}
			</Form>
		</Modal>
	);
}

import React, { useEffect, useState } from 'react';
import { Alert, Form, InputNumber, Modal, notification, Select } from 'antd';
import { CpuMemoryItem } from '@/pages/OrganizationDetail/organization.detail';
import StockAssessment from '@/pages/ServiceCatalog/components/StockAssessment';
import { getNamespaceCpuMemory, getProjectNamespace } from '@/services/project';
import { formItemLayout618 } from '@/utils/const';
import transUnit from '@/utils/transUnit';
import storage from '@/utils/storage';
import { NamespaceItem } from '@/pages/ProjectDetail/projectDetail';
interface LateralExpansionProps {
	type: string;
	mode: string;
	quota: any;
	open: boolean;
	onCancel: () => void;
	updateMid: (value: any, actionType: string) => void;
	clusterId: string;
	namespace: string;
	chartVersion: string;
	chartName: string;
	middlewareName: string;
	scaleType: any;
	readWriteProxy: {
		enabled: boolean;
		quota?: null;
		replicas?: number | null;
	};
	groupCount: number | null; // * mq dledger模式下的组数
	groupReplicas: number | null; // * mq dledger模式下的副本数
}
const FormItem = Form.Item;
const Option = Select.Option;
// ! 横向扩容
// * 后端设计 https://alidocs.dingtalk.com/i/nodes/gwva2dxOW4KbmGm3TNAZNgGX8bkz3BRL
export default function LateralExpansion(
	props: LateralExpansionProps
): JSX.Element {
	const {
		type,
		mode,
		quota,
		open,
		readWriteProxy,
		onCancel,
		updateMid,
		clusterId,
		namespace,
		chartName,
		chartVersion,
		middlewareName,
		scaleType,
		groupCount, // * mq dledger模式下的组数
		groupReplicas // * mq dledger模式下的副本数
	} = props;
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const [form] = Form.useForm();
	const [cpu, setCpu] = useState<number>(0);
	const [memory, setMemory] = useState<number>(0);
	const [num, setNum] = useState<number>(0);
	const [cpuMemory, setCpuMemory] = useState<CpuMemoryItem>();
	const [namespaces, setNamespaces] = useState<NamespaceItem[]>([]);
	const [computeQuota, setComputeQuota] = useState<{
		cpu: number;
		memory: number;
	}>({ cpu: 0, memory: 0 });
	useEffect(() => {
		getNamespaceCpuMemory({
			clusterId,
			namespace
		}).then((res) => {
			if (res.success) {
				setCpuMemory(res.data);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
		getProjectNamespace({
			organId,
			projectId,
			clusterId,
			withQuota: false
		}).then((res) => {
			if (res.success) {
				setNamespaces(res.data);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	}, []);
	useEffect(() => {
		if (
			type === 'elasticsearch' ||
			(type === 'redis' && mode !== 'cluster')
		) {
			setCpu(Number(scaleType.cpu));
			setMemory(Number(transUnit.removeUnit(scaleType.memory, 'Gi')));
			setNum(scaleType.num);
			form.setFieldsValue({
				num: scaleType.num
			});
		} else {
			setCpu(Number(quota[type].cpu));
			setMemory(Number(transUnit.removeUnit(quota[type].memory, 'Gi')));
			if (type === 'rocketmq' && mode === 'dledger') {
				setNum(groupReplicas as number);
			} else {
				setNum(quota[type]['num']);
			}
			type === 'redis' &&
			mode === 'cluster' &&
			readWriteProxy.enabled &&
			scaleType.title === 'proxy'
				? form.setFieldsValue({
						num: quota.proxy.num
				  })
				: form.setFieldsValue({
						num: quota[type].num
				  });
		}
	}, []);
	const getNum = () => {
		if (type === 'mysql') {
			let replicasTemp = 0;
			if (mode === '1m-0s') replicasTemp = 1;
			else if (mode === '1m-1s') replicasTemp = 2;
			else if (mode === '1m-3s') replicasTemp = 4;
			else replicasTemp = num + 1;
			return replicasTemp;
		} else if (type === 'postgresql') {
			let replicasTemp = 0;
			if (mode === '1m-0s') replicasTemp = 1;
			else if (mode === '1m-1s') replicasTemp = 2;
			else if (mode === '1m-3s') replicasTemp = 4;
			else replicasTemp = num + 1;
			return replicasTemp;
		} else if (type === 'rocketmq') {
			let replicasTemp = 0;
			if (mode === '2m-noslave') replicasTemp = 2;
			else if (mode === '2m-2s') replicasTemp = 4;
			else if (mode === '3m-3s') replicasTemp = 6;
			else if (mode === 'dledger')
				replicasTemp = (groupCount as number) * num;
			return replicasTemp;
		} else {
			return num;
		}
	};
	const getCpu = () => {
		if (type === 'kafka') {
			let cpuTemp = Number(quota?.[type].cpu ?? 0) || 0;
			cpuTemp = cpuTemp * getNum() + 0.2 + 0.5;
			return cpuTemp;
		} else if (type === 'postgresql') {
			let cpuTemp = Number(quota?.[type].cpu ?? 0) || 0;
			cpuTemp = (cpuTemp + 0.1) * getNum();
			return cpuTemp;
		} else if (type === 'rocketmq') {
			let cpuTemp = Number(quota?.[type].cpu ?? 0) || 0;
			cpuTemp = cpuTemp * getNum() + 0.5 + 1 + 2;
			return cpuTemp;
		} else if (type === 'mysql') {
			let cpuTemp = Number(quota?.[type].cpu ?? 0) || 0;
			cpuTemp = (cpuTemp + 0.2 + 0.2) * getNum();
			return cpuTemp;
		} else if (type === 'redis') {
			if (mode === 'cluster') {
				let cpuTemp = Number(quota?.[type].cpu ?? 0) || 0;
				cpuTemp = (cpuTemp + 0.025) * getNum();
				return cpuTemp;
			} else {
				let cpuTemp = Number(scaleType.cpu ?? 0) || 0;
				cpuTemp = (cpuTemp + 0.025) * scaleType?.num;
				return cpuTemp;
			}
		} else if (type === 'elasticsearch') {
			let cpuTemp = Number(scaleType.cpu ?? 0) || 0;
			cpuTemp = cpuTemp * scaleType?.num + 0.1;
			return cpuTemp;
		} else {
			let cpuTemp = Number(quota?.[type].cpu ?? 0) || 0;
			cpuTemp = cpuTemp * getNum();
			return cpuTemp;
		}
	};
	const getMemory = () => {
		if (type === 'kafka') {
			let memoryTemp =
				Number(transUnit.removeUnit(quota?.[type].memory, 'Gi') ?? 0) ||
				0;
			memoryTemp = memoryTemp * getNum() + 0.5 + 0.5;
			return memoryTemp;
		} else if (type === 'postgresql') {
			let memoryTemp =
				Number(transUnit.removeUnit(quota?.[type].memory, 'Gi') ?? 0) ||
				0;
			memoryTemp = (memoryTemp + 0.125) * getNum();
			return memoryTemp;
		} else if (type === 'rocketmq') {
			let memoryTemp =
				Number(transUnit.removeUnit(quota?.[type].memory, 'Gi') ?? 0) ||
				0;
			memoryTemp = memoryTemp * getNum() + 1 + 2 + 2;
			return memoryTemp;
		} else if (type === 'mysql') {
			let memoryTemp =
				Number(transUnit.removeUnit(quota?.[type].memory, 'Gi') ?? 0) ||
				0;
			memoryTemp = (memoryTemp + 0.2 + 0.2) * getNum();
			return memoryTemp;
		} else if (type === 'redis') {
			if (mode === 'cluster') {
				let memoryTemp =
					Number(
						transUnit.removeUnit(quota?.[type].memory, 'Gi') ?? 0
					) || 0;
				memoryTemp = (memoryTemp + 0.025) * getNum();
				return memoryTemp;
			} else {
				let memoryTemp =
					Number(transUnit.removeUnit(scaleType.memory, 'Gi') ?? 0) ||
					0;
				memoryTemp = (memoryTemp + 0.05) * scaleType?.num;
				return memoryTemp;
			}
		} else if (type === 'elasticsearch') {
			let memoryTemp =
				Number(transUnit.removeUnit(scaleType.memory, 'Gi') ?? 0) || 0;
			memoryTemp = memoryTemp * scaleType?.num + 0.125;
			return memoryTemp;
		} else {
			let memoryTemp =
				Number(transUnit.removeUnit(quota?.[type].memory, 'Gi') ?? 0) ||
				0;
			memoryTemp = memoryTemp * getNum();
			return memoryTemp;
		}
	};
	const onValuesChange = (changeValue: any, allValue: any) => {
		const keys = Object.keys(changeValue);
		if (keys[0] === 'num') setNum(changeValue.num);
	};
	const onChange = (cpu: number, memory: number, storage: number) => {
		setComputeQuota({
			...computeQuota,
			cpu,
			memory
		});
	};
	const onOk = () => {
		const quotaCpu = getCpu();
		const quotaMemory = getMemory();
		form.validateFields().then((values) => {
			if (
				computeQuota.cpu >
				(cpuMemory?.cpu.request || 0) -
					(cpuMemory?.cpu.used || 0) +
					quotaCpu
			) {
				notification.error({
					message: '失败',
					description: '当前命名空间可分配CPU不足！'
				});
				return;
			}
			if (
				computeQuota.memory >
				(cpuMemory?.memory.request || 0) -
					(cpuMemory?.memory.used || 0) +
					quotaMemory
			) {
				notification.error({
					message: '失败',
					description: '当前命名空间可分配内存不足！'
				});
				return;
			}
			if (type === 'postgresql') {
				const sendData = {
					clusterId,
					namespace,
					middlewareName,
					chartName,
					chartVersion,
					type,
					quota: {
						postgresql: {
							cpu: quota['postgresql'].cpu,
							memory: quota['postgresql'].memory,
							num: values.num
						}
					}
				};
				updateMid(sendData, 'lateral');
				return;
			}
			if (type === 'elasticsearch') {
				const sendData = {
					clusterId,
					namespace,
					middlewareName,
					chartName,
					chartVersion,
					type,
					quota: {
						...quota,
						[scaleType.title]: {
							...scaleType,
							cpu: scaleType.cpu,
							memory: scaleType.memory,
							num: values.num,
							limitCpu: scaleType.cpu,
							limitMemory: scaleType.memory
						}
					}
				};
				updateMid(sendData, 'lateral');
				return;
			}
			if (type === 'redis') {
				if (mode === 'cluster') {
					if (!readWriteProxy.enabled) {
						const sendData = {
							clusterId,
							namespace,
							middlewareName,
							chartName,
							chartVersion,
							type,
							quota: {
								redis: {
									cpu: quota.redis.cpu,
									memory: quota.redis.memory,
									num: values.num
								}
							}
						};
						updateMid(sendData, 'lateral');
					} else {
						const sendData = {
							clusterId,
							namespace,
							middlewareName,
							chartName,
							chartVersion,
							type,
							quota: {
								redis: {
									cpu: quota.redis.cpu,
									memory: quota.redis.memory,
									num:
										scaleType.title === 'redis'
											? values.num
											: quota.redis.num
								},
								proxy: {
									cpu: quota.proxy.cpu,
									memory: quota.proxy.memory,
									num:
										scaleType.title === 'proxy'
											? values.num
											: quota.proxy.num
								}
							}
						};
						updateMid(sendData, 'lateral');
					}
				} else {
					const sendData = {
						clusterId,
						namespace,
						middlewareName,
						chartName,
						chartVersion,
						type,
						quota: {
							...quota,
							[scaleType.title]: {
								cpu: scaleType.cpu,
								memory: scaleType.memory,
								num: values.num
							}
						}
					};
					updateMid(sendData, 'lateral');
				}
				return;
			}
			const sendData = {
				clusterId,
				namespace,
				middlewareName,
				chartName,
				chartVersion,
				type,
				quota: {
					[type]: {
						cpu: quota[type].cpu,
						memory: quota[type].memory,
						num: values.num
					}
				}
			};
			updateMid(sendData, 'lateral');
			return;
		});
	};
	const numRender = () => {
		switch (type) {
			case 'redis':
				if (mode === 'cluster') {
					return !readWriteProxy.enabled ||
						scaleType.title === 'redis' ? (
						<FormItem
							name="num"
							label="分片数"
							rules={[
								({ getFieldValue }) => ({
									validator(_, value) {
										if (value < quota.redis.num) {
											return Promise.reject(
												new Error(
													'当前仅支持实例扩容！'
												)
											);
										} else {
											return Promise.resolve();
										}
									}
								})
							]}
						>
							<Select>
								<Option value={6}>三分片</Option>
								<Option value={10}>五分片</Option>
								<Option value={14}>七分片</Option>
								<Option value={18}>九分片</Option>
							</Select>
						</FormItem>
					) : (
						<FormItem
							name="num"
							label="节点数量"
							rules={[
								({ getFieldValue }) => ({
									validator(_, value) {
										if (value < quota.proxy.num) {
											return Promise.reject(
												new Error(
													'当前仅支持实例扩容！'
												)
											);
										} else {
											return Promise.resolve();
										}
									}
								}),
								{
									required: true,
									message: '请输入分片数'
								}
							]}
						>
							<InputNumber
								style={{ width: '100%' }}
								precision={0}
							/>
						</FormItem>
					);
				} else {
					return (
						<FormItem
							name="num"
							label="节点数量"
							rules={[
								{
									required: true,
									message: '请输入节点数量'
								},
								({ getFieldValue }) => ({
									validator(_, value) {
										if (
											value &&
											value < quota[scaleType.title].num
										) {
											return Promise.reject(
												new Error(
													'当前仅支持实例扩容！'
												)
											);
										} else {
											return Promise.resolve();
										}
									}
								}),
								({ getFieldValue }) => ({
									validator(_, value) {
										if (
											value !== 3 &&
											value !== 5 &&
											value !== 7
										) {
											return Promise.reject(
												new Error(
													`当前选择模式下${scaleType.title}节点数仅支持扩容到3、5、7`
												)
											);
										} else {
											return Promise.resolve();
										}
									}
								})
							]}
						>
							<InputNumber
								style={{ width: '100%' }}
								precision={0}
							/>
						</FormItem>
					);
				}
			case 'postgresql':
				return (
					<FormItem
						name="num"
						label="从节点数"
						required
						rules={[
							{
								min: quota.postgresql.num,
								type: 'number',
								message: '当前仅支持实例扩容！'
							},
							{
								required: true,
								message: '请输入节点数量'
							},
							({ getFieldValue }) => ({
								validator(_, value) {
									if (value > 3) {
										return Promise.reject(
											new Error(
												'Postgresql当前最多扩容至一主三从！'
											)
										);
									}
									const cur_namespace = namespaces.find(
										(item) => item.name === namespace
									);
									console.log(cur_namespace);
									if (cur_namespace?.availableDomain) {
										if (value !== 3) {
											return Promise.reject(
												new Error(
													'双活Postgresql当前只能扩容至一主三从！'
												)
											);
										} else {
											return Promise.resolve();
										}
									} else {
										return Promise.resolve();
									}
								}
							})
						]}
					>
						<InputNumber style={{ width: '100%' }} precision={0} />
					</FormItem>
				);
			case 'elasticsearch':
				if (scaleType.title === 'master') {
					return (
						<FormItem
							name="num"
							label="节点数量"
							rules={[
								{
									min: scaleType.num,
									message: '当前仅支持实例扩容！',
									type: 'number'
								},
								({ getFieldValue }) => ({
									validator(_, value) {
										if (
											value !== 3 &&
											value !== 5 &&
											value !== 7
										) {
											return Promise.reject(
												new Error(
													'当前选择模式下主节点数仅支持扩容到3、5、7'
												)
											);
										} else {
											return Promise.resolve();
										}
									}
								}),
								{
									required: true,
									message: '请输入节点数'
								}
							]}
						>
							<InputNumber
								style={{ width: '100%' }}
								precision={0}
							/>
						</FormItem>
					);
				} else {
					return (
						<FormItem
							name="num"
							label="节点数量"
							rules={[
								{
									min: scaleType.num,
									message: '当前仅支持实例扩容！',
									type: 'number'
								},
								{
									required: true,
									message: '请输入分片数'
								}
							]}
						>
							<InputNumber
								style={{ width: '100%' }}
								precision={0}
							/>
						</FormItem>
					);
				}
			case 'kafka':
				return (
					<FormItem
						name="num"
						label="集群实例数量"
						rules={[
							{
								min: quota.kafka.num,
								type: 'number',
								message: '当前仅支持实例扩容！'
							},
							{
								required: true,
								message: '请输入集群实例数量'
							}
						]}
					>
						<InputNumber
							placeholder="请输入集群实例数量"
							max={10}
							style={{ width: '100%' }}
							precision={0}
						/>
					</FormItem>
				);
			case 'zookeeper':
				return (
					<FormItem
						name="num"
						label="节点数"
						rules={[
							{
								min: quota.zookeeper.num,
								type: 'number',
								message: '当前仅支持实例扩容！'
							},
							{
								required: true,
								message: '请输入节点数'
							}
						]}
					>
						<InputNumber
							placeholder="请输入节点数"
							max={10}
							style={{ width: '100%' }}
							precision={0}
						/>
					</FormItem>
				);
			default:
				return <></>;
		}
	};
	return (
		<Modal
			open={open}
			title={`横向扩容(${middlewareName}${
				scaleType ? `:${scaleType.title}` : ''
			})`}
			onCancel={onCancel}
			onOk={onOk}
		>
			<Form
				{...formItemLayout618}
				labelAlign="left"
				form={form}
				onValuesChange={onValuesChange}
			>
				{type === 'redis' && scaleType.title !== 'sentinel' && (
					<Alert
						showIcon
						type="warning"
						style={{ marginBottom: 24 }}
						message="横向扩容会短暂影响Redis服务的性能，请在合适的时间谨慎进行操作。"
					/>
				)}
				{numRender()}
				<StockAssessment
					cpu={cpu}
					memory={memory}
					replicas={num}
					type={type}
					isUpdate={true}
					isLateral={true}
					namespace={cpuMemory}
					onChange={onChange}
					quota={
						type === 'elasticsearch' ||
						(type === 'redis' && mode !== 'cluster')
							? scaleType
							: quota
					}
					mode={mode}
				/>
			</Form>
		</Modal>
	);
}

import React, { useEffect, useState } from 'react';
import { Alert, notification, Modal } from 'antd';
import { useHistory, useParams } from 'react-router';
import Actions from '@/components/Actions';
import ProTable from '@/components/ProTable';
import { middlewareProps } from '@/pages/ServiceList/service.list';
import {
	getVersions,
	upgradeChart,
	upgradeCheck
} from '@/services/serviceList';
import { DetailParams, ServiceVersionProps } from '../detail';
import { serviceVersionStatus } from '@/utils/enum';
import useRefresh from '@/utils/useRefresh';
import storage from '@/utils/storage';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';
import { controlledOperationDisabled } from '@/utils/utils';
const LinkButton = Actions.LinkButton;
const { confirm } = Modal;
export default function ServiceVersion(
	props: ServiceVersionProps
): JSX.Element {
	const { serviceRefresh, lock } = props;
	const history = useHistory();
	const role = JSON.parse(storage.getLocal('role'));
	const {
		clusterId,
		namespace,
		middlewareName,
		name,
		type,
		aliasName
	}: DetailParams = useParams();
	const [versions, setVersions] = useState<middlewareProps[]>([]);
	const [loading, setLoading] = useState<boolean>(false);
	const [btnLoading, setBtnLoading] = useState<boolean>(false);
	const serviceVersionOperatorId = maintenances['Service Version Management'];
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		getData();
	}, [refreshKey]);
	const getData = () => {
		setLoading(true);
		setVersions([]);
		getVersions({
			clusterId,
			middlewareName,
			namespace,
			type: name
		})
			.then((res) => {
				if (res.success) {
					setVersions(res.data);
				} else {
					notification.error({
						message: '失败',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const installUpdate = (index: number, record: middlewareProps) => {
		setBtnLoading(true);
		!btnLoading &&
			upgradeCheck({
				clusterId,
				namespace,
				middlewareName,
				type: name,
				chartName: record.chartName,
				upgradeChartVersion: record.chartVersion
			}).then((res) => {
				setBtnLoading(false);
				if (res.success) {
					confirm({
						title: '操作确认',
						content: '是否确认升级新版本？',
						onOk: async () => {
							await ExecuteOrderFuc();
							return upgradeChart({
								clusterId,
								namespace,
								middlewareName,
								type: name,
								chartName: record.chartName,
								upgradeChartVersion: record.chartVersion
							}).then((res) => {
								if (res.success) {
									getData();
									// * 整个服务刷新
									history.push(
										`/project/${type}/${name}/${aliasName}/container/operatorAbility/${middlewareName}/${record.chartVersion}/${clusterId}/${namespace}`
									);
									serviceRefresh();
								} else {
									notification.error({
										message: '失败',
										description: res.errorMsg
									});
								}
							});
						}
					});
				} else if (res.code === 720004) {
					if (role.isAdmin) {
						confirm({
							title: '操作确认',
							content:
								'经系统检测，该版本的中间件还未安装，请到中间件市场进行升级安装',
							okText: '我知道了',
							cancelText: '立即升级',
							onCancel() {
								history.push(
									`/platform/marketManagement/versionManagement/${name}/${clusterId}`
								);
							}
						});
					} else {
						confirm({
							title: '操作确认',
							content:
								'经系统检测，该版本的中间件还未安装，请联系管理员进行升级安装',
							okText: '我知道了'
						});
					}
				} else if (res.code === 720003) {
					confirm({
						title: '操作确认',
						content: 'operator升级中,请稍后升级'
					});
				} else if (res.code === 720002) {
					confirm({
						title: '操作确认',
						content: res.errorDetail
					});
				} else {
					confirm({
						title: '操作确认',
						content: res.errorDetail
					});
				}
			});
	};
	const versionStatusRender = (value: string) => {
		const color =
			value === 'now'
				? '#00A7FA'
				: value === 'future' || value === 'updating'
				? '#52C41A'
				: '#666666';
		const bgColor =
			value === 'now'
				? '#EBF8FF'
				: value === 'future' || value === 'updating'
				? '#F6FFED'
				: '#F5F5F5';
		return (
			<div
				className="version-status-display"
				style={{
					color: color,
					backgroundColor: bgColor,
					borderColor: color
				}}
			>
				{serviceVersionStatus[value]}
			</div>
		);
	};
	const actionRender = (
		value: string,
		record: middlewareProps,
		index: number
	) => {
		return (
			<Actions>
				<LinkButton
					disabled={
						record.versionStatus !== 'future' ||
						controlledOperationDisabled('maintenance', lock)
					}
					onClick={() => {
						WorkOrderFuc(
							() => {
								installUpdate(index, record);
							},
							lock,
							middlewareName,
							serviceVersionOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
				>
					升级
				</LinkButton>
				{record.versionStatus === 'updating' && (
					<LinkButton disabled>升级中</LinkButton>
				)}
			</Actions>
		);
	};
	return (
		<>
			<Alert
				type="info"
				showIcon={true}
				message="本系统范围内其它集群使用过的中间件版本，都可以自主选择是否安装升级到更新版本"
				style={{ marginBottom: 8 }}
			/>
			<ProTable dataSource={versions} loading={loading} rowKey="id">
				<ProTable.Column
					title="服务名称/中文名称"
					dataIndex="middle"
					render={() => middlewareName}
				/>
				<ProTable.Column
					title="版本号"
					dataIndex="chartVersion"
					width={100}
				/>
				<ProTable.Column
					title="版本状态"
					dataIndex="versionStatus"
					render={versionStatusRender}
					filters={[
						{ text: '当前版本', value: 'now' },
						{ text: '可升级版本', value: 'future' },
						{ text: '历史版本', value: 'history' },
						{ text: '升级中', value: 'updating' }
					]}
					filterMultiple={false}
					onFilter={(value: any, record: middlewareProps) =>
						record.versionStatus === value
					}
					width={150}
				/>
				<ProTable.Column
					title="包含中间件版本"
					dataIndex="version"
					ellipsis={true}
				/>
				<ProTable.Column
					title="操作"
					dataIndex="action"
					width={100}
					render={actionRender}
				/>
			</ProTable>
		</>
	);
}

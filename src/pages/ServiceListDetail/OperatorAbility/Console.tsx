import React, { useEffect, useState } from 'react';
import {
	Col,
	Row,
	Form,
	Select,
	notification,
	Button,
	Cascader,
	Spin
} from 'antd';
import MidTerminal from '@/components/MidTerminal';
import { getPods } from '@/services/middleware';
import { filtersProps } from '@/types/comment';
import { formItemLayout618, maintenances } from '@/utils/const';
import { ContainerItem, DetailParams, PodItem, PodSendData } from '../detail';
import { useHistory, useParams } from 'react-router';
import useRefresh from '@/utils/useRefresh';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { controlledOperationDisabled } from '@/utils/utils';

const FormItem = Form.Item;
const Option = Select.Option;
export default function Console({ lock }: { lock: string }): JSX.Element {
	const {
		clusterId,
		namespace,
		middlewareName,
		name,
		type,
		aliasName
	}: DetailParams = useParams();
	const history = useHistory();
	const [optionValue, setOptionValue] = useState<string[]>();
	const [scriptType, setScriptType] = useState<string>('bash');
	const [options, setOptions] = useState<filtersProps[]>([]);
	const [open, setOpen] = useState<boolean>(false);
	const openConsoleOperatorId = maintenances['Enter Console'];
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		getPodList({
			clusterId,
			namespace,
			middlewareName,
			type: name
		});
	}, [refreshKey]);
	// * 获取pod列表
	const getPodList = (sendData: PodSendData) => {
		setOpen(false);
		getPods(sendData).then((res) => {
			if (res.success) {
				const ot = res.data.pods.map((item: PodItem) => {
					const child = item.containers.map((i: ContainerItem) => {
						return {
							value: i.name,
							label: i.name
						};
					});
					return {
						value: item.podName,
						label: item.podName,
						children: child
					};
				});
				setOptionValue([ot[0]?.value, ot[0]?.children[0]?.value]);
				setOptions(ot);
			} else {
				notification.error({
					message: '失败',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	const onChange = (value: any) => {
		setOpen(false);
		setOptionValue(value);
	};
	const handleSubmit = async () => {
		await ExecuteOrderFuc();
		setOpen(true);
	};
	return (
		<>
			<Row>
				<Col span={10}>
					<FormItem
						labelAlign="left"
						{...formItemLayout618}
						label="实例/容器"
					>
						<Cascader
							value={optionValue}
							options={options}
							onChange={onChange}
							placeholder="请选择实例和容器名称"
						/>
					</FormItem>
				</Col>
				<Col span={10} offset={1}>
					<FormItem
						label="shell类型"
						labelAlign="left"
						{...formItemLayout618}
					>
						<Select
							placeholder="请选择shell类型"
							value={scriptType}
							onChange={(value: string) => {
								setOpen(false);
								setScriptType(value);
							}}
						>
							<Option value="sh">sh</Option>
							<Option value="bash">bash</Option>
						</Select>
					</FormItem>
				</Col>
				<Col span={2} offset={1}>
					<Button
						type="primary"
						disabled={controlledOperationDisabled('expert', lock)}
						onClick={() => {
							WorkOrderFuc(
								handleSubmit,
								lock,
								middlewareName,
								openConsoleOperatorId,
								history,
								type,
								name,
								aliasName,
								clusterId,
								namespace
							);
						}}
						loading={!optionValue}
					>
						确定
					</Button>
				</Col>
			</Row>
			{!optionValue && (
				<div
					style={{
						width: '100%',
						height: '300px',
						lineHeight: '500px',
						textAlign: 'center'
					}}
				>
					<Spin />
				</div>
			)}
			{optionValue && (
				<div>
					<MidTerminal
						middlewareName={middlewareName}
						middlewareType={name}
						scriptType={scriptType}
						pod={optionValue[0]}
						container={optionValue[1]}
						namespace={namespace}
						clusterId={clusterId}
						open={open}
					/>
				</div>
			)}
		</>
	);
}

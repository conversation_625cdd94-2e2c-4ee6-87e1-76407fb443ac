import React, { useEffect, useState } from 'react';
import { Drawer, Modal, Table, notification } from 'antd';
import { userProps } from '@/pages/UserManage/user';
import {
	getMiddlewareAccountBindUsers,
	unBindMiddlewareAccount
} from '@/services/middleware';
import Actions from '@/components/Actions';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';
import { DetailParams } from '../../detail';
import { useHistory, useParams } from 'react-router';
import { controlledOperationDisabled } from '@/utils/utils';
const { LinkButton } = Actions;
export default function BindUserDetail({
	open,
	onCancel,
	currentAccount,
	deployMode,
	lock
}: {
	open: boolean;
	onCancel: () => void;
	currentAccount: MiddlewareAccount;
	deployMode?: string;
	lock: string;
}): JSX.Element {
	const {
		clusterId,
		name,
		aliasName,
		type,
		middlewareName,
		namespace
	}: DetailParams = useParams();
	const history = useHistory();
	const middlewareAccountBindUsersAccountOperatorId =
		maintenances['Account Management Bind Users'];
	const [users, setUsers] = useState<userProps[]>([]);
	const [loading, setLoading] = useState<boolean>(false);
	const [pageSize, setPageSize] = useState<number>(10);
	const [pageCurrent, setPageCurrent] = useState<number>(1);
	const [total, setTotal] = useState<number>();
	useEffect(() => {
		getData(pageCurrent, pageSize);
	}, []);
	const getData = (current: number, size: number) => {
		setLoading(true);
		getMiddlewareAccountBindUsers({
			clusterId,
			middlewareName,
			namespace,
			type: name,
			deployMode: deployMode ? deployMode : 'container',
			accountName: currentAccount?.account,
			size: size,
			current: current
		})
			.then((res) => {
				if (res.success) {
					setPageCurrent(res.data.pageNum);
					setPageSize(res.data.pageSize);
					setTotal(res.data.total);
					setUsers(res.data.list);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const unBindUser = (user_info: userProps) => {
		Modal.confirm({
			title: '操作确认',
			content: (
				<>
					<p>解绑用户后，该用户无法直接通过该账户登录控制台</p>
					<p>是否确定进行解绑操作？</p>
				</>
			),
			onOk: async () => {
				await ExecuteOrderFuc();
				return unBindMiddlewareAccount({
					userId: user_info.id + '',
					accountName: currentAccount.account,
					clusterId,
					middlewareName,
					namespace,
					type: name,
					deployMode: deployMode ? deployMode : 'container'
				}).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '用户解绑成功'
						});
						getData(pageCurrent, pageSize);
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};
	const actionRender = (_: any, record: userProps) => {
		return (
			<Actions>
				<LinkButton
					disabled={
						controlledOperationDisabled('expert', lock) ||
						currentAccount.state === 'accountNotFound'
					}
					onClick={() => {
						WorkOrderFuc(
							() => {
								unBindUser(record);
							},
							lock,
							middlewareName,
							middlewareAccountBindUsersAccountOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
				>
					解绑
				</LinkButton>
			</Actions>
		);
	};
	const onChange = (page: number, pageSize: number) => {
		setPageCurrent(page);
		setPageSize(pageSize);
		getData(page, pageSize);
	};
	return (
		<Drawer
			title="查看绑定平台用户"
			placement="right"
			onClose={onCancel}
			width={600}
			open={open}
		>
			<Table
				dataSource={users}
				rowKey="userName"
				loading={loading}
				pagination={{
					total: total,
					current: pageCurrent,
					pageSize: pageSize,
					onChange: onChange
				}}
			>
				<Table.Column
					dataIndex="userName"
					title="登录账户"
					ellipsis={true}
					render={(value: any) => value || '/'}
				/>
				<Table.Column
					dataIndex="aliasName"
					title="用户名"
					ellipsis={true}
					render={(value: any) => value || '/'}
				/>
				<Table.Column
					dataIndex="action"
					title="操作"
					render={actionRender}
				/>
			</Table>
		</Drawer>
	);
}

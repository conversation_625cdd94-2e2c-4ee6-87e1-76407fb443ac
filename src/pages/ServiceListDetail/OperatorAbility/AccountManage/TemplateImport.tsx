import React, { useEffect, useState } from 'react';
import { Alert, Button, Col, Modal, Row, Upload, notification } from 'antd';
import { api } from '@/api.json';
import storage from '@/utils/storage';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';
import { getUploadProcess } from '@/services/middleware';
import { TOKEN } from '@/services/request';

export default function TemplateImport({
	open,
	onCancel,
	onCreate,
	clusterId,
	type,
	namespace,
	middlewareName,
	deployMode
}: {
	open: boolean;
	onCancel: () => void;
	onCreate: () => void;
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	deployMode?: string;
}): JSX.Element {
	const [fileList, setFileList] = useState<any[]>([]);
	const [okDisabled, setOkDisabled] = useState<boolean>(true);
	const [okLoading, setOkLoading] = useState<boolean>(false);
	const [allowReUpload, setAllowReUpload] = useState<boolean>(false);
	const headers = {
		userToken: storage.getLocal(TOKEN),
		authType: storage.getLocal(TOKEN) ? '1' : '0'
	};
	useEffect(() => {
		getData();
	}, []);
	const getData = () => {
		getUploadProcess({
			clusterId,
			namespace,
			middlewareName,
			type,
			deployMode: deployMode ? deployMode : 'container'
		}).then((res) => {
			if (res.success) {
				if (res.data.state == 'uploading') {
					const file_temp = {
						name: res.data.fileName,
						status: 'uploading'
					};
					setFileList([file_temp]);
				}
				if (res.data.canReUpdate) {
					setAllowReUpload(true);
				} else {
					setAllowReUpload(false);
				}
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	const onOk = async () => {
		const formData = new FormData();
		fileList.forEach((file) => {
			formData.append('file', file);
		});
		setOkLoading(true);
		await ExecuteOrderFuc();
		fetch(
			`${api}/clusters/${clusterId}/namespaces/${namespace}/middlewares/${middlewareName}/accounts/formUpdate?type=${type}&deployMode=${
				deployMode ? deployMode : 'container'
			}`,
			{
				method: 'POST',
				body: formData,
				headers
			}
		).then((res) => {
			res.json()
				.then((result) => {
					if (result.success) {
						notification.success({
							message: '成功',
							description: '账户修改成功'
						});
						onCreate();
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{result.errorMsg}</p>
									<p>{result.errorDetail}</p>
								</>
							)
						});
					}
				})
				.finally(() => {
					setOkLoading(false);
				});
		});
	};
	const beforeUpload = (file: any) => {
		setFileList([file]);
		setOkDisabled(false);
		return false;
	};
	const onRemove = () => {
		if (fileList[0].status === 'uploading') {
			setOkDisabled(true);
			return;
		}
		setFileList([]);
		setOkDisabled(true);
	};
	const reUpload = () => {
		setFileList([]);
		setAllowReUpload(false);
	};
	return (
		<Modal
			title="上传文件"
			open={open}
			onCancel={onCancel}
			onOk={onOk}
			okButtonProps={{ loading: okLoading, disabled: okDisabled }}
		>
			<Alert
				type="info"
				closable
				showIcon
				message={
					<span>
						通过模板批量管理密码，请使用
						<span
							className="name-link"
							onClick={() => {
								window.open(
									encodeURI(
										`${window.location.origin}/api/template/middleware-account-template.xlsx`
									),
									'_blank',
									'noopener,noreferrer'
								);
							}}
						>
							《批量账户密码管理模板》
						</span>
					</span>
				}
			/>
			<Row className="mt-16" align="middle">
				<Col span={6}>上传文件选择:</Col>
				<Col span={18}>
					<Upload
						accept=".xlsx"
						beforeUpload={beforeUpload}
						onRemove={onRemove}
						fileList={fileList}
						maxCount={1}
						disabled={fileList?.[0]?.status === 'uploading'}
					>
						{!fileList.length ? (
							<Button type="primary">选择文件</Button>
						) : null}
					</Upload>
				</Col>
			</Row>
			{allowReUpload && (
				<Button
					className="mt-8"
					type="primary"
					key="re-upload"
					onClick={reUpload}
				>
					重新上传
				</Button>
			)}
		</Modal>
	);
}

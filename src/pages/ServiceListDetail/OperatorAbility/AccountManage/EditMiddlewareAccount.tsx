import React, { useEffect, useState } from 'react';
import { Form, Input, Modal, notification } from 'antd';
import { updateMiddlewareAccountPassword } from '@/services/middleware';
import { accountRuleRender, encrypt } from '@/utils/utils';
import { PUBLIC_KEY, formItemLayout618 } from '@/utils/const';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';

export default function EditMiddlewareAccount({
	open,
	onCancel,
	onCreate,
	clusterId,
	namespace,
	middlewareName,
	middlewareAccount,
	type,
	deployMode
}: {
	open: boolean;
	onCancel: () => void;
	onCreate: () => void;
	clusterId: string;
	namespace: string;
	middlewareName: string;
	middlewareAccount: MiddlewareAccount;
	type: string;
	deployMode?: string;
}): JSX.Element {
	const [form] = Form.useForm();
	const passwordForm = Form.useWatch('password', form);
	const confirmPasswordForm = Form.useWatch('confirmPassword', form);
	const [loading, setLoading] = useState<boolean>(false);
	useEffect(() => {
		if (confirmPasswordForm) {
			form.validateFields(['confirmPassword']);
		}
	}, [passwordForm]);
	const onOk = async () => {
		await form.validateFields();
		const values = form.getFieldsValue();
		const oldPassword_encrypt = values.oldPassword
			? encrypt(values.oldPassword, PUBLIC_KEY)
			: '';
		const password_encrypt = values.password
			? encrypt(values.password, PUBLIC_KEY)
			: '';
		const confirmPassword_encrypt = values.confirmPassword
			? encrypt(values.confirmPassword, PUBLIC_KEY)
			: '';
		if (
			typeof oldPassword_encrypt !== 'string' ||
			typeof password_encrypt !== 'string' ||
			typeof confirmPassword_encrypt !== 'string'
		) {
			notification.warning({
				message: '提示',
				description: '密码加密失败'
			});
			return;
		}
		setLoading(true);
		await ExecuteOrderFuc();
		await updateMiddlewareAccountPassword({
			clusterId,
			namespace,
			middlewareName,
			accountName: middlewareAccount.account,
			type,
			deployMode: deployMode ? deployMode : 'container',
			oldPassword: oldPassword_encrypt,
			password: password_encrypt,
			confirmPassword: confirmPassword_encrypt
		})
			.then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '账户密码修改成功'
					});
					onCreate();
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const checkConfirmPassword = (_: any, value: any) => {
		if (value) {
			if (value !== form.getFieldValue('password')) {
				return Promise.reject(new Error('两次输入的密码信息不一致'));
			}
		}
		return Promise.resolve();
	};
	return (
		<Modal
			open={open}
			onCancel={onCancel}
			destroyOnClose={true}
			title="修改密码"
			width={500}
			onOk={onOk}
			okButtonProps={{ loading: loading }}
		>
			<Form
				form={form}
				{...formItemLayout618}
				labelAlign="left"
				colon={false}
				requiredMark="optional"
			>
				<Form.Item
					name="oldPassword"
					label="原密码"
					rules={[
						{
							required: true,
							message: '原密码不能为空'
						}
					]}
				>
					<Input.Password placeholder="请输入密码" />
				</Form.Item>
				<Form.Item
					name="password"
					label="新密码"
					rules={[
						{
							required: true,
							message: '新密码不能为空'
						},
						{
							type: 'string',
							min: accountRuleRender(type, 'min') as number,
							message: accountRuleRender(
								type,
								'minMaxMessage'
							) as string
						}
					]}
				>
					<Input.Password placeholder="请输入密码" />
				</Form.Item>
				<Form.Item
					name="confirmPassword"
					label="再次输入"
					rules={[
						{
							required: true,
							message: '两次输入的密码信息不一致'
						},
						{ validator: checkConfirmPassword }
					]}
				>
					<Input.Password placeholder="请输入密码" />
				</Form.Item>
			</Form>
		</Modal>
	);
}

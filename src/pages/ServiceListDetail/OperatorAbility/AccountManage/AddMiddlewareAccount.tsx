import React, { useState } from 'react';
import { Form, Input, Modal, notification } from 'antd';
import { PUBLIC_KEY, formItemLayout618 } from '@/utils/const';
import { createMiddlewareAccount } from '@/services/middleware';
import { accountRuleRender, encrypt } from '@/utils/utils';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';

export default function AddMiddlewareAccount({
	open,
	onCancel,
	onCreate,
	clusterId,
	namespace,
	middlewareName,
	type,
	deployMode
}: {
	open: boolean;
	onCancel: () => void;
	onCreate: () => void;
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	deployMode?: string;
}): JSX.Element {
	const [form] = Form.useForm();
	const [loading, setLoading] = useState<boolean>(false);
	const onOk = async () => {
		await form.validateFields();
		const values = form.getFieldsValue();
		const password_encrypt = values.password
			? encrypt(values.password, PUBLIC_KEY)
			: '';
		if (typeof password_encrypt !== 'string') {
			notification.warning({
				message: '提示',
				description: '密码加密失败'
			});
			return;
		}
		setLoading(true);
		await ExecuteOrderFuc();
		await createMiddlewareAccount({
			clusterId,
			namespace,
			middlewareName,
			type,
			deployMode: deployMode ? deployMode : 'container',
			account: values.account,
			password: password_encrypt
		})
			.then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '账户创建成功'
					});
					onCreate();
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	return (
		<Modal
			title="新增账户"
			width={500}
			onCancel={onCancel}
			destroyOnClose={true}
			open={open}
			onOk={onOk}
			okButtonProps={{ loading: loading }}
		>
			<Form
				{...formItemLayout618}
				form={form}
				labelAlign="left"
				colon={false}
				requiredMark="optional"
			>
				<Form.Item
					name="account"
					label="账户名"
					rules={[
						{ required: true, message: '账户名不能为空' },
						{
							pattern: accountRuleRender(
								type,
								'account_pattern'
							) as RegExp,
							message: accountRuleRender(
								type,
								'account_pattern_message'
							) as string
						}
					]}
				>
					<Input placeholder="请输入账户名" />
				</Form.Item>
				<Form.Item
					name="password"
					label="密码"
					rules={[
						{
							required: true,
							message: '密码不能为空'
						},
						{
							type: 'string',
							min: accountRuleRender(type, 'min') as number,
							message: accountRuleRender(
								type,
								'min_max_message'
							) as string
						}
					]}
				>
					<Input.Password placeholder="请输入密码" />
				</Form.Item>
			</Form>
		</Modal>
	);
}

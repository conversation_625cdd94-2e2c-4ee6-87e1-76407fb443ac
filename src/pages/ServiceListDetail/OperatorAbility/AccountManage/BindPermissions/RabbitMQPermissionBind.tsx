import React, { useEffect, useState } from 'react';
import {
	Modal,
	Form,
	Select,
	notification,
	Row,
	Col,
	Space,
	Tag,
	Checkbox,
	Input,
	Button
} from 'antd';
import {
	empowerMiddlewareAccounts,
	getPermissionInfo
} from '@/services/middleware';
import { AutoCompleteOptionItem } from '@/types/comment';
import { formItemLayout618 } from '@/utils/const';
import { PlusOutlined } from '@ant-design/icons';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { ValidateStatus } from 'antd/es/form/FormItem';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';
import pattern from '@/utils/pattern';

export default function RabbitMQPermissionBind({
	open,
	onCancel,
	onCreate,
	clusterId,
	namespace,
	middlewareName,
	type,
	deployMode,
	selectedAccounts
}: {
	open: boolean;
	onCancel: () => void;
	onCreate: () => void;
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	deployMode?: string;
	selectedAccounts: MiddlewareAccount[];
}): JSX.Element {
	const [form] = Form.useForm();
	const vHostListForm = Form.useWatch('vHostList', form);
	const configureForm = Form.useWatch('configure', form);
	const readForm = Form.useWatch('read', form);
	const writeForm = Form.useWatch('write', form);
	const [vHostOptions, setVHostOptions] = useState<AutoCompleteOptionItem[]>(
		[]
	);
	const [addedVHosts, setAddedVHosts] = useState<string[]>([]);
	const [configureRequired, setConfigureRequired] = useState<boolean>(false);
	const [writeRequired, setWriteRequired] = useState<boolean>(false);
	const [readRequired, setReadRequired] = useState<boolean>(false);
	const [validateStatus, setValidateStatus] = useState<ValidateStatus>();
	const [help, setHelp] = useState<string>();
	const [loading, setLoading] = useState<boolean>(false);
	const [disabled, setDisabled] = useState<boolean>(false);
	useEffect(() => {
		getInfo('rabbitmq_vhost');
	}, []);
	useEffect(() => {
		if (configureForm) {
			setConfigureRequired(true);
		} else {
			setConfigureRequired(false);
		}
		if (readForm) {
			setReadRequired(true);
		} else {
			setReadRequired(false);
		}
		if (writeForm) {
			setWriteRequired(true);
		} else {
			setWriteRequired(false);
		}
		if (configureForm || readForm || writeForm) {
			if (addedVHosts.length > 0) {
				setDisabled(false);
			} else {
				setDisabled(true);
			}
		} else {
			setDisabled(true);
		}
	}, [configureForm, readForm, writeForm]);
	const getInfo = (info_type: string) => {
		getPermissionInfo({
			clusterId,
			namespace,
			middlewareName,
			deployMode: deployMode ? deployMode : 'container',
			type,
			infoType: info_type
		}).then((res) => {
			if (res.success) {
				const permissions = JSON.parse(res.data.permissions);
				const vHost_options = permissions?.vhostList?.map(
					(item: string) => {
						return {
							label: item,
							value: item
						};
					}
				);
				setVHostOptions(vHost_options);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	const onAdd = async () => {
		if (!vHostListForm) {
			setValidateStatus('error');
			setHelp('VHost不能为空');
			return;
		} else {
			setValidateStatus(undefined);
			setHelp(undefined);
		}
		const added_vHosts_temp = Array.from(
			new Set(addedVHosts.concat([vHostListForm]))
		);
		setAddedVHosts(added_vHosts_temp);
		if (added_vHosts_temp.length > 0) {
			if (configureForm || readForm || writeForm) {
				setDisabled(false);
			} else {
				setDisabled(true);
			}
		} else {
			setDisabled(true);
		}
	};
	const onClose = (Vhost_temp: string) => {
		const added_VHosts_temp = addedVHosts.filter(
			(item) => item !== Vhost_temp
		);
		if (added_VHosts_temp.length === 0) {
			setValidateStatus('error');
			setHelp('VHost不能为空');
			setDisabled(true);
		}
		setAddedVHosts(added_VHosts_temp);
	};
	const onChange = (e: CheckboxChangeEvent, type: string) => {
		switch (type) {
			case 'configure':
				setConfigureRequired(e.target.checked);
				if (!e.target.checked) {
					form.setFieldValue('configure', undefined);
				}
				break;
			case 'write':
				setWriteRequired(e.target.checked);
				if (!e.target.checked) {
					form.setFieldValue('write', undefined);
				}
				break;
			case 'read':
				setReadRequired(e.target.checked);
				if (!e.target.checked) {
					form.setFieldValue('read', undefined);
				}
				break;
			default:
				break;
		}
		form.validateFields([type]);
	};
	const onOk = async () => {
		if (addedVHosts.length === 0) {
			setValidateStatus('error');
			setHelp('VHost不能为空');
			await form.validateFields();
			return;
		}
		await form.validateFields();
		const values = form.getFieldsValue();
		const sendData = {
			vHostList: addedVHosts,
			configure: values.configure,
			write: values.write,
			read: values.read
		};
		const account_temp = selectedAccounts.map((item) => item.account);
		const permissions_temp = JSON.stringify(sendData);
		setLoading(true);
		await ExecuteOrderFuc();
		await empowerMiddlewareAccounts({
			account: account_temp,
			permissions: permissions_temp,
			clusterId,
			middlewareName,
			namespace,
			type,
			deployMode: deployMode ? deployMode : 'container'
		})
			.then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '权限绑定成功'
					});
					onCreate();
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const checkPattern = (_: any, value: any) => {
		try {
			new RegExp(value);
			return Promise.resolve();
		} catch (e) {
			return Promise.reject(
				new Error('请输入非中文的资源名称或正则表达式')
			);
		}
	};
	return (
		<Modal
			title="授权账户"
			open={open}
			onCancel={onCancel}
			onOk={onOk}
			okButtonProps={{ loading: loading, disabled: disabled }}
		>
			<Form
				form={form}
				{...formItemLayout618}
				colon={false}
				labelAlign="left"
				requiredMark="optional"
			>
				<Form.Item
					label="选择Vhost"
					required
					validateStatus={validateStatus}
					help={help}
				>
					<Input.Group compact>
						<Form.Item noStyle name="vHostList">
							<Select
								style={{ width: '90%' }}
								showSearch
								options={vHostOptions}
								placeholder="请选择Vhost"
								onChange={() => {
									setValidateStatus(undefined);
									setHelp(undefined);
								}}
							/>
						</Form.Item>
						<Button
							icon={<PlusOutlined />}
							style={{ marginLeft: 4 }}
							onClick={onAdd}
						/>
					</Input.Group>
				</Form.Item>
				{addedVHosts?.length > 0 && (
					<Row>
						<Col offset={6} style={{ width: '100%' }}>
							<div className="redis-tag-box">
								<Space wrap>
									{addedVHosts.map((item) => {
										return (
											<Tag
												key={item}
												closable
												onClose={() => onClose(item)}
											>
												{item}
											</Tag>
										);
									})}
								</Space>
							</div>
						</Col>
					</Row>
				)}
				<Form.Item label="权限" required style={{ marginBottom: 0 }}>
					<Form.Item
						{...formItemLayout618}
						required
						label={
							<Checkbox
								checked={configureRequired}
								onChange={(e) => onChange(e, 'configure')}
							>
								配置权限
							</Checkbox>
						}
					>
						<Form.Item
							noStyle
							name="configure"
							rules={[
								{
									required: configureRequired,
									message: '资源名称不能为空'
								},
								{
									pattern: new RegExp(
										pattern.notSupportChinese
									),
									message:
										'请输入非中文的资源名称或正则表达式'
								},
								{ validator: checkPattern }
							]}
						>
							<Input
								style={{ width: '240px' }}
								placeholder="请输入资源名称，支持正则表达输入"
							/>
						</Form.Item>
					</Form.Item>
				</Form.Item>
				<Form.Item label=" " required style={{ marginBottom: 0 }}>
					<Form.Item
						{...formItemLayout618}
						required
						label={
							<Checkbox
								checked={readRequired}
								onChange={(e) => onChange(e, 'read')}
							>
								读权限
							</Checkbox>
						}
					>
						<Form.Item
							noStyle
							name="read"
							rules={[
								{
									required: readRequired,
									message: '资源名称不能为空'
								},
								{
									pattern: new RegExp(
										pattern.notSupportChinese
									),
									message:
										'请输入非中文的资源名称或正则表达式'
								},
								{ validator: checkPattern }
							]}
						>
							<Input
								style={{
									width: '240px'
								}}
								placeholder="请输入资源名称，支持正则表达输入"
							/>
						</Form.Item>
					</Form.Item>
				</Form.Item>
				<Form.Item label=" " required style={{ marginBottom: 0 }}>
					<Form.Item
						{...formItemLayout618}
						required
						label={
							<Checkbox
								checked={writeRequired}
								onChange={(e) => onChange(e, 'write')}
							>
								写权限
							</Checkbox>
						}
					>
						<Form.Item
							noStyle
							name="write"
							rules={[
								{
									required: writeRequired,
									message: '资源名称不能为空'
								},
								{
									pattern: new RegExp(
										pattern.notSupportChinese
									),
									message:
										'请输入非中文的资源名称或正则表达式'
								},
								{ validator: checkPattern }
							]}
						>
							<Input
								style={{
									width: '240px'
								}}
								placeholder="请输入资源名称，支持正则表达输入"
							/>
						</Form.Item>
					</Form.Item>
				</Form.Item>
			</Form>
		</Modal>
	);
}

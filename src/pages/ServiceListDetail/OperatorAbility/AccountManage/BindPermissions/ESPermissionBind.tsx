import React, { useEffect, useState } from 'react';
import { Form, Modal, Select, notification } from 'antd';
import {
	empowerMiddlewareAccounts,
	getPermissionInfo
} from '@/services/middleware';
import { AutoCompleteOptionItem } from '@/types/comment';
import { formItemLayout618 } from '@/utils/const';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';

export default function ESPermissionBind({
	open,
	onCancel,
	onCreate,
	clusterId,
	namespace,
	middlewareName,
	selectedAccounts,
	type,
	deployMode
}: {
	open: boolean;
	onCancel: () => void;
	onCreate: () => void;
	clusterId: string;
	namespace: string;
	middlewareName: string;
	selectedAccounts: MiddlewareAccount[];
	type: string;
	deployMode?: string;
}): JSX.Element {
	const [commandOptions, setCommandOptions] = useState<
		AutoCompleteOptionItem[]
	>([]);
	const [loading, setLoading] = useState<boolean>(false);
	const [okLoading, setOkLoading] = useState<boolean>(false);
	const [form] = Form.useForm();
	console.log(selectedAccounts);
	useEffect(() => {
		getInfo();
	}, []);
	const getInfo = () => {
		setLoading(true);
		getPermissionInfo({
			clusterId,
			namespace,
			middlewareName,
			deployMode: deployMode ? deployMode : 'container',
			type,
			infoType: 'elasticsearch_command'
		})
			.then((res) => {
				if (res.success) {
					const permissions_temp = JSON.parse(res.data.permissions);
					const resources_temp = permissions_temp.commandList.map(
						(item: string) => {
							return {
								value: item,
								label: item
							};
						}
					);
					setCommandOptions(resources_temp);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const onOk = async () => {
		await form.validateFields();
		const values = form.getFieldsValue();
		const permissions_temp = JSON.stringify(values);
		const account_temp = selectedAccounts.map((item) => item.account);
		setOkLoading(true);
		await ExecuteOrderFuc();
		await empowerMiddlewareAccounts({
			account: account_temp,
			permissions: permissions_temp,
			clusterId,
			middlewareName,
			namespace,
			type,
			deployMode: deployMode ? deployMode : 'container'
		})
			.then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '权限绑定成功'
					});
					onCreate();
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setOkLoading(false);
			});
	};
	return (
		<Modal
			width={500}
			open={open}
			onCancel={onCancel}
			title="授权账户"
			onOk={onOk}
			okButtonProps={{ loading: okLoading }}
		>
			<Form
				form={form}
				{...formItemLayout618}
				labelAlign="left"
				colon={false}
			>
				<Form.Item name="commandList" label="授权设置">
					<Select
						loading={loading}
						showSearch
						placeholder="请选择"
						mode="multiple"
						options={commandOptions}
						optionFilterProp="label"
						filterOption={(input, option) => {
							return ((option?.label ?? '') as string).includes(
								input
							);
						}}
					/>
				</Form.Item>
			</Form>
		</Modal>
	);
}

import React, { useEffect, useState } from 'react';
import { Button, Drawer, Modal, Table, notification } from 'antd';
import {
	disempowerMiddlewareAccountPermissions,
	getMiddlewareAccountPermissions
} from '@/services/middleware';
import { nacosAction, rabbitmqPermission, rabbitmqRole } from '@/utils/enum';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { useHistory, useParams } from 'react-router';
import { DetailParams } from '@/pages/ServiceListDetail/detail';
import { maintenances } from '@/utils/const';
import { controlledOperationDisabled } from '@/utils/utils';

export default function NacosAndRabbitmqPermissionDetail({
	open,
	onCancel,
	currentAccount,
	deployMode,
	lock
}: {
	open: boolean;
	onCancel: () => void;
	currentAccount: MiddlewareAccount;
	deployMode?: string;
	lock: string;
}): JSX.Element {
	const {
		name,
		aliasName,
		type,
		clusterId,
		namespace,
		middlewareName
	}: DetailParams = useParams();
	const history = useHistory();
	const updatePermissionMiddlewareAccountOperatorId =
		maintenances['Account Management Update Permission'];
	const [loading, setLoading] = useState<boolean>(false);
	const [permissions, setPermissions] = useState<any[]>();
	const [selectedPermissionKeys, setSelectedPermissionKeys] = useState<
		React.Key[]
	>([]);
	const [selectedPermissions, setSelectedPermissions] = useState<any[]>([]);
	useEffect(() => {
		getData();
	}, []);
	const getData = () => {
		setLoading(true);
		getMiddlewareAccountPermissions({
			clusterId,
			middlewareName,
			namespace,
			type: name,
			deployMode: deployMode ? deployMode : 'container',
			accountName: currentAccount.account
		})
			.then((res) => {
				if (res.success) {
					const permisstions_temp = JSON.parse(res.data.permissions);
					if (name === 'nacos') {
						setPermissions(permisstions_temp.resourceActionList);
					} else {
						setPermissions(permisstions_temp.permissionList);
					}
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const separateAuth = () => {
		Modal.confirm({
			title: '操作确认',
			content: (
				<>
					<p>释放权限操作后，该账户将失去相关权限能力</p>
					<p>是否确定进行释放权限操作？</p>
				</>
			),
			onOk: async () => {
				let permissions: any = {};
				if (name === 'nacos') {
					permissions = JSON.stringify({
						resourceActionList: selectedPermissions
					});
				} else {
					permissions = JSON.stringify({
						permissionList: selectedPermissions
					});
				}
				await ExecuteOrderFuc();
				return disempowerMiddlewareAccountPermissions({
					permissions,
					clusterId,
					middlewareName,
					namespace,
					accountName: currentAccount.account,
					deployMode: deployMode ? deployMode : 'container',
					type: name
				}).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '权限释放成功'
						});
						setSelectedPermissionKeys([]);
						setSelectedPermissions([]);
						getData();
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};
	const onSelectChange = (
		newSelectedRowKeys: React.Key[],
		selectedRows: any[]
	) => {
		setSelectedPermissionKeys(newSelectedRowKeys);
		setSelectedPermissions(selectedRows);
	};
	return (
		<Drawer
			title="查看权限"
			placement="right"
			onClose={onCancel}
			width={600}
			open={open}
		>
			<Button
				danger
				disabled={
					selectedPermissions.length === 0 ||
					controlledOperationDisabled('expert', lock) ||
					currentAccount.state === 'accountNotFound'
				}
				onClick={() => {
					WorkOrderFuc(
						separateAuth,
						lock,
						middlewareName,
						updatePermissionMiddlewareAccountOperatorId,
						history,
						type,
						name,
						aliasName,
						clusterId,
						namespace
					);
				}}
			>
				释放权限
			</Button>
			<Table
				className="mt-16"
				dataSource={permissions}
				rowKey={(record) =>
					record?.action +
					record.resource +
					record?.permission +
					record?.vHost
				}
				loading={loading}
				rowSelection={{
					selectedRowKeys: selectedPermissionKeys,
					onChange: onSelectChange
				}}
			>
				{name === 'nacos' && (
					<Table.Column
						dataIndex="resourceName"
						title="资源"
						ellipsis={true}
						render={(value: any) => value || '/'}
					/>
				)}
				{name === 'nacos' && (
					<Table.Column
						dataIndex="action"
						title="权限"
						ellipsis={true}
						render={(value: any) =>
							value ? nacosAction[value] : '/'
						}
					/>
				)}
				{name === 'rabbitmq' && (
					<Table.Column
						dataIndex="vhost"
						title="资源"
						ellipsis={true}
						render={(value: any) => value || '/'}
					/>
				)}
				{name === 'rabbitmq' && (
					<Table.Column
						dataIndex="permission"
						title="权限"
						ellipsis={true}
						render={(value: any, record: any) =>
							value
								? `${rabbitmqPermission[value]}(${record.resource})`
								: '/'
						}
					/>
				)}
			</Table>
		</Drawer>
	);
}

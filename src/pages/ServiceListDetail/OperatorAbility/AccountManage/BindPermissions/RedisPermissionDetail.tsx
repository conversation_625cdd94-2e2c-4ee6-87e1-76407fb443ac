import React, { useEffect, useState } from 'react';
import {
	But<PERSON>,
	Drawer,
	Collapse,
	notification,
	Space,
	Spin,
	Modal
} from 'antd';
import {
	disempowerMiddlewareAccountPermissions,
	getMiddlewareAccountPermissions
} from '@/services/middleware';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';
import { useHistory, useParams } from 'react-router';
import { DetailParams } from '@/pages/ServiceListDetail/detail';
import { controlledOperationDisabled } from '@/utils/utils';
const { Panel } = Collapse;
export default function RedisPermissionDetail({
	open,
	onCancel,
	currentAccount,
	deployMode,
	lock
}: {
	open: boolean;
	onCancel: () => void;
	currentAccount: MiddlewareAccount;
	deployMode?: string;
	lock: string;
}): JSX.Element {
	const {
		name,
		aliasName,
		type,
		middlewareName,
		clusterId,
		namespace
	}: DetailParams = useParams();
	const history = useHistory();
	const updatePermissionMiddlewareAccountOperatorId =
		maintenances['Account Management Update Permission'];
	const [loading, setLoading] = useState<boolean>(false);
	const [keys, setKeys] = useState<string[]>([]);
	const [commands, setCommands] = useState<string[]>([]);
	const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
	const [selectedCommands, setSelectedCommands] = useState<string[]>([]);
	const [btnDisabled, setBtnDisabled] = useState<boolean>(true);
	useEffect(() => {
		getData();
	}, []);
	useEffect(() => {
		if (selectedCommands.length === 0 && selectedKeys.length === 0) {
			setBtnDisabled(true);
		} else {
			setBtnDisabled(false);
		}
	}, [selectedCommands, selectedKeys]);
	const getData = () => {
		setLoading(true);
		getMiddlewareAccountPermissions({
			clusterId,
			middlewareName,
			namespace,
			type: name,
			deployMode: deployMode ? deployMode : 'container',
			accountName: currentAccount.account
		})
			.then((res) => {
				if (res.success) {
					const permisstions_temp = JSON.parse(res.data?.permissions);
					setKeys(permisstions_temp?.keyList);
					setCommands(permisstions_temp?.commandList);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const separateAuth = () => {
		Modal.confirm({
			title: '操作确认',
			content: (
				<>
					<p>释放权限操作后，该账户将失去相关权限能力</p>
					<p>是否确定进行释放权限操作？</p>
				</>
			),
			onOk: async () => {
				const permissions = JSON.stringify({
					keyList: selectedKeys,
					commandList: selectedCommands
				});
				await ExecuteOrderFuc();
				return disempowerMiddlewareAccountPermissions({
					permissions,
					clusterId,
					middlewareName,
					namespace,
					accountName: currentAccount.account,
					type: name,
					deployMode: deployMode ? deployMode : 'container'
				}).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '权限释放成功'
						});
						setSelectedCommands([]);
						setSelectedKeys([]);
						getData();
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};
	const onChange = (key_or_command: string, type: string) => {
		if (type === 'key') {
			const isExist = selectedKeys.includes(key_or_command);
			if (isExist) {
				setSelectedKeys(
					selectedKeys.filter((item) => item !== key_or_command)
				);
			} else {
				setSelectedKeys(selectedKeys.concat([key_or_command]));
			}
		} else {
			const isExist = selectedCommands.includes(key_or_command);
			if (isExist) {
				setSelectedCommands(
					selectedCommands.filter((item) => item !== key_or_command)
				);
			} else {
				setSelectedCommands(selectedCommands.concat([key_or_command]));
			}
		}
	};
	return (
		<Drawer
			title="查看权限"
			placement="right"
			onClose={onCancel}
			width={600}
			open={open}
		>
			<Button
				danger
				onClick={() => {
					WorkOrderFuc(
						separateAuth,
						lock,
						middlewareName,
						updatePermissionMiddlewareAccountOperatorId,
						history,
						type,
						name,
						aliasName,
						clusterId,
						namespace
					);
				}}
				disabled={
					btnDisabled ||
					controlledOperationDisabled('expert', lock) ||
					currentAccount.state === 'accountNotFound'
				}
				className="mb-8"
			>
				释放权限
			</Button>
			<Spin spinning={loading}>
				<Collapse defaultActiveKey={['1', '2']}>
					<Panel header="授权Key" key="1">
						<Space wrap>
							{keys?.map((item: string) => {
								const isExist = selectedKeys.includes(item);
								if (isExist) {
									return (
										<div
											className="selected-tag"
											key={item}
											onClick={() =>
												onChange(item, 'key')
											}
										>
											{item}
										</div>
									);
								} else {
									return (
										<div
											className="unselected-tag"
											key={item}
											onClick={() =>
												onChange(item, 'key')
											}
										>
											{item}
										</div>
									);
								}
							})}
						</Space>
					</Panel>
					<Panel header="授权命令" key="2">
						<Space wrap>
							{commands?.map((item: string) => {
								const isExist = selectedCommands.includes(item);
								if (isExist) {
									return (
										<div
											className="selected-tag"
											key={item}
											onClick={() =>
												onChange(item, 'command')
											}
										>
											{item}
										</div>
									);
								} else {
									return (
										<div
											className="unselected-tag"
											key={item}
											onClick={() =>
												onChange(item, 'command')
											}
										>
											{item}
										</div>
									);
								}
							})}
						</Space>
					</Panel>
				</Collapse>
			</Spin>
		</Drawer>
	);
}

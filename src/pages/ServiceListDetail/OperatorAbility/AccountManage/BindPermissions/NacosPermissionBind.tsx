import React, { useEffect, useState } from 'react';
import { Form, Modal, Select, notification } from 'antd';
import {
	empowerMiddlewareAccounts,
	getPermissionInfo
} from '@/services/middleware';
import { AutoCompleteOptionItem } from '@/types/comment';
import { nacosAction } from '@/utils/enum';
import { formItemLayout618 } from '@/utils/const';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';

export default function NacosPermissionBind({
	open,
	onCancel,
	onCreate,
	clusterId,
	namespace,
	middlewareName,
	type,
	selectedAccounts,
	deployMode
}: {
	open: boolean;
	onCancel: () => void;
	onCreate: () => void;
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	selectedAccounts: MiddlewareAccount[];
	deployMode?: string;
}): JSX.Element {
	const [form] = Form.useForm();
	const [loading, setLoading] = useState<boolean>(false);
	const [okLoading, setOkLoading] = useState<boolean>(false);
	const [resources, setResources] = useState<any[]>([]);
	const [actionOptions, setActionOptions] = useState<
		AutoCompleteOptionItem[]
	>([]);
	useEffect(() => {
		getInfo();
	}, []);
	const getInfo = () => {
		setLoading(true);
		getPermissionInfo({
			clusterId,
			namespace,
			middlewareName,
			deployMode: deployMode ? deployMode : 'container',
			type,
			infoType: 'nacos_resource_action'
		})
			.then((res) => {
				if (res.success) {
					const permissions_temp = JSON.parse(res.data.permissions);
					const resources_temp = permissions_temp.resourceList.map(
						(item: any) => {
							if (item.resourceName === 'public') {
								item.resource = 'public';
							}
							return item;
						}
					);
					const actions_temp = permissions_temp.actionList.map(
						(item: string) => {
							return {
								value: item,
								label: nacosAction[item]
							};
						}
					);
					setResources(resources_temp);
					setActionOptions(actions_temp);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const onOk = async () => {
		await form.validateFields();
		const values = form.getFieldsValue();
		const resources_temp = resources.find(
			(item) => item.resource === values.resource
		);
		const permissions_temp = JSON.stringify({
			...resources_temp,
			resource: values.resource === 'public' ? '' : values.resource,
			action: values.action
		});
		const account_temp = selectedAccounts.map((item) => item.account);
		setOkLoading(true);
		await ExecuteOrderFuc();
		await empowerMiddlewareAccounts({
			account: account_temp,
			permissions: permissions_temp,
			clusterId,
			middlewareName,
			namespace,
			type,
			deployMode: deployMode ? deployMode : 'container'
		})
			.then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '权限绑定成功'
					});
					onCreate();
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setOkLoading(false);
			});
	};
	return (
		<Modal
			width={600}
			onCancel={onCancel}
			open={open}
			title="授权账户"
			onOk={onOk}
			okButtonProps={{ loading: okLoading }}
		>
			<Form
				form={form}
				{...formItemLayout618}
				labelAlign="left"
				requiredMark="optional"
				colon={false}
			>
				<Form.Item
					name="resource"
					label="选择资源"
					rules={[{ required: true, message: '授权动作不能为空' }]}
				>
					<Select
						showSearch
						placeholder="请选择访问的数据库"
						loading={loading}
						optionFilterProp="children"
						filterOption={(input, option) => {
							return (
								(option?.children ?? '') as string
							).includes(input);
						}}
					>
						{resources?.map((item) => {
							return (
								<Select.Option
									key={item.resource}
									value={item.resource}
								>
									{item.resourceName}
								</Select.Option>
							);
						})}
					</Select>
				</Form.Item>
				<Form.Item
					name="action"
					label="授权动作"
					rules={[{ required: true, message: '授权动作不能为空' }]}
				>
					<Select
						placeholder="请选择授权动作"
						loading={loading}
						options={actionOptions}
					/>
				</Form.Item>
			</Form>
		</Modal>
	);
}

import React, { useEffect, useState } from 'react';
import {
	Button,
	Col,
	Form,
	Input,
	Modal,
	Row,
	Select,
	Space,
	Tag,
	notification
} from 'antd';
import { formItemLayout618 } from '@/utils/const';
import {
	empowerMiddlewareAccounts,
	getPermissionInfo
} from '@/services/middleware';
import { AutoCompleteOptionItem } from '@/types/comment';
import { PlusOutlined } from '@ant-design/icons';
import { ValidateStatus } from 'antd/es/form/FormItem';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';

export default function RedisPermissionBind({
	open,
	onCancel,
	onCreate,
	clusterId,
	namespace,
	middlewareName,
	type,
	selectedAccounts,
	deployMode
}: {
	open: boolean;
	onCancel: () => void;
	onCreate: () => void;
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	selectedAccounts: MiddlewareAccount[];
	deployMode?: string;
}): JSX.Element {
	const [form] = Form.useForm();
	const formKeys = Form.useWatch('key', form);
	const formType = Form.useWatch('type', form);
	const formCommands = Form.useWatch('command', form);
	const [okDisabled, setOkDisabled] = useState<boolean>(true);
	const [okLoading, setOkLoading] = useState<boolean>(false);
	const [commandSets, setCommandSet] = useState<any>();
	const [databaseOptions, setDatabaseOptions] = useState<
		AutoCompleteOptionItem[]
	>([]);
	const [keyOptions, setKeyOptions] = useState<AutoCompleteOptionItem[]>([]);
	const [typeOptions, setTypeOptions] = useState<AutoCompleteOptionItem[]>(
		[]
	);
	const [commandOptions, setCommandOptions] = useState<
		AutoCompleteOptionItem[]
	>([]);
	const [addedKeys, setAddedKeys] = useState<string[]>([]);
	const [addedCommands, setAddedCommands] = useState<string[]>([]);
	const [keyValidateStatus, setKeyValidateStatus] =
		useState<ValidateStatus>();
	const [keyHelp, setKeyHelp] = useState<React.ReactNode>();
	const [commandValidateStatus, setCommandValidateStatus] =
		useState<ValidateStatus>();
	const [commandHelp, setCommandHelp] = useState<React.ReactNode>();
	useEffect(() => {
		getInfo('redis_database');
		getInfo('redis_command');
	}, []);
	useEffect(() => {
		if (addedCommands.length === 0 || addedKeys.length === 0) {
			setOkDisabled(true);
		} else {
			setOkDisabled(false);
		}
	}, [addedCommands, addedKeys]);
	useEffect(() => {
		if (commandSets?.length > 0) {
			const current_type = commandSets?.find(
				(item: any) => item.type === formType
			);
			const command_options_temp = current_type.commandSet.map(
				(item: any) => {
					return {
						value: item,
						label: item
					};
				}
			);
			setCommandOptions(command_options_temp);
		}
	}, [formType]);
	const getInfo = (info_type: string, info_temp?: string) => {
		getPermissionInfo({
			clusterId,
			namespace,
			middlewareName,
			deployMode: deployMode ? deployMode : 'container',
			type,
			infoType: info_type,
			info: info_temp
		}).then((res) => {
			if (res.success) {
				const permissions = JSON.parse(res.data.permissions);
				console.log(permissions);
				if (info_type === 'redis_database') {
					const database_option = permissions?.databaseList.map(
						(item: string) => {
							return {
								label: item,
								value: item
							};
						}
					);
					setDatabaseOptions(database_option);
					return;
				}
				if (info_type === 'redis_command') {
					setCommandSet(permissions?.commandSetList);
					const type_option = permissions?.commandSetList.map(
						(item: any) => {
							return {
								value: item.type,
								label: item.type
							};
						}
					);
					setTypeOptions(type_option);
				}
				if (info_type === 'redis_key') {
					const key_option = permissions?.keyList.map((item: any) => {
						return {
							value: item,
							label: item
						};
					});
					setKeyOptions(key_option);
				}
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	const onDatabaseChange = (database: string) => {
		const key_send_data = JSON.stringify({ database: database });
		getInfo('redis_key', key_send_data);
		setKeyValidateStatus(undefined);
		setKeyHelp(undefined);
		form.setFieldValue('key', undefined);
	};
	const onClose = (key_or_command: string, type: string) => {
		if (type === 'key') {
			setAddedKeys(addedKeys.filter((item) => item !== key_or_command));
		} else {
			setAddedCommands(
				addedCommands.filter((item) => item !== key_or_command)
			);
		}
	};
	const onAdd = (operate_type: string) => {
		if (operate_type === 'key') {
			if (!formKeys) {
				setKeyValidateStatus('error');
				setKeyHelp('数据库和可访问的数据Key不能为空');
				return;
			}
			setKeyValidateStatus(undefined);
			setKeyHelp(undefined);
			form.setFieldValue('key', undefined);
			setAddedKeys(Array.from(new Set(addedKeys.concat(formKeys))));
		} else {
			if (!formType) {
				setCommandValidateStatus('error');
				setCommandHelp('授权命令集不能为空');
				return;
			}
			setCommandValidateStatus(undefined);
			setCommandHelp(undefined);
			form.setFieldValue('command', undefined);
			if (formCommands) {
				setAddedCommands(
					Array.from(new Set(addedCommands.concat(formCommands)))
				);
			} else {
				setAddedCommands(
					Array.from(new Set(addedCommands.concat([formType])))
				);
			}
		}
	};

	const onOk = async () => {
		const sendData = {
			keyList: addedKeys,
			commandList: addedCommands
		};
		const permissions_temp = JSON.stringify(sendData);
		const account_temp = selectedAccounts.map((item) => item.account);
		setOkLoading(true);
		await ExecuteOrderFuc();
		await empowerMiddlewareAccounts({
			account: account_temp,
			permissions: permissions_temp,
			clusterId,
			middlewareName,
			namespace,
			type,
			deployMode: deployMode ? deployMode : 'container'
		})
			.then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '权限绑定成功'
					});
					onCreate();
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setOkLoading(false);
			});
	};
	return (
		<Modal
			title="授权账户"
			open={open}
			onCancel={onCancel}
			onOk={onOk}
			width={800}
			okButtonProps={{ loading: okLoading, disabled: okDisabled }}
		>
			<Form
				form={form}
				{...formItemLayout618}
				colon={false}
				labelAlign="left"
				requiredMark="optional"
			>
				<Form.Item
					label="选择数据库/Key"
					required
					validateStatus={keyValidateStatus}
					help={keyHelp}
				>
					<Input.Group compact>
						<Form.Item noStyle name="database">
							<Select
								style={{ width: '46%' }}
								options={databaseOptions}
								showSearch
								optionFilterProp="label"
								filterOption={(input, option) => {
									return (
										(option?.label ?? '') as string
									).includes(input);
								}}
								onChange={onDatabaseChange}
								placeholder="请选择访问的数据库"
							/>
						</Form.Item>
						<Form.Item name="key" noStyle>
							<Select
								style={{ width: '46%' }}
								mode="multiple"
								options={keyOptions}
								placeholder="请选择可访问的数据Key"
							/>
						</Form.Item>
						<Button
							style={{ marginLeft: 4 }}
							icon={<PlusOutlined />}
							onClick={() => onAdd('key')}
						/>
					</Input.Group>
				</Form.Item>
				{addedKeys?.length > 0 && (
					<Row>
						<Col offset={6} style={{ width: '100%' }}>
							<div className="redis-tag-box">
								<Space wrap>
									{addedKeys.map((item) => {
										return (
											<Tag
												key={item}
												closable
												onClose={() =>
													onClose(item, 'key')
												}
											>
												{item}
											</Tag>
										);
									})}
								</Space>
							</div>
						</Col>
					</Row>
				)}
				<Form.Item
					label="授权命令"
					required
					validateStatus={commandValidateStatus}
					help={commandHelp}
				>
					<Input.Group compact>
						<Form.Item name="type" noStyle>
							<Select
								options={typeOptions}
								onChange={() => {
									setCommandValidateStatus(undefined);
									setCommandHelp(undefined);
									form.setFieldValue('command', undefined);
								}}
								style={{ width: '46%' }}
								placeholder=" 请选择授权命令集"
							/>
						</Form.Item>
						<Form.Item name="command" noStyle>
							<Select
								options={commandOptions}
								style={{ width: '46%' }}
								placeholder="请选择授权命令"
								mode="multiple"
							/>
						</Form.Item>
						<Button
							icon={<PlusOutlined />}
							style={{ marginLeft: 4 }}
							onClick={() => onAdd('command')}
						/>
					</Input.Group>
				</Form.Item>
				{addedCommands.length > 0 && (
					<Row>
						<Col offset={6} style={{ width: '100%' }}>
							<div className="redis-tag-box">
								<Space wrap>
									{addedCommands.map((item) => {
										return (
											<Tag
												key={item}
												closable
												onClose={() =>
													onClose(item, 'command')
												}
											>
												{item}
											</Tag>
										);
									})}
								</Space>
							</div>
						</Col>
					</Row>
				)}
			</Form>
		</Modal>
	);
}

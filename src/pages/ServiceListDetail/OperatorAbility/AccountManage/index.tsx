import React, { useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router';
import ProTable from '@/components/ProTable';
import Actions from '@/components/Actions';
import {
	Alert,
	Button,
	Form,
	Input,
	Modal,
	Space,
	Tooltip,
	notification
} from 'antd';
import { DetailParams } from '../../detail';
import {
	deleteMiddlewareAccount,
	getMiddlewareAccounts,
	saveOperateAccount
} from '@/services/middleware';
import AddMiddlewareAccount from './AddMiddlewareAccount';
import SyncMiddlewareAccount from './SyncMiddlewareAccount';
import EditMiddlewareAccount from './EditMiddlewareAccount';
import {
	EditOutlined,
	InfoCircleFilled,
	InfoCircleOutlined
} from '@ant-design/icons';
import ESPermissionBind from './BindPermissions/ESPermissionBind';
import TemplateImport from './TemplateImport';
import BindUserDetail from './BindUserDetail';
import NacosPermissionBind from './BindPermissions/NacosPermissionBind';
import NacosAndRabbitmqPermissionDetail from './BindPermissions/NacosAndRabbitmqPermissionDetail';
import BindUsers from './BindUsers';
import storage from '@/utils/storage';
import RedisPermissionBind from './BindPermissions/RedisPermissionBind';
import RedisPermissionDetail from './BindPermissions/RedisPermissionDetail';
import useRefresh from '@/utils/useRefresh';
import {
	PRIVATE_KEY,
	PUBLIC_KEY,
	formItemLayout420,
	maintenances
} from '@/utils/const';
import { controlledOperationDisabled, decrypt, encrypt } from '@/utils/utils';
import RabbitMQPermissionBind from './BindPermissions/RabbitMQPermissionBind';
import { rabbitmqRole } from '@/utils/enum';
import RabbitmqRoleChange from './RabbitmqRoleChange';
import PasswordEye from '@/components/PasswordEye';
import { WorkOrderFuc } from '@/components/WorkOrderFuc';
const { LinkButton } = Actions;

export default function AccountManage({
	lock,
	deployMode,
	devAccount,
	serviceRefresh
}: {
	lock: string;
	deployMode?: string;
	devAccount: string;
	serviceRefresh: () => void;
}): JSX.Element {
	const history = useHistory();
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const addMiddlewareAccountOperatorId =
		maintenances['Account Management Add Account'];
	const deleteMiddlewareAccountOperatorId =
		maintenances['Account Management Delete Account'];
	const batchUpdateMiddlewareAccountOperatorId =
		maintenances['Account Management Batch Update Password'];
	const middlewareAccountBindUsersAccountOperatorId =
		maintenances['Account Management Bind Users'];
	const updatePermissionMiddlewareAccountOperatorId =
		maintenances['Account Management Update Permission'];
	const updateMiddlewareAccountOperatorId =
		maintenances['Account Management Update Password'];
	const [form] = Form.useForm();
	const {
		name,
		clusterId,
		namespace,
		middlewareName,
		type,
		aliasName
	}: DetailParams = useParams();
	const [middlewareAccounts, setMiddlewareAccounts] = useState<
		MiddlewareAccount[]
	>([]);
	const [loading, setLoading] = useState<boolean>(false);
	const [editMiddlewareAccount, setEditMiddlewareAccount] =
		useState<MiddlewareAccount>();
	const [addOpen, setAddOpen] = useState<boolean>(false);
	const [updateOpen, setUpdateOpen] = useState<boolean>(false);
	const [syncOpen, setSyncOpen] = useState<boolean>(false);
	const [
		nacosAndRabbitmqPermissionsOpen,
		setNacosAndRabbitmqPermissionsOpen
	] = useState<boolean>(false);
	const [redisPermissionsOpen, setRedisPermissionsOpen] =
		useState<boolean>(false);
	const [redisImpowerOpen, setRedisImpowerOpen] = useState<boolean>(false);
	const [nacosImpowerOpen, setNacosImpowerOpen] = useState<boolean>(false);
	const [esImpowerOpen, setESImpowerOpen] = useState<boolean>(false);
	const [rabbitmqImpowerOpen, setRabbitmqImpowerOpen] =
		useState<boolean>(false);
	const [rabbitmqRoleOpen, setRabbitmqRoleOpen] = useState<boolean>(false);
	const [usersOpen, setUsersOpen] = useState<boolean>(false);
	const [importOpen, setImportOpen] = useState<boolean>(false);
	const [userBindOpen, setUserBindOpen] = useState<boolean>(false);
	const [pageCurrent, setPageCurrent] = useState<number>(1);
	const [pageSize, setPageSize] = useState<number>(10);
	const [total, setTotal] = useState<number>(0);
	const [selectedAccountKeys, setSelectedAccountKeys] = useState<React.Key[]>(
		[]
	);
	const [selectedAccounts, setSelectedAccounts] = useState<
		MiddlewareAccount[]
	>([]);
	const [hasOperateAccount, setHasOperateAccount] = useState<boolean>(false);
	const [btnLoading, setBtnLoading] = useState<boolean>(false);
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		if (hasOperateAccount) {
			getData(pageCurrent, pageSize);
		}
	}, [refreshKey, hasOperateAccount]);
	useEffect(() => {
		if (devAccount) {
			setHasOperateAccount(true);
		} else {
			setHasOperateAccount(false);
		}
	}, [devAccount]);
	const getData = (current: number, size: number) => {
		setSelectedAccountKeys([]);
		setSelectedAccounts([]);
		setLoading(true);
		getMiddlewareAccounts({
			clusterId,
			namespace,
			middlewareName,
			size,
			current,
			deployMode: deployMode ? deployMode : 'container',
			type: name
		})
			.then((res) => {
				if (res.success) {
					setPageSize(res.data.size);
					setPageCurrent(res.data.current);
					setTotal(res.data.total);
					setMiddlewareAccounts(res.data.records);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};

	const toUsersDetail = (middleware_account: MiddlewareAccount) => {
		setEditMiddlewareAccount(middleware_account);
		setUsersOpen(true);
	};
	const toAccountPermissions = (middleware_account: MiddlewareAccount) => {
		setEditMiddlewareAccount(middleware_account);
		if (name === 'nacos' || name === 'rabbitmq') {
			setNacosAndRabbitmqPermissionsOpen(true);
		} else {
			setRedisPermissionsOpen(true);
		}
	};
	const deleteAccount = (middleware_account: MiddlewareAccount) => {
		Modal.confirm({
			title: '操作确认',
			content: (
				<>
					<p>删除账户后，该账户无法重新恢复</p>
					<p>是否确定进行删除操作？</p>
				</>
			),
			onOk: () => {
				return deleteMiddlewareAccount({
					clusterId,
					middlewareName,
					namespace,
					deployMode: deployMode ? deployMode : 'container',
					type: name,
					accountName: middleware_account.account
				}).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '账户删除成功'
						});
						if (pageCurrent > 1 && total % 10 === 1 && total > 10) {
							getData(pageCurrent - 1, pageSize);
						} else {
							getData(pageCurrent, pageSize);
						}
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};
	const onPermission = () => {
		switch (name) {
			case 'redis':
				setRedisImpowerOpen(true);
				break;
			case 'nacos':
				setNacosImpowerOpen(true);
				break;
			case 'elasticsearch':
				setESImpowerOpen(true);
				break;
			case 'rabbitmq':
				setRabbitmqImpowerOpen(true);
				break;
		}
	};
	const Operation = {
		primary: (
			<>
				<Button
					type="primary"
					disabled={controlledOperationDisabled('expert', lock)}
					onClick={() => {
						WorkOrderFuc(
							() => {
								setAddOpen(true);
							},
							lock,
							middlewareName,
							addMiddlewareAccountOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
				>
					新增
				</Button>
				<Button
					disabled={
						selectedAccountKeys.length === 0 ||
						controlledOperationDisabled('expert', lock)
					}
					onClick={() => {
						WorkOrderFuc(
							onPermission,
							lock,
							middlewareName,
							updatePermissionMiddlewareAccountOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
				>
					授权
				</Button>
				<Button
					onClick={() => {
						WorkOrderFuc(
							() => {
								setUserBindOpen(true);
							},
							lock,
							middlewareName,
							middlewareAccountBindUsersAccountOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
					disabled={
						selectedAccountKeys.length === 0 ||
						controlledOperationDisabled('expert', lock)
					}
				>
					绑定
				</Button>
				<Button
					onClick={() => {
						WorkOrderFuc(
							() => {
								setImportOpen(true);
							},
							lock,
							middlewareName,
							batchUpdateMiddlewareAccountOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
					disabled={controlledOperationDisabled('expert', lock)}
				>
					模板导入
				</Button>
			</>
		)
	};
	const accountRender = (value: any, record: MiddlewareAccount) => {
		if (record.state === 'normal') {
			return value;
		}
		let tooltip = '';
		switch (record.state) {
			case 'normal':
				tooltip = '';
				break;
			default:
				tooltip = record.stateErrorReason;
				break;
		}
		return (
			<div style={{ width: '250px' }}>
				<Space>
					<div
						style={{ maxWidth: '235px' }}
						className="text-overflow"
						title={value}
					>
						{value}
					</div>
					<Tooltip title={tooltip}>
						<InfoCircleFilled style={{ color: 'red' }} />
					</Tooltip>
				</Space>
			</div>
		);
	};
	const permissionsRender = (_: any, record: MiddlewareAccount) => {
		if (name === 'elasticsearch') {
			const command_temp = JSON.parse(record.permissions).commandList;
			if (command_temp.length > 0) {
				return command_temp.join(',');
			} else {
				return '/';
			}
		}
		return (
			<div
				className="name-link"
				onClick={() => toAccountPermissions(record)}
			>
				查看详情
			</div>
		);
	};
	const usersRender = (_: any, record: MiddlewareAccount) => {
		return (
			<div className="name-link" onClick={() => toUsersDetail(record)}>
				查看详情
			</div>
		);
	};
	const roleRender = (value: string[], record: MiddlewareAccount) => {
		const value_temp = value
			? value.map((item) =>
					rabbitmqRole[item] ? rabbitmqRole[item] : '/'
			  )
			: ['/'];
		const role_temp = value_temp.join(',');
		if (record.state === 'accountNotFound') {
			return <span>{role_temp}</span>;
		}
		return (
			<Space>
				<span>{role_temp}</span>
				<EditOutlined
					onClick={() => {
						WorkOrderFuc(
							() => {
								setEditMiddlewareAccount(record);
								setRabbitmqRoleOpen(true);
							},
							lock,
							middlewareName,
							addMiddlewareAccountOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
				/>
			</Space>
		);
	};
	const actionRender = (_: any, record: MiddlewareAccount) => {
		const showSync =
			record.state !== 'normal' &&
			record.state !== 'updatePasswordError' &&
			record.state !== 'accountNotFound';
		return (
			<Actions>
				{!showSync && (
					<LinkButton
						onClick={() => {
							WorkOrderFuc(
								() => {
									setEditMiddlewareAccount(record);
									setUpdateOpen(true);
								},
								lock,
								middlewareName,
								updateMiddlewareAccountOperatorId,
								history,
								type,
								name,
								aliasName,
								clusterId,
								namespace
							);
						}}
						disabled={
							controlledOperationDisabled('expert', lock) ||
							record.state === 'accountNotFound'
						}
					>
						修改密码
					</LinkButton>
				)}
				{showSync && (
					<LinkButton
						onClick={() => {
							WorkOrderFuc(
								() => {
									setEditMiddlewareAccount(record);
									setSyncOpen(true);
								},
								lock,
								middlewareName,
								addMiddlewareAccountOperatorId,
								history,
								type,
								name,
								aliasName,
								clusterId,
								namespace
							);
						}}
						disabled={controlledOperationDisabled('expert', lock)}
					>
						同步密码
					</LinkButton>
				)}
				<LinkButton
					onClick={() => {
						WorkOrderFuc(
							() => {
								deleteAccount(record);
							},
							lock,
							middlewareName,
							deleteMiddlewareAccountOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
					disabled={controlledOperationDisabled('expert', lock)}
				>
					删除
				</LinkButton>
			</Actions>
		);
	};
	const allFormCreate = () => {
		setAddOpen(false);
		setSyncOpen(false);
		setUpdateOpen(false);
		setImportOpen(false);
		setUserBindOpen(false);
		setESImpowerOpen(false);
		setNacosImpowerOpen(false);
		setRedisImpowerOpen(false);
		setRabbitmqImpowerOpen(false);
		setRabbitmqRoleOpen(false);
		getData(pageCurrent, pageSize);
	};
	const onSelectChange = (
		newSelectedRowKeys: React.Key[],
		selectedRows: MiddlewareAccount[]
	) => {
		setSelectedAccountKeys(newSelectedRowKeys);
		setSelectedAccounts(selectedRows);
	};
	const onChange = (page: number, pageSize: number) => {
		setPageCurrent(page);
		setPageSize(pageSize);
		getData(page, pageSize);
	};
	const passwordRender = (value: string) => {
		if (!value) return '/';
		const password_decrypt = decrypt(value, PRIVATE_KEY);
		return <PasswordEye value={password_decrypt} />;
	};
	const handleOk = async () => {
		await form.validateFields();
		const values = form.getFieldsValue();
		setBtnLoading(true);
		saveOperateAccount({
			clusterId,
			namespace,
			middlewareName,
			type: name,
			deployMode: deployMode ? deployMode : 'container',
			accountName: values.account,
			account: values.account,
			password: encrypt(values.password, PUBLIC_KEY) || values.password
		})
			.then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '后台管理账户创建成功!'
					});
					serviceRefresh();
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setBtnLoading(false);
			});
	};
	return (
		<div className="account-manage-content">
			{!hasOperateAccount && (
				<div className="account-manage-table">
					<div className="account-manage-form-content">
						<div className="account-manage-form-content-title">
							设置后台管理账户
						</div>
						<div className="account-manage-form-content-body">
							<Alert
								type="info"
								message="你好！使用账户管理功能时需要依赖后台高权限账户，请先设置相关账户信息！"
								showIcon
								className="mb-16"
							/>
							<Form
								form={form}
								{...formItemLayout420}
								labelAlign="left"
								colon={false}
								requiredMark="optional"
							>
								<Form.Item
									label="账户名"
									name="account"
									rules={[
										{
											required: true,
											message: '账户名不能为空'
										}
									]}
								>
									<Input placeholder="请输入账户名" />
								</Form.Item>
								<Form.Item
									label="密码"
									name="password"
									rules={[
										{
											required: true,
											message: '密码不能为空'
										}
									]}
								>
									<Input.Password placeholder="请输入密码" />
								</Form.Item>
							</Form>
						</div>
						<div className="account-manage-form-content-footer">
							<Button
								type="primary"
								onClick={handleOk}
								loading={btnLoading}
							>
								确定
							</Button>
						</div>
					</div>
				</div>
			)}
			<ProTable
				rowKey="account"
				dataSource={middlewareAccounts}
				operation={Operation}
				loading={loading}
				rowSelection={{
					selectedRowKeys: selectedAccountKeys,
					onChange: onSelectChange,
					getCheckboxProps: (record: MiddlewareAccount) => ({
						disabled: record.state === 'accountNotFound'
					})
				}}
				pagination={{
					total: total,
					current: pageCurrent,
					pageSize: pageSize,
					onChange: onChange
				}}
			>
				<ProTable.Column
					title="账户名"
					dataIndex="account"
					width={250}
					ellipsis={true}
					render={accountRender}
				/>
				<ProTable.Column
					title="密码"
					dataIndex="password"
					width={150}
					render={passwordRender}
				/>
				{name === 'rabbitmq' && (
					<ProTable.Column
						title={
							<span className="flex-space-between">
								角色
								<Tooltip
									title={
										<>
											<p>
												超级管理员(administrator)可登录管理控制台(启用management
												plugin的情况下)，可查看所有的信息，并且可以对用户，策略(policy)进行操作；
											</p>
											<p>
												监控者(monitoring)可登录管理控制台(启用management
												plugin的情况下)，同时可以查看rabbitmq节点的相关信息；
											</p>
											<p>
												策略制定者(policymaker)可登录管理控制台(启用management
												plugin的情况下),
												同时可以对policy进行管理，但无法查看节点的相关信息；
											</p>
											<p>
												普通管理者(management)仅可登录管理控制台(启用management
												plugin的情况下)，无法看到节点信息，也无法对策略进行管理；
											</p>
											<p>
												无角色(none)无法登录管理控制台，通常就是普通的生产者和消费者。
											</p>
										</>
									}
								>
									<InfoCircleOutlined />
								</Tooltip>
							</span>
						}
						dataIndex="role"
						render={roleRender}
					/>
				)}
				{name === 'rabbitmq' && (
					<ProTable.Column
						title={
							<span className="flex-space-between">
								权限
								<Tooltip title="configure: 配置权限、write: 写权限、read: 读权限">
									<InfoCircleOutlined />
								</Tooltip>
							</span>
						}
						width={100}
						dataIndex="permissions"
						render={permissionsRender}
					/>
				)}
				{name !== 'rabbitmq' && (
					<ProTable.Column
						title="权限"
						dataIndex="permissions"
						render={permissionsRender}
						ellipsis={true}
					/>
				)}
				<ProTable.Column
					title="绑定平台用户"
					dataIndex="users"
					width={120}
					render={usersRender}
				/>
				<ProTable.Column
					title="操作"
					width={150}
					dataIndex="actions"
					render={actionRender}
				/>
			</ProTable>
			{addOpen && (
				<AddMiddlewareAccount
					open={addOpen}
					onCancel={() => setAddOpen(false)}
					onCreate={allFormCreate}
					clusterId={clusterId}
					namespace={namespace}
					middlewareName={middlewareName}
					type={name}
					deployMode={deployMode}
				/>
			)}
			{syncOpen && editMiddlewareAccount && (
				<SyncMiddlewareAccount
					open={syncOpen}
					onCancel={() => setSyncOpen(false)}
					onCreate={allFormCreate}
					clusterId={clusterId}
					namespace={namespace}
					middlewareName={middlewareName}
					middlewareAccount={editMiddlewareAccount}
					type={name}
					deployMode={deployMode}
				/>
			)}
			{updateOpen && editMiddlewareAccount && (
				<EditMiddlewareAccount
					open={updateOpen}
					onCancel={() => setUpdateOpen(false)}
					onCreate={allFormCreate}
					clusterId={clusterId}
					namespace={namespace}
					middlewareName={middlewareName}
					middlewareAccount={editMiddlewareAccount}
					type={name}
					deployMode={deployMode}
				/>
			)}
			{importOpen && (
				<TemplateImport
					open={importOpen}
					onCancel={() => setImportOpen(false)}
					onCreate={allFormCreate}
					clusterId={clusterId}
					namespace={namespace}
					middlewareName={middlewareName}
					deployMode={deployMode}
					type={name}
				/>
			)}
			{/* 查看平台账户 */}
			{usersOpen && editMiddlewareAccount && (
				<BindUserDetail
					open={usersOpen}
					onCancel={() => setUsersOpen(false)}
					currentAccount={editMiddlewareAccount}
					deployMode={deployMode}
					lock={lock}
				/>
			)}
			{/* 绑定平台用户 */}
			{userBindOpen && selectedAccountKeys && (
				<BindUsers
					open={userBindOpen}
					onCancel={() => setUserBindOpen(false)}
					onCreate={allFormCreate}
					clusterId={clusterId}
					namespace={namespace}
					middlewareName={middlewareName}
					projectId={projectId}
					organId={organId}
					selectedAccounts={selectedAccountKeys}
					deployMode={deployMode}
					type={name}
				/>
			)}
			{/* es 授权账户 */}
			{esImpowerOpen && selectedAccountKeys && (
				<ESPermissionBind
					open={esImpowerOpen}
					onCancel={() => setESImpowerOpen(false)}
					onCreate={allFormCreate}
					clusterId={clusterId}
					namespace={namespace}
					middlewareName={middlewareName}
					selectedAccounts={selectedAccounts}
					type={name}
					deployMode={deployMode}
				/>
			)}
			{/* nacos rabbitmq 查看平台权限 */}
			{nacosAndRabbitmqPermissionsOpen && editMiddlewareAccount && (
				<NacosAndRabbitmqPermissionDetail
					open={nacosAndRabbitmqPermissionsOpen}
					onCancel={() => {
						setNacosAndRabbitmqPermissionsOpen(false);
					}}
					currentAccount={editMiddlewareAccount}
					deployMode={deployMode}
					lock={lock}
				/>
			)}
			{/* nacos 授权账户 */}
			{nacosImpowerOpen && selectedAccountKeys && (
				<NacosPermissionBind
					open={nacosImpowerOpen}
					onCancel={() => setNacosImpowerOpen(false)}
					onCreate={allFormCreate}
					clusterId={clusterId}
					namespace={namespace}
					middlewareName={middlewareName}
					selectedAccounts={selectedAccounts}
					type={name}
					deployMode={deployMode}
				/>
			)}
			{/* redis 查看平台权限 */}
			{redisPermissionsOpen && editMiddlewareAccount && (
				<RedisPermissionDetail
					open={redisPermissionsOpen}
					onCancel={() => setRedisPermissionsOpen(false)}
					currentAccount={editMiddlewareAccount}
					deployMode={deployMode}
					lock={lock}
				/>
			)}
			{/* redis 授权账户 */}
			{redisImpowerOpen && selectedAccountKeys && (
				<RedisPermissionBind
					open={redisImpowerOpen}
					onCancel={() => setRedisImpowerOpen(false)}
					onCreate={allFormCreate}
					clusterId={clusterId}
					namespace={namespace}
					middlewareName={middlewareName}
					selectedAccounts={selectedAccounts}
					type={name}
					deployMode={deployMode}
				/>
			)}
			{/* rabbitmq 授权账户 */}
			{rabbitmqImpowerOpen && selectedAccountKeys && (
				<RabbitMQPermissionBind
					open={rabbitmqImpowerOpen}
					onCancel={() => setRabbitmqImpowerOpen(false)}
					onCreate={allFormCreate}
					clusterId={clusterId}
					namespace={namespace}
					middlewareName={middlewareName}
					selectedAccounts={selectedAccounts}
					type={name}
					deployMode={deployMode}
				/>
			)}
			{/* rabbitmq修改角色 */}
			{rabbitmqRoleOpen && editMiddlewareAccount && (
				<RabbitmqRoleChange
					open={rabbitmqRoleOpen}
					onCancel={() => setRabbitmqRoleOpen(false)}
					onCreate={allFormCreate}
					clusterId={clusterId}
					namespace={namespace}
					middlewareName={middlewareName}
					middlewareAccount={editMiddlewareAccount}
					type={name}
					deployMode={deployMode}
				/>
			)}
		</div>
	);
}

import React, { useEffect, useState } from 'react';
import { Form, Modal, Select, notification } from 'antd';
import { formItemLayout618 } from '@/utils/const';
import { getProjectMember } from '@/services/project';
import { UserItem } from '@/pages/ProjectDetail/projectDetail';
import { middlewareAccountBindToUsers } from '@/services/middleware';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';
export default function BindUsers({
	open,
	onCancel,
	onCreate,
	clusterId,
	namespace,
	middlewareName,
	projectId,
	organId,
	selectedAccounts,
	deployMode,
	type
}: {
	open: boolean;
	onCancel: () => void;
	onCreate: () => void;
	clusterId: string;
	namespace: string;
	middlewareName: string;
	projectId: string;
	organId: string;
	selectedAccounts: React.Key[];
	deployMode?: string;
	type: string;
}): JSX.Element {
	const [form] = Form.useForm();
	const [users, setUsers] = useState<UserItem[]>([]);
	const [loading, setLoading] = useState<boolean>(false);
	useEffect(() => {
		getUsers();
	}, []);
	const getUsers = () => {
		getProjectMember({
			organId,
			projectId,
			allocatable: false,
			dbaUser: true
		}).then((res) => {
			if (res.success) {
				setUsers(res.data);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const onOk = async () => {
		await form.validateFields();
		const values = form.getFieldsValue();
		setLoading(true);
		await ExecuteOrderFuc();
		await middlewareAccountBindToUsers({
			clusterId: clusterId,
			middlewareName: middlewareName,
			namespace: namespace,
			account: selectedAccounts as string[],
			userIds: values.userIds,
			deployMode: deployMode ? deployMode : 'container',
			type
		})
			.then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '账户绑定成功'
					});
					onCreate();
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	return (
		<Modal
			open={open}
			onCancel={onCancel}
			title="绑定平台用户"
			width={600}
			onOk={onOk}
			okButtonProps={{ loading: loading }}
		>
			<Form
				form={form}
				{...formItemLayout618}
				labelAlign="left"
				colon={false}
				requiredMark={false}
			>
				<Form.Item
					name="userIds"
					label="选择用户"
					rules={[{ required: true, message: '平台用户不能为空' }]}
				>
					<Select
						showSearch
						placeholder="请选择平台用户"
						mode="multiple"
						optionFilterProp="label"
						filterOption={(input, option) => {
							return (
								(option?.children ?? '') as string
							).includes(input);
						}}
					>
						{users.map((item: UserItem) => {
							return (
								<Select.Option key={item.id} value={item.id}>
									{item.aliasName}
								</Select.Option>
							);
						})}
					</Select>
				</Form.Item>
			</Form>
		</Modal>
	);
}

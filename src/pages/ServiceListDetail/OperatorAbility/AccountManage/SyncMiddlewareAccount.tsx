import React, { useEffect } from 'react';
import { Form, Input, Modal, notification } from 'antd';
import { PUBLIC_KEY, formItemLayout618 } from '@/utils/const';
import { syncMiddlewareAccount } from '@/services/middleware';
import { accountRuleRender, encrypt } from '@/utils/utils';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';

export default function SyncMiddlewareAccount({
	open,
	onCancel,
	onCreate,
	clusterId,
	namespace,
	middlewareAccount,
	middlewareName,
	type,
	deployMode
}: {
	open: boolean;
	onCancel: () => void;
	onCreate: () => void;
	clusterId: string;
	namespace: string;
	middlewareName: string;
	middlewareAccount: MiddlewareAccount;
	type: string;
	deployMode?: string;
}): JSX.Element {
	const [form] = Form.useForm();
	const passwordForm = Form.useWatch('password');
	const confirmPasswordForm = Form.useWatch('confirmPassword');
	useEffect(() => {
		if (confirmPasswordForm) {
			form.validateFields(['confirmPassword']);
		}
	}, [passwordForm]);
	const onOk = async () => {
		await form.validateFields();
		const values = form.getFieldsValue();
		const password_encrypt = values.password
			? encrypt(values.password, PUBLIC_KEY)
			: '';
		const confirmPassword_encrypt = values.confirmPassword
			? encrypt(values.confirmPassword, PUBLIC_KEY)
			: '';
		if (
			typeof password_encrypt !== 'string' ||
			typeof confirmPassword_encrypt !== 'string'
		) {
			notification.warning({
				message: '提示',
				description: '密码加密失败'
			});
			return;
		}
		await ExecuteOrderFuc();
		await syncMiddlewareAccount({
			clusterId,
			namespace,
			middlewareName,
			type,
			deployMode: deployMode ? deployMode : 'container',
			accountName: middlewareAccount.account,
			password: password_encrypt,
			confirmPassword: confirmPassword_encrypt
		}).then((res) => {
			if (res.success) {
				notification.success({
					message: '成功',
					description: '信息同步成功'
				});
				onCreate();
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	const checkConfirmPassword = (_: any, value: any) => {
		if (value) {
			if (value !== form.getFieldValue('password')) {
				return Promise.reject(new Error('两次输入的密码信息不一致'));
			}
		}
		return Promise.resolve();
	};
	return (
		<Modal
			title="同步密码"
			width={500}
			onCancel={onCancel}
			destroyOnClose={true}
			open={open}
			onOk={onOk}
		>
			<Form
				labelAlign="left"
				form={form}
				{...formItemLayout618}
				colon={false}
				requiredMark="optional"
			>
				<Form.Item
					name="password"
					label="同步密码"
					rules={[
						{
							required: true,
							message: '同步密码不能为空'
						},
						{
							type: 'string',
							min: accountRuleRender(type, 'min') as number,
							message: accountRuleRender(
								type,
								'min_max_message'
							) as string
						}
					]}
				>
					<Input.Password placeholder="请输入密码" />
				</Form.Item>
				<Form.Item
					name="confirmPassword"
					label="再次输入"
					rules={[
						{
							required: true,
							message: '两次输入的密码信息不一致'
						},
						{ validator: checkConfirmPassword }
					]}
				>
					<Input.Password placeholder="请输入密码" />
				</Form.Item>
			</Form>
		</Modal>
	);
}

import React, { useState } from 'react';
import { Form, Modal, Select, notification } from 'antd';
import { formItemLayout420 } from '@/utils/const';
import { updateAccountRole } from '@/services/project';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';

const roleOptions = [
	{ value: 'administrator', label: '超级管理员' },
	{ value: 'monitoring', label: '监控者' },
	{ value: 'policymaker', label: '策略制定者' },
	{ value: 'management', label: '普通管理者' },
	{ value: 'none', label: '无角色' }
];
export default function RabbitmqRoleChange({
	open,
	onCancel,
	clusterId,
	namespace,
	middlewareName,
	type,
	deployMode,
	middlewareAccount,
	onCreate
}: {
	open: boolean;
	onCancel: () => void;
	onCreate: () => void;
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	deployMode?: string;
	middlewareAccount: MiddlewareAccount;
}): JSX.Element {
	const [form] = Form.useForm();
	const [loading, setLoading] = useState<boolean>(false);
	const onOk = async () => {
		await form.validateFields();
		const values = form.getFieldsValue();
		setLoading(true);
		await ExecuteOrderFuc();
		await updateAccountRole({
			clusterId,
			namespace,
			middlewareName,
			type,
			deployMode: deployMode ? deployMode : 'container',
			accountName: middlewareAccount.account,
			role: values.role
		})
			.then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '角色修改成功'
					});
					onCreate();
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	return (
		<Modal
			open={open}
			title="变更角色"
			onCancel={onCancel}
			onOk={onOk}
			okButtonProps={{ loading: loading }}
		>
			<Form
				form={form}
				labelAlign="left"
				requiredMark="optional"
				colon={false}
				{...formItemLayout420}
			>
				<Form.Item
					label="角色"
					name="role"
					rules={[{ required: true, message: '角色不能为空' }]}
				>
					<Select
						mode="multiple"
						options={roleOptions}
						placeholder="请选择角色"
					/>
				</Form.Item>
			</Form>
		</Modal>
	);
}

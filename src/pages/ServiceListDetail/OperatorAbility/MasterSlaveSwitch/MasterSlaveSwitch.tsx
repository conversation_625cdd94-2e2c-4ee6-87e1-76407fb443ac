import React, { useEffect, useState } from 'react';
import {
	QuestionCircleOutlined,
	SwapOutlined,
	ExclamationCircleFilled,
	ExclamationCircleOutlined
} from '@ant-design/icons';
import {
	Button,
	notification,
	Space,
	Spin,
	Switch,
	Tooltip,
	Modal,
	Badge
} from 'antd';
import { useHistory, useParams } from 'react-router';
import PodCard from '@/components/PodCard';
import {
	getBurstList,
	getPods,
	getSwitch,
	pgSwitchHover,
	mysqlRecover,
	getMysqlRecover,
	switchMiddlewareMasterSlave,
	autoSwitchMiddlewareMasterSlave,
	reCreateBackup,
	getReCreateBackupStatus,
	nodeDegradation
} from '@/services/middleware';
import {
	DetailParams,
	MasterSlaveSwitchProps,
	PodItem,
	PodSendData
} from '../../detail';
import EditBrust from './editBrust';
import useRefresh from '@/utils/useRefresh';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances, recoverStatusList } from '@/utils/const';
import { controlledOperationDisabled } from '@/utils/utils';

const { confirm } = Modal;
export default function MasterSlaveSwitch(
	props: MasterSlaveSwitchProps
): JSX.Element {
	const { serviceRefresh, serverStatus, mode, lock } = props;
	const {
		clusterId,
		namespace,
		middlewareName,
		name,
		chartVersion,
		type,
		aliasName
	}: DetailParams = useParams();
	const history = useHistory();
	// * mysql/pgsql 显示使用
	const [masterPods, setMasterPods] = useState<PodItem[]>([]);
	const [slavePods, setSalvePods] = useState<PodItem[]>([]);
	// * 自动切换禁用
	const [autoSwitchDisabled, setAutoSwitchDisabled] =
		useState<boolean>(false);
	// * 手动切换禁用提示
	const [switchDisabledMsg, setSwitchDisabledMsg] = useState<string>('');
	// * 故障切换禁用提示
	const [switchoverDisabledMsg, setSwitchoverDisabledMsg] =
		useState<string>('');
	// * 上一次切换时间
	const [lastAutoSwitchTime, setLastAutoSwitchTime] = useState<string>('');
	// * 自动切换开关控制
	const [switchCheck, setSwitchCheck] = useState<boolean>();
	// * 主从关系显示loading
	const [spinning, setSpinning] = useState<boolean>(false);
	// * 立即切换loading
	const [switchLoading, setSwitchLoading] = useState<boolean>(false);
	// * pg主从切换loading
	const [switchoverLoading, setSwitchoverLoading] = useState<boolean>(false);
	// * 立即切换状态
	const [switchStatus, setSwitchStatus] = useState<boolean>();
	// * 立即切换失败原因
	const [switchReason, setSwitchReason] = useState<string>('');
	// * redis 分片信息
	const [burstList, setBurstList] = useState<string[]>([]);
	// * redis 分片切换弹窗显示
	const [burstVisible, setBurstVisible] = useState<boolean>(false);
	const [redisPodList, setRedisPodList] = useState<any[]>([]);
	const [selectSlave, setSelectSlave] = useState<string>();
	const [selectKeys, setSelectKeys] = useState<string[]>([]);
	// * 备库重搭状态
	const [recoverStatus, setRecoverStatus] = useState<string>();
	// * 备库重搭信息
	const [recoverMessage, setRecoverMessage] = useState<string>();
	// * 备库重搭自动备份
	const [backupCheck, setBackupCheck] = useState<boolean>(true);
	// * 当前备库重搭实例
	const [currentRecoverPod, setCurrentRecoverPod] = useState<string>('');
	const [isBackupLibrary, setIsBackupLibrary] = useState<boolean>(false);
	const [role, setRole] = useState<string>('');
	const [nowPodIp, setNowPodIp] = useState<string>('');
	const [reCreateBackupState, setReCreateBackupState] = useState<string>('');
	const [isNotSamePod, setIsNotSamePod] = useState<boolean>(false);
	const [slaveState, setSlaveState] = useState<string>(''); //记录异步节点状态，
	const [syncSlaveState, setSyncSlaveState] = useState<string>(''); //记录同步节点状态，
	const masterSlaveSwitchOperatorId = maintenances['Master-Slave Switching'];
	const postgresqlBackupDatabaseReconfigurationOperatorId =
		maintenances['【Postgresql】Backup Database Reconfiguration'];
	const mysqlBackupDatabaseReconfigurationOperatorId =
		maintenances['【Mysql】Backup Database Reconfiguration'];
	const postgresqlSyncNodeDowngradeOperatorId =
		maintenances['【Postgresql】Sync Node Downgrade'];
	const [refreshKey, setRefreshKey] = useState<number>(0);
	// * 节点降级loading
	const [nodeDegradationLoading, setNodeDegradationLoading] =
		useState<boolean>(false);
	let timer: any = null;

	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		getSwitchInfo();
		getPodList({
			clusterId,
			namespace,
			middlewareName,
			type: name
		});
		name === 'mysql' && getReconnectStatus();
	}, [refreshKey]);
	useEffect(() => {
		type === 'mysql' && getReconnectStatus();
	}, [selectKeys]);
	useEffect(() => {
		if (masterPods && slavePods && burstList) {
			const list = burstList.map((item: string) => {
				const array = item.split(' -> ');
				return {
					masterPods: masterPods.find(
						(item) => item.podName === array[0]
					),
					slavePods: slavePods.find(
						(item) => item.podName === array[1]
					)
				};
			});
			setRedisPodList(list);
		}
	}, [masterPods, slavePods, burstList]);
	// * 获取redis主从关系列表
	const getBurstLists = () => {
		getBurstList({
			clusterId,
			namespace,
			middlewareName,
			mode
		}).then((res) => {
			if (res.success) {
				if (res.data && JSON.stringify(res.data) !== '{}') {
					const list = [];
					for (const key in res.data) {
						list.push(`${key} -> ${res.data[key]}`);
					}
					setBurstList(list);
				}
			} else {
				notification.error({
					message: '失败',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	// * 获取中间件切换信息
	const getSwitchInfo = () => {
		getSwitch({
			clusterId,
			namespace,
			middlewareName,
			type: name
		}).then((res) => {
			if (res.success) {
				setAutoSwitchDisabled(res.data?.status ? false : true);
			} else {
				setAutoSwitchDisabled(true);
			}
			setSwitchCheck(res.data?.isAuto);
			setLastAutoSwitchTime(res.data?.lastAutoSwitchTime);
		});
	};
	// * 获取中间件手动切换信息 -- 暂时前端根据节点角色判断
	useEffect(() => {
		if (name === 'postgresql') {
			const syncSlave = slavePods?.filter(
				(item) =>
					item.role === 'syncslave' || item.role === 'sync_slave'
			);
			const slave = slavePods?.filter(
				(item) =>
					item.role !== 'syncslave' && item.role !== 'sync_slave'
			);
			if (!masterPods.length && !slavePods.length) {
				setSwitchDisabledMsg('当前未识别到任一节点，无法进行切换');
				setSwitchoverDisabledMsg('当前未识别到任一节点，无法进行切换');
			}
			if (masterPods?.[0]?.status === 'Running') {
				if (syncSlave?.[0]?.status === 'Running') {
					setSwitchDisabledMsg('');
					setSwitchoverDisabledMsg(
						'当前主节点与同步节点状态正常，不建议进行故障切换，请尝试进行主从切换'
					);
				} else {
					setSwitchDisabledMsg('当前同步节点状态异常，无法进行切换');
					setSwitchoverDisabledMsg(
						'当前同步节点状态异常，无法进行切换'
					);
				}
			} else {
				if (syncSlave?.[0]?.status === 'Running') {
					setSwitchDisabledMsg(
						'当前主节点状态异常，无法进行主从切换，请尝试进行故障切换'
					);
					setSwitchoverDisabledMsg('');
				} else {
					if (
						slave?.filter((item) => item.status === 'Running')
							?.length
					) {
						setSwitchDisabledMsg(
							'当前主节点状态异常，无法进行主从切换，请尝试进行故障切换'
						);
						setSwitchoverDisabledMsg('');
					}
				}
			}
		}
	}, [masterPods, slavePods]);
	// * 获取pod列表
	const getPodList = (sendData: PodSendData) => {
		setSpinning(true);
		getPods(sendData)
			.then((res) => {
				if (res.success) {
					const masterP = res.data.pods.filter(
						(item: PodItem) => item.role === 'master'
					);
					setMasterPods(masterP || []);
					const slaveP = res.data.pods.filter(
						(item: PodItem) =>
							item.role === 'slave' ||
							item.role === 'syncslave' ||
							item.role === 'sync_slave'
					);
					slaveP.forEach((element: any) => {
						if (element.role === 'slave') {
							setSlaveState(element.status);
						}
						if (
							element.role === 'sync_slave' ||
							element.role === 'syncslave'
						) {
							setSyncSlaveState(element.status);
						}
					});
					setSalvePods(slaveP);
				} else {
					notification.error({
						message: '失败',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setSpinning(false);
				name === 'redis' && getBurstLists();
			});
	};
	const switchMiddleware = (value: boolean | null) => {
		// * 传boolean和传null的区别
		// * 传 null 代表的是 点击了立即切换，直接发起切换
		// * 传 boolean 代表的是 改变了自动主从切换的开关， 没有发起切换
		const api =
			value === null
				? switchMiddlewareMasterSlave
				: autoSwitchMiddlewareMasterSlave;
		const sendData: any = {
			clusterId,
			namespace,
			middlewareName,
			type: name,
			chartVersion,
			isAuto: value,
			masterName:
				name === 'redis'
					? selectSlave?.split(' -> ')[0]
					: masterPods[0]?.podName
		};
		// * redis 需要从节点名称（redis没有自动切换开关）
		if (name === 'redis') {
			sendData.slaveName = selectSlave?.split(' -> ')[1];
		}
		setSwitchLoading(true);
		setBurstVisible(false);
		api(sendData)
			.then((res) => {
				setSwitchLoading(false);
				if (res.success) {
					if (typeof value !== 'boolean') {
						setSwitchStatus(true);
						setSwitchReason('');
						notification.success({
							message: '成功',
							description:
								name === 'mysql' ? (
									<span>
										已完成切换：
										<br /> {masterPods[0]?.podName}:
										{'主节点 -> 同步节点'}
										<br /> {res.data.newMasterName}:
										{'同步节点 -> 主节点'}
									</span>
								) : (
									'切换成功, 5s后刷新数据'
								)
						});
					} else {
						notification.success({
							message: '成功',
							description: `自动切换${
								value ? '开启' : '关闭'
							}成功！`
						});
					}
					setTimeout(function () {
						serviceRefresh();
						getSwitchInfo();
						getPodList({
							clusterId,
							namespace,
							middlewareName,
							type: name
						});
					}, 5000);
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
					setSwitchStatus(false);
					setSwitchReason(res.errorMsg);
				}
			})
			.finally(() => {
				setSwitchLoading(false);
			});
	};
	const switchMiddlewareHover = () => {
		confirm({
			title: '操作确认',
			content:
				'主从切换过程中可能会有闪断，请确保您的应用程序具有自动重连机制，是否确定进行主从切换？',
			onOk: () => {
				const sendData: any = {
					clusterId,
					namespace,
					middlewareName
				};
				setSwitchoverLoading(true);
				pgSwitchHover(sendData).then((res) => {
					setSwitchoverLoading(false);
					if (res.success) {
						setSwitchStatus(true);
						setSwitchReason('');
						notification.success({
							message: '成功',
							description: '切换成功, 5s后刷新数据'
						});
						setTimeout(function () {
							serviceRefresh();
							getSwitchInfo();
							getPodList({
								clusterId,
								namespace,
								middlewareName,
								type: name
							});
						}, 5000);
					} else {
						setSwitchStatus(false);
						setSwitchReason(res.errorMsg);
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			}
		});
	};
	const onChange = (checked: boolean) => {
		confirm({
			title: '操作确认',
			content: checked ? (
				'当此开关打开时，若出现主节点异常重启时，会自动进行被动主从切换，是否继续'
			) : (
				<>
					<p>当此开关关闭后，需要人为介入进行集群的异常处理</p>
					<p>是否继续?</p>
				</>
			),
			onOk: () => {
				ExecuteOrderFuc(() => {
					setSwitchCheck(checked);
					switchMiddleware(checked);
				});
			}
		});
	};
	const autoSwitch = () => {
		if (masterPods.length === 0 || masterPods[0].status !== 'Running') {
			confirm({
				title: '操作确认',
				content:
					'当前主节点运行异常，执行此操作可能会造成数据丢失，是否继续',
				onOk: () => {
					ExecuteOrderFuc(() => {
						switchMiddleware(null);
					});
				}
			});
		} else {
			if (name === 'redis') {
				setBurstVisible(true);
			} else {
				confirm({
					title: '操作确认',
					content:
						name === 'postgresql'
							? '故障切换过程中可能会有闪断，请确保您的应用程序具有自动重连机制，是否确定进行故障切换？'
							: '主从切换过程中可能会有闪断，请确保您的应用程序具有自动重连机制，是否确定进行主从切换？',
					onOk: () => {
						ExecuteOrderFuc(() => {
							switchMiddleware(null);
						});
					}
				});
			}
		}
	};
	const breakdownSwitch = () => {
		confirm({
			title: '操作确认',
			content:
				'故障切换过程中可能会有闪断，请确保您的应用程序具有自动重连机制，是否确定进行故障切换？',
			onOk: () => switchMiddleware(null)
		});
	};
	// * 备库重搭
	const reConnects = () => {
		Modal.confirm({
			title: '操作确认',
			content: (
				<>
					<p>
						{isBackupLibrary
							? '进行强制备库重搭操作将会重新开始备库重搭'
							: '进行备库重搭操作可能会导致服务状态运行异常'}
					</p>
					<p>是否确定进行备库重搭？</p>
				</>
			),
			onOk: async () => {
				await ExecuteOrderFuc();
				if (name === 'mysql') {
					mysqlRecover({
						clusterId,
						namespace,
						middlewareName,
						podName: selectKeys[0],
						backup: backupCheck
					}).then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '备库重搭成功，5s后刷新'
							});
							setTimeout(function () {
								getReconnectStatus();
							}, 5000);
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					});
				} else {
					const sendData: any = {
						clusterId,
						namespace,
						middlewareName,
						podName: selectKeys[0],
						podIp: nowPodIp,
						force: isBackupLibrary
					};
					reCreateBackup(sendData).then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: isBackupLibrary
									? '强制备库重搭成功'
									: '备库重搭成功'
							});
							timer = setInterval(function () {
								getReconnectStatus();
							}, 5000);
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					});
				}
			}
		});
	};
	const getReconnectStatus = () => {
		if (name === 'mysql') {
			getMysqlRecover({
				clusterId,
				namespace,
				middlewareName,
				podName: selectKeys[0]
			}).then((res) => {
				if (res.success) {
					setRecoverMessage(res.data.message);
					setRecoverStatus(res.data.status);
					setCurrentRecoverPod(res.data.slavePodName);
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		} else {
			getReCreateBackupStatus({
				clusterId,
				namespace,
				middlewareName,
				podName: selectKeys[0],
				podIp: nowPodIp
			}).then((res) => {
				if (res.success) {
					const state = res.data?.state || '';
					setReCreateBackupState(state);
					const isBackupLibraryNew =
						state === 'creating replica' ? true : false; //true则代表在备库重搭中,显示强制备库重搭
					setIsBackupLibrary(isBackupLibraryNew);
					if (state !== 'creating replica') {
						clearInterval(timer);
					}
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		}
	};
	// * 节点降级
	const nodeDegradationOperate = () => {
		confirm({
			title: '操作确认',
			content:
				'降级当前同步节点后，会随机升级一个异步节点为新的同步节点，是否确定进行节点降级？',
			onOk: async () => {
				setNodeDegradationLoading(true);
				await ExecuteOrderFuc();
				return nodeDegradation({
					clusterId,
					namespace,
					middlewareName,
					podName: selectKeys[0],
					podIp: nowPodIp
				})
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '节点降级完成'
							});
							setSelectKeys([]);
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					})
					.finally(() => {
						setNodeDegradationLoading(false);
					});
			}
		});
	};
	const onSelect = (item: any) => {
		if (name === 'mysql' || name === 'postgresql') {
			if (selectKeys.includes(item.podName)) {
				setSelectKeys([]);
			} else {
				setSelectKeys([item.podName]);
			}
			if (name === 'postgresql') {
				if (item.podName !== selectKeys[0]) {
					//如果前后不是同一个节点，那需要判断当前正在进行的备库重搭的状态，已完成则可以点击备库重搭
					setIsNotSamePod(true);
				} else {
					setIsNotSamePod(false);
				}
				setRole(item.role);
				setNowPodIp(item.podIp);
			}
		} else {
			return;
		}
	};
	return (
		<div>
			<div className="flex-space-between">
				<div>
					<Space>
						{(name === 'mysql' || name === 'postgresql') &&
						selectKeys.length ? (
							masterPods?.[0]?.status !== 'Running' ||
							(name === 'postgresql' && !switchCheck) ? (
								<Tooltip
									title={
										name === 'postgresql' && !switchCheck
											? '当前自动主从切换能力未开启，无法进行备库重搭'
											: '主节点状态异常，无法进行备库重搭'
									}
								>
									<Space>
										<Button disabled type="primary">
											{isBackupLibrary
												? '强制备库重搭'
												: '备库重搭'}
										</Button>
									</Space>
								</Tooltip>
							) : (
								<Button
									type="primary"
									disabled={
										(name === 'mysql' &&
											recoverStatus === 'Running') ||
										(name === 'postgresql' && !isNotSamePod)
									}
									onClick={() => {
										const operator_id =
											name === 'mysql'
												? mysqlBackupDatabaseReconfigurationOperatorId
												: postgresqlBackupDatabaseReconfigurationOperatorId;
										WorkOrderFuc(
											reConnects,
											lock,
											middlewareName,
											operator_id,
											history,
											type,
											name,
											aliasName,
											clusterId,
											namespace
										);
									}}
								>
									{isBackupLibrary
										? '强制备库重搭'
										: '备库重搭'}
								</Button>
							)
						) : null}
						{name === 'postgresql' && selectKeys.length ? (
							<Button
								type="primary"
								disabled={
									(role !== 'sync_slave' &&
										role !== 'syncslave') ||
									!(
										slaveState === 'Running' &&
										syncSlaveState === 'Running'
									)
								}
								danger
								loading={nodeDegradationLoading}
								onClick={() => {
									WorkOrderFuc(
										nodeDegradationOperate,
										lock,
										middlewareName,
										postgresqlSyncNodeDowngradeOperatorId,
										history,
										type,
										name,
										aliasName,
										clusterId,
										namespace
									);
								}}
							>
								节点降级
							</Button>
						) : null}
						{name === 'mysql' && selectKeys.length ? (
							<>
								<div>
									<label className="ms-switch-label">
										自动备份
									</label>
									<Tooltip title="开启后将会在备库重搭时进行一次数据备份，请确保拥有50%及以上的磁盘空间">
										<ExclamationCircleOutlined />
									</Tooltip>
								</div>
								<Switch
									checked={backupCheck}
									onChange={(checked) =>
										setBackupCheck(checked)
									}
								/>
							</>
						) : null}
						{name === 'postgresql' &&
							switchDisabledMsg &&
							!selectKeys.length && (
								<Tooltip title={switchDisabledMsg}>
									<Button
										disabled
										type="primary"
										loading={switchoverLoading}
										onClick={switchMiddlewareHover}
									>
										{switchoverLoading
											? '切换中...'
											: '主从切换'}
									</Button>
								</Tooltip>
							)}
						{name === 'postgresql' &&
						!switchDisabledMsg &&
						!selectKeys.length ? (
							<Button
								disabled={serverStatus === 'GracefulRestart'}
								type="primary"
								loading={switchoverLoading}
								onClick={switchMiddlewareHover}
							>
								{switchoverLoading ? '切换中...' : '主从切换'}
							</Button>
						) : null}
						{name !== 'postgresql' && !selectKeys.length ? (
							<Button
								type="primary"
								loading={switchLoading}
								onClick={autoSwitch}
								disabled={serverStatus === 'GracefulRestart'}
							>
								{switchLoading ? '切换中...' : '立即切换'}
							</Button>
						) : null}
						{name === 'postgresql' && !selectKeys.length ? (
							switchoverDisabledMsg ? (
								<Tooltip title={switchoverDisabledMsg}>
									<Button
										disabled
										loading={switchLoading}
										onClick={breakdownSwitch}
									>
										{switchLoading
											? '切换中...'
											: '故障切换'}
									</Button>
								</Tooltip>
							) : (
								<Button
									loading={switchLoading}
									onClick={breakdownSwitch}
								>
									{switchLoading ? '切换中...' : '故障切换'}
								</Button>
							)
						) : null}
						{((name !== 'redis' && !selectKeys.length) ||
							name === 'postgresql') && (
							<>
								<div>
									<label className="ms-switch-label">
										自动主从切换
									</label>
									<Tooltip title="开启状态下，在出现主节点异常重启的时候，会自动进行被动主从切换，在某些情况下，您也可以关闭主备自动切换，而采用人为介入的方式进行集群异常的处理。">
										<QuestionCircleOutlined />
									</Tooltip>
								</div>
								<Switch
									checked={switchCheck}
									disabled={
										(autoSwitchDisabled &&
											name === 'postgresql') ||
										controlledOperationDisabled(
											'maintenance',
											lock
										)
									}
									onChange={(checked: boolean) => {
										WorkOrderFuc(
											() => {
												onChange(checked);
											},
											lock,
											middlewareName,
											masterSlaveSwitchOperatorId,
											history,
											type,
											name,
											aliasName,
											clusterId,
											namespace
										);
									}}
								/>
								{autoSwitchDisabled &&
									name === 'postgresql' && (
										<Tooltip title="当前实例状态异常，无法获取自动切换开关状态">
											<ExclamationCircleFilled
												style={{
													marginLeft: 4,
													color: '#faad14'
												}}
											/>
										</Tooltip>
									)}
							</>
						)}
					</Space>
				</div>
			</div>
			<div className="ms-pod-content">
				<Space style={{ marginBottom: '46px' }}>
					{name === 'mysql' && (
						<div className="ms-pod-last-time">
							上一次切换时间:
							<label className="ml-12">
								{lastAutoSwitchTime || '/'}
							</label>
						</div>
					)}
					{typeof switchStatus === 'boolean' && (
						<div className="ms-pod-switch-status">
							切换状态:
							<label className="ml-12">
								{switchStatus ? (
									<Badge text="切换成功" status="success" />
								) : (
									<Badge
										text={`切换失败 ${
											switchReason && ':' + switchReason
										}`}
										status="error"
									/>
								)}
							</label>
						</div>
					)}
				</Space>
				{type === 'mysql' && recoverStatus && (
					<div className="ms-pod-last-time">
						{currentRecoverPod}备库重搭状态:
						<label className="ml-12">
							{recoverStatusList[recoverStatus] || '/'}
						</label>
					</div>
				)}
				{type === 'postgresql' &&
					reCreateBackupState === 'creating replica' && (
						<div className="ms-pod-last-time">
							{selectKeys[0]}备库重搭状态:
							<label className="ml-12">进行中</label>
						</div>
					)}
				{type === 'mysql' && recoverMessage && (
					<div className="ms-pod-last-time">
						错误信息:
						<label className="ml-12">{recoverMessage || '/'}</label>
					</div>
				)}
				<Spin spinning={spinning}>
					{name === 'redis' &&
						redisPodList.length > 0 &&
						redisPodList.map((item, index) => {
							return (
								<div className="ms-pod-box" key={index}>
									<div className="ms-pod-master-box">
										<PodCard
											key={item.masterPods?.podName}
											ip={item.masterPods?.podIp}
											role={item.masterPods?.role}
											status={item.masterPods?.status}
											podName={item.masterPods?.podName}
											color="#fa8c16"
											type={name}
										/>
									</div>
									<div className="ms-pod-change-icon">
										<SwapOutlined />
									</div>
									<div className="ms-pod-slave-box">
										<PodCard
											key={item.slavePods?.podName}
											ip={item.slavePods?.podIp}
											role={item.slavePods?.role}
											status={item.slavePods?.status}
											podName={item.slavePods?.podName}
											type={name}
										/>
									</div>
								</div>
							);
						})}
					{name !== 'redis' && (
						<div className="ms-pod-box">
							<div className="ms-pod-master-box">
								{masterPods.map((item: PodItem) => {
									return (
										<PodCard
											key={item.podName}
											ip={item.podIp}
											role={item.role}
											status={item.status}
											podName={item.podName}
											color="#fa8c16"
											type={name}
										/>
									);
								})}
							</div>
							<div className="ms-pod-change-icon">
								<SwapOutlined />
							</div>
							<div className="ms-pod-slave-box">
								{slavePods.map((item: PodItem) => {
									return (
										<PodCard
											key={item.podName}
											ip={item.podIp}
											role={item.role}
											status={item.status}
											podName={item.podName}
											selectKeys={selectKeys}
											onChange={() => onSelect(item)}
											type={name}
										/>
									);
								})}
							</div>
						</div>
					)}
				</Spin>
			</div>
			{burstVisible && (
				<EditBrust
					open={burstVisible}
					onCancel={() => setBurstVisible(false)}
					onOk={() => {
						if (selectSlave) {
							ExecuteOrderFuc(() => {
								switchMiddleware(null);
							});
						} else {
							notification.warn({
								message: '提示',
								description: '请选择分片'
							});
						}
					}}
					burstList={burstList}
					selectSlave={selectSlave}
					onChange={(value: any) => setSelectSlave(value)}
				/>
			)}
		</div>
	);
}

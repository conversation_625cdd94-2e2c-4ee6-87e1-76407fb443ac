import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import { Modal } from 'antd';
import Actions from '@/components/Actions';
import ProTable from '@/components/ProTable';
import { DBUserItem } from '../detail';

const { confirm } = Modal;
const LinkButton = Actions.LinkButton;
export default function DatabaseMag(): JSX.Element {
	const [users, setUsers] = useState<DBUserItem[]>([
		{
			user: 'root',
			password: 'Ab123456',
			passwordCheck: true,
			createTime: 'ddddd',
			id: 1,
			dbs: [],
			description: '/'
		}
	]);
	useEffect(() => {
		// todo 等待后端接口修改
	}, []);
	const handleSearch = (value: string) => {
		console.log(value);
	};
	const statusRender = (value: any, record: any, index: number) => {
		// <CloseOutlined style={{ cursor: 'pointer', color: '#Ef595C' }} />;
		return (
			<>
				<CheckOutlined
					style={{ cursor: 'pointer', color: '#0070cc' }}
				/>{' '}
				{value}
			</>
		);
	};
	// * todo 等待后端梳理接口
	const actionRender = () => (
		<Actions>
			<LinkButton
				onClick={() => {
					confirm({
						title: '操作确认',
						content: '请确认是否要重置该账户的密码？',
						onOk: () => {
							console.log('onOk');
						}
					});
				}}
			>
				重置密码
			</LinkButton>
		</Actions>
	);
	return (
		<ProTable
			dataSource={users}
			showRefresh
			rowKey="id"
			search={{
				placeholder: '请输入账户名称进行检索',
				onSearch: handleSearch,
				style: { width: '360px' }
			}}
		>
			<ProTable.Column title="账户名" dataIndex="user" width={100} />
			<ProTable.Column
				title="状态"
				dataIndex="status"
				render={statusRender}
			/>
			<ProTable.Column
				title="操作"
				dataIndex="action"
				render={actionRender}
				width={160}
			/>
		</ProTable>
	);
}

import React, { useEffect, useState } from 'react';
import { ProP<PERSON>, ProContent, ProHeader } from '@/components/ProPage';
import { useParams, useHistory } from 'react-router';
import { Button, notification, Space, Input } from 'antd';
import FormBlock from '@/components/FormBlock';
import { getNodes, migratePod, migrateLocalPod } from '@/services/middleware';
import { NodeItem } from '@/pages/ActiveActive/activeActive';
import { IconFont } from '@/components/IconFont';

const { Search } = Input;
interface AreaConfigParams {
	name: string;
	podName: string;
	clusterId: string;
	namespace: string;
	middlewareName: string;
	zone: string;
	nodeZone: string;
	nodeName: string;
}
export default function HostConfig(): JSX.Element {
	const params: AreaConfigParams = useParams();
	const history = useHistory();
	const [nodes, setNodes] = useState<NodeItem[]>([]);
	const [showNodes, setShowNodes] = useState<NodeItem[]>([]);
	const [selectNode, setSelectNode] = useState<string>();
	useEffect(() => {
		getData();
	}, []);
	const getData = () => {
		getNodes({ clusterId: params.clusterId, zone: params.zone }).then(
			(res) => {
				if (res.success) {
					setNodes(
						res.data.filter(
							(item: any) => params.nodeName !== item.name
						)
					);
					setShowNodes(
						res.data.filter(
							(item: any) => params.nodeName !== item.name
						)
					);
				}
			}
		);
	};
	const onSearch = (value: string) => {
		const list = nodes.filter((item: NodeItem) => item.ip.includes(value));
		setShowNodes(list);
	};
	const handleClick = () => {
		const api = params.name === 'postgresql' ? migrateLocalPod : migratePod;
		api({
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params.middlewareName,
			podName: params.podName,
			targetHost: selectNode
		}).then((res) => {
			if (res.success) {
				notification.success({
					message: '成功',
					description: '节点迁移成功'
				});
				history.goBack();
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	return (
		<ProPage>
			<ProHeader title="主机迁移" onBack={() => window.history.back()} />
			<ProContent>
				<FormBlock title="选择节点名称">
					<div className="zeus-area-config-content">
						<div className="zeus-area-label-content">
							选择节点名称
						</div>
						<div className="zeus-area-ip-content">
							<div className="zeus-area-search-content">
								<div>
									<Search
										allowClear={true}
										onSearch={onSearch}
										style={{ width: '250px' }}
									/>
								</div>
							</div>
							{params.nodeZone !== 'null' ? (
								<div
									style={{
										padding: '16px 24px 0',
										fontWeight: 500
									}}
								>
									{params.nodeZone}:
								</div>
							) : null}
							<div className="zeus-area-node-content">
								{showNodes.map(
									(item: NodeItem, index: number) => {
										return (
											<div
												key={index}
												className={`zeus-area-node-item ${
													item.name === selectNode
														? 'zeus-area-node-item-active'
														: ''
												} ${
													item.scheduable
														? ''
														: 'disabled'
												}`}
												onClick={() =>
													item.scheduable &&
													setSelectNode(item.name)
												}
											>
												<IconFont
													type="icon-yunzhuji"
													style={{
														color: '#888A8C',
														fontSize: 16
													}}
												/>
												<span>
													节点名称: {item.name}
												</span>
												<IconFont
													type="icon-xuanzhong"
													style={
														item.name === selectNode
															? {
																	position:
																		'absolute',
																	right: 0,
																	bottom: 0,
																	visibility:
																		'visible'
															  }
															: {
																	position:
																		'absolute',
																	right: 0,
																	bottom: 0,
																	visibility:
																		'hidden'
															  }
													}
												/>
											</div>
										);
									}
								)}
							</div>
						</div>
					</div>
				</FormBlock>
				<Space>
					<Button
						disabled={!selectNode}
						type="primary"
						onClick={handleClick}
					>
						确定
					</Button>
					<Button
						onClick={() => {
							history.goBack();
						}}
					>
						取消
					</Button>
				</Space>
			</ProContent>
		</ProPage>
	);
}

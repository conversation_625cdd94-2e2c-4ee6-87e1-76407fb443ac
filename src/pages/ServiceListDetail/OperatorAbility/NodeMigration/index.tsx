import React, { useState, useEffect } from 'react';
import { notification, Toolt<PERSON>, Button, Modal } from 'antd';
import { CloseCircleFilled, ExclamationCircleFilled } from '@ant-design/icons';
import { useHistory, useParams } from 'react-router';
import Actions from '@/components/Actions';
import ProTable from '@/components/ProTable';
import { getPods, getMigrate, deleteMigrate } from '@/services/middleware';
import transTime from '@/utils/transTime';
import { PodItem, PodSendData, DetailParams } from '../../detail';
import { controlledOperationDisabled, formatNumber } from '@/utils/utils';
import useRefresh from '@/utils/useRefresh';
import { WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';

const LinkButton = Actions.LinkButton;
export default function NodeMigration({ lock }: { lock: string }): JSX.Element {
	const {
		middlewareName,
		clusterId,
		namespace,
		name,
		aliasName,
		type
	}: DetailParams = useParams();
	const history = useHistory();
	const nodeMigrationOperatorId = maintenances['Node Migration'];
	const [pods, setPods] = useState<PodItem[]>([]);
	// * 主机迁移状态
	const [migrate, setMigrate] = useState<any>();
	const [loading, setLoading] = useState<boolean>(false);
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		const sendData: PodSendData = {
			clusterId,
			namespace,
			middlewareName,
			type
		};
		getMigrateStatus();
		getPodList(sendData);
	}, [refreshKey]);

	// * 获取中间件切换信息
	const getMigrateStatus = () => {
		getMigrate({
			clusterId,
			namespace,
			middlewareName
		}).then((res) => {
			setMigrate(res.data);
		});
	};

	// * 获取pod列表
	const getPodList = (sendData: PodSendData) => {
		setLoading(true);
		getPods(sendData)
			.then((res) => {
				if (res.success) {
					const list: any = [];
					res.data &&
						res.data.podInfoGroup &&
						res.data.podInfoGroup.listChildGroup &&
						res.data.podInfoGroup.listChildGroup.forEach(
							(el: any) => {
								if (el.role.includes('shard')) {
									list.push(
										...el.pods.map(
											(
												item: any,
												index: number,
												arr: any
											) => {
												if (index === 0) {
													return {
														...item,
														identify: el.role,
														span: arr.length
													};
												} else {
													return {
														...item,
														identify: el.role,
														span: 0
													};
												}
											}
										)
									);
								} else {
									list.push(
										...el.pods.map((item: any) => {
											return {
												...item,
												identify: el.role,
												span: 1
											};
										})
									);
								}
							}
						);
					list.forEach((item: any, index: number) => {
						if (index % 10 === 0 && item.span === 0) item.span = 1;
					});
					setPods(list);
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};

	const Operation = {
		primary: (
			<div className="title-content">
				<div className="blue-line"></div>
				<div className="detail-title">实例列表</div>
			</div>
		)
	};

	const actionRender = (value: any, record: PodItem, index: number) => {
		return (
			<Actions>
				{record.resources.provisioner === 'hspc.csi.hitachi.com' ||
				record.resources.provisioner === 'kubernetes.io/glusterfs' ||
				record.resources.provisioner === 'csi.huawei.com' ||
				record.resources.provisioner ===
					'nfs-client-provisioner-default' ||
				name === 'postgresql' ? (
					<div>
						<LinkButton
							disabled={
								record.status !== 'Running' ||
								migrate[record.podName]?.status ===
									'Migrating' ||
								controlledOperationDisabled('maintenance', lock)
							}
							onClick={() => {
								WorkOrderFuc(
									() => {
										if (
											record.resources.provisioner !==
												'hspc.csi.hitachi.com' &&
											record.resources.provisioner !==
												'kubernetes.io/glusterfs' &&
											record.resources.provisioner !==
												'nfs-client-provisioner-default'
										) {
											Modal.confirm({
												title: '操作确认',
												content: (
													<>
														<p>
															进行基于本地存储的主机迁移时，会删除相关存储
														</p>
														<p>
															是否确定进行主机迁移？
														</p>
													</>
												),
												onOk: () => {
													history.push(
														`/project/${type}/${name}/${aliasName}/container/hostConfig/${clusterId}/${namespace}/${middlewareName}/operatorAbility/${
															record.podName
														}/${
															record.zone ||
															'none'
														}/${
															record.nodeZone ||
															null
														}/${record.nodeName}`
													);
												}
											});
										} else {
											history.push(
												`/project/${type}/${name}/${aliasName}/container/hostConfig/${clusterId}/${namespace}/${middlewareName}/operatorAbility/${
													record.podName
												}/${record.zone || 'none'}/${
													record.nodeZone || null
												}/${record.nodeName}`
											);
										}
									},
									lock,
									middlewareName,
									nodeMigrationOperatorId,
									history,
									type,
									name,
									aliasName,
									clusterId,
									namespace
								);
							}}
						>
							节点迁移
						</LinkButton>
					</div>
				) : (
					<Tooltip
						title={
							record.role === 'sentinel' ||
							record.role === 'proxy'
								? '该类节点不支持此功能'
								: '当前存储不支持此功能'
						}
					>
						<LinkButton
							disabled
							title={
								record.role === 'sentinel' ||
								record.role === 'proxy'
									? '该类节点不支持此功能'
									: '当前存储不支持此功能'
							}
						>
							节点迁移
						</LinkButton>
					</Tooltip>
				)}
			</Actions>
		);
	};

	const roleRender = (value: string, record: PodItem, index: number) => {
		if (record.podName.includes('exporter')) {
			return 'Exporter';
		} else {
			if (name === 'elasticsearch') {
				if (record.podName.includes('kibana')) {
					return 'Kibana';
				} else if (record.podName.includes('client')) {
					return '协调节点';
				} else if (record.podName.includes('master')) {
					return '主节点';
				} else if (record.podName.includes('data')) {
					return '数据节点';
				} else if (record.podName.includes('cold')) {
					return '冷节点';
				} else {
					return value
						? value.substring(0, 1).toUpperCase() +
								value.substring(1)
						: '/';
				}
			} else {
				switch (value) {
					case 'master':
						return '主节点';
					case 'slave':
						return name === 'mysql' || name === 'postgresql'
							? '异步节点'
							: '从节点';
					case 'data':
						return '数据节点';
					case 'client':
						return '协调节点';
					case 'cold':
						return '冷节点';
					case 'kibana':
						return 'Kibana';
					case 'nameserver':
						return 'Nameserver';
					case 'exporter':
						return 'Exporter';
					case 'sentinel':
						return '哨兵';
					case 'proxy':
						return '代理';
					case 'syncslave':
						return '同步节点';
					case 'sync_slave':
						return '同步节点';
					case 'default':
						return '/';
					default:
						if (value?.includes('shard')) {
							return '分片';
						} else {
							return value
								? value.substring(0, 1).toUpperCase() +
										value.substring(1)
								: '/';
						}
				}
			}
		}
	};

	const resourceRender = (value: string, record: PodItem, index: number) => {
		return `${formatNumber(record.resources.cpu)}C/${formatNumber(
			record.resources.memory
		)}G`;
	};

	const storageRender = (value: string, record: PodItem, index: number) => {
		return `${
			record.storageResources
				?.map(
					(item) =>
						`${item.storageClassQuota}(${item.storageClassName})`
				)
				.join(', ') || '无(无)'
		}`;
	};

	const createTimeRender = (value: string) => {
		return transTime.gmt2local(value);
	};
	const podNameRender = (value: string) => {
		return (
			<div
				style={{ width: '150px', wordBreak: 'break-all' }}
				title={value}
			>
				{value}
			</div>
		);
	};
	const nodeNameRender = (value: string, record: PodItem) => {
		return (
			<span>
				{value}
				{record.nodeZone ? `(${record.nodeZone})` : ''}
			</span>
		);
	};
	const statusRender = (value: string, record: any) => {
		if (
			migrate[record.podName] &&
			migrate[record.podName].status !== 'MigrateSucceed'
		) {
			return migrate[record.podName].status !== 'MigrateFailed' ? (
				migrate[record.podName].status
			) : (
				<p style={{ display: 'flex', alignItems: 'center' }}>
					{value}
					<Tooltip
						color="#ffffff"
						overlayStyle={{ width: 'auto' }}
						overlayInnerStyle={{ color: '#000000' }}
						title={
							<div>
								<p>
									<CloseCircleFilled
										style={{
											color: '#ff4d4f',
											marginRight: 4
										}}
									/>
									迁移失败
								</p>
								<p style={{ display: 'flex' }}>
									<span>失败原因：</span>
									{migrate[record.podName].reason}
								</p>
								<p>
									<span>上次迁移时间：</span>
									{migrate[record.podName].migrateTimestamp}
								</p>
								<Button
									size="small"
									type="primary"
									style={{ marginTop: 8 }}
									onClick={() =>
										deleteMigrate({
											clusterId,
											namespace,
											middlewareName,
											mtName: migrate[record.podName]
												.mtName
										}).then((res) => {
											if (res.success) {
												const sendData: PodSendData = {
													clusterId,
													namespace,
													middlewareName,
													type: name
												};
												getPodList(sendData);
												getMigrateStatus();
											}
										})
									}
								>
									确定
								</Button>
							</div>
						}
					>
						<span style={{ cursor: 'pointer' }}>
							<ExclamationCircleFilled
								style={{
									color: '#faad14',
									marginLeft: 4
								}}
							/>
						</span>
					</Tooltip>
				</p>
			);
		} else {
			return value;
		}
	};

	const restartRender = (value: number, record: PodItem, index: number) => {
		return `${value}(${
			transTime.gmt2local(record.lastRestartTime) || '无'
		})`;
	};

	return (
		<ProTable
			dataSource={pods}
			operation={Operation}
			rowKey="podName"
			scroll={{ x: 1490 }}
			loading={loading}
		>
			<ProTable.Column
				title="实例名称"
				dataIndex="podName"
				render={podNameRender}
				width={170}
				fixed="left"
			/>
			<ProTable.Column
				title="状态"
				dataIndex="status"
				width={120}
				render={statusRender}
			/>
			<ProTable.Column title="实例 IP" dataIndex="podIp" width={150} />
			<ProTable.Column
				title="所在主机"
				dataIndex="nodeName"
				width={220}
				render={nodeNameRender}
			/>
			<ProTable.Column
				title="节点类型"
				dataIndex="role"
				render={roleRender}
				width={100}
			/>
			<ProTable.Column
				title="节点资源"
				dataIndex="resource"
				render={resourceRender}
				width={100}
			/>
			<ProTable.Column
				title="节点存储"
				dataIndex="storage"
				ellipsis
				render={storageRender}
				width={160}
			/>
			<ProTable.Column
				title="创建时间"
				dataIndex="createTime"
				render={createTimeRender}
				width={160}
			/>
			<ProTable.Column
				title="异常重启次数(最近时间)"
				dataIndex="restartCount"
				render={restartRender}
				width={180}
			/>
			<ProTable.Column
				title="操作"
				render={actionRender}
				width={80}
				fixed="right"
			/>
		</ProTable>
	);
}

import React, { useState, useEffect } from 'react';
import ProTable from '@/components/ProTable';
import { Button, Modal, notification } from 'antd';
import AddAgent from './addAgent';
import { DetailParams } from '../../detail';
import Actions from '@/components/Actions';
import { deleteHba, getHba } from '@/services/operatorPanel';
import { nullRender } from '@/utils/utils';
import { useHistory, useParams } from 'react-router';
import { maintenances } from '@/utils/const';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';

interface AgentAuthenticationProps {
	lock: string;
	serviceRefresh: () => void;
}

const LinkButton = Actions.LinkButton;
const AgentAuthentication = (props: AgentAuthenticationProps): JSX.Element => {
	const history = useHistory();
	const params: DetailParams = useParams();
	const { lock, serviceRefresh } = props;
	const {
		clusterId,
		namespace,
		middlewareName,
		name,
		chartVersion,
		aliasName,
		type
	} = params;
	const [data, setData] = useState([]);
	const [record, setRecord] = useState();
	const [loading, setLoading] = useState<boolean>(false);
	const [visible, setVisible] = useState<boolean>(false);
	const [selectedRows, setSelectRows] = useState<any[]>();
	const [selectedRowKeys, setSelectRowKeys] = useState<React.Key[]>([]);
	const agentAuthenticationOperatorId =
		maintenances['【Postgresql】 Agent Authentication'];

	useEffect(() => {
		getData();
	}, []);

	const getData = () => {
		setLoading(true);
		getHba({ clusterId, namespace, middlewareName }).then((res) => {
			setLoading(false);
			if (res.success) {
				setData(
					res.data.map((item: any) => ({
						id: Math.random(),
						...item
					}))
				);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};

	const deleteAgent = (record: any) => {
		Modal.confirm({
			title: '操作确认',
			content: (
				<>
					<p>删除客户端认证信息后无法恢复</p>
					<p>是否确认删除？</p>
				</>
			),
			onOk: async () => {
				await ExecuteOrderFuc();
				deleteHba({
					clusterId,
					namespace,
					middlewareName,
					chartName: name,
					chartVersion,
					configs: [
						{
							newHba: {
								...record
							}
						}
					]
				}).then((res) => {
					if (res.success) {
						getData();
						serviceRefresh();
						notification.success({
							message: '成功',
							description: '删除成功'
						});
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};

	const deleteAgents = () => {
		const configs = selectedRows?.map((item) => {
			return { newHba: { ...item } };
		});
		Modal.confirm({
			title: '操作确认',
			content: (
				<>
					<p>删除客户端认证信息后无法恢复</p>
					<p>是否确认删除？</p>
				</>
			),
			onOk: async () => {
				await ExecuteOrderFuc();
				deleteHba({
					clusterId,
					namespace,
					middlewareName,
					chartName: name,
					chartVersion,
					configs
				}).then((res) => {
					if (res.success) {
						getData();
						serviceRefresh();
						setSelectRows([]);
						notification.success({
							message: '成功',
							description: '删除成功'
						});
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};

	const Operation = {
		primary: (
			<>
				{selectedRows?.length ? (
					<Button
						type="primary"
						danger
						onClick={() => {
							WorkOrderFuc(
								() => {
									deleteAgents();
								},
								lock,
								middlewareName,
								agentAuthenticationOperatorId,
								history,
								type,
								name,
								aliasName,
								clusterId,
								namespace
							);
						}}
					>
						批量删除
					</Button>
				) : null}
				<Button
					type="primary"
					onClick={() => {
						WorkOrderFuc(
							() => {
								setRecord(undefined);
								setVisible(true);
							},
							lock,
							middlewareName,
							agentAuthenticationOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
				>
					新增
				</Button>
			</>
		)
	};

	const rowSelection = {
		selectedRowKeys: selectedRowKeys,
		onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
			setSelectRows(selectedRows);
			setSelectRowKeys(selectedRowKeys);
		}
	};

	const actionRender = (value: string, record: any) => {
		return (
			<Actions>
				<LinkButton
					onClick={() => {
						WorkOrderFuc(
							() => {
								setRecord(record);
								setVisible(true);
							},
							lock,
							middlewareName,
							agentAuthenticationOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
				>
					编辑
				</LinkButton>
				<LinkButton
					onClick={() => {
						WorkOrderFuc(
							() => {
								deleteAgent(record);
							},
							lock,
							middlewareName,
							agentAuthenticationOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
				>
					删除
				</LinkButton>
			</Actions>
		);
	};

	return (
		<>
			<ProTable
				rowKey="id"
				dataSource={data}
				loading={loading}
				operation={Operation}
				rowSelection={{
					...rowSelection
				}}
			>
				<ProTable.Column title="类型" dataIndex="type" />
				<ProTable.Column title="数据库" dataIndex="database" />
				<ProTable.Column title="用户" dataIndex="user" />
				<ProTable.Column
					title="地址"
					dataIndex="address"
					render={nullRender}
				/>
				<ProTable.Column title="认证方式" dataIndex="method" />
				<ProTable.Column
					title="认证选项"
					dataIndex="option"
					render={nullRender}
				/>
				<ProTable.Column title="操作" render={actionRender} />
			</ProTable>
			{visible && (
				<AddAgent
					data={record}
					visible={visible}
					clusterId={clusterId}
					namespace={namespace}
					middlewareName={middlewareName}
					chartName={name}
					chartVersion={chartVersion}
					onOk={() => {
						getData();
						serviceRefresh();
						setVisible(false);
					}}
					onCancel={() => setVisible(false)}
				/>
			)}
		</>
	);
};

export default AgentAuthentication;

import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, Select, notification } from 'antd';
import {
	formItemLayout420,
	authenticationType,
	authenticationMode
} from '@/utils/const';
import { addHba, editHba } from '@/services/operatorPanel';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';

interface AddAgentProps {
	data: any;
	visible: boolean;
	clusterId: string;
	namespace: string;
	middlewareName: string;
	chartName: string;
	chartVersion: string;
	onOk: () => void;
	onCancel: () => void;
}

const AddAgent = (props: AddAgentProps): JSX.Element => {
	const {
		data,
		clusterId,
		namespace,
		middlewareName,
		chartName,
		chartVersion,
		visible,
		onOk,
		onCancel
	} = props;
	const [form] = Form.useForm();
	const [type, setType] = useState<string>();
	const [buttonLoading, setButtonLoading] = useState<boolean>(false);

	useEffect(() => {
		if (data) {
			setType(data.type);
			form.setFieldsValue(data);
		}
	}, []);

	useEffect(() => {
		type === 'local' && form.setFieldValue('address', '');
	}, [type]);

	const handleSubmit = () => {
		form.validateFields().then(async (values) => {
			let sendData: any = {
				clusterId,
				namespace,
				middlewareName,
				chartName,
				chartVersion,
				configs: [
					{
						newHba: {
							...values
						}
					}
				]
			};
			setButtonLoading(true);
			await ExecuteOrderFuc();
			if (data) {
				sendData = {
					...sendData,
					configs: [
						{
							oldHba: {
								...data
							},
							newHba: {
								...values
							}
						}
					]
				};
				editHba(sendData).then((res) => {
					setButtonLoading(false);
					if (res.success) {
						onOk();
						notification.success({
							message: '成功',
							description: '修改成功'
						});
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			} else {
				addHba(sendData).then((res) => {
					setButtonLoading(false);
					if (res.success) {
						onOk();
						notification.success({
							message: '成功',
							description: '添加成功'
						});
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};

	return (
		<Modal
			title={`${data ? '编辑' : '新增'}客户端认证`}
			open={visible}
			onOk={handleSubmit}
			onCancel={onCancel}
			okButtonProps={{ loading: buttonLoading }}
		>
			<Form form={form} labelAlign="left" {...formItemLayout420}>
				<Form.Item
					label="类型"
					name="type"
					rules={[
						{
							required: true,
							message: '请选择类型'
						}
					]}
				>
					<Select
						placeholder="请选择类型"
						value={type}
						onChange={(value) => setType(value)}
						options={authenticationType}
					/>
				</Form.Item>
				<Form.Item
					label="数据库"
					name="database"
					rules={[
						{
							required: true,
							message: '请输入数据库'
						}
					]}
				>
					<Input placeholder="请输入数据库" />
				</Form.Item>
				<Form.Item
					label="用户"
					name="user"
					rules={[
						{
							required: true,
							message: '请输入用户'
						}
					]}
				>
					<Input placeholder="请输入用户" />
				</Form.Item>
				<Form.Item
					label="地址"
					name="address"
					rules={[
						{
							required: type !== 'local' ? true : false,
							message: '请输入地址'
						}
					]}
				>
					<Input
						placeholder="请输入地址"
						disabled={type === 'local'}
					/>
				</Form.Item>
				<Form.Item
					label="认证方式"
					name="method"
					rules={[
						{
							required: true,
							message: '请选择认证方式'
						}
					]}
				>
					<Select
						placeholder="请选择认证方式"
						options={authenticationMode}
					/>
				</Form.Item>
				<Form.Item label="认证选项" name="option">
					<Input placeholder="请输入认证选项，多个认证选项用英文逗号分隔" />
				</Form.Item>
			</Form>
		</Modal>
	);
};

export default AddAgent;

import React, { useEffect, useState } from 'react';
import { Table, notification, Modal, Alert } from 'antd';
import Actions from '@/components/Actions';
import { getAppVersion, upgradeApp } from '@/services/middleware';
import { DetailParams } from '../detail';
import { useHistory, useParams } from 'react-router';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';
import { controlledOperationDisabled } from '@/utils/utils';
const LinkButton = Actions.LinkButton;
const { confirm } = Modal;
interface AppVersionItem {
	version: string;
	status: string;
	type: string;
}
enum appVersionStatus {
	now = '当前版本',
	future = '可升级版本',
	history = '旧版本'
}
export default function MiddlewareVersion({
	lock,
	serviceRefresh
}: {
	lock: string;
	serviceRefresh: () => void;
}): JSX.Element {
	const history = useHistory();
	const params: DetailParams = useParams();
	const {
		clusterId,
		namespace,
		middlewareName,
		name,
		chartVersion,
		aliasName,
		type
	} = params;
	const [loading, setLoading] = useState<boolean>(false);
	const [dataSource, setDataSource] = useState<AppVersionItem[]>([]);
	const updateMiddlewareVersionOperatorId =
		maintenances['Middleware Version Upgrade'];
	useEffect(() => {
		getData();
	}, []);
	const getData = () => {
		setLoading(true);
		getAppVersion({
			clusterId,
			namespace,
			type: name,
			middlewareName
		})
			.then((res) => {
				if (res.success) {
					const list = res.data.map((item: any) => {
						item.type = aliasName;
						return item;
					});
					setDataSource(list);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const versionStatusRender = (value: string) => {
		const color =
			value === 'now'
				? '#00A7FA'
				: value === 'future'
				? '#52C41A'
				: '#666666';
		const bgColor =
			value === 'now'
				? '#EBF8FF'
				: value === 'future'
				? '#F6FFED'
				: '#F5F5F5';
		return (
			<div
				className="version-status-display"
				style={{
					color: color,
					backgroundColor: bgColor,
					borderColor: color
				}}
			>
				{appVersionStatus[value]}
			</div>
		);
	};
	const updateVersion = (record: AppVersionItem) => {
		let message = `是否确定${
			record.status === 'history' ? '降级' : '升级'
		}到该版本？`;
		if (name === 'mysql' && record.version.charAt(0) === '8') {
			message = `升级到MySQL8.x版本后将无法降级，是否确定升级到该版本？`;
		}
		confirm({
			title: '操作确认',
			content: message,
			onOk: async () => {
				await ExecuteOrderFuc();
				return upgradeApp({
					clusterId,
					namespace,
					type: name,
					middlewareName,
					chartName: name,
					chartVersion,
					upgradeAppVersion: record.version
				}).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: `中间件版本${
								record.status === 'history' ? '降级' : '升级'
							}成功`
						});
						serviceRefresh();
						getData();
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};
	const actionRender = (value: string, record: AppVersionItem) => {
		return (
			<Actions>
				{(record.status === 'now' || record.status === 'future') && (
					<LinkButton
						disabled={
							record.status === 'now' ||
							controlledOperationDisabled('maintenance', lock)
						}
						onClick={() => {
							WorkOrderFuc(
								() => {
									updateVersion(record);
								},
								lock,
								middlewareName,
								updateMiddlewareVersionOperatorId,
								history,
								type,
								name,
								aliasName,
								clusterId,
								namespace
							);
						}}
					>
						升级
					</LinkButton>
				)}
				{record.status === 'history' && (
					<LinkButton
						disabled={controlledOperationDisabled(
							'maintenance',
							lock
						)}
						onClick={() => {
							WorkOrderFuc(
								() => {
									updateVersion(record);
								},
								lock,
								middlewareName,
								updateMiddlewareVersionOperatorId,
								history,
								type,
								name,
								aliasName,
								clusterId,
								namespace
							);
						}}
					>
						降级
					</LinkButton>
				)}
			</Actions>
		);
	};
	return (
		<>
			{name === 'postgresql' && (
				<Alert
					type="warning"
					message="升级PostgreSQL版本后可能会导致服务不可用，请谨慎操作！"
					closable
				/>
			)}
			<div className="mt-8">
				<Table loading={loading} dataSource={dataSource}>
					<Table.Column dataIndex="type" title="中间件类型" />
					<Table.Column dataIndex="version" title="版本号" />
					<Table.Column
						dataIndex="status"
						title="当前状态"
						render={versionStatusRender}
					/>
					<Table.Column
						dataIndex="action"
						title="操作"
						render={actionRender}
					/>
				</Table>
			</div>
		</>
	);
}

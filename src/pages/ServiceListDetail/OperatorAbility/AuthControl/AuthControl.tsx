import React, { useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router';
import { Switch, Modal, notification } from 'antd';
import { EditOutlined, InfoCircleOutlined } from '@ant-design/icons';
import DataFields from '@/components/DataFields';
import { rocketMQAccount } from '@/components/RocketACLForm/acl';
import { updateMiddleware } from '@/services/middleware';
import { AuthControlProps, DetailParams } from '../../detail';
import RocketAclEditForm from './rocketAclEditForm';
import RocketAclUserInfo from './rocketAclUserInfo';
import { WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';
import { controlledOperationDisabled } from '@/utils/utils';
const globalIpsItems = [
	{ dataIndex: 'accessKey', label: '账户' },
	{ dataIndex: 'globalIps', label: '全局IP白名单' }
];
const { confirm } = Modal;
export default function AuthControl(props: AuthControlProps): JSX.Element {
	const { serviceRefresh, acl, lock } = props;
	const {
		clusterId,
		namespace,
		name,
		middlewareName,
		chartVersion,
		type,
		aliasName
	}: DetailParams = useParams();
	const history = useHistory();
	const aclOperatorId = maintenances['【RocketMQ】Access Control'];
	const [aclCheck, setACLCheck] = useState<boolean>(false);
	const [globalIps, setGlobalIps] = useState({
		globalIps: ''
	});
	const [visible, setVisible] = useState<boolean>(false); // * 修改acl
	useEffect(() => {
		if (acl) {
			setACLCheck(acl.enable);
			setGlobalIps({
				globalIps: acl.globalWhiteRemoteAddresses || ''
			});
		} else {
			setACLCheck(false);
		}
	}, [acl]);
	const aclSwitchChange = (checked: boolean) => {
		confirm({
			title: '提示',
			content: checked
				? '请确定是否开启访问权限控制认证'
				: '请确定是否关闭访问权限控制认证',
			onOk: async () => {
				if (!checked) {
					const sendData = {
						clusterId,
						namespace,
						type: name,
						chartName: name,
						chartVersion: chartVersion,
						middlewareName: middlewareName,
						rocketMQParam: {
							acl: {
								enable: false
							}
						}
					};
					const res = await updateMiddleware(sendData);
					if (res.success) {
						notification.success({
							message: '成功',
							description:
								'访问权限控制认证关闭成功,3秒后刷新页面'
						});
						setACLCheck(checked);
						setTimeout(() => {
							serviceRefresh();
						}, 3000);
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				} else {
					setACLCheck(checked);
				}
			}
		});
	};
	const onCancel = (value: boolean) => {
		setVisible(false);
		if (value) {
			setTimeout(() => {
				serviceRefresh();
			}, 3000);
		}
	};
	return (
		<>
			<div className="rocketmq-acl-field-content">
				<div className="rocketmq-acl-field-title">
					<div className="rocketmq-acl-left">
						<div className="rocketmq-acl-blue-line"></div>
						<span>
							访问权限控制认证(
							{acl?.enable ? '已开启' : '已关闭'})
						</span>
						{aclCheck && (
							<span
								className="name-link ml-12"
								onClick={() => {
									if (
										!controlledOperationDisabled(
											'maintenance',
											lock
										)
									) {
										WorkOrderFuc(
											() => {
												setVisible(true);
											},
											lock,
											middlewareName,
											aclOperatorId,
											history,
											type,
											name,
											aliasName,
											clusterId,
											namespace
										);
									}
								}}
								style={
									controlledOperationDisabled(
										'maintenance',
										lock
									)
										? {
												color: '#ccc',
												cursor: 'not-allowed'
										  }
										: { lineHeight: '20px' }
								}
							>
								<EditOutlined
									style={{
										marginRight: 8,
										verticalAlign: 'middle'
									}}
								/>
								编辑
							</span>
						)}
					</div>
					<div className="rocketmq-acl-right">
						<span className="acl-title-flag">
							{aclCheck ? '已开启' : '已关闭'}
							<Switch
								style={{
									marginLeft: 16,
									verticalAlign: 'middle'
								}}
								size="small"
								checked={aclCheck}
								disabled={controlledOperationDisabled(
									'maintenance',
									lock
								)}
								onChange={(checked: boolean) => {
									WorkOrderFuc(
										() => {
											aclSwitchChange(checked);
										},
										lock,
										middlewareName,
										aclOperatorId,
										history,
										type,
										name,
										aliasName,
										clusterId,
										namespace
									);
								}}
							/>
						</span>
					</div>
				</div>
				{aclCheck && (
					<DataFields dataSource={globalIps} items={globalIpsItems} />
				)}
			</div>
			{aclCheck &&
				acl?.rocketMQAccountList.map((item: rocketMQAccount) => {
					return (
						<RocketAclUserInfo key={item.accessKey} data={item} />
					);
				})}
			{!aclCheck && (
				<div className="rocketmq-no-acl">
					<InfoCircleOutlined style={{ marginRight: 8 }} />
					访问权限控制认证已关闭
				</div>
			)}
			{visible && (
				<RocketAclEditForm
					visible={visible}
					onCancel={onCancel}
					data={acl}
					clusterId={clusterId}
					namespace={namespace}
					middlewareName={middlewareName}
					chartVersion={chartVersion}
					chartName={name}
				/>
			)}
		</>
	);
}

import React, { useEffect, useState } from 'react';
import { Alert, Modal, notification } from 'antd';
import StorageSlider from '@/components/StorageSlider';
import { getNamespaceQuota } from '@/services/home';
import { scalePVCStorage } from '@/services/middleware';
import { formatNumber } from '@/utils/utils';
import { PVCItem } from '../../detail';
import './index.less';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';

interface StorageDilatationProps {
	open: boolean;
	onCancel: () => void;
	clusterId: string;
	data: PVCItem;
	namespace: string;
	middlewareName: string;
	onRefresh: () => void;
	type: string;
}
export default function StorageDilatation(
	props: StorageDilatationProps
): JSX.Element {
	const {
		open,
		onCancel,
		clusterId,
		namespace,
		data,
		middlewareName,
		onRefresh,
		type
	} = props;
	const [storageMax, setStorageMax] = useState<number>(0);
	const [storageResidue, setStorageResidue] = useState<number>(0);
	const [targetStorage, setTargetStorage] = useState<number>(data.storage);
	useEffect(() => {
		getNamespaceQuota({
			clusterId,
			namespace,
			storageClass: data.storageClass
		}).then((res) => {
			if (res.success) {
				setStorageMax(
					formatNumber(
						(res.data.storageList[0]?.storage.request || 0) -
							(res.data.storageList[0]?.storage.used || 0) +
							data.storage
					) as number
				);
				setStorageResidue(
					formatNumber(
						(res.data.storageList[0]?.storage.request || 0) -
							(res.data.storageList[0]?.storage.used || 0)
					) as number
				);
			}
		});
	}, []);
	const onOk = async () => {
		await ExecuteOrderFuc();
		scalePVCStorage({
			clusterId,
			namespace,
			middlewareName: middlewareName,
			pvcName: data.pvcName,
			storage: data.storage,
			storageClass: data.storageClass,
			targetStorage: targetStorage,
			type: type
		}).then((res) => {
			if (res.success) {
				notification.success({
					message: ' 成功',
					description: '该存储正在扩容中...'
				});
				onRefresh();
				onCancel();
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const handleChange = (value: number) => {
		setTargetStorage(Number(value));
	};
	return (
		<Modal
			title="存储扩容"
			open={open}
			onCancel={onCancel}
			onOk={onOk}
			width={420}
			okButtonProps={{
				disabled: targetStorage <= data.storage,
				title: '当前不支持存储缩容'
			}}
		>
			<Alert
				message="当前操作会导致服务暂时不可用"
				type="warning"
				showIcon
				style={{ marginBottom: 24 }}
			/>
			<div className="storage-dilatation-target-capacity">
				<div className="storage-dilatation-title">目标容量</div>
				<StorageSlider
					max={storageMax}
					min={data.storage}
					residue={storageResidue}
					handleChange={handleChange}
				/>
			</div>
		</Modal>
	);
}

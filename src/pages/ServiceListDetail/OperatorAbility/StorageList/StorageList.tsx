import React, { useEffect, useState } from 'react';
import { notification, Modal } from 'antd';
import moment from 'moment';
import Actions from '@/components/Actions';
import ProTable from '@/components/ProTable';
import {
	deletePvcStorage,
	forceDeleteStorage,
	getMiddlewarePVC
} from '@/services/middleware';
import { DetailParams, PVCItem } from '../../detail';
import StorageDilatation from './StorageDilatation';
import StorageDilatationIng from './StorageDilatationIng';
import StorageLog from './storageLog';
import storage from '@/utils/storage';
import useRefresh from '@/utils/useRefresh';
import { useHistory, useParams } from 'react-router';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';
import { controlledOperationDisabled } from '@/utils/utils';
import { ColumnFilterItem } from 'antd/lib/table/interface';
const LinkButton = Actions.LinkButton;
const { confirm } = Modal;
export default function StorageList({ lock }: { lock: string }): JSX.Element {
	const {
		clusterId,
		namespace,
		middlewareName,
		name,
		chartVersion,
		type,
		aliasName
	}: DetailParams = useParams();
	const history = useHistory();
	const [pvcs, setPVCs] = useState<PVCItem[]>([]);
	const [pvcLoading, setPvcLoading] = useState<boolean>(false);
	// * 存储扩容 feature 功能
	const [storageModifyAPI] = useState<boolean>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'storageModify')?.enabled ??
			true
	);
	// * 存储列表查看日志
	const [storageLogOpen, setStorageLogOpen] = useState<boolean>(false);
	// * 扩容
	const [storageDilatationOpen, setStorageDilatationOpen] =
		useState<boolean>(false);
	// * 扩容中
	const [storageDilatationIngOpen, setStorageDilatationIngOpen] =
		useState<boolean>(false);
	const [currentStorage, setCurrentStorage] = useState<PVCItem>();
	const [refreshKey, setRefreshKey] = useState<number>(0);
	const scaleStorageOperatorId = maintenances['Storage Expansion'];
	const deleteStorageOperatorId = maintenances['Storage Deletion'];
	const [podFilters, setPodFilters] = useState<ColumnFilterItem[]>([]);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		getPVCs();
	}, [refreshKey]);
	// * 获取中间件pvc列表信息
	const getPVCs = () => {
		setPvcLoading(true);
		setPVCs([]);
		getMiddlewarePVC({
			clusterId,
			namespace,
			type: name,
			middlewareName
		})
			.then((res) => {
				if (res.success) {
					setPVCs(res.data);
					const filter_temp = res.data.map((item: PVCItem) => {
						return {
							text: item.instancePod,
							value: item.instancePod
						};
					});
					setPodFilters(filter_temp);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setPvcLoading(false);
			});
	};
	const operation = {
		primary: (
			<div className="title-content">
				<div className="blue-line"></div>
				<div className="detail-title">存储列表</div>
			</div>
		)
	};
	const deleteStorage = (record: any) => {
		confirm({
			title: '操作确认',
			content: (
				<>
					<p>删除存储会导致存储内数据被清空且无法恢复</p>
					<p>是否确定删除存储？</p>
				</>
			),
			onOk: async () => {
				await ExecuteOrderFuc();
				return deletePvcStorage({
					clusterId,
					namespace,
					type: name,
					middlewareName,
					pvcName: record.pvcName
				}).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '存储删除成功'
						});
						getPVCs();
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};
	const forceDelete = (record: any) => {
		confirm({
			title: '操作确认',
			content:
				'强制删除存储会导致存储内数据被清空且无法恢复，是否确定删除存储？',
			onOk: async () => {
				await ExecuteOrderFuc();
				return forceDeleteStorage({
					clusterId,
					namespace,
					type: name,
					middlewareName,
					pvcName: record.pvcName
				}).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '强制删除成功'
						});
						getPVCs();
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};
	const storageActionRender = (value: any, record: any) => {
		return (
			<Actions>
				{storageModifyAPI && record.status === 'Bound' && (
					<LinkButton
						disabled={controlledOperationDisabled(
							'maintenance',
							lock
						)}
						onClick={() => {
							WorkOrderFuc(
								() => {
									setCurrentStorage(record);
									setStorageDilatationOpen(true);
								},
								lock,
								middlewareName,
								scaleStorageOperatorId,
								history,
								type,
								name,
								aliasName,
								clusterId,
								namespace
							);
						}}
					>
						扩容
					</LinkButton>
				)}
				{storageModifyAPI && record.status === 'scaleUpPv' && (
					<LinkButton
						disabled={controlledOperationDisabled(
							'maintenance',
							lock
						)}
						onClick={() => {
							setCurrentStorage(record);
							setStorageDilatationIngOpen(true);
						}}
					>
						扩容中
					</LinkButton>
				)}
				{storageModifyAPI && (
					<LinkButton
						onClick={() => {
							setCurrentStorage(record);
							setStorageLogOpen(true);
						}}
					>
						查看日志
					</LinkButton>
				)}
				{name === 'redis' &&
					!(
						record.status === 'Terminating' &&
						record.podNodeStatus === 'False'
					) && (
						<LinkButton
							title={
								!!record.masterPodPvc &&
								'主节点绑定存储无法删除'
							}
							disabled={
								!!record.masterPodPvc ||
								record.status === 'Terminating' ||
								controlledOperationDisabled('maintenance', lock)
							}
							onClick={() => {
								WorkOrderFuc(
									() => {
										deleteStorage(record);
									},
									lock,
									middlewareName,
									deleteStorageOperatorId,
									history,
									type,
									name,
									aliasName,
									clusterId,
									namespace
								);
							}}
						>
							删除
						</LinkButton>
					)}
				{name === 'redis' &&
					record.status === 'Terminating' &&
					record.podNodeStatus === 'False' && (
						<LinkButton
							disabled={controlledOperationDisabled(
								'maintenance',
								lock
							)}
							onClick={() => {
								WorkOrderFuc(
									() => {
										forceDelete(record);
									},
									lock,
									middlewareName,
									deleteStorageOperatorId,
									history,
									type,
									name,
									aliasName,
									clusterId,
									namespace
								);
							}}
						>
							强制删除
						</LinkButton>
					)}
			</Actions>
		);
	};
	return (
		<>
			<ProTable
				rowKey="pvcName"
				dataSource={pvcs}
				operation={operation}
				loading={pvcLoading}
			>
				<ProTable.Column title="存储名称" dataIndex="pvcName" />
				<ProTable.Column title="状态" dataIndex="status" />
				<ProTable.Column title="访问策略" dataIndex="accessModes" />
				<ProTable.Column title="存储大小" dataIndex="storage" />
				<ProTable.Column
					title="storageClass"
					dataIndex="storageClass"
				/>
				<ProTable.Column title="回收策略" dataIndex="reclaimPolicy" />
				<ProTable.Column
					title="绑定实例"
					dataIndex="instancePod"
					filters={podFilters}
					onFilter={(value: any, record: PVCItem) =>
						record.instancePod === value
					}
				/>
				<ProTable.Column
					title="创建时间"
					dataIndex="createTime"
					sorter={(a: any, b: any) =>
						moment(a.createTime).unix() -
						moment(b.createTime).unix()
					}
				/>
				{(storageModifyAPI || name === 'redis') && (
					<ProTable.Column
						title="操作"
						dataIndex="action"
						render={storageActionRender}
					/>
				)}
			</ProTable>
			{storageLogOpen && currentStorage && (
				<StorageLog
					open={storageLogOpen}
					onCancel={() => setStorageLogOpen(false)}
					data={currentStorage}
					imagePath={`${name}-${chartVersion}.svg`}
					clusterId={clusterId}
					namespace={namespace}
					middlewareName={middlewareName}
				/>
			)}
			{storageDilatationOpen && currentStorage && (
				<StorageDilatation
					open={storageDilatationOpen}
					onCancel={() => setStorageDilatationOpen(false)}
					clusterId={clusterId}
					namespace={namespace}
					middlewareName={middlewareName}
					data={currentStorage}
					onRefresh={getPVCs}
					type={name}
				/>
			)}
			{storageDilatationIngOpen && currentStorage && (
				<StorageDilatationIng
					open={storageDilatationIngOpen}
					onCancel={() => setStorageDilatationIngOpen(false)}
					clusterId={clusterId}
					namespace={namespace}
					middlewareName={middlewareName}
					data={currentStorage}
					onRefresh={getPVCs}
					type={name}
				/>
			)}
		</>
	);
}

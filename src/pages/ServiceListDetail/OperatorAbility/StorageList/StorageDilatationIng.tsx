import { <PERSON><PERSON>, Button, Modal, notification } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { connect } from 'react-redux';
import StorageSlider from '@/components/StorageSlider';
import { setPvcLog, cleanPvcLog, setPvcFinishLog } from '@/redux/log/log';
import { getNamespaceQuota } from '@/services/home';
import { scalePVCStorage } from '@/services/middleware';
import { PVCItem } from '../../detail';
import Socket from '@/services/websocket.js';
import { StoreState } from '@/types';
import './index.less';
const { confirm } = Modal;
interface StorageDilatationIngProps {
	open: boolean;
	onCancel: () => void;
	clusterId: string;
	data: PVCItem;
	namespace: string;
	middlewareName: string;
	onRefresh: () => void;
	setPvcLog: (value: any[]) => void;
	setPvcFinishLog: (value: any[]) => void;
	cleanPvcLog: () => void;
	pvcLog: any[];
	type: string;
}
function StorageDilatationIng(props: StorageDilatationIngProps): JSX.Element {
	const {
		open,
		onCancel,
		onRefresh,
		clusterId,
		namespace,
		data,
		middlewareName,
		setPvcLog,
		setPvcFinishLog,
		cleanPvcLog,
		pvcLog,
		type
	} = props;
	const [storageMax, setStorageMax] = useState<number>(0);
	const [storageResidue, setStorageResidue] = useState<number>(0);
	const [isReScale, setIsReScale] = useState<boolean>(true);
	const [targetStorage, setTargetStorage] = useState<number>(data.storage);
	const [isUpdateDisabled, setIsUpdateDisabled] = useState<boolean>(false);
	const ws = useRef<any>(null);
	useEffect(() => {
		getNamespaceQuota({
			clusterId,
			namespace,
			storageClass: data.storageClass
		}).then((res) => {
			if (res.success) {
				setStorageMax(
					(res.data.storageList[0]?.storage.request || 0) -
						(res.data.storageList[0]?.storage.used || 0) +
						data.storage
				);
				setStorageResidue(
					(res.data.storageList[0]?.storage.request || 0) -
						(res.data.storageList[0]?.storage.used || 0)
				);
			}
		});
	}, []);
	useEffect(() => {
		if (clusterId && namespace && middlewareName && data) {
			ws.current = new Socket({
				socketUrl: `/pvcScale?clusterId=${clusterId}&namespace=${namespace}&middlewareName=${middlewareName}&pvcName=${data.pvcName}`,
				timeout: 5000,
				socketMessage: (receive: any) => {
					if (JSON.parse(receive.data).text[0] === 'scale succeed') {
						setPvcFinishLog([
							...JSON.parse(receive.data).text,
							'扩容成功'
						]);
						setIsUpdateDisabled(true);
						setIsReScale(true);
					} else {
						setPvcLog([...JSON.parse(receive.data).text]);
					}
				},
				socketClose: (msg: any) => {
					cleanPvcLog();
				},
				socketError: () => {
					console.log('连接建立失败');
				},
				socketOpen: () => {
					console.log('连接建立成功');
					ws.current.sendMessage({ action: 'list pvc event' });
				}
			});
			try {
				ws.current.connection();
			} catch (e) {
				// * 捕获异常，防止js error
				console.log(e);
			}
			return () => {
				ws.current.onclose();
			};
		}
		return () => {
			cleanPvcLog();
		};
	}, [data, clusterId, namespace, middlewareName]);
	const handleChange = (value: number) => {
		setIsReScale(false);
		setTargetStorage(Number(value));
	};
	const reScalePvc = () => {
		confirm({
			title: '操作确认',
			content: '确认要重新扩容？',
			onOk: () => {
				ws.current.onclose();
				scalePVCStorage({
					clusterId,
					namespace,
					middlewareName: middlewareName,
					pvcName: data.pvcName,
					storage: data.storage,
					storageClass: data.storageClass,
					targetStorage: targetStorage,
					type: type
				}).then((res) => {
					if (res.success) {
						notification.success({
							message: ' 成功',
							description: '该存储正在重新扩容中...'
						});
						ws.current = new Socket({
							socketUrl: `/pvcScale?clusterId=${clusterId}&namespace=${namespace}&middlewareName=${middlewareName}&pvcName=${data.pvcName}`,
							timeout: 5000,
							socketMessage: (receive: any) => {
								if (
									JSON.parse(receive.data).text[0] ===
									'scale succeed'
								) {
									setPvcFinishLog([
										...JSON.parse(receive.data).text,
										'扩容成功'
									]);
									setIsUpdateDisabled(true);
									setIsReScale(true);
								} else {
									setPvcLog([
										...JSON.parse(receive.data).text
									]);
								}
							},
							socketClose: (msg: any) => {
								// cleanPvcLog();
							},
							socketError: () => {
								console.log('连接建立失败');
							},
							socketOpen: () => {
								console.log('连接建立成功');
								ws.current.sendMessage({
									action: 'list pvc event'
								});
							}
						});
						try {
							ws.current.connection();
						} catch (e) {
							// * 捕获异常，防止js error
							console.log(e);
						}
						return () => {
							ws.current.onclose();
						};
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			}
		});
	};
	const onOk = () => {
		onRefresh();
		onCancel();
	};
	return (
		<Modal
			title="存储扩容"
			open={open}
			onCancel={onOk}
			onOk={onOk}
			width={800}
		>
			<Alert
				message="当前操作会导致服务暂时不可用"
				type="warning"
				showIcon
				style={{ marginBottom: 24 }}
			/>
			<div className="storage-dilatation-ing-content">
				<div className="storage-dilatation-ing-target-capacity">
					<div className="storage-dilatation-ing-title">目标容量</div>
					<StorageSlider
						residue={storageResidue}
						min={data.capacity + 1}
						storageValue={data.storage}
						max={storageMax}
						handleChange={
							isUpdateDisabled ? undefined : handleChange
						}
					/>
				</div>
				<div className="storage-dilatation-ing-return-message">
					<div className="storage-dilatation-ing-title">返回信息</div>
					<div className="storage-dilatation-ing-message">
						<div className="storage-dilatation-ing-massage-content">
							{pvcLog.map((item: string, index: number) => {
								return <p key={index}>{item}</p>;
							})}
						</div>
						<Button
							type="primary"
							block
							disabled={isReScale}
							onClick={reScalePvc}
						>
							重新扩容
						</Button>
					</div>
				</div>
			</div>
		</Modal>
	);
}
const mapStateToProps = (state: StoreState) => ({
	pvcLog: state.log.pvcLog
});
export default connect(mapStateToProps, {
	setPvcLog,
	cleanPvcLog,
	setPvcFinishLog
})(StorageDilatationIng);

.storage-dilatation-ing-content {
	display: flex;
	.storage-dilatation-ing-target-capacity,
	.storage-dilatation-ing-return-message {
		flex: 1 1 50%;
		.storage-dilatation-ing-title {
			font-size: @font-2;
			font-weight: @font-weight;
			color: #333333;
			line-height: @line-height-2;
		}
		.storage-dilatation-ing-message {
			height: 240px;
			background: @white;
			border-radius: @border-radius;
			border: 1px solid #C0C6CC;
			padding: 8px;
			margin-top: 4px;
			.storage-dilatation-ing-massage-content {
				height: 190px;
				overflow-y: auto;
				font-size: @font-1;
				font-weight: 400;
				color: rgba(51,51,51,0.5);
				line-height: @line-height-1;
			}
		}
	}
}

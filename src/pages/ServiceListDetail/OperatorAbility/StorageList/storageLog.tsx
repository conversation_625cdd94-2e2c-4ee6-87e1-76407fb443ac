import React, { useEffect, useState } from 'react';
import { Drawer, Table } from 'antd';
import nodata from '@/assets/images/nodata.svg';
import { api } from '@/api.json';
import { getPVClog } from '@/services/middleware';
import { EventItem, PVCItem } from '../../detail';

interface StorageLogProps {
	open: boolean;
	onCancel: () => void;
	imagePath: string | null;
	data: PVCItem;
	clusterId: string;
	namespace: string;
	middlewareName: string;
}
export default function StorageLog(props: StorageLogProps): JSX.Element {
	const {
		open,
		onCancel,
		imagePath,
		data,
		clusterId,
		namespace,
		middlewareName
	} = props;
	const [eventList, setEventList] = useState<EventItem[]>([]);
	useEffect(() => {
		getPVClog({
			clusterId,
			namespace,
			pvcName: data.pvcName,
			middlewareName
		}).then((res) => {
			if (res.success) {
				setEventList(res.data);
			}
		});
	}, []);
	return (
		<Drawer
			title={
				<div className="icon-type-content">
					<img
						width={14}
						height={14}
						src={
							imagePath
								? `${api}/images/middleware/${imagePath}`
								: nodata
						}
					/>
					<div style={{ marginLeft: 8 }}>{data.pvcName}</div>
				</div>
			}
			placement="right"
			onClose={onCancel}
			open={open}
			width={600}
		>
			<Table rowKey="serviceName" dataSource={eventList}>
				<Table.Column dataIndex="type" title="类型" width={80} />
				<Table.Column
					dataIndex="reason"
					title="原因"
					ellipsis={true}
					width={150}
				/>
				<Table.Column
					dataIndex="message"
					title="提示信息"
					ellipsis={true}
				/>
			</Table>
		</Drawer>
	);
}

import React, { useEffect, useState } from 'react';
import { UnControlled as CodeMirror } from 'react-codemirror2';
import { Cascader, Col, Form, notification, Row, Spin } from 'antd';
import { useParams } from 'react-router';
import { getYaml, getYamlResource } from '@/services/middleware';
import 'codemirror/lib/codemirror.js';
import 'codemirror/lib/codemirror.css';
import 'codemirror/mode/css/css.js';
import 'codemirror/mode/yaml/yaml.js';
import 'codemirror/mode/yaml-frontmatter/yaml-frontmatter.js';
import 'codemirror/theme/twilight.css';
import 'codemirror/addon/selection/active-line';
import { formItemLayout618 } from '@/utils/const';
import { DetailParams } from '../detail';
import { filtersProps } from '@/types/comment';
import useRefresh from '@/utils/useRefresh';
const FormItem = Form.Item;

export default function YamlCheck(): JSX.Element {
	const { clusterId, namespace, middlewareName, name }: DetailParams =
		useParams();
	const [yamlValue, setYamlValue] = useState<string>('');
	const [options, setOptions] = useState<filtersProps[]>([]);
	const [optionValue, setOptionValue] = useState<string[]>();
	const [spinning, setSpinning] = useState<boolean>(false);
	const [loading, setLoading] = useState<boolean>(false);
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		setLoading(true);
		getYamlResource({
			clusterId,
			namespace,
			middlewareName,
			type: name
		})
			.then((res) => {
				if (res.success) {
					const list = res.data.map((item: any) => {
						const child = item.resourceNameList.map((i: string) => {
							return {
								value: i,
								label: i
							};
						});
						return {
							value: item.plural,
							label: item.plural,
							children: child
						};
					});
					setOptions(list);
					setOptionValue([
						list[0]?.value,
						list[0]?.children[0].value
					]);
					list.length &&
						getYamlValue(
							list[0]?.value,
							list[0]?.children[0]?.value
						);
				}
			})
			.finally(() => {
				setLoading(false);
			});
	}, [refreshKey]);
	const onChange = (value: any) => {
		setOptionValue(value);
		getYamlValue(value[0], value[1]);
	};
	const getYamlValue = (plural: string, name: string) => {
		setSpinning(true);
		getYaml({
			clusterId,
			namespace,
			plural,
			name
		})
			.then((res) => {
				if (res.success && res.data !== null) {
					setYamlValue(res.data);
				} else {
					setYamlValue('Something Error!');
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setSpinning(false);
			});
	};
	return (
		<>
			<Row>
				<Col span={12}>
					<FormItem
						labelAlign="left"
						{...formItemLayout618}
						label="选择YAML"
					>
						<Cascader
							value={optionValue}
							options={options}
							onChange={onChange}
							loading={loading}
							allowClear={false}
							placeholder="请选择实例和容器名称"
						/>
					</FormItem>
				</Col>
			</Row>
			<div className="yaml-check-title">YAML查看</div>
			<Spin spinning={spinning}>
				<div id="yaml-check-codemirror">
					<CodeMirror
						value={yamlValue}
						options={{
							mode: 'yaml',
							theme: 'twilight',
							lineNumbers: true,
							readOnly: true,
							lineWrapping: true
						}}
					/>
				</div>
			</Spin>
		</>
	);
}

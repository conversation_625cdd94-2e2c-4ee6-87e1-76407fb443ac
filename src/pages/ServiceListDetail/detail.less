.title-content {
	display: flex !important;
	min-height: 22px;
	align-items: center;
	margin-bottom: 8px;
	.blue-line {
		width: 1px;
		height: 14px;
		border: 1px solid @primary-color;
	}
	.detail-title {
		@include lineHeight(22px);
		font-size: @font-2;
		font-weight: @font-weight;
		color: @text-color-title;
		margin-left: 5px;
	}
}

// .next-tabs-bar{
// 	margin-bottom: 15px;
// }
.backup-setting {
	width: 50%;
	display: flex;
	color: @text-color-title;
	margin-bottom: 6px;
	font-size: @font-1;
	.backup-title {
		@include lineHeight(22px);
		width: 160px;
		font-size: @font-3;
		font-weight: @font-weight-lg;
		color: @text-color-title;
	}
	.backup-action {
		@include lineHeight(20px);
		color: @primary-color;
		cursor: pointer;
	}
}
.backup-display-content {
	display: flex;
	flex-wrap: wrap;
	.backup-setting {
		width: 50%;
		display: flex;
		color: @text-color-title;
		margin-bottom: 6px;
		font-size: @font-1;
		.backup-label {
			@include lineHeight(20px);
			width: 160px;
			font-size: @font-1;
			font-weight: @font-weight;
			color: #666666;
		}
		.backup-value {
			display: flex;
			align-items: center;
			span {
				margin-left: 12px;
			}
		}
	}
}
.use-backup-form-title {
	display: flex;
	margin-bottom: 12px;
	.blue-line {
		width: 1px;
		height: 14px;
		border: 1px solid @primary-color;
		margin-right: 8px;
	}
}
.password-content {
	display: flex;
	.password-display {
		height: 18px;
		font-size: @font-1;
		font-weight: @font-weight-sm;
		color: @text-color-title;
		line-height: @line-height-3;
	}
	.password-reset {
		margin-left: 24px;
	}
}
.switch-master {
	@include lineHeight(24px);
}
.config-ml {
	margin-left: 100px;
}
.refresh-btn {
	align-self: flex-end;
}
.pod-content {
	display: flex;
	height: 22px;
	align-items: center;
	margin-bottom: 8px;
	justify-content: space-between;
	.title-box {
		display: flex;
		height: 22px;
		align-items: center;
		margin-bottom: 8px;
		.blue-line {
			width: 1px;
			height: 14px;
			border: 1px solid @primary-color;
		}
		.detail-title {
			@include lineHeight(22px);
			font-size: @font-2;
			font-weight: @font-weight;
			color: @text-color-title;
			margin-left: 5px;
		}
	}
}
.details-go-back {
	&:hover {
		color: @primary-color;
	}
}
.balloon-text {
	line-height: @line-height-3;
}
.updated-value {
	display: inline-block;
	// max-width: 218px;
	color: #c80000;
	margin-right: 8px;
	text-overflow: ellipsis;
	overflow: hidden;
}
.before-update {
	display: inline-block;
	// max-width: 218px;
	color: @text-color-title;
	margin-right: 8px;
	text-overflow: ellipsis;
	overflow: hidden;
}

.parameter-content {
	display: flex;
	align-items: center;
}
.create-alarm-content {
	width: 150px;
	display: flex;
	align-items: center;
	margin-bottom: 16px;
	cursor: pointer;
	.create-alarm-text {
		color: @primary-color;
		margin-left: 8px;
		font-size: @font-1;
		font-weight: @font-weight-sm;
		@include lineHeight(16px);
	}
}
.create-alarm-form-layout {
	display: flex;
	align-items: center;
	& label {
		width: 88px;
		@include lineHeight(18px);
		font-size: @font-1;
		font-weight: @font-weight;
		color: @text-color-title;
	}
}
.create-alarm-form-hr {
	margin: 24px 0;
}
.create-alarm-form-del {
	margin-left: 33px;
}
.warning-info {
	margin: 0 24px 16px 24px;
	height: auto;
	background: #fff7d1;
	padding: 10px;
	border-radius: @border-radius;
	position: relative;
	& > .warning-icon {
		font-size: @font-2;
		margin-right: 10px;
		color: #fac800;
	}
	& > .info-text {
		font-size: @font-1;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: @font-weight-sm;
		color: @text-color-title;
		line-height: @line-height-2 * 2;
	}
	& > .warning-close {
		position: absolute;
		top: 10px;
		right: 10px;
		color: @text-color-title;
	}
}
.edit-icon {
	cursor: pointer;
	&:hover {
		color: @primary-color;
	}
}
.yaml-edit-content {
	height: 100%;
	.yaml-edit-console-header,
	.yaml-edit-header {
		width: 100%;
		height: 34px;
		background: #222222;
		margin-top: 16px;
		border-radius: 4px 4px 0 0;
		color: #bfbfbf;
		line-height: 34px;
		display: flex;
		padding-left: 18px;
		.yaml-edit-right-2,
		.yaml-edit-left-2 {
			width: 50%;
		}
		.yaml-edit-right-1 {
			width: 240px;
			padding-left: 12px;
			position: relative;
			&::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				height: 100%;
				border-left: 1px solid #404040;
			}
		}
		.yaml-edit-left-1 {
			width: calc(100% - 240px);
		}
	}
	.yaml-edit-code {
		height: 70%;
	}
	.yaml-edit-console-header {
		border-radius: 0;
		margin-top: 0px;
		padding-left: 18px;
	}
	.yaml-edit-console-footer {
		width: 100%;
		height: 30px;
		background-color: #141414;
		border-radius: 0 0 4px 4px;
		border-top: 1px solid #9f9797;
	}
	.yaml-edit-console-content {
		width: 100%;
		height: 115px;
		background-color: #141414;
		border-radius: 0 0 4px 4px;
		padding: 8px 12px;
		overflow: auto;
		.codeIDE-pre-code {
			margin: 0;
			padding: 0;
			border: 0;
			white-space: pre-wrap;
			word-wrap: break-word;
			color: #fff;
			background-color: transparent;
		}
	}
	.yaml-edit-editor-content {
		display: flex;
		height: 100%;
		.yaml-edit-editor-left {
			width: calc(100% - 240px);
			background-color: #141414;
			border-radius: 0 0 0 4px;
			height: calc(100% - 132px);
		}
		.yaml-edit-editor-right {
			width: 240px;
			background-color: #141414;
			height: calc(100% - 132px);
			border-left: 1px solid #404040;
			border-radius: 0 0 4px 0;
			padding: 8px 12px;
			overflow: auto;
			.codeIDE-pre-code {
				width: 100%;
				margin: 0;
				padding: 0;
				border: 0;
				white-space: pre-wrap;
				word-wrap: break-word;
				color: #fff;
				background-color: transparent;
			}
		}
	}
}
.zeus-edit-param-summit-btn {
	width: 100%;
	padding: 0px 16px 16px 0px;
	background: @white;
	& > button {
		margin-right: @margin;
	}
}
.zeus-edit-param-summit-btn-fix {
	position: fixed;
	z-index: 799;
	bottom: 0;
	padding: 16px 16px 16px 0px;
}
.zeus-edit-return-list {
	text-align: center;
}
.zeus-param-edit-table-content {
	margin-bottom: 16px;
}
.zeus-param-display-area-content {
	width: 442px;
	background: rgba(245, 245, 245, 0.5);
	border-radius: 2px;
	padding: 22px 56px;
	.zeus-param-display-info {
		& li {
			height: 18px;
			font-size: @font-1;
			font-weight: @font-weight-sm;
			color: @text-color-title;
			line-height: @line-height-3;
			margin-bottom: 16px;
			display: flex;
			text-align: left;
			& label {
				width: 80px;
				font-weight: @font-weight-lg;
			}
			& span {
				max-width: 210px;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
		}
	}
}
.ant-popover-message-title {
	padding-left: 0px !important;
}
.zeus-params {
	.ant-result-content {
		background-color: @white;
		display: flex;
		justify-content: center;
	}
}
.log-file-box-panel {
	width: 100%;
	.zeus-list-card-title {
		width: auto;
	}
	.zeus-list-card-subTitle {
		display: none;
	}
	.ant-collapse-content-box {
		display: flex;
		flex-wrap: wrap;
	}
}

// * 运维能力整合
.yaml-check-title {
	background: @text-color-title;
	color: #bfbfbf;
	padding: 6px 20px;
	border-radius: @border-radius-lg @border-radius-lg 0 0;
	&.border-radius-none{
		border-radius: 0;
	}
}
#yaml-check-codemirror{
	width: 100%;
	height: 100%;
	min-height: 650px;
	.react-codemirror2 {
		height: 100%;
		min-height: 650px;
		.CodeMirror {
		min-height: 650px;
			height: 100% !important;
		}
	}
}
.ms-switch-label {
	margin-right: 4px;
	font-weight: @font-weight;
	font-size: @font-1;
}
.instance-scale-content,
.ms-pod-content {
	width: 100%;
	min-height: 222px;
	margin-top: @margin;
	height: auto;
	background-color: @black-9;
	padding: @padding-lg;
	.ms-pod-last-time {
	}
	.ms-pod-change-icon {
		font-size: 46px;
		color: @black-5;
		margin: 0px 60px;
	}
	.ms-pod-master-box,
	.ms-pod-slave-box {
		display: flex;
		flex-direction: column;
		gap: 12px;
	}
	.ms-pod-box {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 12px;
	}
}
.instance-scale-content {
	display: flex;
	justify-content: center;
	align-items: center;
	flex-wrap: wrap;
	gap: 12px;
}
.scale-form-custom-form {
	margin-top: 16px;
	background: #EFEFEF;
	padding: 12px
}
.unselected-tag {
	padding: 0 7px;
	line-height: 20px;
	cursor: pointer;
	color: #000000;
	background-color: #fafafa;
	border: 1px solid #d9d9d9;
}
.selected-tag {
	padding: 0 7px;
	line-height: 20px;
	cursor: pointer;
	color: #096dd9;
	background-color: #e6f7ff;
	border: 1px solid #91d5ff;
}
.redis-tag-box {
	width: 1005;
	max-height: 100px;
	overflow-y: auto;
	background: @black-9;
	margin-bottom: @padding-sm;
	padding: @padding-sm;
	border-radius: @border-radius-lg;
}
.account-manage-content {
	position: relative;
	height: 100%;
	min-height: 350px;
	.account-manage-table {
		position: absolute;
		top: 0;
		background: rgba(245, 245, 245, 0.5);
		backdrop-filter: blur(2px);
		width: 100%;
		height: 100%;
		z-index: 1000;
		display: flex;
		justify-content: center;
		align-items: center;
		.account-manage-form-content {
			width: 500px;
			background: #ffffff;
			.account-manage-form-content-title {
				padding: 16px;
				font-size: @font-2;
				font-weight: @font-weight;
				color: @black-2;
				line-height: @line-height-2;
				border-bottom: 1px solid #f0f0f0;
			}
			.account-manage-form-content-footer,
			.account-manage-form-content-body {
				padding: 16px 16px 0px;
			}
			.account-manage-form-content-footer {
				padding: 16px;
				width: 100%;
				display: flex;
				justify-content: flex-end;
				border-top: 1px solid #f0f0f0;
			}
		}
	}
}

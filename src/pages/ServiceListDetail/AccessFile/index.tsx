import React, { useEffect, useState } from 'react';
import { Menu, Empty } from 'antd';
import SecondContent from '@/components/SecondContent';
import AccessFileDetail from './AccessFileDetail';
import AccessFileEdit from './AccessFileEdit';
import AccessFileEditHistory from './AccessFileEditHistory';
import storage from '@/utils/storage';
import DefaultPicture from '@/components/DefaultPicture';

const AccessFile = ({
	detail,
	subMenu
}: {
	detail: any;
	subMenu: any[];
}): JSX.Element => {
	const session_storage_selected_key = storage.getSession('configFileTab');
	const [notAuth, setNotAuth] = useState<boolean>(true);
	const [selectedKey, setSelectedKey] = useState<string[]>(
		session_storage_selected_key
			? [session_storage_selected_key]
			: ['fileDetail']
	);
	const [items, setItems] = useState([
		{
			label: '文件详情',
			key: 'fileDetail',
			code: 'confFileDetail'
		},
		{
			label: '文件编辑',
			key: 'fileEdit',
			code: 'confFileEdit'
		},
		{
			label: '修改历史',
			key: 'editHistory',
			code: 'confFileHistory'
		}
	]);
	useEffect(() => {
		if (detail?.capabilities?.includes('config')) {
			setNotAuth(false);
		} else {
			setNotAuth(true);
		}
	}, [detail]);
	useEffect(() => {
		const menu = items.filter((item: any) =>
			subMenu.find((menu: any) => menu.name === item.code)
		);
		setItems(menu);
		!storage.getSession('configFileTab') && setSelectedKey([menu[0].key]);
	}, [subMenu]);
	const ConsoleMenu = () => {
		return (
			<Menu
				selectedKeys={selectedKey}
				onClick={(info: any) => {
					setSelectedKey(info.keyPath);
					storage.setSession('configFileTab', info.key);
				}}
				style={{ height: '100%' }}
				items={items}
				mode="inline"
				className="serve-alarm-menu"
			/>
		);
	};
	const childrenRender = (key: string) => {
		switch (key) {
			case 'fileDetail':
				return <AccessFileDetail lock={detail?.lock} />;
			case 'fileEdit':
				return <AccessFileEdit lock={detail?.lock} />;
			case 'editHistory':
				return <AccessFileEditHistory lock={detail?.lock} />;
			default:
				return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />;
		}
	};
	if (notAuth) {
		return <DefaultPicture />;
	}
	return (
		<SecondContent menu={<ConsoleMenu />} style={{ margin: 0, padding: 0 }}>
			{childrenRender(selectedKey[0])}
		</SecondContent>
	);
};

export default AccessFile;

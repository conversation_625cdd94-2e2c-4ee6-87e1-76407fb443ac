import React, { useEffect, useState } from 'react';
import {
	<PERSON>ert,
	Col,
	Modal,
	Radio,
	RadioChangeEvent,
	Row,
	Table,
	notification
} from 'antd';
import { PodItem } from '../detail';
import { DEPLOY_MOD_SERVER } from '@/utils/const';
import {
	getAccessFilePaths,
	getPods,
	saveAndIssuedAccessFile
} from '@/services/middleware';
import { podRoleRender } from '@/utils/utils';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';

export default function AccessSaveAndIssued({
	open,
	onCancel,
	clusterId,
	namespace,
	middlewareName,
	type,
	curFile,
	onCreate
}: {
	open: boolean;
	onCancel: () => void;
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	curFile: string;
	onCreate: () => void;
}): JSX.Element {
	const [issuedMode, setIssuedMode] = useState<'checkbox' | 'radio'>('radio');
	const [podList, setPodList] = useState<PodItem[]>([]);
	const [loading, setLoading] = useState<boolean>(false);
	const [selectedPods, setSelectedPods] = useState<React.Key[]>([]);
	const [filePaths, setFilePaths] = useState<AccessFilePath[]>();
	const [okLoading, setOkLoading] = useState<boolean>(false);
	const [okDisabled, setOkDisabled] = useState<boolean>(true);
	useEffect(() => {
		setLoading(true);
		getAccessFilePaths({
			clusterId,
			namespace,
			middlewareName,
			checkExists: true
		}).then((res) => {
			if (res.success) {
				setFilePaths(res.data);
			}
		});
		getPods({
			clusterId,
			namespace,
			middlewareName,
			type,
			deployMod: DEPLOY_MOD_SERVER
		})
			.then((res) => {
				if (res.success) {
					setPodList(res.data?.pods || []);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	}, []);
	const columns = [
		{
			dataIndex: 'podAliasName',
			title: '实例名称',
			ellipsis: true
		},
		{
			dataIndex: 'status',
			title: '状态',
			ellipsis: true,
			width: 110
		},
		{
			dataIndex: 'role',
			title: '节点类型',
			width: 80,
			ellipsis: true,
			render: (value: any) => podRoleRender(value, type)
		},
		{
			dataIndex: 'createTime',
			title: '接入时间',
			width: 160,
			render: (value: any) => value || '/'
		}
	];
	const onChange = (e: RadioChangeEvent) => {
		setIssuedMode(e.target.value);
		setOkDisabled(true);
		setSelectedPods([]);
	};
	const rowSelection = {
		onChange: (selectedRowKeys: React.Key[], selectedRows: PodItem[]) => {
			if (selectedRowKeys?.length === 0) {
				setOkDisabled(true);
			} else {
				setOkDisabled(false);
			}
			setSelectedPods(selectedRowKeys);
		},
		getCheckboxProps: (record: PodItem) => ({
			disabled:
				!filePaths?.find(
					(item) => item.instanceName === record.podName
				) || record.status === 'ConnectFailed'
		})
	};
	const onOk = async () => {
		const configFileDtoListTemp = selectedPods.map((item) => {
			return {
				instanceName: item as string,
				content: curFile,
				charset: filePaths?.find((i) => i.instanceName === item)
					?.charset,
				path: filePaths?.find((i) => i.instanceName === item)?.path
			};
		});
		const sendData = {
			clusterId,
			namespace,
			middlewareName,
			configFileDtoList: configFileDtoListTemp
		};
		setOkLoading(true);
		await ExecuteOrderFuc();
		saveAndIssuedAccessFile(sendData)
			.then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '文件保存并下发成功'
					});
					onCreate();
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setOkLoading(false);
			});
	};
	return (
		<Modal
			title="保存并下发"
			open={open}
			onCancel={onCancel}
			width={620}
			onOk={onOk}
			okButtonProps={{
				loading: okLoading,
				disabled: okDisabled
			}}
		>
			<Alert
				message="下发文件后会覆盖原节点的文件内容，请谨慎操作"
				showIcon
				closable
				type="warning"
			/>
			<Row className="mt-8 mb-8">
				<Col span={4}>下发方式选择:</Col>
				<Col span={20}>
					<Radio.Group onChange={onChange} value={issuedMode}>
						<Radio value="radio">单节点下发</Radio>
						<Radio value="checkbox">多节点下发</Radio>
					</Radio.Group>
				</Col>
			</Row>
			<Row>
				<Col offset={4}>
					<Table
						rowKey="podName"
						dataSource={podList}
						loading={loading}
						columns={columns}
						rowSelection={{
							type: issuedMode,
							selectedRowKeys: selectedPods,
							...rowSelection
						}}
					/>
				</Col>
			</Row>
		</Modal>
	);
}

import React, { useEffect, useState } from 'react';
import { DatePicker, Input, Space, Table, notification } from 'antd';
import { useHistory, useParams } from 'react-router';
import Actions from '@/components/Actions';
import transTime from '@/utils/transTime';
import { AccessIndexParams } from '../detail';
import { getAccessFileHistoryRecord } from '@/services/middleware';
import storage from '@/utils/storage';
import useRefresh from '@/utils/useRefresh';
const LinkButton = Actions.LinkButton;
const { RangePicker } = DatePicker;
export default function AccessFileEditHistory({
	lock
}: {
	lock: string;
}): JSX.Element {
	const history = useHistory();
	const {
		type,
		name,
		aliasName,
		currentTab,
		middlewareName,
		clusterId,
		namespace
	}: AccessIndexParams = useParams();
	const [loading, setLoading] = useState<boolean>(false);
	const [startTime, setStartTime] = useState<string>();
	const [endTime, setEndTime] = useState<string>();
	const [total, setTotal] = useState<number>();
	const [currentPage, setCurrentPage] = useState<number>(1);
	const [searchText, setSearchText] = useState<string>('');
	const [pageSize, setPageSize] = useState<number>(10);
	const [historyRecords, setHistoryRecords] =
		useState<AccessFileHistoryRecord[]>();
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		getData(currentPage, pageSize, searchText, startTime, endTime);
	}, [refreshKey]);
	const getData = (
		current: number,
		size: number,
		keyword: string,
		startTime?: string,
		endTime?: string
	) => {
		setLoading(true);
		getAccessFileHistoryRecord({
			clusterId,
			namespace,
			middlewareName,
			current,
			size,
			keyword,
			startTime: startTime || '',
			endTime: endTime || ''
		})
			.then((res) => {
				if (res.success) {
					setHistoryRecords(res.data.list);
					setTotal(res.data.total);
					setCurrentPage(res.data.pageNum);
					setPageSize(res.data.pageSize);
				} else {
					notification.error({
						message: '失败',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const actionRender = (_: any, record: AccessFileHistoryRecord) => {
		return (
			<Actions>
				<LinkButton
					code="confFileHistoryDetail"
					onClick={() => {
						storage.setSession('file-history-detail', {
							...record,
							lock
						});
						history.push(
							`/project/${type}/${name}/${aliasName}/server/${currentTab}/${middlewareName}/${clusterId}/${namespace}/${record.historyId}`
						);
					}}
				>
					查看详情
				</LinkButton>
			</Actions>
		);
	};
	const columns = [
		{
			dataIndex: 'updateTime',
			title: '变更时间',
			key: 'updateTime',
			ellipsis: true
		},
		{
			dataIndex: 'podAliasName',
			title: '影响实例',
			key: 'podAliasName',
			ellipsis: true,
			render: (value: string) => value ?? '/'
		},
		{
			dataIndex: 'configPath',
			title: '配置文件路径',
			key: 'configPath',
			ellipsis: true,
			render: (value: string) => value ?? '/'
		},
		{
			dataIndex: 'updateBy',
			title: '操作用户名',
			key: 'updateBy',
			ellipsis: true
		},
		{
			dataIndex: 'action',
			title: '操作',
			render: actionRender
		}
	];
	const onChange = (time_values: any) => {
		if (time_values) {
			setStartTime(time_values[0]);
			setEndTime(time_values[1]);
			const start = transTime.local2gmt2(time_values[0]);
			const end = transTime.local2gmt2(time_values[1]);
			getData(currentPage, pageSize, searchText, start, end);
		} else {
			setStartTime(undefined);
			setEndTime(undefined);
			getData(currentPage, pageSize, searchText);
		}
	};
	const onSearch = (keyword_value: string) => {
		setSearchText(keyword_value);
		getData(currentPage, pageSize, keyword_value, startTime, endTime);
	};
	const onTableChange = (page: number, pageSize: number) => {
		setCurrentPage(page);
		setPageSize(pageSize);
		getData(page, pageSize, searchText, startTime, endTime);
	};
	return (
		<>
			<Space>
				<RangePicker showTime onChange={onChange} />
				<Input.Search
					placeholder="请输入搜索关键字"
					onChange={(e) => setSearchText(e.target.value)}
					onSearch={onSearch}
					allowClear
				/>
			</Space>
			<Table
				rowKey="historyId"
				className="mt-16"
				columns={columns}
				dataSource={historyRecords}
				loading={loading}
				pagination={{
					total: total,
					current: currentPage,
					pageSize: pageSize,
					onChange: onTableChange
				}}
			/>
		</>
	);
}

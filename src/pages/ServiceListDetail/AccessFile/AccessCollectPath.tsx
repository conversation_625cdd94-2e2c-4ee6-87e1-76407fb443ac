import { DEPLOY_MOD_SERVER, formItemLayout420 } from '@/utils/const';
import { Button, Form, Input, Modal, Select, Space, notification } from 'antd';
import { MinusOutlined, PlusOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import pattern from '@/utils/pattern';
import { PodItem } from '../detail';
import { getPods, saveAccessFilePaths } from '@/services/middleware';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';

export default function AccessCollectPath({
	open,
	onCancel,
	clusterId,
	namespace,
	middlewareName,
	type,
	data,
	onCreate
}: {
	open: boolean;
	onCancel: () => void;
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	data?: AccessFilePath[];
	onCreate: () => void;
}): JSX.Element {
	const [form] = Form.useForm();
	const pathsForm = Form.useWatch('paths', form);
	const [podList, setPodList] = useState<PodItem[]>([]);
	const [loading, setLoading] = useState<boolean>(false);
	const [okLoading, setOkLoading] = useState<boolean>(false);
	useEffect(() => {
		if (data?.length && data.length > 0) {
			form.setFieldsValue({
				paths: data
			});
		} else {
			form.setFieldsValue({
				paths: [{}]
			});
		}
	}, [data]);
	useEffect(() => {
		setLoading(true);
		getPods({
			clusterId,
			namespace,
			middlewareName,
			type,
			deployMod: DEPLOY_MOD_SERVER
		})
			.then((res) => {
				if (res.success) {
					setPodList(res.data?.pods || []);
				}
			})
			.finally(() => {
				setLoading(false);
			});
	}, []);
	const onOk = async () => {
		await form.validateFields();
		const configFileDtoListTemp = form.getFieldsValue();
		setOkLoading(true);
		await ExecuteOrderFuc();
		const res = await saveAccessFilePaths({
			clusterId,
			namespace,
			middlewareName,
			configFileDtoList: configFileDtoListTemp.paths
		});
		setOkLoading(false);
		if (!res.success) {
			notification.error({
				message: '错误',
				description: (
					<>
						<p>{res.errorMsg}</p>
						<p>{res.errorDetail}</p>
					</>
				)
			});
		} else {
			notification.success({
				message: '成功',
				description: '配置文件保存成功'
			});
			onCreate();
		}
	};
	return (
		<Modal
			open={open}
			onCancel={onCancel}
			title="路径配置"
			width={705}
			onOk={onOk}
			okButtonProps={{ loading: okLoading }}
		>
			<Form
				form={form}
				labelAlign="left"
				colon={false}
				{...formItemLayout420}
			>
				<Form.List name="paths">
					{(fields, { add, remove }) => (
						<>
							{fields.map(
								({ key, name, ...restField }, index) => (
									<Form.Item
										key={key}
										label={
											index === 0 ? '文件采集路径' : ' '
										}
										style={{ marginBottom: '0px' }}
									>
										<Space
											style={{
												display: 'flex',
												marginBottom: 8
											}}
											align="baseline"
										>
											<Button
												size="small"
												style={{
													width: 16,
													height: 16,
													padding: 0
												}}
												icon={<MinusOutlined />}
												type="default"
												disabled={fields.length === 1}
												onClick={() => remove(name)}
											/>
											<Form.Item
												{...restField}
												name={[name, 'instanceName']}
												rules={[
													{
														required: true,
														message:
															'节点选择不能为空'
													}
												]}
												style={{ marginBottom: '0px' }}
											>
												<Select
													allowClear
													style={{ width: '120px' }}
													placeholder="请选择节点"
													loading={loading}
													dropdownMatchSelectWidth={
														false
													}
												>
													{podList.map(
														(item: PodItem) => {
															return (
																<Select.Option
																	key={
																		item.podName
																	}
																	value={
																		item.podName
																	}
																	disabled={pathsForm?.find(
																		(
																			i: any
																		) =>
																			i?.instanceName ===
																			item.podName
																	)}
																>
																	{
																		item.podAliasName
																	}
																</Select.Option>
															);
														}
													)}
												</Select>
											</Form.Item>
											<Form.Item
												{...restField}
												name={[name, 'charset']}
												rules={[
													{
														required: true,
														message:
															'编码规则不能为空'
													}
												]}
											>
												<Select
													placeholder="请选择编码规则"
													style={{ width: '150px' }}
												>
													<Select.Option value="UTF8">
														UTF8
													</Select.Option>
													<Select.Option value="GBK">
														GBK
													</Select.Option>
												</Select>
											</Form.Item>
											<Form.Item
												{...restField}
												name={[name, 'path']}
												rules={[
													{
														required: true,
														message:
															'文件采集路径不能为空'
													},
													{
														type: 'string',
														min: 2,
														max: 256,
														pattern: new RegExp(
															pattern.path
														),
														message:
															'请输入正确格式的文件采集路径'
													}
												]}
												style={{ marginBottom: '0px' }}
											>
												<Input
													style={{ width: '250px' }}
													placeholder="请输入采集路径及文件名，例/path/my.conf"
												/>
											</Form.Item>
										</Space>
									</Form.Item>
								)
							)}
							<Form.Item label=" ">
								<Button
									type="link"
									size="small"
									onClick={() => add()}
									style={{ marginLeft: '-3px' }}
									disabled={
										pathsForm?.length === podList?.length
									}
								>
									<PlusOutlined
										style={{
											border: '1px solid',
											borderRadius: '2px',
											marginRight: '6px'
										}}
									/>
									新增
								</Button>
							</Form.Item>
						</>
					)}
				</Form.List>
			</Form>
		</Modal>
	);
}

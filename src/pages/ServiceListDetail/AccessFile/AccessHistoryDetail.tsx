import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>ontent, ProHeader, ProPage } from '@/components/ProPage';
import { Alert, Button, Modal, notification } from 'antd';
import { useHistory, useParams } from 'react-router';
import CodeMirror from 'codemirror';
import 'codemirror/addon/merge/merge.css';
import 'codemirror/addon/merge/merge.js';
import 'codemirror/mode/yaml/yaml.js';
import 'codemirror/mode/yaml-frontmatter/yaml-frontmatter.js';
import 'codemirror/theme/twilight.css';
import 'codemirror/addon/selection/active-line';
import { restoreAccessFile } from '@/services/middleware';
import { AccessHistoryDetailParams } from '../detail';
import storage from '@/utils/storage';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';
import { controlledOperationDisabled } from '@/utils/utils';
import Auth from '@/components/Auth';
const { confirm } = Modal;

export default function AccessHistoryDetail(): JSX.Element {
	const params: AccessHistoryDetailParams = useParams();
	const historyDetail: AccessFileHistoryRecord = storage.getSession(
		'file-history-detail'
	);
	const codeEditor = useRef<any>(null);
	const history = useHistory();
	const [restoreLoading, setRestoreLoading] = useState<boolean>(false);
	const restoreEditHistoryOperatorId = maintenances['Restore Edit History'];
	useEffect(() => {
		init();
	}, []);
	const init = () => {
		codeEditor.current.innerHTML = '';
		CodeMirror.MergeView(codeEditor?.current as HTMLElement, {
			theme: 'twilight',
			value: historyDetail?.beforeData || '暂无数据',
			orig: historyDetail?.afterData || '暂无数据',
			mode: 'yaml',
			lineNumbers: true, // * 显示行数
			lineWrapping: true,
			styleActiveLine: true,
			revertButtons: false, // * 确定是都显示允许用户还原更改的按钮
			connect: 'left', // * 设置用于连接更改的代码块的样式
			collapseIdentical: false, // * 是否将为更改的文本进行折叠
			allowEditingOriginals: false, // * 原始编辑器是否可编辑
			readOnly: true
		});
	};
	const restore = () => {
		confirm({
			title: '操作确认',
			content:
				'还原操作会将配置文件还原成当前操作之前的文件，并覆盖当前操作之后的修改，是否确定进行还原操作?',
			onOk: async () => {
				setRestoreLoading(true);
				await ExecuteOrderFuc();
				return restoreAccessFile({
					clusterId: params.clusterId,
					historyId: params.historyId,
					middlewareName: params.middlewareName,
					namespace: params.namespace
				})
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '配置文件还原成功'
							});
							history.goBack();
						} else {
							notification.error({
								message: '错误',
								description: (
									<>
										<p>{res.errorMsg}</p>
										<p>{res.errorDetail}</p>
									</>
								)
							});
						}
					})
					.finally(() => {
						setRestoreLoading(false);
					});
			}
		});
	};
	return (
		<ProPage>
			<ProHeader
				title="查看修改历史详情"
				onBack={() => {
					history.goBack();
				}}
			/>
			<ProContent>
				<Alert
					message="还原操作会将配置文件还原成当前操作之前的文件，并覆盖当前操作之后的修改，请谨慎操作！"
					showIcon
					closable
					type="warning"
				/>
				<Auth code="confFileHistoryRoleBack">
					<Button
						loading={restoreLoading}
						className="mt-16"
						type="primary"
						title={
							!historyDetail.configSameAsNow
								? '该实例或当前配置文件路径已变更，无法进行还原操作！'
								: undefined
						}
						disabled={
							!historyDetail.configSameAsNow ||
							controlledOperationDisabled(
								'maintenance',
								historyDetail?.lock as string
							)
						}
						onClick={() => {
							WorkOrderFuc(
								restore,
								historyDetail.lock as string,
								params.middlewareName,
								restoreEditHistoryOperatorId,
								history,
								params.type,
								params.name,
								params.aliasName,
								params.clusterId,
								params.namespace
							);
						}}
					>
						还原
					</Button>
				</Auth>
				<div className="yaml-edit-content">
					<div className="yaml-edit-header">
						<div className="yaml-edit-left-2">修改前</div>
						<div className="yaml-edit-right-2">修改后</div>
					</div>
					<div className="yaml-edit-code">
						<div ref={codeEditor}></div>
					</div>
					<div className="yaml-edit-console-footer"></div>
				</div>
			</ProContent>
		</ProPage>
	);
}

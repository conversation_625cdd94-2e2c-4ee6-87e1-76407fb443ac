import React, { useEffect, useState } from 'react';
import {
	Button,
	Col,
	Form,
	Modal,
	Radio,
	RadioChangeEvent,
	Row,
	Select,
	Table,
	Upload,
	notification
} from 'antd';
import { PodItem } from '../detail';
import { DEPLOY_MOD_SERVER, formItemLayout420 } from '@/utils/const';
import {
	getAccessFileContent,
	getAccessFilePaths,
	getAccessFileSuffixes,
	getPods
} from '@/services/middleware';
import { judgeFileNameIsNoExtension, podRoleRender } from '@/utils/utils';
export default function AccessUploadFile({
	open,
	onCancel,
	clusterId,
	namespace,
	middlewareName,
	type,
	onCreate
}: {
	open: boolean;
	onCancel: () => void;
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	onCreate: (file_detail: string) => void;
}): JSX.Element {
	const [uploadMode, setUploadMode] = useState<'new' | 'old'>('new');
	const [fileList, setFileList] = useState<any[]>([]);
	const [filePaths, setFilePaths] = useState<AccessFilePath[]>();
	const [podList, setPodList] = useState<PodItem[]>([]);
	const [podLoading, setPodLoading] = useState<boolean>(false);
	const [curFile, setCurFile] = useState<string>();
	const [selectedPod, setSelectedPod] = useState<React.Key[]>();
	const [okDisabled, setOkDisabled] = useState<boolean>(true);
	const [okLoading, setOkLoading] = useState<boolean>(false);
	const [suffixes, setSuffixes] = useState<string[]>([]);
	const [form] = Form.useForm();
	const charsetForm = Form.useWatch('charset', form);
	const columns = [
		{
			dataIndex: 'podAliasName',
			title: '实例名称',
			ellipsis: true
		},
		{
			dataIndex: 'status',
			title: '状态',
			ellipsis: true,
			width: 110
		},
		{
			dataIndex: 'role',
			title: '节点类型',
			width: 80,
			ellipsis: true,
			render: (value: any) => podRoleRender(value, type)
		},
		{
			dataIndex: 'createTime',
			title: '接入时间',
			width: 160,
			render: (value: any) => value || '/'
		}
	];
	useEffect(() => {
		setPodLoading(true);
		async function getData() {
			getAccessFileSuffixes({
				clusterId,
				namespace,
				middlewareName
			}).then((res) => {
				if (res.success) {
					setSuffixes(res.data);
				}
			});
			const fileRes = await getAccessFilePaths({
				clusterId,
				namespace,
				middlewareName,
				checkExists: true
			});
			if (fileRes.success) {
				setFilePaths(fileRes.data);
			}
			await getPods({
				clusterId,
				namespace,
				middlewareName,
				type,
				deployMod: DEPLOY_MOD_SERVER
			})
				.then((res) => {
					if (res.success) {
						const podListTemp =
							res.data?.pods?.map((item: PodItem) =>
								item.status !== 'ConnectFailed' &&
								fileRes.data.find(
									(i) => item.podName === i.instanceName
								)?.path
									? item
									: { ...item, disabled: true }
							) || [];
						setPodList(podListTemp);
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				})
				.finally(() => {
					setPodLoading(false);
				});
		}
		getData();
	}, []);
	useEffect(() => {
		if (charsetForm) {
			if (uploadMode === 'new') {
				if (!curFile || fileList.length === 0) {
					setOkDisabled(true);
				} else {
					const reader = new window.FileReader();
					reader.onload = function () {
						setCurFile((reader.result as string) || '');
					};
					reader.readAsText(fileList[0], charsetForm);
					setOkDisabled(false);
				}
			}
		} else {
			setOkDisabled(true);
		}
	}, [charsetForm]);
	const onChange = (e: RadioChangeEvent) => {
		setUploadMode(e.target.value);
		if (e.target.value === 'new') {
			if (!curFile || fileList.length === 0) {
				setOkDisabled(true);
			} else {
				if (charsetForm) {
					setOkDisabled(false);
				} else {
					setOkDisabled(true);
				}
			}
		} else {
			setOkDisabled(true);
		}
	};
	const beforeUpload = (file: any) => {
		// * 把 无后缀的 空字符串 筛选掉
		const suffixes_temp = suffixes.filter((item) => !!item);
		if (suffixes_temp.every((item) => !file.name.includes(item))) {
			if (!judgeFileNameIsNoExtension(file.name)) {
				notification.warning({
					message: '提醒',
					description: '当前文件格式不支持上传'
				});
				return false;
			}
		}
		const reader = new window.FileReader();
		reader.onload = function () {
			setCurFile((reader.result as string) || '');
		};
		reader.readAsText(file, charsetForm);
		setFileList([file]);
		setOkDisabled(false);
		// * 只在前端缓存文件内容，所以在此拦截请求；
		return false;
	};
	const onRemove = () => {
		setFileList([]);
		setCurFile(undefined);
		setOkDisabled(true);
	};
	const getFileContent = async (
		path: string | undefined,
		podName: string,
		charset: string
	) => {
		if (!path || path === '') {
			return '未找到当前实例的配置文件路径';
		}
		setOkLoading(true);
		const res = await getAccessFileContent({
			clusterId,
			namespace,
			middlewareName,
			path: path,
			podName: podName,
			charset: charset
		});
		setOkLoading(false);
		if (!res.success) {
			return `${res.errorMsg}:${res.errorDetail}`;
		} else {
			return res.data;
		}
	};
	const onOk = async () => {
		await form.validateFields();
		if (uploadMode === 'new') {
			if (!curFile) {
				notification.warning({
					message: '提醒',
					description: '未获取到当前文件内容'
				});
				return;
			}
			if (curFile) {
				onCreate(curFile);
			}
		} else {
			const current_path_data = filePaths?.find(
				(item) => item.instanceName === selectedPod?.[0]
			);
			const curPath = current_path_data?.path;
			const curCharset = current_path_data?.charset || 'UTF8';
			const curPod = selectedPod?.[0] as string;
			const res = await getFileContent(curPath, curPod, curCharset);
			onCreate(res);
		}
	};
	return (
		<Modal
			title="上传文件"
			open={open}
			onCancel={onCancel}
			width={620}
			onOk={onOk}
			okButtonProps={{ disabled: okDisabled, loading: okLoading }}
		>
			<Row className="mb-16">
				<Col span={4} style={{ color: '#252525' }}>
					上传文件选择:
				</Col>
				<Col span={20}>
					<Radio.Group onChange={onChange} value={uploadMode}>
						<Radio value="new">上传新文件</Radio>
						<Radio value="old">选择已有文件</Radio>
					</Radio.Group>
				</Col>
			</Row>
			{uploadMode === 'new' && (
				<Form
					form={form}
					{...formItemLayout420}
					labelAlign="left"
					requiredMark="optional"
				>
					<Form.Item
						label="编码规则选择"
						name="charset"
						rules={[
							{ required: true, message: '编码规则不能为空' }
						]}
					>
						<Select
							placeholder="请选择编码规则"
							style={{ width: '150px' }}
						>
							<Select.Option value="UTF8">UTF8</Select.Option>
							<Select.Option value="GBK">GBK</Select.Option>
						</Select>
					</Form.Item>
				</Form>
			)}
			<Row className="mt-16">
				<Col offset={4}>
					{uploadMode === 'new' && (
						<Upload
							accept={suffixes.join(',')}
							beforeUpload={beforeUpload}
							onRemove={onRemove}
							fileList={fileList}
							maxCount={1}
						>
							{!fileList.length ? (
								<Button
									type="primary"
									disabled={!charsetForm}
									title={
										!charsetForm
											? '请先选择编码规则'
											: undefined
									}
								>
									选择文件
								</Button>
							) : null}
						</Upload>
					)}
					{uploadMode === 'old' && (
						<Table
							rowKey="podName"
							dataSource={podList}
							loading={podLoading}
							columns={columns}
							rowSelection={{
								type: 'radio',
								getCheckboxProps: (record: PodItem) => ({
									disabled: record.disabled
								}),
								onChange(selectedRowKeys: React.Key[]) {
									setOkDisabled(false);
									setSelectedPod(selectedRowKeys);
								}
							}}
						/>
					)}
				</Col>
			</Row>
		</Modal>
	);
}

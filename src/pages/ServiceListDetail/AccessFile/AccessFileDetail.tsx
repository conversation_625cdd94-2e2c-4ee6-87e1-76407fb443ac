import React, { useEffect, useState } from 'react';
import { Button, Select, Space, Spin, notification } from 'antd';
import { useHistory, useParams } from 'react-router';
import { UnControlled as CodeMirror } from 'react-codemirror2';
import useRefresh from '@/utils/useRefresh';
import {
	downloadAccessFile,
	getAccessFileContent,
	getAccessFilePaths,
	getPods
} from '@/services/middleware';
import { AccessIndexParams, PodItem } from '../detail';
import { DEPLOY_MOD_SERVER } from '@/utils/const';
import AccessCollectPath from './AccessCollectPath';
import {
	ArrowsAltOutlined,
	DownloadOutlined,
	ShrinkOutlined
} from '@ant-design/icons';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';
import 'codemirror/theme/twilight.css';
import 'codemirror/lib/codemirror.css';
import { controlledOperationDisabled } from '@/utils/utils';
import Auth from '@/components/Auth';
const options = {
	mode: 'xml',
	theme: 'twilight',
	readOnly: true,
	lineNumbers: true,
	fullScreen: false,
	lineWrapping: true
};
export default function AccessFileDetail({
	lock
}: {
	lock: string;
}): JSX.Element {
	const history = useHistory();
	const params: AccessIndexParams = useParams();
	const [collectPathData, setCollectPathData] = useState<AccessFilePath[]>();
	const [fileDetail, setFileDetail] = useState<string>('暂无数据');
	const [curPod, setCurPod] = useState<string>();
	const [podList, setPodList] = useState<PodItem[]>([]);
	const [podLoading, setPodLoading] = useState<boolean>(false);
	const [fileLoading, setFileLoading] = useState<boolean>(false);
	const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
	const [collectPathOpen, setCollectPathOpen] = useState<boolean>(false);
	const configurationFilePathOperatorId =
		maintenances['Configuration File Path'];
	const downloadConfigurationFileOperatorId =
		maintenances['Download Configuration File'];
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		async function getAllData() {
			try {
				setPodLoading(true);
				setFileDetail('暂无数据');
				setCurPod(undefined);
				await getData();
				const podsRes = await getPods({
					clusterId: params.clusterId,
					namespace: params.namespace,
					middlewareName: params.middlewareName,
					type: params.name,
					deployMod: DEPLOY_MOD_SERVER
				});
				if (podsRes.success) {
					setPodList(podsRes.data?.pods || []);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{podsRes.errorMsg}</p>
								<p>{podsRes.errorDetail}</p>
							</>
						)
					});
					setPodList([]);
				}
			} finally {
				setPodLoading(false);
			}
		}
		getAllData();
	}, [refreshKey]);
	const getData = async () => {
		const filePathRes = await getAccessFilePaths({
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params.middlewareName
		});
		if (!filePathRes.success) {
			notification.error({
				message: '错误',
				description: (
					<>
						<p>{filePathRes.errorMsg}</p>
						<p>{filePathRes.errorDetail}</p>
					</>
				)
			});
		} else {
			setCollectPathData(filePathRes.data);
			return filePathRes.data;
		}
	};
	const onPodChange = (pod_value: string) => {
		setCurPod(pod_value);
		const current_path_data = collectPathData?.find(
			(item) => item.instanceName === pod_value
		);
		const curPath = current_path_data?.path;
		if (!curPath || curPath === '') {
			setFileDetail('暂无数据');
			return;
		}
		const curCharset = current_path_data?.charset || 'UTF8';
		getFileContent(curPath, pod_value, curCharset);
	};
	const getFileContent = async (
		path: string,
		podName: string,
		charset: string
	) => {
		setFileLoading(true);
		const res = await getAccessFileContent({
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params.middlewareName,
			path: path,
			podName: podName,
			charset: charset
		});
		setFileLoading(false);
		if (!res.success) {
			setFileDetail(res.errorMsg || '暂无数据');
			return;
		}
		setFileDetail(res.data);
	};
	const fileDownload = async () => {
		if (!curPod) {
			notification.warning({
				message: '提示',
				description: '请选择节点'
			});
			return;
		}
		const current_path_data = collectPathData?.find(
			(item) => item.instanceName === curPod
		);
		const curPath = current_path_data?.path;
		const curCharset = current_path_data?.charset || 'UTF8';
		if (!curPath || curPath === '') {
			notification.warning({
				message: '提示',
				description: '当前节点未配置采集路径'
			});
			return;
		}
		const _url = downloadAccessFile({
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params.middlewareName
		});
		const url = `${_url}?path=${curPath}&podName=${curPod}&charset=${curCharset}`;
		await ExecuteOrderFuc();
		window.open(url, '_target');
	};
	const onCreate = async () => {
		setCollectPathOpen(false);
		const newestPathData = await getData();
		if (!curPod) {
			return;
		}
		const current_path_data = newestPathData?.find(
			(item) => item.instanceName === curPod
		);
		const curPath = current_path_data?.path;
		const curCharset = current_path_data?.charset || 'UTF8';
		if (!curPath) {
			setFileDetail('暂无数据');
			return;
		}
		getFileContent(curPath, curPod, curCharset);
	};
	return (
		<div>
			<Space>
				<Auth code="confFileDetailPath">
					<Button
						type="primary"
						disabled={controlledOperationDisabled(
							'maintenance',
							lock
						)}
						onClick={() => {
							WorkOrderFuc(
								() => {
									setCollectPathOpen(true);
								},
								lock,
								params.middlewareName,
								configurationFilePathOperatorId,
								history,
								params.type,
								params.name,
								params.aliasName,
								params.clusterId,
								params.namespace
							);
						}}
					>
						采集路径配置
					</Button>
				</Auth>
				<Select
					style={{ width: '200px' }}
					placeholder="请选择节点"
					loading={podLoading}
					value={curPod}
					onChange={onPodChange}
					dropdownMatchSelectWidth={false}
				>
					{podList.map((item: PodItem) => {
						return (
							<Select.Option
								key={item.podName}
								value={item.podName}
							>
								{item.podAliasName}
							</Select.Option>
						);
					})}
				</Select>
			</Space>
			<Spin spinning={fileLoading}>
				<div
					className={`log-display ${
						isFullscreen ? 'log-full-screen' : ''
					}`}
					style={{ marginTop: 16 }}
				>
					<div className="title">
						<div className="display-inline-block">文件详情</div>
						<div className={`display-inline-block tips`}>
							<Auth code="confFileDetailDownload">
								<div
									className={`display-inline-block btn`}
									style={{
										color: controlledOperationDisabled(
											'maintenance',
											lock
										)
											? '#ccc'
											: '#bfbfbf',
										cursor: controlledOperationDisabled(
											'maintenance',
											lock
										)
											? 'not-allowed'
											: 'pointer'
									}}
									onClick={() => {
										if (
											!controlledOperationDisabled(
												'maintenance',
												lock
											)
										) {
											WorkOrderFuc(
												fileDownload,
												lock,
												params.middlewareName,
												downloadConfigurationFileOperatorId,
												history,
												params.type,
												params.name,
												params.aliasName,
												params.clusterId,
												params.namespace
											);
										}
									}}
								>
									文件下载 <DownloadOutlined />
								</div>
							</Auth>
							{!isFullscreen && (
								<ArrowsAltOutlined
									onClick={() => setIsFullscreen(true)}
								/>
							)}
							{isFullscreen && (
								<ShrinkOutlined
									onClick={() => setIsFullscreen(false)}
								/>
							)}
						</div>
					</div>
					<CodeMirror
						value={fileDetail}
						options={options}
						className="log-codeMirror"
					/>
				</div>
			</Spin>
			{collectPathOpen && (
				<AccessCollectPath
					data={collectPathData}
					open={collectPathOpen}
					onCreate={onCreate}
					onCancel={() => setCollectPathOpen(false)}
					clusterId={params.clusterId}
					namespace={params.namespace}
					middlewareName={params.middlewareName}
					type={params.name}
				/>
			)}
		</div>
	);
}

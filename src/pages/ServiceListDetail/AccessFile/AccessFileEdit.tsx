import React, { useRef, useState } from 'react';
import { Alert, Button, Space } from 'antd';
import { useHistory, useParams } from 'react-router';
import { Controlled as CodeMirror } from 'react-codemirror2';
import { ArrowsAltOutlined, ShrinkOutlined } from '@ant-design/icons';
import AccessUploadFile from './AccessUploadFile';
import { AccessIndexParams } from '../detail';
import AccessSaveAndIssued from './AccessSaveAndIssued';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';
import { controlledOperationDisabled } from '@/utils/utils';
import Auth from '@/components/Auth';

export default function AccessFileEdit({
	lock
}: {
	lock: string;
}): JSX.Element {
	const history = useHistory();
	const codeRef: any = useRef(null);
	const params: AccessIndexParams = useParams();
	const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
	const [isEdit, setIsEdit] = useState<boolean>(false);
	const [editDisabled, setEditDisabled] = useState<boolean>(true);
	const [uploadFileOpen, setUploadFileOpen] = useState<boolean>(false);
	const [uploadFile, setUploadFile] = useState<string>('');
	const [saveAndIssuedOpen, setSaveAndIssuedOpen] = useState<boolean>(false);
	const [curFile, setCurFile] = useState<string>('暂无数据');
	const uploadConfigurationFileOperatorId =
		maintenances['Upload Configuration File'];
	const editConfigurationFileOperatorId =
		maintenances['Edit Configuration File'];
	const [options, setOptions] = useState({
		mode: 'yaml',
		readOnly: true,
		theme: 'twilight',
		lineNumbers: true,
		fullScreen: false,
		lineWrapping: true,
		styleActiveLine: true,
		revertButtons: false, // * 确定是都显示允许用户还原更改的按钮
		connect: 'left', // * 设置用于连接更改的代码块的样式
		collapseIdentical: false, // * 是否将为更改的文本进行折叠
		allowEditingOriginals: false // * 原始编辑器是否可编辑
	});
	const onCreate = async (file_detail: string) => {
		await ExecuteOrderFuc();
		setUploadFile(file_detail);
		setCurFile(file_detail);
		setUploadFileOpen(false);
		setEditDisabled(false);
	};
	const onSaveCreate = () => {
		setSaveAndIssuedOpen(false);
		setOptions({
			...options,
			readOnly: true
		});
		setUploadFile(curFile);
		setIsEdit(false);
		setEditDisabled(false);
	};
	return (
		<>
			<Alert
				type="warning"
				message="修改配置文件后，需要手动重启服务，相关修改才会生效，请谨慎操作！"
				showIcon
				closable
				className="mb-16"
			/>
			<Space>
				{!isEdit && (
					<>
						<Auth code="confFileEditUpdate">
							<Button
								type="primary"
								disabled={
									editDisabled ||
									controlledOperationDisabled(
										'maintenance',
										lock
									)
								}
								title={
									editDisabled
										? '请先上传文件后使用编辑功能'
										: undefined
								}
								onClick={() => {
									WorkOrderFuc(
										() => {
											setOptions({
												...options,
												readOnly: false
											});
											setIsEdit(true);
										},
										lock,
										params.middlewareName,
										editConfigurationFileOperatorId,
										history,
										params.type,
										params.name,
										params.aliasName,
										params.clusterId,
										params.namespace
									);
								}}
							>
								编辑
							</Button>
						</Auth>
						<Auth code="confFileEditUpload">
							<Button
								disabled={controlledOperationDisabled(
									'maintenance',
									lock
								)}
								onClick={() => {
									WorkOrderFuc(
										() => {
											setUploadFileOpen(true);
										},
										lock,
										params.middlewareName,
										uploadConfigurationFileOperatorId,
										history,
										params.type,
										params.name,
										params.aliasName,
										params.clusterId,
										params.namespace
									);
								}}
							>
								上传文件
							</Button>
						</Auth>
					</>
				)}
				{isEdit && (
					<>
						<Button
							type="primary"
							onClick={() => {
								setSaveAndIssuedOpen(true);
							}}
						>
							保存并下发
						</Button>
						<Button
							danger
							onClick={() => {
								setIsEdit(false);
								setCurFile(uploadFile);
								setOptions({
									...options,
									readOnly: true
								});
							}}
						>
							取消
						</Button>
					</>
				)}
			</Space>
			<div
				className={`log-display ${
					isFullscreen ? 'log-full-screen' : ''
				}`}
				style={{ marginTop: 16 }}
			>
				<div className="title">
					<div className="display-inline-block">文件详情</div>
					<div className={`display-inline-block tips`}>
						{!isFullscreen && (
							<ArrowsAltOutlined
								onClick={() => setIsFullscreen(true)}
							/>
						)}
						{isFullscreen && (
							<ShrinkOutlined
								onClick={() => setIsFullscreen(false)}
							/>
						)}
					</div>
				</div>
				<CodeMirror
					value={curFile}
					ref={codeRef}
					options={options}
					onBeforeChange={(editor, data, value) => {
						setCurFile(value);
					}}
					className="log-codeMirror"
				/>
			</div>
			{uploadFileOpen && (
				<AccessUploadFile
					open={uploadFileOpen}
					onCancel={() => setUploadFileOpen(false)}
					onCreate={onCreate}
					clusterId={params.clusterId}
					namespace={params.namespace}
					middlewareName={params.middlewareName}
					type={params.name}
				/>
			)}
			{saveAndIssuedOpen && (
				<AccessSaveAndIssued
					open={saveAndIssuedOpen}
					onCancel={() => setSaveAndIssuedOpen(false)}
					curFile={curFile}
					clusterId={params.clusterId}
					namespace={params.namespace}
					middlewareName={params.middlewareName}
					type={params.name}
					onCreate={onSaveCreate}
				/>
			)}
		</>
	);
}

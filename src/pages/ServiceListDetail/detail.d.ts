import {
	monitorProps,
	middlewareDetailProps,
	storageProps,
	resProps
} from '@/types/comment';
import { paramReduxProps } from '@/types';
import { SendDataParamsProps } from '../OperationPanel';
export interface aclEditProps {
	visible: boolean;
	onCancel: (value: boolean) => void;
	data: any;
	clusterId: string;
	namespace: string;
	middlewareName: string;
	chartName: string;
	chartVersion: string;
}
export interface UserInfoProps {
	data: any;
}
export interface DetailParams {
	name: string;
	aliasName: string;
	currentTab: string;
	middlewareName: string;
	type: string;
	chartVersion: string;
	namespace: string;
	clusterId: string;
}
export interface ServiceIngressAddParams {
	name: string;
	aliasName: string;
	currentTab: string;
	middlewareName: string;
	clusterId: string;
	chartVersion: string;
	namespace: string;
	mode: string;
	brokerNum: string;
	enableExternal: string;
}
export interface ParamterSettingProps {
	customMid: boolean;
	capabilities: string[];
	readWriteProxy: { enabled: boolean } | null;
	lock: string;
	subMenu: any[];
}
export interface ParamterListProps {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	onFreshChange: () => void;
}
export interface ConfigItem {
	defaultValue: string;
	description: string;
	name: string;
	paramType: string;
	pattern: null | string;
	ranges: string;
	restart: boolean;
	topping: null | boolean;
	value: string;
	modifiedValue: string;
	updateTime: string;
	addition: boolean;
	[propName: string]: string;
}
export interface ConfigSendData {
	url: {
		clusterId: string;
		middlewareName: string;
		namespace: string;
	};
	data: {
		clusterId: string;
		middlewareName: string;
		namespace: string;
		type: string;
		customConfigList: ConfigItem[];
	};
}
export interface ParamterHistoryItem {
	after: string;
	date: string;
	id: number;
	item: string;
	last: string;
	name: string;
	restart: boolean;
	status: boolean;
	role: string;
	allowRollback: boolean;
}
export interface ParamterHistorySendData {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	item: string;
	startTime?: string;
	endTime?: string;
}
export interface ParamterTemplateItem {
	createTime: string;
	customConfigList: null | ConfigItem[];
	description: string;
	name: string;
	num: null;
	restart: null;
	type: string;
	uid: string;
	role: string;
}
export interface MonitorProps {
	grafanaOpen?: boolean;
	customMid: boolean;
	capabilities: string[];
}
export interface EventItem {
	chartName: null;
	chartVersion: null;
	count: number;
	eventExplain: null;
	eventTime: null;
	firstTimestamp: string;
	involvedObject: {
		apiVersion: string;
		fieldPath: string;
		kind: string;
		name: string;
		namespace: string;
		resourceVersion: string;
		uid: string;
	};
	lastTimestamp: string;
	message: string;
	middleware: boolean;
	reason: string;
	span: null;
	spanMetric: null;
	type: string;
	show?: boolean;
}
export interface EventsSendData {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	kind?: string;
}

export interface BasicInfoProps {
	type: string;
	data: middlewareDetailProps;
	customMid: boolean;
	// operateFlag: boolean;
	onRefresh: () => void;
	toDetail: () => void;
}
export interface InfoParams {
	title: string;
	name: string;
	aliasName: string;
	label: string;
	chartVersion?: string;
	hostAffinity: string;
	hostAnti?: string;
	description: string;
	annotations: string;
	tolerations: string;
	disasterInstanceName?: string;
	mirror?: string;
	hostNetwork?: boolean;
	scheduler: boolean | null;
}
export interface configParams {
	title: string;
	version: string;
	characterSet: string;
	port: string | number;
	mirror: string;
	autoCreateTopicEnable: boolean;
	kafkaDTO?: any;
	customVolumes?: any;
	pgPort?: number;
	apiPort?: number;
	exporterPort?: number;
	bgMonPort?: number;
	httpPort?: number;
	tcpPort?: number;
	kibanaPort?: number;
	redisPort?: number;
	sentinelPort?: number;
	predixyPort?: number;
	containerUID: number | string;
	containerGID: number | string;
	exporterTolerations?: string;
	exporterNodeAffinity?: string;
	managerTolerations?: string;
	managerNodeAffinity?: string;
	kibanaTolerations?: string;
	kibanaNodeAffinity?: string;
	consoleTolerations?: string;
	consoleNodeAffinity?: string;
}
export interface runParams {
	title: string;
	status: string;
	createTime: string;
	model: string;
	namespace: string;
	group: number;
	replicas: number;
	storageClassName: string;
	storageType: string;
	hostNetwork?: any;
}
export interface DynamicDataParams {
	title: string;
	[propsName: string]: any;
}
export interface eventsParams {
	title: string;
	table: string;
}
export interface BackupRecoveryProps {
	customMid: boolean;
	capabilities: string[];
	lock: string;
}
export interface ListProps {
	clusterId: string;
	namespace: string;
	data?: middlewareDetailProps;
	storage?: storageProps;
}
export interface BackupRecordItem {
	aliasName: null | string;
	backupAddressList: string[];
	backupFileName: string;
	backupName: string;
	backupTime: string;
	backupType: string;
	phrase: string;
	podRole: string;
	sourceName: string;
}
export interface BackupRuleItem {
	aliasName: null;
	backupScheduleName: string;
	backupType: string;
	canPause: boolean;
	createTime: null | string;
	cron: string;
	lastBackupTime: null | string;
	limitRecord: number;
	nextBackupTime: null | string;
	pause: string;
	podRole: string;
	sourceName: string;
}
export interface ConfigProps {
	clusterId: string;
	namespace: string;
	data?: middlewareDetailProps;
}
export interface BackupDataParams {
	configed: boolean;
	limitRecord: number;
	cycle: string;
	time: string;
	nextBackupTime: string;
	pause: string;
	canPause: boolean;
}
export interface PodSendData {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	deployMod?: string;
}
export interface BackupRuleSendData {
	clusterId: string;
	namespace: string;
	middlewareName?: string;
	backupScheduleName?: string;
	backupName?: string;
	backupFileName?: string;
	type: string;
	limitRecord?: number;
	cron?: string;
	pod?: string | string[];
}

export interface ExternalAccessProps {
	customMid: boolean;
	capabilities: string[];
	middlewareName: string;
	type: string;
	namespace: string;
}
export interface LogProps {
	data: middlewareDetailProps;
	customMid: boolean;
	logging: any;
	capabilities: string[];
	onRefresh?: () => void;
	role: User;
	subMenu: any[];
	// operateFlag: boolean;
}
export interface ContainerItem {
	exitCode: null;
	finishedAt: null;
	images: null;
	message: null;
	name: string;
	ready: boolean;
	reason: null;
	restartCount: null;
	signal: null;
	startedAt: null;
	state: null;
}
export interface ResourceParams {
	cpu: string;
	isLvmStorage: boolean;
	limitCpu: string;
	limitMemory: string;
	memory: string;
	num: null | number;
	storageClassName: string;
	storageClassQuota: string;
	provisioner?: string;
}
export interface PodItem {
	containers: ContainerItem[];
	createTime: string;
	hasConfigBackup: boolean;
	initContainers: ContainerItem[];
	lastRestartTime: string;
	nodeName: string;
	podIp: string;
	podName: string;
	podAliasName: string;
	pvcs: string[];
	resources: ResourceParams;
	storageResources: ResourceParams[];
	restartCount: number;
	role: string;
	status: string;
	nodeZone: string;
	zone: string;
	podNodeStatus: string;
	disabled?: boolean;
	maintenance?: any;
}
export interface PodSendData {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
}
export interface CommonLogProps {
	logging?: any;
	audit?: boolean;
	slowSql?: boolean;
	filelogEnabled?: boolean;
	stdoutEnabled?: boolean;
	onRefresh?: () => void;
	lock: string;
	isAdmin: boolean;
	// operateFlag: boolean;
}
export interface RealTimeProps {
	log: string;
	setRealLog: (value: string) => void;
	cleanRealLog: () => void;
}
export interface ServeAlarmProps {
	middlewareName: string;
	clusterId: string;
	namespace: string;
	type: string;
	customMid?: boolean;
	capabilities?: string[];
	alertOpen: boolean;
	alarmType: string;
	role: User;
	organId: string;
	projectId: string;
	lock: string;
	subMenu: any[];
}
export interface LabelItem {
	severity?: string;
	middleware?: string;
	clusterId?: string;
}
export interface ServiceRuleItem {
	id?: string | number;
	alert?: string;
	alertId?: string;
	alertTime?: string;
	alertTimes?: string;
	annotations?: {
		alertLevel?: string;
		message?: string;
		metric?: string;
		product?: string;
		service?: string;
		summary?: string;
	};
	clusterId?: string;
	content?: string;
	createTime?: string;
	description?: string;
	ding?: string;
	enable?: number;
	expr?: string;
	ip?: string;
	labels?: LabelItem;
	level?: string;
	lay?: string;
	mail?: string;
	middlewareName?: string;
	name?: string;
	namespace?: string;
	silence?: null | string;
	status?: null | string;
	symbol?: string;
	threshold?: string | number;
	time?: string;
	type?: string;
	unit?: string;
	level?: string;
	severity?: string;
}
export interface ServiceRuleItemRes extends resProps {
	data: ServiceRuleItem[];
}

export interface AlarmItem {
	alert: string | null;
	description: string | null;
	annotations?: any;
	expr?: string;
	name?: string;
	status?: string;
	labels?: LabelItem;
	level?: string;
	time?: string;
	type?: string;
	unit?: string;
}

export interface AlarmSendData {
	url: {
		clusterId: string | undefined;
		middlewareName?: string;
		namespace?: string;
		alertName?: string;
	};
	ding: string;
	data: {
		middlewareAlertsDTOList?: string;
		users: any[];
		middlewareAlertsDTO?: any;
	};
	alertName?: string;
}
export interface HighProps {
	data: middlewareDetailProps;
	onRefresh: (value?: string) => void;
	customMid: boolean;
	// operateFlag: boolean;
}
export interface valuesProps {
	container: string;
	scriptType: string;
}

export interface ConsoleDataProps {
	clusterId: string;
	namespace: string;
	podName: string;
	type: string;
	name: string;
	role?: string;
}
export interface consoleProps {
	visible: boolean;
	onCancel: () => void;
	containers: string[];
	data: ConsoleDataProps;
	currentContainer: string;
}
export interface LogFileItem {
	logPath: string;
	name: string;
}
export interface LogDetailItem {
	msg: string;
	offset: number;
	timestamp: string;
}
export interface DownLoadLogSendData {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	logTimeEnd: string;
	logTimeStart: string;
	pageSize: number;
	podLog: boolean;
	pod?: string;
	container?: string;
	searchWord: string;
	searchType: string;
	middlewareType: string;
	scrollId?: string;
	logPath?: string;
}
export interface QuotaParams {
	cpu: string;
	memory: string;
	storageClassQuota?: string;
	[propsName: string]: any;
}
export interface NodeSpeProps {
	visible: boolean;
	onCreate: (value: any) => void;
	onCancel: () => void;
	quota: QuotaParams;
}
export interface EsSendDataProps {
	quota?: any;
}

export interface EsNodeProps {
	visible: boolean;
	onCreate: (value: EsSendDataProps) => void;
	onCancel: () => void;
	data: any;
}
export interface CreateServeAlarmProps {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	alarmType: string;
	alertName: string;
}

export interface EditParamTemplateProps {
	param: paramReduxProps;
	readWriteProxy?: boolean;
	setParamTemplateBasic: (value: BasicProps) => void;
	setParamTemplateConfig: (value: ConfigItem[]) => void;
	setParamTemplateBasicClear: () => void;
	setParamTemplateConfigClear: () => void;
}
export interface ParamterItem {
	defaultValue: string;
	modifiedValue: string;
	description: string;
	name: string;
	paramType: null | any;
	pattern: null | any;
	ranges: string;
	restart: boolean;
	value: null | any;
}
export interface RedisSentinelNodeSpeProps {
	visible: boolean;
	onCreate: (value: any) => void;
	onCancel: () => void;
	data: any;
}
export interface ServiceDetailIngressProps {
	customMid: boolean;
	capabilities: string[];
	mode: string;
	imagePath: string;
	status: string;
	data: middlewareDetailProps;
	// operateFlag: boolean;
}

export interface ServiceNameItem {
	name: string;
	label: string;
	icon: string;
	port?: number;
}
export interface HttpPathItem {
	path: string;
	serviceName: string;
	servicePort: number | string;
	id: number;
}

export interface InternalServiceItem {
	clusterIP: null;
	internalAddress: string;
	portDetailDtoList: [];
	serviceName: string;
	servicePurpose: string;
}

export interface HostNetworkServiceItem {
	exposeIp: string;
	exposePort: string;
	servicePurpose: string;
	exposeType: string;
}
export interface PVCItem {
	accessModes: string;
	capacity: number;
	createTime: string;
	instancePod: string;
	pvcName: string;
	reclaimPolicy: string;
	status: string;
	storage: number;
	storageClass: string;
}
export interface getPVCRes extends resProps {
	data: PVCItem[];
}
export interface getPVCSendDataParams {
	clusterId: string;
	namespace: string;
	type: string;
	middlewareName: string;
}
export interface getPVCLogSendDataParams {
	clusterId: string;
	namespace: string;
	pvcName: string;
	middlewareName: string;
}
export interface scalePVCStorageSendDataParams {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	pvcName: string;
	storage: number;
	targetStorage: number;
	storageClass: string;
	type: string;
}
export interface getNamespaceQuotaRes extends resProps {
	data: {
		cpu: {
			request: number;
			total: number;
			usable: number;
			usage: number;
			used: number;
		};
		memory: {
			request: number;
			total: number;
			usable: number;
			usage: number;
			used: number;
		};
		storageList: StorageItem[];
	};
}
// * 运维能力相关
export interface OperatorAbilityProps {
	onRefresh: () => void;
	data: middlewareDetailProps;
	customMid: boolean;
	subMenu: any[];
}
export interface ServiceVersionProps {
	serviceRefresh: () => void;
	lock: string;
}
export interface AuthControlProps {
	serviceRefresh: () => void;
	acl: any | null;
	lock: string;
}
export interface MasterSlaveSwitchProps {
	serverStatus: string;
	serviceRefresh: () => void;
	mode: string;
	lock: string;
}
export interface InstanceScaleProps {
	mode: string;
	quota: any;
	status: string;
	serviceRefresh: () => void;
	readWriteProxy: {
		enabled: boolean;
		quota?: null;
		replicas?: number | null;
	};
	typeParam: any;
	lock: string;
	enableScale?: boolean;
}
export interface DBUserItem {
	createTime: string;
	dbs: any;
	description: string | null;
	id: number;
	password: string;
	passwordCheck: boolean;
	user: string;
}
export interface YamlMsgProp {
	time: string;
	msg: string;
	messageStatus: number;
}
export interface VerticalExpansionProps {
	open: boolean;
	onCancel: () => void;
	type: string;
	middlewareName: string;
	mode: string;
	updateMid: (value: any, actionType: string) => void;
	clusterId: string;
	namespace: string;
	chartVersion: string;
	chartName: string;
	quota: any;
	scaleType: any;
	groupCount: number | null; // * mq dledger模式下的组数
	groupReplicas: number | null; // * mq dledger模式下的副本数
}

export interface podGroupStatus {
	podList: string[];
	role: string;
	status: string;
}
export interface podGroupStatusRes extends resProps {
	data: podGroupStatus[];
}
export interface AvailablePortItem {
	clusterId: string;
	tcpExposeType: string;
	ingressClassName: string;
	portList: number[];
	portSet: number[];
	exposeType: string;
}

export interface IngressHttpPathsItem {
	path: string;
	serviceName: string;
	servicePort: string;
	id?: number;
}
export interface RuleItem {
	host: string;
	ingressHttpPaths: IngressHttpPathsItem[];
}
export interface ServiceIngressTypeItem {
	aliasExposeType: string;
	chartVersion: string | null;
	clusterId: string | null;
	exposeId: any;
	exposeType: string;
	ingressClassName: string | null;
	ingressIpList: any;
	middlewareName: string | null;
	middlewareType: string | null;
	namespace: string | null;
	protocol: string | null;
	serviceDTOList: any;
	skipPortConflict: boolean | null;
	tcpExposeType: string | null;
	rules: RuleItem[] | null;
}

export interface ServiceIngressTypeRes extends resProps {
	data: ServiceIngressTypeItem[];
}
export interface AccessIndexParams {
	type: string;
	name: string;
	aliasName: string;
	currentTab: string;
	middlewareName: string;
	chartVersion: string;
	clusterId: string;
	namespace: string;
}
export interface AccessHistoryDetailParams extends AccessIndexParams {
	historyId: string;
}

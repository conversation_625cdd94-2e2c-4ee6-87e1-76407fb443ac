import React, { useState, useEffect } from 'react';
import { useHistory, useParams } from 'react-router';
import { ProPage, ProContent } from '@/components/ProPage';
import { Select, Row, Col, Input, notification, Collapse, Button } from 'antd';
import {
	EditOutlined,
	ArrowsAltOutlined,
	ShrinkOutlined,
	CheckCircleFilled,
	CloseCircleOutlined,
	DownloadOutlined
} from '@ant-design/icons';
import { UnControlled as CodeMirror } from 'react-codemirror2';

import {
	downloadFile,
	getLogContent,
	getLogFiles,
	getLogPath,
	getPods,
	saveLogPath
} from '@/services/middleware';
import { AccessIndexParams } from '../detail';

import { PodItem } from '../detail';

import 'codemirror/lib/codemirror.css';
import 'codemirror/theme/twilight.css';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';
import useRefresh from '@/utils/useRefresh';
import { controlledOperationDisabled } from '@/utils/utils';
import Auth from '@/components/Auth';
import DefaultPicture from '@/components/DefaultPicture';

const { Option } = Select;
const AccessLog = ({ detail }: { detail: any }): JSX.Element => {
	const params: AccessIndexParams = useParams();
	const options = {
		mode: 'xml',
		theme: 'twilight',
		readOnly: true,
		lineNumbers: true,
		fullScreen: false,
		lineWrapping: true
	};
	const history = useHistory();
	const [pod, setPod] = useState<PodItem>();
	const [podList, setPodList] = useState<PodItem[]>([]);
	const [isEdit, setIsEdit] = useState<boolean>(false);
	const [notAuth, setNotAuth] = useState<boolean>(true);
	const [fileInfo, setFileInfo] = useState<{
		name: string;
		path: string;
	}>();
	const [filePath, setFilePath] = useState<string>('');
	const [fileList, setFileList] = useState<any>([]);
	const [currentFile, setCurrentFile] = useState<string>('');
	const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
	const [data, setData] = useState<string>('');
	const [errorPath, setErrorPath] = useState<string>('');
	const logConfigOperatorId = maintenances['Log Configuration'];
	const downloadExtraLogOperatorId = maintenances['Extra Log Download'];
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		async function getData() {
			await getPods({
				clusterId: params.clusterId,
				namespace: params.namespace,
				middlewareName: params.middlewareName,
				type: params.name,
				deployMod: 'server'
			}).then((res) => {
				if (res.success) {
					setPodList(res.data?.pods || []);
					if (res.data.pods.length > 0) {
						setPod(res.data.pods[0]);
						getPath(
							res.data.pods[0].podAliasName,
							res.data.pods[0].podName
						);
					}
				}
			});
		}
		if (!notAuth) {
			getData();
		}
	}, [refreshKey, notAuth]);
	useEffect(() => {
		if (detail?.capabilities?.includes('log')) {
			setNotAuth(false);
		} else {
			setNotAuth(true);
		}
	}, [detail]);
	const changePod = (pod_name: string) => {
		const cur_pod = podList.find(
			(item: PodItem) => item.podName === pod_name
		);
		if (!cur_pod) return;
		setPod(cur_pod);
		getPath(cur_pod.podAliasName, cur_pod.podName);
		setErrorPath('');
	};

	const changeContainr = (value: string) => {
		setFilePath(value);
		value.indexOf('/') === 0 && setErrorPath('');
	};

	const screenExtend = () => {
		setIsFullscreen(true);
	};

	const screenShrink = () => {
		setIsFullscreen(false);
	};

	const getPath = async (pod_alias_name: string, pod_name: string) => {
		await getLogPath({
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params.middlewareName,
			podName: pod_alias_name
		}).then((res) => {
			if (res.success) {
				setData('');
				setFilePath(res.data?.path);
				setFileInfo({
					name: res.data?.name,
					path: res.data?.path
				});
				getLogFilesData(pod_name, res.data?.name, res.data?.path);
			}
		});
	};
	const getLogFilesData = async (
		pod_name: string,
		file_name: string,
		file_path: string
	) => {
		setCurrentFile('');
		await getLogFiles({
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params.middlewareName,
			podName: pod_name,
			path: file_path || '/',
			name: file_name || ''
		}).then((res) => {
			if (res.success) {
				setFileList(res.data);
				setCurrentFile(res.data?.[0]?.path);
			} else {
				notification.error({
					message: '失败',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};

	const save = async () => {
		if (!pod) {
			setErrorPath('请选择实例！');
			return;
		}
		if (!filePath || filePath.indexOf('/') !== 0) {
			setErrorPath('输入正确的采集路径格式以保证功能的正常运行！');
			return;
		}
		await ExecuteOrderFuc();
		saveLogPath({
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params.middlewareName,
			podName: pod?.podAliasName,
			path: filePath,
			name: fileInfo?.name || ''
		}).then((res) => {
			if (res.success) {
				getPath(pod.podAliasName, pod.podName);
				setIsEdit(false);
				notification.success({
					message: '成功',
					description: '保存成功'
				});
			} else {
				notification.error({
					message: '失败',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};

	// * 导出日志
	const downloadLog = async () => {
		const sendData = {
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params.middlewareName,
			podName: pod?.podName,
			path: currentFile,
			name: fileInfo?.name || ''
		};
		const _url = downloadFile(sendData);
		const url = `${_url}?path=${currentFile}`;
		await ExecuteOrderFuc();
		window.open(url, '_target');
	};

	const getData = (path: string) => {
		getLogContent({
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params.middlewareName,
			podName: pod?.podName,
			path,
			name: fileInfo?.name || ''
		}).then((res) => {
			if (res.success) {
				setData(res.data);
			} else {
				notification.error({
					message: '失败',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};

	useEffect(() => {
		currentFile && currentFile !== '/' ? getData(currentFile) : setData('');
	}, [currentFile]);
	if (notAuth) {
		return <DefaultPicture />;
	}
	return (
		<ProPage>
			<ProContent style={{ margin: 0, padding: 0 }}>
				<div className={`display-flex filter-wrapper`}>
					<div className="filter-item-realtime">
						<Row align="middle">
							<Col span={4}>
								<label>实例列表</label>
							</Col>
							<Col span={20}>
								<Select
									placeholder="请选择实例"
									value={pod?.podName}
									onChange={changePod}
									style={{ width: '100%' }}
									dropdownMatchSelectWidth={false}
								>
									{podList.map((item) => (
										<Option
											value={item.podName}
											key={item.podName}
										>
											{item.podAliasName}
										</Option>
									))}
								</Select>
							</Col>
						</Row>
					</div>
					<div className="filter-item-realtime">
						<Row align="middle">
							<Col offset={2} span={3}>
								<label>采集路径</label>
							</Col>
							<Col span={19}>
								{isEdit ? (
									<>
										<Input
											placeholder="请输入日志采集路径，例/var/log/*.log"
											value={filePath}
											onChange={(e) =>
												changeContainr(e.target.value)
											}
											style={{
												width: 'calc(100% - 40px)'
											}}
											status={errorPath ? 'error' : ''}
										/>
										<CheckCircleFilled
											style={{
												color: '#52c41a',
												marginLeft: 8
											}}
											onClick={save}
										/>
										<CloseCircleOutlined
											style={{
												color: '#ff4d4f',
												marginLeft: 8
											}}
											onClick={() => {
												setIsEdit(false);
												setFilePath(
													fileInfo?.path || ''
												);
											}}
										/>
										{errorPath ? (
											<p
												style={{
													color: '#ff4d4f',
													position: 'absolute',
													top: 32
												}}
											>
												{errorPath}
											</p>
										) : null}
									</>
								) : (
									<div
										className="display-flex flex-align"
										style={{ width: '100%' }}
									>
										<div
											className="text-overflow"
											title={filePath || '/'}
										>
											{filePath || '/'}
										</div>
										<Auth code="extraLogPath">
											<EditOutlined
												style={{
													marginLeft: 8,
													color: controlledOperationDisabled(
														'maintenance',
														detail?.lock
													)
														? '#ccc'
														: '#1a1a1a',
													cursor: controlledOperationDisabled(
														'maintenance',
														detail?.lock
													)
														? 'not-allowed'
														: 'pointer'
												}}
												onClick={() => {
													if (
														!controlledOperationDisabled(
															'maintenance',
															detail?.lock
														)
													) {
														WorkOrderFuc(
															() => {
																setIsEdit(true);
															},
															detail?.lock,
															params.middlewareName,
															logConfigOperatorId,
															history,
															params.type,
															params.name,
															params.aliasName,
															params.clusterId,
															params.namespace
														);
													}
												}}
											/>
										</Auth>
									</div>
								)}
							</Col>
						</Row>
					</div>
				</div>
				<Row style={{ margin: 8 }}>
					<Col span={2}>
						<label
							style={{
								color: '#1a1a1a',
								fontWeight: 500
							}}
						>
							文件列表
						</label>
					</Col>
					<Col span={22}>
						<Collapse defaultActiveKey="1">
							<Collapse.Panel header={pod?.podAliasName} key="1">
								{fileList.length ? (
									fileList.map((item: any) => {
										return (
											<Button
												type={
													item.path === currentFile
														? 'link'
														: 'text'
												}
												key={item.path}
												onClick={() => {
													setCurrentFile(item.path);
												}}
											>
												{item.name}
											</Button>
										);
									})
								) : (
									<div style={{ textAlign: 'center' }}>
										暂无数据
									</div>
								)}
							</Collapse.Panel>
						</Collapse>
					</Col>
				</Row>
				<div
					className={`log-display ${
						isFullscreen ? 'log-full-screen' : ''
					}`}
					style={{ marginTop: 16 }}
				>
					<div className="title">
						<div className="display-inline-block">日志详情</div>
						<div className={`display-inline-block tips`}>
							<Auth code="extraLogExport">
								<div
									className={`display-inline-block btn ${
										controlledOperationDisabled(
											'maintenance',
											detail?.lock
										)
											? 'disabled'
											: ''
									}`}
									onClick={() => {
										if (
											!controlledOperationDisabled(
												'maintenance',
												detail?.lock
											)
										) {
											WorkOrderFuc(
												downloadLog,
												detail?.lock,
												params.middlewareName,
												downloadExtraLogOperatorId,
												history,
												params.type,
												params.name,
												params.aliasName,
												params.clusterId,
												params.namespace
											);
										}
									}}
								>
									日志导出 <DownloadOutlined />
								</div>
							</Auth>
							{!isFullscreen && (
								<ArrowsAltOutlined onClick={screenExtend} />
							)}
							{isFullscreen && (
								<ShrinkOutlined onClick={screenShrink} />
							)}
						</div>
					</div>
					<CodeMirror
						value={data}
						options={options}
						className="log-codeMirror"
					/>
				</div>
			</ProContent>
		</ProPage>
	);
};

export default AccessLog;

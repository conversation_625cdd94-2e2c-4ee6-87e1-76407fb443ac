import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router';
import { Table, notification } from 'antd';
import { getServerAudit } from '@/services/middleware';
import { AccessIndexParams } from '../detail';
import useRefresh from '@/utils/useRefresh';

export default function AccessAudit(): JSX.Element {
	const params: AccessIndexParams = useParams();
	const [pageNum, setPageNum] = useState<number>(1);
	const [pageSize, setPageSize] = useState<number>(10);
	const [dataSource, setDataSource] = useState([]);
	const [loading, setLoading] = useState<boolean>(false);
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		getData(pageNum, pageSize);
	}, [refreshKey]);
	const getData = (current: number, size: number) => {
		setLoading(true);
		getServerAudit({
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params.middlewareName,
			pageNum: current,
			pageSize: size
		})
			.then((res) => {
				if (res.success) {
					setPageNum(res.data.pageNum);
					setPageSize(res.data.pageSize);
					setDataSource(res.data.list);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const onTableChange = (pagination: any) => {
		setPageNum(pagination.current);
		setPageSize(pagination.pageSize);
		getData(pagination.current, pagination.pageSize);
	};
	return (
		<Table
			dataSource={dataSource}
			loading={loading}
			onChange={onTableChange}
		>
			<Table.Column
				dataIndex="cmdName"
				title="运维操作"
				width={200}
				ellipsis
			/>
			<Table.Column
				dataIndex="creator"
				title="执行用户"
				ellipsis
				width={200}
			/>
			<Table.Column
				dataIndex="creatorRole"
				title="执行角色"
				width={200}
				ellipsis
			/>
			<Table.Column
				dataIndex="targetObject"
				title="生效目标"
				width={150}
				ellipsis
			/>
			<Table.Column dataIndex="cmdStr" title="命令内容" ellipsis />
			<Table.Column
				dataIndex="execDate"
				title="执行时间"
				width={180}
				ellipsis
			/>
		</Table>
	);
}

import React, { useEffect, useState } from 'react';
import {
	<PERSON><PERSON>,
	Divider,
	Drawer,
	Form,
	Input,
	Radio,
	Select,
	Space,
	Spin,
	notification,
	Modal
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { ListCard, ListCardItem } from '@/components/ListCard';
import Actions from '@/components/Actions';
import { formItemLayout618, maintenances } from '@/utils/const';
import { IconFont } from '@/components/IconFont';
import {
	addOperator,
	deleteOperator,
	executeOperator,
	getOperators,
	getPods,
	safeCheck,
	updateOperator
} from '@/services/middleware';
import { useHistory, useParams } from 'react-router';
import { AccessIndexParams } from '../detail';
import pattern from '@/utils/pattern';
import { controlledOperationDisabled, podRoleRender } from '@/utils/utils';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import useRefresh from '@/utils/useRefresh';
const { confirm } = Modal;
const LinkButton = Actions.LinkButton;
export default function AccessOperator(props: { data: any }): JSX.Element {
	const { data } = props;
	const params: AccessIndexParams = useParams();
	const history = useHistory();
	const [dataSource, setDataSource] = useState<OperatorItem[]>([]);
	const [spinning, setSpinning] = useState<boolean>(false);
	const [open, setOpen] = useState<boolean>(false);
	const [editData, setEditData] = useState<OperatorItem>();
	const [pods, setPods] = useState<any[]>([]);
	const [loading, setLoading] = useState<boolean>(false);
	const addOperatorId = maintenances['Add Operational Capability'];
	const editOperatorId = maintenances['Edit Operational Capability'];
	const execOperatorId = maintenances['Execute Operational Capability'];
	const deleteOperatorId = maintenances['Delete Operational Capability'];
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	const [form] = Form.useForm();
	useEffect(() => {
		getData();
		getPods({
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params.middlewareName,
			type: params.name,
			deployMod: 'server'
		}).then((res) => {
			if (res.success) {
				setPods(res.data.pods);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	}, [refreshKey]);
	const getData = () => {
		setSpinning(true);
		getOperators({
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params.middlewareName,
			type: params.name
		})
			.then((res) => {
				if (res.success) {
					setDataSource(res.data);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setSpinning(false);
			});
	};
	const execute = (record: OperatorItem) => {
		confirm({
			title: '操作确认',
			content: (
				<>
					<p>即将执行已选择的运维能力</p>
					<p>是否继续?</p>
				</>
			),
			onOk: async () => {
				await ExecuteOrderFuc();
				return executeOperator({
					clusterId: params.clusterId,
					namespace: params.namespace,
					middlewareName: params.middlewareName,
					devopsAbilityName: record.name,
					operationScope: record.operationScope,
					// pod: record.pod,
					exec: record.exec
				}).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '该操作执行成功！'
						});
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};
	const deleteOpe = (record: OperatorItem) => {
		confirm({
			title: '操作提示',
			content: '删除运维能力后将无法恢复，请谨慎操作！',
			onOk: async () => {
				await ExecuteOrderFuc();
				return deleteOperator({
					clusterId: params.clusterId,
					namespace: params.namespace,
					middlewareName: params.middlewareName,
					devopsAbilityName: record.name,
					operationScope: record.operationScope
					// pod: record.pod
				}).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '该操作删除成功'
						});
						getData();
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};
	const handleSubmit = () => {
		form.validateFields().then(async (values) => {
			setLoading(true);
			await ExecuteOrderFuc();
			if (editData) {
				const res = await safeCheck({ cmd: values.exec });
				if (res.data) {
					updateOperator({
						clusterId: params.clusterId,
						namespace: params.namespace,
						middlewareName: params.middlewareName,
						...values,
						middleware: params.name,
						name: editData.name,
						defaultAbility: editData.defaultAbility,
						instances: values.instances?.map((item: string) => ({
							instanceName: item
						}))
					})
						.then((res) => {
							if (res.success) {
								notification.success({
									message: '成功',
									description: '该操作编辑成功！'
								});
							} else {
								notification.error({
									message: '错误',
									description: (
										<>
											<p>{res.errorMsg}</p>
											<p>{res.errorDetail}</p>
										</>
									)
								});
							}
						})
						.finally(() => {
							setLoading(false);
							setEditData(undefined);
							setOpen(false);
							getData();
						});
				} else {
					notification.error({
						message: '错误',
						description: '命令内容输入不正确，请重新输入！'
					});
					setLoading(false);
				}
			} else {
				const res = await safeCheck({ cmd: values.exec });
				if (res.data) {
					addOperator({
						clusterId: params.clusterId,
						namespace: params.namespace,
						middlewareName: params.middlewareName,
						middleware: params.name,
						...values,
						instances: values.instances?.map((item: string) => ({
							instanceName: item
						}))
					})
						.then((res) => {
							if (res.success) {
								notification.success({
									message: '成功',
									description: '该操作新增成功！'
								});
							} else {
								notification.error({
									message: '错误',
									description: (
										<>
											<p>{res.errorMsg}</p>
											<p>{res.errorDetail}</p>
										</>
									)
								});
							}
						})
						.finally(() => {
							setLoading(false);
							setOpen(false);
							getData();
						});
				} else {
					notification.error({
						message: '错误',
						description: '命令内容输入不正确，请重新输入！'
					});
					setLoading(false);
				}
			}
		});
	};
	const executeDisabled = (res: OperatorItem): boolean => {
		if (res.operationScope === 1) {
			const statuses = res?.instanceNameSet.map((podName: string) => {
				const pod = pods?.find(
					(item: any) => item.podAliasName === podName
				);
				return pod?.status;
			});
			return (
				statuses.some(
					(status: string) =>
						status === 'Deleted' || status === 'ConnectFailed'
				) || !res?.instanceNameSet?.length
			);
		} else {
			return pods?.some(
				(item: any) =>
					item.status === 'Deleted' || item.status === 'ConnectFailed'
			);
		}
	};
	return (
		<>
			<Button
				type="dashed"
				block
				style={{ height: '64px', marginBottom: 16 }}
				disabled={controlledOperationDisabled(
					'maintenance',
					data?.lock
				)}
				onClick={() => {
					WorkOrderFuc(
						() => {
							setEditData(undefined);
							setOpen(true);
						},
						data.lock,
						params.middlewareName,
						addOperatorId,
						history,
						params.type,
						params.name,
						params.aliasName,
						params.clusterId,
						params.namespace
					);
				}}
			>
				<Space>
					<PlusOutlined /> 新增运维能力
				</Space>
			</Button>
			<Spin spinning={spinning}>
				{dataSource.map((item: OperatorItem, index: number) => {
					return (
						<ListCard
							key={index}
							actionStyle={{ width: '150px' }}
							actionRender={
								<Actions threshold={4}>
									<LinkButton
										disabled={controlledOperationDisabled(
											'maintenance',
											data?.lock
										)}
										onClick={() => {
											WorkOrderFuc(
												() => {
													execute(item);
												},
												data.lock,
												params.middlewareName,
												execOperatorId,
												history,
												params.type,
												params.name,
												params.aliasName,
												params.clusterId,
												params.namespace
											);
										}}
									>
										执行
									</LinkButton>
									<LinkButton
										disabled={controlledOperationDisabled(
											'maintenance',
											data?.lock
										)}
										onClick={() => {
											WorkOrderFuc(
												() => {
													setEditData(item);
													setOpen(true);
												},
												data.lock,
												params.middlewareName,
												editOperatorId,
												history,
												params.type,
												params.name,
												params.aliasName,
												params.clusterId,
												params.namespace
											);
										}}
									>
										编辑
									</LinkButton>
									<LinkButton
										disabled={
											item.defaultAbility ||
											controlledOperationDisabled(
												'maintenance',
												data?.lock
											)
										}
										onClick={() => {
											WorkOrderFuc(
												() => {
													deleteOpe(item);
												},
												data.lock,
												params.middlewareName,
												deleteOperatorId,
												history,
												params.type,
												params.name,
												params.aliasName,
												params.clusterId,
												params.namespace
											);
										}}
									>
										删除
									</LinkButton>
								</Actions>
							}
							title={item.aliasName || item.name}
							subTitle="名称"
							icon={
								<IconFont
									type="icon-yunweicaozuoicon"
									style={{
										width: '40px',
										height: '40px',
										fontSize: '24px',
										lineHeight: '43px',
										borderRadius: '2px',
										backgroundColor: '#e6a768',
										marginRight: '8px',
										color: 'white'
									}}
								/>
							}
						>
							<ListCardItem
								value={item.describe || '/'}
								label="描述"
								width={300}
							/>
							<ListCardItem
								value={
									<p
										style={{ width: 300 }}
										className="text-overflow"
										title={
											item.operationScope === 1
												? `实例(${
														item?.instances
															?.map(
																(item: any) =>
																	item.instanceName
															)
															?.join('，') || '/'
												  })`
												: '服务'
										}
									>
										{item.operationScope === 1
											? `实例(${
													item?.instances
														?.map(
															(item: any) =>
																item.instanceName
														)
														?.join('，') || '/'
											  })`
											: '服务'}
									</p>
								}
								label="作用范围"
								width={300}
							/>
						</ListCard>
					);
				})}
			</Spin>
			<Drawer
				destroyOnClose={true}
				title={editData ? '编辑运维能力' : '新增运维能力'}
				placement="right"
				onClose={() => {
					setEditData(undefined);
					setOpen(false);
				}}
				open={open}
				width={650}
			>
				<Form
					form={form}
					{...formItemLayout618}
					labelAlign="left"
					validateTrigger={['onBlur', 'onSubmit', 'onChange']}
					preserve={false}
				>
					<Form.Item
						name="aliasName"
						label="能力名称"
						rules={[
							{ required: true, message: '能力名称不能为空' },
							{
								pattern: new RegExp(pattern.operatorName),
								message:
									'请输入以中文、英文字母、数字、“_”、“-”、“/”、“.”组成的2-32个字符的能力名称'
							},
							({ getFieldValue }) => ({
								validateTrigger: ['onBlur', 'onSubmit'],
								async validator(_, value) {
									let list = dataSource;
									if (editData) {
										list = list.filter(
											(item) =>
												item.aliasName !==
												editData.aliasName
										);
									}
									if (
										list.find(
											(item) => item.aliasName === value
										)
									) {
										return Promise.reject(
											new Error('该运维能力名称已存在！')
										);
									} else {
										return Promise.resolve();
									}
								}
							})
						]}
						initialValue={
							editData
								? editData.aliasName || editData.name
								: undefined
						}
					>
						<Input
							disabled={editData?.defaultAbility}
							placeholder="请输入能力名称"
						/>
					</Form.Item>
					<Form.Item
						name="exec"
						label="命令内容"
						rules={[
							{ required: true, message: '命令内容不能为空' }
						]}
						initialValue={editData ? editData.exec : undefined}
					>
						<Input.TextArea rows={5} placeholder="请输入命令内容" />
					</Form.Item>
					<Form.Item
						name="describe"
						label="描述"
						initialValue={editData ? editData.describe : undefined}
						rules={[
							{
								type: 'string',
								min: 2,
								max: 512,
								message: '请输入2-512个字符的描述'
							}
						]}
					>
						<Input
							disabled={editData?.defaultAbility}
							placeholder="请输入描述信息"
						/>
					</Form.Item>
					<Form.Item
						name="operationScope"
						label="作用范围"
						rules={[{ required: true, message: '请选择作用范围' }]}
						initialValue={editData ? editData.operationScope : 2}
					>
						<Radio.Group
							disabled={!!editData?.operationScope || false}
							options={[
								{ label: '服务', value: 2 },
								{ label: '实例', value: 1 }
							]}
						/>
					</Form.Item>
					<Form.Item noStyle shouldUpdate>
						{({ getFieldValue }) => {
							if (getFieldValue('operationScope') === 1) {
								return (
									<Form.Item
										name="instances"
										label="选择实例"
										rules={[
											{
												required: true,
												message: '请选择实例'
											}
										]}
										initialValue={editData?.instances?.map(
											(item: any) => item.instanceName
										)}
									>
										<Select mode="multiple">
											{pods.map((item: any) => {
												return (
													<Select.Option
														value={
															item.podAliasName
														}
														key={item.podAliasName}
													>
														{item.podAliasName}(
														{podRoleRender(
															item.role,
															params.name
														)}
														)
													</Select.Option>
												);
											})}
										</Select>
									</Form.Item>
								);
							} else {
								return null;
							}
						}}
					</Form.Item>
					<Divider />
					<Space>
						<Button
							onClick={() => {
								setEditData(undefined);
								setOpen(false);
							}}
						>
							取消
						</Button>
						<Button
							type="primary"
							onClick={handleSubmit}
							loading={loading}
						>
							确定
						</Button>
					</Space>
				</Form>
			</Drawer>
		</>
	);
}

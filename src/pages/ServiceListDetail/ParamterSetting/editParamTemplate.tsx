import React, { useEffect, useState } from 'react';
import { useParams, useHistory } from 'react-router';
import { ProPage, ProContent, ProHeader } from '@/components/ProPage';
import { Button, Form, Input, notification, Steps, Result, Select } from 'antd';
import FormBlock from '@/components/FormBlock';
import ParamEditTable from './components/paramEditTable';
import {
	createParamsTemp,
	getParamsTemp,
	editParamsTemp
} from '@/services/template';
import { getConfigs } from '@/services/middleware';
import pattern from '@/utils/pattern';
import { formItemLayout614 } from '@/utils/const';
import { filterProps } from '@/utils/constant';
import { getConfigRole } from '@/services/middleware';
import { ConfigItem } from '../detail';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';

const { Step } = Steps;
export interface ParamsProps {
	type: string;
	name: string;
	aliasName: string;
	chartVersion: string;
	middlewareName: string;
	uid: string;
	templateName: string;
	currentTab: string;
	clusterId: string;
	namespace: string;
	role: string;
}

const FormItem = Form.Item;
function EditParamTemplate(): JSX.Element {
	const {
		type,
		chartVersion,
		middlewareName,
		uid,
		templateName,
		name,
		aliasName,
		clusterId,
		namespace,
		role
	}: ParamsProps = useParams();
	const [current, setCurrent] = useState<number>(0);
	const [btnDisable, setBtnDisable] = useState<boolean>(false);
	const [fixFlag, setFixFlag] = useState<boolean>(false);
	const [countdown, setCountDown] = useState<number>(5);
	const [form] = Form.useForm();
	const history = useHistory();
	const [roleList, setRoleList] = useState<filterProps[]>([]);
	const [nodeType, setNodeType] = useState<string>();
	const [paramTemplateBasic, setParamTemplateBasic] = useState<any>();
	const [paramTemplateData, setParamTemplateData] = useState<ConfigItem[]>(
		[]
	);

	useEffect(() => {
		clusterId && getRoles(clusterId, namespace, middlewareName, name);
	}, [clusterId]);
	useEffect(() => {
		if (uid) {
			const sendData = {
				uid,
				type: name,
				chartVersion,
				clusterId
			};
			getParamsTemp(sendData).then((res) => {
				if (res.success) {
					setParamTemplateBasic({
						name: res.data.name,
						description: res.data.description
					});
					setParamTemplateData(res.data.customConfigList);
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		}
	}, [uid]);
	useEffect(() => {
		let timer: any = null;
		if (current === 0 && paramTemplateBasic) {
			form.setFieldsValue({
				name: paramTemplateBasic.name,
				description: paramTemplateBasic.description
			});
		}
		if (current === 2 || current === 3) {
			if (countdown !== -1) {
				let count = countdown;
				timer = setInterval(() => {
					if (count === -1) {
						clearInterval(timer);
						timer = null;
						history.push(
							`/project/${type}/${name}/${aliasName}/container/paramterSetting/${middlewareName}/${chartVersion}/${clusterId}/${namespace}`
						);
					} else {
						setCountDown(count--);
					}
				}, 1000);
			}
		}
		return () => {
			clearInterval(timer);
		};
	}, [current, paramTemplateBasic]);
	useEffect(() => {
		const content =
			document.getElementsByClassName('windcc-app-layout__content')[0]
				?.clientHeight - 147;
		const OneArea = document.getElementsByClassName(
			'param-step-one-content'
		)[0]?.clientHeight;
		const twoArea = document.getElementsByClassName(
			'zeus-param-edit-table-content'
		)[0]?.clientHeight;
		if (current === 0) {
			if (content > OneArea) {
				setFixFlag(false);
			} else {
				setFixFlag(true);
			}
		} else {
			if (content > twoArea) {
				setFixFlag(false);
			} else {
				setFixFlag(true);
			}
		}
		window.onresize = function () {
			const content =
				document.getElementsByClassName('windcc-app-layout__content')[0]
					?.clientHeight - 147;
			const OneArea = document.getElementsByClassName(
				'param-step-one-content'
			)[0]?.clientHeight;
			const twoArea = document.getElementsByClassName(
				'zeus-param-edit-table-content'
			)[0]?.clientHeight;
			if (content > OneArea) {
				setFixFlag(false);
			} else if (content > twoArea) {
				setFixFlag(false);
			} else {
				setFixFlag(true);
			}
		};
	}, [
		document.getElementsByClassName('zeus-param-edit-table-content')[0]
			?.clientHeight,
		document.getElementsByClassName('param-step-one-content')[0]
			?.clientHeight
	]);
	const getRoles = (
		clusterId: string,
		namespace: string,
		middlewareName: string,
		name: string
	) => {
		getConfigRole({
			clusterId,
			namespace,
			middlewareName,
			type: name
		}).then((res) => {
			if (res.success) {
				setRoleList(
					res.data.map((item: string) => {
						return {
							label:
								item === 'major' ? '服务节点' : item + '节点',
							value: item
						};
					})
				);
			}
		});
	};
	const getData = (
		clusterId: string,
		namespace: string,
		middlewareName: string,
		name: string,
		role: string,
		order = 'descend'
	) => {
		const sendData = {
			clusterId,
			namespace,
			middlewareName,
			type: name,
			order,
			role,
			filterAddition: true
		};
		getConfigs(sendData).then((res) => {
			if (res.success) {
				const list =
					res.data &&
					res.data.map((item: ConfigItem) => {
						item.modifiedValue =
							item.targetValue || item.defaultValue;
						item.value = item.value || item.defaultValue;
						return item;
					});
				setParamTemplateData(list);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const goLast = () => {
		setCurrent(current - 1);
		if (current - 1 === 0) {
			form.setFieldsValue({
				name: paramTemplateBasic.name,
				description: paramTemplateBasic.description
			});
		}
	};
	const goNext = () => {
		if (current === 0) {
			form.validateFields().then((values) => {
				setParamTemplateBasic(values);
				setCurrent(current + 1);
			});
			if (!uid) {
				getData(
					clusterId,
					namespace,
					middlewareName,
					name,
					nodeType || 'major'
				);
			}
		}
		if (current === 1) {
			setCurrent(current + 1);
			if (uid) {
				// * 更新模板
				const sendData = {
					name: paramTemplateBasic.name,
					description: paramTemplateBasic.description,
					customConfigList: paramTemplateData.map((item) => {
						item.targetValue = item.modifiedValue;
						return item;
					}),
					type: name,
					uid,
					role,
					clusterId,
					chartVersion
				};
				ExecuteOrderFuc(() => {
					editParamsTemp(sendData).then((res) => {
						if (res.success) {
							setCurrent(2);
						} else {
							setCurrent(3);
						}
					});
				});
			} else {
				// * 创建模板
				const sendData = {
					name: paramTemplateBasic.name,
					description: paramTemplateBasic.description,
					customConfigList: paramTemplateData.map((item) => {
						item.targetValue = item.modifiedValue;
						return item;
					}),
					type: name,
					role: nodeType || 'major',
					clusterId,
					chartVersion
				};
				ExecuteOrderFuc(() => {
					createParamsTemp(sendData).then((res) => {
						if (res.success) {
							setCurrent(2);
						} else {
							setCurrent(3);
							notification.error({
								message: '错误',
								description: (
									<>
										<p>{res.errorMsg}</p>
										<p>{res.errorDetail}</p>
									</>
								)
							});
						}
					});
				});
			}
		}
	};
	const childrenRender = (value: number) => {
		switch (value) {
			case 0:
				return (
					<FormBlock
						title="基础信息"
						className="param-step-one-content"
					>
						<Form
							form={form}
							{...formItemLayout614}
							labelAlign="left"
							style={{ paddingLeft: 8, width: '50%' }}
						>
							<FormItem
								label="模板名称"
								required
								name="name"
								rules={[
									{
										pattern: new RegExp(
											pattern.paramTemplateName
										),
										message:
											'请输入由小写字母数字及“-”组成的2-30个字符'
									},
									{
										required: true,
										message: '模板名称不能为空'
									}
								]}
							>
								<Input
									// minLength={2}
									maxLength={30}
									placeholder="请输入由小写字母数字及“-”组成的2-30个字符"
								/>
							</FormItem>
							{(name === 'mysql' || name === 'redis') && (
								<FormItem
									label="节点类型"
									name="role"
									rules={[
										{
											required: true,
											message: '请选择节点类型'
										}
									]}
									initialValue={role}
								>
									<Select
										value={nodeType}
										disabled={!!uid}
										options={roleList}
										placeholder="请选择节点类型"
										onChange={(value) => setNodeType(value)}
									/>
								</FormItem>
							)}
							<FormItem
								label="描述"
								name="description"
								rules={[
									{
										max: 100,
										message: '描述长度不可超过100字符'
									}
								]}
							>
								<Input.TextArea
									placeholder="请输入描述"
									maxLength={100}
									showCount
								/>
							</FormItem>
						</Form>
					</FormBlock>
				);
			case 1:
				return (
					<ParamEditTable
						lock="unlocked"
						clusterId={clusterId}
						namespace={namespace}
						middlewareName={middlewareName}
						type={name}
						handleBtnClick={setBtnDisable}
						role={role || nodeType || 'major'}
						paramTemplateData={paramTemplateData}
					/>
				);
			case 2:
				return (
					<Result
						className="zeus-params"
						status="success"
						title={
							uid ? '恭喜，模板修改完成!' : '恭喜，模板创建完成!'
						}
						extra={
							<Button
								type="primary"
								onClick={() => {
									history.push(
										`/project/${type}/${name}/${aliasName}/container/paramterSetting/${middlewareName}/${chartVersion}/${clusterId}/${namespace}`
									);
								}}
							>
								返回列表({countdown}s)
							</Button>
						}
					>
						<div className="zeus-param-display-area-content">
							<div className="title-content">
								<div className="blue-line"></div>
								<div className="detail-title">基础信息</div>
							</div>
							<ul className="zeus-param-display-info">
								<li>
									<label>模板名称</label>
									<span>{paramTemplateBasic.name}</span>
								</li>
								{(name === 'mysql' || name === 'redis') && (
									<li>
										<label>节点类型</label>
										<span>
											{
												roleList.find(
													(item: filterProps) =>
														item.value ===
														(role || nodeType)
												)?.label
											}
										</span>
									</li>
								)}
								<li>
									<label>描述</label>
									<span
										title={paramTemplateBasic.description}
									>
										{paramTemplateBasic.description}
									</span>
								</li>
							</ul>
						</div>
					</Result>
				);
			case 3:
				return (
					<Result
						status="error"
						title={uid ? '模板修改失败' : '模板创建失败'}
						extra={
							<Button
								type="primary"
								onClick={() => {
									history.push(
										`/project/${type}/${name}/${aliasName}/container/paramterSetting/${middlewareName}/${chartVersion}/${clusterId}/${namespace}`
									);
								}}
							>
								返回创建列表({countdown}s)
							</Button>
						}
					/>
				);
			default:
				break;
		}
	};
	return (
		<ProPage>
			<ProHeader
				title={uid ? '修改参数模板' : '新建参数模板'}
				onBack={() => {
					window.history.back();
				}}
			/>
			<ProContent className="param-content">
				<Steps
					style={{ marginBottom: 32 }}
					current={current}
					labelPlacement="horizontal"
				>
					<Step key={0} title="基础信息" />
					<Step key={1} title="自定义参数" />
					<Step key={2} title="完成" />
				</Steps>
				{childrenRender(current)}
				{(current === 0 || current === 1) && (
					<div
						className={`zeus-edit-param-summit-btn ${
							fixFlag ? 'zeus-edit-param-summit-btn-fix' : ''
						}`}
						style={{
							background: btnDisable ? '#F8F8F9' : '#ffffff',
							cursor: btnDisable ? 'not-allowed' : 'auto'
						}}
					>
						{current === 1 && (
							<Button
								disabled={btnDisable}
								type="default"
								onClick={goLast}
							>
								上一步
							</Button>
						)}
						<Button
							disabled={
								btnDisable ||
								(!paramTemplateData.length && current === 1)
							}
							type="primary"
							onClick={goNext}
						>
							下一步
						</Button>
						<Button
							disabled={btnDisable}
							type="default"
							onClick={() => {
								window.history.back();
							}}
						>
							取消
						</Button>
					</div>
				)}
			</ProContent>
		</ProPage>
	);
}
export default EditParamTemplate;

import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router';
import { Menu } from 'antd';
import ParamterList from './paramerListVersiontwo';
import ParamterHistory from './paramterHistory';
import ParamterTemplate from './paramterTemplate';
import ConfigMapEdit from './configMapEdit';
import ParameterBase from './parameterBase';
import DefaultPicture from '@/components/DefaultPicture';
import storage from '@/utils/storage';
import { ParamterSettingProps, DetailParams } from '../detail';
import { MenuInfo } from '@/types/comment';
import SecondContent from '@/components/SecondContent';

export default function ParamterSetting(
	props: ParamterSettingProps
): JSX.Element {
	const { customMid, capabilities, readWriteProxy, lock, subMenu } = props;
	const {
		clusterId,
		name,
		namespace,
		middlewareName,
		currentTab
	}: DetailParams = useParams();
	const hasWebpage = window.location.href.includes('webpage');
	const [selectedKey, setSelectedKey] = useState<string[]>([
		storage.getSession('paramsTab') || 'list'
	]);
	const [items, setItems] = useState(
		name === 'mysql' ||
			name === 'redis' ||
			name === 'postgresql' ||
			name === 'elasticsearch'
			? [
					{ label: '参数列表', key: 'list', code: 'parameterList' },
					{
						label: '参数模板',
						key: 'template',
						code: 'parameterTemplate'
					},
					{
						label: '参数修改历史',
						key: 'config',
						code: 'parameterHistory'
					},
					{
						label: '额外参数库',
						key: 'moreBase',
						code: 'parameterLibrary'
					}
					// { label: 'ConfigMap编辑', key: 'configMap' }
			  ]
			: [
					{ label: '参数列表', key: 'list', code: 'parameterList' },
					{
						label: '参数模板',
						key: 'template',
						code: 'parameterTemplate'
					},
					{
						label: '参数修改历史',
						key: 'config',
						code: 'parameterHistory'
					}
					// { label: 'ConfigMap编辑', key: 'configMap' }
			  ]
	);

	const menuSelect = (info: MenuInfo) => {
		setSelectedKey(info.keyPath);
		storage.setSession('paramsTab', info.key);
	};

	useEffect(() => {
		const menu = items.filter((item: any) =>
			subMenu.find((menu: any) => menu.name === item.code)
		);
		if (!hasWebpage) {
			setItems(menu);
		} else {
			menuSelect({ keyPath: ['list'], key: 'list' } as MenuInfo);
		}
		// (!storage.getSession('paramsTab') ||
		// 	!menu?.some(
		// 		(item) => item.key === storage.getSession('paramsTab')
		// 	)) &&
		// 	setSelectedKey([menu[0]?.key]);
	}, [subMenu]);

	const ConsoleMenu = () => (
		<Menu
			selectedKeys={selectedKey}
			onClick={menuSelect}
			style={{ height: '100%' }}
			items={items}
			mode="inline"
			className="serve-alarm-menu"
		/>
	);
	const childrenRender = (selectedKey: string) => {
		switch (selectedKey) {
			case 'list':
				return (
					<ParamterList
						clusterId={clusterId}
						middlewareName={middlewareName}
						namespace={namespace}
						type={name}
						readWriteProxy={readWriteProxy}
						lock={lock}
					/>
				);
			case 'config':
				return <ParamterHistory lock={lock} />;
			case 'template':
				return <ParamterTemplate lock={lock} />;
			case 'moreBase':
				return <ParameterBase lock={lock} />;
			case 'configMap':
				return <ConfigMapEdit />;
			default:
				return null;
		}
	};
	if (customMid && !(capabilities || []).includes('config')) {
		return <DefaultPicture />;
	}
	return (
		<SecondContent menu={<ConsoleMenu />} style={{ margin: 0, padding: 0 }}>
			{childrenRender(selectedKey[0])}
		</SecondContent>
	);
}

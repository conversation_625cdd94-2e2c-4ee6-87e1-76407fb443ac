import React, { useState, useEffect } from 'react';
import { DatePicker, Modal, Space, Tooltip, notification } from 'antd';
import { useHistory, useParams } from 'react-router';
import { ProPage, ProContent } from '@/components/ProPage';
import ProTable from '@/components/ProTable';
import {
	getConfigHistory,
	getConfigRole,
	rollbackParam
} from '@/services/middleware';
import transTime from '@/utils/transTime';
import {
	ParamterHistoryItem,
	ParamterHistorySendData,
	DetailParams
} from '../detail';
import { filterProps } from '@/utils/constant';
import useRefresh from '@/utils/useRefresh';
import Actions from '@/components/Actions';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';
import { QuestionCircleOutlined } from '@ant-design/icons';
const LinkButton = Actions.LinkButton;
const { RangePicker } = DatePicker;
const { confirm } = Modal;
export default function ParamterHistory({
	lock
}: {
	lock: string;
}): JSX.Element {
	const history = useHistory();
	const {
		clusterId,
		namespace,
		middlewareName,
		name,
		aliasName,
		type
	}: DetailParams = useParams();
	const [dataSource, setDataSource] = useState<ParamterHistoryItem[]>([]);
	const [searchText, setSearchText] = useState<string>('');
	const [startTime, setStartTime] = useState<string>();
	const [endTime, setEndTime] = useState<string>();
	const [roleList, setRoleList] = useState<filterProps[]>([]);
	const [refreshKey, setRefreshKey] = useState<number>(0);
	const editParamsListOperatorId = maintenances['Edit Parameter'];
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		const start = startTime ? transTime.local2gmt2(startTime) : null;
		const end = endTime ? transTime.local2gmt2(endTime) : null;
		getData(searchText, start, end);
		getRoles();
	}, [refreshKey]);

	const getData = (
		searchText: string,
		startTime: string | null = null,
		endTime: string | null = null
	) => {
		const sendData: ParamterHistorySendData = {
			clusterId,
			namespace,
			middlewareName,
			type: name,
			item: searchText
		};
		if (startTime && endTime) {
			sendData.startTime = startTime;
			sendData.endTime = endTime;
		}
		getConfigHistory(sendData).then((res) => {
			if (res.success) {
				setDataSource(res.data);
			}
		});
	};
	const onChange = (val: any) => {
		if (val) {
			setStartTime(val[0]);
			setEndTime(val[1]);
			const start = transTime.local2gmt2(val[0]);
			const end = transTime.local2gmt2(val[1]);
			getData(searchText, start, end);
		} else {
			setStartTime(undefined);
			setEndTime(undefined);
			getData(searchText);
		}
	};

	const Operation = {
		primary: <RangePicker showTime onChange={onChange} />
	};
	const handleSearch = (value: string) => {
		const start = startTime ? transTime.local2gmt2(startTime) : null;
		const end = endTime ? transTime.local2gmt2(endTime) : null;
		getData(value, start, end);
	};

	const statusRender = (value: boolean) => {
		return value ? '是' : '否';
	};

	const getRoles = () => {
		getConfigRole({
			clusterId,
			namespace,
			middlewareName,
			type: name
		}).then((res) => {
			if (res.success) {
				setRoleList(
					res.data.map((item: string) => {
						return {
							label:
								item === 'major' ? '服务节点' : item + '节点',
							value: item
						};
					})
				);
			}
		});
	};
	const rollback = (record: ParamterHistoryItem) => {
		const contentTemp = record.restart ? (
			'回退该参数修改后，相关参数将会恢复变更前参数值，并重启服务，是否确定进行回退？'
		) : (
			<>
				<p>回退该参数修改后，相关参数将会恢复变更前参数值</p>
				<p>是否确定进行回退？</p>
			</>
		);
		confirm({
			title: '操作确认',
			content: contentTemp,
			onOk: async () => {
				await ExecuteOrderFuc();
				return rollbackParam({
					item: record.item,
					last: record.last,
					after: record.after,
					restart: record.restart,
					role: record.role,
					clusterId,
					namespace,
					middlewareName,
					type: name
				}).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '参数回退成功'
						});
						const start = startTime
							? transTime.local2gmt2(startTime)
							: null;
						const end = endTime
							? transTime.local2gmt2(endTime)
							: null;
						getData(searchText, start, end);
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};
	const actionRender = (value: any, record: ParamterHistoryItem) => {
		return (
			<Actions>
				<LinkButton
					code="parameterHistoryRollBack"
					title={
						!record.allowRollback
							? '当前参数为不可变参数，无法进行回退，请联系管理员处理'
							: ''
					}
					disabled={!record.allowRollback}
					onClick={() => {
						WorkOrderFuc(
							() => {
								rollback(record);
							},
							lock,
							middlewareName,
							editParamsListOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
				>
					回退
				</LinkButton>
			</Actions>
		);
	};
	return (
		<ProPage>
			<ProContent style={{ padding: '0 0' }}>
				<ProTable
					dataSource={dataSource}
					rowKey="id"
					operation={Operation}
					search={{
						value: searchText,
						onSearch: handleSearch,
						onChange: (value) => setSearchText(value.target.value),
						placeholder: '请输入关键词搜索'
					}}
				>
					<ProTable.Column
						title="参数名"
						dataIndex="item"
						width={210}
						ellipsis={true}
						fixed="left"
					/>
					{(name === 'mysql' || name === 'redis') && (
						<ProTable.Column
							title="节点类型"
							dataIndex="role"
							width={120}
							filters={roleList.map((item: filterProps) => {
								return { value: item.value, text: item.label };
							})}
							filterMultiple={false}
							onFilter={(
								value: any,
								record: ParamterHistoryItem
							) => record.role === value}
							render={(value) =>
								roleList.find(
									(item: filterProps) => item.value === value
								)?.label || '/'
							}
						/>
					)}
					<ProTable.Column
						title="变更前的参数值"
						dataIndex="last"
						ellipsis={true}
						width={150}
					/>
					<ProTable.Column
						title="变更后的参数值"
						dataIndex="after"
						ellipsis={true}
						width={150}
					/>
					<ProTable.Column
						title={
							<div className="flex-space-between">
								<span title="是否生效">是否生效</span>
								<Tooltip title="参数不生效的原因可能在于未重启服务、控制器异常等">
									<QuestionCircleOutlined />
								</Tooltip>
							</div>
						}
						dataIndex="status"
						render={statusRender}
						width={100}
					/>
					<ProTable.Column title="变更时间" dataIndex="date" />
					<ProTable.Column
						title="操作"
						dataIndex="action"
						width={80}
						render={actionRender}
					/>
				</ProTable>
			</ProContent>
		</ProPage>
	);
}

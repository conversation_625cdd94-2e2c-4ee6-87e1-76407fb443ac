import React from 'react';
import ParamEditTable from './components/paramEditTable';

interface ParamerListProps {
	clusterId: string;
	namespace: string;
	type: string;
	middlewareName: string;
	readWriteProxy: { enabled: boolean } | null;
	lock: string;
}
export default function ParamerList(props: ParamerListProps): JSX.Element {
	const { clusterId, namespace, type, middlewareName, readWriteProxy, lock } =
		props;
	return (
		<ParamEditTable
			type={type}
			clusterId={clusterId}
			namespace={namespace}
			middlewareName={middlewareName}
			readWriteProxy={readWriteProxy}
			source="list"
			paramTemplateData={[]}
			lock={lock}
		/>
	);
}

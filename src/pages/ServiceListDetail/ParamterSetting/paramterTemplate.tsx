import React, { useEffect, useState } from 'react';
import moment from 'moment';
import { Button, notification, Modal } from 'antd';
import { Key } from 'antd/lib/table/interface';
import Actions from '@/components/Actions';
import ProTable from '@/components/ProTable';
import { useHistory, useParams } from 'react-router';
import { getParamsTemps, deleteParamsTemp } from '@/services/template';
import { getConfigRole } from '@/services/middleware';
import { DetailParams, ParamterTemplateItem } from '../detail';
import { controlledOperationDisabled, nullRender } from '@/utils/utils';
import { filterProps } from '@/utils/constant';
import useRefresh from '@/utils/useRefresh';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';
import Auth from '@/components/Auth';

const { confirm } = Modal;
const LinkButton = Actions.LinkButton;
const ParamterTemplate = ({ lock }: { lock: string }) => {
	const [originData, setOriginData] = useState([]);
	const [dataSource, setDataSource] = useState([]);
	const [roleList, setRoleList] = useState<filterProps[]>([]);
	const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);
	const [compareDisabled, setCompareDisabled] = useState<boolean>(false);
	const history = useHistory();
	const addTemplateOperatorId = maintenances['Add Parameter Template'];
	const deleteTemplateOperatorId = maintenances['Delete Parameter Template'];
	const useTemplateOperatorId = maintenances['Use Parameter Template'];
	const editTemplateOperatorId = maintenances['Edit Parameter Template'];
	const params: DetailParams = useParams();
	const {
		clusterId,
		namespace,
		middlewareName,
		chartVersion,
		name,
		aliasName,
		type
	} = params;
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		getRoles();
	}, [refreshKey]);
	useEffect(() => {
		roleList.length && getData();
	}, [roleList]);
	const getData = () => {
		setSelectedRowKeys([]);
		getParamsTemps({ type: name }).then((res) => {
			if (res.success) {
				setOriginData(
					res.data.filter((item: any) =>
						roleList.map((item) => item.value).includes(item.role)
					)
				);
				setDataSource(
					res.data.filter((item: any) =>
						roleList.map((item) => item.value).includes(item.role)
					)
				);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const getRoles = () => {
		getConfigRole({
			clusterId,
			namespace,
			middlewareName,
			type: name
		}).then((res) => {
			if (res.success) {
				setRoleList(
					res.data.map((item: string) => {
						return {
							label:
								item === 'major' ? '服务节点' : item + '节点',
							value: item
						};
					})
				);
			}
		});
	};

	const Operation = {
		primary: (
			<>
				<Auth code="parameterTemplateAdd">
					<Button
						onClick={() => {
							WorkOrderFuc(
								() => {
									history.push(
										`/project/${params.type}/${params.name}/${params.aliasName}/container/paramterSetting/template/${middlewareName}/${chartVersion}/${params.clusterId}/${params.namespace}`
									);
								},
								lock,
								middlewareName,
								addTemplateOperatorId,
								history,
								type,
								name,
								aliasName,
								clusterId,
								namespace
							);
						}}
						disabled={controlledOperationDisabled(
							'maintenance',
							lock
						)}
						type="primary"
					>
						新建
					</Button>
				</Auth>
				{/* {selectedRowKeys.length === 2 && (
					<Button
						onClick={() => {
							history.push(
								`/project/${params.type}/${params.name}/${params.aliasName}/container/paramterSetting/compareTemplate/${chartVersion}/${selectedRowKeys[0]}/${selectedRowKeys[1]}/${params.clusterId}/${params.namespace}/compare`
							);
						}}
						type="default"
						disabled={compareDisabled}
						title={
							compareDisabled
								? '不同节点类型的参数模板，无法进行比较'
								: ''
						}
					>
						对比
					</Button>
				)} */}
				{selectedRowKeys.length > 0 && (
					<Auth code="parameterTemplateDelete">
						<Button
							disabled={controlledOperationDisabled(
								'maintenance',
								lock
							)}
							onClick={() => {
								WorkOrderFuc(
									() => {
										const sendData = {
											type: name,
											uids: selectedRowKeys.join(',')
										};
										deleteTemp(sendData);
									},
									lock,
									middlewareName,
									deleteTemplateOperatorId,
									history,
									type,
									name,
									aliasName,
									clusterId,
									namespace
								);
							}}
							type="primary"
							danger
						>
							删除
						</Button>
					</Auth>
				)}
				{selectedRowKeys.length > 0 && (
					<Button
						type="default"
						onClick={() => {
							setSelectedRowKeys([]);
						}}
					>
						取消
					</Button>
				)}
			</>
		)
	};
	const deleteTemp = (sendData: { type: string; uids: string }) => {
		confirm({
			title: '操作确认',
			content: '请确认是否删除所选择自定义参数模板',
			onOk: async () => {
				await ExecuteOrderFuc();
				return deleteParamsTemp(sendData).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '模板删除成功'
						});
						getData();
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			}
		});
	};
	const actionRender = (
		value: string,
		record: ParamterTemplateItem,
		index: number
	) => {
		return (
			<Actions>
				<LinkButton
					code="parameterTemplateUse"
					onClick={() => {
						WorkOrderFuc(
							() => {
								history.push(
									`/project/${params.type}/${params.name}/${params.aliasName}/container/paramterSetting/useTemplate/${params.middlewareName}/${params.chartVersion}/${record.uid}/${params.clusterId}/${params.namespace}/${record.role}`
								);
							},
							lock,
							middlewareName,
							useTemplateOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
					disabled={
						selectedRowKeys.length > 0 ||
						controlledOperationDisabled('maintenance', lock)
					}
					title={selectedRowKeys.length > 0 ? '请取消勾选后操作' : ''}
				>
					使用
				</LinkButton>
				<LinkButton
					code="parameterTemplateUpdate"
					onClick={() => {
						WorkOrderFuc(
							() => {
								history.push(
									`/project/${params.type}/${params.name}/${params.aliasName}/container/paramterSetting/template/${params.middlewareName}/${params.chartVersion}/${record.uid}/${record.name}/${params.clusterId}/${params.namespace}/${record.role}`
								);
							},
							lock,
							middlewareName,
							editTemplateOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
					disabled={
						selectedRowKeys.length > 0 ||
						controlledOperationDisabled('maintenance', lock)
					}
					title={selectedRowKeys.length > 0 ? '请取消勾选后操作' : ''}
				>
					编辑
				</LinkButton>
				<LinkButton
					code="parameterTemplateDelete"
					onClick={() => {
						WorkOrderFuc(
							() => {
								const sendData = {
									type: record.type,
									uids: record.uid
								};
								deleteTemp(sendData);
							},
							lock,
							middlewareName,
							deleteTemplateOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
					disabled={
						selectedRowKeys.length > 0 ||
						controlledOperationDisabled('maintenance', lock)
					}
					title={selectedRowKeys.length > 0 ? '请取消勾选后操作' : ''}
				>
					删除
				</LinkButton>
			</Actions>
		);
	};
	const handleSearch = (value: string) => {
		const list = originData.filter((item: any) =>
			item.name.includes(value)
		);
		setDataSource(list);
	};
	const onChange = (selectedRowKeys: Key[]) => {
		const t1: any = dataSource.find(
			(item: any) => item.uid === selectedRowKeys[0]
		);
		const t2: any = dataSource.find(
			(item: any) => item.uid === selectedRowKeys[1]
		);
		if (t1?.role !== t2?.role) {
			setCompareDisabled(true);
		} else {
			setCompareDisabled(false);
		}
		setSelectedRowKeys(selectedRowKeys);
	};
	return (
		<ProTable
			dataSource={dataSource}
			rowKey="uid"
			operation={Operation}
			search={{
				onSearch: handleSearch,
				placeholder: '请输入关键词搜索'
			}}
			rowSelection={{
				onChange: onChange,
				selectedRowKeys: selectedRowKeys
			}}
		>
			<ProTable.Column
				title="模板名称"
				dataIndex="name"
				ellipsis={true}
				fixed="left"
				width={150}
			/>
			<ProTable.Column
				title="描述"
				dataIndex="description"
				ellipsis={true}
				render={nullRender}
			/>
			{(name === 'mysql' || name === 'redis') && (
				<ProTable.Column
					title="节点类型"
					dataIndex="role"
					width={160}
					filters={roleList.map((item: filterProps) => {
						return { value: item.value, text: item.label };
					})}
					filterMultiple={false}
					onFilter={(value: any, record: any) =>
						record.role === value
					}
					render={(value) =>
						roleList.find(
							(item: filterProps) => item.value === value
						)?.label || '/'
					}
				/>
			)}
			<ProTable.Column
				title="创建时间"
				dataIndex="createTime"
				width={180}
				sorter={(a: ParamterTemplateItem, b: ParamterTemplateItem) =>
					moment(a.createTime).unix() - moment(b.createTime).unix()
				}
			/>
			<ProTable.Column
				title="操作"
				dataIndex="action"
				width={180}
				render={actionRender}
			/>
		</ProTable>
	);
};
export default ParamterTemplate;

import * as React from 'react';
import { useState, useEffect } from 'react';
import { useHistory, useParams } from 'react-router';
import { Button, Input, Modal, notification } from 'antd';
import ProTable from '@/components/ProTable';
import Actions from '@/components/Actions';
import AddParameterBase from './addParameterBase';
import { controlledOperationDisabled, nullRender } from '@/utils/utils';

import {
	deleteParam,
	getParam,
	getConfigRole,
	addition
} from '@/services/middleware';
import { filterProps } from '@/utils/constant';
import { paramTypeList } from './addParameterBase';
import { DetailParams } from '../detail';
import useRefresh from '@/utils/useRefresh';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';
import Auth from '@/components/Auth';

const LinkButton = Actions.LinkButton;
const ParameterBase = ({ lock }: { lock: string }): JSX.Element => {
	const [visible, setVisible] = useState<boolean>();
	const params: DetailParams = useParams();
	const history = useHistory();
	const { clusterId, namespace, middlewareName, name, type, aliasName } =
		params;
	const [keyword, setKeyword] = useState<string>('');
	const [dataSource, setDataSource] = useState();
	const [record, setRecord] = useState<any>();
	const [roleList, setRoleList] = useState<filterProps[]>([]);
	const [selectedRows, setSelectedRows] = useState<any>([]);
	const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>();
	const addMoreBaseOperatorId = maintenances['More Add Parameter'];
	const addToListMoreBaseOperatorId = maintenances['More Add to List'];
	const editMoreBaseOperatorId = maintenances['More Edit Parameter'];
	const deleteMoreBaseOperatorId = maintenances['More Delete Parameter'];
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		getData('');
		getRoles();
	}, [refreshKey]);

	const getData = (keyword: string) => {
		getParam({ middlewareType: name, keyword }).then((res) => {
			if (res.success) {
				setDataSource(res.data);
			} else {
				notification.error({
					message: '失败',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};

	const getRoles = () => {
		getConfigRole({
			clusterId,
			namespace,
			middlewareName,
			type: name
		}).then((res) => {
			if (res.success) {
				setRoleList(
					res.data.map((item: string) => {
						return {
							label:
								item === 'major' ? '服务节点' : item + '节点',
							value: item
						};
					})
				);
			}
		});
	};

	const deleteData = (value: string, record: any) => {
		Modal.confirm({
			title: '提示',
			content: '是否确认删除',
			onOk: async () => {
				await ExecuteOrderFuc();
				return deleteParam({
					middlewareType: name,
					paramName: record.name
				}).then((res) => {
					if (res.success) {
						getData('');
						notification.success({
							message: '成功',
							description: '删除成功'
						});
					} else {
						notification.error({
							message: '失败',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};

	const addParamList = () => {
		Modal.confirm({
			title: '操作确认',
			content: (
				<>
					<p>将选中参数添加到参数列表</p>
					<p>是否继续?</p>
				</>
			),
			onOk: async () => {
				await ExecuteOrderFuc();
				return addition({
					clusterId,
					namespace,
					middlewareName,
					type: name,
					chartVersion: params.chartVersion,
					customConfigList: selectedRows
				}).then((res) => {
					if (res.success) {
						getData('');
						setSelectedRowKeys([]);
						notification.success({
							message: '成功',
							description: '添加成功'
						});
					} else {
						notification.error({
							message: '失败',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};

	const Operation = {
		primary: (
			<>
				<Auth code="parameterLibraryAdd">
					<Button
						type="primary"
						disabled={controlledOperationDisabled(
							'maintenance',
							lock
						)}
						onClick={() => {
							WorkOrderFuc(
								() => {
									setVisible(true);
									setRecord(null);
								},
								lock,
								middlewareName,
								addMoreBaseOperatorId,
								history,
								type,
								name,
								aliasName,
								clusterId,
								namespace
							);
						}}
					>
						新增
					</Button>
				</Auth>
				<Auth code="parameterLibraryAddToList">
					<Button
						disabled={
							!selectedRowKeys?.length ||
							controlledOperationDisabled('maintenance', lock)
						}
						onClick={() => {
							WorkOrderFuc(
								addParamList,
								lock,
								middlewareName,
								addToListMoreBaseOperatorId,
								history,
								type,
								name,
								aliasName,
								clusterId,
								namespace
							);
						}}
					>
						添加到
					</Button>
				</Auth>
				<Input.Search
					placeholder="请输入关键字搜索"
					value={keyword}
					allowClear
					onChange={(e) => setKeyword(e.target.value)}
					onSearch={(value) => getData(value)}
				/>
			</>
		)
	};
	const actionRender = (value: string, record: any) => {
		return (
			<Actions>
				<LinkButton
					code="parameterLibraryUpdate"
					disabled={controlledOperationDisabled('maintenance', lock)}
					onClick={() => {
						WorkOrderFuc(
							() => {
								setVisible(true);
								setRecord(record);
							},
							lock,
							middlewareName,
							editMoreBaseOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
				>
					编辑
				</LinkButton>
				<LinkButton
					code="parameterLibraryDelete"
					disabled={controlledOperationDisabled('maintenance', lock)}
					onClick={() => {
						WorkOrderFuc(
							() => {
								deleteData(value, record);
							},
							lock,
							middlewareName,
							deleteMoreBaseOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
				>
					删除
				</LinkButton>
			</Actions>
		);
	};

	return (
		<div>
			<ProTable
				operation={Operation}
				dataSource={dataSource}
				rowKey="name"
				onRefresh={() => getData('')}
				rowSelection={{
					type: 'checkbox',
					selectedRowKeys,
					onChange(selectedRowKeys, selectedRows, info) {
						setSelectedRows(selectedRows);
						setSelectedRowKeys(selectedRowKeys);
					}
				}}
			>
				<ProTable.Column
					title="参数名"
					dataIndex="name"
					width={100}
					ellipsis
				/>
				<ProTable.Column
					title="是否重启"
					dataIndex="restart"
					width={80}
					render={(value) => (value === true ? '是' : '否')}
				/>
				{(name === 'mysql' || name === 'redis') && (
					<ProTable.Column
						title="节点类型"
						dataIndex="role"
						width={120}
						filters={roleList.map((item: filterProps) => {
							return { value: item.value, text: item.label };
						})}
						filterMultiple={false}
						onFilter={(value: any, record: any) =>
							record.role === value
						}
						render={(value) =>
							roleList.find(
								(item: filterProps) => item.value === value
							)?.label || '/'
						}
					/>
				)}
				<ProTable.Column
					title="参数类型"
					dataIndex="paramType"
					filters={paramTypeList.map((item: filterProps) => {
						return { value: item.value, text: item.label };
					})}
					filterMultiple={false}
					onFilter={(value: any, record: any) =>
						record.paramType === value
					}
					render={(value) =>
						paramTypeList.find((item) => item.value === value)
							?.label || '/'
					}
				/>
				<ProTable.Column
					title="参数范围"
					dataIndex="ranges"
					width={200}
					ellipsis
				/>
				<ProTable.Column
					title="参数描述"
					dataIndex="description"
					width={160}
					render={nullRender}
				/>
				<ProTable.Column
					title="操作"
					dataIndex="action"
					render={actionRender}
				/>
			</ProTable>
			{visible && (
				<AddParameterBase
					visible={visible}
					data={record}
					roleList={roleList}
					onCreate={() => {
						getData('');
						setVisible(false);
					}}
					onCancel={() => setVisible(false)}
				/>
			)}
		</div>
	);
};

export default ParameterBase;

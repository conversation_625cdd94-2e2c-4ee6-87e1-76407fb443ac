import * as React from 'react';
import { useState, useEffect } from 'react';
import {
	Modal,
	Form,
	Input,
	Select,
	notification,
	InputNumber,
	Button,
	Row,
	Col,
	Tag
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';

import { addParam, editParam, addition } from '@/services/middleware';
import { filterProps } from '@/utils/constant';
import { useParams } from 'react-router';
import { ParamsProps } from './editParamTemplate';
import { formItemLayout618 } from '@/utils/const';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';
import storage from '@/utils/storage';

interface AddParameterBaseProps {
	data?: any;
	visible: boolean;
	isMore?: boolean;
	clusterId?: string;
	onCreate: () => void;
	onCancel: () => void;
	roleList?: filterProps[];
}
export const paramTypeList = [
	{
		label: '数值型',
		value: 'input'
	},
	{
		label: '单选型',
		value: 'select'
	},
	{
		label: '多选型',
		value: 'multiSelect'
	}
];
const AddParameterBase = (props: AddParameterBaseProps): JSX.Element => {
	const { data, visible, onCreate, onCancel, roleList, isMore, clusterId } =
		props;
	const params: ParamsProps = useParams();
	const { name, namespace, middlewareName, chartVersion } = params;
	const [form] = Form.useForm();
	const [paramType, setParamType] = useState<string>('input');
	const [rangeStart, setRangeStart] = useState<any>();
	const [rangeEnd, setRangeEnd] = useState<any>();
	const [selectValue, setSelectValue] = useState<string>('');
	const [multiSelectValue, setMultiSelectValue] = useState<string>('');
	const [list, setList] = useState<string[]>([]);

	useEffect(() => {
		if (data) {
			if (
				data.paramType === 'select' ||
				data.paramType === 'multiSelect'
			) {
				let selects: string;
				if (data.ranges.includes('"')) {
					selects = data.ranges.substring(2, data.ranges.length - 2);
				} else {
					selects = data.ranges.substring(1, data.ranges.length - 1);
				}
				const listTemp = selects.split('|');
				setList(listTemp);
			} else {
				let selects: string;
				if (data.ranges.includes('"')) {
					selects = data.ranges.substring(2, data.ranges.length - 2);
				} else {
					selects = data.ranges.substring(1, data.ranges.length - 1);
				}
				const temp = selects.split('-');
				form.setFieldsValue({
					rangeStart: +temp[0],
					rangeEnd: +temp[1]
				});
				setRangeStart(+temp[0]);
				setRangeEnd(+temp[1]);
			}
			setParamType(data.paramType);
			form.setFieldsValue({
				...data,
				defaultValue:
					data.paramType === 'multiSelect'
						? data.defaultValue.split(',')
						: data.defaultValue
			});
		}
	}, [data]);

	const onOk = async () => {
		await form.validateFields();
		const values = form.getFieldsValue();
		let ranges = '';
		if (values.paramType === 'input') {
			ranges = rangeStart + '-' + rangeEnd;
		} else if (values.paramType === 'select') {
			ranges = list.join('|');
		} else {
			ranges = list.join('|');
		}
		const sendData = {
			name: values.name,
			paramType: values.paramType,
			restart: values.restart,
			role: values.role,
			description: values.description,
			ranges,
			defaultValue:
				values.paramType === 'multiSelect'
					? values.defaultValue.join(',')
					: values.defaultValue
		};
		await ExecuteOrderFuc();
		if (data) {
			editParam({
				middlewareType: name,
				paramName: data.name,
				...sendData
			}).then((res) => {
				if (res.success) {
					onCreate();
					notification.success({
						message: '成功',
						description: '修改成功'
					});
				} else {
					notification.error({
						message: '失败',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			});
		} else {
			if (isMore) {
				addition({
					clusterId,
					namespace,
					middlewareName,
					type: name,
					chartVersion,
					customConfigList: [
						{
							...sendData
						}
					]
				}).then((res) => {
					if (res.success) {
						onCreate();
						notification.success({
							message: '成功',
							description: '添加成功'
						});
					} else {
						notification.error({
							message: '失败',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			} else {
				addParam({ middlewareType: name, ...sendData }).then((res) => {
					if (res.success) {
						onCreate();
						notification.success({
							message: '成功',
							description: '添加成功'
						});
					} else {
						notification.error({
							message: '失败',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		}
	};

	return (
		<Modal
			title={data ? '编辑参数' : '新增参数'}
			open={visible}
			onOk={onOk}
			onCancel={onCancel}
		>
			<Form form={form} {...formItemLayout618} labelAlign="left">
				<Form.Item
					label="参数名"
					name="name"
					rules={[
						{
							required: true,
							message: '请输入参数名'
						},
						{
							pattern: /^[a-zA-Z0-9._-]+$/,
							message: '参数名由字母、数字及“.”或“_”或“-”组成'
						}
					]}
				>
					<Input placeholder="请输入参数名" disabled={data} />
				</Form.Item>
				<Form.Item
					label="是否重启"
					name="restart"
					rules={[
						{
							required: true,
							message: '请选择是否重启'
						}
					]}
					initialValue={true}
				>
					<Select
						options={[
							{ label: '是', value: true },
							{ label: '否', value: false }
						]}
					/>
				</Form.Item>
				<Form.Item
					label="节点类型"
					name="role"
					rules={[
						{
							required: true,
							message: '请选择节点类型'
						}
					]}
					initialValue="major"
				>
					<Select options={roleList} disabled={data} />
				</Form.Item>
				<Form.Item
					label="参数类型"
					name="paramType"
					rules={[
						{
							required: true,
							message: '请选择参数类型'
						}
					]}
					initialValue="input"
				>
					<Select
						options={paramTypeList}
						value={paramType}
						onChange={(value) => {
							setParamType(value);
							form.resetFields(['ranges']);
							form.setFieldValue(
								'defaultValue',
								value === 'multiSelect' ? [] : null
							);
						}}
					/>
				</Form.Item>
				{paramType === 'input' ? (
					<Form.Item label="参数范围" name="ranges" required>
						<div className="flex-form flex-center">
							<Form.Item
								rules={[
									{
										required: true,
										message: '请输入'
									}
								]}
								name="rangeStart"
							>
								<InputNumber
									value={rangeStart}
									max={rangeEnd}
									placeholder="请输入"
									style={{ width: 155 }}
									onChange={(value) => {
										setRangeStart(value);
										form.setFieldValue('defaultValue', '');
									}}
								/>
							</Form.Item>
							<span className="ml-12 mr-12">——</span>
							<Form.Item
								rules={[
									{
										required: true,
										message: '请输入'
									}
								]}
								name="rangeEnd"
							>
								<InputNumber
									value={rangeEnd}
									placeholder="请输入"
									min={rangeStart}
									style={{ width: 155 }}
									onChange={(value) => {
										setRangeEnd(value);
										form.setFieldValue('defaultValue', '');
									}}
								/>
							</Form.Item>
						</div>
					</Form.Item>
				) : (
					<Form.Item
						label="参数范围"
						name="ranges"
						rules={[
							{
								required: list.length ? false : true,
								message: '请输入参数范围'
							}
						]}
					>
						<div className="display-flex">
							{paramType === 'select' && (
								<Input
									placeholder="请输入参数范围"
									value={selectValue}
									onChange={(e) =>
										setSelectValue(e.target.value)
									}
									onBlur={(e) => {
										if (e.target.value) {
											setSelectValue('');
											!list.includes(e.target.value) &&
												setList([
													...list,
													e.target.value
												]);
										}
									}}
								/>
							)}
							{paramType === 'multiSelect' && (
								<Input
									placeholder="请输入参数范围"
									value={multiSelectValue}
									onChange={(e) =>
										setMultiSelectValue(e.target.value)
									}
									onBlur={(e) => {
										if (e.target.value) {
											setMultiSelectValue('');
											!list.includes(e.target.value) &&
												setList([
													...list,
													e.target.value
												]);
										}
									}}
								/>
							)}
							<Button
								style={{ marginLeft: 16 }}
								onClick={() => {
									if (paramType === 'select') {
										setSelectValue('');
										selectValue &&
											!list.includes(selectValue) &&
											setList([...list, selectValue]);
									} else {
										setMultiSelectValue('');
										multiSelectValue &&
											!list.includes(multiSelectValue) &&
											setList([
												...list,
												multiSelectValue
											]);
									}
								}}
							>
								<PlusOutlined
									style={{
										color: '#005AA5'
									}}
								/>
							</Button>
						</div>
					</Form.Item>
				)}
				<Row>
					<Col span={18} offset={6}>
						{paramType !== 'input' && list.length ? (
							<div className="tags mb-16">
								{list.map((item: string) => {
									return (
										<Tag
											key={item}
											style={{
												padding: '4px 10px',
												margin: '0 6px 6px 0'
											}}
											closable
											onClose={(
												e: React.MouseEvent<HTMLElement>
											) => {
												e.preventDefault();
												form.setFieldValue(
													'defaultValue',
													paramType === 'select'
														? null
														: form
																.getFieldValue(
																	'defaultValue'
																)
																.filter(
																	(
																		arr: string
																	) =>
																		arr !==
																		item
																)
												);
												setList(
													list.filter(
														(str: string) =>
															str !== item
													)
												);
											}}
										>
											{item}
										</Tag>
									);
								})}
							</div>
						) : null}
					</Col>
				</Row>
				{/* FormItem分开判断是因为Form.Item如果有超过一个子元素表单方法会失效 */}
				{paramType === 'input' && (
					<Form.Item
						label="默认值"
						name="defaultValue"
						required
						rules={[
							{
								validator(rule, value, callback) {
									if (!value && value !== 0) {
										return Promise.reject(
											new Error('请输入默认值')
										);
									} else if (paramType === 'input') {
										if (
											typeof rangeStart !== 'number' ||
											typeof rangeEnd !== 'number'
										) {
											return Promise.reject(
												new Error(
													'请输入在参数范围内的默认值'
												)
											);
										}
										if (
											Number(value) <
												Number(rangeStart) ||
											Number(value) > Number(rangeEnd)
										) {
											return Promise.reject(
												new Error(
													'请输入在参数范围内的默认值'
												)
											);
										}
										return Promise.resolve();
									} else {
										return Promise.resolve();
									}
								}
							}
						]}
					>
						<InputNumber
							placeholder="请输入默认值"
							style={{ width: '100%' }}
						/>
					</Form.Item>
				)}
				{paramType === 'select' && (
					<Form.Item
						label="默认值"
						name="defaultValue"
						rules={[
							{
								required: true,
								message: '请输入默认值'
							}
						]}
					>
						<Select
							placeholder="请输入默认值"
							options={list.map((item: string) => ({
								label: item,
								value: item
							}))}
						/>
					</Form.Item>
				)}
				{paramType === 'multiSelect' && (
					<Form.Item
						label="默认值"
						name="defaultValue"
						rules={[
							{
								required: true,
								message: '请输入默认值'
							}
						]}
					>
						<Select
							placeholder="请输入默认值"
							options={list.map((item: string) => ({
								label: item,
								value: item
							}))}
							mode="multiple"
						/>
					</Form.Item>
				)}
				<Form.Item label="参数描述" name="description">
					<Input placeholder="请输入参数描述" />
				</Form.Item>
			</Form>
		</Modal>
	);
};

export default AddParameterBase;

import React, { useState, useEffect } from 'react';
import { useParams, useHistory } from 'react-router';
import moment from 'moment';
import {
	Button,
	Input,
	Form,
	Select,
	notification,
	Modal,
	Tooltip
} from 'antd';
import Actions from '@/components/Actions';
import ProTable from '@/components/ProTable';
import AddParameterBase from '../addParameterBase';

import {
	getConfigs,
	updateConfig,
	getConfigRole,
	deleteAddition
} from '@/services/middleware';
import {
	controlledOperationDisabled,
	nullRender,
	questionTooltipRender
} from '@/utils/utils';

import { ConfigItem } from '../../detail';
import { ParamsProps } from '../editParamTemplate';
import { filterProps } from '@/utils/constant';
import { ExclamationCircleFilled } from '@ant-design/icons';
import useRefresh from '@/utils/useRefresh';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';
import Auth from '@/components/Auth';
const { confirm, warning } = Modal;
const { Option } = Select;
const FormItem = Form.Item;
const LinkButton = Actions.LinkButton;
const containCurrentValue = ['mysql', 'postgresql', 'redis', 'elasticsearch'];
interface ParamEditTableProps {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	source?: string;
	role?: string;
	readWriteProxy?: { enabled: boolean } | null;
	handleBtnClick?: (value: boolean) => void;
	paramTemplateData: any[];
	lock: string;
}

function ParamEditTable(props: ParamEditTableProps): JSX.Element {
	const {
		clusterId,
		namespace,
		middlewareName,
		type,
		role,
		handleBtnClick,
		source = 'template',
		paramTemplateData,
		lock
	} = props;
	const params: ParamsProps = useParams();
	const { uid, currentTab, aliasName, name, chartVersion } = params;
	const history = useHistory();
	const [dataSource, setDataSource] = useState<ConfigItem[]>([]);
	const [showDataSource, setShowDataSource] = useState<ConfigItem[]>([]);
	const [editFlag, setEditFlag] = useState<boolean>(false);
	const [disableFlag, setDisableFlag] = useState<boolean>(true);
	const [roleList, setRoleList] = useState<filterProps[]>([]);
	const [nodeType, setNodeType] = useState<string>('');
	const [visible, setVisible] = useState<boolean>(false);
	const editParamsListOperatorId = maintenances['Edit Parameter'];
	const addParamOperatorId = maintenances['Add Parameter'];
	const deleteParamOperatorId = maintenances['Delete Parameter'];
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		if (refreshKey > 0) {
			setEditFlag(false);
			if (!uid) {
				getRoles(clusterId, namespace, middlewareName, type);
			} else {
				const list = paramTemplateData.map((item: ConfigItem) => {
					item.modifiedValue = item.targetValue || item.defaultValue;
					item.value = item.value || item.defaultValue;
					return item;
				});
				setDataSource([...list]);
				setShowDataSource([...list]);
			}
		}
	}, [refreshKey]);
	useEffect(() => {
		if (source === 'template') {
			const list = paramTemplateData.map((item: ConfigItem) => {
				item.modifiedValue =
					item.modifiedValue || item.targetValue || item.defaultValue;
				item.value = item.value || item.defaultValue;
				return item;
			});
			setDataSource([...list]);
			setShowDataSource([...list]);
		} else {
			if (clusterId && namespace && middlewareName && type) {
				!roleList.length &&
					getRoles(clusterId, namespace, middlewareName, type);
				roleList.length &&
					getData(
						clusterId,
						namespace,
						middlewareName,
						type,
						role || nodeType
					);
			}
		}
	}, [
		clusterId,
		namespace,
		middlewareName,
		roleList.length,
		paramTemplateData
	]);
	useEffect(() => {
		setEditFlag(false);
		setRefreshKey(0);
	}, [currentTab]);
	const getData = (
		clusterId: string,
		namespace: string,
		middlewareName: string,
		type: string,
		role: string,
		order = 'descend',
		filterAddition?: boolean
	) => {
		const sendData = {
			clusterId,
			namespace,
			middlewareName,
			type,
			order,
			role,
			filterAddition
		};
		getConfigs(sendData).then((res) => {
			if (res.success) {
				const list =
					res.data &&
					res.data.map((item: ConfigItem) => {
						// item.addition 为true时为自定义参数，新建自定义参数不现实默认值，需要用户手动设置，audit_whitelist_cmds参数值允许为空
						item.modifiedValue =
							item.addition ||
							item.name === 'audit_whitelist_cmds'
								? item.targetValue
								: item.targetValue || item.defaultValue;
						item.targetValue =
							item.addition ||
							item.name === 'audit_whitelist_cmds'
								? item.targetValue
								: item.targetValue || item.defaultValue;
						return item;
					});
				setDataSource([...list]);
				setShowDataSource([...list]);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const getRoles = (
		clusterId: string,
		namespace: string,
		middlewareName: string,
		type: string
	) => {
		getConfigRole({ clusterId, namespace, middlewareName, type }).then(
			(res) => {
				if (res.success) {
					setNodeType('major');
					setRoleList(
						res.data.map((item: string) => {
							return {
								label:
									item === 'major'
										? '服务节点'
										: item + '节点',
								value: item
							};
						})
					);
				}
			}
		);
	};
	const saveTemplate = () => {
		if (source === 'template') {
			handleBtnClick && handleBtnClick(false);
			setEditFlag(false);
			setDisableFlag(true);
		} else {
			const list = dataSource.filter(
				(item) => item.targetValue != item.modifiedValue
			);
			const restartFlag = list.some((item) => {
				if (item.restart === true) return true;
				return false;
			});
			let contentTemp;
			if (restartFlag && type === 'postgresql') {
				contentTemp =
					'当前修改参数中包括需要重启才能生效的参数，请立即重启';
			} else if (restartFlag && (type === 'redis' || type === 'mysql')) {
				contentTemp = `当前修改参数中包括需要重启才能生效的参数，${`是否立即${
					nodeType === 'major' ? '优雅' : ''
				}重启（选择稍后重启时需要自行去重启服务才能使参数生效）？`}`;
			} else if (restartFlag) {
				contentTemp = (
					<>
						<p>当前修改参数中包括需要重启才能生效的参数</p>
						<p>是否立即修改？</p>
					</>
				);
			} else {
				contentTemp =
					'本次修改无需重启服务，参数将在提交后的15秒左右生效，请确认提交';
			}
			if (restartFlag && type === 'postgresql') {
				warning({
					title: '重启提示',
					content: contentTemp,
					okText: '立即重启',
					onOk: async () => {
						const sendList = list.map((item) => {
							item.targetValue = item.modifiedValue;
							return item;
						});
						const sendData = {
							url: {
								clusterId,
								middlewareName,
								namespace
							},
							data: {
								clusterId,
								middlewareName,
								namespace,
								type,
								customConfigList: sendList,
								role: role || nodeType,
								reboot: true
							}
						};
						await ExecuteOrderFuc();
						return updateConfig(sendData)
							.then((res) => {
								if (res.success) {
									notification.success({
										message: '修改成功',
										description: `共修改了${sendData.data.customConfigList.length}个参数`
									});
								} else {
									notification.error({
										message: '失败',
										description: res.errorMsg
									});
								}
							})
							.finally(() => {
								getData(
									clusterId,
									namespace,
									middlewareName,
									type,
									role || nodeType
								);
								setEditFlag(false);
								setDisableFlag(true);
							});
					}
				});
			} else {
				confirm({
					title: '重启提示',
					content: contentTemp,
					okText:
						(restartFlag && type === 'redis') ||
						(restartFlag && type === 'mysql') ||
						(restartFlag && type === 'postgresql')
							? '立即重启'
							: '确认',
					cancelText:
						(restartFlag && type === 'redis') ||
						(restartFlag && type === 'mysql') ||
						(restartFlag && type === 'postgresql')
							? '稍后重启'
							: '取消',
					onOk: async () => {
						const sendList = list.map((item) => {
							item.targetValue = item.modifiedValue;
							return item;
						});
						console.log(sendList);
						const sendData = {
							url: {
								clusterId,
								middlewareName,
								namespace
							},
							data: {
								clusterId,
								middlewareName,
								namespace,
								type,
								customConfigList: sendList,
								role: role || nodeType,
								reboot:
									(restartFlag && type === 'redis') ||
									(restartFlag && type === 'mysql') ||
									(restartFlag && type === 'postgresql')
										? true
										: false
							}
						};
						console.log(sendData);
						await ExecuteOrderFuc();
						return updateConfig(sendData)
							.then((res) => {
								if (res.success) {
									notification.success({
										message: '修改成功',
										description: `共修改了${sendData.data.customConfigList.length}个参数`
									});
								} else {
									notification.error({
										message: '失败',
										description: res.errorMsg
									});
								}
							})
							.finally(() => {
								getData(
									clusterId,
									namespace,
									middlewareName,
									type,
									role || nodeType
								);
								setEditFlag(false);
								setDisableFlag(true);
							});
					},
					onCancel: async () => {
						if (
							(restartFlag && type === 'redis') ||
							(restartFlag && type === 'mysql')
						) {
							const sendList = list.map((item) => {
								item.targetValue = item.modifiedValue;
								return item;
							});
							const sendData = {
								url: {
									clusterId,
									middlewareName,
									namespace
								},
								data: {
									clusterId,
									middlewareName,
									namespace,
									type,
									role: role || nodeType,
									customConfigList: sendList,
									reboot: false
								}
							};
							await ExecuteOrderFuc();
							return updateConfig(sendData)
								.then((res) => {
									if (res.success) {
										notification.success({
											message: '修改成功',
											description: `共修改了${sendData.data.customConfigList.length}个参数`
										});
									} else {
										notification.error({
											message: '失败',
											description: res.errorMsg
										});
									}
								})
								.finally(() => {
									getData(
										clusterId,
										namespace,
										middlewareName,
										type,
										role || nodeType
									);
									setEditFlag(false);
									setDisableFlag(true);
								});
						} else {
							if (!uid) {
								getData(
									clusterId,
									namespace,
									middlewareName,
									type,
									role || nodeType
								);
								setEditFlag(false);
								setDisableFlag(true);
							} else {
								const list = paramTemplateData.map(
									(item: ConfigItem) => {
										item.modifiedValue =
											item.value || item.defaultValue;
										item.value =
											item.value || item.defaultValue;
										return item;
									}
								);
								setDataSource([...list]);
								setShowDataSource([...list]);
								setEditFlag(false);
								setDisableFlag(true);
							}
						}
					}
				});
			}
		}
	};
	const handleSearch = (value: string) => {
		const list = dataSource.filter((item) => item.name.includes(value));
		setShowDataSource(list);
	};
	const isRestartRender = (value: boolean) => {
		return value ? '是' : '否';
	};
	const defaultValueRender = (value: string) => {
		return (
			<div
				title={value}
				style={{ width: '100%' }}
				className="text-overflow"
			>
				{value}
			</div>
		);
	};
	const updateValue = (value: any, record: ConfigItem) => {
		let cValue = value;
		if (record.paramType === 'multiSelect') {
			cValue = value.join(',');
		}
		const flag = typeof cValue === 'string' && cValue.trim();
		if (
			(flag === null || flag === undefined || flag === '') &&
			record.name !== 'audit_whitelist_cmds' &&
			name === 'mysql'
		) {
			notification.error({
				message: '失败',
				description: '不能将目标值设置为空'
			});
			setDisableFlag(true);
			return;
		}
		if (record.paramType === 'multiSelect') {
			record.modifiedValue = value.join(',');
		} else {
			record.modifiedValue = value;
		}
		setDataSource([...dataSource]);
		setShowDataSource([...showDataSource]);
		const list = dataSource.filter(
			(item) => item.targetValue != item.modifiedValue
		);
		if (list.length === 0) {
			setDisableFlag(true);
		} else {
			setDisableFlag(false);
		}
	};
	const targetValueRender = (
		value: string,
		record: ConfigItem,
		index: number
	) => {
		let selectList: string[] = [];
		let defaultSelects: string[] = [];
		if (
			record.paramType === 'select' ||
			record.paramType === 'multiSelect'
		) {
			let selects: string;
			if (record.ranges.includes('"')) {
				selects = record.ranges.substring(2, record.ranges.length - 2);
			} else {
				selects = record.ranges.substring(1, record.ranges.length - 1);
			}
			const listTemp = selects.split('|');
			selectList = listTemp;
		}
		if (record.paramType === 'multiSelect' && record.modifiedValue) {
			const arr1 = record.modifiedValue.split(',');
			defaultSelects = [...arr1];
		}
		if (editFlag) {
			switch (record.paramType) {
				case 'input':
					return (
						<FormItem
							name={record.name}
							rules={[
								{
									pattern: new RegExp(String(record.pattern)),
									message: '输入的值不在参数范围中。'
								}
							]}
							noStyle
						>
							<Input
								placeholder="请输入"
								defaultValue={record.modifiedValue}
								onChange={(e) => {
									updateValue(e.target.value, record);
								}}
							/>
						</FormItem>
					);
				case 'select':
					return (
						<FormItem name={record.name} noStyle>
							<Select
								style={{ width: '100%', minWidth: '100px' }}
								defaultValue={record.modifiedValue}
								onChange={(value: any) => {
									updateValue(value, record);
								}}
								dropdownMatchSelectWidth={false}
							>
								{selectList &&
									selectList.map((item) => {
										return (
											<Option key={item} value={item}>
												{item}
											</Option>
										);
									})}
							</Select>
						</FormItem>
					);
				case 'multiSelect':
					return (
						<FormItem name={record.name} noStyle>
							<Select
								style={{ minWidth: '100px', width: '100%' }}
								defaultValue={defaultSelects}
								mode="multiple"
								onChange={(value: any) => {
									updateValue(value, record);
								}}
								dropdownMatchSelectWidth={false}
							>
								{selectList &&
									selectList.map((item) => {
										return (
											<Option key={item} value={item}>
												{item}
											</Option>
										);
									})}
							</Select>
						</FormItem>
					);
				default:
					return (
						<FormItem
							name={record.name}
							rules={[
								{
									pattern: new RegExp(String(record.pattern)),
									message: '输入的值不在参数范围中。'
								}
							]}
							noStyle
						>
							<Input
								placeholder="请输入"
								defaultValue={record.modifiedValue}
								onChange={(e) => {
									updateValue(e.target.value, record);
								}}
							/>
						</FormItem>
					);
			}
		} else {
			const flag = record.targetValue != record.modifiedValue;
			return (
				<div
					title={value}
					style={{
						width: '100%',
						color: flag ? '#C80000' : '#333333'
					}}
					className="text-overflow"
				>
					{record.addition && !value && (
						<Tooltip title="当前目标值为空，修改参数目标值后参数生效">
							<ExclamationCircleFilled
								style={{ cursor: 'pointer' }}
							/>
						</Tooltip>
					)}
					{value}
				</div>
			);
		}
	};
	const deleteParam = (value: string, record: ConfigItem) => {
		Modal.confirm({
			title: '提示',
			content: '是否确认删除',
			onOk: async () => {
				await ExecuteOrderFuc();
				return deleteAddition({
					clusterId,
					namespace,
					middlewareName,
					type,
					role: role || nodeType,
					paramName: record.name
				}).then((res) => {
					if (res.success) {
						getData(
							clusterId,
							namespace,
							middlewareName,
							type,
							role || nodeType
						);
						notification.success({
							message: '成功',
							description: '删除成功'
						});
					} else {
						notification.error({
							message: '失败',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};
	const operation = () => {
		if (editFlag) {
			return {
				primary: (
					<>
						<Button
							onClick={saveTemplate}
							type="primary"
							disabled={disableFlag}
						>
							保存
						</Button>
						<Button
							onClick={() => {
								confirm({
									title: '操作确认',
									content:
										'取消后，编辑后数据将会丢失，请谨慎操作',
									onOk: () => {
										if (!uid && source === 'list') {
											getData(
												clusterId,
												namespace,
												middlewareName,
												type,
												role || nodeType
											);
										} else {
											const list = paramTemplateData.map(
												(item: ConfigItem) => {
													item.modifiedValue =
														item.targetValue ||
														item.defaultValue;
													item.value =
														item.value ||
														item.defaultValue;
													return item;
												}
											);
											setDataSource([...list]);
											setShowDataSource([...list]);
										}
										setTimeout(() => {
											setEditFlag(false);
											setDisableFlag(true);
											handleBtnClick &&
												handleBtnClick(false);
										}, 1000);
									}
								});
							}}
						>
							取消
						</Button>
					</>
				)
			};
		} else {
			return {
				primary: (
					<>
						{source === 'list' &&
							(name === 'mysql' ||
								name === 'redis' ||
								name === 'postgresql' ||
								name === 'elasticsearch') && (
								<Auth code="parameterListAdd">
									<Button
										type="primary"
										disabled={controlledOperationDisabled(
											'maintenance',
											lock
										)}
										onClick={() => {
											WorkOrderFuc(
												() => {
													setVisible(true);
												},
												lock,
												middlewareName,
												addParamOperatorId,
												history,
												params.type,
												name,
												aliasName,
												clusterId,
												namespace
											);
										}}
									>
										新增
									</Button>
								</Auth>
							)}
						<Auth code="parameterListUpdate">
							<Button
								onClick={() => {
									WorkOrderFuc(
										() => {
											handleBtnClick &&
												handleBtnClick(true);
											setEditFlag(true);
										},
										lock,
										middlewareName,
										editParamsListOperatorId,
										history,
										params.type,
										name,
										aliasName,
										clusterId,
										namespace
									);
								}}
								type="primary"
								disabled={controlledOperationDisabled(
									'maintenance',
									lock
								)}
							>
								编辑
							</Button>
						</Auth>
					</>
				)
			};
		}
	};
	const nameRender = (value: string, record: any) => {
		return (
			<div>
				{value}
				{record.addition && !record.modifiedValue && (
					<Tooltip title="当前目标值为空，修改参数目标值后参数生效">
						<ExclamationCircleFilled
							style={{
								cursor: 'pointer',
								color: '#ff4d4f',
								marginLeft: 8
							}}
						/>
					</Tooltip>
				)}
			</div>
		);
	};
	const actionRender = (value: string, record: ConfigItem, index: number) => {
		return (
			<Actions>
				<LinkButton
					code="parameterListDelete"
					disabled={
						!record.addition ||
						editFlag ||
						controlledOperationDisabled('maintenance', lock)
					}
					onClick={() => {
						WorkOrderFuc(
							() => {
								deleteParam(value, record);
							},
							lock,
							middlewareName,
							deleteParamOperatorId,
							history,
							params.type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
				>
					删除
				</LinkButton>
			</Actions>
		);
	};
	return (
		<>
			{(type === 'mysql' || type === 'redis') && !role && (
				<div className="mb-16">
					<label className="mr-24">节点类型选择：</label>
					<Select
						options={roleList}
						style={{ width: 200 }}
						disabled={editFlag}
						placeholder="请选择节点类型"
						value={nodeType}
						onChange={(value) => {
							setNodeType(value);
							getData(
								clusterId,
								namespace,
								middlewareName,
								type,
								value
							);
						}}
					/>
				</div>
			)}
			<ProTable
				dataSource={showDataSource}
				rowKey="name"
				operation={operation()}
				search={{
					onSearch: handleSearch,
					style: { width: '200px' },
					placeholder: '请输入关键词搜索'
				}}
				showRefresh={source === 'template' ? true : false}
				onRefresh={() => {
					setEditFlag(false);
					if (!uid) {
						getData(
							clusterId,
							namespace,
							middlewareName,
							type,
							role || nodeType,
							'descend',
							true
						);
					} else {
						const list = paramTemplateData.map(
							(item: ConfigItem) => {
								item.modifiedValue =
									item.value || item.defaultValue;
								item.value = item.value || item.defaultValue;
								return item;
							}
						);
						setDataSource([...list]);
						setShowDataSource([...list]);
					}
				}}
				refreshDisabled={editFlag}
				pagination={false}
				scroll={{ x: 1500 }}
				rowClassName={(record) => {
					if ((source === 'list' && record.topping) || editFlag) {
						return 'table-row-topping';
					}
					return '';
				}}
			>
				<ProTable.Column
					title="参数名"
					dataIndex="name"
					width={210}
					ellipsis={true}
					fixed="left"
					render={nameRender}
				/>
				<ProTable.Column
					title="默认值"
					dataIndex="defaultValue"
					render={defaultValueRender}
					width={310}
				/>
				{source === 'list' && !editFlag && (
					<ProTable.Column
						title="目标值"
						dataIndex="targetValue"
						width={410}
					/>
				)}
				{source === 'template' && !editFlag && (
					<ProTable.Column
						title="目标值"
						dataIndex="modifiedValue"
						width={410}
					/>
				)}
				{editFlag && (
					<ProTable.Column
						title="目标值"
						dataIndex="modifiedValue"
						render={targetValueRender}
						width={410}
					/>
				)}
				{/* {source === 'list' && containCurrentValue.includes(name) && (
					<ProTable.Column
						title="当前值"
						width={410}
						dataIndex="value"
					/>
				)} */}
				<ProTable.Column
					title="是否重启"
					dataIndex="restart"
					render={isRestartRender}
					filterMultiple={false}
					filters={[
						{ value: true, text: '是' },
						{ value: false, text: '否' }
					]}
					onFilter={(value, record: ConfigItem) =>
						record.restart === value
					}
					width={120}
				/>
				<ProTable.Column
					title="参数值范围"
					dataIndex="ranges"
					render={questionTooltipRender}
					width={100}
				/>
				<ProTable.Column
					title="参数描述"
					dataIndex="description"
					render={questionTooltipRender}
					width={100}
				/>
				{source === 'list' && (
					<ProTable.Column
						title="修改时间"
						dataIndex="updateTime"
						render={nullRender}
						sorter={(a: ConfigItem, b: ConfigItem) =>
							moment(a.updateTime).unix() -
							moment(b.updateTime).unix()
						}
						width={150}
					/>
				)}
				{source === 'list' && (
					<ProTable.Column
						title="操作"
						dataIndex="action"
						render={actionRender}
						width={100}
					/>
				)}
			</ProTable>
			{visible && (
				<AddParameterBase
					isMore
					visible={visible}
					roleList={roleList}
					clusterId={clusterId}
					onCancel={() => setVisible(false)}
					onCreate={() => {
						getData(
							clusterId,
							namespace,
							middlewareName,
							type,
							role || nodeType
						);
						setVisible(false);
					}}
				/>
			)}
		</>
	);
}

export default ParamEditTable;

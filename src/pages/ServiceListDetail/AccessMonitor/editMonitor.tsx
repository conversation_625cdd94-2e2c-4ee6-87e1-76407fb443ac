import React, { useEffect } from 'react';
import { useParams } from 'react-router';
import { Modal, Form, notification } from 'antd';
import MonitorAddressFormItem from '@/components/MonitorAddressFormItem';
import { formItemLayout420 } from '@/utils/const';
import { accessMonitor } from '@/services/middleware';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';

interface EditMonitorProps {
	data: any;
	visible: boolean;
	onOk: () => void;
	onCancel: () => void;
	chartVersion: string;
}

const EditMonitor = (props: EditMonitorProps) => {
	const { data, chartVersion } = props;
	const params: any = useParams();
	const [form] = Form.useForm();
	const { visible, onOk, onCancel } = props;

	const handleSubmit = async () => {
		await form.validateFields();
		const values = form.getFieldsValue();
		await ExecuteOrderFuc();
		accessMonitor({
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params.middlewareName,
			type: params.name,
			chartName: params.name,
			chartVersion,
			deployMod: 'server',
			...values
		}).then((res) => {
			if (res.success) {
				onOk();
				notification.success({
					message: '成功',
					description: '保存成功'
				});
			} else {
				notification.error({
					message: '失败',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};

	useEffect(() => {
		data &&
			form.setFieldsValue({
				extraMiddlewareMonitorInfoList:
					data?.extraMiddlewareMonitorInfoList || [
						{
							protocol: null,
							address: '',
							port: '',
							path: ''
						}
					]
			});
	}, [data]);

	return (
		<Modal
			title="监控配置"
			width={800}
			open={visible}
			onOk={handleSubmit}
			onCancel={onCancel}
		>
			<Form form={form} {...formItemLayout420} labelAlign="left">
				<MonitorAddressFormItem layout={4} required={true} />
			</Form>
		</Modal>
	);
};

export default EditMonitor;

import React, { useEffect, useState } from 'react';
import { Button, Select, notification } from 'antd';
import {
	getCanReleaseMiddleware,
	getMiddlewareDetail,
	getMiddlewareMonitorUrl
} from '@/services/middleware';

import svg from '@/assets/images/grafana_icon.svg';
import EditMonitor from './editMonitor';
import { useHistory, useParams } from 'react-router';
import { WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';
import useRefresh from '@/utils/useRefresh';
import { controlledOperationDisabled } from '@/utils/utils';
import DefaultPicture from '@/components/DefaultPicture';

interface MonitorItem {
	title: string;
	url: string;
	authorization: string | null;
	uid: string;
}
const AccessMonitor = ({ detail }: { detail: any }) => {
	const history = useHistory();
	const params: any = useParams();
	const { clusterId, namespace, middlewareName, name } = params;
	const [menuHide, setMenuHide] = useState(false);
	const [notAuth, setNotAuth] = useState<boolean>(true);
	const [monitorUrls, setMonitorUrls] = useState<MonitorItem[]>([]);
	const [currentMonitorUrl, setCurrentMonitorUrl] = useState<MonitorItem>();
	const [loading, setLoading] = useState<boolean>(false);
	const [visible, setVisible] = useState<boolean>(false);
	const [chartVersion, setChartVersion] = useState<string>('');
	const [data, setData] = useState<any>();
	const monitorConfigOperatorId = maintenances['Monitor Configuration'];
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		if (!notAuth) {
			getData();
			getCanReleaseMiddleware({
				clusterId: params.clusterId,
				type: params.name
			}).then((res) => {
				if (res.success) {
					setChartVersion(res.data.chartVersion);
				}
			});
		}
	}, [notAuth]);
	useEffect(() => {
		if (detail?.capabilities?.includes('monitoring')) {
			setNotAuth(false);
		} else {
			setNotAuth(true);
		}
	}, [detail]);
	useEffect(() => {
		if (chartVersion && !notAuth) {
			setLoading(true);
			getMiddlewareMonitorUrl({
				clusterId,
				namespace,
				middlewareName,
				type: name,
				chartVersion,
				deployMod: 'server'
			})
				.then((res) => {
					if (res.success) {
						setMonitorUrls(res.data);
						setCurrentMonitorUrl(res.data[0]);
						setNotAuth(false);
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
						if (`${res.code}${res.errorMsg}` === '400007无权限') {
							setNotAuth(true);
						}
						setCurrentMonitorUrl(undefined);
					}
				})
				.finally(() => {
					setLoading(false);
				});
		}
	}, [chartVersion, refreshKey, notAuth]);

	useEffect(() => {
		if (currentMonitorUrl) {
			const iframe: any = document.getElementById('iframe');
			iframe.onload = function () {
				iframe.contentWindow.postMessage(
					{ showMenu: false },
					encodeURI(new URL(currentMonitorUrl.url).origin)
				);
			};
		}
	}, [currentMonitorUrl]);

	useEffect(() => {
		// 子页面去掉侧边栏之后再显示iframe
		window.addEventListener(
			'message',
			function (event) {
				if (event.data.menuHide) {
					setMenuHide(true);
				}
			},
			false
		);
	});

	const getData = () => {
		const sendData = {
			clusterId,
			namespace,
			middlewareName,
			type: name,
			deployMod: 'server'
		};
		getMiddlewareDetail(sendData).then((res) => {
			if (res.success) {
				setData(res.data);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	if (notAuth) {
		return <DefaultPicture />;
	}
	return (
		<div className="monitor">
			<div className="mb-8">
				<Button
					type="primary"
					style={{ marginRight: 16 }}
					disabled={controlledOperationDisabled(
						'maintenance',
						detail?.lock
					)}
					onClick={() => {
						WorkOrderFuc(
							() => {
								setVisible(true);
							},
							detail.lock,
							middlewareName,
							monitorConfigOperatorId,
							history,
							params.type,
							params.name,
							params.aliasName,
							params.clusterId,
							params.namespace
						);
					}}
				>
					监控配置
				</Button>
				<Select
					loading={loading}
					placeholder="请选择监控面板"
					style={{ width: 250 }}
					value={currentMonitorUrl?.uid}
					onChange={(value: string) => {
						setCurrentMonitorUrl(
							monitorUrls.find((i) => i.uid === value)
						);
					}}
				>
					{monitorUrls.map((item) => {
						return (
							<Select.Option key={item.uid} value={item.uid}>
								{item.title}
							</Select.Option>
						);
					})}
				</Select>
			</div>
			<div
				style={{
					height: 'calc(100vh - 83px)',
					visibility: menuHide ? 'visible' : 'hidden'
				}}
			>
				{currentMonitorUrl && (
					<iframe
						id="iframe"
						src={currentMonitorUrl?.url || ''}
						frameBorder="no"
						// border="0"
						scrolling="no"
						style={{
							width: '100%',
							height: '100%',
							background: '#FFF'
						}}
					></iframe>
				)}
			</div>
			<div
				className="loading-gif"
				style={{
					top: 32,
					visibility: menuHide ? 'hidden' : 'visible'
				}}
			>
				<div className="loading-icon">
					<img src={svg} width="60" />
				</div>
			</div>
			{visible && (
				<EditMonitor
					visible={visible}
					data={data}
					chartVersion={chartVersion}
					onOk={() => {
						getData();
						setVisible(false);
					}}
					onCancel={() => setVisible(false)}
				/>
			)}
		</div>
	);
};

export default AccessMonitor;

import DataFields from '@/components/DataFields';
import ProTable from '@/components/ProTable';
import { Button, notification, Modal, Popconfirm, Form, Input } from 'antd';
import React, { useEffect, useState } from 'react';
import AccessInstance from './AccessInstance';
import Actions from '@/components/Actions';
import {
	addInstance,
	deleteInstance,
	getPods,
	updateMiddleware
} from '@/services/middleware';
import { useHistory, useParams } from 'react-router';
import { AccessIndexParams, PodItem } from '../detail';
import {
	nullRender,
	agentStatusRender,
	controlledOperationDisabled
} from '@/utils/utils';
import { EditOutlined } from '@ant-design/icons';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';
import useRefresh from '@/utils/useRefresh';
import AccessConsoleAddress from '@/pages/PublishService/components/AccessConsoleAddress';

const { confirm } = Modal;
const LinkButton = Actions.LinkButton;
const items = [
	{
		dataIndex: 'title',
		label: '标题'
	},
	{
		dataIndex: 'serviceName',
		label: '服务名称'
	},
	{
		dataIndex: 'aliasName',
		label: '显示名称'
	},
	{
		dataIndex: 'namespaceAliasName',
		label: '所在分区',
		render: (val: string, dataSource: any) => {
			return (
				<div
					className="text-overflow-one"
					title={`${val || dataSource.namespace}(${
						dataSource.namespace
					})`}
				>
					{`${val || dataSource.namespace}(${dataSource.namespace})`}
				</div>
			);
		}
	},
	{
		dataIndex: 'version',
		label: '版本'
	},
	{
		dataIndex: 'status',
		label: '健康状态',
		render: (val: string) => agentStatusRender(val)
	}
];
const createTemp = {
	dataIndex: 'createTime',
	label: '创建时间'
};
const AccessServiceHasConsoleAddress = [
	'nacos',
	'rabbitmq',
	'elasticsearch',
	'kibana',
	'skywalking'
];
export default function AccessBasicInfo({
	data,
	onRefresh
}: {
	data: any;
	onRefresh: () => void;
}): JSX.Element {
	const history = useHistory();
	const params: AccessIndexParams = useParams();
	const [form] = Form.useForm();
	const [basic, setBasic] = useState<any>({
		serviceName: '',
		aliasName: '',
		version: '',
		status: '',
		createTime: ''
	});
	const [open, setOpen] = useState<boolean>(false);
	const [pods, setPods] = useState<PodItem[]>([]);
	const accessInstanceOperatorId = maintenances['Access Instance'];
	const uninstallInstanceOperatorId = maintenances['Uninstall Instance'];
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		getPodList();
	}, [refreshKey]);
	const editServiceDetail = async (value: any, type: string) => {
		const sendData: any = {
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params.middlewareName,
			chartName: params.name,
			chartVersion: params.chartVersion,
			type: params.name,
			deployMod: 'server'
		};
		if (type === 'description') {
			sendData.description = value.description;
		} else {
			sendData.managementUrl = {
				protocol: value.consoleAddress?.protocol,
				url: value.consoleAddress?.address,
				port: value.consoleAddress?.port
					? Number(value.consoleAddress?.port)
					: null
			};
		}
		await updateMiddleware(sendData).then((res) => {
			if (res.success) {
				notification.success({
					message: '成功',
					description: `${
						type === 'description' ? '备注' : '服务控制台地址'
					}修改成功,5秒后刷新页面`
				});
				setTimeout(() => {
					onRefresh();
				}, 5000);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};

	const descriptionTemp = {
		dataIndex: 'description',
		label: '备注',
		render: (val: string) => {
			return (
				<div className="display-flex flex-align">
					<div className="text-overflow-one" title={val}>
						{val}
					</div>
					<Popconfirm
						title={
							<Form form={form}>
								<Form.Item name="description">
									<Input
										placeholder="请输入"
										defaultValue={val}
									/>
								</Form.Item>
							</Form>
						}
						icon={null}
						onConfirm={async () =>
							await editServiceDetail(
								form.getFieldsValue(),
								'description'
							)
						}
					>
						<EditOutlined
							style={{
								marginLeft: 8,
								cursor: 'pointer',
								fontSize: 14,
								verticalAlign: 'middle'
							}}
						/>
					</Popconfirm>
				</div>
			);
		}
	};
	const managementUrlRender = (url_obj: any) => {
		if (!url_obj) return '/';
		if (
			url_obj.protocol === null &&
			url_obj.url === null &&
			url_obj.port === null
		)
			return '/';
		if (url_obj.port === null)
			return `${url_obj.protocol}://${url_obj.url}`;
		return `${url_obj.protocol}://${url_obj.url}:${url_obj.port}`;
	};
	const managementUrlTemp = {
		dataIndex: 'managementUrl',
		label: '服务控制台地址',
		render: (val: any) => {
			form.setFieldsValue({
				consoleAddress: {
					protocol: val?.protocol,
					address: val?.url,
					port: val?.port
				}
			});
			return (
				<div className="display-flex flex-align">
					<div className="text-overflow-one" title={val}>
						{managementUrlRender(val)}
					</div>
					<Popconfirm
						title={
							<Form form={form}>
								<AccessConsoleAddress labelHidden={true} />
							</Form>
						}
						icon={null}
						onConfirm={async () => {
							await ExecuteOrderFuc();
							await editServiceDetail(
								form.getFieldsValue(),
								'managementUrl'
							);
						}}
					>
						<EditOutlined
							style={{
								marginLeft: 8,
								cursor: 'pointer',
								fontSize: 14,
								verticalAlign: 'middle'
							}}
						/>
					</Popconfirm>
				</div>
			);
		}
	};
	useEffect(() => {
		if (data) {
			setBasic({
				serviceName: data.name || '/',
				aliasName: data.aliasName || '/',
				version: data.version || '/',
				status: data.status || '/',
				createTime: data.createTime || '/',
				namespace: data.namespace || '/',
				namespaceAliasName: data.namespaceAliasName,
				managementUrl: data.managementUrl,
				description: data.description || '无'
			});
		}
	}, [data]);
	const Operation = {
		primary: (
			<Button
				type="primary"
				onClick={() => {
					WorkOrderFuc(
						() => {
							setOpen(true);
						},
						data.lock,
						params.middlewareName,
						accessInstanceOperatorId,
						history,
						params.type,
						params.name,
						params.aliasName,
						params.clusterId,
						params.namespace
					);
				}}
				disabled={controlledOperationDisabled(
					'maintenance',
					data?.lock
				)}
			>
				接入实例
			</Button>
		)
	};
	const getPodList = () => {
		getPods({
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params.middlewareName,
			type: params.name,
			deployMod: 'server'
		}).then((res) => {
			if (res.success) {
				setPods(res.data.pods);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const uninstall = (record: PodItem) => {
		confirm({
			title: '操作确认',
			content: '移除该实例后需要重新填写内容进行添加，请谨慎操作！',
			onOk: async () => {
				await ExecuteOrderFuc();
				return deleteInstance({
					clusterId: params.clusterId,
					namespace: params.namespace,
					middlewareName: params.middlewareName,
					instanceName: record.podName
				})
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '实例移除成功！'
							});
							getPodList();
						} else {
							notification.error({
								message: '错误',
								description: (
									<>
										<p>{res.errorMsg}</p>
										<p>{res.errorDetail}</p>
									</>
								)
							});
						}
					})
					.finally(() => {
						getPodList();
					});
			}
		});
	};
	const actionRender = (_: any, record: any, index: number) => {
		return (
			<Actions>
				<LinkButton
					onClick={() => {
						WorkOrderFuc(
							() => {
								uninstall(record);
							},
							data.lock,
							params.middlewareName,
							uninstallInstanceOperatorId,
							history,
							params.type,
							params.name,
							params.aliasName,
							params.clusterId,
							params.namespace
						);
					}}
					disabled={
						pods.length === 1 ||
						pods.some((item: any) => item.status === 'Deleted') ||
						controlledOperationDisabled('maintenance', data?.lock)
					}
				>
					卸载
				</LinkButton>
			</Actions>
		);
	};
	const onCreate = async (values: any) => {
		const sendData = {
			clusterId: params.clusterId,
			namespace: params.namespace,
			middleware: params.name,
			middlewareName: params.middlewareName,
			...values
		};
		ExecuteOrderFuc();
		return addInstance(sendData)
			.then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '实例添加成功'
					});
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setOpen(false);
				getPodList();
			});
	};
	const roleRender = (value: string) => {
		switch (value) {
			case 'master':
				return '主节点';
			case 'slave':
				return '从节点';
			case 'data':
				return '数据节点';
			case 'client':
				return '协调节点';
			case 'cold':
				return '冷节点';
			case 'kibana':
				return 'Kibana';
			case 'nameserver':
				return 'Nameserver';
			case 'exporter':
				return 'Exporter';
			case 'sentinel':
				return '哨兵';
			case 'proxy':
				return '代理';
			case 'syncslave':
				return '同步节点';
			case 'sync_slave':
				return '同步节点';
			case 'default':
				return '/';
			default:
				return value
					? value.substring(0, 1).toUpperCase() + value.substring(1)
					: '/';
		}
	};
	return (
		<>
			<h2>服务信息</h2>
			<DataFields
				dataSource={basic}
				items={
					AccessServiceHasConsoleAddress.includes(params.name)
						? [
								...items,
								managementUrlTemp,
								createTemp,
								descriptionTemp
						  ]
						: [...items, createTemp, descriptionTemp]
				}
			/>
			<h2 className="mt-16">实例详情</h2>
			<ProTable
				rowKey="podName"
				dataSource={pods}
				operation={Operation}
				onRefresh={getPodList}
			>
				<ProTable.Column dataIndex="podAliasName" title="实例名称" />
				<ProTable.Column dataIndex="status" title="状态" />
				<ProTable.Column
					dataIndex="hostIp"
					title="服务器IP"
					render={nullRender}
				/>
				<ProTable.Column
					dataIndex="port"
					title="端口"
					render={nullRender}
				/>
				{params.name === 'elasticsearch' && (
					<ProTable.Column dataIndex="protocol" title="协议" />
				)}
				<ProTable.Column
					dataIndex="role"
					title="节点类型"
					render={roleRender}
				/>
				<ProTable.Column
					dataIndex="createTime"
					title="接入时间"
					render={nullRender}
				/>
				<ProTable.Column
					dataIndex="action"
					title="操作"
					render={actionRender}
				/>
			</ProTable>
			{open && (
				<AccessInstance
					open={open}
					onCancel={() => setOpen(false)}
					onCreate={onCreate}
				/>
			)}
		</>
	);
}

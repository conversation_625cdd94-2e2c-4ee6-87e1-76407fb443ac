import React, { useEffect, useState } from 'react';
import { Form, Input, InputNumber, Modal, Select, notification } from 'antd';
import { getProjectAgentList } from '@/services/agent';
import { formItemLayout618 } from '@/utils/const';
import pattern from '@/utils/pattern';
import storage from '@/utils/storage';
import { useParams } from 'react-router';
import { AccessIndexParams } from '../detail';
export default function AccessInstance({
	open,
	onCancel,
	agent,
	onCreate
}: {
	agent?: any;
	open: boolean;
	onCancel: () => void;
	onCreate: (values: any, agent: any) => any;
}): JSX.Element {
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const params: AccessIndexParams = useParams();
	const [form] = Form.useForm();
	const [loading, setLoading] = useState<boolean>(false);
	const [agents, setAgents] = useState<AgentItem[]>([]);
	useEffect(() => {
		getProjectAgentList({
			organId,
			projectId,
			clusterId: params.clusterId
		}).then((res) => {
			if (res.success) {
				setAgents(res.data);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	}, []);
	const onOk = async () => {
		await form.validateFields();
		try {
			setLoading(true);
			const values = form.getFieldsValue();
			const flag = await onCreate(values, agent);
			try {
				setLoading(false);
			} catch {
				setLoading(false);
			}
			flag === true && setLoading(false);
		} catch (error) {
			setLoading(false);
		}
	};
	const agentNameRender = () => {
		if (agent) {
			return (
				<Form.Item
					name="address"
					label="实例IP"
					rules={[{ required: true, message: '请选择实例IP' }]}
					initialValue={agent.address}
				>
					<Input disabled={true} />
				</Form.Item>
			);
		} else {
			return (
				<Form.Item
					name="agentName"
					label="实例IP"
					rules={[{ required: true, message: '请选择实例IP' }]}
				>
					<Select placeholder="请选择实例IP">
						{agents.map((item) => {
							return (
								<Select.Option
									key={item.name}
									value={item.name}
									disabled={item.phase === 'Offline'}
								>
									{item.address}
								</Select.Option>
							);
						})}
					</Select>
				</Form.Item>
			);
		}
	};
	return (
		<Modal
			open={open}
			title="接入实例"
			onCancel={onCancel}
			onOk={onOk}
			destroyOnClose={true}
			okButtonProps={{ loading: loading }}
		>
			<Form
				form={form}
				{...formItemLayout618}
				labelAlign="left"
				preserve={false}
			>
				{agentNameRender()}
				<Form.Item
					name="name"
					label="实例名称"
					rules={[
						{
							pattern: new RegExp(pattern.agentInstanceName),
							message:
								'请输入以小写字母、数字及“_”或“-”或“.”组成的实例名称'
						}
					]}
				>
					<Input placeholder="请输入实例名称" />
				</Form.Item>
				<Form.Item
					name="port"
					label="端口"
					rules={[
						{
							required: true,
							message: '请输入端口'
						},
						{
							type: 'number',
							min: 1,
							max: 65535,
							message: '请输入正确范围内的端口号'
						}
					]}
				>
					<InputNumber
						placeholder="请输入端口"
						style={{ width: '100%' }}
					/>
				</Form.Item>
				{params.name === 'elasticsearch' && (
					<Form.Item
						label="协议"
						name="protocol"
						rules={[{ required: true, message: '协议不能为空' }]}
						initialValue="http"
					>
						<Select placeholder="请选择协议">
							<Select.Option value="https">https</Select.Option>
							<Select.Option value="http">http</Select.Option>
						</Select>
					</Form.Item>
				)}
			</Form>
		</Modal>
	);
}

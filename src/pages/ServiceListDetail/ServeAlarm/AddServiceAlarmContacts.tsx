import { Pro<PERSON><PERSON>nt, ProHeader, ProPage } from '@/components/ProPage';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';
import UserContacts from '@/pages/AlarmCenter/AlarmContacts/UserContacts';
import { AlarmContactItem } from '@/pages/AlarmCenter/alarm';
import {
	addServiceAlarmContacts,
	getServiceAlarmContacts
} from '@/services/alarm';
import { Button, Divider, Form, Space, Switch, notification } from 'antd';
import React, { useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router';
interface ParamsProps {
	organId: string;
	projectId: string;
	clusterId: string;
	namespace: string;
	middlewareName: string;
	name: string;
	aliasName: string;
	type: string;
	chartVersion: string;
}
export default function AddServiceAlarmContacts(): JSX.Element {
	const history = useHistory();
	const params: ParamsProps = useParams();
	const [loading, setLoading] = useState<boolean>(false);
	const [contacts, setContacts] = useState<AlarmContactItem[]>([]);
	const [spinning, setSpinning] = useState<boolean>(false);
	const [checkedContacts, setCheckedContacts] = useState<
		AlarmContactItem[] | null
	>([]);
	const [switchChecked, setSwitchChecked] = useState<boolean>(false);
	useEffect(() => {
		setSpinning(true);
		getServiceAlarmContacts({
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params.middlewareName,
			projectId: params.projectId,
			organId: params.organId,
			allocatable: true
		})
			.then((res) => {
				if (res.success) {
					setContacts(res.data);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setSpinning(false);
			});
	}, []);
	const handleChange = (values: AlarmContactItem[] | null) => {
		console.log(values);
		if (values && values.length > 0) {
			setSwitchChecked(true);
		} else {
			setSwitchChecked(false);
		}
		setCheckedContacts(values);
	};
	const handleSubmit = async () => {
		if (checkedContacts && checkedContacts.length > 0) {
			await ExecuteOrderFuc();
			setLoading(true);
			addServiceAlarmContacts({
				clusterId: params.clusterId,
				namespace: params.namespace,
				middlewareName: params.middlewareName,
				alertUserDtoList: checkedContacts
			})
				.then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '告警联系人添加成功！'
						});
						history.push(
							`/project/${params.type}/${params.name}/${params.aliasName}/container/alarm/${params.middlewareName}/${params.chartVersion}/${params.clusterId}/${params.namespace}`
						);
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				})
				.finally(() => {
					setLoading(false);
				});
		} else {
			notification.error({
				message: '错误',
				description: '请选择告警联系人'
			});
		}
	};
	return (
		<ProPage>
			<ProHeader title="告警联系人新增" onBack={() => history.goBack()} />
			<ProContent>
				<Form
					labelWrap
					wrapperCol={{ flex: 1 }}
					labelCol={{ flex: '200px' }}
					colon={false}
				>
					<Form.Item labelAlign="left" label="用户选择" required>
						<div className="import-contact-from-user-content">
							<UserContacts
								users={contacts}
								onChange={handleChange}
								loading={spinning}
							/>
						</div>
					</Form.Item>
					<Form.Item
						labelAlign="left"
						label="邮箱告警通知"
						required
						valuePropName="checked"
					>
						<Switch
							checked={switchChecked}
							disabled={
								!checkedContacts ||
								(checkedContacts &&
									checkedContacts.length === 0)
							}
							onChange={(checked: boolean) =>
								setSwitchChecked(checked)
							}
						/>
					</Form.Item>
				</Form>
				<Divider />
				<Space>
					<Button
						loading={loading}
						type="primary"
						onClick={handleSubmit}
						disabled={
							spinning ||
							!checkedContacts ||
							(checkedContacts && checkedContacts.length === 0) ||
							!switchChecked
						}
					>
						确认
					</Button>
					<Button onClick={() => history.goBack()}>取消</Button>
				</Space>
			</ProContent>
		</ProPage>
	);
}

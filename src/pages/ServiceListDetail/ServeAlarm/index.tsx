import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router';
import {
	Button,
	Empty,
	Modal,
	Space,
	Spin,
	Switch,
	Tooltip,
	notification
} from 'antd';
import ProTable from '@/components/ProTable';
import Actions from '@/components/Actions';
import { useHistory } from 'react-router';
import { controlledOperationDisabled, nullRender } from '@/utils/utils';
import { alarmWarn, maintenances, silences } from '@/utils/const';
import moment from 'moment';

import { deleteAlarms, getUsedAlarms } from '@/services/middleware';
import {
	deleteServiceAlarmContact,
	getServiceAlarmContacts,
	getServiceBackupNoti,
	updateServiceBackupNoti
} from '@/services/alarm';
import storage from '@/utils/storage';
import { DetailParams, ServiceRuleItem } from '../detail';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { AlarmContactItem } from '@/pages/AlarmCenter/alarm';
import ContactItem from '@/pages/AlarmCenter/AlarmContacts/ContactItem';
import useRefresh from '@/utils/useRefresh';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
const { confirm } = Modal;
const LinkButton = Actions.LinkButton;
function Rules({ lock }: { lock: string }): JSX.Element {
	const history = useHistory();
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const params: DetailParams = useParams();
	const {
		name,
		aliasName,
		currentTab,
		chartVersion,
		clusterId,
		namespace,
		middlewareName,
		type
	} = params;
	const [searchText, setSearchText] = useState<string>('');
	const [rules, setRules] = useState<ServiceRuleItem[]>([]);
	const [dataSource, setDataSource] = useState<ServiceRuleItem[]>([]);
	const [loading, setLoading] = useState<boolean>(false);
	const [backupSwitch, setBackupSwitch] = useState<boolean>(true);
	const [contacts, setContacts] = useState<AlarmContactItem[]>([]);
	const [spinning, setSpinning] = useState<boolean>(false);
	const addRuleOperatorId = maintenances['Add Alarm Rule'];
	const editRuleOperatorId = maintenances['Edit Alarm Rule'];
	const deleteRuleOperatorId = maintenances['Delete Alarm Rule'];
	const addContactOperatorId = maintenances['Add Alarm Contact'];
	const deleteContactOperatorId = maintenances['Delete Alarm Contact'];
	const modifyBackupToReceiveNotificationOperatorId =
		maintenances['Modify Backup To Receive Notifications'];
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	const createTimeRender = (value: string) => {
		if (!value) return '--';
		return moment(value).format('YYYY-MM-DD HH:mm:ss');
	};

	useEffect(() => {
		getData();
		getServiceBackupNotification();
		getContacts();
	}, [refreshKey]);
	const getServiceBackupNotification = () => {
		getServiceBackupNoti({
			clusterId,
			namespace,
			middlewareName
		}).then((res) => {
			if (res.success) {
				setBackupSwitch(res.data);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	const getContacts = () => {
		setSpinning(true);
		getServiceAlarmContacts({
			clusterId,
			namespace,
			middlewareName,
			allocatable: false
		})
			.then((res) => {
				if (res.success) {
					setContacts(res.data);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setSpinning(false);
			});
	};
	const getData = () => {
		setLoading(true);
		getUsedAlarms({
			clusterId,
			namespace,
			middlewareName,
			type: name
		})
			.then((res) => {
				if (res.success) {
					setRules(res.data);
					setDataSource(res.data);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const onSearch = (value: string) => {
		const list = rules.filter((item: ServiceRuleItem) =>
			item.expr?.includes(value)
		);
		setDataSource(list);
	};

	const removeAlarm = (record: ServiceRuleItem) => {
		const sendData = {
			clusterId,
			middlewareName,
			namespace,
			alertName: record.name
		};
		Modal.confirm({
			title: '操作确认',
			content: '是否确认删除?',
			onOk: async () => {
				await ExecuteOrderFuc();
				return deleteAlarms(sendData).then((res) => {
					if (res.success) {
						getData();
						notification.success({
							message: '成功',
							description: '删除成功'
						});
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			}
		});
	};

	const actionRender = (
		value: any,
		record: ServiceRuleItem,
		index: number
	) => {
		return (
			<Actions>
				<LinkButton
					title={
						record.silence === null
							? '原生告警规则暂不支持编辑'
							: ''
					}
					disabled={
						record.silence === null ||
						controlledOperationDisabled('maintenance', lock)
					}
					onClick={() => {
						WorkOrderFuc(
							() => {
								history.push(
									`/project/${params.type}/${name}/${aliasName}/container/${currentTab}/createAlarm/${middlewareName}/${chartVersion}/${clusterId}/${namespace}/${record.name}`
								);
							},
							lock,
							middlewareName,
							editRuleOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
				>
					编辑
				</LinkButton>
				<LinkButton
					disabled={controlledOperationDisabled('maintenance', lock)}
					onClick={() => {
						WorkOrderFuc(
							() => {
								removeAlarm(record);
							},
							lock,
							middlewareName,
							deleteRuleOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
				>
					删除
				</LinkButton>
			</Actions>
		);
	};

	const Operation = {
		primary: (
			<div>
				<Button
					type="primary"
					disabled={controlledOperationDisabled('maintenance', lock)}
					onClick={() => {
						WorkOrderFuc(
							() => {
								history.push(
									`/project/${params.type}/${name}/${aliasName}/container/${currentTab}/createAlarm/${params.middlewareName}/${chartVersion}/${params.clusterId}/${params.namespace}`
								);
							},
							lock,
							middlewareName,
							addRuleOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
				>
					新增
				</Button>
			</div>
		)
	};
	const onClose = (
		e: React.MouseEvent<HTMLElement>,
		record: AlarmContactItem
	) => {
		e.preventDefault();
		confirm({
			title: '操作确认',
			content: '请确认是否删除该告警联系人？',
			onOk: async () => {
				await ExecuteOrderFuc();
				return deleteServiceAlarmContact({
					clusterId,
					namespace,
					middlewareName,
					username: record.username
				}).then((res) => {
					if (res.success) {
						getContacts();
						notification.success({
							message: '成功',
							description: '告警联系人删除成功！'
						});
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			},
			onCancel: () => {
				e.preventDefault();
			}
		});
	};
	const onChange = (checked: boolean) => {
		confirm({
			title: '操作确认',
			content: `请确认是否${checked ? '开启' : '关闭'}备份通知？`,
			onOk: async () => {
				await ExecuteOrderFuc();
				return updateServiceBackupNoti({
					clusterId,
					namespace,
					middlewareName,
					enable: checked,
					type: name
				}).then((res) => {
					if (res.success) {
						setBackupSwitch(checked);
						notification.success({
							message: '成功',
							description: `备份通知${
								checked ? '开启' : '关闭'
							}成功`
						});
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};

	const levelRender = (value: any) => {
		const temp = alarmWarn.find((item) => item.value === value);
		return (
			<span className={value + ' level'}>
				{value && temp ? temp?.text : ''}
			</span>
		);
	};

	return (
		<>
			<h2>告警联系人</h2>
			<Space>
				<Button
					type="primary"
					disabled={controlledOperationDisabled('maintenance', lock)}
					onClick={() => {
						WorkOrderFuc(
							() => {
								history.push(
									`/project/${params.type}/${name}/${aliasName}/container/alarm/addContacts/${params.middlewareName}/${chartVersion}/${params.namespace}/${params.clusterId}/${organId}/${projectId}`
								);
							},
							lock,
							middlewareName,
							addContactOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
				>
					新增
				</Button>
				{name === 'redis' ||
				name === 'elasticsearch' ||
				name === 'mysql' ||
				name === 'postgresql' ? (
					<>
						<span>接收备份通知</span>
						<Tooltip title="开启会同步将备份记录中备份失败的信息通知至联系人">
							<QuestionCircleOutlined />
						</Tooltip>
						<Switch
							checked={backupSwitch}
							onChange={(checked: boolean) => {
								WorkOrderFuc(
									() => {
										onChange(checked);
									},
									lock,
									middlewareName,
									modifyBackupToReceiveNotificationOperatorId,
									history,
									type,
									name,
									aliasName,
									clusterId,
									namespace
								);
							}}
						/>
					</>
				) : null}
			</Space>
			<div className="contacts-content">
				<Spin spinning={spinning}>
					{contacts.length === 0 && (
						<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
					)}
					{contacts.length > 0 && (
						<Space wrap>
							{contacts.map(
								(item: AlarmContactItem, index: number) => {
									return (
										<ContactItem
											key={index}
											contact={item}
											disabled={controlledOperationDisabled(
												'maintenance',
												lock
											)}
											onClose={(
												e: React.MouseEvent<HTMLElement>
											) => {
												WorkOrderFuc(
													() => {
														onClose(e, item);
													},
													lock,
													middlewareName,
													deleteContactOperatorId,
													history,
													type,
													name,
													aliasName,
													clusterId,
													namespace
												);
											}}
										/>
									);
								}
							)}
						</Space>
					)}
				</Spin>
			</div>
			<h2>告警规则</h2>
			<ProTable
				dataSource={dataSource}
				loading={loading}
				rowKey="alertId"
				search={{
					placeholder: '请输入告警规则进行搜索',
					value: searchText,
					onChange: (e) => setSearchText(e.target.value),
					onSearch: onSearch,
					style: {
						width: '360px'
					}
				}}
				operation={Operation}
			>
				<ProTable.Column
					title="告警名称"
					dataIndex="name"
					ellipsis={true}
				/>
				<ProTable.Column
					title="告警规则"
					dataIndex="expr"
					ellipsis={true}
				/>
				<ProTable.Column
					title="告警等级"
					dataIndex="level"
					filters={alarmWarn}
					filterMultiple={false}
					onFilter={(value, record: ServiceRuleItem) =>
						value === record.level
					}
					render={levelRender}
					width={120}
				/>
				<ProTable.Column
					title="告警间隔"
					dataIndex="silence"
					filters={silences}
					filterMultiple={false}
					onFilter={(value, record: ServiceRuleItem) =>
						value === record.silence
					}
					render={nullRender}
					width={120}
				/>
				<ProTable.Column
					title="最近修改时间"
					dataIndex="createTime"
					render={createTimeRender}
					sorter={(a: any, b: any) =>
						new Date(a.createTime).getTime() -
						new Date(b.createTime).getTime()
					}
					width={160}
				/>
				<ProTable.Column
					title="操作"
					dataIndex="option"
					render={actionRender}
					width={120}
				/>
			</ProTable>
		</>
	);
}

export default Rules;

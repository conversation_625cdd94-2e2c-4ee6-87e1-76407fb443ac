import React, { useState, useEffect } from 'react';
import { ProPage, ProContent, ProHeader } from '@/components/ProPage';
import {
	Button,
	Select,
	Row,
	Col,
	Popover,
	notification,
	InputNumber
} from 'antd';
import {
	QuestionCircleOutlined,
	PlusOutlined,
	MinusOutlined
} from '@ant-design/icons';
import { IconFont } from '@/components/IconFont';

import { getComponents } from '@/services/common';
import {
	createAlarms,
	getCanUseAlarms,
	updateAlarms,
	getAlarmDetail
} from '@/services/middleware';
import { symbols, alarmWarn, silences } from '@/utils/const';
import { useParams } from 'react-router';

import {
	AlarmItem,
	AlarmSendData,
	CreateServeAlarmProps,
	LabelItem,
	ServiceRuleItem
} from '../detail';
import { ComponentProp } from '@/pages/ResourcePoolManagement/resource.pool';
import './index.less';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';

const { Option } = Select;

function CreateAlarm(): JSX.Element {
	const params: CreateServeAlarmProps = useParams();
	const { clusterId, namespace, middlewareName, type, alertName } = params;
	const [alarms, setAlarms] = useState<AlarmItem[]>([
		{
			alert: null,
			description: null
		}
	]);
	const [alarmRules, setAlarmRules] = useState<ServiceRuleItem[]>([]);
	const [selectUser, setSelectUser] = useState<any[]>([]);
	const [mailChecked, setMailChecked] = useState<boolean>(false);
	const [dingChecked, setDingChecked] = useState<boolean>(false);
	const [isRule, setIsRule] = useState<boolean>();
	const [detail, setDetail] = useState<ServiceRuleItem>();
	const [disabled, setDisabled] = useState<boolean>(true);
	const [alertManager, setAlertManager] = useState<ComponentProp>();
	useEffect(() => {
		getComponents({
			clusterId: clusterId
		}).then((res) => {
			if (res.success) {
				const at = res.data.find(
					(item: ComponentProp) => item.component === 'alertmanager'
				);
				setAlertManager(at);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	}, []);
	const getCanUse = (
		clusterId: string,
		namespace: string,
		middlewareName: string,
		type: string
	) => {
		const sendData = {
			clusterId,
			namespace,
			middlewareName,
			type
		};
		getCanUseAlarms(sendData).then((res) => {
			if (res.success) {
				setDisabled(false);
				setAlarms(JSON.parse(JSON.stringify(res.data)));
				if (alertName) {
					getAlarmDetail({
						alertName,
						clusterId,
						namespace,
						middlewareName
					}).then((res: any) => {
						setAlarmRules([
							{
								...res.data,
								severity: res.data.level
							}
						]);
						res.data.ding
							? setDingChecked(true)
							: setDingChecked(false);
						res.data.mail
							? setMailChecked(true)
							: setMailChecked(false);
						setDetail(res.data);
					});
				} else {
					setAlarmRules([{ silence: alertManager?.silentTime }]);
				}
				if (res.data.length < 0) {
					notification.error({
						message: '错误',
						description: '当前中间件没有可以设置规则的监控项！'
					});
				}
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};

	const onChange = (value: any, record: ServiceRuleItem, type: string) => {
		if (type === 'alert') {
			const listTemp = alarms;
			const filterItem: AlarmItem[] = listTemp.filter(
				(item) => item.alert === value
			);
			const list = alarmRules.map((item: ServiceRuleItem) => {
				if (item.id === record.id) {
					item.alert = value;
					item.annotations = filterItem[0].annotations;
					item.description = filterItem[0].description as string;
					item.expr = filterItem[0].expr as string;
					item.labels = filterItem[0].labels as LabelItem;
					item.level = filterItem[0].level;
					item.name = value;
					item.status = filterItem[0].status as string;
					item.time = filterItem[0].time as string;
					item.type = filterItem[0].type as string;
					item.unit = filterItem[0].unit as string;
					return item;
				} else {
					return item;
				}
			});
			setAlarmRules(list);
		} else if (type === 'alertTime') {
			const list = alarmRules.map((item) => {
				if (item.id === record.id) {
					item.alertTime = value;
					if (
						Number(item.alertTime) > 1440 ||
						Number(item.alertTime) < 1
					) {
						setIsRule(true);
					} else {
						setIsRule(false);
					}
					return item;
				} else {
					return item;
				}
			});
			setAlarmRules(list);
		} else if (type === 'alertTimes') {
			const list = alarmRules.map((item) => {
				if (item.id === record.id) {
					item.alertTimes = value;
					if (
						Number(item.alertTimes) > 1000 ||
						Number(item.alertTimes) < 1
					) {
						setIsRule(true);
					} else {
						setIsRule(false);
					}
					return item;
				} else {
					return item;
				}
			});
			setAlarmRules(list);
		} else {
			const list = alarmRules.map((item) => {
				if (item.id === record.id) {
					item[type] = value;
					return item;
				} else {
					return item;
				}
			});
			setAlarmRules(list);
		}
	};

	const addAlarm = (index: number) => {
		alertManager?.silentTime
			? setAlarmRules([
					...alarmRules,
					{
						id: Math.random() * 100,
						silence: alarmRules[index].silence
					}
			  ])
			: setAlarmRules([
					...alarmRules,
					{
						id: Math.random() * 100
					}
			  ]);
	};
	const copyAlarm = (index: number) => {
		if (alarms && alarms.length > 0) {
			const addItem = alarmRules[index];
			setAlarmRules([
				...alarmRules,
				{
					...addItem,
					id: Math.random() * 100,
					alert: undefined,
					content: ''
				}
			]);
		}
	};
	const delAlarm = (i: number) => {
		const list = alarmRules.filter((item) => item.id !== i);
		setAlarmRules(list);
	};

	useEffect(() => {
		if (alertManager) {
			getCanUse(clusterId, namespace, middlewareName, type);
		}
	}, [alertManager]);

	const onCreate = async (value: any) => {
		const sendData: AlarmSendData = {
			url: {
				clusterId: clusterId,
				middlewareName: middlewareName,
				namespace: namespace
			},
			ding: dingChecked ? 'ding' : '',
			data: {
				middlewareAlertsDTOList: value,
				users: selectUser
			}
		};
		await ExecuteOrderFuc();
		if (alertName) {
			const updateSendData = {
				clusterId: clusterId,
				middlewareName: middlewareName,
				namespace: namespace,
				alertName: alertName,
				symbol: value[0].symbol,
				threshold: value[0].threshold,
				alertTime: value[0].alertTime,
				alertTimes: value[0].alertTimes,
				level: value[0].level,
				silence: value[0].silence,
				labels: value[0].labels,
				annotations: value[0].annotations,
				expr: value[0].expr
			};
			updateAlarms(updateSendData).then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '告警规则修改成功'
					});
					window.history.back();
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		} else {
			createAlarms(sendData).then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '告警规则设置成功'
					});
					window.history.back();
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		}
	};

	const onOk = () => {
		const flag = alarmRules.every(
			(item) =>
				item.name &&
				item.alertTimes &&
				item.alertTime &&
				item.symbol &&
				(item.threshold || item.threshold === 0) &&
				item.severity &&
				item.silence
		);
		if (isRule) {
			notification.error({
				message: '失败',
				description: '监控项不符合规则'
			});
			return;
		}
		const list = alarmRules.map((item) => {
			item.labels = {
				...item.labels,
				severity: item.severity as string
			};
			item.lay = 'service';
			detail ? (item.enable = detail.enable) : (item.enable = 0);
			dingChecked ? (item.ding = 'ding') : (item.ding = '');
			mailChecked ? (item.mail = 'mail') : (item.mail = '');
			item.ip = window.location.host;
			return item;
		});
		if (flag) {
			if (mailChecked) {
				if (selectUser && selectUser.length) {
					onCreate(list);
				} else {
					notification.error({
						message: '失败',
						description: '请选择邮箱通知用户'
					});
				}
			} else {
				onCreate(list);
			}
		} else {
			notification.error({
				message: '失败',
				description: '缺少监控项'
			});
		}
	};

	return (
		<ProPage className="create-alarm">
			<ProHeader
				title={
					alertName
						? '修改告警规则'
						: `新建告警规则${
								middlewareName ? '(' + middlewareName + ')' : ''
						  }`
				}
				onBack={() => {
					window.history.back();
				}}
			/>
			<ProContent>
				<h2>告警规则</h2>
				<Row className="table-header">
					<Col span={5}>
						<span>告警指标</span>
					</Col>
					<Col span={5}>
						<span>告警阈值</span>
					</Col>
					<Col span={6}>
						<span>触发规则</span>
						<Popover
							content={'特定时间内≥设定的触发次数，则预警一次'}
						>
							<QuestionCircleOutlined
								style={{
									marginLeft: '5px',
									color: '#33',
									cursor: 'pointer'
								}}
							/>
						</Popover>
					</Col>
					<Col span={3}>
						<span>告警等级</span>
					</Col>
					<Col span={3}>
						<span>告警间隔</span>
						<Popover
							content={
								'预警一次过后，间隔多久后再次执行预警监测，防止预警信息爆炸'
							}
						>
							<QuestionCircleOutlined
								style={{
									marginLeft: '5px',
									color: '#33',
									cursor: 'pointer'
								}}
							/>
						</Popover>
					</Col>
					<Col span={2}>
						<span>告警操作</span>
					</Col>
				</Row>
				<div style={{ maxHeight: '470px', overflowY: 'auto' }}>
					{alarmRules &&
						alarmRules.map((item, index) => {
							return (
								<div key={index}>
									<Row>
										<Col span={5}>
											<span className="ne-required"></span>
											<Select
												placeholder="请选择"
												onChange={(value) =>
													onChange(
														value,
														item,
														'alert'
													)
												}
												style={{
													marginRight: 8,
													width: '100%'
												}}
												dropdownMatchSelectWidth={false}
												value={item.name}
												disabled={
													alertName as unknown as boolean
												}
											>
												{alarms &&
													alarms.map((i) => {
														return (
															<Option
																key={i.alert}
																value={i.alert}
															>
																{type ===
																'zookeeper'
																	? i.alert
																	: i.description}
															</Option>
														);
													})}
											</Select>
										</Col>
										<Col span={5}>
											<Select
												placeholder="请选择"
												onChange={(value) =>
													onChange(
														value,
														item,
														'symbol'
													)
												}
												style={{
													width: '50%',
													minWidth: 'auto'
												}}
												dropdownMatchSelectWidth={false}
												value={item.symbol}
											>
												{symbols.map((i) => {
													return (
														<Option
															key={i.value}
															value={i.value}
														>
															{i.label}
														</Option>
													);
												})}
											</Select>
											<InputNumber
												style={{
													width: '50%',
													borderLeft: 0
												}}
												min="0"
												max="100"
												value={item.threshold}
												onChange={(value) => {
													onChange(
														value || '0',
														item,
														'threshold'
													);
												}}
											/>
										</Col>
										<Col span={6}>
											<InputNumber
												style={{ width: '80px' }}
												value={item.alertTime}
												placeholder="1-1440"
												min="1"
												max="1440"
												onChange={(value) => {
													onChange(
														value || '0',
														item,
														'alertTime'
													);
												}}
											/>
											<span className="info">
												分钟内触发
											</span>
											<InputNumber
												style={{ width: '80px' }}
												value={item.alertTimes}
												placeholder="1-1000"
												min="1"
												max="1000"
												onChange={(value) => {
													onChange(
														value || '0',
														item,
														'alertTimes'
													);
												}}
											></InputNumber>
											<span className="info">次</span>
										</Col>
										<Col span={3}>
											<Select
												placeholder="请选择"
												onChange={(value) =>
													onChange(
														value,
														item,
														'severity'
													)
												}
												style={{ width: '100%' }}
												value={item.severity}
												dropdownMatchSelectWidth={false}
											>
												{alarmWarn.map((i) => {
													return (
														<Option
															key={i.text}
															value={i.value}
														>
															{i.text}
														</Option>
													);
												})}
											</Select>
										</Col>
										<Col span={3}>
											<Select
												placeholder="请选择"
												onChange={(value) =>
													onChange(
														value,
														item,
														'silence'
													)
												}
												dropdownMatchSelectWidth={false}
												style={{ width: '100%' }}
												defaultValue={
													alertManager?.silentTime
												}
												value={item.silence}
											>
												{silences.map((i) => {
													return (
														<Option
															key={i.value}
															value={i.value}
														>
															{i.text}
														</Option>
													);
												})}
											</Select>
										</Col>
										<Col span={2}>
											<Button
												disabled={
													alertName as unknown as boolean
												}
												style={{ marginRight: '8px' }}
												onClick={() => copyAlarm(index)}
												icon={
													<IconFont
														type="icon-fuzhi1"
														size={12}
														style={{
															color: '#0064C8'
														}}
													/>
												}
											></Button>
											<Button
												disabled={
													alertName as unknown as boolean
												}
												onClick={() => addAlarm(index)}
												style={{ marginRight: '8px' }}
												icon={
													<PlusOutlined
														style={{
															color: '#0064C8'
														}}
													/>
												}
											></Button>
											{index !== 0 && (
												<Button
													onClick={() =>
														delAlarm(
															item.id as number
														)
													}
													icon={
														<MinusOutlined
															style={{
																color: '#0064C8'
															}}
														/>
													}
												></Button>
											)}
										</Col>
									</Row>
								</div>
							);
						})}
				</div>
				<div className="alarm-bottom" style={{ margin: 0 }}>
					<Button
						onClick={onOk}
						type="primary"
						disabled={disabled}
						style={{ marginRight: '9px' }}
					>
						确认
					</Button>
					<Button
						onClick={() => {
							window.history.back();
						}}
					>
						取消
					</Button>
				</div>
			</ProContent>
		</ProPage>
	);
}
export default CreateAlarm;

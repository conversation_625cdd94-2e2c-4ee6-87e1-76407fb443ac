import DefaultPicture from '@/components/DefaultPicture';
import storage from '@/utils/storage';
import { Alert, Button, notification, Menu } from 'antd';
import React, { useEffect, useState } from 'react';
import { DetailParams, ServeAlarmProps } from '../detail';
import { useHistory, useParams } from 'react-router';
import Rules from './index';
import Record from './Record';
import { getCluster } from '@/services/common';
import SecondContent from '@/components/SecondContent';

export default function ServeAlarm(props: ServeAlarmProps): JSX.Element {
	const { alertOpen, role, customMid, capabilities, lock, subMenu } = props;
	const [selectedKey, setSelectedKey] = useState<string[]>([
		storage.getSession('alertTab') || 'record'
	]);
	const hasWebpage = window.location.href.includes('webpage');
	const params: DetailParams = useParams();
	const { currentTab, clusterId } = params;
	const history = useHistory();
	const [clusterAliasName, setClusterAliasName] = useState<string>('');
	const [items, setItems] = useState([
		{
			label: '告警记录',
			key: 'record',
			code: 'alarmLog'
		},
		{
			label: '告警规则及通知',
			key: 'rule',
			code: 'alarmRule'
		}
	]);

	// useEffect(() => {
	// 	currentTab && currentTab !== 'alarm' && setSelectedKey(['record']);
	// }, [currentTab]);
	useEffect(() => {
		const menu = items.filter((item: any) =>
			subMenu.find((menu: any) => menu.name === item.code)
		);
		if (!hasWebpage) {
			setItems(menu);
		}
		// (!storage.getSession('alertTab') ||
		// 	!menu?.some(
		// 		(item) => item.key === storage.getSession('alertTab')
		// 	)) &&
		// 	setSelectedKey([menu[0].key]);
	}, [subMenu]);
	useEffect(() => {
		// * 当未告警组件未开启时，调用获取集群详情的接口，获取当前集群的nickname进行跳转
		if (clusterId && !alertOpen) {
			getCluster({ clusterId }).then((res) => {
				if (res.success) {
					setClusterAliasName(res.data.nickname);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			});
		}
	}, [clusterId, alertOpen]);
	const menuSelect = (item: any) => {
		setSelectedKey(item.keyPath);
		storage.setSession('alertTab', item.key);
	};
	const ConsoleMenu = () => {
		return (
			<Menu
				selectedKeys={selectedKey}
				onClick={menuSelect}
				items={items}
				mode="inline"
				className="serve-alarm-menu"
			/>
		);
	};
	const childrenRender = (selectedKey: string) => {
		switch (selectedKey) {
			case 'record':
				return <Record lock={lock} />;
			case 'rule':
				return <Rules lock={lock} />;
			default:
				return null;
		}
	};
	// * 自定义组件不支持告警时
	if (customMid && !capabilities?.includes('alert')) {
		return <DefaultPicture />;
	}
	return (
		<>
			{!alertOpen && (
				<Alert
					type="warning"
					showIcon
					style={{ marginBottom: 24 }}
					message={
						<div>
							集群{clusterAliasName}
							尚且未安装告警组件，将无法正常告警!
							{role.isAdmin && (
								<Button
									type="link"
									style={{
										height: 20,
										padding: 0
									}}
									onClick={() => {
										storage.setSession(
											'cluster-detail-current-tab',
											'component'
										);
										history.push(
											`/platform/clusterManagement/resourcePoolDetail/${clusterId}`
										);
									}}
								>
									立即安装
								</Button>
							)}
							{!role.isAdmin && '请联系管理员进行安装!'}
						</div>
					}
				/>
			)}
			<SecondContent
				menu={<ConsoleMenu />}
				style={{ margin: 0, padding: 0 }}
			>
				{childrenRender(selectedKey[0])}
			</SecondContent>
		</>
	);
}

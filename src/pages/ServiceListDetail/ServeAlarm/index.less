.create-alarm {
	.header {
		padding: 0 24px;
		margin-top: 16px;
		display: flex;
		align-items: center;
		.next-icon::before {
			cursor: pointer;
			width: 32px;
			font-size: 32px;
		}
		.sc-fod<PERSON>ek {
			margin: 0;
			padding-left: 16px;
		}
	}

	h2 {
		font-size: @font-2;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: @font-weight;
		color: @text-color-title;
		line-height: 22px;
		margin: 16px 0;
		display: flex;
		align-items: center;
		&::before {
			content: '';
			display: inline-block;
			width: 1px;
			height: 14px;
			border: 1px solid #0064c8;
			margin-right: 5px;
		}
	}

	.ant-row {
		box-shadow: 0px 1px 0px 0px @border-color;
		align-items: center;
		.ant-col {
			padding: 10px;
			display: flex;
			vertical-align: baseline;
			.ant-input {
				min-width: auto;
			}
		}
		&.table-header {
			background: #fbfcfd;
			font-size: @font-1;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: @font-weight;
			color: @text-color-title;
			height: 40px;
			.ant-col {
				padding: 0 10px;
			}
			span {
				line-height: 39px;
			}
		}
		.ne-required {
			&::before {
				top: 20px;
			}
		}
		.ant-btn {
			width: 24px;
			height: 24px;
		}
		.info {
			display: inline-block;
			background: #ebebeb;
			color: #c1c1c1;
			height: 32px;
			padding: 0 8px;
			line-height: 32px;
		}
		.error-info {
			span {
				color: #c80000;
			}
		}
	}
	.users {
		display: flex;
		align-items: center;
	}
	// .transfer {
	// 	margin-top: 16px;
	// 	display: flex;
	// 	box-sizing: border-box;
	// 	padding: 18px 20px;
	// 	background: #f7f7f7;
	// 	border-radius: @border-radius;
	// 	.ne-required {
	// 		width: 75px;
	// 	}
	// 	.transfer-container {
	// 		width: 100%;
	// 	}
	// 	.transfer-header {
	// 		display: flex;
	// 		justify-content: space-between;
	// 	}
	// 	.transfer-title {
	// 		padding: 8px;
	// 		border: 1px solid #ddd;
	// 		border-bottom: none;
	// 		width: calc(50% - 30px);
	// 		background: @white;
	// 		box-sizing: border-box;
	// 	}
	// 	.next-transfer {
	// 		width: 100%;
	// 		justify-content: space-between;
	// 		align-items: center;
	// 		.next-transfer-operations {
	// 			button {
	// 				color: #0064c8;
	// 				background: transparent !important;
	// 				.next-icon::before {
	// 					font-size: 24px;
	// 					transform: translateX(-6px);
	// 				}
	// 			}
	// 			button:disabled {
	// 				color: #ddd;
	// 			}
	// 			button:nth-of-type(2) {
	// 				position: relative;
	// 				top: -50px;
	// 				z-index: 99;
	// 				left: 60px;
	// 				opacity: 0;
	// 			}
	// 		}

	// 		.next-transfer-panel {
	// 			width: calc(50% - 30px);
	// 			overflow: auto;
	// 			.next-menu {
	// 				width: 100%;
	// 			}
	// 			&:nth-of-type(1) {
	// 				.label {
	// 					display: none;
	// 				}
	// 				.disabled {
	// 					.item-content {
	// 						color: #ddd;
	// 					}
	// 				}
	// 			}
	// 			&:nth-of-type(3) {
	// 				label {
	// 					display: none;
	// 				}
	// 				margin-left: 12px;
	// 			}
	// 			ul {
	// 				width: 800px !important;
	// 				border-top: 1px solid @border-color;
	// 			}

	// 			.next-transfer-panel-footer {
	// 				width: 800px;
	// 				border-top: 1px solid @border-color;
	// 			}

	// 			.next-transfer-panel-header {
	// 				width: 800px;
	// 				transform: translateY(50px);
	// 				padding: 16px 30px 16px 45px;
	// 				span {
	// 					width: 100px;
	// 					display: inline-block;
	// 					font-size: @font-1;
	// 					font-family: PingFangSC-Medium, PingFang SC;
	// 					font-weight: @font-weight;
	// 					color: @text-color-title;
	// 					line-height: @line-height-3;
	// 					&:nth-of-type(3) {
	// 						width: 160px;
	// 					}
	// 					&:nth-of-type(5) {
	// 						width: 160px;
	// 					}
	// 				}
	// 			}
	// 			.next-menu-item-inner {
	// 				overflow: visible;
	// 			}
	// 			.next-search {
	// 				transform: translateY(-50px);
	// 			}
	// 			.item-content {
	// 				display: inline-block;
	// 				font-size: @font-1;
	// 				font-family: PingFangSC-Regular, PingFang SC;
	// 				font-weight: @font-weight-sm;
	// 				color: @text-color-title;
	// 				line-height: @line-height-3;
	// 				width: 100px;
	// 				word-break: break-all;
	// 				&:nth-of-type(3) {
	// 					width: 160px;
	// 				}
	// 				&:nth-of-type(5) {
	// 					width: 160px;
	// 				}
	// 			}
	// 		}
	// 	}
	// }
	.alarm-bottom {
		margin-top: 32px;
		padding: 16px 9px;
		box-shadow: 0px -1px 0px 0px @border-color;
	}
	.email {
		margin-top: 16px;
		padding: 16px 24px;
		display: flex;
		background: @black-8;
		.ne-required {
			margin-right: 16px;
			&::before {
				top: 12px;
			}
		}
	}
	.transfer {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 12px;
		width: 1050px;
		.transfer-box {
			border: 1px solid @border-color;
			background: @black-10;
			.transfer-title {
				padding: 8px;
				border-bottom: 1px solid @border-color;
			}
			.titles {
				width: 700px;
				padding: 8px;
				border-bottom: 1px solid @border-color;
				color: #000000;
				font-weight: 500;
			}
			.disabled {
				span{
					color: @black-6;
				}
			}
			p > span {
				display: inline-block;
				width: 100px;
				padding: 0 16px;
				box-sizing: border-box;
				&:nth-of-type(1) {
					margin-left: 32px;
				}
			}
			li > span {
				display: inline-block;
				width: 100px;
				padding: 0 16px;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				&.anticon-delete {
					width: auto;
				}
			}
			.transfer-header {
				padding: 8px 8px 0 8px;
			}
			.transfer-content {
				width: 500px;
				overflow: scroll;
			}
			ul {
				width: 700px;
				height: 160px;
				li {
					padding: 8px;
					display: flex;
					align-items: center;
					cursor: pointer;
					&:hover {
						background-color: #f7f9fa;
					}
					& > span {
						display: inline-block;
					}
				}
			}
			.transfer-footer {
				height: 39px;
				border-top: 1px solid @border-color;
				color: #0070cc;
				line-height: 23px;
				padding: 8px 16px;
				cursor: pointer;
			}
		}
		.db-name {
			width: 70px;
			margin-right: 16px;
			text-overflow: ellipsis;
			overflow: hidden;
			white-space: nowrap;
		}
	}
}

.level {
	width: 40px;
	height: 22px;
	text-align: center;
	line-height: 22px;
	font-family: PingFangSC-Regular, PingFang SC;
	font-weight: @font-weight-sm;
	border-radius: @border-radius;
	display: inline-block;
	&.critical {
		background: #fff1f0;
		color: #ff4d4f;
		border: 1px solid #ffa39e;
	}
	&.warning {
		background: #ffecc7;
		color: #faa700;
		border: 1px solid #ffdb94;
	}
	&.info {
		background: #c7ecff;
		color: #00a7fa;
		border: 1px solid #94dbff;
	}
}

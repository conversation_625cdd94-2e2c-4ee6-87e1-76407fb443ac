import React, { useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router';
import ProTable from '@/components/ProTable';
import { Button, Modal, notification } from 'antd';
import type { TablePaginationConfig } from 'antd/es/table';
import { AlarmRecordItem } from '@/pages/AlarmCenter/alarm';
import { getServiceAlertRecords } from '@/services/alarm';
import { alarmWarn, maintenances } from '@/utils/const';
import { nullRender } from '@/utils/utils';
import { deleteRecords } from '@/services/alarm';
import { DetailParams } from '../detail';
import useRefresh from '@/utils/useRefresh';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';

interface TableParams {
	pagination?: TablePaginationConfig;
	filters?: any;
	sorter?: any;
}

export default function Record({ lock }: { lock: string }): JSX.Element {
	const {
		clusterId,
		name,
		namespace,
		middlewareName,
		type,
		aliasName
	}: DetailParams = useParams();
	const history = useHistory();
	const deleteRecordOperatorId = maintenances['Alarm Record Management'];
	const [records, setRecords] = useState<AlarmRecordItem[]>([]);
	const [current, setCurrent] = useState<number>(1);
	const [size, setSize] = useState<number>(10);
	const [total, setTotal] = useState<number>(0);
	const [keyword, setKeyword] = useState<string>('');
	const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
	const [tableParams, setTableParams] = useState<TableParams>({});
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		const sendData = {
			alertLevel: '',
			alertRecordQueryDto: '',
			alertTarget: '',
			alertTime: '',
			alertType: 'service',
			clusterId,
			current,
			keyword,
			middlewareName,
			middlewareType: name,
			namespace,
			receiveTime: '',
			size: size
		};
		getData(sendData);
	}, [refreshKey]);
	const onSearch = (value: string) => {
		let alertLevelFilter = '';
		let alertTimeSorter = '';
		let alertReceiveTimeSorter = '';
		if (
			tableParams?.filters &&
			JSON.stringify(tableParams.filters) !== '{}'
		) {
			alertLevelFilter = tableParams.filters[0];
		}
		if (tableParams.sorter && tableParams.sorter.field === 'alertTime') {
			alertTimeSorter = tableParams.sorter.order
				? tableParams.sorter.order === 'ascend'
					? 'asc'
					: 'desc'
				: '';
		}
		if (
			tableParams.sorter &&
			tableParams.sorter.field === 'alertReceiveTime'
		) {
			alertReceiveTimeSorter = tableParams.sorter.order
				? tableParams.sorter.order === 'ascend'
					? 'asc'
					: 'desc'
				: '';
		}
		const sendData = {
			alertLevel: alertLevelFilter ? alertLevelFilter[0] : '',
			alertRecordQueryDto: '',
			alertTarget: '',
			alertTime: alertTimeSorter,
			alertType: 'service',
			clusterId,
			current: 1,
			keyword: value,
			middlewareName,
			middlewareType: name,
			namespace,
			receiveTime: alertReceiveTimeSorter,
			size: size
		};
		getData(sendData);
	};
	const getData = (sendData: any) => {
		getServiceAlertRecords(sendData).then((res) => {
			if (res.success) {
				setRecords(res.data.list);
				setCurrent(res.data.pageNum || 1);
				setTotal(res.data.total);
			} else {
				notification.error({
					message: '错误',
					description: res.errorMsg
				});
			}
		});
	};
	const onChange = (page: number, pageSize: number) => {
		console.log(page, pageSize);
		setCurrent(page);
		setSize(pageSize);
	};
	const onTableChange = (pagination: any, filters: any, sorter: any) => {
		let alertTimeSorterTemp = '';
		let receiveTimeSorterTemp = '';
		let alertLevelFilterTemp = [''];
		setTableParams({
			pagination,
			filters,
			sorter
		});
		if (sorter.field === 'alertReceiveTime') {
			receiveTimeSorterTemp = sorter.order
				? sorter.order === 'ascend'
					? 'asc'
					: 'desc'
				: '';
		}
		if (sorter.field === 'alertTime') {
			alertTimeSorterTemp = sorter.order
				? sorter.order === 'ascend'
					? 'asc'
					: 'desc'
				: '';
		}
		if (JSON.stringify(filters) !== '{}') {
			alertLevelFilterTemp = filters[0];
		}
		const sendData = {
			alertLevel: alertLevelFilterTemp ? alertLevelFilterTemp[0] : '',
			alertRecordQueryDto: '',
			alertTarget: '',
			alertTime: alertTimeSorterTemp,
			alertType: 'service',
			clusterId,
			current: pagination.current,
			keyword,
			middlewareName,
			middlewareType: name,
			namespace,
			receiveTime: receiveTimeSorterTemp,
			size: pagination.pageSize
		};
		getData(sendData);
	};
	const levelRender = (
		value: string,
		record: AlarmRecordItem,
		index: number
	) => {
		return (
			<span className={value + ' level'}>
				{value && alarmWarn.find((item) => item.value === value)
					? alarmWarn.find((item) => item.value === value).text
					: ''}
			</span>
		);
	};
	const deleteSelected = () => {
		Modal.confirm({
			title: '操作确认',
			content: '是否确认删除当前所选的告警记录?',
			okText: '确定',
			cancelText: '取消',
			onOk: async () => {
				await ExecuteOrderFuc();
				return deleteRecords({
					ids: selectedRowKeys
				}).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '删除成功'
						});
						onSearch(keyword);
						setSelectedRowKeys([]);
					} else {
						notification.success({
							message: '失败',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};
	const Operation = {
		primary: (
			<div>
				{selectedRowKeys.length ? (
					<Button
						type="primary"
						danger
						className="mr-16"
						onClick={() => {
							WorkOrderFuc(
								() => {
									deleteSelected();
								},
								lock,
								middlewareName,
								deleteRecordOperatorId,
								history,
								type,
								name,
								aliasName,
								clusterId,
								namespace
							);
						}}
					>
						删除选中
					</Button>
				) : null}
			</div>
		)
	};
	return (
		<ProTable
			dataSource={records}
			search={{
				placeholder: '请输入告警信息搜索',
				onSearch: onSearch,
				value: keyword,
				onChange: (e) => setKeyword(e.target.value)
			}}
			operation={Operation}
			pagination={{
				total: total,
				current: current,
				pageSize: size,
				onChange: onChange
			}}
			rowSelection={{
				selectedRowKeys,
				onChange(selectedRowKeys, selectedRows, info) {
					setSelectedRowKeys(selectedRowKeys);
				}
			}}
			onChange={onTableChange}
			rowKey="id"
		>
			<ProTable.Column
				dataIndex="level"
				title="告警等级"
				filters={alarmWarn}
				filterMultiple={false}
				render={levelRender}
				width={100}
			/>
			<ProTable.Column
				dataIndex="alertTime"
				title="告警时间"
				render={nullRender}
				width={180}
				sorter={true}
			/>
			<ProTable.Column
				dataIndex="alertReceiveTime"
				title="接收时间"
				render={nullRender}
				width={180}
				sorter={true}
			/>
			<ProTable.Column
				dataIndex="message"
				title="告警信息"
				ellipsis={true}
			/>
		</ProTable>
	);
}

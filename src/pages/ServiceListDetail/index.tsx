import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { StoreState } from '@/types';
import { useHistory, useParams, useLocation } from 'react-router-dom';
import { ProPage, ProHeader, ProContent } from '@/components/ProPage';
import { Button, Tabs, notification, Alert, Space, Spin } from 'antd';

import BasicInfo from './BasicInfo/index';
import HighAvailability from './HighAvailability/index';
import BackupRecovery from './BackupRecovery/index';
import Monitor from './Monitor/index';
import Log from './Log/index';
import ServerAlarm from './ServeAlarm/serveAlarm';
import ParamterSetting from './ParamterSetting/index';
import Disaster from './Disaster/index';
import RedisDisaster from './Disaster/redisDisaster';
import DataBase from './Database/index';
import RedisDataBase from './RedisDatabase/index';
import ServiceDetailIngress from './ServiceIngress';

import { getMiddlewareDetail } from '@/services/middleware';
import { getComponents } from '@/services/common';
import { licenseFeatures } from '@/services/user';
import storage from '@/utils/storage';

import { DetailParams } from './detail';
import { middlewareDetailProps } from '@/types/comment';
import { User } from '@/types';
import {
	LoadingOutlined,
	LockOutlined,
	ReloadOutlined
} from '@ant-design/icons';
import OperatorAbility from './OperatorAbility';
import './detail.less';
import { WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';
import { controlledOperationDisabled } from '@/utils/utils';

/*
 * 自定义中间tab页显示判断 后端
 * 基本信息  basic
 * 实例详情  high
 * 运维能力  ？
 * 数据安全  backup
 * 服务暴露  ingress
 * 数据监控  monitoring
 * 日志详情  log
 * 参数设置  config
 * 服务告警  alert
 * 灾备服务  disaster(目前mysql\redis\kafka中间件特有)
 */

const { TabPane } = Tabs;
const InstanceDetails = ({ buttonList }: { buttonList: any }) => {
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const hasWebpage = window.location.href.includes('webpage');
	const history = useHistory();
	const location = useLocation<{ flag: boolean }>();
	const params: DetailParams = useParams();
	const {
		middlewareName,
		type,
		chartVersion,
		currentTab,
		name,
		aliasName,
		namespace,
		clusterId
	} = params;
	const [data, setData] = useState<middlewareDetailProps>();
	const [status, setStatus] = useState<string>('');
	const [customMid, setCustomMid] = useState<boolean>(false); // * 判断是否是自定义中间件
	const [reason, setReason] = useState<string>('');
	const [activeKey, setActiveKey] = useState<string>(
		currentTab || 'basicInfo'
	);
	const [loggingOpen, setLoggingOpen] = useState<boolean>(false);
	const [grafanaOpen, setGrafanaOpen] = useState<boolean>(false);
	const [alertOpen, setAlertOpen] = useState<boolean>(false);
	// * 灾备是否开启判断
	const [disasterOpen, setDisasterOpen] = useState<boolean>(false);
	// * 用户角色
	const [role, setRole] = useState<User>();
	// * 是否支持灾备
	const [canDisaster, setCanDisaster] = useState<boolean>(false);
	// * 运维面板 feature功能
	const [maintenanceDashboardAPI] = useState<any>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'maintenanceDashboard')
			?.enabled ?? true
	);
	// * 获取进入运维面板的operatorId
	const intoMaintenanceOperatorId =
		maintenances['Enter Operations Panel/Service Console'];
	// * 详情页loading
	const [spinning, setSpinning] = useState<boolean>(false);
	// * 判断当前角色权限是否显示该菜单
	const showMenu: any = (code: string) =>
		hasWebpage || buttonList?.find((item: any) => item.name === code);
	const subMenu: any = (code: string) =>
		buttonList?.find((item: any) => item.name === code)?.subMenu || [];

	useEffect(() => {
		if (location?.state?.flag) {
			window.location.reload();
		}
	}, [location?.state?.flag]);
	useEffect(() => {
		if (storage.getLocal('role')) {
			const roleT = JSON.parse(storage.getLocal('role'));
			setRole(roleT);
		}
	}, [organId, storage.getLocal('role')]);
	useEffect(() => {
		getData();
		licenseFeatures().then((res) => {
			if (res.success) {
				setDisasterOpen(res.data.disasterRecoveryEnable);
			}
		});
		getComponents({ clusterId }).then((res) => {
			if (res.success) {
				const loggingTemp = res.data.find(
					(item: any) => item.component === 'logging'
				).status;
				const grafanaTemp = res.data.find(
					(item: any) => item.component === 'grafana'
				).status;
				const alertTemp = res.data.find(
					(item: any) => item.component === 'alertmanager'
				).status;
				switch (loggingTemp) {
					case 1:
						setLoggingOpen(true);
						break;
					case 3:
						setLoggingOpen(true);
						break;
					case 4:
						setLoggingOpen(true);
						break;
					default:
						setLoggingOpen(false);
						break;
				}
				switch (grafanaTemp) {
					case 1:
						setGrafanaOpen(true);
						break;
					case 3:
						setGrafanaOpen(true);
						break;
					case 4:
						setGrafanaOpen(true);
						break;
					default:
						setGrafanaOpen(false);
						break;
				}
				switch (alertTemp) {
					case 1:
						setAlertOpen(true);
						break;
					case 3:
						setAlertOpen(true);
						break;
					case 4:
						setAlertOpen(true);
						break;
					default:
						setAlertOpen(false);
						break;
				}
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	}, [middlewareName, clusterId]);

	useEffect(() => {
		setActiveKey(currentTab);
		currentTab !== 'operatorAbility' &&
			storage.setSession('operatorTab', 'storageList');
	}, [currentTab]);

	const getData = () => {
		const sendData = {
			clusterId,
			namespace,
			middlewareName,
			type: name,
			deployMod: 'container'
		};
		setSpinning(true);
		getMiddlewareDetail(sendData)
			.then((res) => {
				if (res.success) {
					storage.setSession('currentService', res.data);
					setData(res.data);
					setReason(res.data.reason);
					setStatus(res.data.status || 'Failed');
					if (
						name === 'redis' ||
						name === 'mysql' ||
						name === 'kafka' ||
						name === 'elasticsearch' ||
						name === 'postgresql'
					) {
						if (
							name === 'redis' &&
							res.data.version.split('.')[0] !== '6'
						) {
							setCanDisaster(false);
						} else if (
							name === 'mysql' &&
							res.data.mode === '1m-0s'
						) {
							setCanDisaster(false);
						} else if (
							name === 'mysql' &&
							res.data.readWriteProxy?.enabled === true
						) {
							setCanDisaster(false);
						} else if (
							name === 'elasticsearch' &&
							res.data.version.split('.')[0] !== '6'
						) {
							setCanDisaster(false);
						} else {
							setCanDisaster(true);
						}
					} else {
						setCanDisaster(false);
					}
					if (res.data.dynamicValues) {
						setCustomMid(true);
					} else {
						setCustomMid(false);
					}
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			})
			.finally(() => {
				setSpinning(false);
			});
	};

	const refresh = (key = activeKey) => {
		getData();
		setActiveKey(key);
	};

	const childrenRender = (key: string) => {
		if (data) {
			switch (key) {
				case 'basicInfo':
					return (
						<BasicInfo
							type={name}
							data={data}
							customMid={customMid}
							onRefresh={refresh}
							toDetail={toDetail}
							// operateFlag={operateFlag}
						/>
					);
				case 'highAvailability':
					return (
						<HighAvailability
							data={data}
							onRefresh={refresh}
							customMid={customMid}
							// operateFlag={operateFlag}
						/>
					);
				case 'operatorAbility':
					return (
						<OperatorAbility
							onRefresh={refresh}
							data={data}
							subMenu={subMenu('ops')}
							customMid={customMid}
						/>
					);
				case 'backupRecovery':
					return (
						<BackupRecovery
							lock={data.lock}
							customMid={customMid}
							capabilities={(data && data.capabilities) || []}
						/>
					);
				case 'externalAccess':
					return (
						<ServiceDetailIngress
							customMid={customMid}
							capabilities={(data && data.capabilities) || []}
							mode={data.mode}
							imagePath={data.imagePath}
							status={data.status}
							data={data}
							// operateFlag={operateFlag}
						/>
					);
				case 'monitor':
					return (
						<Monitor
							grafanaOpen={grafanaOpen}
							customMid={customMid}
							capabilities={(data && data.capabilities) || []}
						/>
					);
				case 'log':
					return (
						<Log
							data={data}
							customMid={customMid}
							logging={loggingOpen}
							onRefresh={refresh}
							role={role}
							subMenu={subMenu('logDetail')}
							// operateFlag={operateFlag}
							capabilities={(data && data.capabilities) || []}
						/>
					);
				case 'paramterSetting':
					return (
						<ParamterSetting
							customMid={customMid}
							capabilities={(data && data.capabilities) || []}
							readWriteProxy={data && data.readWriteProxy}
							lock={data.lock}
							subMenu={subMenu('parameter')}
						/>
					);
				case 'alarm':
					return (
						<ServerAlarm
							middlewareName={middlewareName}
							clusterId={clusterId}
							namespace={namespace}
							type={type}
							customMid={customMid}
							capabilities={(data && data.capabilities) || []}
							alertOpen={alertOpen}
							alarmType={'service'}
							role={role}
							organId={organId}
							projectId={projectId}
							lock={data.lock}
							subMenu={subMenu('alarm')}
						/>
					);
				case 'disaster':
					return name === 'mysql' ? (
						<Disaster
							data={data}
							onRefresh={refresh}
							toDetail={toDetail}
						/>
					) : (
						<RedisDisaster
							data={data}
							onRefresh={refresh}
							toDetail={toDetail}
						/>
					);
				case 'database':
					return (
						<DataBase
							chartName={name}
							chartVersion={chartVersion}
							middlewareName={middlewareName}
							clusterId={clusterId}
							namespace={namespace}
							data={data}
							onRefresh={refresh}
							toDetail={toDetail}
						/>
					);
				case 'redisDatabase':
					return (
						<RedisDataBase
							chartName={name}
							chartVersion={chartVersion}
							middlewareName={middlewareName}
							clusterId={clusterId}
							namespace={namespace}
							data={data}
							onRefresh={refresh}
							toDetail={toDetail}
						/>
					);
			}
		}
	};

	const statusRender = (value: string) => {
		switch (value) {
			case 'Running':
				return '运行正常';
			case 'Creating':
				return '启动中';
			case 'Recover':
				return '恢复中';
			case 'GracefulRestart':
				return '重启中';
			case 'Switching':
				return '切换中';
			case 'Upgrading':
				return '更新中';
			case undefined:
				return '';
			default:
				return '运行异常';
		}
	};
	const toDetail = () => {
		if (!data?.mysqlDTO.relationExist) {
			notification.error({
				message: '失败',
				description: '该关联实例不存在，无法进行跳转'
			});
			return;
		} else {
			// ! 这里的redis灾备跳转存在问题
			history.push({
				pathname: `/project/${type}/${name}/${aliasName}/container/basicInfo/${data?.mysqlDTO.relationName}/${chartVersion}/${data?.mysqlDTO.relationClusterId}/${data?.mysqlDTO.relationNamespace}`
			});
		}
	};

	const onChange = (key: string | number) => {
		setActiveKey(key as string);
		const pre = hasWebpage ? 'webpage' : 'project';
		history.push(
			`/${pre}/${type}/${name}/${aliasName}/container/${key}/${middlewareName}/${chartVersion}/${clusterId}/${namespace}`
		);
	};

	const toOperatorPanel = () => {
		window.open(
			`#/operationalPanel/sqlConsole/${organId}/${projectId}/${clusterId}/${namespace}/${name}/${middlewareName}/${data?.version}/${data?.mode}`,
			'_blank'
		);
	};
	return (
		<ProPage>
			<Spin spinning={spinning}>
				<ProHeader
					backIcon={!hasWebpage}
					title={
						status ? (
							<Space>
								<span>
									{aliasName}:{middlewareName}
								</span>
								<span>
									{data?.lock === 'locked' && (
										<LockOutlined />
									)}
								</span>
								<span>({statusRender(status)})</span>
							</Space>
						) : (
							<Space>
								<span>
									{aliasName}:{middlewareName}
								</span>
								( <LoadingOutlined /> )
							</Space>
						)
					}
					onBack={() =>
						history.push(`/project/${type}/${name}/${aliasName}`)
					}
					subTitle={
						data?.mysqlDTO?.openDisasterRecoveryMode &&
						data?.mysqlDTO?.isSource === false ? (
							<div
								className="gray-circle"
								style={{ marginTop: 9 }}
							>
								备
							</div>
						) : null
					}
					extra={
						<Space>
							{maintenanceDashboardAPI &&
								data &&
								status !== 'Creating' &&
								(name === 'mysql' ||
									name === 'redis' ||
									name === 'postgresql') && (
									<Button
										type="primary"
										disabled={controlledOperationDisabled(
											'expert',
											data?.lock
										)}
										onClick={() => {
											WorkOrderFuc(
												toOperatorPanel,
												data?.lock,
												middlewareName,
												intoMaintenanceOperatorId,
												history,
												type,
												name,
												aliasName,
												clusterId,
												namespace
											);
										}}
									>
										运维面板(beta)
									</Button>
								)}
							{data?.mysqlDTO?.openDisasterRecoveryMode &&
							data?.mysqlDTO?.isSource === false ? (
								<Button type="primary" onClick={toDetail}>
									返回源服务
								</Button>
							) : null}
							<Button
								onClick={() => refresh(activeKey)}
								id="detailRefresh"
								icon={<ReloadOutlined id="detailRefresh" />}
							/>
						</Space>
					}
				/>
				{reason &&
					reason !== 'unknow' &&
					status !== 'Running' &&
					status !== 'Creating' && (
						<Alert
							message={reason}
							type="warning"
							showIcon
							closable
							style={{ margin: '0 24px' }}
						/>
					)}
				<ProContent>
					<Tabs
						activeKey={activeKey}
						onChange={onChange}
						destroyInactiveTabPane
					>
						{showMenu('baseInfo') && (
							<TabPane tab="基本信息" key="basicInfo">
								{childrenRender('basicInfo')}
							</TabPane>
						)}
						{showMenu('InstanceDetail') && (
							<TabPane tab="实例详情" key="highAvailability">
								{childrenRender('highAvailability')}
							</TabPane>
						)}
						{showMenu('ops') && (
							<TabPane tab="运维操作" key="operatorAbility">
								{childrenRender('operatorAbility')}
							</TabPane>
						)}
						{showMenu('dataSecurity') && (
							<TabPane tab="数据安全" key="backupRecovery">
								{childrenRender('backupRecovery')}
							</TabPane>
						)}
						{showMenu('serviceExposure') && (
							<TabPane tab="服务暴露" key="externalAccess">
								{childrenRender('externalAccess')}
							</TabPane>
						)}
						{showMenu('dataMonitor') && (
							<TabPane tab="数据监控" key="monitor">
								{childrenRender('monitor')}
							</TabPane>
						)}
						{showMenu('logDetail') && (
							<TabPane tab="日志详情" key="log">
								{childrenRender('log')}
							</TabPane>
						)}
						{showMenu('parameter') && (
							<TabPane tab="参数设置" key="paramterSetting">
								{childrenRender('paramterSetting')}
							</TabPane>
						)}
						{showMenu('alarm') && (
							<TabPane tab="服务告警" key="alarm">
								{childrenRender('alarm')}
							</TabPane>
						)}
						{showMenu('disasterBackup') &&
							disasterOpen &&
							canDisaster && (
								<TabPane tab="灾备服务" key="disaster">
									{childrenRender('disaster')}
								</TabPane>
							)}
					</Tabs>
				</ProContent>
			</Spin>
		</ProPage>
	);
};
export default connect((state: StoreState) => ({
	buttonList: state.auth.buttonList
}))(InstanceDetails);

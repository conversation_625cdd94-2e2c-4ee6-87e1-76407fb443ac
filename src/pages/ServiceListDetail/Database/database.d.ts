export interface FormProps {
	visible: boolean;
	onCreate: () => void;
	onCancel: () => void;
	data: any | undefined | null;
	clusterId: string;
	namespace: string;
	middlewareName: string;
	charsetList?: any;
}
export interface ManageProps {
	type: string;
	data: {
		type: string;
		middlewareName: string;
		clusterId: string;
		namespace: string;
		stdoutEnabled?: boolean;
		filelogEnabled?: boolean;
		audit?: boolean;
		slowSql?: boolean;
		data?: any;
	};
	onRefresh?: () => void;
}

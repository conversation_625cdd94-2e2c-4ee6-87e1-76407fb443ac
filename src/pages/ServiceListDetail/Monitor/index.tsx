import React, { useEffect, useState } from 'react';
import { Select, notification } from 'antd';
import { getMiddlewareMonitorUrl } from '@/services/middleware';
import DefaultPicture from '@/components/DefaultPicture';
import ComponentNull from '@/components/ComponentsNull';

import svg from '@/assets/images/grafana_icon.svg';
import { DetailParams, MonitorProps } from '../detail';
import './monitor.less';
import { useParams } from 'react-router';
import useRefresh from '@/utils/useRefresh';

interface MonitorItem {
	title: string;
	url: string;
	authorization: string | null;
	uid: string;
}
const Monitor = (props: MonitorProps) => {
	const { grafanaOpen, customMid, capabilities } = props;
	const {
		clusterId,
		namespace,
		name,
		middlewareName,
		chartVersion
	}: DetailParams = useParams();
	const [menuHide, setMenuHide] = useState(true);
	const [notAuth, setNotAuth] = useState<boolean>(false);
	const [monitorUrls, setMonitorUrls] = useState<MonitorItem[]>([]);
	const [currentMonitorUrl, setCurrentMonitorUrl] = useState<MonitorItem>();
	const [loading, setLoading] = useState<boolean>(false);
	const [refreshKey, setRefreshKey] = useState<number>(0);
	const isFrame = window !== window.parent;

	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		if (!customMid || capabilities.includes('monitoring')) {
			if (grafanaOpen) {
				if (chartVersion && clusterId && namespace) {
					setLoading(true);
					getMiddlewareMonitorUrl({
						clusterId,
						namespace,
						middlewareName,
						type: name,
						chartVersion,
						deployMod: 'container'
					})
						.then((res) => {
							if (res.success) {
								setMonitorUrls(res.data);
								const _data = res.data[0];
								if (isFrame) {
									if (_data.url) {
										const _url = new URL(_data.url);
										setCurrentMonitorUrl({
											..._data,
											url:
												'/grafana' +
												_url.pathname +
												_url.search
										});
									}
								} else {
									setCurrentMonitorUrl(_data);
								}
								console.log('data :>> ', _data);
								setNotAuth(false);
							} else {
								notification.error({
									message: '失败',
									description: res.errorMsg
								});
								if (
									`${res.code}${res.errorMsg}` ===
									'400007无权限'
								) {
									setNotAuth(true);
								}
								setCurrentMonitorUrl(undefined);
							}
						})
						.finally(() => {
							setLoading(false);
						});
				}
			}
		}
	}, [grafanaOpen, refreshKey]);

	useEffect(() => {
		if (currentMonitorUrl) {
			const iframe: any = document.getElementById('iframe');
			iframe.onload = function () {
				iframe.contentWindow.postMessage(
					{ showMenu: false },
					encodeURI(new URL(currentMonitorUrl.url).origin)
				);
			};
		}
	}, [currentMonitorUrl]);

	useEffect(() => {
		// 子页面去掉侧边栏之后再显示iframe
		window.addEventListener(
			'message',
			function (event) {
				if (event.data.menuHide) {
					setMenuHide(true);
				}
			},
			false
		);
	});

	if (customMid && !capabilities.includes('monitoring')) {
		return <DefaultPicture />;
	}
	if (!grafanaOpen) {
		return (
			<ComponentNull title="该功能所需要数据监控和监控面板工具支持，您可前往“集群——>平台组件进行安装" />
		);
	}
	if (notAuth) {
		return (
			<DefaultPicture title="当前用户无该操作权限，请联系项目管理员分配权限" />
		);
	}

	return (
		<div className="monitor">
			<div className="mb-8">
				<Select
					loading={loading}
					placeholder="请选择监控面板"
					style={{ width: 250 }}
					value={currentMonitorUrl?.uid}
					onChange={(value: string) => {
						setCurrentMonitorUrl(
							monitorUrls.find((i) => i.uid === value)
						);
					}}
				>
					{monitorUrls.map((item) => {
						return (
							<Select.Option key={item.uid} value={item.uid}>
								{item.title}
							</Select.Option>
						);
					})}
				</Select>
			</div>
			<div
				style={{
					height: 'calc(100vh - 83px)',
					visibility: menuHide ? 'visible' : 'hidden'
				}}
			>
				{currentMonitorUrl && (
					<iframe
						id="iframe"
						src={currentMonitorUrl?.url || ''}
						frameBorder="no"
						// border="0"
						scrolling="no"
						style={{
							width: '100%',
							height: '100%',
							background: '#FFF'
						}}
						// allow="geolocation 'self' http://localhost:3000 http://************:32398"
						sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
					></iframe>
				)}
			</div>
			<div
				className="loading-gif"
				style={{
					visibility: menuHide ? 'hidden' : 'visible'
				}}
			>
				<div className="loading-icon">
					<img src={svg} width="60" />
				</div>
			</div>
		</div>
	);
};

export default Monitor;

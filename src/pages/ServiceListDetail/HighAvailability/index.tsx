import React, { useState, useEffect } from 'react';
import { Modal, notification, But<PERSON>, Tooltip } from 'antd';
import { CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons';
import { useHistory, useParams } from 'react-router';
import Actions from '@/components/Actions';
import ProTable from '@/components/ProTable';
import Visualization from './visualization';
import DefaultPicture from '@/components/DefaultPicture';
import './esEdit.less';
import {
	getPods,
	restartPods,
	rebootService,
	getMasterName,
	forceDeletePod,
	stopgracefully,
	resumegracefully
} from '@/services/middleware';
import transTime from '@/utils/transTime';
import { DetailParams, HighProps, PodItem, PodSendData } from '../detail';
import { controlledOperationDisabled, formatNumber } from '@/utils/utils';
import './index.less';
import useRefresh from '@/utils/useRefresh';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';
const { confirm } = Modal;
const LinkButton = Actions.LinkButton;
export default function HighAvailability(props: HighProps): JSX.Element {
	const { data, onRefresh, customMid } = props;
	const params: DetailParams = useParams();
	const history = useHistory();
	const { clusterId, namespace, middlewareName, name, type, aliasName } =
		params;
	const [pods, setPods] = useState<PodItem[]>([]);
	const [topoData, setTopoData] = useState();
	const [refreshKey, setRefreshKey] = useState<number>(0);
	const [stopServiceFlag, setIsStopServiceFlag] = useState<boolean>(false);
	const restartServiceOperatorId = maintenances['Restart Service'];
	const restartPodOperatorId = maintenances['Restart Instance'];
	const redisInstanceDownOperatorId = maintenances['【Redis】Instance Down'];

	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		const sendData: PodSendData = {
			clusterId,
			namespace,
			middlewareName,
			type: name,
			deployMod: 'container'
		};
		getPodList(sendData);
	}, [refreshKey]);
	// * 获取pod列表
	const getPodList = (sendData: PodSendData) => {
		setIsStopServiceFlag(false);
		getPods(sendData).then((res) => {
			if (res.success) {
				const list: any = [];
				res.data &&
					res.data.podInfoGroup &&
					res.data.podInfoGroup.listChildGroup &&
					res.data.podInfoGroup.listChildGroup.forEach((el: any) => {
						if (el.role.includes('shard')) {
							list.push(
								...el.pods.map(
									(item: any, index: number, arr: any) => {
										if (index === 0) {
											return {
												...item,
												identify: el.role,
												span: arr.length
											};
										} else {
											return {
												...item,
												identify: el.role,
												span: 0
											};
										}
									}
								)
							);
						} else {
							list.push(
								...el.pods.map((item: any) => {
									return {
										...item,
										identify: el.role,
										span: 1
									};
								})
							);
						}
					});
				list.forEach((item: any, index: number) => {
					if (index % 10 === 0 && item.span === 0) item.span = 1;
					if (item.maintenance) {
						setIsStopServiceFlag(true);
					}
				});
				setPods(list);
				setTopoData(res.data);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};

	// * 重启节点
	const reStart = async (value: string, record: PodItem, index: number) => {
		let res;
		if (record.role === 'slave' && name === 'redis') {
			res = await getMasterName({
				clusterId,
				namespace,
				middlewareName,
				slaveName: record.podName,
				mode: data.mode
			});
		}
		const sendData = {
			clusterId,
			namespace,
			middlewareName,
			type: name,
			podName: record.podName
		};
		confirm({
			title: '重启确认',
			icon: null,
			content:
				name === 'redis' ? (
					<div className="pod-restart">
						<p className="restart-title">您当前选择的是</p>

						<p>{record.podName}</p>
						<p>
							<span className="label">运行状态：</span>
							{record.status}
						</p>
						<p>
							<span className="label">节点角色：</span>
							{roleRender(value, record, index)}
						</p>
						<p>
							<span className="label">绑定主节点：</span>
							{record.role === 'slave' ? res.data || '-' : '-'}
						</p>
						<div
							className={
								record.role !== 'master'
									? 'info-success'
									: 'info-error'
							}
						>
							{record.role !== 'master' ? (
								<CheckCircleFilled
									style={{
										color: '#52c41a',
										marginRight: 4
									}}
								/>
							) : (
								<CloseCircleFilled
									style={{
										color: '#ff4d4f',
										marginRight: 4
									}}
								/>
							)}
							{record.role === 'master' &&
								'当前选择的是主节点，直接重启可能会对服务产生影响。'}
							{record.role === 'slave' &&
								'当前选择的是从节点，可以直接重启节点且不会对服务产生影响。'}
							{record.role !== 'master' &&
								record.role !== 'slave' &&
								`当前选择的是${roleRender(
									value,
									record,
									index
								)}，可以直接重启节点且不会对服务产生影响。`}
						</div>
					</div>
				) : (
					'根据重启的节点角色不同，重启操作可能会导致服务中断，请谨慎操作'
				),
			onOk: async () => {
				await ExecuteOrderFuc();
				return restartPods(sendData).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '重启中, 3s 后获取数据'
						});
						setTimeout(function () {
							const sendData = {
								clusterId,
								namespace,
								middlewareName,
								type: name,
								deployMod: 'container'
							};
							getPodList(sendData);
						}, 3000);
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			}
		});
	};
	// * 强制重启节点
	const restartForce = (record: PodItem) => {
		confirm({
			title: '操作确认',
			content: '强制重启实例会导致服务异常，是否确定强制重启实例？',
			onOk: () => {
				return forceDeletePod({
					clusterId,
					namespace,
					middlewareName: data.name,
					podName: record.podName
				}).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '强制重启中，3s 后获取数据'
						});
						setTimeout(function () {
							const sendData = {
								clusterId,
								namespace,
								middlewareName: data.name,
								type: data.type
							};
							getPodList(sendData);
						}, 3000);
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};
	const stopServiceOperate = async (param: any) => {
		const sendData = {
			clusterId,
			namespace,
			middlewareName: data.name,
			type: data.type,
			podName: param.podName
		};
		await ExecuteOrderFuc();
		stopgracefully(sendData).then((res) => {
			if (res.success) {
				notification.success({
					message: '成功',
					description: '停机成功'
				});
				setTimeout(function () {
					const sendData = {
						clusterId,
						namespace,
						middlewareName: data.name,
						type: data.type
					};
					getPodList(sendData);
				}, 3000);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const resumegracefullyOperate = (param: any) => {
		const sendData = {
			clusterId,
			namespace,
			middlewareName: data.name,
			type: data.type,
			podName: param.podName
		};
		resumegracefully(sendData).then((res) => {
			if (res.success) {
				notification.success({
					message: '成功',
					description: '恢复停机成功'
				});
				setTimeout(function () {
					const sendData = {
						clusterId,
						namespace,
						middlewareName: data.name,
						type: data.type
					};
					getPodList(sendData);
				}, 3000);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	//停机
	const stopService = (record?: any) => {
		confirm({
			title: '操作确认',
			content: (
				<>
					{data.type === 'postgresql' ? (
						<p>
							停机后，该服务会进入维护模式，如果此时出现集群故障，需要人工介入处理，是否确定进行停机操作？
						</p>
					) : data.type === 'redis' ? (
						<p>
							停机后，该服务进入状态异常，请谨慎操作
							<br />
							是否确定进行停机操作？
						</p>
					) : data.type === 'elasticsearch' ? (
						<p>
							请确保服务中每个分片都有相应的副本，否则会导致数据丢失，请谨慎操作，是否确定进行停机操作？
						</p>
					) : (
						''
					)}
				</>
			),
			onOk: () => {
				//停机操作
				stopServiceOperate(record);
			}
		});
	};
	//停机恢复
	const recoveryShutDown = (record?: any) => {
		confirm({
			title: '操作确认',
			content:
				'停机恢复后，该节点将恢复正常，请确保相关主机已正常运行，是否确定进行停机恢复操作？',
			onOk: () => {
				//停机恢复
				resumegracefullyOperate(record);
			}
		});
	};
	const actionRender = (value: any, record: PodItem, index: number) => {
		return (
			<Actions>
				{data.type === 'redis' &&
					record.status === 'Terminating' &&
					record.podNodeStatus === 'False' && (
						<LinkButton
							disabled={controlledOperationDisabled(
								'maintenance',
								data.lock
							)}
							onClick={() => {
								WorkOrderFuc(
									() => {
										restartForce(record);
									},
									data.lock,
									middlewareName,
									restartPodOperatorId,
									history,
									type,
									name,
									aliasName,
									clusterId,
									namespace
								);
							}}
						>
							强制重启
						</LinkButton>
					)}
				{(data.type === 'redis' || data.type === 'postgresql') &&
					(record.maintenance ? (
						<LinkButton onClick={() => recoveryShutDown(record)}>
							停机恢复
						</LinkButton>
					) : record.status !== 'Running' ||
					  (data.type === 'redis' && record?.role === 'proxy') ? (
						<LinkButton disabled>停机</LinkButton>
					) : record.role === 'master' ? (
						<Tooltip title="主节点无法停机，请先进行主从切换后再进行停机操作！">
							<LinkButton disabled>停机</LinkButton>
						</Tooltip>
					) : (
						<LinkButton
							onClick={() => {
								WorkOrderFuc(
									() => {
										stopService(record);
									},
									data.lock,
									middlewareName,
									redisInstanceDownOperatorId,
									history,
									type,
									name,
									aliasName,
									clusterId,
									namespace
								);
							}}
						>
							停机
						</LinkButton>
					))}
				{data.type === 'elasticsearch' &&
				(record.podName.includes('kibana') ||
					record.podName.includes('exporter') ||
					record?.role === 'kibana' ||
					record?.role === 'exporter')
					? ''
					: data.type === 'elasticsearch' &&
					  (record.maintenance ? (
							<LinkButton
								onClick={() => recoveryShutDown(record)}
							>
								停机恢复
							</LinkButton>
					  ) : record.status !== 'Running' ||
					    (!record.podName.includes('client') &&
								!record.podName.includes('master') &&
								!record.podName.includes('data') &&
								!record.podName.includes('cold')) ? (
							<LinkButton disabled>停机</LinkButton>
					  ) : stopServiceFlag ? (
							<Tooltip title="当前仅支持停机一个节点，请恢复已停机节点后重试！">
								<LinkButton disabled>停机</LinkButton>
							</Tooltip>
					  ) : (
							<LinkButton
								onClick={() => {
									stopService(record);
								}}
							>
								停机
							</LinkButton>
					  ))}
				{!(
					data.type === 'redis' &&
					record.status === 'Terminating' &&
					record.podNodeStatus === 'False'
				) && (
					<LinkButton
						onClick={() => {
							WorkOrderFuc(
								() => {
									reStart(record.role, record, index);
								},
								data.lock,
								middlewareName,
								restartPodOperatorId,
								history,
								type,
								name,
								aliasName,
								clusterId,
								namespace
							);
						}}
						disabled={
							// !operateFlag ||
							controlledOperationDisabled(
								'maintenance',
								data.lock
							)
						}
					>
						重启
					</LinkButton>
				)}
			</Actions>
		);
	};

	const restartRender = (value: number, record: PodItem, index: number) => {
		return `${value}(${
			transTime.gmt2local(record.lastRestartTime) || '无'
		})`;
	};

	const roleRender = (value: string, record: PodItem, index: number) => {
		if (record.podName.includes('exporter')) {
			return 'Exporter';
		} else {
			if (name === 'elasticsearch') {
				if (record.podName.includes('kibana')) {
					return 'Kibana';
				} else if (record.podName.includes('client')) {
					return '协调节点';
				} else if (record.podName.includes('master')) {
					return '主节点';
				} else if (record.podName.includes('data')) {
					return '数据节点';
				} else if (record.podName.includes('cold')) {
					return '冷节点';
				} else {
					return value
						? value.substring(0, 1).toUpperCase() +
								value.substring(1)
						: '/';
				}
			} else {
				switch (value) {
					case 'master':
						return '主节点';
					case 'slave':
						return name === 'mysql' || name === 'postgresql'
							? '异步节点'
							: '从节点';
					case 'data':
						return '数据节点';
					case 'client':
						return '协调节点';
					case 'cold':
						return '冷节点';
					case 'kibana':
						return 'Kibana';
					case 'nameserver':
						return 'Nameserver';
					case 'exporter':
						return 'Exporter';
					case 'sentinel':
						return '哨兵';
					case 'proxy':
						return '代理';
					case 'syncslave':
						return '同步节点';
					case 'sync_slave':
						return '同步节点';
					case 'default':
						return '/';
					default:
						if (value?.includes('shard')) {
							return '分片';
						} else {
							return value
								? value.substring(0, 1).toUpperCase() +
										value.substring(1)
								: '/';
						}
				}
			}
		}
	};

	const resourceRender = (value: string, record: PodItem, index: number) => {
		return `${formatNumber(record.resources.cpu)}C/${formatNumber(
			record.resources.memory
		)}G`;
	};

	const createTimeRender = (value: string) => {
		return transTime.gmt2local(value);
	};
	const podNameRender = (value: string) => {
		return (
			<div
				style={{ width: '150px', wordBreak: 'break-all' }}
				title={value}
			>
				{value}
			</div>
		);
	};
	const restartService = () => {
		confirm({
			title: '操作确认',
			content: '请确认是否重启服务？',
			onOk: async () => {
				const sendData = {
					clusterId,
					middlewareName,
					namespace,
					type: name
				};
				await ExecuteOrderFuc();
				return rebootService(sendData).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '服务重启成功！'
						});
						onRefresh();
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			}
		});
	};
	const Operation = {
		primary: (
			<div className="title-content">
				<div className="blue-line"></div>
				<div className="detail-title">实例列表</div>
			</div>
		),
		secondary: (
			<Button
				type="primary"
				onClick={() => {
					WorkOrderFuc(
						restartService,
						data.lock,
						middlewareName,
						restartServiceOperatorId,
						history,
						type,
						name,
						aliasName,
						clusterId,
						namespace
					);
				}}
				disabled={
					// !operateFlag ||
					controlledOperationDisabled('maintenance', data.lock)
				}
			>
				重启服务
			</Button>
		)
	};
	const nodeNameRender = (value: string, record: PodItem) => {
		return (
			<span>
				{value}
				{record.nodeZone ? `(${record.nodeZone})` : ''}
			</span>
		);
	};
	const statusRender = (value: string, record: PodItem) => {
		return record.maintenance ? 'Maintaining' : value;
	};
	if (customMid && !(data.capabilities || []).includes('high')) {
		return <DefaultPicture />;
	}
	return (
		<div>
			<ProTable
				dataSource={pods}
				rowKey="podName"
				operation={Operation}
				scroll={{ x: 1490 }}
			>
				{name === 'redis' && (
					<ProTable.Column
						title=""
						dataIndex="identify"
						render={roleRender}
						onCell={(_: any) => {
							return {
								rowSpan: _.span
							};
						}}
						width={100}
						fixed="left"
					/>
				)}
				<ProTable.Column
					title="实例名称"
					dataIndex="podName"
					render={podNameRender}
					width={170}
					fixed="left"
				/>
				<ProTable.Column
					title="状态"
					dataIndex="status"
					width={120}
					render={statusRender}
				/>
				<ProTable.Column
					title="实例 IP"
					dataIndex="podIp"
					width={150}
				/>
				<ProTable.Column
					title="所在主机"
					dataIndex="nodeName"
					width={220}
					render={nodeNameRender}
				/>
				<ProTable.Column
					title="节点类型"
					dataIndex="role"
					render={roleRender}
					width={100}
				/>
				<ProTable.Column
					title="节点资源"
					dataIndex="resource"
					render={resourceRender}
					width={100}
				/>
				<ProTable.Column
					title="创建时间"
					dataIndex="createTime"
					render={createTimeRender}
					width={160}
				/>
				<ProTable.Column
					title="异常重启次数(最近时间)"
					dataIndex="restartCount"
					render={restartRender}
					width={180}
				/>
				<ProTable.Column
					title="操作"
					render={actionRender}
					width={120}
				/>
			</ProTable>
			<div style={{ marginBottom: '24px' }}></div>
			{topoData && (
				<>
					<Visualization
						topoData={topoData}
						serverData={data}
						reStart={reStart}
					/>
					<div style={{ height: '24px' }} />
				</>
			)}
		</div>
	);
}

.spec-config {
	padding: 8px 0 16px 0;
	.instance-spec-content {
		flex-direction: column;
		.spec-custom {
			width: 562px;
			margin-top: 8px;
			padding: 12px 44px 0 30px;
			background: #efefef;
		}
	}
}
.es-mode {
	flex-direction: column;
	.mode-content {
		margin-top: 8px;
		.node-box {
			width: 182px;
			border-top: 1px solid #b2d4ef;
			border-right: 1px solid #b2d4ef;
			border-bottom: 1px solid #b2d4ef;
			.node-type {
				width: 100%;
				height: 30px;
				border-bottom: 1px solid #b2d4ef;
				text-align: center;
				line-height: 30px;
				.circle {
					padding: 0 4px;
					background-color: #c3d6f0;
					border-radius: @border-radius-lg * 2;
				}
			}
			.node-data {
				width: 100%;
				height: 138px;
				padding: 16px;
				position: relative;
				.btn {
					position: absolute;
					right: 0;
					bottom: 0;
				}
				.not-start {
					color: @text-color-title;
				}
			}
		}
		.node-box:first-child {
			border-left: 1px solid #b2d4ef;
		}
		.node-box.disabled {
			background-color: #efefef;
		}
	}
}
.visualization {
	position: relative;
	height: 600px;
	h2 {
		font-size: @font-2;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: @font-weight;
		color: @text-color-title;
		line-height: 22px;
		margin: 16px 0;
		display: flex;
		align-items: center;
		&::before {
			content: '';
			display: inline-block;
			width: 1px;
			height: 14px;
			border: 1px solid @primary-color;
			margin-right: 5px;
		}
	}
	.tools {
		height: 80px;
		padding: 10px 30px;
		box-sizing: border-box;
		background-color: #f9f9f9;
		display: flex;
		align-items: center;
		.rotate {
			transform: rotate(-90deg);
		}
		button {
			min-width: 24px !important;
			width: 24px;
			height: 24px;
			padding: 0;
			color: #666;
			&:not(:last-child) {
				border-right: none;
			}
			&:hover {
				color: @white;
				background: #0064c8;
				svg {
					color: @white !important;
				}
			}
			&:active {
				color: @white;
				background: #0064c8;
			}
		}
	}
	.legend {
		padding: 0 32px;
		display: flex;
		p {
			display: flex;
			align-items: center;
			margin-right: 16px;
			img {
				width: 12px;
				height: 12px;
				border-radius: 50%;
				margin-right: 3px;
			}
		}
	}
}
.pod-restart {
	.restart-title {
		color: @black-4;
		margin: 12px 0;
	}
	p {
		line-height: @line-height-2;
	}
	.label {
		display: inline-block;
		width: 80px;
	}
	.info-success {
		margin-top: 12px;
		padding: 8px 12px 24px;
		border: 1px solid @black;
		background-color: @green-1;
		border-color: @green-5;
	}
	.info-error {
		margin-top: 12px;
		padding: 8px 12px 24px;
		border: 1px solid @black;
		background-color: @red-1;
		border-color: @red-5;
	}
}

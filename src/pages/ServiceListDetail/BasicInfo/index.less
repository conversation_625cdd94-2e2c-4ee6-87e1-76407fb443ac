.basic-info.detail {
	width: 100%;
	.ant-timeline {
		margin-top: 16px;
		padding-left: 24px;
		.ant-timeline-item-content{
			margin: 0 0 0 42px;
		}
	}
}

.rocketmq-acl-field-content {
	.rocketmq-acl-field-title {
		display: flex;
		justify-content: space-between;
		margin-bottom: 22px;
		.rocketmq-acl-left {
			display: flex;
			align-items: center;
			font-size: @font-2;
			.rocketmq-acl-blue-line {
				width: 1px;
				height: 14px;
				border: 1px solid @primary-color;
				margin-right: 8px;
			}
		}
	}
}
.rocketmq-no-acl {
	text-align: center;
	color: #666666;
}
.none-flex{
	.ant-descriptions-item-container{
		display: block;
	}
}
@media screen and (max-width: 1920px) {
	.annotation-content {
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		width: 80%;
	}
}
@media screen and (max-width: 1840px) {
	.annotation-content {
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		width: 70%;
	}
}
@media screen and (max-width: 1650px) {
	.annotation-content {
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		width: 50%;
	}
}
@media screen and (max-width: 1340px) {
	.annotation-content {
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		width: 40%;
	}
}

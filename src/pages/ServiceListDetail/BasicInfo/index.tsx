import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Modal, notification, Form, Input, Popconfirm } from 'antd';
import { EditOutlined } from '@ant-design/icons';
import DataFields from '@/components/DataFields';
import DefaultPicture from '@/components/DefaultPicture';

import DirectoryItem from '@/components/DirectoryItem';
import DirectoryDetail from '@/components/DirectoryCard/detail';
import EventsList from './eventsList';

import { updateMiddleware } from '@/services/middleware';

import { objectRemoveDuplicatesByKey, statusRender } from '@/utils/utils';
import transTime from '@/utils/transTime';
import {
	BasicInfoProps,
	configParams,
	DetailParams,
	DynamicDataParams,
	InfoParams,
	runParams
} from '../detail';

import storage from '@/utils/storage';
import './index.less';

const FormItem = Form.Item;
const { confirm } = Modal;
const info: InfoParams = {
	title: '规格配置',
	name: '',
	aliasName: '',
	label: '',
	hostAffinity: '',
	hostAnti: '',
	chartVersion: '',
	description: '',
	annotations: '',
	tolerations: '',
	mirror: '',
	scheduler: null
};

const InfoConfig = [
	{
		dataIndex: 'title',
		key: 'title',
		render: (val: string) => (
			<div className="title-content">
				<div className="blue-line"></div>
				<div className="detail-title">{val}</div>
			</div>
		),
		span: 24
	},
	{
		dataIndex: 'name',
		key: 'name',
		label: '服务名称'
	},
	{
		dataIndex: 'aliasName',
		key: 'aliasName',
		label: '显示名称',
		render: (val: string) => (
			<div className="text-overflow-one" title={val}>
				{val}
			</div>
		)
	},
	{
		dataIndex: 'label',
		key: 'label',
		label: '标签',
		render: (val: string) => (
			<div className="text-overflow-one" title={val}>
				{val}
			</div>
		)
	},
	{
		dataIndex: 'annotations',
		key: 'annotations',
		label: '注解',
		render: (val: string) => (
			<div className="text-overflow-one" title={val}>
				{val}
			</div>
		)
	},
	{
		dataIndex: 'hostAffinity',
		key: 'hostAffinity',
		label: '主机亲和',
		render: (val: string) => (
			<div className="text-overflow-one" title={val}>
				{val}
			</div>
		)
	},
	{
		dataIndex: 'hostAnti',
		key: 'hostAnti',
		label: '主机反亲和',
		render: (val: string) => (
			<div className="text-overflow-one" title={val}>
				{val}
			</div>
		)
	},
	{
		dataIndex: 'tolerations',
		key: 'tolerations',
		label: '主机容忍',
		render: (val: string) => (
			<div className="text-overflow-one" title={val}>
				{val}
			</div>
		)
	}
];

const config: configParams = {
	title: '配置信息',
	version: '',
	characterSet: '',
	port: 0,
	autoCreateTopicEnable: false,
	mirror: '',
	containerUID: '',
	containerGID: ''
};

const runStatus: runParams = {
	title: '运行状态',
	status: '',
	createTime: '',
	model: '',
	namespace: '',
	storageClassName: '',
	storageType: '',
	hostNetwork: '',
	group: 0,
	replicas: 0
};

const modelMap: any = {
	MasterSlave: '一主一从',
	'1m-1s': '一主一从',
	'1m-2s': '一主二从',
	'1m-3s': '一主三从',
	'1m-ns': '一主多从',
	'1m-0s': '单实例',
	simple: 'N主',
	complex: 'N主N数据N协调',
	'complex-cold': 'N主N数据N冷',
	'cold-complex': 'N主N数据N冷N协调',
	regular: 'N主N数据',
	sentinel: '哨兵',
	'2m-noslave': '双主',
	'2m-2s': '两主两从',
	'3m-3s': '三主三从',
	2: '单分片',
	4: '二分片',
	6: '三分片',
	8: '四分片',
	10: '五分片',
	12: '六分片',
	14: '七分片',
	16: '八分片',
	18: '九分片',
	20: '十分片',
	dledger: 'DLedger模式',
	cluster: '集群模式'
};
const titleConfig = {
	dataIndex: 'title',
	key: 'title',
	render: (val: string) => (
		<div className="title-content">
			<div className="blue-line"></div>
			<div className="detail-title">{val}</div>
		</div>
	),
	span: 24
};
const versionConfig = {
	dataIndex: 'version',
	key: 'version',
	label: '版本'
};
const healthConfig = {
	dataIndex: 'status',
	key: 'status',
	label: '健康状态',
	render: (val: string) => statusRender(val)
};
const createTimeConfig = {
	dataIndex: 'createTime',
	key: 'createTime',
	label: '创建时间',
	render: (val: string) => transTime.gmt2local(val)
};
const modelConfig = {
	dataIndex: 'model',
	key: 'model',
	label: '模式'
};
const replicasConfig = {
	dataIndex: 'replicas',
	key: 'replicas',
	label: '副本数',
	render: (val: number) => val || '--'
};
const groupConfig = {
	dataIndex: 'group',
	key: 'group',
	label: 'DLedger组数',
	render: (val: number) => val || '--'
};
const namespaceConfig = {
	dataIndex: 'namespaceAliasName',
	key: 'namespaceAliasName',
	label: '所在分区',
	render: (val: string, dataSource: any) => {
		return (
			<div
				className="text-overflow-one"
				title={`${val || dataSource.namespace}(${
					dataSource.namespace
				})`}
			>
				{`${val || dataSource.namespace}(${dataSource.namespace})`}
			</div>
		);
	}
};
const storageClassNameConfig = {
	dataIndex: 'storageClassName',
	key: 'storageClassName',
	label: '存储名称',
	render: (val: string, dataSource: any) =>
		`${val || '/'}(${dataSource.storageType || '/'})`
};
const zookeeperConfig = {
	dataIndex: 'kafkaDTO',
	key: 'kafkaDTO',
	label: 'Zookeeper配置',
	render: (val: any) => (
		<div>{val ? val?.zkAddress + ':' + val?.zkPort + val?.path : '/'}</div>
	)
};
function BasicInfo(props: BasicInfoProps): JSX.Element {
	const { type, data, customMid, onRefresh, toDetail } = props;
	const [form] = Form.useForm();
	const params: DetailParams = useParams();
	const { clusterId, namespace, middlewareName } = params;
	// * 规格配置
	const [basicData, setBasicData] = useState(info);
	const [infoConfig, setInfoConfig] = useState(InfoConfig);
	// * 运行状态
	const [runData, setRunData] = useState<any>(runStatus);
	const [runConfig, setRunConfig] = useState(
		type !== 'rocketmq'
			? [
					titleConfig,
					healthConfig,
					createTimeConfig,
					modelConfig,
					namespaceConfig,
					storageClassNameConfig
			  ]
			: data.mode === 'dledger'
			? [
					titleConfig,
					healthConfig,
					createTimeConfig,
					modelConfig,
					groupConfig,
					replicasConfig,
					namespaceConfig,
					storageClassNameConfig
			  ]
			: [
					titleConfig,
					healthConfig,
					createTimeConfig,
					modelConfig,
					namespaceConfig,
					storageClassNameConfig
			  ]
	);
	// * 分盘信息
	const [directoryConfig] = useState({
		dataIndex: 'customVolumes',
		label: '挂载目录查看',
		className: 'none-flex',
		labelStyle: { display: 'block', width: '100%', marginBottom: 16 },
		render: (value: any) =>
			value ? (
				type === 'elasticsearch' ? (
					<div
						style={{
							display: 'flex',
							flexWrap: 'wrap',
							alignItems: 'flex-start'
						}}
					>
						{Object.keys(value).map((key) => (
							<DirectoryDetail key={key} data={value[key]} />
						))}
					</div>
				) : (
					<div style={{ display: 'flex' }}>
						{Object.keys({
							pgdb: value.pgdb || {},
							pgwal: value.pgwal || {},
							pglog: value.pglog || {},
							pgarch: value.pgarch || {},
							pgextension: value.pgextension || {},
							'redis-data': value['redis-data'] || {},
							'redis-logs': value['redis-logs'] || {}
						}).map(
							(key) =>
								JSON.stringify(
									{
										pgdb: value.pgdb || {},
										pgwal: value.pgwal || {},
										pglog: value.pglog || {},
										pgarch: value.pgarch || {},
										pgextension: value.pgextension || {},
										'redis-data': value['redis-data'] || {},
										'redis-logs': value['redis-logs'] || {}
									}[key]
								) !== '{}' && (
									<DirectoryItem
										key={key}
										type={key}
										data={value[key]}
										middlewareType={type}
										clusterId={clusterId}
										namespace={namespace}
										disabled
										readOnly
										onChange={() => false}
									/>
								)
						)}
					</div>
				)
			) : (
				<div></div>
			)
	});
	const [configData, setConfigData] = useState<any>(config);
	const [configConfig, setConfigConfig] = useState([
		titleConfig,
		versionConfig
	]);
	// * 动态表单相关
	const [dynamicData, setDynamicData] = useState<DynamicDataParams>({
		title: '其他'
	});
	const [dynamicConfig, setDynamicConfig] = useState<any[]>([titleConfig]);
	// * feature 智能调度是否打开
	const [extendSchedulerAPI] = useState<boolean>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'extendScheduler')?.enabled ??
			true
	);
	// * feature 主机网络是否打开
	const [hostNetWorkAPI] = useState<boolean>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'hostNetWork')?.enabled ?? true
	);
	// * feature 主机分盘是否打开
	const [dataPartitionAPI] = useState<any>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'dataPartition')?.enabled ??
			true
	);
	const disasterInstanceConfig = {
		dataIndex: 'disasterInstanceName',
		key: 'disasterInstanceName',
		label: '备份服务名称',
		render: (val: string) => {
			return (
				<span
					className="name-link"
					onClick={() => {
						if (clusterId !== data.mysqlDTO.relationClusterId) {
							confirm({
								title: '提醒',
								content:
									'该备用服务不在当前集群命名空间，返回源服务页面请点击右上角“返回源服务”按钮',
								onOk: () => {
									toDetail();
								}
							});
						} else {
							toDetail();
						}
					}}
				>
					{val}
				</span>
			);
		}
	};
	const originInstanceConfig = {
		dataIndex: 'disasterInstanceName',
		key: 'disasterInstanceName',
		label: '源服务名称',
		render: (val: string) => {
			return (
				<span
					className="name-link"
					onClick={() => {
						toDetail();
					}}
				>
					{val}
				</span>
			);
		}
	};
	const editDescription = (value: any) => {
		const sendData = {
			clusterId: clusterId,
			namespace: namespace,
			middlewareName: middlewareName,
			chartName: type,
			chartVersion: params.chartVersion,
			type: type,
			description: value.description
		};
		updateMiddleware(sendData).then((res) => {
			if (res.success) {
				notification.success({
					message: '成功',
					description: '备注修改成功,5秒后刷新页面'
				});
				setTimeout(() => {
					onRefresh();
				}, 5000);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};

	const modeText = (data: any) => {
		if (!data.mode) return '/';
		if (data.type === 'redis' || data.type === 'mysql') {
			if (type === 'mysql') {
				if (data.mode === '1m-0s') {
					return '单实例模式';
				} else {
					return `${
						data.readWriteProxy?.enabled
							? '读写分离模式'
							: '高可用模式'
					}（${modelMap[data.mode]}）`;
				}
			} else {
				let text = '';
				if (data.mode === 'cluster' || data.mode === 'agent') {
					if (data.readWriteProxy?.enabled) {
						text = '集群代理模式';
					} else {
						text = '集群模式';
					}
				} else {
					if (data.readWriteProxy?.enabled) {
						text = '哨兵代理模式';
					} else {
						text = '哨兵模式';
					}
				}
				return `${text}（${modelMap[data.quota.redis.num]}）`;
			}
		} else {
			return modelMap[data.mode];
		}
	};

	useEffect(() => {
		if (data !== undefined) {
			if (data.mysqlDTO || data?.mysqlDTO?.openDisasterRecoveryMode) {
				setBasicData({
					title: '规格配置',
					name: data.name,
					aliasName: data.aliasName || '/',
					label: data.labels || '-',
					hostAffinity: `${
						(data.nodeAffinity &&
							data.nodeAffinity.length > 0 &&
							data.nodeAffinity
								.filter((item) => !item.anti)
								.map((item: any) => item.label)
								.join(';')) ||
						'无'
					}(${
						data.nodeAffinity &&
						data.nodeAffinity.length > 0 &&
						data.nodeAffinity.filter((item) => !item.anti)[0]
							?.required
							? '强制'
							: '非强制'
					})`,
					hostAnti: `${
						(data.nodeAffinity &&
							data.nodeAffinity.length > 0 &&
							data.nodeAffinity
								.filter((item) => item.anti)
								.map((item: any) => item.label)
								.join(';')) ||
						'无'
					}(${
						data.nodeAffinity &&
						data.nodeAffinity.length > 0 &&
						data.nodeAffinity.filter((item) => item.anti)[0]
							?.required
							? '强制'
							: '非强制'
					})`,
					chartVersion: data.chartVersion,
					disasterInstanceName: data.mysqlDTO.relationName || '/',
					annotations: data.annotations || '/',
					description: data.description || '无',
					tolerations: `${
						(data.tolerations && data.tolerations.join(',')) || '/'
					}`,
					scheduler: data.scheduler,
					mirror: data.mirrorImage || '/',
					hostNetwork:
						data.type === 'elasticsearch'
							? data?.esParam?.hostNetwork
							: data.type === 'postgresql' ||
							  data.type === 'redis'
							? data[data.type + 'Param'].hostNetwork
							: ''
				});
			} else {
				setBasicData({
					title: '规格配置',
					name: data.name,
					aliasName: data.aliasName || '/',
					label: data.labels || '/',
					hostAffinity: `${
						(data.nodeAffinity &&
							data.nodeAffinity.length > 0 &&
							data.nodeAffinity
								.filter((item) => !item.anti)
								.map((item: any) => item.label)
								.join(';')) ||
						'无'
					}(${
						data.nodeAffinity &&
						data.nodeAffinity.length > 0 &&
						data.nodeAffinity.filter((item) => !item.anti)[0]
							?.required
							? '强制'
							: '非强制'
					})`,
					hostAnti: `${
						(data.nodeAffinity &&
							data.nodeAffinity.length > 0 &&
							data.nodeAffinity
								.filter((item) => item.anti)
								.map((item: any) => item.label)
								.join(';')) ||
						'无'
					}(${
						data.nodeAffinity &&
						data.nodeAffinity.length > 0 &&
						data.nodeAffinity.filter((item) => item.anti)[0]
							?.required
							? '强制'
							: '非强制'
					})`,
					chartVersion: data.chartVersion,
					annotations: data.annotations || '/',
					tolerations: `${
						(data.tolerations && data.tolerations.join(',')) || '/'
					}`,
					description: data.description || '无',
					scheduler: data.scheduler,
					hostNetwork:
						data.type === 'elasticsearch'
							? data?.esParam?.hostNetwork
							: data.type === 'postgresql' ||
							  data.type === 'redis'
							? data[data.type + 'Param'].hostNetwork
							: ''
				});
			}
			const resultObj: any = {};
			for (const key in data?.customVolumes) {
				const [name, type] = key.split('-');
				if (!resultObj[name]) {
					resultObj[name] = {
						name
					};
				}
				resultObj[name][type] = data?.customVolumes[key];
			}
			setConfigData({
				title: '配置信息',
				version: data.version ?? '/',
				characterSet: data.charSet ?? '/',
				port: data.port || '',
				mirror: data.mirrorImage || '/',
				containerUID: data.containerUID ?? '/',
				containerGID: data.containerGID ?? '/',
				kafkaDTO: data.kafkaDTO,
				autoCreateTopicEnable:
					data?.rocketMQParam?.autoCreateTopicEnable,
				customVolumes:
					type === 'elasticsearch' ? resultObj : data?.customVolumes,
				pgPort: data.postgresqlParam?.pgPort,
				apiPort: data.postgresqlParam?.apiPort,
				passwordEncryption: data.postgresqlParam?.passwordEncryption,
				exporterPort:
					data.type === 'postgresql' || data.type === 'redis'
						? data[data.type + 'Param'].exporterPort
						: data.type === 'elasticsearch'
						? data.esParam?.exporterPort
						: '',
				bgMonPort: data.postgresqlParam?.bgMonPort,
				httpPort: data.esParam?.httpPort,
				tcpPort: data.esParam?.tcpPort,
				kibanaPort: data.esParam?.kibanaPort,
				redisPort: data.redisParam?.redisPort,
				sentinelPort: data.redisParam?.sentinelPort,
				predixyPort: data.redisParam?.predixyPort,
				predixyExporterPort: data.redisParam?.predixyExporterPort,
				sentinelExporterPort: data.redisParam?.sentinelExporterPort,
				exporterTolerations:
					data.type === 'rocketmq'
						? data.rocketMQParam?.exporterTolerations?.join(',') ||
						  '/'
						: data.type === 'kafka'
						? data.kafkaDTO?.exporterTolerations?.join(',') || '/'
						: data.type === 'elasticsearch'
						? data.esParam?.exporterTolerations?.join(',') || '/'
						: '无',
				exporterNodeAffinity:
					data.type === 'rocketmq'
						? data.rocketMQParam?.exporterNodeAffinity
								?.map((item: any) => item.label)
								.join(';') || '无'
						: data.type === 'kafka'
						? data.kafkaDTO.exporterNodeAffinity
								?.map((item: any) => item.label)
								.join(';') || '无'
						: data.type === 'elasticsearch'
						? data.esParam?.exporterNodeAffinity
								?.map((item: any) => item.label)
								.join(';') || '无'
						: '无',
				managerTolerations:
					data.kafkaDTO?.managerTolerations?.join(',') || '/',
				managerNodeAffinity:
					data.kafkaDTO?.managerNodeAffinity
						?.map((item: any) => item.label)
						.join(';') || '无',
				kibanaTolerations:
					data.esParam?.kibanaTolerations?.join(',') || '/',
				kibanaNodeAffinity:
					data.esParam?.kibanaNodeAffinity
						?.map((item: any) => item.label)
						.join(';') || '无',
				consoleTolerations:
					data.rocketMQParam?.consoleTolerations?.join(',') || '/',
				consoleNodeAffinity:
					data.rocketMQParam?.consoleNodeAffinity
						?.map((item: any) => item.label)
						.join(';') || '无'
			});
			const storageClassName =
				data.type === 'elasticsearch'
					? data.quota?.master.storageClassAliasName || ''
					: data.quota?.[data.type].storageClassAliasName;
			const storageType =
				data.type === 'elasticsearch'
					? data.quota?.master.storageClassName || ''
					: data.quota?.[data.type].storageClassName;
			setRunData({
				title: '运行状态',
				status: data.status || 'Failed',
				createTime: data.createTime || '',
				model: modeText(data),
				namespace: data.namespace || '',
				namespaceAliasName: data.namespaceAliasName,
				storageClassName: storageClassName,
				storageType: storageType,
				hostNetwork: data?.hostNetwork,
				group: data.rocketMQParam?.group,
				replicas: data.rocketMQParam?.replicas,
				storageResource: data?.storageResource
			});
		}
	}, [data]);

	useEffect(() => {
		// * 动态表单 设置其他
		if (data !== undefined && data.dynamicValues) {
			const listConfig = [...dynamicConfig];
			const listData = { ...dynamicData };
			for (const index in data.dynamicValues) {
				listConfig.push({
					dataIndex: index,
					label: index
				});
				listData[index] = data.dynamicValues[index];
			}
			setDynamicConfig(listConfig);
			setDynamicData(listData);
		}
	}, []);

	useEffect(() => {
		const listConfigTemp = [...configConfig];
		const dataIndexList = listConfigTemp.map((item) => item.dataIndex);
		if (!dataIndexList.includes('mirror') && !data.dynamicValues) {
			const mirror = {
				dataIndex: 'mirror',
				key: 'mirror',
				label: '镜像仓库',
				render: (val: string) => (
					<div className="text-overflow-one" title={val}>
						{val}
					</div>
				)
			};
			listConfigTemp.push(mirror);
		}
		if (type === 'mysql') {
			if (!dataIndexList.includes('characterSet')) {
				listConfigTemp.push({
					dataIndex: 'characterSet',
					key: 'characterSet',
					label: '字符集'
				});
			}
			if (!dataIndexList.includes('port')) {
				listConfigTemp.push({
					dataIndex: 'port',
					key: 'port',
					label: '端口号'
				});
			}
		}
		if (!dataIndexList.includes('containerUID')) {
			listConfigTemp.push({
				dataIndex: 'containerUID',
				key: 'containerUID',
				label: '容器uid'
			});
		}
		if (!dataIndexList.includes('containerGID')) {
			listConfigTemp.push({
				dataIndex: 'containerGID',
				key: 'containerGID',
				label: '容器gid'
			});
		}
		if (type === 'postgresql') {
			if (!dataIndexList.includes('pgPort')) {
				listConfigTemp.push({
					dataIndex: 'pgPort',
					key: 'pgPort',
					label: 'PostgreSQL端口'
				});
			}
			if (!dataIndexList.includes('apiPort')) {
				listConfigTemp.push({
					dataIndex: 'apiPort',
					key: 'apiPort',
					label: '运维端口'
				});
			}
			if (!dataIndexList.includes('bgMonPort')) {
				listConfigTemp.push({
					dataIndex: 'bgMonPort',
					key: 'bgMonPort',
					label: '进程监控端口'
				});
			}
		}
		if (type === 'elasticsearch') {
			if (!dataIndexList.includes('httpPort')) {
				listConfigTemp.push({
					dataIndex: 'httpPort',
					key: 'httpPort',
					label: 'HTTP端口'
				});
			}
			if (!dataIndexList.includes('kibanaPort')) {
				listConfigTemp.push({
					dataIndex: 'kibanaPort',
					key: 'kibanaPort',
					label: 'Kibana端口'
				});
			}
			if (!dataIndexList.includes('tcpPort')) {
				listConfigTemp.push({
					dataIndex: 'tcpPort',
					key: 'tcpPort',
					label: 'TCP端口'
				});
			}
		}
		if (type === 'redis') {
			if (
				!dataIndexList.includes('sentinelPort') &&
				data.mode === 'sentinel'
			) {
				listConfigTemp.push({
					dataIndex: 'sentinelPort',
					key: 'sentinelPort',
					label: '哨兵节点端口'
				});
			}
			if (
				!dataIndexList.includes('sentinelExporterPort') &&
				data.mode === 'sentinel'
			) {
				listConfigTemp.push({
					dataIndex: 'sentinelExporterPort',
					key: 'sentinelExporterPort',
					label: '哨兵节点Exporter端口'
				});
			}
			if (
				!dataIndexList.includes('predixyPort') &&
				data.readWriteProxy?.enabled
			) {
				listConfigTemp.push({
					dataIndex: 'predixyPort',
					key: 'predixyPort',
					label: '代理节点端口'
				});
			}
			if (
				!dataIndexList.includes('predixyExporterPort') &&
				data.readWriteProxy?.enabled
			) {
				listConfigTemp.push({
					dataIndex: 'predixyExporterPort',
					key: 'predixyExporterPort',
					label: '代理节点Exporter端口'
				});
			}
			if (!dataIndexList.includes('redisPort')) {
				listConfigTemp.push({
					dataIndex: 'redisPort',
					key: 'redisPort',
					label: 'Redis端口'
				});
			}
		}
		if (
			type === 'redis' ||
			type === 'elasticsearch' ||
			type === 'postgresql'
		) {
			if (!dataIndexList.includes('exporterPort')) {
				listConfigTemp.push({
					dataIndex: 'exporterPort',
					key: 'exporterPort',
					label: 'Exporter端口',
					span: 2
				});
			}
		}
		if (type === 'rocketmq') {
			if (!dataIndexList.includes('autoCreateTopicEnable')) {
				listConfigTemp.push({
					dataIndex: 'autoCreateTopicEnable',
					key: 'autoCreateTopicEnable',
					label: '自动创建Topic',
					render: (value) => <span>{value ? '是' : '否'}</span>
				});
			}
		}
		if (
			type === 'elasticsearch' ||
			type === 'kafka' ||
			type === 'rocketmq'
		) {
			if (!dataIndexList.includes('exporterTolerations')) {
				listConfigTemp.push({
					dataIndex: 'exporterTolerations',
					key: 'exporterTolerations',
					label: '监控采集污点容忍'
				});
			}
			if (!dataIndexList.includes('exporterNodeAffinity')) {
				listConfigTemp.push({
					dataIndex: 'exporterNodeAffinity',
					key: 'exporterNodeAffinity',
					label: '监控采集节点亲和'
				});
			}
		}
		if (type === 'elasticsearch') {
			if (!dataIndexList.includes('kibanaTolerations')) {
				listConfigTemp.push({
					dataIndex: 'kibanaTolerations',
					key: 'kibanaTolerations',
					label: 'kibana污点容忍'
				});
			}
			if (!dataIndexList.includes('kibanaNodeAffinity')) {
				listConfigTemp.push({
					dataIndex: 'kibanaNodeAffinity',
					key: 'kibanaNodeAffinity',
					label: 'kibana节点亲和'
				});
			}
		}
		if (type === 'kafka') {
			if (!dataIndexList.includes('managerTolerations')) {
				listConfigTemp.push({
					dataIndex: 'managerTolerations',
					key: 'managerTolerations',
					label: '控制台污点容忍'
				});
			}
			if (!dataIndexList.includes('managerNodeAffinity')) {
				listConfigTemp.push({
					dataIndex: 'managerNodeAffinity',
					key: 'managerNodeAffinity',
					label: '控制台节点亲和'
				});
			}
		}
		if (type === 'rocketmq') {
			if (!dataIndexList.includes('consoleTolerations')) {
				listConfigTemp.push({
					dataIndex: 'consoleTolerations',
					key: 'consoleTolerations',
					label: '控制台污点容忍'
				});
			}
			if (!dataIndexList.includes('consoleNodeAffinity')) {
				listConfigTemp.push({
					dataIndex: 'consoleNodeAffinity',
					key: 'consoleNodeAffinity',
					label: '控制台节点亲和'
				});
			}
		}
		if (type === 'postgresql') {
			if (
				!dataIndexList.includes('passwordEncryption') &&
				data.postgresqlParam?.passwordEncryption
			) {
				listConfigTemp.push({
					dataIndex: 'passwordEncryption',
					key: 'passwordEncryption',
					label: '密码认证方式'
				});
			}
		}
		setConfigConfig(listConfigTemp);
	}, [props]);
	useEffect(() => {
		let listRunConfigTemp = [...runConfig];
		const dataIndexList = listRunConfigTemp.map((item) => item.dataIndex);
		if (dataIndexList.includes('storageType')) {
			if (type === 'elasticsearch') {
				listRunConfigTemp = listRunConfigTemp.filter(
					(item) => item.dataIndex !== 'storageType'
				);
			}
		}
		if (customMid) {
			if (
				dataIndexList.includes('model') &&
				dataIndexList.includes('storageType')
			) {
				listRunConfigTemp = listRunConfigTemp.filter(
					(item) =>
						item.dataIndex !== 'model' &&
						item.dataIndex !== 'storageType'
				);
			}
		}
		if (dataIndexList.includes('storageClassName')) {
			if (data.customVolumes) {
				listRunConfigTemp = listRunConfigTemp.filter(
					(item) => item.dataIndex !== 'storageClassName'
				);
			}
		}
		setRunConfig(listRunConfigTemp);
	}, [props]);
	useEffect(() => {
		const infoConfigTemp = [...infoConfig];
		const version = {
			dataIndex: 'chartVersion',
			key: 'chartVersion',
			label: '服务版本'
		};
		infoConfigTemp.splice(4, 0, version);
		if (
			data?.mysqlDTO?.openDisasterRecoveryMode &&
			data?.mysqlDTO?.isSource
		) {
			infoConfigTemp.splice(5, 0, disasterInstanceConfig);
		} else if (
			data?.mysqlDTO?.openDisasterRecoveryMode &&
			!data?.mysqlDTO?.isSource
		) {
			infoConfigTemp.splice(5, 0, originInstanceConfig);
		}
		if (extendSchedulerAPI) {
			const schedulerConfig = {
				dataIndex: 'scheduler',
				key: 'scheduler',
				label: '智能调度',
				render: (val: any) => <span>{val ? '开启' : '关闭'}</span>
			};
			infoConfigTemp.splice(5, 0, schedulerConfig);
		}
		if (
			(type === 'redis' ||
				type === 'postgresql' ||
				type === 'elasticsearch') &&
			hostNetWorkAPI
		) {
			const hostNetwork = {
				dataIndex: 'hostNetwork',
				key: 'hostNetwork',
				label: '主机网络',
				render: (value: string) => <span>{value ? '是' : '否'}</span>
			};
			infoConfigTemp.splice(9, 0, hostNetwork);
		}
		const descriptionTemp = {
			dataIndex: 'description',
			key: 'description',
			label: '备注',
			render: (val: string) => {
				return (
					<div className="display-flex flex-align">
						<div className="text-overflow-one" title={val}>
							{val}
						</div>
						<Popconfirm
							title={
								<Form form={form}>
									<FormItem name="description">
										<Input
											placeholder="请输入"
											defaultValue={val}
										/>
									</FormItem>
								</Form>
							}
							icon={null}
							onConfirm={() =>
								editDescription(form.getFieldsValue())
							}
						>
							<EditOutlined
								style={{
									marginLeft: 8,
									cursor: 'pointer',
									fontSize: 14,
									verticalAlign: 'middle'
								}}
							/>
						</Popconfirm>
					</div>
				);
			}
		};
		infoConfigTemp.push(descriptionTemp);
		setInfoConfig(objectRemoveDuplicatesByKey(infoConfigTemp, 'dataIndex'));
	}, [props.data]);

	const configConfigTemp =
		type === 'kafka'
			? [...configConfig, zookeeperConfig]
			: [
					...configConfig,
					(type === 'postgresql' ||
						type === 'redis' ||
						type === 'elasticsearch') &&
					data.customVolumes &&
					dataPartitionAPI
						? directoryConfig
						: {}
			  ];
	return (
		<div className="basic-info detail">
			{customMid &&
			!(data.capabilities || ['basic']).includes('basic') ? (
				<DefaultPicture />
			) : (
				<>
					<DataFields dataSource={basicData} items={infoConfig} />
					<div className="detail-divider" />
					<DataFields
						dataSource={configData}
						items={configConfigTemp}
					/>
					<div className="detail-divider" />
					<DataFields dataSource={runData} items={runConfig} />
					<div className="detail-divider" />
					{customMid && (
						<>
							<DataFields
								dataSource={dynamicData}
								items={dynamicConfig}
							/>
							<div className="detail-divider" />
						</>
					)}
					<EventsList />
				</>
			)}
		</div>
	);
}
export default BasicInfo;

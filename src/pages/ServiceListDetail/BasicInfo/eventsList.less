/* eventsList */
.success-tip{
	.mixin (lineHeight, 22px);
	display: inline-block;
	width: 40px;
	background: #F6FFED;
	border-radius: @border-radius;
	border: 1px solid #B7EB8F;
	color: #52C41A;
}
.error-tip{
	.mixin (lineHeight, 22px);
	display: inline-block;
	width: 40px;
	background: #FFF1F0;
	border-radius: @border-radius;
	border: 1px solid #FFA39E;
	color: #FF4D4F;
}
.normal-tip {
	.mixin (lineHeight, 22px);
	display: inline-block;
	padding: 0 8px;
	background: #F5F5F5;
	border-radius: @border-radius;
	border: 1px solid #D6D6D6;
	color: #575757;
}

.event-line-box {
	width: 100%;
	max-height: 250px;
	overflow: auto;
}

.event-content {
	width: 100%;
	border-radius: @border-radius @border-radius 0px 0px;
	border: 1px solid #EBEBEB;
	.event-info {
		width: 100%;
		line-height: 22px;
		padding: 12px 18px;
		background: #FAFAFA;
		justify-content: space-between;
		.event-name {
			.active {
				transform: rotate(90deg);
				transition: all 0.2s ease-out;
			}
		}
		.event-time {
			color: #898989;
		}
	}
	.event-message {
		border-top: 1px solid #EBEBEB;
		padding: 20px 16px;
	}
}

import React, { useState, useEffect } from 'react';
import { Empty, Select, Space, Timeline } from 'antd';
import { RightOutlined } from '@ant-design/icons';
import { useParams } from 'react-router';
import { getMiddlewareEvents } from '@/services/middleware';
import transTime from '@/utils/transTime';
import { DetailParams, EventItem, EventsSendData } from '../detail';
import './eventsList.less';
import './index.less';
import useRefresh from '@/utils/useRefresh';

const successTip = <div className="success-tip">正常</div>;

const errorTip = <div className="error-tip">异常</div>;

export default function EventsList(): JSX.Element {
	const params: DetailParams = useParams();
	const { clusterId, namespace, middlewareName, name } = params;
	// * 事件
	const [eventType, setEventType] = useState<string>('All');
	const [kind, setKind] = useState<string>('All');
	const [eventList, setEventList] = useState<EventItem[]>([]);
	const [originEventList, setOriginEventList] = useState<EventItem[]>([]);
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	const getEventsData = async (kind: string) => {
		const sendData: EventsSendData = {
			clusterId,
			namespace,
			middlewareName,
			type: name
		};
		if (kind) sendData.kind = kind === 'All' ? '' : kind;
		const res = await getMiddlewareEvents(sendData);
		if (res.success) {
			const temp = res.data.map((item: EventItem) => ({
				...item,
				show: false
			}));
			setOriginEventList(temp);
			setEventList(
				temp.filter((item: EventItem) => {
					if (eventType === 'All') return true;
					else if (eventType === 'Normal')
						return item.type === 'Normal';
					else return item.type !== 'Normal';
				})
			);
		}
	};

	const eventHandler = (item: EventItem, index: number) => {
		const tempArr = [...eventList];
		item.show = !item.show;
		tempArr[index] = item;
		setEventList(tempArr);
	};
	useEffect(() => {
		if (refreshKey > 0) {
			getEventsData(kind);
		}
	}, [refreshKey]);
	useEffect(() => {
		getEventsData(kind);
	}, [kind]);

	useEffect(() => {
		if (eventType) {
			const temp = originEventList.filter((item) => {
				if (eventType === 'All') return true;
				else if (eventType === 'Normal') return item.type === 'Normal';
				else return item.type !== 'Normal';
			});
			setEventList(temp);
		}
	}, [eventType]);

	return (
		<>
			<div className="flex-space-between">
				<h2>实时事件</h2>
				<Space>
					<Select
						size="small"
						style={{ marginRight: 16 }}
						value={eventType}
						onChange={(val) => setEventType(val)}
						dropdownMatchSelectWidth={false}
					>
						<Select.Option value={'All'}>全部状态</Select.Option>
						<Select.Option value={'Normal'}>正常</Select.Option>
						<Select.Option value={'Abnormal'}>异常</Select.Option>
					</Select>
					<Select
						size="small"
						value={kind}
						onChange={(val) => setKind(val)}
						dropdownMatchSelectWidth={false}
					>
						<Select.Option value={'All'}>全部类型</Select.Option>
						<Select.Option value={'Pod'}>Pod</Select.Option>
						<Select.Option value={'StatefulSet'}>
							StatefulSet
						</Select.Option>
					</Select>
				</Space>
			</div>
			<div className="event-line-box">
				{eventList && eventList.length === 0 ? (
					<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
				) : null}
				{eventList && (
					<Timeline>
						{eventList.map((item, index) => (
							<Timeline.Item
								key={index}
								dot={
									item.type === 'Normal'
										? successTip
										: errorTip
								}
							>
								<div className="event-content">
									<div
										className={`event-info display-flex`}
										onClick={() =>
											eventHandler(item, index)
										}
									>
										<div className="event-name">
											<RightOutlined
												className={`${
													item.show ? 'active' : null
												}`}
											/>
											{item.involvedObject && (
												<span>
													{item.involvedObject.kind}:{' '}
													{item.involvedObject.name}
												</span>
											)}
											<span
												className="normal-tip"
												style={{ marginLeft: 18 }}
											>
												{item.reason}
											</span>
										</div>
										<div className="event-time">
											<span>
												{transTime.gmt2local(
													item.lastTimestamp
												)}
											</span>
										</div>
									</div>
									{item.show ? (
										<div className="event-message">
											<p>{item.message}</p>
										</div>
									) : null}
								</div>
							</Timeline.Item>
						))}
					</Timeline>
				)}
			</div>
		</>
	);
}

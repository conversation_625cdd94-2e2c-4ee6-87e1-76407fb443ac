import { checkPort } from '@/services/ingress';
import { Form, InputNumber } from 'antd';
import { ValidateStatus } from 'antd/es/form/FormItem';
import React, { useState } from 'react';

export default function ExposePort({
	label,
	name,
	nodePortArray,
	ingressPortArray,
	ingresses,
	disabled,
	clusterId,
	namespace,
	middlewareName,
	middlewareType,
	chartVersion
}: {
	label: string;
	name: string;
	nodePortArray: any[];
	ingressPortArray: any[];
	ingresses: any[];
	disabled?: boolean;
	clusterId: string;
	namespace: string;
	middlewareName: string;
	middlewareType: string;
	chartVersion: string;
}): JSX.Element {
	const form = Form.useFormInstance();
	const [validateStatus, setValidateStatus] = useState<ValidateStatus>();
	const [help, setHelp] = useState<React.ReactNode>();
	const checkExposePort = async (_: any, value: any) => {
		if (!value) {
			setValidateStatus('error');
			setHelp('请输入端口');
			return Promise.reject();
		}
		const exportType = form.getFieldValue('tcpExposeType');
		if (exportType === 'NodePort') {
			if (
				value >= Number(nodePortArray[0]) &&
				value <= Number(nodePortArray[1])
			) {
				const res = await checkPort({
					clusterId,
					namespace,
					middlewareName,
					middlewareType,
					chartVersion,
					startPort: value + '',
					endPort: value + ''
				});
				if (res.success) {
					if (res.data === '[]') {
						setValidateStatus('success');
						setHelp('');
						return Promise.resolve();
					} else {
						setHelp(
							<>
								<p>{`当前端口组为${nodePortArray.join(
									'-'
								)},请在端口组范围内选择端口`}</p>
								<p>{`${res.data}端口号被占用`}</p>
							</>
						);
						setValidateStatus('error');
						return Promise.reject();
					}
				} else {
					setHelp('端口校验失败');
					setValidateStatus('error');
					return Promise.reject();
				}
			} else {
				setHelp(
					`当前端口组为${nodePortArray.join(
						'-'
					)},请在端口组范围内选择端口`
				);
				setValidateStatus('error');
				return Promise.reject();
			}
		} else {
			const ingressClassName = form.getFieldValue('ingressClassName');
			if (!ingressClassName) {
				setValidateStatus('error');
				setHelp('负载均衡不能为空');
				return Promise.reject();
			}
			const cur = ingresses.find(
				(item) => item.ingressClassName === ingressClassName
			);
			if (cur?.type === 'nginx') {
				if (
					value >= ingressPortArray[0] &&
					value <= ingressPortArray[1]
				) {
					const res = await checkPort({
						clusterId,
						namespace,
						middlewareName,
						middlewareType,
						chartVersion,
						startPort: value + '',
						endPort: value + ''
					});
					if (res.success) {
						if (res.data === '[]') {
							setValidateStatus('success');
							setHelp('');
							return Promise.resolve();
						} else {
							setHelp(
								<>
									<p>{`当前负载均衡相关端口组为${ingressPortArray.join(
										'-'
									)}请在端口组范围内选择端口`}</p>
									<p>{`${res.data}端口号被占用`}</p>
								</>
							);
							setValidateStatus('error');
							return Promise.reject();
						}
					} else {
						setHelp('端口校验失败');
						setValidateStatus('error');
						return Promise.reject();
					}
				} else {
					setHelp(
						`当前负载均衡相关端口组为${ingressPortArray.join(
							'-'
						)}请在端口组范围内选择端口`
					);
					setValidateStatus('error');
					return Promise.reject();
				}
			} else {
				for (
					let index = 0;
					index < (cur?.traefikPortList?.length as number);
					index++
				) {
					const ele = cur?.traefikPortList[index];
					if (value >= ele.startPort && value <= ele.endPort) {
						const res = await checkPort({
							clusterId,
							namespace,
							middlewareName,
							middlewareType,
							chartVersion,
							startPort: value + '',
							endPort: value + ''
						});
						if (res.success) {
							if (res.data) {
								setValidateStatus('success');
								setHelp('');
								return Promise.resolve();
							} else {
								setHelp(
									<>
										<p>{`当前负载均衡相关端口组为${cur?.traefikPortList
											.map(
												(item: any) =>
													`${item.startPort}-${item.endPort}`
											)
											.join(
												','
											)}请在端口组范围内选择端口`}</p>
										<p>{`${res.data}端口号被占用`}</p>
									</>
								);
								setValidateStatus('error');
								return Promise.reject();
							}
						} else {
							setHelp('端口校验失败');
							setValidateStatus('error');
							return Promise.reject();
						}
					}
				}
				setHelp(`当前负载均衡相关端口组为
				${cur?.traefikPortList
					.map((item: any) => `${item.startPort}-${item.endPort}`)
					.join(',')}
				请在端口组范围内选择端口`);
				setValidateStatus('error');
				return Promise.reject();
			}
		}
	};
	return (
		<Form.Item
			label={label}
			name={name}
			required={true}
			validateStatus={validateStatus}
			help={help}
			rules={[
				{ validator: checkExposePort, validateTrigger: ['onBlur'] }
			]}
		>
			<InputNumber
				placeholder="请输入规定范围以内的端口"
				style={{ width: 250 }}
				disabled={disabled || false}
			/>
		</Form.Item>
	);
}

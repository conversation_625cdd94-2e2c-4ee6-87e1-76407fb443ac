import React, { useState } from 'react';
import { Input } from 'antd';

interface domainPath {
	host: string;
	path: string;
}
export default function DomainPath({
	value,
	onChange
}: {
	value?: domainPath;
	onChange?: (value: any) => void;
}): JSX.Element {
	const [host, setHost] = useState<string>();
	const [path, setPath] = useState<string>();
	return (
		<Input.Group compact>
			<Input
				value={value?.host || host}
				onChange={(e) => {
					setHost(e.target.value);
					onChange &&
						onChange({
							host: e.target.value,
							path
						});
				}}
				style={{ width: '65%' }}
				placeholder="请输入域名"
			/>
			<Input
				value={value?.path || path}
				onChange={(e) => {
					setPath(e.target.value);
					onChange &&
						onChange({
							host,
							path: e.target.value
						});
				}}
				style={{
					width: '35%'
				}}
				placeholder="请输入路径"
			/>
		</Input.Group>
	);
}

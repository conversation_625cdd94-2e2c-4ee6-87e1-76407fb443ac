import SelectCard from '@/components/SelectCard';
import React, { useState } from 'react';
import { ServiceIngressTypeItem } from '../../detail';

export default function IngressTypeContent({
	value,
	onChange,
	disabled,
	options
}: {
	value?: string;
	onChange?: (value: string) => void;
	disabled: boolean;
	options: ServiceIngressTypeItem[];
}): JSX.Element {
	const [ingressType, setIngressType] = useState<string>('');
	return (
		<SelectCard
			options={options}
			disabled={disabled}
			currentValue={value || ingressType}
			onCallBack={(value: any) => {
				setIngressType(value);
				onChange && onChange(value);
			}}
		/>
	);
}

import SelectBlock from '@/components/SelectBlock';
import React, { useState } from 'react';

const fourNetworkIngress = [
	{ value: 'Ingress', label: 'Ingress-TCP' },
	{ value: 'NodePort', label: 'NodePort' }
];
export default function TcpExposeType({
	value,
	onChange,
	disabled
}: {
	value?: any;
	onChange?: (value: any) => void;
	disabled: boolean;
}): JSX.Element {
	const [tcpExposeType, setTcpExposeType] = useState<string>('');
	return (
		<SelectBlock
			disabled={disabled}
			options={fourNetworkIngress}
			currentValue={value || tcpExposeType}
			onCallBack={(valueTemp: any) => {
				setTcpExposeType(valueTemp);
				onChange && onChange(valueTemp);
			}}
		/>
	);
}

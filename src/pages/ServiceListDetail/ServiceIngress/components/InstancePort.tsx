import React, { useState } from 'react';
import { Checkbox, Form, InputNumber, Space, notification } from 'antd';
import { ValidateStatus } from 'antd/es/form/FormItem';
import { checkPort } from '@/services/ingress';
export default function InstancePort({
	nodePortArray,
	ingressPortArray,
	ingresses,
	serviceIngressTypes,
	clusterId,
	namespace,
	middlewareName,
	middlewareType,
	chartVersion,
	mode
}: {
	nodePortArray: any[];
	ingressPortArray: any[];
	ingresses: any[];
	serviceIngressTypes: any[];
	clusterId: string;
	namespace: string;
	middlewareName: string;
	middlewareType: string;
	chartVersion: string;
	mode: string;
}): JSX.Element {
	const form = Form.useFormInstance();
	const [validateStatus, setValidateStatus] = useState<ValidateStatus>();
	const [help, setHelp] = useState<React.ReactNode>();
	const checkInstancePort = async (_: any, value: any) => {
		if (!value) {
			return Promise.reject(new Error('请输入起始端口号'));
		}
		if (form.getFieldValue('tcpExposeType') === 'NodePort') {
			if (
				value.port >= Number(nodePortArray[0]) &&
				value.port <= Number(nodePortArray[1])
			) {
				const ingressType = form.getFieldValue('ingressType');
				const curType = serviceIngressTypes.find(
					(item) => item.exposeType === ingressType
				);
				const podsNum =
					mode === 'cluster'
						? curType.serviceDTOList.length - 1
						: curType.serviceDTOList.length - 2;
				const res = await checkPort({
					clusterId,
					namespace,
					middlewareName,
					middlewareType,
					chartVersion,
					startPort: value.port + '',
					endPort: value.port + podsNum + ''
				});
				if (res.success) {
					if (res.data === '[]') {
						setHelp(
							<>
								<p>
									{`当前端口组为${nodePortArray.join(
										'-'
									)},请在端口组范围内选择端口`}
								</p>
								<p>{`当前的实例端口配置是[${value.port}-${
									value.port + podsNum
								}]`}</p>
								<p>其中没有端口被占用！</p>
							</>
						);
						setValidateStatus('success');
						return Promise.resolve();
					} else {
						if (value.checked) {
							setHelp(
								<>
									<p>
										{`当前端口组为${nodePortArray.join(
											'-'
										)},请在端口组范围内选择端口`}
									</p>
									<p>{`当前的实例端口配置是[${value.port}-${
										value.port + podsNum
									}]`}</p>
									<p>{`其中${res.data}端口号被占用，已跳过冲突端口`}</p>
								</>
							);
							setValidateStatus('success');
							return Promise.resolve();
						} else {
							setHelp(
								<>
									<p>
										{`当前端口组为${nodePortArray.join(
											'-'
										)},请在端口组范围内选择端口`}
									</p>
									<p>{`当前的实例端口配置是[${value.port}-${
										value.port + podsNum
									}]`}</p>
									<p>{`其中${res.data}端口号被占用，请重新输入`}</p>
								</>
							);
							setValidateStatus('error');
							return Promise.reject();
						}
					}
				} else {
					setHelp(
						`当前端口组为${nodePortArray.join(
							'-'
						)},请在端口组范围内选择端口`
					);
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
					setValidateStatus('error');
					return Promise.reject();
				}
			} else {
				setHelp(
					`当前端口组为${nodePortArray.join(
						'-'
					)},请在端口组范围内选择端口`
				);
				setValidateStatus('error');
				return Promise.reject();
			}
		} else {
			const ingressClassName = form.getFieldValue('ingressClassName');
			if (!ingressClassName) {
				return Promise.reject(new Error('负载均衡不能为空'));
			}
			const cur = ingresses.find(
				(item) => item.ingressClassName === ingressClassName
			);
			const ingressType = form.getFieldValue('ingressType');
			const curType = serviceIngressTypes.find(
				(item) => item.exposeType === ingressType
			);
			const podsNum =
				mode === 'cluster'
					? curType.serviceDTOList.length - 1
					: curType.serviceDTOList.length - 2;
			if (cur?.type === 'nginx') {
				if (
					value.port >= ingressPortArray[0] &&
					value.port <= ingressPortArray[1]
				) {
					const res = await checkPort({
						clusterId,
						namespace,
						middlewareName,
						middlewareType,
						chartVersion,
						startPort: value.port + '',
						endPort: value.port + podsNum + ''
					});
					if (res.success) {
						if (res.data === '[]') {
							setHelp(
								<>
									<p>
										{`当前端口组为${ingressPortArray.join(
											'-'
										)},请在端口组范围内选择端口`}
									</p>
									<p>{`当前的实例端口配置是[${value.port}-${
										value.port + podsNum
									}]`}</p>
									<p>其中没有端口被占用！</p>
								</>
							);
							setValidateStatus('success');
							return Promise.resolve();
						} else {
							if (value.checked) {
								setHelp(
									<>
										<p>
											{`当前端口组为${ingressPortArray.join(
												'-'
											)},请在端口组范围内选择端口`}
										</p>
										<p>{`当前的实例端口配置是[${
											value.port
										}-${value.port + podsNum}]`}</p>
										<p>{`其中${res.data}端口号被占用，已跳过冲突端口`}</p>
									</>
								);
								setValidateStatus('success');
								return Promise.resolve();
							} else {
								setHelp(
									<>
										<p>
											{`当前端口组为${ingressPortArray.join(
												'-'
											)},请在端口组范围内选择端口`}
										</p>
										<p>{`当前的实例端口配置是[${
											value.port
										}-${value.port + podsNum}]`}</p>
										<p>{`其中${res.data}端口号被占用，请重新输入`}</p>
									</>
								);
								setValidateStatus('error');
								return Promise.reject();
							}
						}
					} else {
						setHelp(
							`当前负载均衡相关端口组为${ingressPortArray.join(
								'-'
							)}请在端口组范围内选择端口`
						);
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
						setValidateStatus('error');
						return Promise.reject();
					}
				} else {
					setHelp(
						`当前负载均衡相关端口组为${ingressPortArray.join(
							'-'
						)}请在端口组范围内选择端口`
					);
					setValidateStatus('error');
					return Promise.reject();
				}
			} else {
				for (
					let index = 0;
					index < (cur?.traefikPortList?.length as number);
					index++
				) {
					const ele = cur?.traefikPortList[index];
					if (
						value.port >= ele.startPort &&
						value.port <= ele.endPort
					) {
						const res = await checkPort({
							clusterId,
							namespace,
							middlewareName,
							middlewareType,
							chartVersion,
							startPort: value.port + '',
							endPort: value.port + podsNum + ''
						});
						if (res.success) {
							if (res.data === '[]') {
								setHelp(
									<>
										<p>
											{`当前负载均衡相关端口组为
											${cur?.traefikPortList
												.map(
													(item: any) =>
														`${item.startPort}-${item.endPort}`
												)
												.join(',')}
											请在端口组范围内选择端口`}
										</p>
										<p>
											{`当前的实例端口配置是[${
												value.port
											}-${value.port + podsNum}]`}
										</p>
										<p>其中没有端口被占用！</p>
									</>
								);
								setValidateStatus('success');
								return Promise.resolve();
							} else {
								if (value.checked) {
									setHelp(
										<>
											<p>
												{`当前负载均衡相关端口组为
													${cur?.traefikPortList
														.map(
															(item: any) =>
																`${item.startPort}-${item.endPort}`
														)
														.join(',')}
													请在端口组范围内选择端口`}
											</p>
											<p>{`当前的实例端口配置是[${
												value.port
											}-${value.port + podsNum}]`}</p>
											<p>{`其中${res.data}端口号被占用，已跳过冲突端口`}</p>
										</>
									);
									setValidateStatus('success');
									return Promise.resolve();
								} else {
									setHelp(
										<>
											<p>
												{`当前负载均衡相关端口组为
													${cur?.traefikPortList
														.map(
															(item: any) =>
																`${item.startPort}-${item.endPort}`
														)
														.join(',')}
													请在端口组范围内选择端口`}
											</p>
											<p>{`当前的实例端口配置是[${
												value.port
											}-${value.port + podsNum}]`}</p>
											<p>{`其中${res.data}端口号被占用，请重新输入`}</p>
										</>
									);
									setValidateStatus('error');
									return Promise.reject();
								}
							}
						} else {
							setHelp(`当前负载均衡相关端口组为
							${cur?.traefikPortList
								.map(
									(item: any) =>
										`${item.startPort}-${item.endPort}`
								)
								.join(',')}
							请在端口组范围内选择端口`);
							setValidateStatus('error');
							return Promise.reject();
						}
					}
				}
				setHelp(`当前负载均衡相关端口组为
				${cur?.traefikPortList
					.map((item: any) => `${item.startPort}-${item.endPort}`)
					.join(',')}
				请在端口组范围内选择端口`);
				setValidateStatus('error');
				return Promise.reject();
			}
		}
	};
	return (
		<Form.Item
			label="实例端口配置"
			name="instancePort"
			required
			validateStatus={validateStatus}
			help={help}
			rules={[
				{ validator: checkInstancePort, validateTrigger: ['onBlur'] }
			]}
		>
			<StartPort />
		</Form.Item>
	);
}

const StartPort = ({
	value,
	onChange
}: {
	value?: any;
	onChange?: (value: any) => void;
}) => {
	const [port, setPort] = useState<number>(value?.port);
	const [checked, setChecked] = useState<boolean>(false);
	return (
		<Space>
			<InputNumber
				value={value?.port || port}
				onChange={(valueT) => {
					setPort(valueT);
					onChange && onChange({ port: valueT, checked });
				}}
				placeholder="请输入起始端口号"
				style={{ width: 250 }}
			/>
			<Checkbox
				checked={value?.checked || checked}
				onChange={(e) => {
					setChecked(e.target.checked);
					onChange &&
						onChange({
							port: value?.port || port,
							checked: e.target.checked
						});
				}}
			>
				跳过冲突端口
			</Checkbox>
		</Space>
	);
};

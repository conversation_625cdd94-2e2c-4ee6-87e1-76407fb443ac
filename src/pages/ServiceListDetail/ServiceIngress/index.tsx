import React, { useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router';
import {
	Button,
	Drawer,
	Modal,
	notification,
	Popover,
	Space,
	Spin,
	Table,
	Alert,
	Tooltip
} from 'antd';
import ProList from '@/components/ProList';
import { ListCard, ListCardItem, ListPanel } from '@/components/ListCard';
import ArrowLine from '@/components/ArrowLine';
import Actions from '@/components/Actions';
import { IconFont } from '@/components/IconFont';
import {
	InternalServiceItem,
	HostNetworkServiceItem,
	ServiceDetailIngressProps,
	ServiceIngressTypeItem,
	IngressHttpPathsItem,
	DetailParams
} from '../detail';
import DefaultPicture from '@/components/DefaultPicture';
import {
	getInternalServices,
	getServiceIngress,
	getHostNetwork,
	deleteServiceIngress
} from '@/services/ingress';
import otherColor from '@/assets/images/nodata.svg';
import { api } from '@/api.json';
import { controlledOperationDisabled, copyValue } from '@/utils/utils';
import { CheckCircleFilled, ProfileOutlined } from '@ant-design/icons';
import nodata from '@/assets/images/nodata.svg';
import storage from '@/utils/storage';
import EditPortForm from './EditPortForm';
import { getMidImagePath } from '@/services/common';
import './index.less';
import useRefresh from '@/utils/useRefresh';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';

const LinkButton = Actions.LinkButton;

enum TcpExportTypeEnum {
	nginx = 'Nginx',
	nodePort = 'NodePort',
	traefik = 'Traefik'
}
export default function ServiceDetailIngress(
	props: ServiceDetailIngressProps
): JSX.Element {
	const { customMid, capabilities, mode, status, data } = props;
	const params: DetailParams = useParams();
	const {
		name,
		aliasName,
		middlewareName,
		namespace,
		clusterId,
		chartVersion,
		type
	}: DetailParams = useParams();
	const history = useHistory();
	const [dataSource, setDataSource] = useState<ServiceIngressTypeItem[]>([]);
	const [visible, setVisible] = useState<boolean>(false);
	const [hostVisible, setHostVisible] = useState<boolean>(false);
	const [clusterOpen, setClusterOpen] = useState<boolean>(false);
	const [internalDataSource, setInternalDataSource] = useState<
		InternalServiceItem[]
	>([]);
	const [hostNetworkDataSource, setHostNetworkDataSource] = useState<
		HostNetworkServiceItem[]
	>([]);
	const [current, setCurrent] = useState<ServiceIngressTypeItem>();
	const [editVisible, setEditVisible] = useState<boolean>(false);
	const [spinning, setSpinning] = useState<boolean>(false);
	const [imagePath, setImagePath] = useState<string>();
	const [clusterDataSource, setClusterDataSource] = useState<
		ServiceIngressTypeItem['serviceDTOList']
	>([]);
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	// * 判断当前服务是否发布了集群外访问
	const [externalEnable, setExternalEnable] = useState<boolean>(false);

	// * feature 主机网络是否打开
	const [hostNetWorkAPI] = useState<boolean>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'hostNetWork')?.enabled ?? true
	);
	const addIngressOperatorId = maintenances['Add Service Exposure'];
	const deleteIngressOperatorId = maintenances['Delete Service Exposure'];
	const editIngressOperatorId = maintenances['Edit Service Exposure'];
	const onRefresh = () => {
		getIngressByMid();
		getInternalService();
		getHostNetworkAddress();
	};
	const judgeDisabled = () => {
		if (name === 'kafka' && status !== 'Running') {
			return {
				flag: true,
				title: '当前中间件运行异常，无法新增服务暴露。'
			};
		} else if (name === 'rocketmq' && status !== 'Running') {
			return {
				flag: true,
				title: '当前中间件运行异常，无法新增服务暴露。'
			};
		} else if (
			name === 'redis' &&
			mode === 'cluster' &&
			dataSource?.length > 0
		) {
			return {
				flag: true,
				title: '该服务已存在集群外访问，不可重复添加。'
			};
		} else if (name === 'redis' && status !== 'Running') {
			return {
				flag: true,
				title: '当前中间件运行异常，无法新增服务暴露。'
			};
		} else {
			return { flag: false, title: '' };
		}
	};
	const judgeEdit = (record: any) => {
		if (name === 'redis' && record.exposeType === 'external')
			return { flag: true, title: '' };
	};
	const addServiceIngress = () => {
		if (name === 'elasticsearch') {
			history.push(
				`/project/${params.type}/${params.name}/${params.aliasName}/container/externalAccess/add/es/${middlewareName}/${clusterId}/${chartVersion}/${namespace}/${mode}`
			);
		} else if (
			name === 'mysql' ||
			name === 'postgresql' ||
			name === 'zookeeper' ||
			name === 'redis' ||
			name === 'rocketmq' ||
			name === 'kafka'
		) {
			const external = dataSource?.some(
				(item) => item.exposeType === 'external'
			);
			history.push(
				`/project/${params.type}/${params.name}/${params.aliasName}/container/externalAccess/add/other/${middlewareName}/${clusterId}/${chartVersion}/${namespace}/${mode}/${external}`
			);
		} else {
			history.push(
				`/project/${params.type}/${name}/${aliasName}/container/externalAccess/addExternalAccess/${middlewareName}/${clusterId}/${chartVersion}/${namespace}`
			);
		}
	};
	const Operation = {
		primary: (
			<>
				<Button
					type="primary"
					title={judgeDisabled().title}
					disabled={
						judgeDisabled().flag ||
						controlledOperationDisabled('maintenance', data.lock)
					}
					onClick={() => {
						WorkOrderFuc(
							addServiceIngress,
							data.lock,
							middlewareName,
							addIngressOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
				>
					新增
				</Button>
				<Button onClick={() => setVisible(true)}>集群内访问</Button>
				{((name === 'postgresql' || name === 'redis') &&
					data[name + 'Param'].hostNetwork &&
					hostNetWorkAPI) ||
				(name === 'elasticsearch' &&
					data.esParam?.hostNetwork &&
					hostNetWorkAPI) ? (
					<Button onClick={() => setHostVisible(true)}>
						主机网络访问
					</Button>
				) : null}
			</>
		)
	};
	const editServiceIngress = (item: ServiceIngressTypeItem) => {
		if (name === 'elasticsearch') {
			history.push(
				`/project/${params.type}/${params.name}/${params.aliasName}/container/externalAccess/edit/es/${middlewareName}/${clusterId}/${chartVersion}/${namespace}/${mode}/${item.exposeId}`
			);
		} else if (
			name === 'mysql' ||
			name === 'postgresql' ||
			name === 'zookeeper' ||
			name === 'redis' ||
			name === 'rocketmq' ||
			name === 'kafka'
		) {
			history.push(
				`/project/${params.type}/${params.name}/${params.aliasName}/container/externalAccess/edit/other/${middlewareName}/${clusterId}/${chartVersion}/${namespace}/${mode}/${item.exposeId}`
			);
		} else {
			storage.setLocal('availableRecord', item);
			history.push(
				`/project/${params.type}/${params.name}/${params.aliasName}/container/externalAccess/addExternalAccess/${middlewareName}/${clusterId}/${chartVersion}/${namespace}/${item.exposeId}`
			);
		}
	};
	useEffect(() => {
		getIngressByMid();
		getInternalService();
		getHostNetworkAddress();
		getMiddlewareImagePath();
	}, [refreshKey]);
	const getInternalService = () => {
		getInternalServices({
			clusterId,
			namespace,
			middlewareType: name,
			middlewareName
		}).then((res) => {
			if (res.success) {
				const initService = [
					`${middlewareName}-0-master`,
					`${middlewareName}-1-master`,
					`${middlewareName}-2-master`,
					`${middlewareName}-0-slave`,
					`${middlewareName}-1-slave`,
					`${middlewareName}-2-slave`,
					`${middlewareName}nameserver-proxy-svc-0`,
					`${middlewareName}nameserver-proxy-svc-1`,
					`${middlewareName}-kafka-external-svc`
				];
				const list = res.data.filter((item: InternalServiceItem) => {
					return (
						!initService.some((i: string) =>
							item.serviceName?.includes(i)
						) && !item.serviceName.endsWith('16379')
					);
				});
				setInternalDataSource(list);
			}
		});
	};
	const getHostNetworkAddress = () => {
		getHostNetwork({
			clusterId,
			namespace,
			middlewareType: name,
			middlewareName,
			chartVersion
		}).then((res) => {
			if (res.success) {
				setHostNetworkDataSource(res.data);
			}
		});
	};
	const getIngressByMid = () => {
		setSpinning(true);
		const sendData = {
			clusterId,
			namespace,
			middlewareName,
			middlewareType: name,
			chartVersion
		};
		getServiceIngress(sendData)
			.then((res) => {
				if (res.success) {
					setDataSource(res.data);
					if (
						res.data.some(
							(item: ServiceIngressTypeItem) =>
								item.exposeType === 'external'
						)
					) {
						setExternalEnable(true);
					} else {
						setExternalEnable(false);
					}
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setSpinning(false);
			});
	};
	const getMiddlewareImagePath = () => {
		getMidImagePath({
			clusterId,
			namespace,
			type: name,
			version: chartVersion
		}).then((res) => {
			if (res.success) {
				setImagePath(res.data);
			} else {
				setImagePath('');
			}
		});
	};
	const handleDelete = (record: ServiceIngressTypeItem) => {
		Modal.confirm({
			title: '操作确认',
			content:
				'删除对外路由会影响当前正在通过对外地址访问中间件的服务，请确认执行',
			okText: '确认',
			cancelText: '取消',
			onOk: async () => {
				const sendData = {
					...record,
					clusterId: clusterId,
					middlewareName: middlewareName,
					namespace: namespace,
					chartVersion
				};
				await ExecuteOrderFuc();
				return deleteServiceIngress(sendData)
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '对外路由删除成功'
							});
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					})
					.finally(() => {
						getIngressByMid();
					});
			}
		});
	};
	const judgeInit = (record: any) => {
		if (
			record.middlewareType === 'rocketmq' ||
			record.middlewareType === 'kafka'
		) {
			if (record.exposeType === 'external') {
				return true;
			} else {
				return false;
			}
		} else {
			return false;
		}
	};
	const ipValue = (record: ServiceIngressTypeItem) => {
		if (mode === 'cluster' && externalEnable === true)
			return record.aliasExposeType || '未知';
		if (record.protocol === 'HTTP') return record?.rules?.[0].host;
		if (record.exposeType === 'external' && mode === 'sentinel') {
			const temp = record.serviceDTOList.find(
				(item: any) => item.serviceName === `${middlewareName}-sentinel`
			);
			return temp?.exposePort;
		}
		return record.serviceDTOList[0]?.exposePort;
	};
	const internalAddressRender = (value: string) => {
		return (
			<span>
				{value}
				<Popover
					content={
						<div>
							<CheckCircleFilled
								style={{
									color: '#00A700',
									marginRight: '5px'
								}}
							/>
							复制成功
						</div>
					}
					trigger="click"
				>
					<IconFont
						className="card-ip-copy"
						type="icon-fuzhi1"
						style={{
							fontSize: 12
						}}
						onClick={() => copyValue(value)}
					/>
				</Popover>
			</span>
		);
	};
	const hostNetworkAddressRender = (
		value: string,
		record: HostNetworkServiceItem
	) => {
		return (
			<span>
				{`${value}:${record.exposePort}`}
				<Popover
					content={
						<div>
							<CheckCircleFilled
								style={{
									color: '#00A700',
									marginRight: '5px'
								}}
							/>
							复制成功
						</div>
					}
					trigger="click"
				>
					<IconFont
						className="card-ip-copy"
						type="icon-fuzhi1"
						style={{
							fontSize: 12
						}}
						onClick={() =>
							copyValue(`${value}:${record.exposePort}`)
						}
					/>
				</Popover>
			</span>
		);
	};
	const exposePortAndIpRender = (value: any, record: any) => {
		const ipsTemp = current?.ingressIpList;
		const valueTemp = `${ipsTemp?.[0]}:${record?.exposePort}`;
		return (
			<span>
				{ipsTemp?.[0]}
				<Tooltip
					title={ipsTemp?.map((item: string) => (
						<p key={item}>{item}</p>
					))}
				>
					<IconFont
						style={{ marginLeft: 4, marginRight: 2 }}
						type="icon-qita"
					/>
				</Tooltip>
				: {record?.exposePort}
				<Popover
					content={
						<div>
							<CheckCircleFilled
								style={{
									color: '#00A700',
									marginRight: '5px'
								}}
							/>
							复制成功
						</div>
					}
					trigger="click"
				>
					<IconFont
						className="card-ip-copy"
						type="icon-fuzhi1"
						style={{
							fontSize: 12
						}}
						onClick={() => copyValue(valueTemp)}
					/>
				</Popover>
			</span>
		);
	};
	const judgeIsPanel = (record: ServiceIngressTypeItem) => {
		if (mode === 'cluster' && externalEnable === true && name === 'redis')
			return false;
		if (record.protocol === 'HTTP') return true;
		if (
			record.aliasExposeType === '哨兵' &&
			record.tcpExposeType === 'nodePort'
		)
			return true;
		if (record.protocol === 'TCP' && record.tcpExposeType === 'traefik')
			return true;
		if (record.protocol === 'TCP' && record.tcpExposeType === 'nginx')
			return true;
		return false;
	};
	// * 当前中间件为自定义中间件时，且后端所传的capabilities包含ingress，则显示该页面的功能
	if (customMid && !capabilities.includes('ingress')) {
		return <DefaultPicture />;
	}
	return (
		<>
			<Alert
				message="请单击服务暴露项展开并查看已暴露的域名或IP+端口号"
				type="info"
				showIcon
				closable
				style={{ marginBottom: '16px' }}
			/>
			<Spin spinning={spinning}>
				<ProList operation={Operation}>
					{dataSource?.map((item: ServiceIngressTypeItem) => {
						if (judgeIsPanel(item)) {
							return (
								<ListPanel
									key={item.exposeId}
									title={middlewareName}
									subTitle={name}
									icon={
										<img
											height={34}
											width={34}
											style={{ marginRight: 8 }}
											src={
												imagePath
													? `${api}/images/middleware/${imagePath}`
													: otherColor
											}
											alt={name}
										/>
									}
									actionRender={
										<Actions>
											<LinkButton
												disabled={
													judgeEdit(item)?.flag ||
													controlledOperationDisabled(
														'maintenance',
														data.lock
													)
												}
												onClick={() => {
													WorkOrderFuc(
														() => {
															editServiceIngress(
																item
															);
														},
														data.lock,
														middlewareName,
														editIngressOperatorId,
														history,
														type,
														name,
														aliasName,
														clusterId,
														namespace
													);
												}}
											>
												编辑
											</LinkButton>
											<LinkButton
												disabled={
													judgeInit(item) ||
													// !operateFlag ||
													controlledOperationDisabled(
														'maintenance',
														data.lock
													)
												}
												onClick={() => {
													WorkOrderFuc(
														() => {
															handleDelete(item);
														},
														data.lock,
														middlewareName,
														deleteIngressOperatorId,
														history,
														type,
														name,
														aliasName,
														clusterId,
														namespace
													);
												}}
											>
												删除
											</LinkButton>
										</Actions>
									}
									columnGap="24px"
									render={
										<>
											<Space wrap>
												{item.protocol === 'TCP' &&
													item.ingressIpList?.map(
														(i: string) => {
															return (
																<div
																	key={
																		item.ingressClassName
																	}
																	className="pod-card-item"
																>
																	<IconFont
																		type="icon-duankou"
																		style={{
																			fontSize: 32,
																			marginRight: 8
																		}}
																	/>
																	<div className="pod-card-des">
																		<div className="pod-card-ip">
																			{i}:
																			{
																				item
																					.serviceDTOList[0]
																					.exposePort
																			}
																			<Popover
																				content={
																					<div>
																						<CheckCircleFilled
																							style={{
																								color: '#00A700',
																								marginRight:
																									'5px'
																							}}
																						/>
																						复制成功
																					</div>
																				}
																				trigger="click"
																			>
																				<IconFont
																					className="pod-card-ip-copy"
																					type="icon-fuzhi1"
																					style={{
																						fontSize: 12
																					}}
																					onClick={() =>
																						copyValue(
																							`${i}:${item.serviceDTOList[0].exposePort}`
																						)
																					}
																				/>
																			</Popover>
																		</div>
																		<div
																			className="pod-card-name"
																			title={
																				item.ingressClassName ||
																				''
																			}
																		>
																			{
																				item.ingressClassName
																			}
																		</div>
																	</div>
																</div>
															);
														}
													)}
												{item.exposeType ===
													'NodePort' &&
													item.serviceDTOList.map(
														(item: any) => {
															return (
																<div
																	key={
																		item.serviceName
																	}
																	className="pod-card-item"
																>
																	<IconFont
																		type="icon-duankou"
																		style={{
																			fontSize: 32,
																			marginRight: 8
																		}}
																	/>
																	<div className="pod-card-des">
																		<div className="pod-card-ip">
																			{
																				item.exposePort
																			}
																			<Popover
																				content={
																					<div>
																						<CheckCircleFilled
																							style={{
																								color: '#00A700',
																								marginRight:
																									'5px'
																							}}
																						/>
																						复制成功
																					</div>
																				}
																				trigger="click"
																			>
																				<IconFont
																					className="pod-card-ip-copy"
																					type="icon-fuzhi1"
																					style={{
																						fontSize: 12
																					}}
																					onClick={() =>
																						copyValue(
																							item.exposePort
																						)
																					}
																				/>
																			</Popover>
																		</div>
																		<div
																			className="pod-card-name"
																			title={
																				item.serviceName
																			}
																		>
																			{
																				item.serviceName
																			}
																		</div>
																	</div>
																</div>
															);
														}
													)}
												{item.protocol === 'HTTP' &&
													item.rules?.[0].ingressHttpPaths?.map(
														(
															i: IngressHttpPathsItem
														) => {
															return (
																<div
																	key={i.path}
																	className="pod-card-item"
																>
																	<IconFont
																		type="icon-duankou"
																		style={{
																			fontSize: 32,
																			marginRight: 8
																		}}
																	/>
																	<div className="pod-card-des">
																		<div className="pod-card-ip">
																			{
																				item
																					?.rules?.[0]
																					.host
																			}
																			{
																				i.path
																			}
																			<Popover
																				content={
																					<div>
																						<CheckCircleFilled
																							style={{
																								color: '#00A700',
																								marginRight:
																									'5px'
																							}}
																						/>
																						复制成功
																					</div>
																				}
																				trigger="click"
																			>
																				<IconFont
																					className="pod-card-ip-copy"
																					type="icon-fuzhi1"
																					style={{
																						fontSize: 12
																					}}
																					onClick={() =>
																						copyValue(
																							`${item.rules?.[0].host}:${i.servicePort}${i.path}`
																						)
																					}
																				/>
																			</Popover>
																		</div>
																		<div
																			className="pod-card-name"
																			title={
																				i.serviceName
																			}
																		>
																			{
																				i.serviceName
																			}
																		</div>
																	</div>
																</div>
															);
														}
													)}
											</Space>
										</>
									}
								>
									<ArrowLine
										style={{
											width: 'calc((100% - 350px) / 2)'
										}}
										label={item.aliasExposeType || '未知'}
									/>
									<ListCardItem
										width={100}
										label="暴露方式"
										value={item.protocol || ''}
									/>
									<ArrowLine
										style={{
											width: 'calc((100% - 350px) / 2)'
										}}
									/>
									<ListCardItem
										width={250}
										label={
											item.protocol === 'TCP'
												? '四层网络暴露'
												: '七层网络暴露'
										}
										value={ipValue(item)}
										icon={
											<IconFont
												type="icon-duankou"
												style={{
													fontSize: 32,
													marginRight: 8
												}}
											/>
										}
									/>
								</ListPanel>
							);
						} else {
							return (
								<ListCard
									key={item.exposeId}
									title={middlewareName}
									subTitle={name}
									icon={
										<img
											height={32}
											width={32}
											style={{ marginRight: 8 }}
											src={
												imagePath
													? `${api}/images/middleware/${imagePath}`
													: otherColor
											}
											alt={name}
										/>
									}
									actionRender={
										<Actions>
											<LinkButton
												disabled={
													judgeEdit(item)?.flag ||
													controlledOperationDisabled(
														'maintenance',
														data.lock
													)
												}
												onClick={() => {
													WorkOrderFuc(
														() => {
															editServiceIngress(
																item
															);
														},
														data.lock,
														middlewareName,
														editIngressOperatorId,
														history,
														type,
														name,
														aliasName,
														clusterId,
														namespace
													);
												}}
											>
												编辑
											</LinkButton>
											<LinkButton
												disabled={
													judgeInit(item) ||
													// !operateFlag ||
													controlledOperationDisabled(
														'maintenance',
														data.lock
													)
												}
												onClick={() => {
													WorkOrderFuc(
														() => {
															handleDelete(item);
														},
														data.lock,
														middlewareName,
														deleteIngressOperatorId,
														history,
														type,
														name,
														aliasName,
														clusterId,
														namespace
													);
												}}
											>
												删除
											</LinkButton>
											{mode === 'cluster' &&
												item.exposeType ===
													'external' && (
													<LinkButton
														onClick={() => {
															setCurrent(item);
															setClusterDataSource(
																item.serviceDTOList
																	.filter(
																		(
																			i: any
																		) =>
																			i.servicePort !==
																			16379
																	)
																	.sort(
																		(
																			a: any,
																			b: any
																		) =>
																			+a.exposePort -
																			+b.exposePort
																	)
															);
															setClusterOpen(
																true
															);
														}}
													>
														<ProfileOutlined />
													</LinkButton>
												)}
										</Actions>
									}
									columnGap="24px"
								>
									<ArrowLine
										style={{
											width: 'calc((100% - 350px) / 2)'
										}}
										label={
											mode === 'cluster'
												? ''
												: item.aliasExposeType || '未知'
										}
									/>
									<ListCardItem
										width={100}
										label="暴露方式"
										value={item.protocol || ''}
									/>
									<ArrowLine
										style={{
											width: 'calc((100% - 350px) / 2)'
										}}
									/>
									<ListCardItem
										width={250}
										label={
											item.protocol === 'TCP'
												? '四层网络暴露'
												: '七层网络暴露'
										}
										value={<span>{ipValue(item)}</span>}
										icon={
											<IconFont
												type="icon-duankou"
												style={{
													fontSize: 32,
													marginRight: 8
												}}
											/>
										}
									/>
								</ListCard>
							);
						}
					})}
				</ProList>
			</Spin>
			<Drawer
				title={
					<div className="icon-type-content">
						<img
							width={14}
							height={14}
							src={
								imagePath
									? `${api}/images/middleware/${imagePath}`
									: nodata
							}
						/>
						<div style={{ marginLeft: 8 }}>{middlewareName}</div>
					</div>
				}
				placement="right"
				onClose={() => {
					setVisible(false);
					setHostVisible(false);
					setClusterOpen(false);
					setCurrent(undefined);
					setClusterDataSource([]);
				}}
				open={visible || hostVisible || clusterOpen}
				width={600}
			>
				{visible ? (
					<Table rowKey="serviceName" dataSource={internalDataSource}>
						<Table.Column
							width={100}
							dataIndex="servicePurpose"
							title="暴露服务"
						/>
						<Table.Column
							dataIndex="internalAddress"
							title="服务及端口号"
							render={internalAddressRender}
						/>
					</Table>
				) : hostVisible ? (
					<Table
						rowKey="serviceName"
						dataSource={hostNetworkDataSource}
					>
						<Table.Column
							width={150}
							dataIndex="servicePurpose"
							title="实例名称"
							ellipsis
						/>
						<Table.Column
							dataIndex="exposeIP"
							title="主机ip及端口号"
							render={hostNetworkAddressRender}
						/>
					</Table>
				) : (
					<Table rowKey="exposePort" dataSource={clusterDataSource}>
						<Table.Column
							dataIndex="exposeIP"
							title="ip及端口号"
							render={exposePortAndIpRender}
						/>
					</Table>
				)}
			</Drawer>
			{editVisible && (
				<EditPortForm
					visible={editVisible}
					onCancel={() => setEditVisible(false)}
					clusterId={clusterId}
					namespace={namespace}
					middlewareName={middlewareName}
					onRefresh={onRefresh}
				/>
			)}
		</>
	);
}

import React, { useEffect, useState } from 'react';
import {
	<PERSON><PERSON>,
	But<PERSON>,
	Divider,
	Form,
	Select,
	Spin,
	Switch,
	Tag,
	notification
} from 'antd';
import { useParams } from 'react-router';
import { ProContent, ProHeader, ProPage } from '@/components/ProPage';
import {
	createServiceIngress,
	getAvailablePorts,
	getServiceIngressDetail,
	getServiceIngressType,
	updateServiceIngress
} from '@/services/ingress';
import { AvailablePortItem, ServiceIngressTypeItem } from '../detail.d';
import IngressTypeContent from './components/IngressTypeContent';
import { IngressItemProps } from '@/pages/ResourcePoolManagement/resource.pool';
import {
	getIngressTCPPort,
	getIngresses,
	getNodePort
} from '@/services/common';
import { formItemLayout410 } from '@/utils/const';
import ExposePort from './components/ExposePort';
import TcpExposeType from './components/TcpExposeType';
import DomainPath from './components/DomainPath';
import InstancePort from './components/InstancePort';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';

interface ParamProps {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	name: string;
	chartVersion: string;
	exposeId?: string;
	external?: string;
	mode?: string;
}
export default function AddIngress(): JSX.Element {
	const [form] = Form.useForm();
	const params: ParamProps = useParams();
	const {
		clusterId,
		namespace,
		middlewareName,
		name,
		exposeId,
		external,
		mode,
		chartVersion
	} = params;
	const [spinning, setSpinning] = useState<boolean>(false);
	const [loading, setLoading] = useState<boolean>(false);
	const [ingresses, setIngresses] = useState<IngressItemProps[]>([]);
	const [serviceIngressTypes, setServiceIngressTypes] = useState<
		ServiceIngressTypeItem[]
	>([]);
	const [ingressPortArray, setIngressPortArray] = useState<string[]>([]);
	const [nodePortArray, setNodePortArray] = useState<string[]>([]);
	const [availablePorts, setAvailablePorts] = useState<AvailablePortItem[]>(
		[]
	);
	const [alarmVisible, setAlarmVisible] = useState<boolean>(false);
	const [serviceIngress, setServiceIngress] =
		useState<ServiceIngressTypeItem>();
	useEffect(() => {
		async function getAllData() {
			setSpinning(true);
			if (exposeId) {
				const res4 = await getServiceIngressDetail({
					clusterId: clusterId,
					namespace: namespace,
					middlewareName: middlewareName,
					middlewareType: name,
					chartVersion: chartVersion,
					exposeId
				});
				if (res4.success) {
					setServiceIngress(res4.data);
					form.setFieldsValue({
						ingressType: res4.data.exposeType
					});
					res4.data.serviceDTOList &&
						res4.data.serviceDTOList.sort(
							(a: any, b: any) => a?.exposePort - b?.exposePort
						);
					if (res4.data.tcpExposeType === 'nodePort') {
						if (name === 'redis') {
							if (mode === 'cluster') {
								form.setFieldsValue({
									tcpExposeType: 'NodePort',
									exposePort:
										res4.data.serviceDTOList[0].exposePort,
									instancePort: {
										port: res4.data.serviceDTOList?.[0]
											?.exposePort,
										checked:
											res4.data.skipPortConflict || false
									}
								});
							} else {
								form.setFieldsValue({
									tcpExposeType: 'NodePort',
									exposePort:
										res4.data.serviceDTOList[0].exposePort,
									instancePort: {
										port: res4.data.serviceDTOList?.[1]
											?.exposePort,
										checked:
											res4.data.skipPortConflict || false
									}
								});
							}
						} else {
							form.setFieldsValue({
								tcpExposeType: 'NodePort',
								exposePort:
									res4.data.serviceDTOList[0].exposePort
							});
							if (name === 'kafka' || name === 'rocketmq') {
								const temp = res4.data.serviceDTOList.map(
									(item: any) => {
										form.setFieldsValue({
											[`${item.serviceName}`]:
												item.exposePort
										});
									}
								);
							}
						}
					} else {
						if (name === 'redis') {
							if (mode === 'cluster') {
								form.setFieldsValue({
									tcpExposeType: 'Ingress',
									ingressClassName:
										res4.data.ingressClassName,
									exposePort:
										res4.data.serviceDTOList[0].exposePort,
									instancePort: {
										port: res4.data.serviceDTOList?.[0]
											?.exposePort,
										checked:
											res4.data.skipPortConflict || false
									}
								});
							} else {
								form.setFieldsValue({
									tcpExposeType: 'Ingress',
									ingressClassName:
										res4.data.ingressClassName,
									exposePort:
										res4.data.serviceDTOList[0].exposePort,
									instancePort: {
										port: res4.data.serviceDTOList?.[1]
											?.exposePort,
										checked:
											res4.data.skipPortConflict || false
									}
								});
							}
						} else {
							form.setFieldsValue({
								tcpExposeType: 'Ingress',
								ingressClassName: res4.data.ingressClassName,
								exposePort:
									res4.data.serviceDTOList[0].exposePort
							});
							if (name === 'kafka' || name === 'rocketmq') {
								const temp = res4.data.serviceDTOList.map(
									(item: any) => {
										form.setFieldsValue({
											[`${item.serviceName}`]:
												item.exposePort
										});
									}
								);
							}
						}
					}
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res4.errorMsg}</p>
								<p>{res4.errorDetail}</p>
							</>
						)
					});
				}
			}
			if (
				(name === 'kafka' || name === 'rocketmq') &&
				external === 'false' &&
				mode !== 'dledger'
			) {
				setAlarmVisible(true);
			}
			const res = await getServiceIngressType({
				clusterId,
				namespace,
				middlewareName,
				middlewareType: name,
				chartVersion
			});
			if (res.success) {
				if (external === 'true') {
					const list = res.data.filter(
						(item) => item.exposeType !== 'external'
					);
					setServiceIngressTypes(list);
				} else {
					setServiceIngressTypes(res.data);
				}
				if (!exposeId) {
					form.setFieldsValue({
						ingressType: res.data[0].exposeType
					});
				}
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
			const res1 = await getIngresses({ clusterId: clusterId });
			if (res1.success) {
				setIngresses(res1.data);
			} else {
				notification.error({
					message: '失败',
					description: res1.errorMsg
				});
			}
			const res2 = await getIngressTCPPort();
			if (res2.success) {
				setIngressPortArray(res2.data.split('-'));
			}
			const res3 = await getNodePort();
			if (res3.success) {
				setNodePortArray(res3.data.split('-'));
			}
			const res4 = await getAvailablePorts({
				clusterId,
				namespace,
				middlewareName
			});
			if (res4.success) {
				setAvailablePorts(res4.data);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res4.errorMsg}</p>
							<p>{res4.errorDetail}</p>
						</>
					)
				});
			}
			setSpinning(false);
		}
		getAllData();
	}, []);
	const onValuesChange = (changed: any, all: any) => {
		console.log(changed);
		if (changed?.tcpExposeType === 'NodePort') {
			const cur = availablePorts.find(
				(item) => item.tcpExposeType === 'nodePort'
			);
			if (name === 'redis' && mode === 'cluster') {
				form.setFieldsValue({
					exposePort: cur?.portList[0],
					instancePort: {
						port: cur?.portList[0]
					},
					ingressClassName: undefined
				});
			} else {
				form.setFieldsValue({
					exposePort: cur?.portList[0],
					instancePort: {
						port: cur?.portList[1]
					},
					ingressClassName: undefined
				});
			}
			form.validateFields(['exposePort', 'instancePort']);
			if (name === 'kafka') {
				const curIngressTemp = serviceIngressTypes
					.find((item) => item.exposeType === all.ingressType)
					?.serviceDTOList.map((item: any, index: number) => {
						form.setFieldsValue({
							[`${item.serviceName}`]: cur?.portList[index]
						});
						form.validateFields([`${item.serviceName}`]);
					});
				return;
			} else {
				const curIngressTemp = serviceIngressTypes
					.find((item) => item.exposeType === all.ingressType)
					?.serviceDTOList.map((item: any, index: number) => {
						form.setFieldsValue({
							[`${item.serviceName}`]: cur?.portList[index + 1]
						});
						form.validateFields([`${item.serviceName}`]);
					});
				return;
			}
		}
		if (changed?.tcpExposeType === 'Ingress') {
			const cur = availablePorts.find(
				(item) => item.ingressClassName === all.ingressClassName
			);
			if (name === 'redis' && mode === 'cluster') {
				form.setFieldsValue({
					exposePort: cur?.portList[0],
					instancePort: {
						port: cur?.portList[0]
					}
				});
			} else {
				form.setFieldsValue({
					exposePort: cur?.portList[0],
					instancePort: {
						port: cur?.portList[1]
					}
				});
			}
			form.validateFields(['exposePort', 'instancePort']);
			if (name === 'kafka') {
				const curIngressTemp = serviceIngressTypes
					.find((item) => item.exposeType === all.ingressType)
					?.serviceDTOList.map((item: any, index: number) => {
						form.setFieldsValue({
							[`${item.serviceName}`]: cur?.portList[index]
						});
						form.validateFields([`${item.serviceName}`]);
					});
				return;
			} else {
				const curIngressTemp = serviceIngressTypes
					.find((item) => item.exposeType === all.ingressType)
					?.serviceDTOList.map((item: any, index: number) => {
						form.setFieldsValue({
							[`${item.serviceName}`]: cur?.portList[index + 1]
						});
						form.validateFields([`${item.serviceName}`]);
					});
				return;
			}
		}
		if (Object.keys(changed)[0] === 'ingressClassName') {
			const cur = availablePorts.find(
				(item) => item.ingressClassName === changed.ingressClassName
			);
			if (name === 'redis' && mode === 'cluster') {
				form.setFieldsValue({
					exposePort: cur?.portList[0],
					instancePort: {
						port: cur?.portList[0]
					}
				});
				form.validateFields(['exposePort', 'instancePort']);
			} else {
				form.setFieldsValue({
					exposePort: cur?.portList[0],
					instancePort: {
						port: cur?.portList[1]
					}
				});
				form.validateFields(['exposePort', 'instancePort']);
			}
			if (name === 'kafka') {
				const curIngressTemp = serviceIngressTypes
					.find((item) => item.exposeType === all.ingressType)
					?.serviceDTOList.map((item: any, index: number) => {
						form.setFieldsValue({
							[`${item.serviceName}`]: cur?.portList[index]
						});
						form.validateFields([`${item.serviceName}`]);
					});
				return;
			} else {
				const curIngressTemp = serviceIngressTypes
					.find((item) => item.exposeType === all.ingressType)
					?.serviceDTOList.map((item: any, index: number) => {
						form.setFieldsValue({
							[`${item.serviceName}`]: cur?.portList[index + 1]
						});
						form.validateFields([`${item.serviceName}`]);
					});
				return;
			}
		}
	};
	const handleSubmit = async () => {
		await form.validateFields();
		const values = form.getFieldsValue();
		const curService = serviceIngressTypes.find(
			(item) => item.exposeType === values.ingressType
		);
		const curIngressClass = ingresses.find(
			(item) => item.ingressClassName === values.ingressClassName
		);
		const tcpExposeTypeTemp =
			values.tcpExposeType === 'Ingress'
				? curIngressClass?.type === 'nginx'
					? 'nginx'
					: 'traefik'
				: 'nodePort';
		let serviceDTOList = [
			{
				exposePort: values.exposePort,
				serviceName: curService?.serviceDTOList[0].serviceName
			}
		];
		if (values.ingressType === 'external') {
			if (name === 'redis') {
				serviceDTOList = curService?.serviceDTOList.map(
					(item: any, index: number) => {
						if (mode === 'sentinel') {
							if (index === 0) {
								return {
									serviceName: item.serviceName,
									exposePort: values.exposePort
								};
							}
							if (
								!!exposeId &&
								values.instancePort.port !==
									serviceIngress?.serviceDTOList?.[0]
										.exposePort
							) {
								return {
									serviceName: item.serviceName,
									exposePort:
										serviceIngress?.serviceDTOList.find(
											(i: any) =>
												i.serviceName ===
												item.serviceName
										).exposePort
								};
							} else {
								return {
									serviceName: item.serviceName,
									exposePort:
										values.instancePort.port + index - 1
								};
							}
						} else {
							if (
								!!exposeId &&
								values.instancePort.port !==
									serviceIngress?.serviceDTOList?.[0]
										.exposePort
							) {
								return {
									serviceName: item.serviceName,
									exposePort:
										serviceIngress?.serviceDTOList.find(
											(i: any) =>
												i.serviceName ===
												item.serviceName
										).exposePort
								};
							} else {
								return {
									serviceName: item.serviceName,
									exposePort: values.instancePort.port + index
								};
							}
						}
					}
				);
			} else {
				serviceDTOList = curService?.serviceDTOList.map((item: any) => {
					return {
						serviceName: item.serviceName,
						exposePort: values[item.serviceName]
					};
				});
			}
		}
		let rules = null;
		if (values.protocol === 'HTTP') {
			rules = [
				{
					host: values.domainPath.host,
					ingressHttpPaths: [
						{
							path: values.domainPath.path,
							serviceName:
								curService?.serviceDTOList[0].serviceName
						}
					]
				}
			];
		}
		let sendData: any = {
			clusterId,
			namespace,
			middlewareName,
			chartVersion,
			middlewareType: name,
			exposeType: curService?.exposeType,
			protocol: values.protocol ? values.protocol : 'TCP',
			ingressClassName: values.ingressClassName,
			tcpExposeType: tcpExposeTypeTemp,
			serviceDTOList: serviceDTOList,
			rules: rules
		};
		if (values.instancePort) {
			sendData = {
				skipPortConflict: values.instancePort?.checked,
				...sendData
			};
		}
		if (serviceIngress) {
			sendData = {
				exposeId: serviceIngress.exposeId,
				...sendData
			};
		}
		setLoading(true);
		// console.log(sendData);
		await ExecuteOrderFuc();
		if (serviceIngress) {
			updateServiceIngress(sendData)
				.then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '服务暴露编辑成功'
						});
						window.history.back();
					} else {
						if (res.code == 600014) {
							notification.warning({
								message: '提示',
								description: res.errorMsg
							});
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					}
				})
				.finally(() => {
					setLoading(false);
				});
		} else {
			createServiceIngress(sendData)
				.then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '服务暴露添加成功'
						});
						window.history.back();
					} else {
						if (res.code == 600014) {
							notification.warning({
								message: '提示',
								description: res.errorMsg
							});
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					}
				})
				.finally(() => {
					setLoading(false);
				});
		}
	};
	return (
		<ProPage>
			<ProHeader
				title={`服务暴露${serviceIngress ? '编辑' : '新增'}`}
				onBack={() => window.history.back()}
			/>
			<ProContent>
				<Spin spinning={spinning}>
					{alarmVisible && (
						<Alert
							message="您好！选择集群外访问服务暴露后需要重启服务！创建完成后将无法删除服务暴露，请谨慎操作！"
							type="info"
							showIcon
							style={{ marginBottom: 8 }}
						/>
					)}
					<Form
						form={form}
						{...formItemLayout410}
						labelAlign="left"
						onValuesChange={onValuesChange}
						validateTrigger={['onBlur', 'onSubmit']}
					>
						<h2>暴露服务</h2>
						<Form.Item
							label="暴露服务"
							rules={[
								{
									required: true,
									message: '请选择暴露服务'
								}
							]}
							name="ingressType"
						>
							<IngressTypeContent
								options={serviceIngressTypes}
								disabled={!!serviceIngress}
							/>
						</Form.Item>
						<h2>暴露方式</h2>
						<Form.Item noStyle shouldUpdate>
							{({ getFieldValue }) => {
								if (
									getFieldValue('ingressType') ===
										'manager' ||
									getFieldValue('ingressType') === 'console'
								) {
									return (
										<>
											<Form.Item
												name="protocol"
												label="暴露方式"
												required
												initialValue="TCP"
											>
												<Select
													disabled={!!serviceIngress}
												>
													<Select.Option value="TCP">
														四层网络暴露
													</Select.Option>
													<Select.Option value="HTTP">
														七层网络暴露
													</Select.Option>
												</Select>
											</Form.Item>
											<Form.Item noStyle shouldUpdate>
												{({ getFieldValue }) => {
													if (
														getFieldValue(
															'protocol'
														) === 'TCP'
													) {
														return (
															<Form.Item
																label=" "
																colon={false}
																name="tcpExposeType"
																initialValue="Ingress"
															>
																<TcpExposeType
																	disabled={
																		!!serviceIngress
																	}
																/>
															</Form.Item>
														);
													} else {
														return null;
													}
												}}
											</Form.Item>
										</>
									);
								} else {
									return (
										<Form.Item
											label="暴露方式"
											name="tcpExposeType"
											rules={[
												{
													required: true,
													message: '暴露方式不能为空'
												}
											]}
											initialValue="Ingress"
										>
											<Select
												placeholder="请选择暴露方式"
												disabled={!!serviceIngress}
											>
												<Select.Option
													value="NodePort"
													key="NodePort"
												>
													NodePort
												</Select.Option>
												<Select.Option
													value="Ingress"
													key="Ingress"
												>
													Ingress-TCP
												</Select.Option>
											</Select>
										</Form.Item>
									);
								}
							}}
						</Form.Item>
						<Form.Item noStyle shouldUpdate>
							{({ getFieldValue }) => {
								if (
									getFieldValue('tcpExposeType') === 'Ingress'
								) {
									return (
										<Form.Item
											label="负载均衡选择"
											name="ingressClassName"
											rules={[
												{
													required: true,
													message: '负载均衡不能为空'
												}
											]}
										>
											<Select
												placeholder="请选择负载均衡"
												disabled={!!serviceIngress}
												dropdownMatchSelectWidth={false}
											>
												{ingresses.map(
													(
														item: IngressItemProps
													) => {
														return (
															<Select.Option
																key={
																	item.ingressClassName
																}
																value={
																	item.ingressClassName
																}
															>
																<div className="flex-space-between">
																	{
																		item.ingressClassName
																	}
																	<Tag
																		color={
																			item.type ===
																			'nginx'
																				? 'cyan'
																				: 'green'
																		}
																	>
																		{
																			item.type
																		}
																	</Tag>
																</div>
															</Select.Option>
														);
													}
												)}
											</Select>
										</Form.Item>
									);
								} else {
									return null;
								}
							}}
						</Form.Item>
						<Form.Item noStyle shouldUpdate>
							{({ getFieldValue }) => {
								if (
									getFieldValue('ingressType') ===
										'external' &&
									name === 'rocketmq'
								) {
									const curIngress = serviceIngressTypes.find(
										(item) =>
											item.exposeType ===
											getFieldValue('ingressType')
									);
									return (
										<>
											<ExposePort
												label="服务代理proxy-0端口配置"
												name={
													curIngress
														?.serviceDTOList[0]
														.serviceName
												}
												nodePortArray={nodePortArray}
												ingressPortArray={
													ingressPortArray
												}
												ingresses={ingresses}
												clusterId={clusterId}
												namespace={namespace}
												middlewareName={middlewareName}
												middlewareType={name}
												chartVersion={chartVersion}
											/>
											<ExposePort
												label="服务代理proxy-1端口配置"
												name={
													curIngress
														?.serviceDTOList[1]
														.serviceName
												}
												nodePortArray={nodePortArray}
												ingressPortArray={
													ingressPortArray
												}
												ingresses={ingresses}
												clusterId={clusterId}
												namespace={namespace}
												middlewareName={middlewareName}
												middlewareType={name}
												chartVersion={chartVersion}
											/>
										</>
									);
								} else {
									return null;
								}
							}}
						</Form.Item>
						<Form.Item noStyle shouldUpdate>
							{({ getFieldValue }) => {
								if (
									getFieldValue('ingressType') ===
										'external' &&
									name === 'kafka'
								) {
									return (
										<>
											<Form.Item
												name="autoConfig"
												label="自动配置broker对外端口"
												required
												initialValue={
													serviceIngress
														? false
														: true
												}
												valuePropName="checked"
											>
												<Switch />
											</Form.Item>
											<Form.Item noStyle shouldUpdate>
												{({ getFieldValue }) => {
													if (
														getFieldValue(
															'autoConfig'
														) === false
													) {
														const curIngress =
															serviceIngressTypes.find(
																(item) =>
																	item.exposeType ===
																	getFieldValue(
																		'ingressType'
																	)
															);
														return (
															<>
																{curIngress?.serviceDTOList.map(
																	(
																		item: any,
																		index: number
																	) => {
																		return (
																			<ExposePort
																				key={
																					index
																				}
																				name={
																					item.serviceName
																				}
																				label={`broker对外端口${index}配置`}
																				nodePortArray={
																					nodePortArray
																				}
																				ingressPortArray={
																					ingressPortArray
																				}
																				ingresses={
																					ingresses
																				}
																				clusterId={
																					clusterId
																				}
																				namespace={
																					namespace
																				}
																				middlewareName={
																					middlewareName
																				}
																				middlewareType={
																					name
																				}
																				chartVersion={
																					chartVersion
																				}
																			/>
																		);
																	}
																)}
															</>
														);
													} else {
														return null;
													}
												}}
											</Form.Item>
										</>
									);
								} else if (
									(getFieldValue('ingressType') ===
										'manager' ||
										getFieldValue('ingressType') ===
											'console') &&
									getFieldValue('protocol') === 'HTTP'
								) {
									return (
										<Form.Item
											label="域名路径"
											required
											name="domainPath"
											rules={[
												{
													validator: (
														_: any,
														value: any
													) => {
														if (!value) {
															return Promise.reject(
																new Error(
																	'请输入域名和路径'
																)
															);
														}
														if (!value.host) {
															return Promise.reject(
																new Error(
																	'请输入域名'
																)
															);
														}
														if (!value.path) {
															return Promise.reject(
																new Error(
																	'请输入路径'
																)
															);
														}
														return Promise.resolve();
													}
												}
											]}
										>
											<DomainPath />
										</Form.Item>
									);
								} else if (
									getFieldValue('ingressType') ===
										'external' &&
									name === 'redis' &&
									mode === 'sentinel'
								) {
									return (
										<>
											<ExposePort
												label="哨兵端口配置"
												name="exposePort"
												nodePortArray={nodePortArray}
												ingressPortArray={
													ingressPortArray
												}
												ingresses={ingresses}
												clusterId={clusterId}
												namespace={namespace}
												middlewareName={middlewareName}
												middlewareType={name}
												chartVersion={chartVersion}
											/>
											<InstancePort
												nodePortArray={nodePortArray}
												ingressPortArray={
													ingressPortArray
												}
												ingresses={ingresses}
												serviceIngressTypes={
													serviceIngressTypes
												}
												clusterId={clusterId}
												namespace={namespace}
												middlewareName={middlewareName}
												middlewareType={name}
												chartVersion={chartVersion}
												mode={mode}
											/>
										</>
									);
								} else if (
									getFieldValue('ingressType') ===
										'external' &&
									name === 'redis' &&
									mode === 'cluster'
								) {
									return (
										<InstancePort
											nodePortArray={nodePortArray}
											ingressPortArray={ingressPortArray}
											ingresses={ingresses}
											serviceIngressTypes={
												serviceIngressTypes
											}
											clusterId={clusterId}
											namespace={namespace}
											middlewareName={middlewareName}
											middlewareType={name}
											chartVersion={chartVersion}
											mode={mode}
										/>
									);
								} else if (
									name === 'rocketmq' &&
									getFieldValue('ingressType') === 'external'
								) {
									const curIngress = serviceIngressTypes.find(
										(item) =>
											item.exposeType ===
											getFieldValue('ingressType')
									);
									return (
										<>
											{curIngress?.serviceDTOList.map(
												(item: any, index: number) => {
													if (
														index !== 0 &&
														index !== 1
													) {
														return (
															<ExposePort
																key={index}
																name={
																	item.serviceName
																}
																label={`对外端口${
																	index - 2
																}配置`}
																nodePortArray={
																	nodePortArray
																}
																ingressPortArray={
																	ingressPortArray
																}
																ingresses={
																	ingresses
																}
																disabled={
																	!!serviceIngress
																}
																clusterId={
																	clusterId
																}
																namespace={
																	namespace
																}
																middlewareName={
																	middlewareName
																}
																middlewareType={
																	name
																}
																chartVersion={
																	chartVersion
																}
															/>
														);
													} else {
														return null;
													}
												}
											)}
										</>
									);
								} else {
									return (
										<ExposePort
											label="对外端口配置"
											name="exposePort"
											nodePortArray={nodePortArray}
											ingressPortArray={ingressPortArray}
											ingresses={ingresses}
											clusterId={clusterId}
											namespace={namespace}
											middlewareName={middlewareName}
											middlewareType={name}
											chartVersion={chartVersion}
										/>
									);
								}
							}}
						</Form.Item>
						<Divider style={{ marginTop: 40 }} />
						<Button
							type="primary"
							onClick={handleSubmit}
							style={{ marginRight: 16 }}
							loading={loading}
						>
							确定
						</Button>
						<Button onClick={() => window.history.back()}>
							取消
						</Button>
					</Form>
				</Spin>
			</ProContent>
		</ProPage>
	);
}

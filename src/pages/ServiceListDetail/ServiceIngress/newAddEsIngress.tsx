import React, { useEffect, useState } from 'react';
import {
	Spin,
	Form,
	Select,
	notification,
	Tag,
	Divider,
	Button,
	Input,
	Row,
	Col,
	Space
} from 'antd';
import { useParams } from 'react-router';
import {
	CloseCircleOutlined,
	DribbbleOutlined,
	PlusCircleOutlined
} from '@ant-design/icons';
import { ProContent, ProHeader, ProPage } from '@/components/ProPage';
import { formItemLayout410 } from '@/utils/const';
import {
	createServiceIngress,
	getAvailablePorts,
	getServiceIngressDetail,
	getServiceIngressType,
	updateServiceIngress
} from '@/services/ingress';
import {
	AvailablePortItem,
	IngressHttpPathsItem,
	ServiceIngressTypeItem
} from '../detail';
import IngressTypeContent from './components/IngressTypeContent';
import { IngressItemProps } from '@/pages/ResourcePoolManagement/resource.pool';
import {
	getIngressTCPPort,
	getIngresses,
	getNodePort
} from '@/services/common';
import ExposePort from './components/ExposePort';
import pattern from '@/utils/pattern';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';

interface ParamProps {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	name: string;
	chartVersion: string;
	exposeId?: string;
	enableExternal?: string;
	mode?: string;
}
export default function NewAddEsIngress(): JSX.Element {
	const [form] = Form.useForm();
	const params: ParamProps = useParams();
	const {
		clusterId,
		namespace,
		middlewareName,
		name,
		exposeId,
		enableExternal,
		mode,
		chartVersion
	} = params;
	const [spinning, setSpinning] = useState<boolean>(false);
	const [serviceIngressTypes, setServiceIngressTypes] = useState<
		ServiceIngressTypeItem[]
	>([]);
	const [serviceIngress, setServiceIngress] =
		useState<ServiceIngressTypeItem>();
	const [ingresses, setIngresses] = useState<IngressItemProps[]>([]);
	const [ingressPortArray, setIngressPortArray] = useState<string[]>([]);
	const [nodePortArray, setNodePortArray] = useState<string[]>([]);
	const [loading, setLoading] = useState<boolean>(false);
	const [httpPaths, setHttpPaths] = useState<IngressHttpPathsItem[]>([
		{ path: '', serviceName: '', servicePort: '', id: Math.random() }
	]);
	const [availablePorts, setAvailablePorts] = useState<AvailablePortItem[]>(
		[]
	);
	useEffect(() => {
		async function getAllData() {
			setSpinning(true);
			if (exposeId) {
				const res4 = await getServiceIngressDetail({
					clusterId: clusterId,
					namespace: namespace,
					middlewareName: middlewareName,
					middlewareType: name,
					chartVersion: chartVersion,
					exposeId
				});
				if (res4.success) {
					setServiceIngress(res4.data);
					form.setFieldsValue({
						protocol: res4.data.protocol,
						ingressType: res4.data.exposeType
					});
					if (res4.data.protocol === 'HTTP') {
						form.setFieldsValue({
							ingressClassName: res4.data.ingressClassName,
							host: res4.data.rules?.[0].host
						});
						setHttpPaths(res4.data.rules?.[0].ingressHttpPaths);
						const temp = res4.data.rules?.[0].ingressHttpPaths?.map(
							(item: any, index: number) => {
								form.setFieldsValue({
									[`service${index}`]: item.serviceName,
									[`path${index}`]: item.path
								});
							}
						);
					} else {
						if (res4.data.tcpExposeType === 'nodePort') {
							form.setFieldsValue({
								tcpExposeType: 'NodePort',
								exposePort:
									res4.data.serviceDTOList?.[0].exposePort
							});
						} else {
							form.setFieldsValue({
								tcpExposeType: 'Ingress',
								ingressClassName: res4.data.ingressClassName,
								exposePort:
									res4.data.serviceDTOList?.[0].exposePort
							});
						}
					}
				}
			}
			const res = await getServiceIngressType({
				clusterId,
				namespace,
				middlewareName,
				middlewareType: name,
				chartVersion
			});
			if (res.success) {
				if (enableExternal === 'true') {
					const list = res.data.filter(
						(item) => item.exposeType !== 'external'
					);
					setServiceIngressTypes(list);
				} else {
					setServiceIngressTypes(res.data);
				}
				if (!exposeId) {
					form.setFieldsValue({
						ingressType: res.data[0].exposeType
					});
				}
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
			const res1 = await getIngresses({ clusterId: clusterId });
			if (res1.success) {
				setIngresses(res1.data);
			} else {
				notification.error({
					message: '失败',
					description: res1.errorMsg
				});
			}
			const res2 = await getIngressTCPPort();
			if (res2.success) {
				setIngressPortArray(res2.data.split('-'));
			}
			const res3 = await getNodePort();
			if (res3.success) {
				setNodePortArray(res3.data.split('-'));
			}
			const res4 = await getAvailablePorts({
				clusterId,
				namespace,
				middlewareName
			});
			if (res4.success) {
				setAvailablePorts(res4.data);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res4.errorMsg}</p>
							<p>{res4.errorDetail}</p>
						</>
					)
				});
			}
			setSpinning(false);
		}
		getAllData();
	}, []);
	const deleteHttpPath = (record: IngressHttpPathsItem) => {
		setHttpPaths(
			httpPaths.filter(
				(item: IngressHttpPathsItem) => item.id !== record.id
			)
		);
	};
	const addHttpPath = () => {
		setHttpPaths([
			...httpPaths,
			{
				path: '',
				serviceName: '',
				servicePort: '',
				id: Math.random()
			}
		]);
	};
	const handleChange = (
		value: string,
		record: IngressHttpPathsItem,
		type: string
	) => {
		const lt = httpPaths.map((item) => {
			if (item.id === record.id) {
				switch (type) {
					case 'serviceName':
						item.serviceName = value;
						item.servicePort = serviceIngressTypes.find(
							(item) =>
								item.serviceDTOList[0].serviceName === value
						)?.serviceDTOList[0].servicePort;
						break;
					case 'path':
						item.path = value;
						break;
					default:
						break;
				}
			}
			return item;
		});
		setHttpPaths(lt);
	};
	const onValuesChange = (changed: any, all: any) => {
		if (all.protocol === 'TCP') {
			if (changed.tcpExposeType === 'NodePort') {
				const cur = availablePorts.find(
					(item) => item.tcpExposeType === 'nodePort'
				);
				form.setFieldsValue({
					exposePort: cur?.portList[0]
				});
				form.validateFields(['exposePort']);
				return;
			}
			if (changed.tcpExposeType === 'Ingress') {
				const cur = availablePorts.find(
					(item) => item.ingressClassName === all.ingressClassName
				);
				form.setFieldsValue({
					exposePort: cur?.portList[0]
				});
				form.validateFields(['exposePort']);
				return;
			}
			if (Object.keys(changed)[0] === 'ingressClassName') {
				const cur = availablePorts.find(
					(item) => item.ingressClassName === changed.ingressClassName
				);
				form.setFieldsValue({
					exposePort: cur?.portList[0]
				});
				form.validateFields(['exposePort']);
				return;
			}
		}
	};
	const handleSubmit = async () => {
		await form.validateFields();
		const values = form.getFieldsValue();
		const curService = serviceIngressTypes.find(
			(item) => item.exposeType === values.ingressType
		);
		const curIngressClass = ingresses.find(
			(item) => item.ingressClassName === values.ingressClassName
		);
		const tcpExposeTypeTemp =
			values.tcpExposeType === 'Ingress'
				? curIngressClass?.type === 'nginx'
					? 'nginx'
					: 'traefik'
				: 'nodePort';
		const serviceDTOList = [
			{
				exposePort: values.exposePort,
				serviceName: curService?.serviceDTOList[0].serviceName
			}
		];
		if (values.protocol === 'HTTP') {
			if (
				httpPaths.some(
					(item: IngressHttpPathsItem) =>
						item.path === '' || item.serviceName === ''
				)
			) {
				notification.warning({
					message: '提示',
					description: '请添加服务暴露和路径'
				});
				return;
			}
		}
		let rules = null;
		if (values.protocol === 'HTTP') {
			rules = [
				{
					host: values.host,
					ingressHttpPaths: httpPaths
				}
			];
		}
		let sendData: any = {
			clusterId,
			namespace,
			middlewareName,
			chartVersion,
			middlewareType: name,
			exposeType: curService?.exposeType,
			protocol: values.protocol ? values.protocol : 'TCP',
			ingressClassName: values.ingressClassName,
			tcpExposeType: tcpExposeTypeTemp,
			serviceDTOList: serviceDTOList,
			rules: rules
		};
		if (exposeId) {
			sendData = {
				...sendData,
				exposeId: exposeId
			};
		}
		console.log(sendData);
		setLoading(true);
		await ExecuteOrderFuc();
		if (serviceIngress) {
			updateServiceIngress(sendData)
				.then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '服务暴露编辑成功'
						});
						window.history.back();
					} else {
						if (res.code == 600014) {
							notification.warning({
								message: '提示',
								description: res.errorMsg
							});
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					}
				})
				.finally(() => {
					setLoading(false);
				});
		} else {
			createServiceIngress(sendData)
				.then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '服务暴露添加成功'
						});
						window.history.back();
					} else {
						if (res.code == 600014) {
							notification.warning({
								message: '提示',
								description: res.errorMsg
							});
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					}
				})
				.finally(() => {
					setLoading(false);
				});
		}
	};
	return (
		<ProPage>
			<ProHeader
				title={`服务暴露${serviceIngress ? '编辑' : '新增'}`}
				onBack={() => window.history.back()}
			/>
			<ProContent>
				<Spin spinning={spinning}>
					<Form
						{...formItemLayout410}
						form={form}
						labelAlign="left"
						onValuesChange={onValuesChange}
						validateTrigger={['onBlur', 'onSubmit']}
					>
						<h2>暴露方式</h2>
						<Form.Item
							label="暴露方式"
							required
							name="protocol"
							initialValue="TCP"
						>
							<Select disabled={!!serviceIngress}>
								<Select.Option value="TCP">
									四层网络暴露
								</Select.Option>
								<Select.Option value="HTTP">
									七层网络暴露
								</Select.Option>
							</Select>
						</Form.Item>
						<Form.Item noStyle shouldUpdate>
							{({ getFieldValue }) => {
								if (getFieldValue('protocol') === 'TCP') {
									return (
										<>
											<h2>暴露服务</h2>
											<Form.Item
												label="暴露服务"
												name="ingressType"
												rules={[
													{
														required: true,
														message:
															'请选择暴露服务'
													}
												]}
												required
											>
												<IngressTypeContent
													options={
														serviceIngressTypes
													}
													disabled={!!serviceIngress}
												/>
											</Form.Item>
											<h2>暴露配置</h2>
											<Form.Item
												required
												label="暴露方式"
												name="tcpExposeType"
												initialValue="Ingress"
											>
												<Select
													disabled={!!serviceIngress}
												>
													<Select.Option value="NodePort">
														NodePort
													</Select.Option>
													<Select.Option value="Ingress">
														Ingress-TCP
													</Select.Option>
												</Select>
											</Form.Item>
											<Form.Item noStyle shouldUpdate>
												{({ getFieldValue }) => {
													if (
														getFieldValue(
															'tcpExposeType'
														) === 'Ingress'
													) {
														return (
															<Form.Item
																label="负载均衡选择"
																name="ingressClassName"
																rules={[
																	{
																		required:
																			true,
																		message:
																			'请选择负载均衡'
																	}
																]}
															>
																<Select
																	placeholder="请选择负载均衡"
																	disabled={
																		!!serviceIngress
																	}
																	dropdownMatchSelectWidth={
																		false
																	}
																>
																	{ingresses.map(
																		(
																			item: IngressItemProps
																		) => {
																			return (
																				<Select.Option
																					key={
																						item.ingressClassName
																					}
																					value={
																						item.ingressClassName
																					}
																				>
																					<div className="flex-space-between">
																						{
																							item.ingressClassName
																						}
																						<Tag
																							color={
																								item.type ===
																								'nginx'
																									? 'cyan'
																									: 'green'
																							}
																						>
																							{
																								item.type
																							}
																						</Tag>
																					</div>
																				</Select.Option>
																			);
																		}
																	)}
																</Select>
															</Form.Item>
														);
													} else {
														return null;
													}
												}}
											</Form.Item>
											<ExposePort
												label="对外端口配置"
												name="exposePort"
												nodePortArray={nodePortArray}
												ingressPortArray={
													ingressPortArray
												}
												ingresses={ingresses}
												clusterId={clusterId}
												namespace={namespace}
												middlewareName={middlewareName}
												middlewareType={name}
												chartVersion={chartVersion}
											/>
										</>
									);
								} else {
									return null;
								}
							}}
						</Form.Item>
						<Form.Item noStyle shouldUpdate>
							{({ getFieldValue }) => {
								if (getFieldValue('protocol') === 'HTTP') {
									return (
										<>
											<h2>暴露服务</h2>
											<Form.Item
												name="ingressClassName"
												required
												label="负载均衡选择"
												rules={[
													{
														required: true,
														message:
															'请选择负载均衡'
													}
												]}
											>
												<Select
													disabled={!!serviceIngress}
													placeholder="请选择负载均衡"
												>
													{ingresses.map(
														(
															item: IngressItemProps
														) => {
															return (
																<Select.Option
																	key={
																		item.ingressClassName
																	}
																	value={
																		item.ingressClassName
																	}
																>
																	<div className="flex-space-between">
																		{
																			item.ingressClassName
																		}
																		<Tag
																			color={
																				item.type ===
																				'nginx'
																					? 'cyan'
																					: 'green'
																			}
																		>
																			{
																				item.type
																			}
																		</Tag>
																	</div>
																</Select.Option>
															);
														}
													)}
												</Select>
											</Form.Item>
											<Form.Item
												name="host"
												label="域名"
												required
												rules={[
													{
														required: true,
														message: '请填写域名'
													},
													{
														pattern: new RegExp(
															pattern.esDomain
														),
														message:
															'请输入正确的域名格式'
													}
												]}
											>
												<Input
													disabled={!!serviceIngress}
													placeholder="请输入域名"
												/>
											</Form.Item>
											{httpPaths.map(
												(
													item: IngressHttpPathsItem,
													index: number
												) => {
													return (
														<Row key={item.id}>
															<Col span={4}></Col>
															<Col span={10}>
																<div className="add-list-item-box">
																	<DribbbleOutlined
																		style={{
																			fontSize: 24,
																			color: '#8a8a8a'
																		}}
																	/>
																	<Form.Item
																		labelCol={{
																			span: 8
																		}}
																		wrapperCol={{
																			span: 12
																		}}
																		style={{
																			marginBottom: 0
																		}}
																		labelAlign="right"
																		label="暴露服务"
																		required
																		name={`service${index}`}
																		initialValue={
																			item.serviceName
																		}
																	>
																		<Select
																			style={{
																				width: '140px'
																			}}
																			value={
																				item.serviceName
																			}
																			onChange={(
																				value
																			) =>
																				handleChange(
																					value,
																					item,
																					'serviceName'
																				)
																			}
																		>
																			{serviceIngressTypes.map(
																				(
																					st: ServiceIngressTypeItem
																				) => {
																					return (
																						<Select.Option
																							key={
																								st
																									.serviceDTOList[0]
																									.serviceName
																							}
																							value={
																								st
																									.serviceDTOList[0]
																									.serviceName
																							}
																							disabled={httpPaths.some(
																								(
																									hp
																								) =>
																									hp.serviceName ===
																									st
																										.serviceDTOList[0]
																										.serviceName
																							)}
																						>
																							{
																								st.aliasExposeType
																							}
																						</Select.Option>
																					);
																				}
																			)}
																		</Select>
																	</Form.Item>
																	<Form.Item
																		labelCol={{
																			span: 6
																		}}
																		wrapperCol={{
																			span: 12
																		}}
																		style={{
																			marginBottom: 0
																		}}
																		labelAlign="right"
																		label="路径"
																		name={`path${index}`}
																		required
																		initialValue={
																			item.path
																		}
																	>
																		<Input
																			value={
																				item.path
																			}
																			style={{
																				width: '140px'
																			}}
																			onChange={(
																				e: React.ChangeEvent<HTMLInputElement>
																			) =>
																				handleChange(
																					e
																						.target
																						.value,
																					item,
																					'path'
																				)
																			}
																		/>
																	</Form.Item>
																	<CloseCircleOutlined
																		className="add-list-item-closed"
																		style={{
																			visibility:
																				index !==
																				0
																					? 'initial'
																					: 'hidden'
																		}}
																		onClick={() =>
																			deleteHttpPath(
																				item
																			)
																		}
																	/>
																</div>
															</Col>
														</Row>
													);
												}
											)}
											{httpPaths.length < 2 && (
												<Row>
													<Col span={4}></Col>
													<Col span={10}>
														<Button
															type="dashed"
															block
															onClick={
																addHttpPath
															}
															disabled={
																!!serviceIngress
															}
														>
															<Space>
																<PlusCircleOutlined />
																新增
															</Space>
														</Button>
													</Col>
												</Row>
											)}
										</>
									);
								} else {
									return null;
								}
							}}
						</Form.Item>
						<Divider style={{ marginTop: 40 }} />
						<Button
							type="primary"
							onClick={handleSubmit}
							style={{ marginRight: 16 }}
							loading={loading}
						>
							确定
						</Button>
						<Button onClick={() => window.history.back()}>
							取消
						</Button>
					</Form>
				</Spin>
			</ProContent>
		</ProPage>
	);
}

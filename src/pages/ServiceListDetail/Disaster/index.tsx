import React, { useState, useEffect } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { Modal, notification, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd';
import {
	CheckCircleFilled,
	CloseCircleFilled,
	SyncOutlined
} from '@ant-design/icons';
import DataFields from '@/components/DataFields';

import {
	DisasterOriginCard,
	DisasterBackupCardNone,
	DisasterBackupCard
} from './DisasterCard';

import {
	switchDisasterIns,
	getMysqlExternal,
	unBindMysqlDisaster,
	mysqlRecovery
} from '@/services/middleware';

import './index.less';
import { getServiceIngress } from '@/services/ingress';
import { DetailParams } from '../detail';
import useRefresh from '@/utils/useRefresh';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances, status } from '@/utils/const';
import { controlledOperationDisabled } from '@/utils/utils';
import storage from '@/utils/storage';

const clusterInfo = {
	title: '服务详情'
};
const originDataInit = {
	cluster: '',
	namespace: '',
	name: '',
	dbUser: '',
	dbPass: '',
	address: '',
	port: '',
	recoveryPort: ''
};
const backupDataInit = {
	cluster: '',
	namespace: '',
	name: '',
	dbUser: '',
	dbPass: '',
	address: '',
	port: '',
	recoveryPort: ''
};
interface disasterProps {
	data: any;
	onRefresh: (key?: string) => void;
	toDetail: () => void;
}
interface OriginProps {
	cluster: string;
	namespace: string;
	name: string;
	dbUser: string;
	dbPass: string;
	address: string;
	port: string;
	recoveryPort?: string;
}
interface runStateProps {
	title: string;
	status: string;
	lastSwitchTime: string;
	lastUpdateTime: string;
}

export default function Disaster(props: disasterProps): JSX.Element {
	const history = useHistory();
	const { data, onRefresh, toDetail } = props;
	const {
		clusterId,
		name,
		namespace,
		middlewareName,
		chartVersion,
		type,
		aliasName
	}: DetailParams = useParams();
	const [originData, setOriginData] = useState<OriginProps>(originDataInit);
	const [backupData, setBackupData] = useState<OriginProps>(backupDataInit);
	const [runState, setRunState] = useState<runStateProps>({
		title: '应急切换',
		status: '',
		lastUpdateTime: '',
		lastSwitchTime: ''
	});
	// const [license, setLicense] = useState<boolean>(false);
	const [reason, setReason] = useState<string>();
	const [disabled, setDisabled] = useState<boolean>(false);
	const [spinning, setSpinning] = useState<boolean>(false);
	const [switchable, setSwitchable] = useState<boolean>(false);
	const disasterOperatorId =
		maintenances['Configure Disaster Recovery Relationship'];
	const [refreshKey, setRefreshKey] = useState<number>(0);
	const [reLoading, setReLoading] = useState<boolean>(false);

	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		async function getData() {
			setSpinning(true);
			await getDisasterData();
			if (
				data.mysqlDTO.isSource === null ||
				data.mysqlDTO.isSource === true
			) {
				const res2 = await getServiceIngress({
					clusterId,
					namespace,
					middlewareType: 'mysql',
					middlewareName,
					chartVersion
				});
				if (res2.success) {
					if (res2.data.length > 0) {
						setDisabled(false);
					} else {
						setDisabled(true);
						setReason(
							'当前服务未创建服务暴露，无法正常使用灾备能力，请先前往服务详情-服务暴露页创建服务暴露'
						);
					}
				} else {
					setDisabled(true);
					setReason('服务暴露列表接口获取异常，请重试！');
				}
			}
			setSpinning(false);
		}
		getData();
	}, [refreshKey]);
	const getDisasterData = async () => {
		const res = await getMysqlExternal({
			clusterId,
			namespace,
			mysqlName: middlewareName
		});
		if (res.success) {
			setOriginData({
				cluster: res?.data?.source?.clusterId || '',
				namespace: res?.data?.source?.namespace || '',
				name: res?.data?.source?.middlewareName || '',
				dbUser: res?.data?.source?.username || '',
				dbPass: res?.data?.source?.password || '',
				address: res?.data?.source?.address || '',
				port: res?.data?.source?.port || '',
				recoveryPort: res?.data?.source?.recoveryPort || ''
			});
			setBackupData({
				cluster: res?.data?.disasterRecovery?.clusterId || '',
				namespace: res?.data?.disasterRecovery?.namespace || '',
				name: res?.data?.disasterRecovery?.middlewareName || '',
				dbUser: res?.data?.disasterRecovery?.username || '',
				dbPass: res?.data?.disasterRecovery?.password || '',
				address: res?.data?.disasterRecovery?.address || '',
				port: res?.data?.disasterRecovery?.port || '',
				recoveryPort: res?.data?.disasterRecovery?.recoveryPort || ''
			});
			setRunState({
				title: '应急切换',
				status: res.data?.phase || '',
				lastSwitchTime: res.data?.lastSwitchTime || '--',
				lastUpdateTime: res.data.lastUpdateTime || '--'
			});
			setSwitchable(res.data.switchable);
		} else {
			notification.error({
				message: '失败',
				description: res.errorMsg
			});
		}
	};
	const items: any = [
		{
			dataIndex: 'title',
			render: (val: string) => (
				<div className="title-content">
					<div className="blue-line"></div>
					<div className="detail-title">{val}</div>
				</div>
			)
		}
	];
	const changeDisaster = () => {
		Modal.confirm({
			title: '操作',
			content: '是否确认进行切换？',
			onOk: async () => {
				const sendData = {
					clusterId: clusterId,
					namespace: namespace,
					mysqlName: middlewareName
				};
				await ExecuteOrderFuc();
				return switchDisasterIns(sendData)
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '服务切换成功'
							});
							onRefresh();
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					})
					.finally(() => {
						getDisasterData();
					});
			}
		});
	};
	const runItems: any = [
		{
			dataIndex: 'title',
			render: (val: string, record: any) => (
				<div>
					<div className="title-content">
						<div className="blue-line"></div>
						<div className="detail-title">{val}</div>
					</div>
					<div>
						<Button
							type="primary"
							disabled={
								!switchable ||
								controlledOperationDisabled(
									'expert',
									data?.lock
								)
							}
							onClick={() => {
								WorkOrderFuc(
									changeDisaster,
									data.lock,
									middlewareName,
									disasterOperatorId,
									history,
									type,
									name,
									aliasName,
									clusterId,
									namespace
								);
							}}
						>
							手动切换
						</Button>
						{record.status !== 'Running' &&
						record.status !== 'Syncing' &&
						record.status !== 'switching' &&
						((data?.mysqlDTO?.isSource &&
							data.status === 'Running') ||
							(!data?.mysqlDTO?.isSource &&
								data?.mysqlDTO?.phase === 'Running')) ? (
							<Button
								type="primary"
								onClick={recovery}
								loading={reLoading}
								style={{ marginLeft: '16px' }}
							>
								故障恢复
							</Button>
						) : null}
					</div>
				</div>
			),
			span: 24
		},
		{
			dataIndex: 'status',
			label: 'MySQL同步器状态',
			render: (val: any) => {
				if (val === 'Syncing') {
					return (
						<div>
							<CheckCircleFilled
								style={{
									color: '#00A700'
								}}
							/>{' '}
							{status[val]}
						</div>
					);
				} else if (val === 'StopSyncing') {
					return (
						<div>
							<SyncOutlined
								style={{
									color: '#0091FF'
								}}
							/>{' '}
							{status[val]}
						</div>
					);
				} else if (val === 'switching') {
					return (
						<div>
							<SyncOutlined
								style={{
									color: '#00A700'
								}}
								spin
							/>{' '}
							{status[val]}
						</div>
					);
				} else {
					return (
						<div>
							<CloseCircleFilled
								style={{
									color: '#C80000'
								}}
							/>{' '}
							错误
						</div>
					);
				}
			}
		},
		{
			dataIndex: 'lastUpdateTime',
			label: 'MySQL同步器最近同步时间'
		},
		{
			dataIndex: 'lastSwitchTime',
			label: '上次切换时间'
		}
	];
	const toCreateBackup: () => void = () => {
		if (disabled || controlledOperationDisabled('expert', data?.lock))
			return;
		WorkOrderFuc(
			() => {
				storage.setSession('backup', data);
				history.push(
					`/project/db/mysql/MySQL/mysqlDisaster/${chartVersion}/${clusterId}/${namespace}/${middlewareName}`
				);
			},
			data.lock,
			middlewareName,
			disasterOperatorId,
			history,
			type,
			name,
			aliasName,
			clusterId,
			namespace
		);
		// }
	};
	const deleteInstance: () => void = () => {
		Modal.confirm({
			title: '操作',
			content: '请确认是否解绑该服务？',
			onOk: async () => {
				const sendData = {
					clusterId,
					namespace,
					middlewareName
				};
				await ExecuteOrderFuc();
				return unBindMysqlDisaster(sendData)
					.then((res) => {
						if (res) {
							notification.success({
								message: '成功',
								description: '解绑成功'
							});
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					})
					.finally(() => {
						onRefresh('disaster');
					});
			}
		});
	};
	const recovery = () => {
		Modal.confirm({
			title: '操作确认',
			content:
				'进行故障恢复时，会引发灾备服务的备库重搭操作，请确保源服务运行正常，是否确定进行故障恢复?',
			onOk: () => {
				const sendData = {
					clusterId,
					namespace,
					middlewareName
				};
				setReLoading(true);
				mysqlRecovery(sendData)
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '恢复成功'
							});
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					})
					.finally(() => {
						setReLoading(false);
						onRefresh('disaster');
					});
			}
		});
	};
	return (
		<Spin spinning={spinning}>
			<div className="service-disaster">
				{reason && (
					<Alert
						message={reason}
						type="warning"
						showIcon
						closable
						style={{ marginBottom: 16 }}
					/>
				)}
				{data?.mysqlDTO?.openDisasterRecoveryMode ? (
					<>
						<DataFields
							dataSource={runState}
							items={runItems}
							className="refresh-color"
						/>
						<div className="detail-divider"></div>
					</>
				) : null}
				<DataFields dataSource={clusterInfo} items={items} />
				<div className="disaster-card-content">
					{data?.mysqlDTO?.openDisasterRecoveryMode ? (
						<>
							<DisasterOriginCard
								type={name}
								originData={originData}
								backupData={backupData}
								toBasicInfo={() => {
									data?.mysqlDTO?.isSource
										? onRefresh('basicInfo')
										: toDetail();
								}}
							/>
							<DisasterBackupCard
								type={name}
								lock={data?.lock}
								backupData={backupData}
								deleteInstance={() => {
									if (
										!controlledOperationDisabled(
											'expert',
											data?.lock
										)
									) {
										WorkOrderFuc(
											deleteInstance,
											data.lock,
											middlewareName,
											disasterOperatorId,
											history,
											type,
											name,
											aliasName,
											clusterId,
											namespace
										);
									}
								}}
								toDetail={() => {
									data?.mysqlDTO?.isSource
										? toDetail()
										: onRefresh('basicInfo');
								}}
							/>
						</>
					) : (
						<>
							<DisasterOriginCard
								type={name}
								data={data}
								originData={originData}
								backupData={backupData}
								toBasicInfo={() => {
									history.location.pathname ===
									'/disasterBackup/disasterCenter'
										? toDetail()
										: onRefresh('basicInfo');
								}}
							/>
							<DisasterBackupCardNone
								disabled={
									disabled ||
									controlledOperationDisabled(
										'expert',
										data?.lock
									)
								}
								type={name}
								toCreateBackup={toCreateBackup}
							/>
						</>
					)}
				</div>
			</div>
		</Spin>
	);
}

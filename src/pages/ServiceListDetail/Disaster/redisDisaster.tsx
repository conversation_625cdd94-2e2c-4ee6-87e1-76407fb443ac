import React, { useState, useEffect } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { Modal, notification, But<PERSON>, Spin, Alert } from 'antd';
import {
	CheckCircleFilled,
	CloseCircleOutlined,
	ExclamationCircleOutlined
} from '@ant-design/icons';
import DataFields from '@/components/DataFields';
import ArrowLine from '@/components/ArrowLine';

import {
	DisasterOriginCard,
	DisasterBackupCardNone,
	DisasterBackupCard
} from './DisasterCard';

import {
	getDisaster,
	switchServiceDisaster,
	cancelSwitch,
	removeDisaster,
	repairDisaster
} from '@/services/middleware';
import { DetailParams } from '../detail';
import { getComponent } from '@/services/common';
import semver from 'semver';

import './index.less';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';
import useRefresh from '@/utils/useRefresh';
import { controlledOperationDisabled } from '@/utils/utils';

const runStateInit = {
	title: '同步器信息',
	gcPhase: '',
	dataPhase: '',
	componentStatus: 0
};
interface disasterProps {
	data: any;
	onRefresh: (key?: string) => void;
	toDetail: () => void;
}
interface OriginProps {
	cluster: string;
	namespace: string;
	name: string;
	address: string;
	gcPort: string;
	localClusterPhase?: string;
	minioAddress?: string;
}
interface runStateProps {
	title: string;
	gcPhase: string;
	dataPhase: string;
	lastRestoreTime?: string;
	componentStatus?: number;
}

export default function RedisDisaster(props: disasterProps): JSX.Element {
	const { data, onRefresh, toDetail } = props;
	const disasterOperatorId =
		maintenances['Configure Disaster Recovery Relationship'];
	const params: DetailParams = useParams();
	const {
		clusterId,
		name,
		namespace,
		middlewareName,
		chartVersion,
		type,
		aliasName,
		currentTab
	} = params;
	const history = useHistory();
	const [originData, setOriginData] = useState<OriginProps>();
	const [backupData, setBackupData] = useState<OriginProps>();
	const [runState, setRunState] = useState<runStateProps>(runStateInit);
	const [loading, setLoading] = useState<boolean>(true);
	const [isSwitch, setIsSwitch] = useState<boolean>(false);
	const [canFix, setCanFix] = useState<boolean>(false);
	const [component, setComponent] = useState<number>();
	const [backupComponent, setBackupComponent] = useState<number>();
	const [slaveComponent, setSlaveComponent] = useState<number>();
	const [reason, setReason] = useState<string>();
	const [disabled, setDisabled] = useState<boolean>(false);
	const [refreshKey, setRefreshKey] = useState<number>(0);
	const [runItems, setRunItems] = useState([
		{
			dataIndex: 'title',
			render: (val: string) => (
				<div className="display-flex flex-space-between">
					<div className="title-content">
						<div className="blue-line"></div>
						<div className="detail-title">{val}</div>
					</div>
				</div>
			),
			span: 24
		},
		{
			dataIndex: 'gcPhase',
			label: '灾备同步器状态',
			render: (val: any) => {
				if (val === 'running') {
					return (
						<div>
							<CheckCircleFilled
								style={{
									color: '#00A700',
									marginRight: 4
								}}
							/>
							运行正常
						</div>
					);
				} else if (val === 'uninstall') {
					return (
						<div>
							<ExclamationCircleOutlined
								style={{
									color: '#faad14',
									marginRight: 4
								}}
							/>
							未安装
						</div>
					);
				} else {
					return (
						<div>
							<CloseCircleOutlined
								style={{
									color: '#ff4d4f',
									marginRight: 4
								}}
							/>
							运行异常
						</div>
					);
				}
			}
		},
		{
			dataIndex: 'dataPhase',
			label: '数据同步器状态',
			render: (val: any) => {
				if (val === 'running') {
					return (
						<div>
							<CheckCircleFilled
								style={{
									color: '#00A700',
									marginRight: 4
								}}
							/>
							运行正常
						</div>
					);
				} else if (val === 'uninstall') {
					return (
						<div>
							<ExclamationCircleOutlined
								style={{
									color: '#faad14',
									marginRight: 4
								}}
							/>
							未安装
						</div>
					);
				} else {
					return (
						<div>
							<CloseCircleOutlined
								style={{
									color: '#ff4d4f',
									marginRight: 4
								}}
							/>
							运行异常
						</div>
					);
				}
			}
		}
	]);

	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));

	useEffect(() => {
		async function getData() {
			await getDetail();
			await getComponent({
				clusterId,
				componentName: 'gossip-operator'
			}).then((res) => {
				if (res.success) {
					setComponent(res.data[clusterId]?.status);
				}
			});
		}
		getData();
	}, [refreshKey]);
	useEffect(() => {
		const dataIndexList = runItems.map((item) => item.dataIndex);
		if (
			name === 'elasticsearch' &&
			typeof backupComponent !== 'undefined'
		) {
			if (
				!dataIndexList.includes('componentStatus') &&
				!dataIndexList.includes('lastRestoreTime')
			) {
				setRunItems([
					...runItems,
					{
						dataIndex: 'componentStatus',
						label: '备份控制器状态',
						render: (val: number) => {
							if (val === 0) {
								return (
									<div>
										<ExclamationCircleOutlined
											style={{
												color: '#faad14',
												marginRight: 4
											}}
										/>
										未安装
									</div>
								);
							} else if (val === 3) {
								return (
									<div>
										<CheckCircleFilled
											style={{
												color: '#00A700',
												marginRight: 4
											}}
										/>
										运行正常
									</div>
								);
							} else {
								return (
									<div>
										<CloseCircleOutlined
											style={{
												color: '#ff4d4f',
												marginRight: 4
											}}
										/>
										运行异常
									</div>
								);
							}
						}
					},
					{
						dataIndex: 'lastRestoreTime',
						label: '最近一次同步时间',
						render: (val) => val || '/'
					}
				]);
			}
		}
	}, [backupComponent]);
	useEffect(() => {
		backupData?.cluster &&
			getComponent({
				clusterId: backupData.cluster,
				componentName: 'gossip-operator'
			}).then((res) => {
				if (res.success) {
					setSlaveComponent(res.data[backupData.cluster]?.status);
				}
			});
	}, [backupData?.cluster]);
	useEffect(() => {
		if (
			name === 'redis' &&
			semver.lt(semver.valid(chartVersion) || '0.0.0', '2.8.2')
		) {
			setDisabled(true);
			setReason('当前服务版本过低，请先升级至2.8.2版本');
			return;
		}
		if (
			name === 'kafka' &&
			semver.lt(semver.valid(chartVersion) || '0.0.0', '1.8.0')
		) {
			setDisabled(true);
			setReason('当前服务版本过低，请先升级至1.8.0版本');
			return;
		}
		if (component === 0) {
			setDisabled(true);
			setReason(
				'检测到当前集群中灾备同步器组件未安装，请先联系管理员安装相关组件'
			);
			return;
		}
		if (component === 4) {
			setDisabled(true);
			setReason(
				'检测到当前集群中灾备同步器组件运行异常，请先联系管理员处理'
			);
			return;
		}
		if (component === 5 || component === 6) {
			setDisabled(true);
			setReason(
				`检测到当前集群中灾备同步器组件${
					component === 5 ? '卸载中' : '安装失败'
				}，请先联系管理员安装相关组件`
			);
			return;
		}
		if (
			component !== 0 &&
			component !== 4 &&
			component !== 5 &&
			component !== 6
		) {
			setDisabled(false);
			setReason('');
		}
		if (backupComponent === 0) {
			setDisabled(true);
			setReason(
				'检测到当前集群中备份控制器组件未安装，请先联系管理员安装相关组件'
			);
			return;
		} else {
			setDisabled(false);
			setReason('');
		}
		if (originData && !originData.address) {
			setReason(
				'当前源服务未创建服务暴露，无法正常使用灾备能力，请先前往服务详情-服务暴露页创建服务暴露'
			);
			return;
		} else {
			setReason('');
		}
		if (backupData && !backupData.address) {
			setReason(
				'当前灾备服务未创建服务暴露，无法正常使用灾备能力，请先前往服务详情-服务暴露页创建服务暴露'
			);
			return;
		} else {
			setReason('');
		}
	}, [component, originData, backupData, chartVersion, backupComponent]);
	const getDetail = async () => {
		const res1 = await getComponent({
			clusterId,
			componentName: 'middlewarebackup-controller'
		});
		if (res1.success) {
			setBackupComponent(res1.data[clusterId]?.status);
		}
		await getDisaster({
			clusterId,
			namespace,
			middlewareName,
			type: name
		}).then((res) => {
			setLoading(false);
			if (res.success) {
				setRunState({
					...runState,
					title: '同步器信息',
					gcPhase: res.data.gcPhase,
					dataPhase: res.data.dataPhase,
					lastRestoreTime: res.data.lastRestoreTime,
					componentStatus: res1.data[clusterId]?.status
				});
				setIsSwitch(res.data.isSwitch);
				setCanFix(res.data.canFix);
				setOriginData({
					cluster: res.data?.master?.middleware?.clusterId || '',
					namespace: res.data?.master?.middleware?.namespace || '',
					name: res.data?.master?.middleware?.name || '',
					address: res.data?.master?.connectionAddress || '',
					gcPort: res.data?.master?.gcPort || '',
					localClusterPhase:
						res.data?.master?.localClusterPhase || '',
					minioAddress:
						res.data?.master?.middleware?.minioInfo?.address || ''
				});
				res.data.slave
					? setBackupData({
							cluster:
								res.data.slave?.middleware?.clusterId || '',
							namespace:
								res.data.slave?.middleware?.namespace || '',
							name: res.data?.slave?.middleware?.name || '',
							address: res.data?.slave?.connectionAddress || '',
							gcPort: res.data?.slave?.gcPort || '',
							minioAddress:
								res.data.slave?.middleware?.minioInfo
									?.address || ''
					  })
					: setBackupData(undefined);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const toCreateBackup: () => void = () => {
		if (disabled || controlledOperationDisabled('expert', data?.lock))
			return;
		WorkOrderFuc(
			() => {
				history.push(
					`/project/${type}/${name}/${aliasName}/container/${currentTab}/addDisaster/${middlewareName}/${chartVersion}/${namespace}/${clusterId}/${data.version}/${data.mode}`
				);
			},
			data.lock,
			middlewareName,
			disasterOperatorId,
			history,
			type,
			name,
			aliasName,
			clusterId,
			namespace
		);
	};
	const deleteInstance: () => void = () => {
		if (isSwitch) return;
		Modal.confirm({
			title: '操作',
			content:
				runState.gcPhase !== 'running' ||
				runState.dataPhase !== 'running' ? (
					'解绑灾备服务可能会造成数据不同步，如果灾备同步器或数据同步器异常，可能会对灾备服务造成未知错误，请谨慎操作'
				) : (
					<>
						<p>解绑灾备服务可能会造成数据丢失</p>
						<p>请谨慎操作</p>
					</>
				),
			onOk: async () => {
				const sendData = {
					clusterId: backupData?.cluster || '',
					namespace: backupData?.namespace || '',
					middlewareName: backupData?.name || '',
					type: name
				};
				await ExecuteOrderFuc();
				return removeDisaster(sendData).then((res) => {
					if (res.success) {
						getDetail();
						notification.success({
							message: '成功',
							description: '解绑成功'
						});
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			}
		});
	};
	const switchDisaster = () => {
		const sendData = {
			clusterId,
			namespace,
			middlewareName,
			type: name
		};
		Modal.confirm({
			title: '操作确认',
			content:
				'当前灾备数据同步异常，进行主备切换后可能会导致数据丢失，是否确认进行主备切换？',
			onOk: async () => {
				await ExecuteOrderFuc();
				return switchServiceDisaster(sendData).then((res) => {
					if (res.success) {
						getDetail();
						notification.success({
							message: '成功',
							description: '主备切换成功'
						});
					} else {
						notification.error({
							message: '失败',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};
	const cancel = () => {
		const sendData = {
			clusterId,
			namespace,
			middlewareName,
			type: name
		};
		Modal.confirm({
			title: '操作确认',
			content: '是否确认取消',
			onOk: async () => {
				await ExecuteOrderFuc();
				return cancelSwitch(sendData).then((res) => {
					if (res.success) {
						getDetail();
						notification.success({
							message: '成功',
							description: '取消成功'
						});
					} else {
						notification.error({
							message: '失败',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};
	const edit = () => {
		const sendData = {
			clusterId,
			namespace,
			middlewareName,
			type: name
		};
		Modal.confirm({
			title: '操作确认',
			content:
				'进行故障恢复时，会引发灾备服务的备库重搭操作，请确保源服务运行正常，是否确定进行故障恢复？',
			onOk: async () => {
				await ExecuteOrderFuc();
				return repairDisaster(sendData).then((res) => {
					if (res.success) {
						getDetail();
						notification.success({
							message: '成功',
							description: '恢复成功'
						});
					} else {
						notification.error({
							message: '失败',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};

	return (
		<Spin spinning={loading}>
			{reason && (
				<Alert
					message={reason}
					type="warning"
					showIcon
					closable
					style={{ marginBottom: 16 }}
				/>
			)}
			<div className="service-disaster">
				<DataFields
					dataSource={runState}
					items={runItems}
					className="refresh-color"
				/>
				<h2 className="mt-16">服务详情</h2>
				<div className="disaster-card-content">
					{backupData ? (
						<>
							{middlewareName === originData?.name ? (
								<DisasterOriginCard
									type={name}
									isSwitch={isSwitch}
									canFix={canFix}
									originData={originData}
									backupData={backupData}
									toBasicInfo={() => onRefresh('basicInfo')}
								/>
							) : (
								<DisasterBackupCard
									type={name}
									isSwitch={isSwitch}
									canFix={canFix}
									dataPhase={runState.dataPhase}
									backupData={backupData}
									lock={data?.lock}
									deleteInstance={() => {
										if (
											!controlledOperationDisabled(
												'expert',
												data?.lock
											)
										) {
											WorkOrderFuc(
												deleteInstance,
												data.lock,
												middlewareName,
												disasterOperatorId,
												history,
												type,
												name,
												aliasName,
												clusterId,
												namespace
											);
										}
									}}
									toDetail={() => toDetail()}
								/>
							)}
							<div className="trans-line">
								{canFix ? (
									<Button
										disabled={controlledOperationDisabled(
											'expert',
											data?.lock
										)}
										onClick={() => {
											WorkOrderFuc(
												edit,
												data.lock,
												middlewareName,
												disasterOperatorId,
												history,
												type,
												name,
												aliasName,
												clusterId,
												namespace
											);
										}}
									>
										故障恢复
									</Button>
								) : (
									<Button
										onClick={() => {
											WorkOrderFuc(
												switchDisaster,
												data.lock,
												middlewareName,
												disasterOperatorId,
												history,
												type,
												name,
												aliasName,
												clusterId,
												namespace
											);
										}}
										disabled={
											isSwitch ||
											runState.gcPhase !== 'running' ||
											slaveComponent === 4 ||
											controlledOperationDisabled(
												'expert',
												data?.lock
											)
										}
									>
										主备切换
									</Button>
								)}
								<ArrowLine
									style={{
										width: '100%'
									}}
									color={
										isSwitch ||
										(middlewareName === originData?.name &&
											runState?.dataPhase === 'running')
											? '#226ee7'
											: ''
									}
									iconColor={
										(isSwitch ||
											middlewareName ===
												originData?.name) &&
										runState?.dataPhase === 'running'
											? '#226ee7'
											: ''
									}
								/>
								<ArrowLine
									style={{
										width: '100%',
										transform: 'rotate(180deg)'
									}}
									color={
										(isSwitch ||
											middlewareName !==
												originData?.name) &&
										runState?.dataPhase === 'running'
											? '#226ee7'
											: ''
									}
									iconColor={
										(isSwitch ||
											middlewareName !==
												originData?.name) &&
										runState?.dataPhase === 'running'
											? '#226ee7'
											: ''
									}
								/>
								{isSwitch && params.name !== 'postgresql' && (
									<Button
										disabled={controlledOperationDisabled(
											'expert',
											data?.lock
										)}
										onClick={() => {
											WorkOrderFuc(
												cancel,
												data.lock,
												middlewareName,
												disasterOperatorId,
												history,
												type,
												name,
												aliasName,
												clusterId,
												namespace
											);
										}}
									>
										取消
									</Button>
								)}
							</div>
							{middlewareName === originData?.name ? (
								<DisasterBackupCard
									type={name}
									isSwitch={isSwitch}
									canFix={canFix}
									lock={data?.lock}
									dataPhase={runState.dataPhase}
									backupData={backupData}
									deleteInstance={() => {
										if (
											!controlledOperationDisabled(
												'expert',
												data?.lock
											)
										) {
											WorkOrderFuc(
												deleteInstance,
												data.lock,
												middlewareName,
												disasterOperatorId,
												history,
												type,
												name,
												aliasName,
												clusterId,
												namespace
											);
										}
									}}
									toDetail={() => toDetail()}
								/>
							) : (
								<DisasterOriginCard
									type={name}
									isSwitch={isSwitch}
									canFix={canFix}
									originData={originData}
									backupData={backupData}
									toBasicInfo={() => onRefresh('basicInfo')}
								/>
							)}
						</>
					) : (
						<>
							<DisasterOriginCard
								type={name}
								isSwitch={isSwitch}
								canFix={canFix}
								data={data}
								originData={originData}
								toBasicInfo={() => {
									history.location.pathname ===
									'/disasterBackup/disasterCenter'
										? toDetail()
										: onRefresh('basicInfo');
								}}
							/>
							<div className="trans-line">
								<ArrowLine style={{ width: '100%' }} />
								<ArrowLine
									style={{
										width: '100%',
										transform: 'rotate(180deg)'
									}}
								/>
							</div>
							<DisasterBackupCardNone
								type={name}
								canFix={canFix}
								isSwitch={isSwitch}
								disabled={disabled}
								toCreateBackup={toCreateBackup}
							/>
						</>
					)}
				</div>
			</div>
		</Spin>
	);
}

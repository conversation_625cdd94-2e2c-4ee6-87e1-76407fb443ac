import React from 'react';
import { Badge } from 'antd';
import {
	PlusOutlined,
	DeleteOutlined,
	LoadingOutlined
} from '@ant-design/icons';
import origin from '@/assets/images/o-instance.svg';
import backup from '@/assets/images/backup-instance.svg';
import { states } from '@/utils/const';
import { controlledOperationDisabled } from '@/utils/utils';

interface disasterCardProps {
	lock?: string;
	originData?: any;
	backupData?: any;
	type: string;
	isSwitch?: boolean;
	canFix?: boolean;
	dataPhase?: string;
	data?: any;
	disabled?: boolean;
	toCreateBackup?: () => void;
	deleteInstance?: () => void;
	toDetail?: () => void;
	toBasicInfo?: () => void;
}
export const DisasterOriginCard: (props: disasterCardProps) => JSX.Element = (
	props: disasterCardProps
) => {
	const {
		originData,
		toBasicInfo,
		backupData,
		data,
		type,
		isSwitch,
		canFix
	} = props;
	return (
		<div className="disaster-card">
			<div
				className="disaster-card-title"
				// onClick={toBasicInfo}
			>
				<img src={origin} />
				<span className="title-text">源服务信息</span>
				{type !== 'mysql' && isSwitch && (
					<Badge
						status={'warning'}
						text={'主备切换中'}
						className="badge"
					/>
				)}
				{type !== 'mysql' && !isSwitch && backupData && (
					<Badge
						status={
							!originData?.localClusterPhase ||
							originData?.localClusterPhase !== 'running'
								? 'error'
								: 'success'
						}
						text={
							!originData?.localClusterPhase ||
							originData?.localClusterPhase !== 'running'
								? '运行异常'
								: '运行正常'
						}
						className="badge"
					/>
				)}
				{type !== 'mysql' && !backupData && (
					<Badge
						color={
							states.find((item) => item.value === data?.status)
								?.color
						}
						text={
							states.find((item) => item.value === data?.status)
								?.text
						}
						className="badge"
					/>
				)}
			</div>
			<ul className="disaster-card-info">
				<li>
					<label>集群名称/命名空间 :</label>
					<span>
						{originData?.cluster || ''}/
						{originData?.namespace || ''}
					</span>
				</li>
				<li>
					<label>服务名称 :</label>
					<span>{originData?.name || '/'}</span>
				</li>
				{/* <li>
					<label>数据库账号 :</label>
					<span>{originData.dbUser || '/'}</span>
				</li> */}
				{/* <li>
					<label>密码 :</label>
					<span>
						<PasswordDisplay value={originData.dbPass} />
					</span>
				</li> */}
				<li>
					<label>连接地址 :</label>
					<span title={originData?.address}>
						{originData?.address}
					</span>
				</li>
				<li>
					<label>灾备同步器端口 :</label>
					<span title={originData?.gcPort || originData?.port}>
						{originData?.gcPort || originData?.port}
					</span>
				</li>
				{type === 'mysql' && backupData && (
					<li>
						<label>故障恢复端口 :</label>
						<span title={originData?.recoveryPort || '/'}>
							{originData?.recoveryPort || '/'}
						</span>
					</li>
				)}
				{type === 'elasticsearch' ? (
					<li>
						<label>Minio链接地址 :</label>
						<span title={originData?.minioAddress || '/'}>
							{originData?.minioAddress || '/'}
						</span>
					</li>
				) : null}
			</ul>
		</div>
	);
};

export const DisasterBackupCardNone: (
	props: disasterCardProps
) => JSX.Element = (props: disasterCardProps) => {
	const { toCreateBackup, type, disabled } = props;

	return (
		<div className="disaster-card">
			<div className="disaster-card-title-backup">
				<img src={backup} />
				<span className="title-text">灾备服务信息</span>
				{type !== 'mysql' && (
					<Badge
						status="warning"
						text="未同步数据"
						className="badge"
					/>
				)}
			</div>
			<ul className={`disaster-card-none ${disabled ? 'disabled' : ''}`}>
				<div className="disaster-card-add" onClick={toCreateBackup}>
					<PlusOutlined />
					<span>添加灾备服务</span>
				</div>
			</ul>
		</div>
	);
};

export const DisasterBackupCard: (props: disasterCardProps) => JSX.Element = (
	props: disasterCardProps
) => {
	const {
		backupData,
		deleteInstance,
		toDetail,
		type,
		isSwitch,
		canFix,
		dataPhase,
		lock
	} = props;
	return (
		<div className="disaster-card">
			<div className="disaster-card-title-backup">
				<img src={backup} />
				<span className="title-text">灾备服务信息</span>
				{type !== 'mysql' && !isSwitch && dataPhase === 'error' && (
					<Badge
						status="error"
						text="数据同步异常"
						className="badge"
					/>
				)}
				{type !== 'mysql' &&
					!isSwitch &&
					!canFix &&
					dataPhase !== 'error' && (
						<span className="ant-badge badge">
							<LoadingOutlined style={{ color: '#226ee7' }} />
							<span className="ant-badge-status-text">
								数据同步中
							</span>
						</span>
					)}
				{type !== 'mysql' && isSwitch && (
					<Badge
						status="warning"
						text="主备切换中"
						className="badge"
					/>
				)}
			</div>
			<ul className="disaster-card-info">
				<li>
					<label>集群名称/命名空间 :</label>
					<span>
						{backupData?.cluster || ''}/
						{backupData?.namespace || ''}
					</span>
				</li>
				<li>
					<label>服务名称 :</label>
					<span>{backupData?.name || '/'}</span>
				</li>
				{/* <li>
					<label>数据库账号 :</label>
					<span>{backupData.dbUser || '/'}</span>
				</li> */}
				{/* <li>
					<label>密码 :</label>
					<span>
						<PasswordDisplay value={backupData.dbPass} />
					</span>
				</li> */}
				<li>
					<label>连接地址 :</label>
					<span title={backupData?.address}>
						{backupData?.address}
					</span>
				</li>
				<li>
					<label>灾备同步器端口 :</label>
					<span title={backupData?.gcPort || backupData?.port}>
						{backupData?.gcPort || backupData?.port}
					</span>
				</li>
				{type === 'mysql' && (
					<li>
						<label>故障恢复端口 :</label>
						<span title={backupData?.recoveryPort || '/'}>
							{backupData?.recoveryPort || '/'}
						</span>
					</li>
				)}
				{type === 'elasticsearch' ? (
					<li>
						<label>Minio链接地址 :</label>
						<span title={backupData?.minioAddress || '/'}>
							{backupData?.minioAddress || '/'}
						</span>
					</li>
				) : null}
			</ul>
			<div
				className={`disaster-card-delete ${
					isSwitch || controlledOperationDisabled('expert', lock)
						? 'disabled'
						: ''
				}`}
				onClick={deleteInstance}
			>
				<DeleteOutlined />
				<span>解绑</span>
			</div>
		</div>
	);
};

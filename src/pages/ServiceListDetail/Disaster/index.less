.service-disaster {
	.disaster-card-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	.disaster-card {
		width: 450px;
		min-height: 320px;
		background: @white;
		box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.09);
		border-radius: @border-radius-xl;
		position: relative;
		.disaster-card-title {
			width: 100%;
			height: 135px;
			position: relative;
			background: @primary-color;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			border-radius: @border-radius-xl @border-radius-xl 0px 0px;
			cursor: pointer;
			& img {
				position: absolute;
				left: 0;
				top: 10px;
			}
			& .title-text {
				height: 33px;
				font-size: 24px;
				font-weight: @font-weight;
				color: @white;
				line-height: 33px;
			}
			.badge {
				margin-top: 8px;
				.ant-badge-status-text {
					color: #ffffff;
				}
			}
		}
		.disaster-card-title-backup {
			width: 100%;
			height: 135px;
			background: #dedede;
			display: flex;
			align-items: center;
			flex-direction: column;
			justify-content: center;
			border-radius: @border-radius-xl @border-radius-xl 0px 0px;
			cursor: pointer;
			position: relative;
			& img {
				position: absolute;
				left: 0;
				top: 10px;
			}
			& .title-text {
				height: 33px;
				font-size: 24px;
				font-weight: @font-weight;
				color: @black;
				line-height: 33px;
			}
			.badge {
				margin-top: 8px;
				.ant-badge-status-text {
					color: #ffffff;
				}
			}
		}
		.disaster-card-info {
			width: 100%;
			padding: 24px;
			& li {
				height: 18px;
				font-size: @font-1;
				font-weight: @font-weight-sm;
				color: @text-color-title;
				line-height: @line-height-3;
				margin-bottom: 16px;
				display: flex;
				& label {
					margin-right: 8px;
				}
				& span {
					max-width: 185px;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}
		}
		.disaster-card-none {
			text-align: center;
			padding-top: 75px;
			.disaster-card-add {
				color: @primary-color;
				vertical-align: middle;

				cursor: pointer;
				@include lineHeight(18px);
				& span {
					margin-left: 8px;
				}
			}
			&.disabled {
				.disaster-card-add {
					color: @black-5;
					cursor: not-allowed;
				}
			}
		}
		.disaster-card-delete {
			width: 100%;
			height: 32px;
			line-height: 32px;
			border-top: 1px solid #ebebeb;
			text-align: center;
			color: #7f7f7f;
			position: absolute;
			bottom: 0;
			cursor: pointer;
			visibility: hidden;
			& span {
				margin-left: 4px;
			}
			&.disabled {
				cursor: not-allowed;
			}
		}
		&:hover {
			.disaster-card-delete {
				visibility: initial;
			}
		}
	}
	.ant-descriptions-item-content {
		align-items: center;
	}
	.trans-line {
		width: 210px;
		margin: 0 24px;
		display: flex;
		align-items: center;
		flex-direction: column;
	}
}

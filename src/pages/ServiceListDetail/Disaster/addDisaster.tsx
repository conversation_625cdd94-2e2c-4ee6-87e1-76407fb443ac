import * as React from 'react';
import { useState, useEffect } from 'react';
import { useHistory } from 'react-router';
import { useParams } from 'react-router';
import { ProPage, ProHeader, ProContent } from '@/components/ProPage';
import ProTable from '@/components/ProTable';
import {
	Button,
	Modal,
	notification,
	Radio,
	Tooltip,
	Form,
	Collapse,
	Select,
	Tag,
	InputNumber,
	Divider,
	Row,
	Col,
	Input
} from 'antd';
import { CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons';

import moment from 'moment';
import { formItemLayout618, maintenances, states } from '@/utils/const';
import {
	serviceProps,
	ListParamsProps
} from '@/pages/ServiceList/service.list';
import { IngressItemProps } from '@/pages/ResourcePoolManagement/resource.pool';
import { getList } from '@/services/serviceList';
import {
	getClusters,
	getComponent,
	getIngresses,
	getNodePort,
	getIngressTCPPort
} from '@/services/common';
import { clusterType } from '@/types';
import { checkLicense } from '@/services/user';
import { serviceListStatusRender, timeRender } from '@/utils/utils';
import {
	createDisaster,
	getDisasterService,
	minioPing
} from '@/services/middleware';
import { FiltersProps } from '@/types/comment';
import semver from 'semver';
import { getAvailablePorts } from '@/services/ingress';
import { AvailablePortItem } from '../detail';
import storage from '@/utils/storage';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';

const AddDisaster = (): JSX.Element => {
	const [form] = Form.useForm();
	const [sourceMinioForm] = Form.useForm();
	const [backupMinioForm] = Form.useForm();
	const history = useHistory();
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	let disabledTitle: string;
	const params: ListParamsProps = useParams();
	const {
		name,
		aliasName,
		chartVersion,
		middlewareName,
		namespace,
		type,
		clusterId,
		version,
		mode
	} = params;
	const [loading, setLoading] = useState<boolean>(false);
	// * 手动操作排序、筛选、分页
	const [filteredInfo, setFilteredInfo] = useState<any>({});
	const [sortedInfo, setSortedInfo] = useState<any>({});
	const [dataSource, setDataSource] = useState<serviceProps[]>([]);
	const [selectedRow, setSelectedRow] = useState<any>();
	const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
	const [license, setLicense] = useState<boolean>(false);
	const [canLicense, setCanLicense] = useState<boolean>(false);
	const [clusterList, setClusterList] = useState<FiltersProps[]>();
	const [componentInfo, setComponentInfo] = useState<any>();
	const [backupComponentInfo, setBackupComponentInfo] = useState<any>();
	const [masterExposeType, setMasterExposeType] = useState<string>('Ingress');
	const [slaveExposeType, setSlaveExposeType] = useState<string>('Ingress');
	const [masterIngresses, setMasterIngresses] = useState<IngressItemProps[]>(
		[]
	);
	const [slaveIngresses, setSlaveIngresses] = useState<IngressItemProps[]>(
		[]
	);
	const [exposePort, setExposePort] = useState<number | null>();
	const [nodePortArray, setNodePortArray] = useState<string[]>([]);
	const [ingressPortArray, setIngressPortArray] = useState<string[]>([]);
	const [masterInfo, setMasterInfo] = useState<boolean>(false);
	const [masterMinioInfo, setMasterMinioInfo] = useState<boolean>(false);
	const [slaveMinioInfo, setSlaveMinioInfo] = useState<boolean>(false);
	const [slaveInfo, setSlaveInfo] = useState<boolean>(false);
	const [disaster, setDisaster] = useState<any[]>([]);
	const [btnLoading, setBtnLoading] = useState<boolean>(false);
	const [masterIngressClassName, setMasterIngressClassName] = useState<{
		value: string;
		type: string;
		traefikPortList: any[];
	}>({
		value: '',
		type: '',
		traefikPortList: []
	});
	const [slaveIngressClassName, setSlaveIngressClassName] = useState<{
		value: string;
		type: string;
		traefikPortList: any[];
	}>({
		value: '',
		type: '',
		traefikPortList: []
	});
	const [availablePorts, setAvailablePorts] = useState<AvailablePortItem[]>(
		[]
	);
	const [slaveAvailablePorts, setSlaveAvailablePorts] = useState<
		AvailablePortItem[]
	>([]);
	// * 获取服务发布的operatorId
	const servicePublishOperatorId = maintenances['Service Publishing'];
	const [sourceMinioStatus, setSourceMinioStatus] = useState<
		boolean | undefined
	>();
	const [backupMinioStatus, setBackupMinioStatus] = useState<
		boolean | undefined
	>();

	useEffect(() => {
		getComponentInfo();
		getClusters().then((res) => {
			if (res.success) {
				setClusterList(
					res.data.map((item: clusterType) => ({
						text: item.name,
						value: item.id
					}))
				);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
		getNodePort().then((res) => {
			if (res.success) {
				setNodePortArray(res.data.split('-'));
			}
		});
		getIngressTCPPort().then((res) => {
			if (res.success) {
				setIngressPortArray(res.data.split('-'));
			}
		});
		getAvailablePorts({
			clusterId: params.clusterId,
			namespace,
			middlewareName: middlewareName || ''
		}).then((res) => {
			if (res.success) {
				setAvailablePorts(res.data);
			}
		});
	}, []);

	useEffect(() => {
		if (params.clusterId) {
			getLicenseCheck();
			getIngresses({ clusterId: params.clusterId }).then((res) => {
				if (res.success) {
					setMasterIngresses(res.data);
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		}
	}, [params.clusterId]);

	useEffect(() => {
		if (selectedRowKeys && selectedRowKeys.length) {
			setSlaveMinioInfo(false);
			setBackupMinioStatus(undefined);
			backupMinioForm.resetFields();
			form.setFieldsValue({
				slaveExposeType: null,
				slaveExposePort: null,
				slaveIngressClassName: null
			});
			getIngresses({ clusterId: selectedRow.clusterId }).then((res) => {
				if (res.success) {
					setSlaveIngresses(res.data);
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
			getAvailablePorts({
				clusterId: selectedRow.clusterId,
				namespace,
				middlewareName: middlewareName || ''
			}).then((res) => {
				if (res.success) {
					setSlaveAvailablePorts(res.data);
				}
			});
		}
	}, [selectedRowKeys]);

	useEffect(() => {
		let mounted = true;
		if (organId && projectId && name) {
			if (mounted) {
				setLoading(true);
				getData();
				getDisasterService({
					organId,
					projectId,
					clusterId: '*',
					namespace: '*',
					middlewareName: '*',
					type: name
				}).then((res) => {
					if (res.success) {
						setDisaster(res.data);
					}
				});
			}
		}
		return () => {
			mounted = false;
		};
	}, [name, organId, projectId]);

	useEffect(() => {
		const values = form.getFieldsValue();
		if (
			(values.slaveExposeType === 'Ingress' &&
				values.slaveIngressClassName &&
				values.slaveExposePort) ||
			(values.slaveExposeType === 'NodePort' && values.slaveExposePort)
		) {
			setSlaveInfo(true);
		} else {
			setSlaveInfo(false);
		}
		if (
			(values.masterExposeType === 'Ingress' &&
				values.masterIngressClassName &&
				values.masterExposePort) ||
			(values.masterExposeType === 'NodePort' && values.masterExposePort)
		) {
			setMasterInfo(true);
		} else {
			setMasterInfo(false);
		}
	}, [form.getFieldsValue()]);

	const getComponentInfo = () => {
		getComponent({
			clusterId: '*',
			componentName: 'gossip-operator'
		}).then((res) => {
			if (res.success) {
				setComponentInfo(res.data);
			}
		});
		if (name === 'elasticsearch') {
			getComponent({
				clusterId: '*',
				componentName: 'middlewarebackup-controller'
			}).then((res) => {
				if (res.success) {
					setBackupComponentInfo(res.data);
				}
			});
		}
	};

	const getData = () => {
		getList({
			organId,
			projectId,
			clusterId: '*',
			namespace: '*',
			keyword: '',
			type: name
		}).then((res) => {
			setLoading(false);
			if (res.success) {
				if (res.data.length > 0) {
					// * 多集群数据整合
					if (name === 'redis' || name === 'elasticsearch') {
						// * 筛选非删除、非删除中、当前集群下非自己、服务版本与当前服务版本相同、redis6.x版本、elasticsearch6.x版本
						setDataSource(
							res.data.filter((item: any) => {
								return (
									item.status !== 'Deleting' &&
									item.status !== 'Deleted' &&
									!(
										item.clusterId === clusterId &&
										item.name == middlewareName
									) &&
									item.name !== clusterId &&
									item.version === version &&
									item.version.split('.')[0] === '6' &&
									item.mode === mode
								);
							})
						);
					} else {
						setDataSource(
							res.data.filter(
								(item: any) =>
									item.status !== 'Deleting' &&
									item.status !== 'Deleted' &&
									!(
										item.clusterId === clusterId &&
										item.name == middlewareName
									) &&
									item.version === version &&
									item.mode === mode
							)
						);
					}
				} else {
					setDataSource([]);
				}
			}
		});
	};

	const handleTableChange = (pagination: any, filters: any, sorter: any) => {
		setFilteredInfo(filters);
		setSortedInfo(sorter);
	};

	const getLicenseCheck = () => {
		checkLicense({ license: '', clusterId: params.clusterId }).then(
			(res) => {
				if (res.success) {
					setLicense(res.data);
					setCanLicense(true);
				}
			}
		);
	};

	const releaseMiddleware = () => {
		if (canLicense && !license) {
			Modal.confirm({
				title: '可用余额不足',
				content:
					'当前平台可用余额已不足2Core，如果您想继续使用zeus中间件一体化管理平台，请联系我们申请授权码。',
				okText: '立即前往'
			});
		} else {
			WorkOrderFuc(
				() => {
					history.push(
						`/project/${type}/${name}/${aliasName}/${name}PublishDisaster/${chartVersion}/${middlewareName}/${namespace}/${clusterId}/isDisaster`
					);
				},
				'locked',
				'null',
				servicePublishOperatorId,
				history,
				type,
				name,
				aliasName,
				clusterId,
				namespace
			);
		}
	};
	const judgeIsInSameCluster = () => {
		if (selectedRow.clusterId === params.clusterId) {
			return 1;
		} else {
			return 0;
		}
	};
	const handleIngressChange = (value: string, isMaster: boolean) => {
		if (isMaster) {
			const cur = masterIngresses.find(
				(item) => item.ingressClassName === value
			);
			setMasterIngressClassName({
				value: value,
				type: cur?.type as string,
				traefikPortList: cur?.traefikPortList || []
			});
			const curPortItem = availablePorts.find(
				(item: AvailablePortItem) => item.ingressClassName === value
			);
			form.setFieldsValue({
				masterExposePort: curPortItem?.portList[0]
			});
		} else {
			const cur = slaveIngresses.find(
				(item) => item.ingressClassName === value
			);
			setSlaveIngressClassName({
				value: value,
				type: cur?.type as string,
				traefikPortList: cur?.traefikPortList || []
			});
			const curPortItem = slaveAvailablePorts.find(
				(item: AvailablePortItem) => item.ingressClassName === value
			);
			form.setFieldsValue({
				slaveExposePort: curPortItem?.portList[judgeIsInSameCluster()]
			});
		}
	};
	const onExposeTypeChange = (value: string, isMaster: boolean) => {
		if (isMaster) {
			setMasterExposeType(value);
			if (value === 'NodePort') {
				const curPortItem = availablePorts.find(
					(item: AvailablePortItem) =>
						item.tcpExposeType === 'nodePort'
				);
				form.setFieldsValue({
					masterExposePort: curPortItem?.portList[0]
				});
			} else {
				const curPortItem = availablePorts.find(
					(item: AvailablePortItem) => {
						if (
							item.ingressClassName ===
							masterIngressClassName.value
						) {
							return item;
						}
					}
				);
				form.setFieldsValue({
					masterExposePort: curPortItem?.portList[0]
				});
			}
		} else {
			setSlaveExposeType(value);
			if (value === 'NodePort') {
				const curPortItem = slaveAvailablePorts.find(
					(item: AvailablePortItem) =>
						item.tcpExposeType === 'nodePort'
				);
				form.setFieldsValue({
					slaveExposePort:
						curPortItem?.portList[judgeIsInSameCluster()]
				});
			} else {
				const curPortItem = slaveAvailablePorts.find(
					(item: AvailablePortItem) => {
						if (
							item.ingressClassName ===
							slaveIngressClassName.value
						) {
							return item;
						}
					}
				);
				form.setFieldsValue({
					slaveExposePort:
						curPortItem?.portList[judgeIsInSameCluster()]
				});
			}
		}
	};
	const Operation = {
		primary: (
			<Button type="primary" onClick={releaseMiddleware}>
				发布服务
			</Button>
		)
	};

	const onOk = async () => {
		await form.validateFields();
		const values = form.getFieldsValue();
		const sourceMinioValues = sourceMinioForm.getFieldsValue();
		const backupMinioValues = backupMinioForm.getFieldsValue();
		const curMasterIngressClass = masterIngresses.find(
			(item) => item.ingressClassName === values.masterIngressClassName
		);
		const tcpMasterExposeTypeTemp =
			values.masterExposeType === 'Ingress'
				? curMasterIngressClass?.type === 'nginx'
					? 'nginx'
					: 'traefik'
				: 'nodePort';
		const curSlaveIngressClass = masterIngresses.find(
			(item) => item.ingressClassName === values.slaveExposePort
		);
		const tcpSlaveExposeTypeTemp =
			values.slaveExposeType === 'Ingress'
				? curSlaveIngressClass?.type === 'nginx'
					? 'nginx'
					: 'traefik'
				: 'nodePort';
		setBtnLoading(true);
		await ExecuteOrderFuc();
		let sendData: any = {
			oldClusterId: params.clusterId,
			oldNamespace: namespace,
			middlewareName,
			slave: {
				clusterId: selectedRow.clusterId,
				namespace: selectedRow.namespace,
				name: selectedRow.name,
				type: selectedRow.type
			},
			masterExpose: {
				protocol: 'TCP',
				middlewareType: 'gc-agent',
				tcpExposeType: tcpMasterExposeTypeTemp,
				ingressClassName: values.masterIngressClassName,
				serviceDTOList: [
					{
						exposePort: values.masterExposePort
					}
				]
			},
			slaveExpose: {
				protocol: 'TCP',
				middlewareType: 'gc-agent',
				tcpExposeType: tcpSlaveExposeTypeTemp,
				ingressClassName: values.slaveIngressClassName,
				serviceDTOList: [
					{
						exposePort: values.slaveExposePort
					}
				]
			}
		};
		if (name === 'elasticsearch') {
			sendData = {
				...sendData,
				masterMinio: {
					address: `${sourceMinioValues.address?.protocol}://${sourceMinioValues.address?.host}:${sourceMinioValues.address?.port}`,
					bucketName: sourceMinioValues.bucketName,
					accessKeyId: sourceMinioValues.accessKeyId,
					secretAccessKey: sourceMinioValues.secretAccessKey
				},
				slaveMinio: {
					address: `${backupMinioValues.address?.protocol}://${backupMinioValues.address?.host}:${backupMinioValues.address?.port}`,
					bucketName: backupMinioValues.bucketName,
					accessKeyId: backupMinioValues.accessKeyId,
					secretAccessKey: backupMinioValues.secretAccessKey
				}
			};
		}
		createDisaster(sendData)
			.then((res) => {
				if (res.success) {
					history.goBack();
					notification.success({
						message: '成功',
						description: '灾备关系创建成功'
					});
				} else {
					notification.error({
						message: '失败',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setBtnLoading(false);
			});
	};

	const testMinio = (type: string) => {
		const minioForm = type === 'source' ? sourceMinioForm : backupMinioForm;
		const setValues =
			type === 'source' ? setSourceMinioStatus : setBackupMinioStatus;
		minioForm.validateFields().then(async (values) => {
			const result = await minioPing({
				clusterId: params.clusterId,
				namespace,
				middlewareName,
				address: `${values.address?.protocol}://${values.address?.host}:${values.address?.port}`,
				bucketName: values.bucketName,
				accessKeyId: values.accessKeyId,
				secretAccessKey: values.secretAccessKey
			});
			setValues(result.success);
		});
	};
	const judgeDisabled = () => {
		if (!selectedRowKeys.length) return { disabled: true, title: '' };
		if (!masterInfo) return { disabled: true, title: '' };
		if (!slaveInfo) return { disabled: true, title: '' };
		if (
			name === 'elasticsearch' &&
			(!sourceMinioStatus || !backupMinioStatus)
		)
			return { disabled: true, title: '' };
	};
	return (
		<ProPage>
			<ProHeader title="新增灾备服务" onBack={() => history.goBack()} />
			<ProContent>
				<ProTable
					dataSource={dataSource}
					showRefresh
					onRefresh={() => {
						getData();
						getComponentInfo();
					}}
					rowKey={(record) =>
						record.clusterId + record.namespace + record.name
					}
					loading={loading}
					operation={Operation}
					rowClassName={(record) => {
						if (
							record.status === 'Deleted' ||
							record.status === 'Deleting'
						) {
							return 'table-row-delete';
						}
						return '';
					}}
					onChange={handleTableChange}
					rowSelection={{
						type: 'radio',
						renderCell: (value, record, index, originNode) => {
							const key =
								record.clusterId +
								record.namespace +
								record.name;
							const isDisaster =
								disaster.find(
									(item: any) =>
										item.master.name === record.name
								) ||
								disaster.find(
									(item: any) =>
										item.slave.name === record.name
								);
							const versionUsable = semver.lt(
								semver.valid(record.chartVersion) || '0.0.0',
								record.type === 'redis' ? '2.8.2' : '1.8.0'
							);
							if (versionUsable) {
								disabledTitle = `当前控制器版本过低, 无法创建灾备服务,请先升级控制器高于${
									record.type === 'redis' ? '2.8.2' : '1.8.0'
								}版本`;
							}
							if (isDisaster) {
								disabledTitle = '该服务已存在相应灾备关系';
							}
							if (
								componentInfo[record.clusterId || '']
									?.status !== 3
							) {
								disabledTitle =
									'当前服务所在集群未安装灾备同步器组件或安装异常，请联系管理员安装对应组件';
							}
							if (
								name === 'elasticsearch' &&
								backupComponentInfo[record.clusterId || '']
									?.status !== 3
							) {
								disabledTitle =
									'当前服务所在集群未安装备份控制器组件或安装异常，请联系管理员安装对应组件';
							}
							if (version !== record.version) {
								disabledTitle = '不可以跨版本灾备';
							}
							return versionUsable ||
								isDisaster ||
								componentInfo[record.clusterId || '']
									?.status !== 3 ||
								(name === 'elasticsearch' &&
									backupComponentInfo[record.clusterId || '']
										?.status !== 3) ||
								version !== record.version ? (
								<Tooltip title={disabledTitle}>
									<Radio
										disabled
										checked={key === selectedRowKeys[0]}
									/>
								</Tooltip>
							) : record.clusterId === params.clusterId ? (
								<Tooltip
									title={
										'若有可用的其它集群的情况下，不建议将灾备服务和源服务部署在一个集群'
									}
								>
									<Radio
										checked={key === selectedRowKeys[0]}
										onChange={() => {
											setSelectedRow(record);
											setSelectedRowKeys([key]);
										}}
									/>
								</Tooltip>
							) : (
								<Radio
									checked={key === selectedRowKeys[0]}
									onChange={() => {
										setSelectedRow(record);
										setSelectedRowKeys([key]);
									}}
								/>
							);
						}
					}}
				>
					<ProTable.Column
						title="服务名称"
						dataIndex="name"
						width={150}
					/>
					<ProTable.Column
						title="状态"
						dataIndex="status"
						key="status"
						width={150}
						render={serviceListStatusRender}
						filters={states}
						filterMultiple={false}
						filteredValue={filteredInfo[1] || null}
						onFilter={(
							value: string | number | boolean,
							record: serviceProps
						) => {
							if (value === 'Other') {
								return (
									record.status !== 'Creating' &&
									record.status !== 'Running' &&
									record.status !== 'Preparing' &&
									record.status !== 'failed' &&
									record.status !== 'Deleted' &&
									record.status !== 'Deleting' &&
									record.status !== 'GracefulRestart'
								);
							}
							return record.status === value;
						}}
					/>
					<ProTable.Column
						title="实例数"
						dataIndex="podNum"
						width={80}
					/>
					<ProTable.Column
						title="所属集群"
						dataIndex="clusterId"
						width={150}
						filters={clusterList}
						filterMultiple={false}
						filteredValue={filteredInfo[3] || null}
						onFilter={(
							value: string | number | boolean,
							record: serviceProps
						) => record.clusterId === value}
						render={(value) =>
							clusterList?.find((item) => item.value === value)
								?.text || '/'
						}
						ellipsis={true}
					/>
					<ProTable.Column
						title="服务版本"
						dataIndex="chartVersion"
						width={100}
					/>
					<ProTable.Column
						title="版本"
						dataIndex="version"
						width={50}
					/>
					<ProTable.Column
						title="创建时间"
						key="createTime"
						dataIndex="createTime"
						width={180}
						sortOrder={
							sortedInfo.field === 'createTime'
								? sortedInfo.order
								: null
						}
						sorter={(a: serviceProps, b: serviceProps) =>
							moment(a.createTime).unix() -
							moment(b.createTime).unix()
						}
						render={timeRender}
					/>
				</ProTable>
				<h2 className="mt-16">灾备端口暴露</h2>
				<Form
					form={form}
					labelAlign="left"
					{...formItemLayout618}
					style={{ width: '60%' }}
				>
					<Collapse defaultActiveKey={['1']}>
						<Collapse.Panel
							header={
								<>
									<span>源服务</span>
									{!masterInfo ? (
										<span style={{ color: '#ff4d4f' }}>
											(未配置)
										</span>
									) : null}
								</>
							}
							key="1"
							forceRender
						>
							<Form.Item
								required
								label="暴露方式"
								name="masterExposeType"
								initialValue={masterExposeType}
							>
								<Select
									value={masterExposeType}
									onChange={(value) =>
										onExposeTypeChange(value, true)
									}
									placeholder="请选择暴露方式"
								>
									<Select.Option value="NodePort">
										NodePort
									</Select.Option>
									<Select.Option value="Ingress">
										Ingress
									</Select.Option>
								</Select>
							</Form.Item>
							{masterExposeType === 'Ingress' && (
								<Form.Item
									name="masterIngressClassName"
									required
									label="负载均衡选择"
									rules={[
										{
											required: true,
											message: '请选择负载均衡'
										}
									]}
								>
									<Select
										value={masterIngressClassName?.value}
										placeholder="请选择负载均衡"
										dropdownMatchSelectWidth={false}
										onChange={(value) =>
											handleIngressChange(value, true)
										}
									>
										{masterIngresses.map(
											(item: IngressItemProps) => {
												return (
													<Select.Option
														key={
															item.ingressClassName
														}
														value={
															item.ingressClassName
														}
													>
														<div className="flex-space-between">
															{
																item.ingressClassName
															}
															<Tag
																color={
																	item.type ===
																	'nginx'
																		? 'cyan'
																		: 'green'
																}
															>
																{item.type}
															</Tag>
														</div>
													</Select.Option>
												);
											}
										)}
									</Select>
								</Form.Item>
							)}
							<Form.Item
								label="对外端口配置"
								name="masterExposePort"
								rules={[
									{
										required: true,
										message: '请输入对外端口配置'
									},
									{
										validator(rule, value) {
											const masterPort =
												form.getFieldValue(
													'slaveExposePort'
												);
											if (selectedRow) {
												if (
													params.clusterId ===
													selectedRow.clusterId
												) {
													if (
														value &&
														value === masterPort
													) {
														return Promise.reject(
															new Error(
																'对外配置端口和灾备服务重复'
															)
														);
													} else {
														return Promise.resolve();
													}
												} else {
													return Promise.resolve();
												}
											} else {
												return Promise.resolve();
											}
										}
									}
								]}
							>
								<InputNumber
									value={exposePort}
									onChange={(value: number | null) =>
										setExposePort(value)
									}
									placeholder={`请输入规定范围以内的端口`}
									style={{ width: '100%' }}
								/>
							</Form.Item>
							{masterExposeType === 'Ingress' &&
								masterIngressClassName?.type !== 'traefik' && (
									<Row>
										<Col span={6}></Col>
										<Col span={18}>
											<div>
												当前负载均衡相关端口组为
												{ingressPortArray.join('-')}
												请在端口组范围内选择端口
											</div>
										</Col>
									</Row>
								)}
							{masterExposeType === 'Ingress' &&
								masterIngressClassName?.type === 'traefik' && (
									<Row>
										<Col span={6}></Col>
										<Col span={18}>
											<div>
												当前负载均衡相关端口组为
												{masterIngressClassName.traefikPortList
													.map(
														(item) =>
															`${item.startPort}-${item.endPort}`
													)
													.join(',')}
												,请在端口组范围内选择端口
											</div>
										</Col>
									</Row>
								)}
							{masterExposeType === 'NodePort' && (
								<Row>
									<Col span={6}></Col>
									<Col span={18}>
										<div>
											当前端口组为
											{nodePortArray.join('-')}
											,请在端口组范围内选择端口
										</div>
									</Col>
								</Row>
							)}
						</Collapse.Panel>
					</Collapse>
					<Collapse defaultActiveKey={['2']}>
						<Collapse.Panel
							header={
								<>
									<span>灾备服务</span>
									{!slaveInfo ? (
										<span style={{ color: '#ff4d4f' }}>
											(未配置)
										</span>
									) : null}
								</>
							}
							key="2"
							forceRender
						>
							<Form.Item
								required
								label="暴露方式"
								name="slaveExposeType"
								initialValue={slaveExposeType}
							>
								<Select
									value={slaveExposeType}
									onChange={(value) =>
										onExposeTypeChange(value, false)
									}
									placeholder="请选择暴露方式"
								>
									<Select.Option value="NodePort">
										NodePort
									</Select.Option>
									<Select.Option value="Ingress">
										Ingress
									</Select.Option>
								</Select>
							</Form.Item>
							{slaveExposeType === 'Ingress' && (
								<Form.Item
									name="slaveIngressClassName"
									required
									label="负载均衡选择"
									rules={[
										{
											required: true,
											message: '请选择负载均衡'
										}
									]}
								>
									<Select
										value={slaveIngressClassName?.value}
										placeholder="请选择负载均衡"
										dropdownMatchSelectWidth={false}
										onChange={(value) =>
											handleIngressChange(value, false)
										}
									>
										{slaveIngresses.map(
											(item: IngressItemProps) => {
												return (
													<Select.Option
														key={
															item.ingressClassName
														}
														value={
															item.ingressClassName
														}
													>
														<div className="flex-space-between">
															{
																item.ingressClassName
															}
															<Tag
																color={
																	item.type ===
																	'nginx'
																		? 'cyan'
																		: 'green'
																}
															>
																{item.type}
															</Tag>
														</div>
													</Select.Option>
												);
											}
										)}
									</Select>
								</Form.Item>
							)}
							<Form.Item
								label="对外端口配置"
								name="slaveExposePort"
								rules={[
									{
										required: true,
										message: '请输入对外端口配置'
									},
									{
										validator(rule, value, callback) {
											const masterPort =
												form.getFieldValue(
													'masterExposePort'
												);
											if (selectedRow) {
												if (
													params.clusterId ===
													selectedRow.clusterId
												) {
													if (
														value &&
														value === masterPort
													) {
														return Promise.reject(
															new Error(
																'对外配置端口和源服务重复'
															)
														);
													} else {
														return Promise.resolve();
													}
												} else {
													return Promise.resolve();
												}
											} else {
												return Promise.resolve();
											}
										}
									}
								]}
							>
								<InputNumber
									value={exposePort}
									onChange={(value: number | null) =>
										setExposePort(value)
									}
									placeholder={`请输入规定范围以内的端口`}
									style={{ width: 250 }}
								/>
							</Form.Item>
							{slaveExposeType === 'Ingress' &&
								slaveIngressClassName?.type !== 'traefik' && (
									<Row>
										<Col span={6}></Col>
										<Col span={18}>
											<div>
												当前负载均衡相关端口组为
												{ingressPortArray.join('-')}
												请在端口组范围内选择端口
											</div>
										</Col>
									</Row>
								)}
							{slaveExposeType === 'Ingress' &&
								slaveIngressClassName?.type === 'traefik' && (
									<Row>
										<Col span={6}></Col>
										<Col span={18}>
											<div>
												当前负载均衡相关端口组为
												{slaveIngressClassName.traefikPortList
													.map(
														(item) =>
															`${item.startPort}-${item.endPort}`
													)
													.join(',')}
												,请在端口组范围内选择端口
											</div>
										</Col>
									</Row>
								)}
							{slaveExposeType === 'NodePort' && (
								<Row>
									<Col span={6}></Col>
									<Col span={18}>
										<div>
											当前端口组为
											{nodePortArray.join('-')}
											,请在端口组范围内选择端口
										</div>
									</Col>
								</Row>
							)}
						</Collapse.Panel>
					</Collapse>
				</Form>
				{name === 'elasticsearch' ? (
					<>
						<h2 className="mt-16">Minio链接地址</h2>
						<Form
							form={sourceMinioForm}
							labelAlign="left"
							{...formItemLayout618}
							style={{ width: '60%' }}
							onFieldsChange={(changedFields, allFields) => {
								console.log(
									allFields.every((item) => item.value)
								);

								allFields.every((item) => item.value)
									? setMasterMinioInfo(true)
									: setMasterMinioInfo(false);
							}}
						>
							<Collapse defaultActiveKey={['1']}>
								<Collapse.Panel
									header={
										<>
											<span>源服务</span>
											{!masterMinioInfo ? (
												<span
													style={{ color: '#ff4d4f' }}
												>
													(未配置)
												</span>
											) : null}
										</>
									}
									key="1"
									forceRender
								>
									<Form.Item
										label="Minio地址"
										required
										style={{ marginBottom: 0 }}
									>
										<Input.Group compact>
											<Form.Item
												name={['address', 'protocol']}
												rules={[
													{
														required: true,
														message: '请选择协议'
													}
												]}
											>
												<Select
													placeholder="请选择协议"
													style={{ width: 100 }}
												>
													<Select.Option value="http">
														http
													</Select.Option>
													<Select.Option value="https">
														https
													</Select.Option>
												</Select>
											</Form.Item>
											<Form.Item
												name={['address', 'host']}
												rules={[
													{
														required: true,
														message:
															'请输入链接地址'
													}
												]}
											>
												<Input
													style={{ width: '100%' }}
													placeholder="请输入链接地址"
												/>
											</Form.Item>
											<Form.Item
												name={['address', 'port']}
												rules={[
													{
														required: true,
														message: '请输入端口'
													}
												]}
											>
												<InputNumber
													style={{ width: '100%' }}
													placeholder="请输入端口"
												/>
											</Form.Item>
										</Input.Group>
									</Form.Item>
									<Form.Item
										name="bucketName"
										required
										label="Bucket名称"
										rules={[
											{
												required: true,
												message: '请输入Bucket名称'
											}
										]}
									>
										<Input
											style={{ width: '50%' }}
											placeholder="请输入Bucket名称"
										/>
									</Form.Item>
									<Form.Item
										name="accessKeyId"
										required
										label="用户名"
										rules={[
											{
												required: true,
												message: '请输入用户名'
											}
										]}
									>
										<Input
											style={{ width: '50%' }}
											placeholder="请输入用户名"
										/>
									</Form.Item>
									<Form.Item
										name="secretAccessKey"
										required
										label="密码"
										rules={[
											{
												required: true,
												message: '请输入密码'
											}
										]}
									>
										<Input.Password
											style={{ width: '50%' }}
											placeholder="请输入密码"
										/>
									</Form.Item>
									<Button
										type="primary"
										onClick={() => testMinio('source')}
									>
										连接测试
									</Button>
									{sourceMinioStatus !== undefined && (
										<>
											{sourceMinioStatus ? (
												<CheckCircleFilled
													style={{
														color: '#52c41a',
														margin: '0 4px 0 8px'
													}}
												/>
											) : (
												<CloseCircleFilled
													style={{
														color: '#ff4d4f',
														margin: '0 4px 0 8px'
													}}
												/>
											)}
											<span>
												{sourceMinioStatus
													? '连接成功'
													: '连接失败'}
											</span>
										</>
									)}
								</Collapse.Panel>
							</Collapse>
						</Form>
						{selectedRow ? (
							<Form
								form={backupMinioForm}
								labelAlign="left"
								{...formItemLayout618}
								style={{ width: '60%' }}
								onFieldsChange={(changedFields, allFields) =>
									allFields.every((item) => item.value)
										? setSlaveMinioInfo(true)
										: setSlaveMinioInfo(false)
								}
							>
								<Collapse defaultActiveKey={['1']}>
									<Collapse.Panel
										header={
											<>
												<span>灾备服务</span>
												{!slaveMinioInfo ? (
													<span
														style={{
															color: '#ff4d4f'
														}}
													>
														(未配置)
													</span>
												) : null}
											</>
										}
										key="1"
										forceRender
									>
										<Form.Item
											label="Minio地址"
											required
											style={{ marginBottom: 0 }}
										>
											<Input.Group compact>
												<Form.Item
													name={[
														'address',
														'protocol'
													]}
													rules={[
														{
															required: true,
															message:
																'请选择协议'
														}
													]}
												>
													<Select
														placeholder="请选择协议"
														style={{ width: 100 }}
													>
														<Select.Option value="http">
															http
														</Select.Option>
														<Select.Option value="https">
															https
														</Select.Option>
													</Select>
												</Form.Item>
												<Form.Item
													name={['address', 'host']}
													rules={[
														{
															required: true,
															message:
																'请输入链接地址'
														}
													]}
												>
													<Input
														style={{
															width: '100%'
														}}
														placeholder="请输入链接地址"
													/>
												</Form.Item>
												<Form.Item
													name={['address', 'port']}
													rules={[
														{
															required: true,
															message:
																'请输入端口'
														}
													]}
												>
													<InputNumber
														style={{
															width: '100%'
														}}
														placeholder="请输入端口"
													/>
												</Form.Item>
											</Input.Group>
										</Form.Item>
										<Form.Item
											name="bucketName"
											required
											label="Bucket名称"
											rules={[
												{
													required: true,
													message: '请输入Bucket名称'
												}
											]}
										>
											<Input
												style={{ width: '50%' }}
												placeholder="请输入Bucket名称"
											/>
										</Form.Item>
										<Form.Item
											name="accessKeyId"
											required
											label="用户名"
											rules={[
												{
													required: true,
													message: '请输入用户名'
												}
											]}
										>
											<Input
												style={{ width: '50%' }}
												placeholder="请输入用户名"
											/>
										</Form.Item>
										<Form.Item
											name="secretAccessKey"
											required
											label="密码"
											rules={[
												{
													required: true,
													message: '请输入密码'
												}
											]}
										>
											<Input.Password
												style={{ width: '50%' }}
												placeholder="请输入密码"
											/>
										</Form.Item>
										<Button
											type="primary"
											onClick={() => testMinio('backup')}
										>
											连接测试
										</Button>
										{backupMinioStatus !== undefined && (
											<>
												{backupMinioStatus ? (
													<CheckCircleFilled
														style={{
															color: '#52c41a',
															margin: '0 4px 0 8px'
														}}
													/>
												) : (
													<CloseCircleFilled
														style={{
															color: '#ff4d4f',
															margin: '0 4px 0 8px'
														}}
													/>
												)}
												<span>
													{backupMinioStatus
														? '连接成功'
														: '连接失败'}
												</span>
											</>
										)}
									</Collapse.Panel>
								</Collapse>
							</Form>
						) : null}
					</>
				) : null}
				<Divider />
				<div className="mt-16">
					<Button
						type="primary"
						disabled={judgeDisabled()?.disabled}
						title={judgeDisabled()?.title}
						onClick={onOk}
						loading={btnLoading}
					>
						确定
					</Button>
					<Button
						style={{ marginLeft: 16 }}
						onClick={() => history.goBack()}
					>
						取消
					</Button>
				</div>
			</ProContent>
		</ProPage>
	);
};

export default AddDisaster;

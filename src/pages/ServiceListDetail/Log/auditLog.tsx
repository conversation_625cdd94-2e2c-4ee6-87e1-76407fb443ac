import React, { useState, useEffect } from 'react';
import { useHistory, useParams } from 'react-router';
import { notification, Switch, TablePaginationConfig, Modal } from 'antd';
import moment from 'moment';
import ProTable from '@/components/ProTable';
import Auth from '@/components/Auth';
import { getAuditSql, uploadLogSwitch } from '@/services/middleware';
import { controlledOperationDisabled, nullRender } from '@/utils/utils';
import { CommonLogProps, DetailParams } from '../detail';
import './log.less';
import useRefresh from '@/utils/useRefresh';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import ComponentsNull from '@/components/ComponentsNull';
import { maintenances } from '@/utils/const';
const { confirm } = Modal;
function SqlAudit(props: CommonLogProps): JSX.Element {
	const { onRefresh: onAllRefresh, audit, lock, logging, isAdmin } = props;
	const params: DetailParams = useParams();
	const history = useHistory();
	const {
		clusterId,
		name,
		namespace,
		middlewareName,
		chartVersion,
		type,
		aliasName
	} = params;
	const [auditLog, setAuditLog] = useState<boolean>(audit || false);
	const [dataSource, setDataSource] = useState<any[]>([]);
	const [keyword, setKeyword] = useState<string>('');
	const [current, setCurrent] = useState<number>(1); // * 页码
	const [pageSize, setPageSize] = useState<number>(); // * 每页条数
	const [total, setTotal] = useState<number | undefined>(10); // * 总数
	const [sortOrder, setSortOrder] = useState<string>('desc');
	const auditLogOperatorId = maintenances['Audit Log Collection'];
	const downloadAuditLogOperatorId = maintenances['Audit Log Download'];
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		if (audit) {
			getData(keyword);
		}
	}, [audit, refreshKey]);
	const getData = (value: string) => {
		getAuditSql({
			searchWord: value,
			clusterId,
			namespace,
			middlewareName,
			current: value ? 1 : current,
			sortOrder: sortOrder,
			size: pageSize || 10,
			type: name
		}).then((res) => {
			if (res.success) {
				res.data &&
					setDataSource(
						res.data.data.map((item: any, index: number) => {
							item.index = index;
							return item;
						})
					);
				res.data && setTotal(res.data.count);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const handleSearch: (value: string) => void = (value: string) => {
		setKeyword(value);
		getData(value);
	};
	const handleChange = (value: boolean) => {
		confirm({
			title: '操作确认',
			content: (
				<>
					<p>{value ? '开启' : '关闭'}审计日志收集会导致服务重启</p>
					<p>请确认是否{value ? '开启' : '关闭'}?</p>
				</>
			),
			onOk: async () => {
				setAuditLog(value);
				const sendData = {
					clusterId,
					namespace,
					middlewareName,
					chartName: name,
					chartVersion,
					type: name,
					audit: value
				};
				await ExecuteOrderFuc();
				return uploadLogSwitch(sendData).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: `审计日志收集${
								value ? '开启' : '关闭'
							}成功`
						});
						setTimeout(() => {
							onAllRefresh && onAllRefresh();
						}, 5000);
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			}
		});
	};

	const createTimeRender = (value: string) => {
		if (!value) return '--';
		return moment(value).format('YYYY-MM-DD HH:mm:ss');
	};

	const pageChange = (
		pagination: TablePaginationConfig,
		filters: any,
		sorter: any
	) => {
		sorter?.order === 'ascend' ? setSortOrder('asc') : setSortOrder('desc');
		setCurrent(pagination.current || 1);
		setPageSize(pagination.pageSize);
		getAuditSql({
			searchWord: keyword,
			clusterId,
			namespace,
			middlewareName,
			current: pagination.current || 1,
			size: pagination.pageSize || 10,
			type: name,
			sortOrder: sorter?.order === 'ascend' ? 'asc' : 'desc'
		}).then((res) => {
			if (res.success) {
				res.data &&
					setDataSource(
						res.data.data.map((item: any, index: number) => {
							item.index = index;
							return item;
						})
					);
				res.data && setTotal(res.data.count);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	if (!logging) {
		return (
			<ComponentsNull
				isAdmin={isAdmin}
				title={
					isAdmin
						? '该功能需要日志采集组件工具支持，您可前往“集群——>平台组件“进行安装'
						: '该功能需要日志采集组件工具支持，请联系管理员进行组件安装'
				}
			/>
		);
	}
	return (
		<>
			<Auth code="auditLogEnabled">
				<ul className="form-layout display-flex flex-align">
					<li className="display-flex form-li">
						<label className="form-name">
							<span style={{ marginRight: 8 }}>审计日志收集</span>
						</label>
						<div
							className={`form-content display-flex flex-align standard-log`}
						>
							<div className="switch">
								{auditLog ? '已开启' : '关闭'}
								<Switch
									checked={auditLog}
									disabled={
										// !operateFlag ||
										controlledOperationDisabled(
											'maintenance',
											lock
										)
									}
									onChange={(checked: boolean) => {
										WorkOrderFuc(
											() => {
												handleChange(checked);
											},
											lock,
											middlewareName,
											auditLogOperatorId,
											history,
											type,
											name,
											aliasName,
											clusterId,
											namespace
										);
									}}
									size="small"
									style={{
										marginLeft: 16,
										verticalAlign: 'middle'
									}}
								/>
							</div>
						</div>
					</li>
				</ul>
			</Auth>
			<div className="zeus-log-content">
				<div
					title="启用该功能后可操作，查看日志"
					className="zeus-log-block-disabled"
					style={{
						display: auditLog ? 'none' : 'block',
						top: '-10px',
						left: '-10px',
						width: '102%',
						height: '109%'
					}}
				></div>
				<ProTable
					dataSource={dataSource}
					rowKey="index"
					bordered={false}
					pagination={{
						total: total,
						current: current,
						pageSize: pageSize
					}}
					search={{
						onSearch: handleSearch,
						placeholder: '请输入执行语句进行搜索',
						style: { width: '360px' }
					}}
					onChange={pageChange}
				>
					<ProTable.Column
						title="操作ip"
						dataIndex="ip"
						width={100}
					/>
					<ProTable.Column
						title="操作账户"
						dataIndex="user"
						width={100}
						render={nullRender}
					/>
					<ProTable.Column
						title="数据库名称"
						dataIndex="db"
						render={nullRender}
						width={100}
					/>
					<ProTable.Column
						title="执行语句"
						dataIndex="query"
						render={nullRender}
					/>
					<ProTable.Column
						title="执行时间"
						dataIndex="queryDate"
						render={createTimeRender}
						sorter={true}
						width={160}
					/>
				</ProTable>
			</div>
		</>
	);
}

export default SqlAudit;

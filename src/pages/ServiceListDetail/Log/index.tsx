import React, { useState } from 'react';
import { Menu } from 'antd';
import DefaultPicture from '@/components/DefaultPicture';
import RealtimeLog from './realtimeLog';
import SlowLog from './slowLog';
import StandardLog from './standardLog';
import LogFile from './logFile';
import { DetailParams, LogProps } from '../detail';
import SqlAudit from './auditLog';
import SecondContent from '@/components/SecondContent';
import { useParams } from 'react-router';

export default function Log(props: LogProps): JSX.Element {
	const {
		data,
		customMid = false,
		logging,
		onRefresh,
		role,
		subMenu
		// operateFlag
	} = props;
	const { name }: DetailParams = useParams();
	const hasWebpage = window.location.href.includes('webpage');

	// * 判断当前角色权限是否显示该菜单
	const showMenu: any = (code: string) =>
		hasWebpage || subMenu?.find((item: any) => item.name === code);
	const items: any = [];
	showMenu('realTimeLog') &&
		items.push({ label: '实时日志', key: 'realtime' });
	!customMid &&
		showMenu('standardLog') &&
		items.push({ label: '标准日志', key: 'standard' });
	!customMid &&
		showMenu('logFile') &&
		items.push({ label: '日志文件', key: 'file' });
	((!customMid && name === 'mysql') || name === 'postgresql') &&
		showMenu('auditLog') &&
		items.push({ label: '审计日志', key: 'audit' });
	name === 'mysql' &&
		showMenu('slowLog') &&
		items.push({ label: '慢日志查看', key: 'slow' });
	const [selectedKey, setSelectedKey] = useState<string[]>([items[0].key]);

	const menuSelect = (item: any) => {
		setSelectedKey(item.keyPath);
	};

	const ConsoleMenu = () => {
		return (
			<Menu
				selectedKeys={selectedKey}
				onClick={menuSelect}
				style={{ height: '100%' }}
				items={items}
				mode="inline"
				className="serve-alarm-menu"
			/>
		);
	};
	const childrenRender = (selectedKey: string) => {
		switch (selectedKey) {
			case 'realtime':
				return <RealtimeLog />;
			case 'standard':
				return (
					<StandardLog
						lock={data.lock}
						stdoutEnabled={data.stdoutEnabled}
						onRefresh={onRefresh}
						logging={logging}
						isAdmin={role.isAdmin}
					/>
				);
			case 'file':
				return (
					<LogFile
						lock={data.lock}
						filelogEnabled={data.filelogEnabled}
						onRefresh={onRefresh}
						logging={logging}
						isAdmin={role.isAdmin}
					/>
				);
			case 'slow':
				return (
					<SlowLog
						lock={data.lock}
						slowSql={data.slowSql}
						onRefresh={onRefresh}
						logging={logging}
						isAdmin={role.isAdmin}
					/>
				);
			case 'audit':
				return (
					<SqlAudit
						lock={data.lock}
						audit={data.audit}
						onRefresh={onRefresh}
						logging={logging}
						isAdmin={role.isAdmin}
					/>
				);
			default:
				return null;
		}
	};
	if (customMid && !(data.capabilities || []).includes('log')) {
		return <DefaultPicture />;
	}
	return (
		<SecondContent menu={<ConsoleMenu />} style={{ margin: 0, padding: 0 }}>
			{childrenRender(selectedKey[0])}
		</SecondContent>
	);
}

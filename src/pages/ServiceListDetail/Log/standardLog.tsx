import React, { useState, useEffect } from 'react';
import {
	Select,
	Row,
	Col,
	Input,
	Button,
	Switch,
	Popover,
	notification,
	Modal
} from 'antd';
import {
	ArrowsAltOutlined,
	ShrinkOutlined,
	DownloadOutlined,
	QuestionCircleOutlined
} from '@ant-design/icons';
import { useHistory, useParams } from 'react-router';
import moment, { Moment } from 'moment';
import Auth from '@/components/Auth';
import { Controlled as CodeMirror } from 'react-codemirror2';
import TimeSelect from '@/components/TimeSelect';
import ComponentsNull from '@/components/ComponentsNull';
import {
	getPods,
	getStandardLogFiles,
	getLogDetail,
	download,
	uploadLogSwitch
} from '@/services/middleware';
import { maintenances, searchTypes } from '@/utils/const';
import transTime from '@/utils/transTime';

import 'codemirror/lib/codemirror.css';
import 'codemirror/theme/twilight.css';
import './log.less';
import '../detail.less';

import {
	CommonLogProps,
	ContainerItem,
	DetailParams,
	DownLoadLogSendData,
	LogDetailItem,
	LogFileItem,
	PodItem
} from '../detail';
import { ListPanel } from '@/components/ListCard';
import useRefresh from '@/utils/useRefresh';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { controlledOperationDisabled } from '@/utils/utils';

const { Option } = Select;
const { confirm } = Modal;
export default function StandardLog(props: CommonLogProps): JSX.Element {
	const { onRefresh, stdoutEnabled, lock, logging, isAdmin } = props;
	const params: DetailParams = useParams();
	const {
		clusterId,
		name,
		namespace,
		middlewareName,
		chartVersion,
		type,
		aliasName
	} = params;
	const history = useHistory();
	const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
	const [logs, setLogs] = useState<string>('');

	const options = {
		mode: 'xml',
		theme: 'twilight',
		readOnly: true,
		lineNumbers: true,
		fullScreen: false,
		lineWrapping: true
	};

	const [pod, setPod] = useState<string>('all');
	const [podList, setPodList] = useState<PodItem[]>([]);
	const [container, setContainer] = useState<string>('all');
	const [searchType, setSearchType] = useState<string>('matchPhrase');
	const [containerList, setContainerList] = useState<ContainerItem[]>([]);
	const [keyword, setKeyword] = useState<string>('');
	const [logFiles, setLogFiles] = useState<LogFileItem[]>([]); // 日志文件列表
	const [currentLogFile, setCurrentLogFile] = useState<LogFileItem>(); // 选中的日志文件
	const defaultStart = moment().subtract({ hours: 1 });
	const [rangeTime, setRangeTime] = useState<Moment[]>([
		defaultStart,
		moment()
	]);
	const [scrollId, setScrollId] = useState();
	const [logList, setLogList] = useState<string[]>([]);
	const [total, setTotal] = useState(0);
	const [standardLog, setStandardLog] = useState<boolean>(
		stdoutEnabled || false
	);
	const standardLogOperatorId = maintenances['Standard Log Collection'];
	const downloadStandardLogOperatorId = maintenances['Standard Log Download'];
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	const changePod = (value: string) => {
		setPod(value);
		if (value === 'all') {
			setContainer('all');
			return;
		}
		for (let i = 0; i < podList.length; i++) {
			if (value === podList[i].podName) {
				setContainerList(podList[i].containers);
				if (podList[i].containers.length > 0) {
					setContainer(podList[i].containers[0].name);
				}
				break;
			}
		}
	};

	useEffect(() => {
		if (stdoutEnabled) {
			getPods({ clusterId, namespace, middlewareName, type: name }).then(
				(res) => {
					if (res.success) {
						setPodList(res.data.pods);
						if (res.data.pods.length > 0) {
							setContainerList(res.data.pods[0].containers);
						}
						setLogFiles([]);
						setCurrentLogFile(undefined);
						setLogList([]);
					}
				}
			);
		}
	}, [stdoutEnabled, refreshKey]);

	useEffect(() => {
		setLogs(logList.join('\n'));
	}, [logList]);

	useEffect(() => {
		if (currentLogFile) {
			const [start, end] = rangeTime;
			const startTime = transTime.local2gmt2(start);
			const endTime = transTime.local2gmt2(end);
			const sendData: DownLoadLogSendData = {
				clusterId: clusterId,
				namespace: namespace,
				middlewareName: middlewareName,
				logTimeEnd: endTime,
				logTimeStart: startTime,
				pageSize: 500,
				podLog: true,
				pod: pod,
				container: container,
				searchWord: keyword,
				searchType: searchType,
				middlewareType: name,
				logPath: currentLogFile.logPath
			};
			if (pod === 'all') delete sendData.pod;
			if (container === 'all') delete sendData.container;
			getLog(sendData);
		}
	}, [currentLogFile]);

	// 查询日志文件
	const getLogFiles = (sendData: any) => {
		getStandardLogFiles(sendData).then((res) => {
			if (res.success) {
				if (res.data.length > 0) {
					res.data.forEach((item: any, index: number, arr: any) => {
						item.children = [];
						arr.forEach((_item: any, _index: number) => {
							if (item.name === _item.name) {
								item.children.push(_item);
							}
						});
					});
					const obj: any = {};
					const result = res.data.reduce((item: any, next: any) => {
						(obj[next.name] ? '' : (obj[next.name] = true)) &&
							item.push(next);
						return item;
					}, []);
					setLogFiles(result);
					setCurrentLogFile(res.data[0]);
				} else {
					setLogFiles([]);
					setLogList(['根据当前查询条件未查询到任何日志文件。']);
					setCurrentLogFile(undefined);
				}
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	// * 查询日志详情
	const getLog = (sendData: any) => {
		getLogDetail(sendData).then((res) => {
			if (res.success) {
				if (sendData.scrollId) {
					if (res.data.log.length === 0) {
						notification.warning({
							message: '完成',
							description: '当前日志文件已经查询结束。'
						});
					} else {
						const logs = res.data.log.map(
							(item: LogDetailItem) => item.msg
						);
						setLogList([...logList, ...logs]);
					}
				} else {
					if (res.data.log.length === 0) {
						setLogList(['当前日志文件没有信息。']);
					} else {
						const log = res.data.log.map(
							(item: LogDetailItem) => item.msg
						);
						setScrollId(res.data.scrollId);
						setLogList(log);
						setTotal(res.data.totalHit);
					}
				}
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};

	const changeContainr = (value: string) => {
		setContainer(value);
	};

	const screenExtend = () => {
		setIsFullscreen(true);
	};

	const screenShrink = () => {
		setIsFullscreen(false);
	};

	const onTimeChange = (rangeTime: Moment[]) => {
		setRangeTime(rangeTime);
	};

	const changeSearchType = (value: string) => {
		setSearchType(value);
	};

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setKeyword(e.target.value);
	};

	const logFilesClick = (value: LogFileItem) => {
		setLogList([]);
		setCurrentLogFile(value);
	};
	// 查询文件列表
	const handleClick = () => {
		const [start, end] = rangeTime;
		const startTime = transTime.local2gmt2(start);
		const endTime = transTime.local2gmt2(end);
		const sendData: DownLoadLogSendData = {
			clusterId: clusterId,
			namespace: namespace,
			middlewareName: middlewareName,
			logTimeEnd: endTime,
			logTimeStart: startTime,
			pageSize: 500,
			podLog: true,
			pod: pod,
			container: container,
			searchWord: keyword,
			searchType: searchType,
			middlewareType: name
		};
		if (pod === 'all') delete sendData.pod;
		if (container === 'all') delete sendData.container;
		// 获取日志文件
		getLogFiles(sendData);
	};
	// 更多日志
	const moreLogs = () => {
		const [start, end] = rangeTime;
		const startTime = transTime.local2gmt2(start);
		const endTime = transTime.local2gmt2(end);
		const sendData: DownLoadLogSendData = {
			clusterId: clusterId,
			namespace: namespace,
			middlewareName: middlewareName,
			logTimeEnd: endTime,
			logTimeStart: startTime,
			pageSize: 500,
			podLog: true,
			pod: pod,
			container: container,
			searchWord: keyword,
			searchType: searchType,
			middlewareType: name,
			scrollId: scrollId,
			logPath: currentLogFile?.logPath
		};
		if (pod === 'all') delete sendData.pod;
		if (container === 'all') delete sendData.container;
		getLog(sendData);
	};

	const downloadLog = async () => {
		const [start, end] = rangeTime;
		const startTime = transTime.local2gmt2(start);
		const endTime = transTime.local2gmt2(end);
		const sendData: DownLoadLogSendData = {
			clusterId: clusterId,
			namespace: namespace,
			middlewareName: middlewareName,
			logTimeEnd: endTime,
			logTimeStart: startTime,
			pageSize: 500,
			podLog: true,
			pod: pod,
			container: container,
			searchWord: keyword,
			searchType: searchType,
			middlewareType: name,
			scrollId: scrollId ? scrollId : '',
			logPath: currentLogFile?.logPath
		};
		if (pod === 'all') delete sendData.pod;
		if (container === 'all') delete sendData.container;
		const _url = download(sendData);
		const url = `${_url}?logTimeEnd=${endTime}&logTimeStart=${startTime}&pageSize=500&podLog=true${
			pod === 'all' ? '' : `&pod=${pod}`
		}${container === 'all' ? '' : `&container=${container}`}&searchWord=${
			keyword || ''
		}&searchType=${searchType}&middlewareType=${name}&logPath=${
			currentLogFile?.logPath
		}`;
		await ExecuteOrderFuc();
		window.open(url, '_target');
	};
	const onChange = (checked: boolean) => {
		confirm({
			title: '操作确认',
			content: (
				<>
					<p>
						{checked ? '开启' : '关闭'}
						标准日志会导致服务重启
					</p>
					<p>
						请确认是否
						{checked ? '开启' : '关闭'}?
					</p>
				</>
			),
			onOk: async () => {
				const sendData = {
					clusterId,
					namespace,
					middlewareName,
					chartName: name,
					chartVersion,
					type: name,
					stdoutEnabled: checked
				};
				await ExecuteOrderFuc();
				return uploadLogSwitch(sendData).then((res) => {
					if (res.success) {
						setStandardLog(checked);
						notification.success({
							message: '成功',
							description: `标准日志${
								checked ? '开启' : '关闭'
							}成功！`
						});
						setTimeout(() => {
							onRefresh && onRefresh();
						}, 5000);
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			}
		});
	};
	if (!logging) {
		return (
			<ComponentsNull
				isAdmin={isAdmin}
				title={
					isAdmin
						? '该功能需要日志采集组件工具支持，您可前往“集群——>平台组件“进行安装'
						: '该功能需要日志采集组件工具支持，请联系管理员进行组件安装'
				}
			/>
		);
	}
	return (
		<div>
			<Auth code="standardLogEnabled">
				<ul className="form-layout display-flex flex-align">
					<li className="display-flex form-li">
						<label className="form-name">
							<span style={{ marginRight: 8 }}>标准日志收集</span>
							<Popover
								content={
									<span
										style={{
											lineHeight: '18px'
										}}
									>
										通过logpilot采集出来的实时日志
									</span>
								}
							>
								<QuestionCircleOutlined />
							</Popover>
						</label>
						<div
							className={`form-content display-flex flex-align standard-log`}
						>
							<div className="switch">
								{standardLog ? '已开启' : '关闭'}
								<Switch
									checked={standardLog}
									disabled={controlledOperationDisabled(
										'maintenance',
										lock
									)}
									onChange={(checked) => {
										WorkOrderFuc(
											() => {
												onChange(checked);
											},
											lock,
											middlewareName,
											standardLogOperatorId,
											history,
											type,
											name,
											aliasName,
											clusterId,
											namespace
										);
									}}
									size="small"
									style={{
										marginLeft: 16,
										verticalAlign: 'middle'
									}}
								/>
							</div>
						</div>
					</li>
				</ul>
			</Auth>
			<div className="zeus-log-content">
				<div
					title="启用该功能后可操作，查看日志"
					className="zeus-log-block-disabled"
					style={{ display: standardLog ? 'none' : 'block' }}
				></div>
				<div className={`display-flex filter-wrapper`}>
					<div className="filter-item-standard">
						<Row align="middle">
							<Col span={5}>
								<label>实例列表</label>
							</Col>
							<Col span={19}>
								<Select
									placeholder="请选择实例"
									value={pod}
									onChange={changePod}
									style={{ width: '100%' }}
									dropdownMatchSelectWidth={false}
								>
									<Option value="all">全部</Option>
									{podList.map((item, index) => (
										<Option
											value={item.podName}
											key={index}
										>
											{item.podName}
										</Option>
									))}
								</Select>
							</Col>
						</Row>
					</div>
					<div className="filter-item-standard">
						<Row align="middle">
							<Col offset={2} span={3}>
								<label>容器列表</label>
							</Col>
							<Col span={19}>
								<Select
									placeholder="请选择容器"
									value={container}
									onChange={changeContainr}
									style={{ width: '100%' }}
									dropdownMatchSelectWidth={false}
								>
									<Option value="all">全部</Option>
									{pod !== 'all' &&
										containerList.map((item, index) => (
											<Option
												value={item.name}
												key={index}
											>
												{item.name}
											</Option>
										))}
								</Select>
							</Col>
						</Row>
					</div>
					<div className="filter-item-standard">
						<Row align="middle">
							<Col span={5}>
								<label>搜索类型</label>
							</Col>
							<Col span={19}>
								<Select
									placeholder="请选择搜索类型"
									value={searchType}
									onChange={changeSearchType}
									style={{ width: '100%' }}
									dropdownMatchSelectWidth={false}
								>
									{searchTypes.map((item) => (
										<Option
											key={item.value}
											value={item.value}
										>
											{item.label}
										</Option>
									))}
								</Select>
							</Col>
						</Row>
					</div>
					<div className="filter-item-standard">
						<Row align="middle">
							<Col offset={2} span={3}>
								<label>关键字</label>
							</Col>
							<Col span={19}>
								<Input
									style={{ width: '100%' }}
									value={keyword}
									onChange={handleChange}
								/>
							</Col>
						</Row>
					</div>
					<div className="filter-item-standard">
						<Row align="middle">
							<TimeSelect
								source="log"
								timeSelect={onTimeChange}
							/>
						</Row>
					</div>
					<div className="filter-item-standard">
						<Button type="primary" onClick={handleClick}>
							搜索
						</Button>
					</div>
				</div>
				{logFiles.length > 0 && (
					<div className="display-flex flex-column">
						<div>服务节点数：{logFiles.length}</div>
						<div className="log-file-flex-wrapper">
							{logFiles.map((item: any) => {
								if (item.children) {
									return (
										<ListPanel
											title={item.name}
											subTitle=""
											icon={null}
											key={item.name}
											className="log-file-box-panel"
											render={item.children.map(
												(res: any) => {
													return (
														<div
															className={`log-file-box'
															} ${
																(currentLogFile &&
																	currentLogFile.logPath) ===
																res.logPath
																	? 'active'
																	: ''
															}`}
															key={res.logPath}
															onClick={() =>
																logFilesClick(
																	res
																)
															}
															title={res.logPath}
														>
															{res.logPath}
														</div>
													);
												}
											)}
										/>
									);
								} else {
									return (
										<div
											key={item.logPath}
											className={`log-file-box ${
												(currentLogFile &&
													currentLogFile.logPath) ===
												item.logPath
													? 'active'
													: ''
											}`}
											onClick={() => logFilesClick(item)}
										>
											{item.name}
										</div>
									);
								}
							})}
						</div>
					</div>
				)}
				<div
					className={`log-display ${
						isFullscreen ? 'log-full-screen' : ''
					}`}
					style={{ marginTop: 16 }}
				>
					<div className="title">
						<div className="display-inline-block">日志详情</div>
						<div className={`display-inline-block tips`}>
							<Auth code="standardLogExport">
								<div
									className={`display-inline-block btn ${
										controlledOperationDisabled(
											'maintenance',
											lock
										)
											? 'disabled'
											: ''
									}`}
									onClick={() => {
										if (
											!controlledOperationDisabled(
												'maintenance',
												lock
											)
										) {
											WorkOrderFuc(
												downloadLog,
												lock,
												middlewareName,
												downloadStandardLogOperatorId,
												history,
												type,
												name,
												aliasName,
												clusterId,
												namespace
											);
										}
									}}
								>
									日志导出 <DownloadOutlined />
								</div>
							</Auth>
							{!isFullscreen && (
								<ArrowsAltOutlined onClick={screenExtend} />
							)}
							{isFullscreen && (
								<ShrinkOutlined onClick={screenShrink} />
							)}
						</div>
					</div>
					<CodeMirror
						value={logs}
						options={options}
						className="log-codeMirror"
						onBeforeChange={() => console.log('')}
					/>
					{total > logList.length ? (
						<div className="foot">
							<div className="display-inline-block">
								<span className="foot-text" onClick={moreLogs}>
									更多日志 {'>'}
									{'>'}
								</span>
							</div>
						</div>
					) : null}
				</div>
			</div>
		</div>
	);
}

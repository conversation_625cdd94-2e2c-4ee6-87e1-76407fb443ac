import React, { useEffect, useState } from 'react';
import {
	Button,
	Input,
	Select,
	Row,
	Col,
	notification,
	Switch,
	Modal
} from 'antd';
import { VerticalAlignBottomOutlined } from '@ant-design/icons';
import { useHistory, useParams } from 'react-router';
import ProTable from '@/components/ProTable';
import TimeSelect from '@/components/TimeSelect';
import { getSlowLogs, uploadLogSwitch } from '@/services/middleware';
import transTime from '@/utils/transTime';
import moment, { Moment } from 'moment';
import NumberRange from '@/components/NumberRange';

import { api } from '@/api.json';
import ComponentsNull from '@/components/ComponentsNull';
import { maintenances, searchTypes } from '@/utils/const';
import { CommonLogProps, DetailParams } from '../detail';
import './log.less';
import useRefresh from '@/utils/useRefresh';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import { controlledOperationDisabled } from '@/utils/utils';
import Auth from '@/components/Auth';
const { confirm } = Modal;
const { Option } = Select;
// * 慢日志表格后端分页

export default function SlowLog(props: CommonLogProps): JSX.Element {
	const {
		onRefresh: onAllRefresh,
		slowSql,
		lock,
		logging,
		isAdmin
		// operateFlag
	} = props;
	const history = useHistory();
	const params: DetailParams = useParams();
	const {
		clusterId,
		name,
		namespace,
		middlewareName,
		chartVersion,
		type,
		aliasName
	} = params;
	const defaultStart = moment().subtract({ hours: 1 });
	const [rangeTime, setRangeTime] = useState<Moment[]>([
		defaultStart,
		moment()
	]);
	const [dataSource, setDataSource] = useState([]);
	const [fromQueryTime, setFromQueryTime] = useState<number | string>();
	const [toQueryTime, setToQueryTime] = useState<number | string>();
	const [searchType, setSearchType] = useState('');
	const [keyword, setKeyword] = useState('');
	const [slowLog, setSlowLog] = useState<boolean>(slowSql || false);
	const [visible, setVisible] = useState<boolean>(false);
	const slowLogOperatorId = maintenances['Slow Log Collection'];
	const downloadSlowLogOperatorId = maintenances['Slow Log Download'];
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		if (refreshKey > 0) {
			setDataSource([]);
		}
	}, [refreshKey]);
	const getData = (
		current: number,
		size: number,
		startTime: string,
		endTime: string,
		fromQueryTime = '',
		toQueryTime = '',
		searchType = '',
		keyword = ''
	) => {
		const sendData = {
			clusterId: clusterId,
			namespace: namespace,
			middlewareName: middlewareName,
			startTime: startTime,
			endTime: endTime,
			current: current,
			size: size,
			fromQueryTime: fromQueryTime,
			toQueryTime: toQueryTime,
			searchType: searchType,
			searchWord: keyword
		};
		getSlowLogs(sendData).then((res) => {
			if (res.success) {
				if (res.data.length > 0) {
					setDataSource(res.data);
				} else {
					setDataSource([]);
				}
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};

	const onTimeChange = (rangeTime: Moment[]) => {
		setRangeTime(rangeTime);
	};

	const timeRender = (value: string) => {
		return transTime.gmt2local(value);
	};

	const slowLogDownload = async () => {
		const url = `${api}/clusters/${clusterId}/middlewares/mysql/${middlewareName}/slowsql/file?startTime=${transTime.local2gmt2(
			rangeTime[0]
		)}&endTime=${transTime.local2gmt2(
			rangeTime[1]
		)}&searchType=${searchType}&searchWord=${keyword}&namespace=${namespace}`;
		await ExecuteOrderFuc();
		window.open(url);
	};

	const onSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setKeyword(e.target.value);
	};

	const onFilterChange = (value: string) => {
		setSearchType(value);
	};
	const handleChange = (value: boolean) => {
		confirm({
			title: '操作确认',
			content: (
				<>
					<p>{value ? '开启' : '关闭'}慢日志收集会导致服务重启</p>
					<p>请确认是否{value ? '开启' : '关闭'}?</p>
				</>
			),
			onOk: async () => {
				setSlowLog(value);
				const sendData = {
					clusterId,
					namespace,
					middlewareName,
					chartName: name,
					chartVersion,
					type: name,
					slowSql: value
				};
				await ExecuteOrderFuc();
				return uploadLogSwitch(sendData).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: `慢日志收集${
								value ? '开启' : '关闭'
							}成功`
						});
						setTimeout(() => {
							onAllRefresh && onAllRefresh();
						}, 5000);
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			}
		});
	};
	const handleClick = () => {
		setVisible(true);
		getData(
			1,
			10,
			transTime.local2gmt2(rangeTime[0]),
			transTime.local2gmt2(rangeTime[1]),
			fromQueryTime as string,
			toQueryTime as string,
			searchType,
			keyword
		);
	};
	const numberRange = (value: string[]) => {
		if (value[0] !== '' && value[1] !== '') {
			if (Number(value[0]) > Number(value[1])) {
				notification.error({
					message: '失败',
					description: '执行时长范围设置错误'
				});
				return;
			}
			setFromQueryTime(Number(value[0]));
			setToQueryTime(Number(value[1]));
		} else {
			setFromQueryTime('');
			setToQueryTime('');
		}
	};
	if (!logging) {
		return (
			<ComponentsNull
				isAdmin={isAdmin}
				title={
					isAdmin
						? '该功能需要日志采集组件工具支持，您可前往“集群——>平台组件“进行安装'
						: '该功能需要日志采集组件工具支持，请联系管理员进行组件安装'
				}
			/>
		);
	}
	return (
		<div>
			<Auth code="slowLogEnabled">
				<ul className="form-layout display-flex flex-align">
					<li className="display-flex form-li">
						<label className="form-name">
							<span style={{ marginRight: 8 }}>慢日志收集</span>
						</label>
						<div
							className={`form-content display-flex flex-align standard-log`}
						>
							<div className="switch">
								{slowLog ? '已开启' : '关闭'}
								<Switch
									checked={slowLog}
									disabled={
										// !operateFlag ||
										controlledOperationDisabled(
											'maintenance',
											lock
										)
									}
									onChange={(checked) => {
										WorkOrderFuc(
											() => {
												handleChange(checked);
											},
											lock,
											middlewareName,
											slowLogOperatorId,
											history,
											type,
											name,
											aliasName,
											clusterId,
											namespace
										);
									}}
									size="small"
									style={{
										marginLeft: 16,
										verticalAlign: 'middle'
									}}
								/>
							</div>
						</div>
					</li>
				</ul>
			</Auth>
			<div className="zeus-log-content">
				<div
					title="启用该功能后可操作，查看日志"
					className="zeus-log-block-disabled"
					style={{
						display: slowLog ? 'none' : 'block',
						top: '-10px',
						left: '-10px',
						width: '102%',
						height: '109%'
					}}
				></div>
				<div className={`display-flex filter-wrapper flex-center`}>
					<div className="filter-item-slowlog">
						<Row align="middle">
							<Col span={5}>
								<label>搜索类型</label>
							</Col>
							<Col span={19}>
								<Select
									placeholder="请选择搜索类型"
									value={searchType}
									onChange={onFilterChange}
									style={{ width: '100%' }}
									dropdownMatchSelectWidth={false}
								>
									{searchTypes.map((item) => (
										<Option
											key={item.value}
											value={item.value}
										>
											{item.label}
										</Option>
									))}
								</Select>
							</Col>
						</Row>
					</div>
					<div className="filter-item-slowlog">
						<Row align="middle">
							<Col offset={2} span={3}>
								<label>关键字</label>
							</Col>
							<Col span={19}>
								<Input
									style={{ width: '100%' }}
									value={keyword}
									onChange={onSearchChange}
								/>
							</Col>
						</Row>
					</div>
					<div className="filter-item-slowlog">
						<Row align="middle">
							<TimeSelect
								source="log"
								timeSelect={onTimeChange}
							/>
						</Row>
					</div>
					<div className="filter-item-slowlog">
						<Row align="middle">
							<Col offset={2} span={3}>
								<label>执行时长</label>
							</Col>
							<Col span={19}>
								<NumberRange
									unit="秒"
									numberRange={numberRange}
								/>
							</Col>
						</Row>
					</div>
					<div className="filter-item-search">
						<>
							<Button type="primary" onClick={handleClick}>
								搜索
							</Button>
							<Auth code="slowLogExport">
								<Button
									onClick={() => {
										WorkOrderFuc(
											slowLogDownload,
											lock,
											middlewareName,
											downloadSlowLogOperatorId,
											history,
											type,
											name,
											aliasName,
											clusterId,
											namespace
										);
									}}
									disabled={
										// !operateFlag ||
										controlledOperationDisabled(
											'maintenance',
											lock
										)
									}
									style={{
										marginRight: 12,
										padding: '0 9px'
									}}
									icon={<VerticalAlignBottomOutlined />}
								></Button>
							</Auth>
						</>
					</div>
				</div>
				{visible && (
					<ProTable dataSource={dataSource}>
						<ProTable.Column
							title="慢日志采集时间"
							dataIndex="timestampMysql"
							render={timeRender}
							width={160}
						/>
						<ProTable.Column
							title="SQL语句"
							dataIndex="query"
							width={450}
						/>
						<ProTable.Column
							title="客户端IP"
							dataIndex="clientip"
							width={120}
						/>
						<ProTable.Column
							title="执行时长（秒）"
							dataIndex="queryTime"
							width={120}
						/>
						<ProTable.Column
							title="锁定时长（秒）"
							dataIndex="lockTime"
							width={120}
						/>
						<ProTable.Column
							title="解析行数"
							dataIndex="rowsExamined"
							width={90}
						/>
						<ProTable.Column
							title="返回行数"
							dataIndex="rowsSent"
							width={90}
						/>
					</ProTable>
				)}
			</div>
		</div>
	);
}

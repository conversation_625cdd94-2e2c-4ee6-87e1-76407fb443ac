import React, { useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router';
import { connect } from 'react-redux';
import { StoreState } from '@/types';
import { Space, Tabs, notification, Button } from 'antd';
import {
	LoadingOutlined,
	LockOutlined,
	ReloadOutlined
} from '@ant-design/icons';
import { ProContent, ProHeader, ProPage } from '@/components/ProPage';
import { AccessIndexParams } from './detail.d';
import AccessBasicInfo from './AccessBasicInfo';
import AccessOperator from './AccessOperator';
import AccessAudit from './AccessAudit';
import AccessLog from './AccessLog';
import AccessFile from './AccessFile';
import { getMiddlewareDetail } from '@/services/middleware';
import storage from '@/utils/storage';
import { User } from '@/types';
import AccessMonitor from './AccessMonitor';
import { WorkOrderFuc } from '@/components/WorkOrderFuc';
import { maintenances } from '@/utils/const';
import { controlledOperationDisabled } from '@/utils/utils';
import AccessAccountManagement from './AccessAccountManagement';

function AccessIndex({ buttonList }: { buttonList: any }): JSX.Element {
	const params: AccessIndexParams = useParams();
	const history = useHistory();
	const {
		name,
		aliasName,
		middlewareName,
		currentTab,
		clusterId,
		namespace,
		type
	} = params;
	const [status, setStatus] = useState<string>('');
	const [activeKey, setActiveKey] = useState<string>(
		currentTab || 'basicInfo'
	);
	const [data, setData] = useState<any>();
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const [role, setRole] = useState<User>();
	const [operateFlag, setOperateFlag] = useState<boolean>(false);
	// * 接入的服务 日志详情 feature功能
	const [extraMiddlewareLoggingAPI] = useState<any>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'extraMiddlewareLogging')
			?.enabled ?? true
	);
	// * 接入的服务 数据监控 feature功能
	const [extraMiddlewareMonitoringAPI] = useState<any>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'extraMiddlewareMonitoring')
			?.enabled ?? true
	);
	// * 接入的服务 配置文件 feature功能
	const [extraMiddlewareConfigAPI] = useState<any>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'extraMiddlewareConfig')
			?.enabled ?? true
	);
	// * 运维面板 feature功能
	const [maintenanceDashboardAPI] = useState<any>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'maintenanceDashboard')
			?.enabled ?? true
	);
	// * 获取进入运维面板的operatorId
	const intoMaintenanceOperatorId =
		maintenances['Enter Operations Panel/Service Console'];
	// * 判断当前角色权限是否显示该菜单
	const showMenu = (code: string) =>
		buttonList?.find((item: any) => item.name === code);
	const subMenu: any = (code: string) =>
		buttonList?.find((item: any) => item.name === code)?.subMenu || [];

	useEffect(() => {
		getData();
	}, []);
	useEffect(() => {
		if (storage.getLocal('role')) {
			const roleT = JSON.parse(storage.getLocal('role'));
			setRole(roleT);
		}
	}, [organId, storage.getLocal('role')]);
	const statusRender = (service_status: string) => {
		switch (service_status) {
			case 'Running':
				return '运行正常';
			case 'Pending':
				return '运行异常';
			case 'Deleted':
				return '删除中';
			case 'Unhealthy':
				return '运行异常';
			default:
				return '运行异常';
		}
	};
	const getData = () => {
		const sendData = {
			clusterId,
			namespace,
			middlewareName,
			type: name,
			deployMod: 'server'
		};
		getMiddlewareDetail(sendData).then((res) => {
			if (res.success) {
				setData(res.data);
				storage.setSession('currentService', res.data);
				setStatus(res.data.status || 'Failed');
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const onChange = (key: string | number) => {
		setActiveKey(key as string);
		if (key !== 'file') {
			storage.setSession('configFileTab', 'fileDetail');
		}
		history.push(
			`/project/${type}/${name}/${aliasName}/server/${key}/${middlewareName}/${clusterId}/${namespace}`
		);
	};
	const childrenRender = (key: string) => {
		switch (key) {
			case 'basicInfo':
				return <AccessBasicInfo data={data} onRefresh={getData} />;
			case 'operator':
				return <AccessOperator data={data} />;
			case 'audit':
				return <AccessAudit />;
			case 'accountManagement':
				return (
					<AccessAccountManagement
						lock={data?.lock}
						devAccount={data?.devAccount}
						onRefresh={getData}
					/>
				);
			case 'log':
				return <AccessLog detail={data} />;
			case 'monitor':
				return <AccessMonitor detail={data} />;
			case 'file':
				return (
					<AccessFile detail={data} subMenu={subMenu('configFile')} />
				);
			default:
				return;
		}
	};
	const toOperatorPanel = () => {
		window.open(
			`#/operationalPanel/sqlConsole/${organId}/${projectId}/${clusterId}/${namespace}/${name}/${middlewareName}/${data?.version}/${data?.mode}`,
			'_blank'
		);
	};
	return (
		<ProPage>
			<ProHeader
				title={
					status ? (
						<Space>
							<span>
								{aliasName}:{middlewareName}
							</span>
							<span>
								{data?.lock === 'locked' && <LockOutlined />}
							</span>
							<span>({statusRender(status)})</span>
						</Space>
					) : (
						<Space>
							<span>
								{aliasName}:{middlewareName}
							</span>
							( <LoadingOutlined /> )
						</Space>
					)
				}
				onBack={() =>
					history.push(
						`/project/${params.type}/${params.name}/${params.aliasName}`
					)
				}
				extra={
					<Space>
						{maintenanceDashboardAPI &&
							// operateFlag &&
							data &&
							status !== 'Creating' &&
							(name === 'mysql' ||
								name === 'redis' ||
								name === 'postgresql') && (
								<Button
									type="primary"
									disabled={controlledOperationDisabled(
										'expert',
										data?.lock
									)}
									onClick={() => {
										WorkOrderFuc(
											toOperatorPanel,
											data?.lock,
											middlewareName,
											intoMaintenanceOperatorId,
											history,
											type,
											name,
											aliasName,
											clusterId,
											namespace
										);
									}}
								>
									运维面板(beta)
								</Button>
							)}
						<Button
							onClick={() => getData()}
							id="detailRefresh"
							icon={<ReloadOutlined id="detailRefresh" />}
						/>
					</Space>
				}
			/>
			<ProContent>
				<Tabs
					activeKey={activeKey}
					onChange={onChange}
					destroyInactiveTabPane={true}
				>
					{showMenu('baseInfo') && (
						<Tabs.TabPane tab="基本信息" key="basicInfo">
							{childrenRender('basicInfo')}
						</Tabs.TabPane>
					)}
					{showMenu('opsPower') && (
						<Tabs.TabPane tab="运维能力" key="operator">
							{childrenRender('operator')}
						</Tabs.TabPane>
					)}
					{showMenu('extraOpsAccountManagement') && (
						<Tabs.TabPane tab="账户管理" key="accountManagement">
							{childrenRender('accountManagement')}
						</Tabs.TabPane>
					)}
					{showMenu('operationsAudit') && (
						<Tabs.TabPane tab="操作审计" key="audit">
							{childrenRender('audit')}
						</Tabs.TabPane>
					)}
					{showMenu('extraLogDetail') && (
						<Tabs.TabPane tab="日志详情" key="log">
							{childrenRender('log')}
						</Tabs.TabPane>
					)}
					{showMenu('dataMonitor') && (
						<Tabs.TabPane tab="数据监控" key="monitor">
							{childrenRender('monitor')}
						</Tabs.TabPane>
					)}
					{showMenu('configFile') && (
						<Tabs.TabPane tab="配置文件" key="file">
							{childrenRender('file')}
						</Tabs.TabPane>
					)}
				</Tabs>
			</ProContent>
		</ProPage>
	);
}

export default connect((state: StoreState) => ({
	buttonList: state.auth.buttonList
}))(AccessIndex);

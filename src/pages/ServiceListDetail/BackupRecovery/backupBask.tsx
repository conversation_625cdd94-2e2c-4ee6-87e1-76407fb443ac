import React, { useState, useEffect } from 'react';
import { Button, Modal, notification, Tooltip } from 'antd';
import { RightOutlined, DownOutlined } from '@ant-design/icons';
import { useHistory, useParams } from 'react-router';
import Actions from '@/components/Actions';
import ProTable from '@/components/ProTable';
import { getBackupTasks, deleteBackupTasks } from '@/services/backup';
import {
	statusBackupRender,
	nullRender,
	controlledOperationDisabled,
	statusRecordRender
} from '@/utils/utils';
import { backupTaskStatus, maintenances } from '@/utils/const';
import storage from '@/utils/storage';
import { BackupRecordItem } from './backup';
import { getComponents } from '@/services/common';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import useRefresh from '@/utils/useRefresh';
const LinkButton = Actions.LinkButton;
const { confirm } = Modal;
const List = ({ lock }: { lock: string }): JSX.Element => {
	const params: any = useParams();
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const [backups, setBackups] = useState<BackupRecordItem[]>([]);
	const [loading, setLoading] = useState<boolean>(false);
	const [backupComponent, setBackupComponent] = useState<boolean>(false);
	const history = useHistory();
	const [keyword, setKeyword] = useState('');
	const createBackOperatorId = maintenances['Create Backup'];
	const deleteBackOperatorId = maintenances['Delete Backup Task'];
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		getComponents({ clusterId: params.clusterId }).then((res) => {
			if (res.success) {
				const backupTemp = res.data.find(
					(item: any) =>
						item.component === 'middlewarebackup-controller'
				).status;
				setBackupComponent(backupTemp === 3 ? true : false);
			}
		});
	}, [refreshKey]);
	useEffect(() => {
		if (projectId && organId) {
			getData(keyword);
		}
	}, [projectId, organId, refreshKey]);

	const getData = (keyword: string) => {
		const sendData = {
			keyword,
			projectId,
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params?.middlewareName || '',
			type: params?.name || ''
		};
		setLoading(true);
		getBackupTasks(sendData).then((res) => {
			setLoading(false);
			if (res.success) {
				if (res.data.length > 0) {
					// * 源服务已删除的服务置灰并排列到后面
					const data = res.data.map((item: any) => {
						if (item.status === 'Deleted' || !item.status) {
							item.index = 1;
							return item;
						} else {
							item.index = 0;
							return item;
						}
					});
					setBackups(
						data.sort(
							(a: BackupRecordItem, b: BackupRecordItem) =>
								a.index - b.index
						)
					);
				} else {
					setBackups(res.data);
				}
			} else {
				setBackups([]);
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const deleteBackup = (record: BackupRecordItem) => {
		confirm({
			title: '操作确认',
			content:
				record.backupMode !== 'single' ? (
					`${
						record.phrase === 'DeleteFailed'
							? '强制删除周期备份任务将删除资源并无法恢复，若存在异常，备份数据将无法删除，请手动操作'
							: '删除周期备份任务，将清除此中间件所有备份数据且无法恢复，请确认执行？'
					}`
				) : record.phrase === 'DeleteFailed' ? (
					'强制删除单次备份任务将删除资源并无法恢复，若存在异常，备份数据将无法删除，请手动操作'
				) : (
					<>
						<p>删除单次备份任务，将清除对应备份数据且无法恢复</p>
						<p>请确认执行？</p>
					</>
				),
			onOk: async () => {
				const sendData = {
					clusterId: params.clusterId,
					namespace: record.namespace,
					type: record.sourceType,
					cron: record.cron || '',
					backupNameList: record.middlewareBackupRecords
						? record.middlewareBackupRecords.map(
								(item: BackupRecordItem) => item.backupName
						  )
						: [record.backupName],
					backupId: record.backupId,
					backupMode: record.backupMode,
					addressName: record.addressName,
					schedule: record.schedule,
					backupFileName: record.backupFileName || '',
					forceDelete: record.phrase === 'DeleteFailed' ? true : false
				};
				await ExecuteOrderFuc();
				return deleteBackupTasks(sendData)
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description:
									'操作成功！删除备份数据需要一定时间，请留意刷新数据！'
							});
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					})
					.finally(() => {
						getData('');
					});
			}
		});
	};
	const actionRender = (
		value: any,
		record: BackupRecordItem,
		index: number
	) => {
		return (
			<Actions>
				<LinkButton
					disabled={
						!backupComponent ||
						record.phrase === 'Deleting' ||
						controlledOperationDisabled('maintenance', lock)
					}
					title={
						!backupComponent
							? '备份控制器组件未安装，请前往平台组件页面安装相应组件'
							: ''
					}
					onClick={() => {
						WorkOrderFuc(
							() => {
								deleteBackup(record);
							},
							lock,
							params.middlewareName,
							deleteBackOperatorId,
							history,
							params.type,
							params.name,
							params.aliasName,
							params.clusterId,
							params.namespace
						);
					}}
				>
					{record.phrase === 'DeleteFailed' ? '强制删除' : '删除'}
				</LinkButton>
			</Actions>
		);
	};

	const Operation = {
		primary: (
			<Button
				onClick={() => {
					WorkOrderFuc(
						() => {
							history.push(
								`/project/${params.type}/${params.name}/${params.aliasName}/container/${params.currentTab}/addBackupTask/${params.middlewareName}/${params.chartVersion}/${params.clusterId}/${params.namespace}`
							);
						},
						lock,
						params.middlewareName,
						createBackOperatorId,
						history,
						params.type,
						params.name,
						params.aliasName,
						params.clusterId,
						params.namespace
					);
				}}
				type="primary"
				disabled={
					!backupComponent ||
					controlledOperationDisabled('maintenance', lock)
				}
				title={
					!backupComponent
						? '备份控制器组件未安装，请前往平台组件页面安装相应组件'
						: ''
				}
			>
				新增
			</Button>
		)
	};

	const taskNameRender = (value: string, record: any, index: number) => {
		return (
			<span
				className={record.phrase === 'Deleting' ? '' : 'name-link'}
				onClick={() => {
					if (record.phrase === 'Deleting') return;
					history.push(
						`/project/${params.type}/${params.name}/${
							params.aliasName
						}/container/${params.currentTab}/backupTaskDetail/${
							params.middlewareName
						}/${params.chartVersion}/${params.namespace}/${
							params.clusterId
						}/${
							record.backupName ||
							record.middlewareBackupRecords[0].backupName
						}/${record.backupId}/${record.backupMode}/${lock}`
					);
					storage.setLocal('backupDetail', {
						...record.middlewareBackupRecords[0],
						...record
					});
				}}
			>
				{value}
			</span>
		);
	};

	const positionRender = (value: string, record: any, index: number) => {
		return record?.backupAddresses.length > 1 ? (
			<Tooltip
				title={
					<>
						<div>{record?.backupAddresses[0]}</div>
						<div>{record?.backupAddresses[1]}</div>
					</>
				}
			>
				{record?.backupAddresses[0].split('(')[0] || '/'}
			</Tooltip>
		) : (
			<Tooltip title={record?.backupAddresses[0]}>
				{record?.backupAddresses[0].split('(')[0] || '/'}
			</Tooltip>
		);
	};

	return (
		<div>
			<ProTable
				dataSource={backups}
				rowKey="backupId"
				loading={loading}
				operation={Operation}
				search={{
					placeholder: '请输入关键字搜索',
					onSearch: (value: string) => {
						getData(value);
						setKeyword(value);
					},
					style: { width: '360px' }
				}}
				expandIcon={({ expanded, onExpand, record }) => {
					return record.middlewareBackupRecords.length > 1 ? (
						expanded ? (
							<DownOutlined
								onClick={(e) => onExpand(record, e)}
							/>
						) : (
							<RightOutlined
								onClick={(e) => onExpand(record, e)}
							/>
						)
					) : (
						''
					);
				}}
			>
				<ProTable.Column
					title="备份任务名称"
					dataIndex="taskName"
					render={taskNameRender}
					width={160}
				/>
				<ProTable.Column
					title="状态"
					dataIndex="phrase"
					render={statusBackupRender}
					width={120}
					filterMultiple={false}
					filters={backupTaskStatus}
					onFilter={(value, record: any) =>
						value !== 'Unknown'
							? record.phrase === value
							: record.phrase !== 'Running' &&
							  record.phrase !== 'Failed' &&
							  record.phrase !== 'Success' &&
							  record.phrase !== 'Deleting' &&
							  record.phrase !== 'DeleteFailed' &&
							  record.phrase !== 'Waiting' &&
							  record.phrase !== 'RecycleFailed'
					}
				/>
				<ProTable.Column
					title="备份源名称"
					dataIndex="sourceName"
					width={160}
				/>
				<ProTable.Column
					title="备份方式"
					dataIndex="schedule"
					render={(value, record: any) =>
						record?.backupMode !== 'period'
							? '单次备份'
							: '周期备份'
					}
					width={120}
					filterMultiple={false}
					filters={[
						{ text: '周期备份', value: 'period' },
						{ text: '单次备份', value: 'single' }
					]}
					onFilter={(value, record: any) =>
						record.backupMode === value
					}
				/>
				<ProTable.Column
					title="备份位置"
					dataIndex="position"
					render={positionRender}
				/>
				<ProTable.Column
					title="最近一次备份状态"
					dataIndex="recentBackupStatus"
					render={(value, record: any, index) =>
						record?.backupMode !== 'period'
							? '/'
							: statusRecordRender(value, record, index)
					}
				/>
				<ProTable.Column
					title="最近一次备份时间"
					dataIndex="recentBackupTime"
					render={nullRender}
				/>
				<ProTable.Column
					title="操作"
					render={actionRender}
					width={120}
				/>
			</ProTable>
		</div>
	);
};
export default List;

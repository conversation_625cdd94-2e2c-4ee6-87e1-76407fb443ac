import React from 'react';
import DefaultPicture from '@/components/DefaultPicture';
import BackupTask from './backupBask';
import { BackupRecoveryProps } from '../detail';

export default function BackupRecovery(
	props: BackupRecoveryProps
): JSX.Element {
	const { customMid, capabilities, lock } = props;
	if (customMid && !capabilities.includes('backup')) {
		return <DefaultPicture />;
	}
	return <BackupTask lock={lock} />;
}

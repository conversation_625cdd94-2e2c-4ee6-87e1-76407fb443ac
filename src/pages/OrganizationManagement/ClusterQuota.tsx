import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON>nt, ProHeader, ProPage } from '@/components/ProPage';
import { getClusterQuota, getClusters } from '@/services/common';
import {
	notification,
	Form,
	Input,
	Select,
	Divider,
	Space,
	Button,
	Tooltip,
	InputNumber
} from 'antd';
import Decimal from 'decimal.js';
import { useHistory } from 'react-router';
import { getCpuMemory, getOrganizationDetail } from '@/services/organization';
import { ClusterQuotaProps, OrganizationItem, quotaItem } from './organization';
import { DetailParams } from '../OrganizationDetail/organization.detail';
import { connect } from 'react-redux';
import { useParams } from 'react-router';
import { formItemLayout618 } from '@/utils/const';
import { IconFont } from '@/components/IconFont';
import { QuestionCircleOutlined } from '@ant-design/icons';
import {
	setOrgCluster,
	setProCluster,
	setEditCluster
} from '@/redux/organization/organization';
import { formatNumber } from '@/utils/utils';
import { getProjectCpuAndMemory } from '@/services/project';
import { StoreState } from '@/types';
import MidSlider from '@/components/MidSlider';
import ClusterNumberDisplay from '@/components/ClusterNumberDisplay';
import './index.less';

function ClusterQuota(props: ClusterQuotaProps): JSX.Element {
	const {
		setOrgCluster,
		setProCluster,
		editCluster,
		propsClusters,
		propsProjectClusters
	} = props;
	const params: DetailParams = useParams();
	const history = useHistory();
	const [basicDetail, setBasicDetail] = useState<OrganizationItem>();
	const [clusters, setClusters] = useState<any[]>([]);
	const [currentCluster, setCurrentCluster] = useState<any>();
	const [editResCluster, setEditResCluster] = useState<any>();
	const [quotaCpu, setQuotaCpu] = useState<quotaItem>({
		allocatable: 0,
		request: 0,
		total: 0,
		usable: 0,
		usage: 0,
		used: 0,
		oused: 0
	});
	const [quotaMemory, setQuotaMemory] = useState<quotaItem>({
		allocatable: 0,
		request: 0,
		total: 0,
		usable: 0,
		usage: 0,
		used: 0,
		oused: 0
	});
	const [loading, setLoading] = useState<{ key: string; loading: boolean }[]>(
		[
			{ key: 'list', loading: false },
			{ key: 'detail', loading: false }
		]
	);
	useEffect(() => {
		console.log(quotaCpu, quotaMemory);
	}, [quotaCpu, quotaMemory]);
	const changeLoading = (key: string, lt: boolean) => {
		const list = [...loading];
		const tt = list.map((item) => {
			if (item.key === key) {
				item.loading = lt;
			}
			return item;
		});
		setLoading(tt);
	};
	const judgeBth = () => {
		if (loading.every((item) => item.loading === false)) {
			return false;
		} else {
			return true;
		}
	};
	useEffect(() => {
		if (params.clusterId && !params.projectId) {
			// * 组织修改集群配额时，设置当前集群
			changeLoading('detail', true);
			getCpuMemory({ organId: params.organId, detail: true })
				.then((res) => {
					if (res.success) {
						const cc = res.data.find(
							(item) => item.clusterId === params.clusterId
						);
						const cct = clusters.find(
							(item) => item.id === params.clusterId
						);
						setEditResCluster(cc);
						setCurrentCluster(cct);
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				})
				.finally(() => {
					changeLoading('detail', false);
				});
		} else if (params.clusterId && params.projectId) {
			// * 项目修改集群配额，设置当前集群
			changeLoading('detail', true);
			getProjectCpuAndMemory({
				organId: params.organId,
				projectId: params.projectId,
				detail: true
			})
				.then((res) => {
					if (res.success) {
						const cc = res.data.find(
							(item) => item.clusterId === params.clusterId
						);
						setEditResCluster(cc);
						const cct = clusters.find(
							(item) => item.clusterId === params.clusterId
						);
						setCurrentCluster(cct);
					}
				})
				.finally(() => {
					changeLoading('detail', false);
				});
		} else {
			// * 组织添加集群，项目添加集群，设置当前集群
			if (JSON.stringify(editCluster) !== '{}') {
				// * 在新增页面点击修改时
				setCurrentCluster(editCluster);
			} else {
				// * 直接点击添加
				setCurrentCluster(clusters[0]);
			}
		}
	}, [clusters]);
	// * 获取组织详情
	useEffect(() => {
		getOrganizationDetail({ organId: params.organId }).then((res) => {
			if (res.success) {
				setBasicDetail(res.data);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	}, []);
	// * 获取集群接口
	useEffect(() => {
		if (params.projectId) {
			// * 项目分配集群资源的时候，调用组织已分配的cpu和memory接口
			changeLoading('list', true);
			getCpuMemory({ organId: params.organId, detail: true })
				.then((res) => {
					if (res.success) {
						if (res.data.length > 0) {
							const propsProjectClusterIds =
								propsProjectClusters.map(
									(item) => item.clusterId
								);
							const list = res.data.filter(
								(item) =>
									!propsProjectClusterIds.includes(
										item.clusterId
									)
							);
							setClusters(list);
						} else {
							setClusters([]);
							setCurrentCluster(undefined);
						}
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				})
				.finally(() => {
					changeLoading('list', false);
				});
		} else {
			// * 组织分配集群资源的时候，调用全部的集群接口（集群管理列表）
			changeLoading('list', true);
			getClusters({ detail: true, key: '' })
				.then((res) => {
					if (res.success) {
						if (res.data.length > 0) {
							const propsClusterIds = propsClusters.map(
								(item) => item.clusterId
							);
							const list = res.data.filter(
								(item: any) =>
									!propsClusterIds.includes(item.id)
							);
							setClusters(list);
						} else {
							setClusters([]);
							setCurrentCluster(undefined);
						}
					} else {
						notification.error({
							message: '失败',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				})
				.finally(() => {
					changeLoading('list', false);
				});
		}
	}, []);
	useEffect(() => {
		// * 选中集群后，设置集群的quota（cpu/memory）
		if (!params.projectId) {
			// * 组织下使用
			if (currentCluster) {
				getClusterQuotaById(currentCluster.id);
			}
		} else {
			// * 项目下使用
			if (currentCluster) {
				if (editResCluster) {
					// * 编辑集群时
					if (JSON.stringify(editCluster) !== '{}') {
						setQuotaCpu({
							...currentCluster.cpu,
							oused: currentCluster.cpu.used,
							allocatable: editCluster.cpu.request,
							used: editResCluster.cpu.used
						});
						setQuotaMemory({
							...currentCluster.memory,
							oused: currentCluster.memory.used,
							allocatable: editCluster.memory.request,
							used: editResCluster.memory.used
						});
					} else {
						setQuotaCpu({
							...currentCluster.cpu,
							oused: currentCluster.cpu.used,
							allocatable: editResCluster.cpu.request,
							used: editResCluster.cpu.used
						});
						setQuotaMemory({
							...currentCluster.memory,
							oused: currentCluster.memory.used,
							allocatable: editResCluster.memory.request,
							used: editResCluster.memory.used
						});
					}
				} else {
					// * 添加集群时
					if (JSON.stringify(editCluster) !== '{}') {
						setQuotaCpu({
							...currentCluster.cpu,
							oused: currentCluster.cpu.used,
							used: 0,
							allocatable: editCluster.cpu.request || 0
						});
						setQuotaMemory({
							...currentCluster.memory,
							oused: currentCluster.memory.used,
							used: 0,
							allocatable: editCluster.memory.request || 0
						});
					} else {
						setQuotaCpu({
							...currentCluster.cpu,
							oused: currentCluster.cpu.used,
							used: 0,
							allocatable: 0
						});
						setQuotaMemory({
							...currentCluster.memory,
							oused: currentCluster.memory.used,
							used: 0,
							allocatable: 0
						});
					}
				}
			}
		}
	}, [currentCluster, editResCluster]);
	const getClusterQuotaById = (clusterId: string) => {
		getClusterQuota({ clusterId, detail: true }).then((res) => {
			if (res.success) {
				if (JSON.stringify(editCluster) !== '{}') {
					// * 已存在使用的情况下编辑
					setQuotaCpu({
						...res.data.cpu,
						allocatable: editCluster?.cpu?.request || 0,
						oused: res.data.cpu.used, // * 集群已使用（其他项目下）
						used: editResCluster?.cpu?.used || 0
					});
					setQuotaMemory({
						...res.data.memory,
						allocatable: editCluster?.memory?.request || 0,
						oused: res.data.memory.used, // * 集群已使用（其他项目下）
						used: editResCluster?.memory.used || 0
					});
				} else {
					// * 新增编辑
					setQuotaCpu({
						...res.data.cpu,
						allocatable: editResCluster?.cpu?.request || 0,
						oused: res.data.cpu.used, // * 集群已使用（其他项目下）
						used: editResCluster?.cpu?.used || 0
					});
					setQuotaMemory({
						...res.data.memory,
						allocatable: editResCluster?.memory?.request || 0,
						oused: res.data.memory.used, // * 集群已使用（其他项目下）
						used: editResCluster?.memory?.used || 0
					});
				}
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	const handleChange = (value: number | null, type: string) => {
		if (getCpuMemoryMax(type) < 0) {
			if (type === 'cpu') {
				setQuotaCpu({
					...quotaCpu,
					allocatable:
						(value as number) <= getCpuMemoryDilatation(type)
							? value
							: getCpuMemoryDilatation(type) || 0
				});
			} else {
				setQuotaMemory({
					...quotaMemory,
					allocatable:
						(value as number) <= getCpuMemoryDilatation(type)
							? value
							: getCpuMemoryDilatation(type) || 0
				});
			}
			return;
		}
		if (type === 'cpu') {
			setQuotaCpu({
				...quotaCpu,
				allocatable: value || 0
			});
		} else {
			setQuotaMemory({
				...quotaMemory,
				allocatable: value || 0
			});
		}
	};
	const onChange = (value: string) => {
		if (params.projectId) {
			const ct = clusters.find((item) => item.clusterId === value);
			setCurrentCluster(ct);
		} else {
			const ct = clusters.find((item) => item.id === value);
			setCurrentCluster(ct);
		}
	};
	const handleSubmit = () => {
		if (getCpuMemoryMax('cpu') > 0) {
			if (!quotaCpu.allocatable) {
				notification.error({
					message: '错误',
					description: '请分配CPU和内存!'
				});
				return;
			}
			if (!quotaCpu.allocatable || quotaCpu.allocatable < 0) {
				notification.error({
					message: '错误',
					description: '当前CPU配额分配错误，请重新分配！'
				});
				return;
			}
		}
		if (getCpuMemoryMax('memory') > 0) {
			if (!quotaMemory.allocatable) {
				notification.error({
					message: '错误',
					description: '请分配CPU和内存!'
				});
				return;
			}
			if (!quotaMemory.allocatable || quotaMemory.allocatable < 0) {
				notification.error({
					message: '错误',
					description: '当前内存配额分配错误，请重新分配！'
				});
				return;
			}
		}
		if (getCpuMemoryReduce('cpu') < 0) {
			notification.error({
				message: '错误',
				description: '当前CPU配额分配错误，请重新分配！'
			});
			return;
		}
		if (getCpuMemoryReduce('memory') < 0) {
			notification.error({
				message: '错误',
				description: '当前内存配额分配错误，请重新分配！'
			});
			return;
		}
		if (
			!quotaCpu.allocatable ||
			quotaCpu.allocatable === 0 ||
			!quotaMemory.allocatable ||
			quotaMemory.allocatable === 0
		) {
			notification.error({
				message: '错误',
				description: '请分配CPU和内存！'
			});
			return;
		}
		if (params.projectId) {
			setProCluster({
				clusterId: currentCluster?.clusterId,
				clusterNickName: currentCluster?.clusterNickName,
				cpu: { ...quotaCpu, request: quotaCpu.allocatable },
				memory: { ...quotaMemory, request: quotaMemory.allocatable }
			});
			history.push(
				`/organization/project/add/${params.organId}/${params.projectId}/${params.projectName}`
			);
		} else {
			setOrgCluster({
				clusterId: currentCluster?.id,
				clusterNickName: currentCluster?.nickname,
				cpu: { ...quotaCpu, request: quotaCpu.allocatable },
				memory: { ...quotaMemory, request: quotaMemory.allocatable }
			});
			history.push(
				`/platform/organizationManagement/add/${params.organId}`
			);
		}
	};
	// * cpu/memory 进度条的最大值
	const getCpuMemoryMax = (type: string) => {
		if (type === 'cpu') {
			const rC = new Decimal(quotaCpu.request);
			const oC = new Decimal(quotaCpu.oused || 0);
			const edC = new Decimal(editResCluster?.cpu?.request || 0);
			const result = rC.minus(oC).plus(edC);
			return result.toNumber();
		} else {
			const rM = new Decimal(quotaMemory.request);
			const oM = new Decimal(quotaMemory.oused || 0);
			const edM = new Decimal(editResCluster?.memory?.request || 0);
			const result = rM.minus(oM).plus(edM);
			return result.toNumber();
		}
	};
	// * cpu/memory 当前可扩容量
	const getCpuMemoryDilatation = (type: string) => {
		if (type === 'cpu') {
			const rC = new Decimal(quotaCpu.request);
			const oC = new Decimal(quotaCpu.oused || 0);
			const edC = new Decimal(editResCluster?.cpu?.request || 0);
			const aC = new Decimal(quotaCpu.allocatable || 0);
			const result = rC.minus(oC).plus(edC).minus(aC);
			return result.toNumber();
		} else {
			const rM = new Decimal(quotaMemory.request);
			const oM = new Decimal(quotaMemory.oused || 0);
			const edM = new Decimal(editResCluster?.memory?.request || 0);
			const aM = new Decimal(quotaMemory.allocatable || 0);
			const result = rM.minus(oM).plus(edM).minus(aM);
			return result.toNumber();
		}
	};
	// * cpu/memory 当前可缩容量
	const getCpuMemoryReduce = (type: string) => {
		if (type === 'cpu') {
			const aC = new Decimal(quotaCpu.allocatable || 0);
			const uC = new Decimal(quotaCpu.used);
			return aC.minus(uC).toNumber();
		} else {
			const aM = new Decimal(quotaMemory.allocatable || 0);
			const uM = new Decimal(quotaMemory.used);
			return aM.minus(uM).toNumber();
		}
	};
	return (
		<ProPage>
			<ProHeader
				title="资源配额分配"
				onBack={() => {
					if (params.projectId) {
						if (params.clusterId) {
							if (JSON.stringify(editCluster) !== '{}') {
								setProCluster({
									clusterId: currentCluster?.clusterId,
									clusterNickName:
										currentCluster?.clusterNickName,
									cpu: {
										...quotaCpu,
										allocatable: editCluster.cpu.request,
										request: editCluster.cpu.request
									},
									memory: {
										...quotaMemory,
										allocatable: editCluster.memory.request,
										request: editCluster.memory.request
									}
								});
							} else {
								setProCluster({
									clusterId: currentCluster?.clusterId,
									clusterNickName:
										currentCluster?.clusterNickName,
									cpu: {
										...quotaCpu,
										request: quotaCpu.used
									},
									memory: {
										...quotaMemory,
										request: quotaMemory.used
									}
								});
							}
						}
						history.push(
							`/organization/project/add/${params.organId}/${params.projectId}/${params.projectName}`
						);
					} else {
						if (params.clusterId) {
							if (JSON.stringify(editCluster) !== '{}') {
								setOrgCluster({
									clusterId: currentCluster?.id,
									clusterNickName: currentCluster?.nickname,
									cpu: {
										...quotaCpu,
										allocatable: editCluster.cpu.request,
										request: editCluster.cpu.request
									},
									memory: {
										...quotaMemory,
										allocatable: editCluster.memory.request,
										request: editCluster.memory.request
									}
								});
							} else {
								setOrgCluster({
									clusterId: currentCluster?.id,
									clusterNickName: currentCluster?.nickname,
									cpu: {
										...quotaCpu,
										request: quotaCpu.used
									},
									memory: {
										...quotaMemory,
										request: quotaMemory.used
									}
								});
							}
						}
						history.push(
							`/platform/organizationManagement/add/${params.organId}`
						);
					}
				}}
			/>
			<ProContent>
				{params.projectId && (
					<>
						<h2>选择项目</h2>
						<Form.Item
							label="项目名称"
							required
							{...formItemLayout618}
							labelAlign="left"
							style={{ width: '585px' }}
						>
							<Input disabled={true} value={params.projectName} />
						</Form.Item>
					</>
				)}
				{!params.projectId && (
					<>
						<h2>选择组织</h2>
						<Form.Item
							label="组织名称"
							required
							{...formItemLayout618}
							labelAlign="left"
							style={{ width: '585px' }}
						>
							<Input disabled={true} value={basicDetail?.name} />
						</Form.Item>
					</>
				)}
				<h2>选择集群</h2>
				<Form.Item
					label="集群名称"
					labelAlign="left"
					{...formItemLayout618}
					style={{ width: '585px' }}
					required
					rules={[{ required: true, message: '请选择集群' }]}
				>
					<Select
						value={currentCluster?.id || currentCluster?.clusterId}
						onChange={onChange}
						disabled={!!params.clusterId}
					>
						{clusters.map((item) => {
							return (
								<Select.Option
									key={item?.id || item?.clusterId}
									value={item?.id || item?.clusterId}
								>
									{item?.nickname || item?.clusterNickName}
								</Select.Option>
							);
						})}
					</Select>
				</Form.Item>
				<h2>分配配额</h2>
				<div className="cluster-quota-display-content">
					<div className="cluster-quota-cpu-display">
						<div className="cluster-quota-cpu-title">
							<IconFont
								type="icon-quota"
								style={{ fontSize: 46 }}
							/>
							<h3>CPU(单位：核)</h3>
						</div>
						<div className="cluster-quota-cpu-content">
							<div className="cluster-quota-cpu-item">
								<span>
									当前分配总量
									<Tooltip
										title={
											params.projectId
												? '当前集群下组织分配到项目的特定资源对象（包含CPU、内存、存储、备份服务器）总配额'
												: '当前已选择的集群分配到组织的特定资源对象（包含CPU、内存、存储、备份服务器）总配额'
										}
									>
										<QuestionCircleOutlined
											style={{ marginLeft: 4 }}
										/>
									</Tooltip>{' '}
									：
								</span>
								<ClusterNumberDisplay
									value={quotaCpu?.allocatable || 0}
								/>
							</div>
							<div className="cluster-quota-cpu-item">
								<span>
									当前可缩容量
									<Tooltip
										title={`当前${
											params.projectId ? '项目' : '组织'
										}资源可缩容量=当前分配总量-当前使用总量`}
									>
										<QuestionCircleOutlined
											style={{ marginLeft: 4 }}
										/>
									</Tooltip>{' '}
									：
								</span>
								<ClusterNumberDisplay
									value={new Decimal(
										quotaCpu.allocatable || 0
									)
										.minus(new Decimal(quotaCpu.used))
										.toNumber()}
								/>
							</div>
							<div className="cluster-quota-cpu-item">
								<span>
									当前使用总量
									<Tooltip
										title={`当前${
											params.projectId ? '项目' : '组织'
										}已分配得到的总量中，有部分资源已被分配给该${
											params.projectId ? '项目' : '组织'
										}下的${
											params.projectId
												? '命名空间'
												: '项目'
										}使用`}
									>
										<QuestionCircleOutlined
											style={{ marginLeft: 4 }}
										/>
									</Tooltip>{' '}
									：
								</span>
								<ClusterNumberDisplay
									value={quotaCpu?.used || 0}
								/>
							</div>
							<div className="cluster-quota-cpu-item">
								<span>
									当前可扩容量
									<Tooltip
										title={
											params.projectId
												? '当前项目资源可扩容量=该集群下组织获得的该资源对象（包含CPU、内存、存储、备份服务器）总配额 - 已分配给其他项目的资源配额-当前分配总量'
												: '当前组织资源可扩容量=集群下该资源对象（包含CPU、内存、存储、备份服务器）总配额 - 已分配给其他组织的资源配额-当前分配总量'
										}
									>
										<QuestionCircleOutlined
											style={{ marginLeft: 4 }}
										/>
									</Tooltip>{' '}
									：
								</span>
								<ClusterNumberDisplay
									value={getCpuMemoryDilatation('cpu')}
								/>
							</div>
						</div>
					</div>
					<div className="cluster-quota-memory-display">
						<div className="cluster-quota-memory-title">
							<IconFont
								type="icon-quota"
								style={{ fontSize: 46 }}
							/>
							<h3>内存(单位：GB)</h3>
						</div>
						<div className="cluster-quota-memory-content">
							<div className="cluster-quota-memory-item">
								<span>
									当前分配总量
									<Tooltip
										title={
											params.projectId
												? '当前集群下组织分配到项目的特定资源对象（包含CPU、内存、存储、备份服务器）总配额'
												: '当前已选择的集群分配到组织的特定资源对象（包含CPU、内存、存储、备份服务器）总配额'
										}
									>
										<QuestionCircleOutlined
											style={{ marginLeft: 4 }}
										/>
									</Tooltip>{' '}
									：
								</span>
								<ClusterNumberDisplay
									value={quotaMemory?.allocatable || 0}
								/>
							</div>
							<div className="cluster-quota-memory-item">
								<span>
									当前可缩容量
									<Tooltip
										title={`当前${
											params.projectId ? '项目' : '组织'
										}资源可缩容量=当前分配总量-当前使用总量`}
									>
										<QuestionCircleOutlined
											style={{ marginLeft: 4 }}
										/>
									</Tooltip>{' '}
									：
								</span>
								<ClusterNumberDisplay
									value={new Decimal(
										quotaMemory.allocatable || 0
									)
										.minus(new Decimal(quotaMemory.used))
										.toNumber()}
								/>
							</div>
							<div className="cluster-quota-memory-item">
								<span>
									当前使用总量
									<Tooltip
										title={`当前${
											params.projectId ? '项目' : '组织'
										}已分配得到的总量中，有部分资源已被分配给该${
											params.projectId ? '项目' : '组织'
										}下的${
											params.projectId
												? '命名空间'
												: '项目'
										}使用`}
									>
										<QuestionCircleOutlined
											style={{ marginLeft: 4 }}
										/>
									</Tooltip>{' '}
									：
								</span>
								<ClusterNumberDisplay
									value={quotaMemory?.used || 0}
								/>
							</div>
							<div className="cluster-quota-memory-item">
								<span>
									当前可扩容量
									<Tooltip
										title={
											params.projectId
												? '当前项目资源可扩容量=该集群下组织获得的该资源对象（包含CPU、内存、存储、备份服务器）总配额 - 已分配给其他项目的资源配额-当前分配总量'
												: '当前组织资源可扩容量=集群下该资源对象（包含CPU、内存、存储、备份服务器）总配额 - 已分配给其他组织的资源配额-当前分配总量'
										}
									>
										<QuestionCircleOutlined
											style={{ marginLeft: 4 }}
										/>
									</Tooltip>{' '}
									：
								</span>
								<ClusterNumberDisplay
									value={getCpuMemoryDilatation('memory')}
								/>
							</div>
						</div>
					</div>
				</div>
				<div className="cluster-slider">
					<div className="cluster-slider-action">
						<div className="cluster-slider-title">
							<div className="cluster-namespace-form-label">
								CPU（单位：Core）
							</div>
						</div>
						<div className="cluster-slider-input-number-content">
							<InputNumber
								style={{ width: '120px' }}
								addonAfter="Core"
								value={quotaCpu?.allocatable || 0}
								onChange={(value: number | null) =>
									handleChange(value, 'cpu')
								}
								step={0.1}
								precision={1}
								min={0}
								max={getCpuMemoryMax('cpu')}
							/>
						</div>
					</div>
					<MidSlider
						min={0}
						max={getCpuMemoryMax('cpu')}
						value={quotaCpu.allocatable}
						setValue={(value) => {
							setQuotaCpu({
								...quotaCpu,
								allocatable: value
							});
						}}
						used={quotaCpu.used}
						step={0.1}
						tooltip={`已使用${formatNumber(quotaCpu.used, 0)}核`}
						type="cpu"
					/>
				</div>
				<div className="cluster-slider">
					<div className="cluster-slider-action">
						<div className="cluster-slider-title">
							<div className="cluster-namespace-form-label">
								内存（单位：GB）
							</div>
						</div>
						<div className="cluster-slider-input-number-content">
							<InputNumber
								style={{ width: '120px' }}
								addonAfter="GB"
								value={quotaMemory?.allocatable || 0}
								onChange={(value: number | null) =>
									handleChange(value, 'memory')
								}
								step={0.1}
								precision={1}
								min={0}
								max={getCpuMemoryMax('memory')}
							/>
						</div>
					</div>
					<MidSlider
						min={0}
						max={getCpuMemoryMax('memory')}
						value={quotaMemory.allocatable}
						setValue={(value) => {
							setQuotaMemory({
								...quotaMemory,
								allocatable: value
							});
						}}
						step={0.1}
						used={quotaMemory.used}
						tooltip={`已使用${formatNumber(quotaMemory.used, 1)}GB`}
						type="memory"
					/>
				</div>
				<Divider />
				<Space>
					<Button
						type="primary"
						onClick={handleSubmit}
						loading={judgeBth()}
					>
						确定
					</Button>
					<Button
						onClick={() => {
							if (params.projectId) {
								// * 项目下集群分配回退
								if (params.clusterId) {
									if (JSON.stringify(editCluster) !== '{}') {
										setProCluster({
											clusterId:
												currentCluster?.clusterId,
											clusterNickName:
												currentCluster?.clusterNickName,
											cpu: {
												...quotaCpu,
												allocatable:
													editCluster.cpu.request,
												request: editCluster.cpu.request
											},
											memory: {
												...quotaMemory,
												allocatable:
													editCluster.memory.request,
												request:
													editCluster.memory.request
											}
										});
									} else {
										setProCluster({
											clusterId:
												currentCluster?.clusterId,
											clusterNickName:
												currentCluster?.clusterNickName,
											cpu: {
												...quotaCpu,
												request: quotaCpu.used
											},
											memory: {
												...quotaMemory,
												request: quotaMemory.used
											}
										});
									}
								}
								history.push(
									`/organization/project/add/${params.organId}/${params.projectId}/${params.projectName}`
								);
							} else {
								// * 组织下集群分配配额回退
								if (params.clusterId) {
									// * 编辑时editCluster会有值，新增时editCluster是一个空对象{}
									if (JSON.stringify(editCluster) !== '{}') {
										setOrgCluster({
											clusterId: currentCluster?.id,
											clusterNickName:
												currentCluster?.nickname,
											cpu: {
												...quotaCpu,
												allocatable:
													editCluster.cpu.request,
												request: editCluster.cpu.request
											},
											memory: {
												...quotaMemory,
												allocatable:
													editCluster.memory.request,
												request:
													editCluster.memory.request
											}
										});
									} else {
										// * 这个else应该进不来，但先放着
										setOrgCluster({
											clusterId: currentCluster?.id,
											clusterNickName:
												currentCluster?.nickname,
											cpu: {
												...quotaCpu,
												request: quotaCpu.used
											},
											memory: {
												...quotaMemory,
												request: quotaMemory.used
											}
										});
									}
								}
								history.push(
									`/platform/organizationManagement/add/${params.organId}`
								);
							}
						}}
						disabled={judgeBth()}
					>
						取消
					</Button>
				</Space>
			</ProContent>
		</ProPage>
	);
}
const mapStateToProps = (state: StoreState) => ({
	editCluster: state.organization.editCluster,
	propsClusters: state.organization.clusters,
	propsProjectClusters: state.organization.proClusters
});
export default connect(mapStateToProps, {
	setOrgCluster,
	setProCluster,
	setEditCluster
})(ClusterQuota);

import { Button, Modal, notification } from 'antd';
import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { useHistory } from 'react-router';
import Actions from '@/components/Actions';
import { ProContent, ProHeader, ProPage } from '@/components/ProPage';
import ProTable from '@/components/ProTable';
import EditOrganization from './EditOrganization';
import { StoreState } from '@/types';
import {
	getOrganizations,
	deleteOrganization,
	createOrganization,
	updateOrganizationApi
} from '@/services/organization';
import { CreateOrganizationProps, OrganizationItem } from './organization.d';
import { nullRender } from '@/utils/utils';
import AllotBackupServer from './allotBackupServer';
import storage from '@/utils/storage';
import { getRoleMenusByRoleId } from '@/services/role';
const LinkButton = Actions.LinkButton;
const { confirm } = Modal;
function OrganizationManagement({
	buttonList
}: {
	buttonList: any[];
}): JSX.Element {
	const history = useHistory();
	const [organizations, setOrganizations] = useState<OrganizationItem[]>([]);
	const [editOrganization, setEditOrganization] =
		useState<OrganizationItem>();
	const [loading, setLoading] = useState<boolean>(false);
	const [keyword, setKeyword] = useState<string>('');
	const [open, setOpen] = useState<boolean>(false);
	// * 是否接入了观云台
	const [isAccess] = useState<boolean>(storage.getLocal('isAccessGYT'));
	// * 分配备份服务器的弹窗表单
	const [allotOpen, setAllotOpen] = useState<boolean>(false);
	// * 获取用户当前角色
	const role = JSON.parse(storage.getLocal('role'));
	// * 判断当前用户是否具有新增、管理配额、删除的权限 true-有权限 false-无权限
	const [actionAuth, setActionAuth] = useState<boolean>(true);
	useEffect(() => {
		getData();
	}, []);
	useEffect(() => {
		async function init() {
			if (JSON.stringify(role) !== '{}') {
				if (role.isAdmin) {
					setActionAuth(true);
					return;
				}
				if (
					role.userRoleList.some((item: any) => item.weight === 2) &&
					role.manager
				) {
					// * role.manager有值 则组织管理员和自定义管理类型角色叠加
					const res = await getRoleMenusByRoleId({
						roleId: role.manager
					});
					if (!res.success) {
						setActionAuth(false);
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
						return;
					}
					if (
						res.data.find(
							(item: any) =>
								item.url ===
									'platform/organizationManagement' &&
								item.own === true
						)
					) {
						setActionAuth(true);
						return;
					} else {
						setActionAuth(false);
						return;
					}
				}
				if (role.userRoleList.some((item: any) => item.weight === 2)) {
					// * 当前用户为组织管理员 或 以下权限
					setActionAuth(false);
					return;
				}
			}
		}
		init();
	}, [role]);
	const getData = (value?: string) => {
		setLoading(true);
		getOrganizations({ keyword: value ?? keyword, manager: true })
			.then((res) => {
				if (res.success) {
					setOrganizations(res.data);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const updateOrganization = (values: CreateOrganizationProps) => {
		if (editOrganization) {
			// * 编辑组织
			updateOrganizationApi(values).then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '组织更新成功!'
					});
					getData();
				} else {
					notification.error({
						message: '失败',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			});
		} else {
			// * 添加组织
			createOrganization(values).then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '组织添加成功!'
					});
					getData();
				} else {
					notification.error({
						message: '失败',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			});
		}
	};
	const Operation = {
		primary: (
			<Button
				type="primary"
				onClick={() => {
					setEditOrganization(undefined);
					setOpen(true);
				}}
				disabled={isAccess}
				title={isAccess ? '平台已接入观云台，请联系观云台管理员' : ''}
			>
				新增
			</Button>
		)
	};
	const onSearch = (value: string) => {
		setKeyword(value);
		getData(value);
	};
	const nameRender = (
		value: string,
		record: OrganizationItem,
		index: number
	) => {
		const isLink = buttonList.filter(
			(item: MenuResItem) => item.name !== 'organizationList'
		);
		return (
			<div
				style={{ width: 250 }}
				title={value}
				className={`${isLink?.length ? 'name-link' : ''} text-overflow`}
				onClick={() => {
					if (isLink?.length) {
						storage.setSession('organId', record.organId);
						history.push(
							`/${isLink?.[0]?.url}` || '/organization/overview'
						);
					}
				}}
			>
				{value}
			</div>
		);
	};
	const actionRender = (
		value: any,
		record: OrganizationItem,
		index: number
	) => {
		return (
			<Actions>
				<LinkButton
					onClick={() => {
						if (isAccess) {
							setEditOrganization(record);
							setAllotOpen(true);
						} else {
							history.push(
								`/platform/organizationManagement/add/${record.organId}`
							);
						}
					}}
				>
					{isAccess ? '备份服务器管理' : '管理配额'}
				</LinkButton>
				<LinkButton
					onClick={() => {
						setEditOrganization(record);
						setOpen(true);
					}}
					disabled={isAccess}
					title={
						isAccess ? '平台已接入观云台，请联系观云台管理员' : ''
					}
				>
					编辑
				</LinkButton>
				<LinkButton
					disabled={isAccess}
					title={
						isAccess ? '平台已接入观云台，请联系观云台管理员' : ''
					}
					onClick={() => {
						if (record.projectCount && record.projectCount !== 0) {
							notification.error({
								message: '错误',
								description: '当前组织下存在项目，无法删除！'
							});
						} else {
							confirm({
								title: '删除确认',
								content: '删除将无法找回，是否继续？',
								onOk: () => {
									return deleteOrganization({
										organId: record.organId
									}).then((res) => {
										if (res.success) {
											notification.success({
												message: '成功',
												description: '组织删除成功！'
											});
											getData();
										} else {
											notification.error({
												message: '失败',
												description: (
													<>
														<p>{res.errorMsg}</p>
														<p>{res.errorDetail}</p>
													</>
												)
											});
										}
									});
								}
							});
						}
					}}
				>
					删除
				</LinkButton>
			</Actions>
		);
	};
	return (
		<ProPage>
			<ProHeader
				title="组织管理"
				subTitle="主要为管理不同组织、配额管理等"
			/>
			<ProContent>
				<ProTable
					dataSource={organizations}
					rowKey="organId"
					operation={actionAuth ? Operation : undefined}
					showRefresh
					search={{
						placeholder: '请输入组织名称进行搜索',
						onSearch: onSearch
					}}
					loading={loading}
					onRefresh={() => getData(keyword || '')}
				>
					<ProTable.Column
						dataIndex="name"
						title="组织名称"
						render={nameRender}
						width={250}
					/>
					<ProTable.Column
						dataIndex="projectCount"
						title="项目数"
						render={nullRender}
					/>
					<ProTable.Column
						dataIndex="userCount"
						title="用户数"
						render={nullRender}
					/>
					<ProTable.Column
						dataIndex="description"
						title="描述"
						ellipsis={true}
					/>
					{actionAuth && (
						<ProTable.Column
							dataIndex="action"
							title="操作"
							width={200}
							render={actionRender}
						/>
					)}
				</ProTable>
			</ProContent>
			{open && (
				<EditOrganization
					open={open}
					onCancel={() => setOpen(false)}
					data={editOrganization}
					onCreate={updateOrganization}
				/>
			)}
			{allotOpen && editOrganization && (
				<AllotBackupServer
					open={allotOpen}
					organId={editOrganization.organId}
					onCancel={() => setAllotOpen(false)}
				/>
			)}
		</ProPage>
	);
}
export default connect((state: StoreState) => ({
	buttonList: state.auth.buttonList
}))(OrganizationManagement);

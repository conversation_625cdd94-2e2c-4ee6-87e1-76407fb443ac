import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>ontent, ProHeader, ProPage } from '@/components/ProPage';
import {
	Button,
	Col,
	Collapse,
	Divider,
	Empty,
	InputNumber,
	notification,
	Row,
	Space,
	Tag,
	Modal,
	Input,
	Table
} from 'antd';
import {
	CheckOutlined,
	DeleteOutlined,
	EditOutlined,
	MinusOutlined,
	PlusOutlined,
	RightOutlined
} from '@ant-design/icons';
import { IconFont } from '@/components/IconFont';
import { useHistory, useParams } from 'react-router';
import {
	CpuMemoryItem,
	DetailParams,
	StorageListTableItem
} from '../OrganizationDetail/organization.detail';
import { connect } from 'react-redux';
import {
	setOrgCluster,
	setOrgClusters,
	setEditCluster,
	deleteClusterById
} from '@/redux/organization/organization';
import { StoreState } from '@/types';
import { OrganizationQuotaProps, StorageAllotItem } from './organization';
import { getServer } from '@/services/backup';
import {
	allotQuota,
	getCpuMemory,
	getOrgBackupServer,
	getOrgStorage
} from '@/services/organization';
import { getClustersQuota } from '@/services/common';
import MidSlider from '@/components/MidSlider';
import {
	agentPhaseRender,
	formatNumber,
	objectRemoveDuplicatesByKey
} from '@/utils/utils';
import { getAgents, getOrganAgentList } from '@/services/agent';
import { FiltersProps } from '@/types/comment';
import './index.less';
const { CheckableTag } = Tag;
const { Panel } = Collapse;
const { confirm } = Modal;
const { Search } = Input;
function OrganizationQuota(props: OrganizationQuotaProps): JSX.Element {
	const { clusters, setOrgClusters, setEditCluster, deleteClusterById } =
		props;
	const history = useHistory();
	const params: DetailParams = useParams();
	const [originClusters, setOriginClusters] = useState<any[]>([]);
	const [orgStorages, setOrgStorage] = useState<any[]>([]);
	const [storages, setStorages] = useState<StorageListTableItem[]>([]);
	const [orgSelectStorages, setOrgSelectStorages] = useState<
		StorageAllotItem[]
	>([]);
	const [selectStorages, setSelectStorages] = useState<StorageAllotItem[]>(
		[]
	);
	const [backups, setBackups] = useState<any[]>([]);
	const [selectBackups, setSelectBackups] = useState<any>([]);
	const [selectedTags, setSelectedTags] = useState<string[]>([]);
	const [selectedBackupServerTags, setSelectedBackupServerTags] = useState<
		string[]
	>([]);
	const [agents, setAgents] = useState<AgentItem[]>([]);
	const [showDataSource, setShowDataSource] = useState<AgentItem[]>([]);
	const [selectAgents, setSelectedAgents] = useState<AgentItem[]>([]);
	const [primaryKeys, setPrimaryKeys] = useState<string[]>([]);
	const [controllerFilters, setControllerFilters] = useState<FiltersProps[]>(
		[]
	);
	const [loading, setLoading] = useState<boolean>(false);
	useEffect(() => {
		async function getAllData() {
			setLoading(true);
			if (clusters.length === 0) {
				const res1 = await getCpuMemory({
					organId: params.organId,
					detail: true
				});
				if (res1.success) {
					if (res1.data.length > 0) {
						const list = res1.data.map((item) => {
							item.cpu = {
								...item.cpu,
								allocatable: item.cpu.request
							};
							item.memory = {
								...item.memory,
								allocatable: item.memory.request
							};
							return item;
						});
						setOrgClusters(list);
					} else {
						setOrgClusters([]);
					}
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res1.errorMsg}</p>
								<p>{res1.errorDetail}</p>
							</>
						)
					});
				}
			}
			// * 组织下已分配的存储
			const res2 = await getOrgStorage({
				organId: params.organId,
				detail: true
			});
			if (res2.success) {
				const st: any[] = [];
				res2.data.map((item: CpuMemoryItem) => {
					item.storageList.map((i: any) => {
						const result = {
							clusterId: item.clusterId,
							storageId: i.storageId,
							allocatable: i.storage.request,
							used: i.storage.used,
							request: i.storage.request
						};
						st.push(result);
					});
				});
				// * 保存一份数据用于取消后的回显
				setOrgSelectStorages(st);
				// * 回显数据
				setSelectStorages(st);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res2.errorMsg}</p>
							<p>{res2.errorDetail}</p>
						</>
					)
				});
			}
			const res3 = await getOrgBackupServer({
				organId: params.organId,
				detail: true
			});
			if (res3.success) {
				setSelectBackups(res3.data);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res3.errorMsg}</p>
							<p>{res3.errorDetail}</p>
						</>
					)
				});
			}
			const res4 = await getOrganAgentList({ organId: params.organId });
			if (res4.success) {
				setSelectedAgents(res4.data);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res4.errorMsg}</p>
							<p>{res4.errorDetail}</p>
						</>
					)
				});
			}
			setLoading(false);
		}
		getAllData();
	}, []);
	useEffect(() => {
		if (orgStorages && selectStorages) {
			// * 分配后，编辑的回显
			const list = orgStorages.map((item) => {
				return {
					...item,
					storage: {
						...item.storage,
						sused: item.storage.used, // * 当前存储的已使用额（全局）
						used:
							selectStorages.find(
								(i) => i.storageId === item.storageId
							)?.used || 0 // * 项目下的已使用额
					}
				};
			});
			setStorages(list);
		}
	}, [orgStorages, selectStorages]);
	useEffect(() => {
		setOriginClusters(clusters);
		setSelectedTags(clusters.map((item: any) => item.clusterId));
		setSelectedBackupServerTags(
			clusters.map((item: any) => item.clusterId)
		);
		if (clusters.length === 0) {
			setStorages([]);
			setBackups([]);
			setAgents([]);
		} else {
			const clusterIds = clusters.map((item: any) => item.clusterId);
			getClustersQuota({
				clusterIdList: clusterIds,
				detail: true
			}).then((res) => {
				if (res.success) {
					const list: any = [];
					res.data.map((item: any) => {
						item.storageList.map((i: any) => {
							list.push({
								...i,
								clusterId: item.clusterId,
								clusterNickName: clusters.find(
									(ci) => ci.clusterId === item.clusterId
								).clusterNickName
							});
						});
					});
					setOrgStorage(list);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			});
			getServer({
				keyword: '',
				clusterIds: clusterIds,
				withDetail: false
			}).then((res) => {
				if (res.success) {
					setBackups(res.data);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			});
			getAgentList(clusterIds).then((res) => {
				const list: any[] = [];
				res.map((item) => {
					if (item?.data) {
						list.push(...item?.data);
					}
				});
				setAgents(list);
				const controllerList = list?.map((item) => ({
					text: item.controller,
					value: item.clusterId
				}));
				setControllerFilters(
					objectRemoveDuplicatesByKey(controllerList, 'value')
				);
			});
		}
		async function getAgentList(clusterIds: string[]) {
			const promise = [];
			for (let i = 0; i < clusterIds.length; i++) {
				const r = await getAgentData(clusterIds[i]);
				promise.push(r);
			}
			return Promise.all(promise);
		}
	}, [clusters]);
	useEffect(() => {
		if (agents.length > 0 && selectAgents) {
			const list = agents.filter(
				(item) =>
					!selectAgents.find(
						(i) =>
							`${i.name}${i.address}${i.clusterId}` ===
							`${item.name}${item.address}${item.clusterId}`
					)
			);
			setShowDataSource(list);
		}
	}, [agents, selectAgents]);
	const getAgentData = async (clusterId: string) => {
		const res = await getAgents({ clusterId });
		if (res.success) {
			return res;
		} else {
			notification.error({
				message: '错误',
				description: (
					<>
						<p>{res.errorMsg}</p>
						<p>{res.errorDetail}</p>
					</>
				)
			});
		}
	};
	const handleSubmit = () => {
		if (originClusters.length === 0) {
			notification.warning({
				message: '提醒',
				description: '请选择集群！'
			});
			return;
		}
		if (
			selectStorages.some(
				(item: { storageId: string; allocatable: number }) =>
					item.allocatable === 0 || !item.allocatable
			)
		) {
			notification.warning({
				message: '提醒',
				description: '请分配存储配额'
			});
			return;
		}
		if (
			selectStorages.some(
				(item) => item.used && item.allocatable < item.used
			)
		) {
			notification.warning({
				message: '提醒',
				description: '当前存储配额值小于已使用值！'
			});
			return;
		}
		const quotasTemp = originClusters.map((item) => {
			const result: any = {};
			result.clusterId = item.clusterId;
			result.cpu = {};
			result.cpu.request = item.cpu.allocatable || item.cpu.request;
			result.memory = {};
			result.memory.request =
				item.memory.allocatable || item.memory.request;
			result.storageList = selectStorages
				.filter((i: any) => i.clusterId === item.clusterId)
				.map((it) => {
					return {
						storageId: it.storageId,
						storage: {
							request: it.allocatable
						}
					};
				});
			result.agentDTOList = selectAgents.filter(
				(o) => o.clusterId === item.clusterId
			);
			return result;
		});
		const sendData = {
			organId: params.organId,
			quotaList: quotasTemp,
			backupServerDTOList: selectBackups
		};
		console.log(sendData);
		allotQuota(sendData).then((res) => {
			if (res.success) {
				notification.success({
					message: '成功',
					description: '组织配额分配成功！'
				});
				setOrgClusters([]);
				history.push('/platform/organizationManagement');
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	// * 存储拖动条和输入框的方法
	const onChange = (
		newValue: number | null,
		record: StorageListTableItem
	) => {
		// * 存储超分情况判断
		if (storageMaxCompute(record) < 0) {
			// * 当拖动/输入的存储不是非选中的存储的情况下默认将当前存储加入选中存储
			if (
				selectStorages.findIndex(
					(item) => item.storageId === record.storageId
				) < 0
			) {
				const st = {
					storageId: record.storageId as string,
					allocatable:
						(newValue as number) <= storageMaxCompute(record)
							? (newValue as number)
							: storageMaxCompute(record),
					clusterId: record.clusterId,
					used: record.storage.used
				};
				setSelectStorages([st, ...selectStorages]);
			} else {
				const list = selectStorages.map((item) => {
					const result: any = { ...item };
					if (item.storageId === record.storageId) {
						result.allocatable =
							(newValue as number) <= storageMaxCompute(record)
								? newValue
								: storageMaxCompute(record);
					}
					return result;
				});
				setSelectStorages(list);
			}
		} else {
			// * 当拖动/输入的存储不是非选中的存储的情况下默认将当前存储加入选中存储
			if (
				selectStorages.findIndex(
					(item) => item.storageId === record.storageId
				) < 0
			) {
				const st = {
					storageId: record.storageId as string,
					allocatable: newValue as number,
					clusterId: record.clusterId,
					used: record.storage.used
				};
				setSelectStorages([st, ...selectStorages]);
			} else {
				const list = selectStorages.map((item) => {
					const result: any = { ...item };
					if (item.storageId === record.storageId) {
						result.allocatable = newValue;
					}
					return result;
				});
				setSelectStorages(list);
			}
		}
	};
	// * 快速筛选方法
	const handleChange = (tag: string, checked: boolean, type: string) => {
		if (type === 'storage') {
			const nextSelectedTags = checked
				? [...selectedTags, tag]
				: selectedTags.filter((t) => t !== tag);
			setSelectedTags(nextSelectedTags);
		} else {
			const nextSelectedTags = checked
				? [...selectedBackupServerTags, tag]
				: selectedBackupServerTags.filter((t) => t !== tag);
			setSelectedBackupServerTags(nextSelectedTags);
		}
	};
	// * cpu memory修改
	const handleEdit = (record: any) => {
		setEditCluster(record);
		deleteClusterById(record.clusterId);
		history.push(
			`/platform/organizationManagement/add/${params.organId}/addCpuAndMemory/${record.clusterId}`
		);
	};
	// * cpu memory移除
	const handleRemove = (record: any) => {
		if (originClusters.length === 1) {
			notification.warning({
				message: '提醒',
				description: '请保证当前组织下最少存在一个可用集群!'
			});
			return;
		}
		if (
			!record.cpu.used ||
			record.cpu.used === 0 ||
			!record.memory.used ||
			record.memory.used === 0
		) {
			const sl = storages.filter(
				(item) => item.clusterId === record.clusterId
			);
			if (sl.find((item) => item.storage.used !== 0)) {
				notification.warning({
					message: '提醒',
					description: '当前集群下存在已使用的存储，无法移除！'
				});
				return;
			}
			const bl = backups.filter(
				(item) => item.clusterId === record.clusterId
			);
			if (bl.find((item) => item.using === true)) {
				notification.warning({
					message: '提醒',
					description: '当前备份服务器已被使用，无法取消分配!'
				});
				return;
			}
			confirm({
				title: '操作提醒',
				content:
					'移除该集群会将其所属的存储和备份服务器一起移除，请确认！',
				onOk: () => {
					const list = originClusters.filter(
						(item) => item.clusterId !== record.clusterId
					);
					setOriginClusters(list);
					setOrgClusters(list);
					const slt = storages.filter(
						(item) => item.clusterId !== record.clusterId
					);
					setStorages(slt);
					const sslt = selectStorages.filter(
						(item) => item.clusterId !== record.clusterId
					);
					setSelectStorages(sslt);
					const blt = backups.filter(
						(item) => item.clusterId !== record.clusterId
					);
					setBackups(blt);
					const sblt = selectBackups.filter(
						(item: any) => item.clusterId !== record.clusterId
					);
					setSelectBackups(sblt);
				}
			});
		} else {
			notification.warning({
				message: '提醒',
				description: '当前集群下已使用CPU和内存，无法移除该集群！'
			});
			return;
		}
	};
	// * 选择存储
	const handleCheckStorage = (record: StorageListTableItem) => {
		if (
			selectStorages.find((item) => item.storageId === record.storageId)
		) {
			const st = selectStorages.find(
				(item) => item.storageId === record.storageId
			);
			if (st?.used && st.used !== 0) {
				notification.warning({
					message: '提醒',
					description: '当前存储已被使用，无法取消分配！'
				});
				return;
			}
			const list = selectStorages.filter(
				(item: StorageAllotItem) => item.storageId !== record.storageId
			);
			setSelectStorages(list);
		} else {
			if (
				orgSelectStorages.find(
					(item) => item.storageId === record.storageId
				)
			) {
				const temp = orgSelectStorages.find(
					(item) => item.storageId === record.storageId
				);
				setSelectStorages([
					...selectStorages,
					temp as StorageAllotItem
				]);
			} else {
				setSelectStorages([
					...selectStorages,
					{
						storageId: record.storageId as string,
						allocatable: record.storage.allocatable as number,
						clusterId: record.clusterId,
						used: record.storage.used
					}
				]);
			}
		}
	};
	// * 选择备份服务器
	const handleCheckBackup = (record: any) => {
		if (selectBackups.find((item: any) => item.id === record.id)) {
			const sbt = selectBackups.find(
				(item: any) => item.id === record.id
			);
			if (sbt.using === true) {
				notification.warning({
					message: '提醒',
					description: '当前备份服务器已被使用，无法取消分配!'
				});
				return;
			}
			const list = selectBackups.filter(
				(item: any) => item.id !== record.id
			);
			setSelectBackups(list);
		} else {
			setSelectBackups([...selectBackups, record]);
		}
	};
	const storageMaxCompute: (record: StorageListTableItem) => number = (
		record: StorageListTableItem
	) => {
		const st = orgSelectStorages.find(
			(i) => i.storageId === record.storageId
		);
		const result =
			record.storage.request - record.storage.sused + (st?.request || 0);
		return result;
	};
	// * 服务器搜索
	const handleSearch = (value: string) => {
		const list = agents
			.filter(
				(item) =>
					!selectAgents.find(
						(i) =>
							`${i.name}${i.address}${i.clusterId}` ===
							`${item.name}${item.address}${item.clusterId}`
					)
			)
			.filter((item) => item.name.includes(value));
		setShowDataSource(list);
	};
	const onAgentChange = (selectedRowKeys: any) => {
		setPrimaryKeys(selectedRowKeys);
	};
	// * 将以选择的服务器添加进右边的已选服务器中
	const onClick = () => {
		const list = primaryKeys.map((item: string) => {
			return agents.find(
				(i: AgentItem) => `${i.name}${i.address}${i.clusterId}` === item
			);
		});
		setPrimaryKeys([]);
		setSelectedAgents([...selectAgents, ...(list as AgentItem[])]);
	};
	const cancelSelect = (record: AgentItem) => {
		if (
			record.relationMiddlewareNum &&
			record.relationMiddlewareNum !== 0
		) {
			notification.warning({
				message: '提示',
				description: '该客户端存在已接入的服务，无法移除！'
			});
			return;
		}
		const list = selectAgents.filter(
			(item) =>
				`${item.name}${item.address}${item.clusterId}` !==
				`${record.name}${record.address}${record.clusterId}`
		);
		setSelectedAgents(list);
	};
	const handleClear = () => {
		if (
			selectAgents.find(
				(item) =>
					item.relationMiddlewareNum &&
					item.relationMiddlewareNum !== 0
			)
		) {
			notification.warning({
				message: '提示',
				description: '当前已选择的客户端存在已接入的服务，无法清空！'
			});
			return;
		}
		setSelectedAgents([]);
	};
	return (
		<ProPage>
			<ProHeader
				title="管理组织配额"
				onBack={() => {
					setOrgClusters([]);
					history.push('/platform/organizationManagement');
				}}
			/>
			<ProContent>
				<Collapse expandIconPosition="end" defaultActiveKey={['1']}>
					<Panel key="1" header="资源配额分配概览">
						<div className="organization-quota-add-quota-content">
							<div
								className="organization-quota-add-quota-item"
								onClick={() => {
									setEditCluster({});
									history.push(
										`/platform/organizationManagement/add/${params.organId}/addCpuAndMemory`
									);
								}}
							>
								<PlusOutlined />
							</div>
							{originClusters.map((item, index) => {
								return (
									<div
										className="organization-quota-item"
										key={index}
									>
										<div className="organization-quota-title">
											<IconFont
												type="icon-jiqun"
												style={{
													fontSize: 28,
													marginRight: 16
												}}
											/>
											<span>{item.clusterNickName}</span>
										</div>
										<div className="organization-quota-content">
											<p>
												CPU(核)：
												{item?.cpu?.request?.toFixed(1)}
											</p>
											<p>
												内存(GB)：
												{item?.memory?.request.toFixed(
													1
												)}
											</p>
										</div>
										<div className="organization-delete">
											<div
												className="organization-delete-item"
												onClick={() => handleEdit(item)}
											>
												<EditOutlined />
												修改
											</div>
											<div
												className="organization-delete-item"
												onClick={() =>
													handleRemove(item)
												}
											>
												<DeleteOutlined />
												移除
											</div>
										</div>
									</div>
								);
							})}
						</div>
					</Panel>
				</Collapse>
				<Collapse expandIconPosition="end" defaultActiveKey={['1']}>
					<Panel key="1" header="存储配额分配概览">
						<div className="organization-quota-storage-content">
							{storages.length === 0 && (
								<Empty
									style={{
										width: '100%'
									}}
									image={Empty.PRESENTED_IMAGE_SIMPLE}
									description={<div>当前无数据</div>}
								/>
							)}
							{storages.length !== 0 && (
								<>
									<div className="rapid-screening-content">
										<div>快速筛选</div>
										{originClusters.map((item) => (
											<CheckableTag
												key={item.clusterId}
												checked={
													selectedTags.indexOf(
														item.clusterId
													) > -1
												}
												onChange={(checked) => {
													handleChange(
														item.clusterId,
														checked,
														'storage'
													);
												}}
											>
												{item.clusterNickName}(
												{
													storages.filter(
														(i: any) =>
															i.clusterId ===
															item.clusterId
													)?.length
												}
												)
											</CheckableTag>
										))}
									</div>
									<div className="organization-quota-storage">
										{storages
											.filter(
												(item: StorageListTableItem) =>
													selectedTags.includes(
														item.clusterId
													)
											)
											.map(
												(
													item: StorageListTableItem
												) => {
													return (
														<div
															className="organization-quota-storage-item"
															key={item.storageId}
															style={
																selectStorages.find(
																	(
																		i: StorageAllotItem
																	) =>
																		i.storageId ===
																		item.storageId
																)
																	? {
																			borderColor:
																				'#226ee7'
																	  }
																	: {}
															}
														>
															<div
																className="organization-quota-storage-title"
																onClick={() =>
																	handleCheckStorage(
																		item
																	)
																}
															>
																<div className="organization-quota-storage-title-content">
																	<IconFont
																		type="icon-quota"
																		style={{
																			fontSize: 28,
																			marginRight: 16
																		}}
																	/>
																	<div className="storage-title-content">
																		<p>
																			(
																			{
																				item
																					?.storageType[0]
																			}
																			)
																			{
																				item?.name
																			}
																			(GB)
																			{item
																				.storageType
																				.length >
																				1 && (
																				<span className="available-domain">
																					可用区
																				</span>
																			)}
																		</p>
																		<p>
																			{
																				item?.clusterNickName
																			}
																		</p>
																	</div>
																</div>
																{selectStorages.find(
																	(
																		i: StorageAllotItem
																	) =>
																		i?.storageId ===
																		item?.storageId
																) && (
																	<div>
																		<CheckOutlined
																			style={{
																				fontSize: 17,
																				color: '#226ee7'
																			}}
																		/>
																	</div>
																)}
															</div>
															<Row>
																<Col span={20}>
																	<MidSlider
																		max={storageMaxCompute(
																			item
																		)}
																		setValue={(
																			value
																		) =>
																			onChange(
																				value,
																				item
																			)
																		}
																		step={1}
																		value={
																			selectStorages.find(
																				(
																					i
																				) =>
																					i.storageId ===
																					item.storageId
																			)
																				?.allocatable
																		}
																		type="storage"
																		tooltip={`已使用${formatNumber(
																			item
																				.storage
																				.used
																		)}GB`}
																		used={
																			item
																				.storage
																				.used
																		}
																		style={{
																			width: '99%'
																		}}
																	/>
																</Col>
																<Col span={4}>
																	<InputNumber
																		min={0}
																		max={storageMaxCompute(
																			item
																		)}
																		value={
																			selectStorages.find(
																				(
																					i
																				) =>
																					i.storageId ===
																					item.storageId
																			)
																				?.allocatable ||
																			0
																		}
																		onChange={(
																			value:
																				| number
																				| null
																		) =>
																			onChange(
																				value,
																				item
																			)
																		}
																		step={1}
																		precision={
																			0
																		}
																		addonAfter="GB"
																	/>
																</Col>
															</Row>
														</div>
													);
												}
											)}
									</div>
								</>
							)}
						</div>
					</Panel>
				</Collapse>
				<Collapse expandIconPosition="end" defaultActiveKey={['1']}>
					<Panel key="1" header="备份服务器分配概览">
						<div className="organization-quota-backup-content">
							{backups.length === 0 && (
								<Empty
									style={{
										width: '100%'
									}}
									image={Empty.PRESENTED_IMAGE_SIMPLE}
									description={<div>当前无数据</div>}
								/>
							)}
							{backups.length !== 0 && (
								<>
									<div className="rapid-screening-content">
										<div>快速筛选</div>
										{originClusters.map((item) => (
											<CheckableTag
												key={item.clusterId}
												checked={
													selectedBackupServerTags.indexOf(
														item.clusterId
													) > -1
												}
												onChange={(checked) => {
													handleChange(
														item.clusterId,
														checked,
														'backupserver'
													);
												}}
											>
												{item?.clusterNickName}(
												{
													backups.filter(
														(i: any) =>
															i.clusterId ===
															item.clusterId
													)?.length
												}
												)
											</CheckableTag>
										))}
									</div>
									<div className="organization-quota-backup">
										{backups
											.filter((item) =>
												selectedBackupServerTags.includes(
													item.clusterId
												)
											)
											.map((item: any, index) => {
												return (
													<div
														className="organization-quota-backup-item"
														style={
															selectBackups.find(
																(i: any) =>
																	i.id ===
																	item.id
															) && {
																backgroundColor:
																	'#226ee7',
																color: 'white'
															}
														}
														key={index}
														onClick={() =>
															handleCheckBackup(
																item
															)
														}
													>
														<IconFont
															type={
																selectBackups.find(
																	(i: any) =>
																		i.id ===
																		item.id
																)
																	? 'icon-quota-white'
																	: 'icon-quota'
															}
															style={{
																fontSize: 28,
																marginRight: 16
															}}
														/>
														<div className="backup-title-content">
															<p>{item?.name}</p>
															<p>
																{
																	item?.clusterNickName
																}
															</p>
														</div>
														{selectBackups.find(
															(i: any) =>
																i.id === item.id
														) && (
															<div>
																<CheckOutlined
																	style={{
																		marginLeft: 18,
																		fontSize: 30,
																		color: 'white'
																	}}
																/>
															</div>
														)}
													</div>
												);
											})}
									</div>
								</>
							)}
						</div>
					</Panel>
				</Collapse>
				<Collapse expandIconPosition="end" defaultActiveKey={['1']}>
					<Panel key="1" header="服务器分配概览">
						<div className="agent-allocation-content">
							<div className="agent-allocation-box">
								<div className="agent-allocation-title">
									<span className="mr-8">选择服务器</span>
									<Search
										allowClear
										placeholder="请输入主机名称"
										onSearch={handleSearch}
										style={{ width: 200 }}
									/>
								</div>
								<Table
									rowKey={(record) =>
										record.name +
										record.address +
										record.clusterId
									}
									dataSource={showDataSource}
									rowSelection={{
										onChange: onAgentChange,
										selectedRowKeys: primaryKeys
									}}
									pagination={false}
									scroll={{ y: 180 }}
								>
									<Table.Column
										title="主机名称"
										dataIndex="name"
										width={150}
									/>
									<Table.Column
										title="IP地址"
										dataIndex="address"
										width={130}
									/>
									<Table.Column
										title="操作系统"
										dataIndex="osType"
										width={100}
										filterMultiple={false}
										filters={[
											{ text: 'linux', value: 'linux' },
											{ text: 'darwin', value: 'darwin' }
										]}
										onFilter={(
											value: any,
											record: AgentItem
										) => record.osType === value}
									/>
									<Table.Column
										title="客户端状态"
										dataIndex="phase"
										render={agentPhaseRender}
										width={120}
										filters={[
											{ text: '运行中', value: 'Online' },
											{ text: '离线', value: 'Offline' },
											{
												text: '卸载中',
												value: 'Terminating'
											},
											{ text: '未知', value: 'unknown' }
										]}
										filterMultiple={false}
										onFilter={(
											value: any,
											record: AgentItem
										) => record.phase === value}
									/>
									<Table.Column
										title="管控控制器"
										dataIndex="controller"
										filterMultiple={false}
										filters={controllerFilters}
										onFilter={(
											value: any,
											record: AgentItem
										) => record.clusterId === value}
									/>
								</Table>
							</div>
							<Button
								type="primary"
								icon={<RightOutlined />}
								size="small"
								disabled={primaryKeys.length === 0}
								onClick={onClick}
							/>
							<div className="agent-allocation-box-1">
								<div className="agent-allocation-title">
									<span>
										已选服务器{' '}
										<span
											className="ml-8"
											style={{
												fontSize: 12,
												fontWeight: 400
											}}
										>
											{selectAgents.length}台
										</span>
									</span>
									<Button
										type="link"
										onClick={handleClear}
										disabled={selectAgents.length === 0}
									>
										清空
									</Button>
								</div>
								{selectAgents.map((item) => {
									return (
										<div
											className="agent-selected-list-item"
											key={
												item.name +
												item.address +
												item.clusterId
											}
										>
											<div>{item.name}</div>
											<div>{item.address}</div>
											<div>
												<Button
													icon={<MinusOutlined />}
													size="small"
													onClick={() =>
														cancelSelect(item)
													}
												/>
											</div>
										</div>
									);
								})}
							</div>
						</div>
					</Panel>
				</Collapse>
				<Divider />
				<Space>
					<Button
						type="primary"
						onClick={handleSubmit}
						loading={loading}
					>
						确定
					</Button>
					<Button
						onClick={() => {
							setOrgClusters([]);
							history.push('/platform/organizationManagement');
						}}
					>
						取消
					</Button>
				</Space>
			</ProContent>
		</ProPage>
	);
}
const mapStateToProps = (state: StoreState) => ({
	clusters: state.organization.clusters
});
export default connect(mapStateToProps, {
	setOrgCluster,
	setOrgClusters,
	setEditCluster,
	deleteClusterById
})(OrganizationQuota);

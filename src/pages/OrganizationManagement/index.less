.organization-quota-add-quota-content {
	display: flex;
	width: 100%;
	height: 178px;
	padding: 16px 20px;
	gap: 12px;
	.organization-quota-add-quota-item {
		width: 190px;
		height: 142px;
		border: 1px dashed #DADADA;
		border-radius: @border-radius;
		font-size: @font-5;
		text-align: center;
		line-height: 142px;
		cursor: pointer;
	}
	.organization-quota-item {
		width: 190px;
		height: 142px;
		border: 1px solid #DADADA;
		border-radius: @border-radius;
		padding: 20px 24px;
		position: relative;
		cursor: pointer;
		.organization-delete {
			position: absolute;
			top: 0;
			left: 0;
			opacity: 0;
			width: 190px;
			height: 142px;
			background-color: rgba(0, 0, 0, 0.7);
			border-radius: @border-radius;
		}
		.organization-quota-title {
			display: flex;
			align-items: center;
		}
		.organization-quota-content {
			margin-top: 32px;
			font-size: @font-1;
			line-height: @line-height-1;
		}
		&:hover {
			.organization-delete {
				opacity: 1;
				color: @white;
				font-size: @font-3;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				transition: all .5s ease;
				.organization-delete-item {
					width: 190px;
					height: 71px;
					display: flex;
					justify-content: center;
					align-items: center;
					border-bottom: 1px solid @white;
				}
			}
		}
	}
}
.organization-quota-storage-content {
	width: 100%;
	.organization-quota-storage {
		display: flex;
		flex-direction: column;
		gap: 12px;
		.organization-quota-storage-item {
			width: 100%;
			height: 114px;
			border: 1px solid #DADADA;
			padding: 14px 16px;
			cursor: pointer;
			.organization-quota-storage-title {
				display: flex;
				justify-content: space-between;
				.organization-quota-storage-title-content {
					display: flex;
					align-items: center;
					.storage-title-content {
						& > p:nth-child(2) {
							font-weight: @font-weight;
							font-size: @font-2;
							color: @black-2;
						}
					}
				}
			}
		}
	}
}
.organization-quota-backup-content {
	width: 100%;
	.organization-quota-backup {
		width: 100%;
		display: flex;
		gap: 12px;
		.organization-quota-backup-item {
			min-width: 178px;
			max-width: 250px;
			height: 64px;
			background-color: #FAF9FA;
			border-radius: @border-radius;
			display: flex;
			align-items: center;
			padding: 12px 16px;
			cursor: pointer;
			.backup-title-content {
				& > p {
					min-width: 60px;
					max-width: 130px;
					.mixin(textEllipsis);
					font-weight: @font-weight;
					font-size: @font-2;
				}
			}

		}
	}
}
.rapid-screening-content {
	width: 100%;
	padding: 0px 20px 14px 20px;
	border-bottom: 1px solid #D9D9D9;
	display: flex;
	gap: 12px;
	margin-bottom: @margin;
}
.cluster-quota-display-content {
	width: 100%;
	height: 200px;
	display: flex;
	gap: 24px;
	.cluster-quota-memory-display,
	.cluster-quota-cpu-display {
		width: 49%;
		height: 180px;
		border: 1px solid #DADADA;
		border-radius: @border-radius;
		padding: 24px;
		.cluster-quota-memory-title,
		.cluster-quota-cpu-title {
			display: flex;
			align-items: center;
			& > h3 {
				font-weight: @font-weight;
				margin-left: @margin;
			}
		}
		.cluster-quota-memory-content,
		.cluster-quota-cpu-content {
			display: flex;
			flex-wrap: wrap;
			margin-top: @margin-lg * 2;
			margin-left: @margin-lg * 2;
			.cluster-quota-memory-item,
			.cluster-quota-cpu-item {
				width: 49%;
				color: @black-2;
				font-size: @font-1;
				line-height: @line-height-1;
				& > span:nth-child(1) {
					width: 150px;
					margin-right: 8px;
				}
			}
		}
	}
}
.agent-allocation-content {
	display: flex;
	gap: 8px;
	align-items: center;
	.agent-allocation-box-1,
	.agent-allocation-box {
		width: 65%;
		height: 300px;
		padding: 12px;
		border-radius: @border-radius;
		border: 1px rgba(216,222,229,.4) solid;
		.agent-allocation-title {
			display: flex;
			align-items: center;
			margin-bottom: 8px;
			font-size: @font-2;
			line-height: @line-height-2;
			font-weight: @font-weight;
		}
	}
	.agent-allocation-box-1 {
		width: 33%;
		overflow-y: auto;
		.agent-allocation-title {
			justify-content: space-between;
		}
		.agent-selected-list-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 8px;
			& > div:nth-child(1) {
				width: 150px;
				.mixin(textEllipsis);
			}
		}
	}
}

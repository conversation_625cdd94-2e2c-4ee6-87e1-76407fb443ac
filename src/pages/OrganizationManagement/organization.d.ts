import { resProps } from '@/types/comment';

export interface EditOrganizationProps {
	open: boolean;
	onCancel: () => void;
	data?: OrganizationItem;
	onCreate: (values: CreateOrganizationProps) => void;
}
export interface UserRoleItem {
	organId: string;
	power: any;
	projectId: string;
	projectName: string;
	roleId: number;
	roleName: string;
	roleType: string;
	userName: string;
}
export interface UserDTO {
	aliasName: string;
	createTime: string;
	email: string;
	id: number;
	isAdmin: boolean;
	password: string;
	passwordTime: string;
	phone: string;
	power: any;
	roleId: number;
	roleName: string;
	userName: string;
	userRoleList: UserRoleItem[];
}
export interface OrganizationItem {
	description: string;
	name: string;
	organId: string;
	organizationManager: string | null;
	organizationManagerRoleId: number | null;
	projectCount: number | null;
	userCount: number | null;
	userDtoList: UserDTO[] | null;
	children?: ProjectItem[];
	projectDtoList?: ProjectItem[];
}
export interface CreateOrganizationProps {
	name: string;
	description: string | null;
	organizationManager?: string | null;
	organizationManagerRoleId?: number | null;
}
export interface getOrganizationResProps extends resProps {
	data: OrganizationItem[];
}
export interface organizationDetailResProps extends resProps {
	data: OrganizationItem;
}
export interface quotaItem {
	allocatable: number | null;
	request: number;
	total: number;
	usable: number;
	usage: number;
	used: number;
	oused: number;
}
export interface ClusterQuotaProps {
	setOrgCluster: (value: any) => void;
	setProCluster: (value: any) => void;
	setRefreshOrganization: (flag: boolean) => void;
	editCluster: any;
	propsClusters: any[];
	propsProjectClusters: any[];
}
export interface OrganizationQuotaProps {
	clusters: any[];
	setOrgCluster: (value: any) => void;
	setOrgClusters: (value: any) => void;
	setEditCluster: (value: any) => void;
	deleteClusterById: (value: any) => void;
}
export interface StorageAllotItem {
	storageId: string;
	allocatable: number;
	clusterId: string;
	used?: number;
	request?: number;
}

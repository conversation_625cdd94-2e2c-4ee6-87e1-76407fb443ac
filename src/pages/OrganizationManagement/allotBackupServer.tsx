import React, { useEffect, useState } from 'react';
import { Modal, notification } from 'antd';
import { CheckOutlined } from '@ant-design/icons';
import { IconFont } from '@/components/IconFont';
import { getServer } from '@/services/backup';
import {
	allotQuota,
	getCpuMemory,
	getOrgBackupServer
} from '@/services/organization';
import './index.less';
interface AllotBackupServerProps {
	open: boolean;
	onCancel: () => void;
	organId: string;
}
export default function AllotBackupServer(
	props: AllotBackupServerProps
): JSX.Element {
	const { open, onCancel, organId } = props;
	const [clusters, setClusters] = useState<any[]>([]);
	const [backups, setBackups] = useState<any[]>([]);
	const [selectBackups, setSelectBackups] = useState<any>([]);
	// const [loading, setLoading] = useState<boolean>(true);
	useEffect(() => {
		const getC = async () => {
			await getCpuMemory({ organId, detail: false }).then((res) => {
				if (res.success) {
					if (res.data.length > 0) {
						setClusters(res.data);
					} else {
						setClusters([]);
					}
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			});
		};
		// * 查组织下的备份服务器
		const getB = async () => {
			await getOrgBackupServer({ organId, detail: true }).then((res) => {
				if (res.success) {
					setSelectBackups(res.data);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			});
		};
		// setLoading(true);
		getB();
		getC();
		// Promise.all([getB, getC]).finally(() => {
		// 	setLoading(false);
		// });
	}, []);
	useEffect(() => {
		if (clusters.length > 0) {
			const clusterIds = clusters.map((item) => item.clusterId);
			getServer({
				keyword: '',
				clusterIds: clusterIds,
				withDetail: false
			}).then((res) => {
				if (res.success) {
					setBackups(res.data);
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		}
	}, [clusters]);
	// * 选择备份服务器
	const handleCheckBackup = (record: any) => {
		if (selectBackups.find((item: any) => item.id === record.id)) {
			const list = selectBackups.filter(
				(item: any) => item.id !== record.id
			);
			setSelectBackups(list);
		} else {
			setSelectBackups([...selectBackups, record]);
		}
	};
	const onOk = () => {
		const sendData = {
			organId,
			backupServerDTOList: selectBackups
		};
		onCancel();
		allotQuota(sendData).then((res) => {
			if (res.success) {
				notification.success({
					message: '成功',
					description: '组织备份服务器分配成功！'
				});
			} else {
				notification.error({
					message: '失败',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	return (
		<Modal
			title="管理配额"
			width={600}
			open={open}
			onCancel={onCancel}
			onOk={onOk}
		>
			<h2>备份服务器分配概览</h2>
			{/* <Spin spinning={loading}> */}
			<div className="organization-quota-backup-content">
				<div className="organization-quota-backup">
					{backups.map((item: any, index) => {
						return (
							<div
								className="organization-quota-backup-item"
								style={
									selectBackups.find(
										(i: any) => i.id === item.id
									) && {
										backgroundColor: '#226ee7',
										color: 'white'
									}
								}
								key={index}
								onClick={() => handleCheckBackup(item)}
							>
								<IconFont
									type={
										selectBackups.find(
											(i: any) => i.id === item.id
										)
											? 'icon-quota-white'
											: 'icon-quota'
									}
									style={{
										fontSize: 28,
										marginRight: 16
									}}
								/>
								<div className="backup-title-content">
									<p title={item?.name}>{item?.name}</p>
									<p title={item?.clusterNickName}>
										{item?.clusterNickName}
									</p>
								</div>
								{selectBackups.find(
									(i: any) => i.id === item.id
								) && (
									<div>
										<CheckOutlined
											style={{
												marginLeft: 18,
												fontSize: 30,
												color: 'white'
											}}
										/>
									</div>
								)}
							</div>
						);
					})}
				</div>
			</div>
			{/* </Spin> */}
		</Modal>
	);
}

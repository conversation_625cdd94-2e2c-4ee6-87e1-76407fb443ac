import React, { useEffect, useState } from 'react';
import { Form, Input, Modal, notification, Select } from 'antd';
import { getUserList } from '@/services/user';
import { formItemLayout618 } from '@/utils/const';
import { EditOrganizationProps } from './organization.d';
import pattern from '@/utils/pattern';
import { getRoleList } from '@/services/role';
import { getOrganizationDetail } from '@/services/organization';
import { UserItem } from '@/pages/ProjectDetail/projectDetail';

export default function EditOrganization(
	props: EditOrganizationProps
): JSX.Element {
	const { open, onCancel, data, onCreate } = props;
	const [form] = Form.useForm();
	const [users, setUsers] = useState<UserItem[]>([]);
	const [organizationRoleId, setOrganizationRoleId] = useState<number>();
	useEffect(() => {
		getRoles();
		getUserList({ keyword: '' }).then((res) => {
			if (res.success) {
				const list = res.data.filter(
					(item: any) => item.isAdmin !== true
				);
				setUsers(list);
			} else {
				notification.error({
					message: '失败',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	}, []);
	useEffect(() => {
		if (data) {
			form.setFieldsValue({
				name: data.name,
				description: data.description
			});
			getOrganizationDetail({
				organId: data.organId
			}).then((res: any) => {
				if (res.success) {
					form.setFieldsValue({
						userDtoList: res.data.userDtoList
							?.filter(
								(item: any) => item.roleName === '组织管理员'
							)
							?.map((item: any) => item.userName)
					});
				}
			});
		}
	}, [data]);

	const getRoles = () => {
		getRoleList({ key: '' }).then((res) => {
			if (res.success) {
				const organId = res.data.find((item) => item.weight === 2)?.id;
				setOrganizationRoleId(organId);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const onOk = () => {
		form.validateFields().then((values) => {
			console.log(values);
			const sendData = {
				...values,
				userDtoList:
					values.userDtoList?.map((item: string) => {
						return { userName: item };
					}) || null
			};
			if (data) {
				sendData.organId = data.organId;
			} else {
				sendData.organizationManagerRoleId = organizationRoleId;
			}
			onCancel();
			onCreate(sendData);
		});
	};
	return (
		<Modal
			open={open}
			onCancel={onCancel}
			onOk={onOk}
			title={data ? '编辑' : '新增'}
		>
			<Form form={form} {...formItemLayout618} labelAlign="left">
				<Form.Item
					name="name"
					label="组织名称"
					rules={[
						{ required: true, message: '请输入组织名称' },
						{
							pattern: new RegExp(pattern.organizationName),
							message:
								'由中文、大小字母、数字和特殊字符“_.-”组成且长度不超过20个字符'
						}
					]}
				>
					<Input placeholder="请输入组织名称" />
				</Form.Item>
				<Form.Item name="description" label="描述">
					<Input placeholder="请输入描述" />
				</Form.Item>
				<Form.Item name="userDtoList" label="绑定组织管理员">
					<Select
						placeholder="请选择管理员"
						dropdownMatchSelectWidth={false}
						allowClear={true}
						style={{ width: '100%' }}
						showSearch
						mode="multiple"
						// filterOption={(input, option) =>
						// 	((option?.label ?? '') as string).includes(input)
						// }
						// filterSort={(optionA, optionB) =>
						// 	((optionA?.label ?? '') as string)
						// 		.toLowerCase()
						// 		.localeCompare(
						// 			(
						// 				(optionB?.label ?? '') as string
						// 			).toLowerCase()
						// 		)
						// }
					>
						{users.map((item: UserItem) => {
							return (
								<Select.Option
									value={item.userName}
									key={item.id}
								>
									{item.aliasName}
								</Select.Option>
							);
						})}
					</Select>
				</Form.Item>
			</Form>
		</Modal>
	);
}

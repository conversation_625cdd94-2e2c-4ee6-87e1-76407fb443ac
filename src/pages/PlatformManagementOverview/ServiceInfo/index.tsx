import * as React from 'react';
import { useState, useEffect } from 'react';
import { useHistory } from 'react-router';
import HomeCard from '@/components/HomeCard';
import { Popover, Empty, Badge, Spin } from 'antd';

import storage from '@/utils/storage';
import { api } from '@/api.json';
import otherColor from '@/assets/images/nodata.svg';
import { briefInfoProps } from '../index.d';
import { getWorkspaceMiddleware } from '@/services/workspace';

const ServiceInfo = (): JSX.Element => {
	const history = useHistory();
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const [spinning, setSpinning] = useState<boolean>(false);
	const [briefInfoList, setBriefInfoList] = useState<any>([]);

	useEffect(() => {
		setSpinning(true);
		getWorkspaceMiddleware({ organId, projectId }).then((res) => {
			setSpinning(false);
			if (res.success) {
				setBriefInfoList(res.data);
			}
		});
	}, [organId, projectId]);

	return (
		<HomeCard
			title="服务信息"
			style={{
				height: '172px',
				width: '100%',
				marginBottom: '16px'
			}}
		>
			<Spin spinning={spinning}>
				<div className="serve-info">
					{briefInfoList?.length ? (
						briefInfoList.map((item: briefInfoProps) => {
							return (
								<div
									className="info-item"
									key={Math.random() + ''}
									onClick={() => {
										history.push(
											`/project/${item.middlewareCategory}/${item.type}/${item.aliasName}`
										);
									}}
								>
									<div className="info-img">
										<img
											height={40}
											width={40}
											src={
												item.imagePath
													? `${api}/images/middleware/${item.imagePath}`
													: otherColor
											}
										/>
										{item.error !== 0 ? (
											<Popover content={'异常服务数'}>
												<span className="err-count">
													{item.error}
												</span>
											</Popover>
										) : null}
									</div>
									<p className="info-name">{item.type}</p>
									<p className="info-count">
										{item?.running ? (
											<Badge
												status="success"
												text={`${item?.running}个`}
												style={{ marginRight: 12 }}
											/>
										) : null}
										{item?.error ? (
											<Badge
												status="error"
												text={`${item?.error}个`}
												style={{ marginRight: 12 }}
											/>
										) : null}
										{item?.warning ? (
											<Badge
												status="warning"
												text={`${item?.warning}个`}
											/>
										) : null}
									</p>
								</div>
							);
						})
					) : (
						<Empty
							style={{
								width: '100%',
								padding: 0
							}}
							image={Empty.PRESENTED_IMAGE_SIMPLE}
							description={
								<div>暂无服务，请前往服务列表发布</div>
							}
						/>
					)}
				</div>
			</Spin>
		</HomeCard>
	);
};

export default ServiceInfo;

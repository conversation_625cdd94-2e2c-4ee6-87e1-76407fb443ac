export interface briefInfoProps {
	chartName: string;
	errServiceNum: number;
	serviceNum: number;
	name: string;
	aliasName: string;
	imagePath: string;
	error?: number;
	running?: number;
	warning?: number;
	type?: string;
	middlewareCategory?: string;
}

export interface ServiceInfoProps {
	briefInfoList: briefInfoProps[];
}

export interface ClusterResourcesOverviewProps {
	isOrgan?: boolean;
	isProject?: boolean;
}

export interface BackupServiceProps {
	isOrgan?: boolean;
	isProject?: boolean;
}

export interface AgentManagementProps {
	isOrgan?: boolean;
	isProject?: boolean;
	[propName: string]: any;
}

export interface AlertRecordProps {
	isOrgan?: boolean;
	isProject?: boolean;
	[propName: string]: any;
}

export interface operatorListProps {
	name: string;
	clusterId: string;
	clusterName: string;
	status: number;
}

export interface StorageServiceProps {
	isOrgan?: boolean;
	isProject?: boolean;
}

export interface OrganAndProjectInfoProps {
	isOrgan?: boolean;
	isProject?: boolean;
}

export interface BackupServiceProps {
	isOrgan?: boolean;
	isProject?: boolean;
}

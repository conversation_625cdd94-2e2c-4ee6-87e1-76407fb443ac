import * as React from 'react';
import { useState, useEffect } from 'react';
import { Pie } from '@ant-design/plots';
import { Empty } from 'antd';
import {
	getOverviewStoragePie,
	getOverviewStorageRank
} from '@/services/platManagementOverview';
import storage from '@/utils/storage';
import { StorageServiceProps } from '../index.d';
import {
	getOrganStoragePie,
	getOrganStorageRank
} from '@/services/organization';
import {
	getProjectStoragePie,
	getProjectStorageRank
} from '@/services/project';

const StorageService = (props: StorageServiceProps): JSX.Element => {
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const { isOrgan, isProject } = props;
	const [data, setData] = useState<any[]>([]);
	const [list, setList] = useState<any[]>([]);
	const [totalCount, setTotalCount] = useState<number>(0);

	useEffect(() => {
		if (!isOrgan && !isProject) {
			getOverviewStoragePie().then((res) => {
				if (res.success) {
					const sum = res.data.reduce(
						(accumulator: any, currentValue: any) => {
							return accumulator + parseInt(currentValue.count);
						},
						0
					);
					setTotalCount(sum);
					res.data && res.data.length
						? setData(
								res.data.map((item: any) => {
									return {
										name: item.type,
										value: +item.count
									};
								})
						  )
						: setData([{ name: 'CSA-LVM', value: 0 }]);
				}
			});
			getOverviewStorageRank().then((res) => {
				if (res.success) {
					setList(res.data);
				}
			});
		}
		if (isOrgan) {
			getOrganStoragePie({ organId }).then((res) => {
				if (res.success) {
					const sum = res.data.reduce(
						(accumulator: any, currentValue: any) => {
							return accumulator + parseInt(currentValue.count);
						},
						0
					);
					setTotalCount(sum);
					res.data && res.data.length
						? setData(
								res.data.map((item: any) => {
									return {
										name: item.type,
										value: +item.count
									};
								})
						  )
						: setData([{ name: 'CSA-LVM', value: 0 }]);
				}
			});
			getOrganStorageRank({ organId }).then((res) => {
				if (res.success) {
					setList(res.data);
				}
			});
		}
		if (isProject) {
			getProjectStoragePie({ organId, projectId }).then((res) => {
				if (res.success) {
					const sum = res.data.reduce(
						(accumulator: any, currentValue: any) => {
							return accumulator + parseInt(currentValue.count);
						},
						0
					);
					setTotalCount(sum);
					res.data && res.data.length
						? setData(
								res.data.map((item: any) => {
									return {
										name: item.type,
										value: +item.count
									};
								})
						  )
						: setData([{ name: 'CSA-LVM', value: 0 }]);
				}
			});
			getProjectStorageRank({ organId, projectId }).then((res) => {
				if (res.success) {
					setList(res.data);
				}
			});
		}
	}, [organId, projectId]);

	const storePieConfig: any = {
		data,
		angleField: 'value',
		colorField: 'name',
		radius: 0.8,
		label: false,
		legend: false,
		interactions: [
			{
				type: 'element-selected'
			},
			{
				type: 'element-active'
			}
		],
		color: ['#226EE6', '#CAE5FF', '#EFF8FF99'],
		pieStyle: {
			shadowColor: 'rgba(34, 110, 230, 0.2)',
			shadowBlur: 17,
			shadowOffsetX: 2,
			shadowOffsetY: 2
		},
		tooltip: {
			formatter(data: any) {
				const { name, value } = data;
				return {
					name,
					value: value + '个'
				};
			},
			showTitle: true,
			title: '存储类型'
		}
	};

	return (
		<div
			className="bottom-card"
			style={{ height: isProject ? '330px' : '450px' }}
		>
			<div className="header">
				<h3>存储服务 单位:Gi</h3>
			</div>
			<div className={`display-flex ${isOrgan ? 'flex-column' : ''}`}>
				<div
					style={{
						width: isOrgan ? '100%' : '40%',
						height: isOrgan ? 150 : 300
					}}
				>
					<Pie {...storePieConfig} />
					<div
						style={{
							textAlign: 'center',
							position: 'relative',
							top: isOrgan ? '-10px' : '0px'
						}}
					>
						<strong style={{ color: '#000' }}>存储服务总数</strong>
						<span
							style={{
								color: '#226ee7',
								fontSize: '22px',
								margin: '0 16px'
							}}
						>
							{totalCount}
						</span>
						个
					</div>
				</div>
				<div
					className="usage-ranking"
					style={{
						width: isOrgan ? '100%' : '60%',
						height: isOrgan ? '220px' : '100%',
						overflowY: isOrgan ? 'auto' : 'hidden',
						marginTop: '16px'
					}}
				>
					<div className="header">
						<h3>存储服务用量排行 Top6</h3>
					</div>
					{list.length ? (
						list?.map((item: any, index: number) => {
							return (
								<div className={'content active'} key={index}>
									<div className="item" key={index}>
										<div
											className={`ranking${
												index + 1 <= 3 ? index + 1 : ''
											}`}
										>
											{index + 1}
										</div>
										<div className="iCnt">
											<div className="item-name">
												<div className="ellipse"></div>
												<p title={item.name}>
													{item.name}
												</p>
											</div>
											<div className="item-content">
												<span style={{ width: 45 }}>
													数量：
												</span>
												<p
													title={
														item?.storage?.request
													}
													style={{
														textAlign: 'left',
														width: 60,
														marginRight: 0
													}}
												>
													{item?.storage?.request}
												</p>
												<span>所属集群:</span>
												<p
													title={
														item.clusterNickName ||
														item.clusterName
													}
												>
													{item.clusterNickName ||
														item.clusterName ||
														'rfsafasdgsag'}
												</p>
											</div>
										</div>
									</div>
								</div>
							);
						})
					) : (
						<Empty
							imageStyle={{
								height: 52,
								marginTop: 50
							}}
						/>
					)}
				</div>
			</div>
		</div>
	);
};

export default StorageService;

import * as React from 'react';
import { useState, useEffect } from 'react';
import { useHistory } from 'react-router';
import { connect } from 'react-redux';
import HomeCard from '@/components/HomeCard';
import { Table } from 'antd';
import {
	LoadingOutlined,
	CheckCircleFilled,
	MinusCircleFilled,
	ExclamationCircleFilled,
	ExclamationCircleOutlined
} from '@ant-design/icons';

import * as echarts from 'echarts';
import { StoreState } from '@/types';
import { getPieOption } from '@/utils/echartsOption';

import { operatorListProps } from '../index.d';
import { getOperatorStatus } from '@/services/platManagementOverview';
import storage from '@/utils/storage';

const statusMap: any = {
	安装中: 0,
	运行正常: 1,
	待安装: 2,
	不可用: 3,
	运行异常: 4
};
const ControlStatus = ({
	buttonList = []
}: {
	buttonList: MenuResItem[];
}): JSX.Element => {
	const history = useHistory();
	const menu = storage.getSession('menu') || [];
	const [operatorList, setOperatorList] = useState<any[]>([]);
	const show = (code: string) =>
		buttonList.find((item: MenuResItem) => item.name === code);
	useEffect(() => {
		const id = document.getElementById('id');
		let chart: any = null,
			x: any;
		if (id) chart = echarts.init(id);
		getOperatorStatus().then((res) => {
			if (res.success) {
				setOperatorList(res.data?.operatorList);
				chart.setOption(getPieOption(res.data));
				chart.on('legendselectchanged', (obj: any) => {
					const cur_status_list: any = [];
					Object.keys(obj.selected).map((status_value) => {
						if (obj.selected[status_value]) {
							cur_status_list.push(statusMap[status_value]);
						}
					});
					x = res.data.operatorList.filter(
						(item: operatorListProps) =>
							cur_status_list.includes(item.status)
					);
					setOperatorList(x);
				});
			}
		});

		window.addEventListener('resize', () => chart.resize());
	}, []);

	const controllerRender = (value: number) => {
		switch (value) {
			case 0:
				return (
					<>
						<LoadingOutlined
							style={{
								color: '#D1D5D9',
								marginRight: '6px',
								fontSize: '12px'
							}}
						/>
						安装中
					</>
				);
			case 1:
				return (
					<>
						<CheckCircleFilled
							style={{
								color: '#52c41a',
								marginRight: '6px',
								fontSize: '12px'
							}}
						/>
						运行正常
					</>
				);
			case 2:
				return (
					<>
						<MinusCircleFilled
							style={{
								color: '#d7d7d7',
								marginRight: '6px',
								fontSize: '12px'
							}}
						/>
						待安装
					</>
				);
			case 3:
				return (
					<>
						<ExclamationCircleOutlined
							style={{
								color: '#ff4d4f',
								marginRight: '6px',
								fontSize: '12px'
							}}
						/>
						不可用
					</>
				);
			case 4:
				return (
					<>
						<ExclamationCircleFilled
							style={{
								color: '#faad14',
								marginRight: '6px',
								fontSize: '12px'
							}}
						/>
						运行异常
					</>
				);
		}
	};

	return (
		<HomeCard
			title="控制器状态"
			style={{
				height: '450px',
				margin: '16px 0'
			}}
			readMore={
				menu?.find(
					(item: any) => item.url === 'platform/marketManagement'
				) && show('overviewJumpMarket')
					? '更多'
					: ''
			}
			readMoreFn={() => history.push('/platform/marketManagement')}
		>
			<div className="control-container">
				<div
					id="id"
					style={{
						width: '100%',
						height: '40%'
					}}
				></div>
				<Table
					dataSource={operatorList}
					rowKey={(record) => `${record.name}(${record.clusterName})`}
					scroll={{ y: 180 }}
					pagination={false}
					size="small"
				>
					<Table.Column
						title="类型"
						dataIndex="name"
						render={(value: string, record: operatorListProps) => (
							<span>
								{record.name + '(' + record.clusterName + ')'}
							</span>
						)}
					/>
					<Table.Column
						title="状态"
						dataIndex="status"
						render={controllerRender}
					/>
				</Table>
			</div>
		</HomeCard>
	);
};

export default connect((state: StoreState) => ({
	buttonList: state.auth.buttonList
}))(ControlStatus);

import * as React from 'react';
import { useState, useEffect } from 'react';
import MLiquid from '../MLiquid';
import { Empty } from 'antd';
import { ClusterResourcesOverviewProps } from '../index.d';
import { getOverviewRank } from '@/services/platManagementOverview';
import { getClusters } from '@/services/common';
import { clusterType } from '@/types';
import { filterProps } from '@/utils/constant';
import storage from '@/utils/storage';
import { getOrganRank } from '@/services/organization';

const ClusterResourcesOverview = (
	props: ClusterResourcesOverviewProps
): JSX.Element => {
	const { isOrgan, isProject } = props;
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const [type, setType] = useState<string>('cpu');
	const [currentCluster, setCurrentCluster] = useState<string>('');
	const [clusterList, setClusterList] = useState<filterProps[]>([]);
	const [data, setData] = useState<any[]>([]);

	useEffect(() => {
		let sendData = {};
		if (isOrgan) {
			sendData = {
				organId
			};
		}
		if (isProject) {
			sendData = {
				organId,
				projectId
			};
		}
		getClusters({ ...sendData }).then((res) => {
			if (res.success) {
				setCurrentCluster(res.data?.[0]?.id);
				setClusterList(
					res.data.map((item: clusterType) => {
						return {
							label: item.nickname || item.name,
							value: item.id
						};
					})
				);
			}
		});
	}, [organId, projectId]);

	useEffect(() => {
		if (currentCluster) {
			if (!isOrgan && !isProject) {
				getOverviewRank({ clusterId: currentCluster, type }).then(
					(res) => {
						if (res.success) {
							setData(res.data);
						}
					}
				);
			}
			if (isOrgan) {
				getOrganRank({ clusterId: currentCluster, organId, type }).then(
					(res) => {
						if (res.success) {
							setData(res.data);
						}
					}
				);
			}
		}
	}, [currentCluster, type, organId]);

	return (
		<div
			className="top-card"
			style={{
				height: isOrgan ? '520px' : isProject ? '240px' : '500px'
			}}
		>
			<MLiquid
				clusterId={currentCluster}
				clusterList={clusterList}
				organId={organId}
				projectId={projectId}
				isOrgan={isOrgan}
				isProject={isProject}
				setCurrentCluster={setCurrentCluster}
				pos="s"
			/>
			{!isProject ? (
				<div className="usage-ranking">
					<div className="header" style={{ marginTop: 0 }}>
						<h3>资源分配情况项目排行 Top6</h3>
						<div className="label">
							<ul className="tab-wrap">
								<li
									className={
										'tab ' +
										(type === 'cpu' ? 'active' : '')
									}
									onClick={() => {
										setType('cpu');
									}}
									key={1}
								>
									<span className="tab-text">CPU (核)</span>
								</li>
								<li
									className={
										'tab ' +
										(type === 'memory' ? 'active' : '')
									}
									onClick={() => {
										setType('memory');
									}}
									key={2}
								>
									<span className="tab-text">内存 (Gi)</span>
								</li>
							</ul>
						</div>
					</div>
					{data.length ? (
						data?.map((item: any, index: number) => {
							return (
								<div className="content active" key={index}>
									<div className="item">
										<div
											className={`ranking${
												index + 1 <= 3 ? index + 1 : ''
											}`}
										>
											{index + 1}
										</div>
										<div className="iCnt">
											<div className="item-name">
												<div className="ellipse"></div>
												<p title={item?.projectName}>
													{item?.projectName}
												</p>
											</div>
											<div className="item-content">
												<span>用量:</span>

												<p
													title={
														item.quotaList?.[0][
															type
														]?.request
													}
												>
													{item.quotaList?.[0][
														type
													]?.request?.toFixed(1)}
												</p>
											</div>
										</div>
									</div>
								</div>
							);
						})
					) : (
						<Empty
							imageStyle={{
								height: 52
							}}
							style={{ marginTop: 24 }}
						/>
					)}
				</div>
			) : null}
		</div>
	);
};

export default ClusterResourcesOverview;

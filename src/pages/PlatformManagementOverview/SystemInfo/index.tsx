import * as React from 'react';
import { useState, useEffect } from 'react';
import HomeCard from '@/components/HomeCard';
import { getOverviewVersion } from '@/services/platManagementOverview';

const SystemInfo = (): JSX.Element => {
	const [version, setVersion] = useState<string>();

	useEffect(() => {
		getOverviewVersion().then((res) => {
			if (res.success) {
				setVersion(res.data?.platform);
			}
		});
	}, []);

	return (
		<HomeCard title="系统信息" style={{ height: '150px', width: '100%' }}>
			<div className="system-info">
				<div className="version">Zeus {version}</div>
				<div>当前系统版本</div>
				<div>Powered by zeus</div>
			</div>
		</HomeCard>
	);
};

export default SystemInfo;

import * as React from 'react';
import { useState, useEffect } from 'react';
import HomeCard from '@/components/HomeCard';
import UserBG from '@/assets/images/user-bg.png';

import { getOrganizationDetail } from '@/services/organization';
import { getProjectDetail } from '@/services/project';
import storage from '@/utils/storage';
import { OrganAndProjectInfoProps } from '../index.d';
import './index.less';

const OrganAndProjectInfo = (props: OrganAndProjectInfoProps): JSX.Element => {
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const { isOrgan, isProject } = props;
	const [data, setData] = useState<any>();

	useEffect(() => {
		if (isOrgan) {
			getOrganizationDetail({ organId }).then((res) => {
				if (res.success) {
					setData(res.data);
				}
			});
		}
	}, [organId, projectId]);

	useEffect(() => {
		if (isProject) {
			getProjectDetail({ organId, projectId, key: '' }).then((res) => {
				if (res.success) {
					setData(res.data);
				}
			});
		}
	}, [organId, projectId]);

	return (
		<HomeCard
			title={isOrgan ? '组织概览' : '项目概览'}
			style={{
				height: '170px',
				width: '100%',
				background: `url(${UserBG}) no-repeat 100% 0 #ffffff`
			}}
		>
			<div className="display-flex organ-project-info">
				{isProject ? (
					<>
						<div>
							<div className="info-number">
								{data?.namespaceCount || 0}
							</div>
							<div className="info-text">命名空间</div>
						</div>
						<div className="info-line"></div>
					</>
				) : null}
				<div>
					<div className="info-number">
						{isOrgan
							? data?.userCount || 0
							: data?.memberCount || 0}
					</div>
					<div className="info-text">
						{isProject ? '项目成员' : '成员数'}
					</div>
				</div>
				<div className="info-line"></div>
				<div>
					<div className="info-number">
						{isOrgan
							? data?.projectCount || 0
							: data?.backupServerList?.length || 0}
					</div>
					<div className="info-text">
						{isProject ? '备份服务器数' : '项目数'}
					</div>
				</div>
			</div>
			<div className="organ-project-description">
				<div className="info-text">描述 </div>
				<div>{data?.description || '/'}</div>
			</div>
		</HomeCard>
	);
};

export default OrganAndProjectInfo;

import * as React from 'react';
import { useState, useEffect } from 'react';
import { Carousel, Empty, Select } from 'antd';
import { Pie } from '@ant-design/plots';
import { AgentManagementProps } from '../index.d';
import {
	getOverviewAgentPie,
	getOverviewAgentRank
} from '@/services/platManagementOverview';
import { getOrganAgentPie, getOrganAgentRank } from '@/services/organization';
import { getProjectAgentPie, getProjectAgentRank } from '@/services/project';
import storage from '@/utils/storage';
import { agentStatus } from '@/utils/enum';
import { getClusters } from '@/services/common';
import { clusterType } from '@/types';
import { filterProps } from '@/utils/constant';

const AgentManagement = (props: AgentManagementProps): JSX.Element => {
	const { isOrgan, isProject } = props;
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const [data, setData] = useState<any[]>([
		{ name: '运行中', num: 0 },
		{ name: '离线', num: 0 }
	]);
	const [allData, setAllData] = useState<any[]>([
		{ name: '运行中', num: 0 },
		{ name: '离线', num: 0 }
	]);
	const [list, setList] = useState<any>([]);
	const [currentCluster, setCurrentCluster] = useState<string>('');
	const [clusterList, setClusterList] = useState<filterProps[]>([]);

	useEffect(() => {
		let sendData = {};
		if (isOrgan) {
			sendData = {
				organId
			};
		}
		if (isProject) {
			sendData = {
				organId,
				projectId
			};
		}
		getClusters({ ...sendData }).then((res) => {
			if (res.success) {
				setCurrentCluster(res.data?.[0]?.id);
				setClusterList(
					res.data.map((item: clusterType) => {
						return {
							label: item.nickname || item.name,
							value: item.id
						};
					})
				);
			}
		});
	}, [organId, projectId]);

	useEffect(() => {
		if (!isOrgan && !isProject) {
			currentCluster &&
				getOverviewAgentPie({ clusterId: currentCluster }).then(
					(res) => {
						if (res.success) {
							res.data &&
								res.data.length &&
								setData(
									res.data.map((item: any) => ({
										name: agentStatus[item.phase],
										num: Number(item.count)
									}))
								);
						}
					}
				);
			getOverviewAgentRank().then((res) => {
				if (res.success) {
					setList(res.data);
					const agentData = res.data
						?.map((item: any) => item.agentDTOList)
						?.flat();
					setAllData([
						{
							name: '运行中',
							num: agentData.filter(
								(item: any) => item.phase === 'Online'
							).length
						},
						{
							name: '离线',
							num: agentData.filter(
								(item: any) => item.phase === 'Offline'
							).length
						}
					]);
				}
			});
		}
		if (isOrgan) {
			currentCluster &&
				getOrganAgentPie({ clusterId: currentCluster, organId }).then(
					(res) => {
						if (res.success) {
							res.data &&
								res.data.length &&
								setData(
									res.data.map((item: any) => ({
										name: agentStatus[item.phase],
										num: Number(item.count)
									}))
								);
						}
					}
				);
			getOrganAgentRank({ organId }).then((res) => {
				if (res.success) {
					setList(res.data);
					const agentData = res.data
						?.map((item: any) => item.agentDTOList)
						?.flat();
					setAllData([
						{
							name: '运行中',
							num: agentData.filter(
								(item: any) => item.phase === 'Online'
							).length
						},
						{
							name: '离线',
							num: agentData.filter(
								(item: any) => item.phase === 'Offline'
							).length
						}
					]);
				}
			});
		}
		if (isProject) {
			currentCluster &&
				getProjectAgentPie({
					clusterId: currentCluster,
					organId,
					projectId
				}).then((res) => {
					if (res.success) {
						res.data &&
							res.data.length &&
							setData(
								res.data.map((item: any) => ({
									name: agentStatus[item.phase],
									num: Number(item.count)
								}))
							);
					}
				});
			getProjectAgentRank({ organId, projectId }).then((res) => {
				if (res.success) {
					setList(res.data);
					const agentData = res.data
						?.map((item: any) => item.agentDTOList)
						?.flat();
					setAllData([
						{
							name: '运行中',
							num: agentData.filter(
								(item: any) => item.phase === 'Online'
							).length
						},
						{
							name: '离线',
							num: agentData.filter(
								(item: any) => item.phase === 'Offline'
							).length
						}
					]);
				}
			});
		}
	}, [organId, projectId, currentCluster]);

	const getPieCount = (pielist: any) => {
		if (!Array.isArray(pielist) || pielist.length === 0) return 0;
		let count = 0;
		pielist.forEach((item) => {
			count += item.num;
		});
		return count;
	};

	const storePieConfig: any = {
		width: 130,
		height: 130,
		data: allData,
		angleField: 'num',
		colorField: 'name',
		radius: 0.9,
		label: false,
		legend: false,
		interactions: [
			{
				type: 'element-selected'
			},
			{
				type: 'element-active'
			}
		],
		color: ['#226EE6', '#4C92F5', '#78B5FF'],
		pieStyle: {
			shadowColor: 'rgba(34, 110, 230, 0.2)',
			shadowBlur: 17,
			shadowOffsetX: 2,
			shadowOffsetY: 2
		}
	};

	const privateStorePieConfig: any = {
		width: 125,
		height: 125,
		data,
		angleField: 'num',
		colorField: 'name',
		radius: 1,
		innerRadius: 0.8,
		appendPadding: [10, 0, 0, 0],
		label: false,
		legend: false,
		interactions: [
			{
				type: 'element-selected'
			},
			{
				type: 'element-active'
			}
		],
		statistic: {
			title: {
				style: {
					fontFamily: 'PingFang SC',
					fontStyle: 'normal',
					fontWeight: 500,
					whiteSpace: 'normal',
					fontSize: 8,
					color: 'rgba(51, 51, 51, 0.6)'
				},
				content: '管控数量',
				offsetY: 25
			},
			content: {
				style: {
					whiteSpace: 'pre-wrap',
					overflow: 'hidden',
					textOverflow: 'ellipsis',
					fontFamily: 'D-DIN',
					fontStyle: 'normal',
					fontWeight: 500,
					fontSize: 32,
					lineHeight: 1,
					color: '#333333',
					borderBottom: ' 0.5px solid rgba(51, 51, 51, 0.08)',
					paddingBottom: 2,
					width: 40
				},
				offsetY: -35,
				content: getPieCount(data) + ''
			}
		},
		color: ['#226EE6', '#4C92F5', '#78B5FF']
	};

	const publicStorePieConfig: any = {
		width: 97,
		height: 97,
		data,
		angleField: 'num',
		colorField: 'name',
		radius: 1,
		innerRadius: 0.8,
		label: false,
		legend: false,
		interactions: [
			{
				type: 'element-selected'
			},
			{
				type: 'element-active'
			}
		],
		statistic: {
			title: {
				style: {
					fontFamily: 'PingFang SC',
					fontStyle: 'normal',
					fontWeight: 500,
					fontSize: 10,
					color: 'rgba(51, 51, 51, 0.6)'
				},
				content: '公有仓库总数',
				offsetY: 25
			},
			content: {
				style: {
					whiteSpace: 'pre-wrap',
					overflow: 'hidden',
					textOverflow: 'ellipsis',
					fontFamily: 'D-DIN',
					fontStyle: 'normal',
					fontWeight: 500,
					fontSize: 32,
					lineHeight: 1,
					color: '#333333',
					borderBottom: ' 0.5px solid rgba(51, 51, 51, 0.08)',
					paddingBottom: 2,
					width: 40
				},
				offsetY: -25,
				content: getPieCount(data) + ''
			}
		},
		color: ['#226EE6', '#4C92F5', '#78B5FF']
	};

	return (
		<div className="center-card">
			<div className="header">
				<h3>客户端管理</h3>
				<Select
					options={clusterList}
					value={currentCluster}
					onChange={(value) => setCurrentCluster(value)}
					style={{ width: 160 }}
				/>
			</div>
			<div className="page-right-buttom">
				<div className="buttom">
					<div className="store-pie">
						<div>
							<Pie {...storePieConfig} />
						</div>
						<div style={{ alignSelf: 'center' }}>
							{allData?.map((item: any, index: number) => {
								return (
									<div className="pieLegend" key={index}>
										<h3>{item.name}</h3>
										<p>{item.num}</p>
									</div>
								);
							})}
						</div>
					</div>

					<div className="line"></div>

					<Carousel dots={{ className: 'dots' }}>
						<div
							className="pie-architecture"
							style={{ width: '220px' }}
						>
							<Pie {...privateStorePieConfig} />
							<div
								style={{ alignSelf: 'center' }}
								className="pieInfo"
							>
								{data?.map((item: any, index: number) => {
									return (
										<div className="pieLegend" key={index}>
											<h3>{item.name}</h3>
											<p
												style={{
													color:
														index == 1
															? '#77B5FE'
															: '#226EE6'
												}}
											>
												{item.num}
											</p>
										</div>
									);
								})}
							</div>
						</div>
						{/* <div className="pie-architecture ">
							<Pie {...publicStorePieConfig} />
							<div
								style={{ alignSelf: 'center' }}
								className="pieInfo"
							>
								{data?.map((item: any, index: number) => {
									return (
										<div className="pieLegend" key={index}>
											<h3>{item.name}</h3>
											<p
												style={{
													color:
														index == 1
															? '#77B5FE'
															: '#226EE6'
												}}
											>
												{item.num}
											</p>
										</div>
									);
								})}
							</div>
						</div> */}
					</Carousel>
				</div>
			</div>
			<div className="usage-ranking">
				<div className="header">
					<h3>集群控制器管控客户端数量排行 Top6</h3>
				</div>
				{list.length ? (
					list?.map((item: any, index: number) => {
						return (
							<div className={'content active'} key={index}>
								<div className="item" key={index}>
									<div
										className={`ranking${
											index + 1 <= 3 ? index + 1 : ''
										}`}
									>
										{index + 1}
									</div>
									<div className="iCnt">
										<div className="item-name">
											<div className="ellipse"></div>
											<p title={item?.clusterNickName}>
												{item?.clusterNickName}
											</p>
										</div>
										<div className="item-content">
											<span>管控量: </span>

											<p
												title={
													item?.agentDTOList?.length
												}
											>
												{item?.agentDTOList?.length}
											</p>
										</div>
									</div>
								</div>
							</div>
						);
					})
				) : (
					<Empty
						imageStyle={{
							height: 52,
							marginTop: 60
						}}
					/>
				)}
			</div>
		</div>
	);
};

export default AgentManagement;

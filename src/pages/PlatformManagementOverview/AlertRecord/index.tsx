import * as React from 'react';
import { useState, useEffect } from 'react';
import HomeCard from '@/components/HomeCard';
import { Radio, Pagination } from 'antd';
import AlarmTimeLine from '@/components/AlarmTimeline';

import { getOverviewAlert } from '@/services/platManagementOverview';
import { eventDataProps } from '@/types/comment';
import { radioList } from '@/utils/const';
import storage from '@/utils/storage';
import { AlertRecordProps } from '../index.d';
import { getProjectAlert } from '@/services/project';

const RadioGroup = Radio.Group;
const AlertRecord = (props: AlertRecordProps): JSX.Element => {
	const { isProject } = props;
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const [level, setLevel] = useState<string>('');
	const [current, setCurrent] = useState<number>(1);
	const [total, setTotal] = useState<number>(10); // 总数
	const [eventData, setEventData] = useState<eventDataProps[]>([]);

	const getData = (sendData: any) => {
		if (!isProject) {
			getOverviewAlert(sendData).then((res) => {
				if (res.success) {
					setCurrent(
						res.data
							? res.data?.alertPageInfo?.list.length === 0
								? 0
								: res.data?.alertPageInfo?.pageNum
							: 1
					);
					setEventData(res.data ? res.data?.alertPageInfo?.list : []);
					setTotal(res.data ? res.data?.alertPageInfo?.total : 0);
				}
			});
		} else {
			getProjectAlert({ ...sendData, organId, projectId }).then((res) => {
				if (res.success) {
					setCurrent(
						res.data
							? res.data?.list.length === 0
								? 0
								: res.data?.pageNum
							: 1
					);
					setEventData(res.data ? res.data?.list : []);
					setTotal(res.data ? res.data?.total : 0);
				}
			});
		}
	};

	const paginationChange = (current: number) => {
		setCurrent(current);
		const alertData = {
			current,
			level
		};
		getData(alertData);
	};

	useEffect(() => {
		const alertData = {
			current,
			level
		};
		getData(alertData);
	}, [level]);

	return (
		<HomeCard
			title="告警事件（全平台）"
			style={{
				height: isProject ? '517px' : '450px',
				width: '100%',
				marginTop: '16px'
			}}
		>
			<RadioGroup
				options={radioList}
				onChange={(e) => setLevel(e.target.value)}
				value={level}
				optionType="button"
				size="middle"
				style={{ marginTop: 16 }}
			/>
			<AlarmTimeLine
				list={eventData}
				style={{
					marginTop: 16,
					height: 'calc(100% - 110px)'
				}}
				type="platform"
			/>
			<div
				style={{
					display: 'flex',
					alignItems: 'center',
					justifyContent: 'space-between',
					marginTop: '8px'
				}}
			>
				<span>总数：{total}</span>
				<Pagination
					style={{ float: 'right' }}
					current={current}
					size="small"
					onChange={paginationChange}
					total={total}
					simple
				/>
			</div>
		</HomeCard>
	);
};

export default AlertRecord;

import React, { useState, useEffect, memo } from 'react';
import { Select } from 'antd';

import { getOverviewResources } from '@/services/platManagementOverview';
import storage from '@/utils/storage';
import { getOrganResources } from '@/services/organization';
import { getProjectResources } from '@/services/project';
import './index.less';

function MLiquid(props: any) {
	const { isOrgan, isProject, organId, projectId } = props;
	const { clusterId, clusterList, setCurrentCluster, pos } = props;
	const [data, setData] = useState<any>();
	const getname = (p: any) => {
		return p > 70 ? 'r' : p > 40 ? 'y' : 'b';
	};

	const getData = () => {
		if (!clusterId) return;
		if (!isOrgan && !isProject) {
			getOverviewResources({ clusterId }).then((res) => {
				if (res.success) {
					setData(res.data);
				}
			});
		}
		if (isOrgan) {
			getOrganResources({ clusterId, organId }).then((res) => {
				if (res.success) {
					setData(res.data);
				}
			});
		}
		if (isProject) {
			getProjectResources({ clusterId, organId, projectId }).then(
				(res) => {
					if (res.success) {
						setData(res.data);
					}
				}
			);
		}
	};

	useEffect(() => {
		getData();
	}, [clusterId, organId, projectId]);

	return (
		<>
			<div className="upper-part2">
				<div className="header">
					<h3>
						{isOrgan ? '组织内' : isProject ? '项目内' : ''}
						集群资源概览
					</h3>
					<Select
						options={clusterList}
						value={clusterId}
						onChange={(value) => setCurrentCluster(value)}
						style={{ width: 160 }}
					/>
				</div>
				{/* 资源使用率 */}
				<div
					className={`resource-utilization ${pos} ${
						isProject ? 'border-none' : ''
					}`}
				>
					<div className="content">
						<div
							className={`${getname(
								parseFloat(data?.cpuRequestPercent || 0)
							)}`}
						>
							<div className="Liquid">
								<p className="percent">
									<sub>{data?.cpuRequestPercent || '0%'}</sub>
								</p>
								<div className="type">CPU</div>
								<div className="unit">核</div>
							</div>
							<div className="left">
								<div className="pieLegend">
									<h3>{`${
										isProject
											? '项目'
											: isOrgan
											? '组织'
											: '集群'
									}总量`}</h3>
									<p>{data?.totalCpu?.toFixed(2)}</p>
								</div>
								<div className="pieLegend">
									<h3>已使用</h3>
									<p>{data?.usedCpu?.toFixed(2)}</p>
								</div>
								<div className="pieLegend">
									<h3>已分配</h3>
									<p>{data?.requestCpu?.toFixed(2)}</p>
								</div>
							</div>
						</div>
						<div
							className={`${getname(
								parseFloat(data?.memoryRequestPercent || 0)
							)}`}
						>
							<div className="Liquid">
								<p className="percent">
									<sub>
										{data?.memoryRequestPercent || '0%'}
									</sub>
								</p>
								<div className="type">内存</div>
								<div className="unit">Gi</div>
							</div>
							<div className="left">
								<div className="pieLegend">
									<h3>{`${
										isProject
											? '项目'
											: isOrgan
											? '组织'
											: '集群'
									}总量`}</h3>
									<p>{data?.totalMemory?.toFixed(2)}</p>
								</div>
								<div className="pieLegend">
									<h3>已使用</h3>
									<p>{data?.usedMemory?.toFixed(2)}</p>
								</div>
								<div className="pieLegend">
									<h3>已分配</h3>
									<p>{data?.requestMemory?.toFixed(2)}</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</>
	);
}

export default memo(MLiquid);

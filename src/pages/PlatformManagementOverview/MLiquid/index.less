//集群实际资源使用率
.resource-utilization {
	height: 200px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	width: 96%;
	margin-left: 2%;
	border-bottom: 1px solid rgba(51, 51, 51, 0.05);

	&.border-none {
		border: none;
	}

	.b {
		.Liquid {
			background-image: url(../../../assets//images//overview/1.png);
		}
		.percent,
		.left p {
			color: #226ee6;
		}
	}
	.y {
		.Liquid {
			background-image: url(../../../assets//images//overview/2.png);
		}
		.percent,
		.left p {
			color: #f5a623;
		}
	}
	.r {
		.Liquid {
			background-image: url(../../../assets//images//overview/3.png);
		}
		.percent,
		.left p {
			color: #f5212d;
		}
	}

	.content {
		display: flex !important;
		position: relative;
		height: 200px;
		sub {
			font-size: 16px;
		}
		& > div {
			width: 50%;
			display: flex;
			justify-content: center;
		}

		// .cpu {
		//     display: flex;
		//     width: 160px;
		//     height: 160px;
		//     background-image: url(./image/cpu.webp);
		// }

		.Liquid {
			display: flex;
			width: 160px;
			height: 160px;
			background-repeat: no-repeat;
			align-self: center;
			position: relative;

			.percent {
				margin-top: 26px;
				margin-left: 35px;
				font-family: 'D-DIN';
				font-style: normal;
				font-weight: 700;
				font-size: 40px;
			}
			.type {
				position: absolute;
				width: 100px;
				height: 20px;
				font-weight: 600;
				font-size: 14px;
				font-family: 'PingFang SC';
				bottom: 22px;
				left: 35px;
				letter-spacing: 0.02em;
				color: white;
			}
			.unit {
				position: absolute;
				top: 34px;
				right: 25px;
				width: 11px;
				height: 14px;
				font-family: 'PingFang SC';
				font-weight: 500;
				font-size: 10px;
				line-height: 14px;
				letter-spacing: 0.02em;
				color: #c8c8c8;
			}
		}

		.left {
			align-self: center;
			margin-left: 30px;

			h2 {
				font-family: 'PingFang SC';
				font-style: normal;
				font-weight: 400;
				font-size: 12px;
				line-height: 17px;
				color: #333333;
			}

			p {
				font-family: 'D-DIN';
				font-style: normal;
				font-weight: 700;
				font-size: 18px;
				/* identical to box height */
				text-shadow: 0px 4px 4px rgba(34, 110, 230, 0.15);
			}
		}
		.pieLegend {
			padding: 5px 0;
			h3,
			p {
				line-height: 20px;
				max-width: 100px;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}

		.pieLegend:nth-child(n + 2) {
			//n+2表示从第二个开始
			border-top: 1px dashed rgba(51, 51, 51, 0.08);
		}
	}
	.dots {
		.slick-active {
			width: 5px !important;
		}
		li {
			width: 5px !important;
			margin: 0 5px !important;
		}
	}
	.dots button {
		background: #226ee6 !important;
		color: #ffffff !important;
		width: 5px !important;
		height: 5px !important;
		border: none !important;
		border-radius: 50% !important;
	}
}
.upper-part2 {
	&:hover {
		.header {
			.refresh {
				display: flex !important;
			}
		}
	}
}

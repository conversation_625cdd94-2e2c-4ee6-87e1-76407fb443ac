import * as React from 'react';
import { useState, useEffect } from 'react';
import SystemInfo from './SystemInfo';
import BackupService from './BackupService';
import ControlStatus from './ControlStatus';
import ClusterResourcesOverview from './ClusterResourcesOverview';
import AgentManagement from './AgentManagement';
import AlarmRecord from './AlertRecord';
import StorageService from './StorageService';

import './index.less';

function PlatformManagementOverview(): JSX.Element {
	return (
		<div className="platform-management-overview">
			<div className="overview-left">
				<ClusterResourcesOverview />
				<AgentManagement />
				<StorageService />
			</div>
			<div className="overview-right">
				<SystemInfo />
				<BackupService />
				<ControlStatus />
				<AlarmRecord />
			</div>
		</div>
	);
}

export default PlatformManagementOverview;

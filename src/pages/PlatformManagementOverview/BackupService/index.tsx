import * as React from 'react';
import { useState, useEffect } from 'react';
import { Empty } from 'antd';

import { getOverviewServer } from '@/services/platManagementOverview';
import { BackupServiceProps } from '../index.d';
import { getProjectServer } from '@/services/project';
import storage from '@/utils/storage';
import { getOrganServer } from '@/services/organization';

const BackupService = (props: BackupServiceProps): JSX.Element => {
	const { isOrgan, isProject } = props;
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const [data, setData] = useState<any[]>([]);

	useEffect(() => {
		if (!isProject && !isOrgan) {
			getOverviewServer().then((res) => {
				if (res.success) {
					setData(res.data);
				}
			});
		}
		if (isOrgan) {
			getOrganServer({ organId }).then((res) => {
				if (res.success) {
					setData(res.data);
				}
			});
		}
		if (isProject) {
			getProjectServer({ organId, projectId }).then((res) => {
				if (res.success) {
					setData(res.data);
				}
			});
		}
	}, [organId, projectId]);

	return (
		<div className="center-card" style={{ height: '333px' }}>
			<div className="header">
				<h3>备份服务器</h3>
			</div>

			<div className="usage-ranking">
				<div className="header">
					<h3>备份位置引用任务数排行 Top6</h3>
				</div>
				{data.length ? (
					data?.map((item: any, index: number) => {
						return (
							<div className={'content active'} key={index}>
								<div className="item" key={index}>
									<div
										className={`ranking${
											index + 1 <= 3 ? index + 1 : ''
										}`}
									>
										{index + 1}
									</div>
									<div className="iCnt">
										<div className="item-name">
											<div className="ellipse"></div>
											<p
												title={`${item.backupServerName}-${item.name}`}
											>
												{`${item.backupServerName}-${item.name}`}
											</p>
										</div>
										<div className="item-content">
											<span style={{ width: 40 }}>
												数量：
											</span>
											<p
												title={item.backupTaskNum}
												style={{ textAlign: 'left' }}
											>
												{item.backupTaskNum}
											</p>
										</div>
									</div>
								</div>
							</div>
						);
					})
				) : (
					<Empty
						imageStyle={{
							height: 52,
							marginTop: 85
						}}
					/>
				)}
			</div>
		</div>
	);
};

export default BackupService;

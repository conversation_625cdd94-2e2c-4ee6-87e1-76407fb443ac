import React from 'react';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router';
import { notification, Modal, Button, Tabs, Tooltip } from 'antd';
import type { TabsProps } from 'antd';
import { useHistory } from 'react-router';
import { ExclamationCircleFilled, ReloadOutlined } from '@ant-design/icons';
import { ProPage, ProHeader, ProContent } from '@/components/ProPage';
import DataFields from '@/components/DataFields';

import {
	getBackups,
	editBackupTasks,
	editIncBackup,
	getIncBackup,
	getTaskDetail,
	backupCheck,
	getBackupCheckResult
} from '@/services/backup';
import storage from '@/utils/storage';
import { middlewareProps } from '@/pages/ServiceList/service.list';
import {
	compareTime,
	controlledOperationDisabled,
	statusBackupRender
} from '@/utils/utils';
import { getCanReleaseMiddleware } from '@/services/middleware';
import { weekMap, backupTaskStatus, maintenances } from '@/utils/const';
import { EditOutlined } from '@ant-design/icons';

import EditTime from './editTime';
import { checkLicense } from '@/services/user';
import EditIncrTime from './editIncrTime';
import FullRecord from './tabs/fullRecord';
import IncrRecord from './tabs/incrRecord';
import CloneRecord from './tabs/cloneRecord';
import { getComponent } from '@/services/common';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import DataCheck from './tabs/dataCheck';
const { confirm } = Modal;

const dataType = [
	{ label: '天', value: 'day', max: 3650 },
	{ label: '周', value: 'week', max: 521 },
	{ label: '月', value: 'month', max: 120 },
	{ label: '年', value: 'year', max: 10 }
];
const info = {
	title: '基本信息',
	phrase: '',
	sourceName: '',
	position: '',
	creationTime: '',
	cron: '',
	retentionTime: '',
	dateUnit: '',
	limitRecord: '',
	successTime: ''
};
function BackupTaskDetail(): JSX.Element {
	const history = useHistory();
	const params: any = useParams();
	const backupDetail = storage.getLocal('backupDetail');
	const cloneServiceOperatorId = maintenances['Clone Service'];
	const editBackupTasksOperatorId = maintenances['Edit Backup Tasks'];
	const postgresqlDataValidityVerificationOperatorId =
		maintenances['【Postgresql】Data Validity Verification'];
	const redisDataValidityVerificationOperatorId =
		maintenances['【Redis】Data Validity Verification'];
	const [disabled, setDisabled] = useState<boolean>(false);
	const [visible, setVisible] = useState<boolean>(false);
	const [incrVisible, setIncrVisible] = useState<boolean>(false);
	const [modalType, setModalType] = useState<string>('');
	const [basicData, setBasicData] = useState<any>(info);
	const [basicData1, setBasicData1] = useState<any>(info);
	const [middlewareInfo, setMiddlewareInfo] = useState<middlewareProps>();
	const [record, setRecord] = useState<any>();
	const [backupComponent, setBackupComponent] = useState<boolean>(false);
	const [incrDataValidity, setIncrDataValidity] = useState<boolean>(false); // 增量数据验证 true为显示，false为隐藏
	const [ifCheck, setIfCheck] = useState<boolean>(false);
	const [incCheck, setIncCheck] = useState<any>();
	const [activeKey, setActiveKey] = useState<string>('1');
	const [items, setItems] = useState<TabsProps['items']>([
		{
			key: '1',
			label: '全量记录',
			children: (
				<FullRecord
					backupComponent={backupComponent}
					lock={params.lock}
				/>
			)
		},
		{
			key: '2',
			label: '增量记录',
			children: (
				<IncrRecord
					lock={params.lock}
					backupComponent={backupComponent}
					setIncrDataValidity={setIncrDataValidity}
				/>
			)
		},
		{
			key: '3',
			label: '克隆记录',
			children: (
				<CloneRecord
					lock={params.lock}
					backupComponent={backupComponent}
				/>
			)
		}
	]);
	const [infoConfig, setInfoConfig] = useState<any>([
		{
			dataIndex: 'title',
			render: (val: string) => (
				<div className="title-content">
					<div className="blue-line"></div>
					<div className="detail-title">{val}</div>
				</div>
			),
			span: 24
		},
		{
			dataIndex: 'phrase',
			label: '状态',
			render: (val: string) => statusBackupRender(val, 0, backupDetail)
		},
		{
			dataIndex: 'sourceName',
			label: '备份源名称',
			render: (val: string) => (
				<div className="text-overflow-one" title={val}>
					{val || '/'}
				</div>
			)
		},
		{
			dataIndex: 'cron',
			label: '备份方式',
			render: (val: string, record: any) => {
				return (
					<div className="text-overflow-one" title={val}>
						{backupDetail.backupMode === 'period' ? '周期' : '单次'}
						{/* 后端和k8s cron表达式问题，有的为* *，有的为? ? */}
						{val
							? val.indexOf('? ?') !== -1
								? `（每周${val
										.split('? ?')[1]
										.split(',')
										.map((item) => weekMap[item.trim()])}${
										Number(
											val.split('* *')[0].split(' ')[1]
										) >= 10
											? val.split('* *')[0].split(' ')[1]
											: '0' +
											  val.split('* *')[0].split(' ')[1]
								  }:${
										Number(
											val.split('* *')[0].split(' ')[0]
										) < 10
											? '0' +
											  val.split('* *')[0].split(' ')[0]
											: val.split('* *')[0].split(' ')[0]
								  }）`
								: `（每周${val
										.split('* *')[1]
										.split(',')
										.map((item) => weekMap[item.trim()])} ${
										Number(
											val.split('* *')[0].split(' ')[1]
										) >= 10
											? val.split('* *')[0].split(' ')[1]
											: '0' +
											  val.split('* *')[0].split(' ')[1]
								  }:${
										val.split('* *')[0].split(' ')[0] ===
										'0'
											? '00'
											: '30'
								  }）`
							: ''}
						{record.backupComponent &&
						record.phrase !== 'DeleteFailed' &&
						val ? (
							<EditOutlined
								style={{ marginLeft: 8, color: '#226EE7' }}
								onClick={() => {
									WorkOrderFuc(
										() => {
											setRecord(record);
											setVisible(true);
											setModalType('way');
										},
										params.lock,
										params.middlewareName,
										editBackupTasksOperatorId,
										history,
										params.type,
										params.name,
										params.aliasName,
										params.clusterId,
										params.namespace
									);
								}}
							/>
						) : null}
					</div>
				);
			}
		},
		{
			dataIndex:
				backupDetail.sourceType === 'mysql' && backupDetail.mysqlBackup
					? 'limitRecord'
					: 'retentionTime',
			label:
				backupDetail.sourceType === 'mysql' && backupDetail.mysqlBackup
					? '备份保留个数'
					: '备份保留时间',
			render: (val: any, record: any) => (
				// * 保留老版本mysql备份方式
				<div
					className="text-overflow-one"
					title={!val || typeof val === 'number' ? val : val[0]}
				>
					{!val || typeof val === 'number' ? val : val[0]}
					{val
						? dataType.find((item) => item.value === val[1])?.label
						: ''}
					{backupDetail.backupMode === 'period' ? '' : '--'}
					{record.backupComponent &&
					record.phrase !== 'DeleteFailed' &&
					backupDetail.backupMode === 'period' ? (
						<EditOutlined
							style={{ marginLeft: 8, color: '#226EE7' }}
							onClick={() => {
								WorkOrderFuc(
									() => {
										setRecord(record);
										setVisible(true);
										setModalType('time');
									},
									params.lock,
									params.middlewareName,
									editBackupTasksOperatorId,
									history,
									params.type,
									params.name,
									params.aliasName,
									params.clusterId,
									params.namespace
								);
							}}
						/>
					) : null}
				</div>
			)
		},
		{
			dataIndex: 'position',
			label: '备份位置',
			render: (val: string | string[]) =>
				typeof val === 'string' ? (
					<Tooltip title={val}>
						<div className="text-overflow-one cursor-pointer">
							{val.split('(')[0] || '/'}
						</div>
					</Tooltip>
				) : (
					<Tooltip
						title={
							<>
								<div>{val[0]}</div>
								<div>{val[1]}</div>
							</>
						}
					>
						<div className="text-overflow-one cursor-pointer">
							{val[0].split('(')[0] || '/'}
						</div>
					</Tooltip>
				)
		},
		{
			dataIndex: 'creationTime',
			label: '创建时间',
			render: (val: string) => (
				<div className="text-overflow-one" title={val}>
					{val || '--'}
				</div>
			)
		}
	]);
	const [infoConfig1, setInfoConfig1] = useState<any>([
		{
			dataIndex: 'title',
			render: (val: string) => (
				<div className="title-content">
					<div className="blue-line"></div>
					<div className="detail-title">{val}</div>
					<Tooltip title="当前任务两可用区备份信息不一致，请注意修改">
						<ExclamationCircleFilled
							style={{
								marginLeft: 4,
								color: '#ff4d4f',
								cursor: 'pointer'
							}}
						/>
					</Tooltip>
				</div>
			),
			span: 24
		},
		{
			dataIndex: 'phrase',
			label: '状态',
			render: (val: string) => statusBackupRender(val, 0, backupDetail)
		},
		{
			dataIndex: 'sourceName',
			label: '备份源名称',
			render: (val: string) => (
				<div className="text-overflow-one" title={val}>
					{val || '/'}
				</div>
			)
		},
		{
			dataIndex: 'cron',
			label: '备份方式',
			render: (val: string, record: any) => (
				<div className="text-overflow-one" title={val}>
					{backupDetail.backupMode === 'period' ? '周期' : '单次'}
					{/* 后端和k8s cron表达式问题，有的为* *，有的为? ? */}
					{val
						? val.indexOf('? ?') !== -1
							? `（每周${val
									.split('? ?')[1]
									.split(',')
									.map((item) => weekMap[item.trim()])}${
									Number(val.split('* *')[0].split(' ')[1]) >=
									10
										? val.split('* *')[0].split(' ')[1]
										: '0' +
										  val.split('* *')[0].split(' ')[1]
							  }:${
									Number(val.split('* *')[0].split(' ')[0]) <
									10
										? '0' +
										  val.split('* *')[0].split(' ')[0]
										: val.split('* *')[0].split(' ')[0]
							  }）`
							: `（每周${val
									.split('* *')[1]
									.split(',')
									.map((item) => weekMap[item.trim()])} ${
									Number(val.split('* *')[0].split(' ')[1]) >=
									10
										? val.split('* *')[0].split(' ')[1]
										: '0' +
										  val.split('* *')[0].split(' ')[1]
							  }:${
									val.split('* *')[0].split(' ')[0] === '0'
										? '00'
										: '30'
							  }）`
						: ''}
					{record.backupComponent && val ? (
						<EditOutlined
							style={{ marginLeft: 8, color: '#226EE7' }}
							onClick={() => {
								WorkOrderFuc(
									() => {
										setRecord(record);
										setVisible(true);
										setModalType('way');
									},
									params.lock,
									params.middlewareName,
									editBackupTasksOperatorId,
									history,
									params.type,
									params.name,
									params.aliasName,
									params.clusterId,
									params.namespace
								);
							}}
						/>
					) : null}
				</div>
			)
		},
		{
			dataIndex:
				backupDetail.sourceType === 'mysql' && backupDetail.mysqlBackup
					? 'limitRecord'
					: 'retentionTime',
			label:
				backupDetail.sourceType === 'mysql' && backupDetail.mysqlBackup
					? '备份保留个数'
					: '备份保留时间',
			render: (val: any, record: any) => (
				// * 保留老版本mysql备份方式
				<div
					className="text-overflow-one"
					title={!val || typeof val === 'number' ? val : val[0]}
				>
					{!val || typeof val === 'number' ? val : val[0]}
					{val
						? dataType.find((item) => item.value === val[1])?.label
						: ''}
					{backupDetail.backupMode === 'period' ? '' : '--'}
					{record.backupComponent &&
					backupDetail.backupMode === 'period' ? (
						<EditOutlined
							style={{ marginLeft: 8, color: '#226EE7' }}
							onClick={() => {
								WorkOrderFuc(
									() => {
										setRecord(record);
										setVisible(true);
										setModalType('time');
									},
									params.lock,
									params.middlewareName,
									editBackupTasksOperatorId,
									history,
									params.type,
									params.name,
									params.aliasName,
									params.clusterId,
									params.namespace
								);
							}}
						/>
					) : null}
				</div>
			)
		},
		{
			dataIndex: 'position',
			label: '备份位置',
			render: (val: string | string[]) =>
				typeof val === 'string' ? (
					<Tooltip title={val}>
						<div className="text-overflow-one cursor-pointer">
							{val.split('(')[0] || '/'}
						</div>
					</Tooltip>
				) : (
					<Tooltip
						title={
							<>
								<div>{val[0]}</div>
								<div>{val[1]}</div>
							</>
						}
					>
						<div className="text-overflow-one cursor-pointer">
							{val[0].split('(')[0] || '/'}
						</div>
					</Tooltip>
				)
		},
		{
			dataIndex: 'creationTime',
			label: '创建时间',
			render: (val: string) => (
				<div className="text-overflow-one" title={val}>
					{val || '--'}
				</div>
			)
		}
	]);
	const [taskStatus, setTaskStatus] = useState<string>();

	const increment = {
		dataIndex: 'pause',
		label: '是否开启增量',
		render: (val: string, record: any) => {
			// * 6/2 晚跟底座讨论结果，关闭是否开启增量的开关的功能，并且开启增量的判断从 pause === 'off' 修改成 newBackupName 是否存在
			return (
				<div className="text-overflow-one">
					{record.newBackupName && record.newBackupName !== ''
						? '已开启'
						: '未开启'}
				</div>
			);
		}
	};

	const time = {
		dataIndex: 'time',
		label: '备份间隔时间',
		render: (val: string, record: any) => (
			<div className="text-overflow-one">
				{(val?.substring(0, val?.length - 1) || '') + '分/次'}
				{record.backupComponent &&
				record.newBackupName &&
				record.newBackupName !== '' ? (
					<EditOutlined
						style={{ marginLeft: 8, color: '#226EE7' }}
						onClick={() => {
							WorkOrderFuc(
								() => {
									setRecord(record);
									setIncrVisible(true);
									setModalType('edit');
								},
								params.lock,
								params.middlewareName,
								editBackupTasksOperatorId,
								history,
								params.type,
								params.name,
								params.aliasName,
								params.clusterId,
								params.namespace
							);
						}}
					/>
				) : null}
			</div>
		)
	};

	const successTime = {
		dataIndex: 'successTime',
		label: '最近一次增量备份时间',
		render: (val: string) => (
			<div className="text-overflow-one" title={val}>
				{val || '--'}
			</div>
		)
	};
	const getComponentInfo = () => {
		getComponent({
			clusterId: params.clusterId,
			componentName: 'middlewarebackup-controller'
		}).then((res) => {
			if (res.success) {
				getBasicInfo(res.data[params.clusterId]?.status);
				getData();
				const backupTemp = res.data[params.clusterId]?.status;
				if (backupTemp === 3) {
					setBackupComponent(true);
				} else {
					setBackupComponent(false);
				}
			}
		});
	};

	useEffect(() => {
		getComponentInfo();
		const list = [...infoConfig];
		backupDetail.backupMode !== 'period' && list.splice(4, 1);
		setInfoConfig(list);
	}, []);
	useEffect(() => {
		if (params.clusterId && params.name) {
			getCanReleaseMiddleware({
				clusterId: params.clusterId,
				type: params.name
			}).then((res) => {
				if (res.success) {
					setMiddlewareInfo(res.data);
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		}
	}, [params.clusterId]);

	useEffect(() => {
		const list = [...infoConfig];
		if (params.type === 'mysql' || params.type === 'postgresql') {
			if (basicData.mysqlBackup) {
				list.find((item) => item.dataIndex === 'pause') &&
					list.splice(5, 1);
			} else {
				!list.find((item) => item.dataIndex === 'pause') &&
					list.splice(5, 0, increment);
			}
			if (basicData.newBackupName && basicData.newBackupName !== '') {
				!list.find((item) => item.dataIndex === 'time') &&
					list.splice(6, 0, time);
				!list.find((item) => item.dataIndex === 'successTime') &&
					list.splice(7, 0, successTime);
				setItems([
					{
						key: '1',
						label: '全量记录',
						children: (
							<FullRecord
								backupComponent={backupComponent}
								lock={params.lock}
							/>
						)
					},
					{
						key: '2',
						label: '增量记录',
						children: (
							<IncrRecord
								backupComponent={backupComponent}
								lock={params.lock}
								setIncrDataValidity={setIncrDataValidity}
							/>
						)
					},
					{
						key: '3',
						label: '克隆记录',
						children: (
							<CloneRecord
								backupComponent={backupComponent}
								lock={params.lock}
							/>
						)
					}
				]);
			} else {
				list.find((item) => item.dataIndex === 'time') &&
					list.splice(6, 1);
				list.find((item) => item.dataIndex === 'successTime') &&
					list.splice(6, 1);
				// * 判断双活增量时的场景·
				if (
					!(
						basicData1.newBackupName &&
						basicData1.newBackupName !== ''
					)
				) {
					setItems([
						{
							key: '1',
							label: '全量记录',
							children: (
								<FullRecord
									backupComponent={backupComponent}
									lock={params.lock}
								/>
							)
						},
						{
							key: '3',
							label: '克隆记录',
							children: (
								<CloneRecord
									backupComponent={backupComponent}
									lock={params.lock}
								/>
							)
						}
					]);
				}
			}
			backupDetail.backupMode === 'period' && setInfoConfig(list);
		} else {
			setItems([
				{
					key: '1',
					label: '全量记录',
					children: (
						<FullRecord
							backupComponent={backupComponent}
							lock={params.lock}
						/>
					)
				},
				{
					key: '3',
					label: '克隆记录',
					children: (
						<CloneRecord
							backupComponent={backupComponent}
							lock={params.lock}
						/>
					)
				}
			]);
		}
	}, [basicData, basicData1, backupComponent]);

	useEffect(() => {
		const list = [...infoConfig];
		if (params.name === 'mysql' || params.name === 'postgresql') {
			if (basicData.mysqlBackup) {
				list.find((item) => item.dataIndex === 'pause') &&
					list.splice(5, 1);
			} else {
				!list.find((item) => item.dataIndex === 'pause') &&
					list.splice(5, 0, increment);
			}
			if (basicData.newBackupName && basicData.newBackupName !== '') {
				!list.find((item) => item.dataIndex === 'time') &&
					list.splice(6, 0, time);
				!list.find((item) => item.dataIndex === 'successTime') &&
					list.splice(7, 0, successTime);
				setItems([
					{
						key: '1',
						label: '全量记录',
						children: (
							<FullRecord
								lock={params.lock}
								backupComponent={backupComponent}
							/>
						)
					},
					{
						key: '2',
						label: '增量记录',
						children: (
							<IncrRecord
								lock={params.lock}
								backupComponent={backupComponent}
								setIncrDataValidity={setIncrDataValidity}
							/>
						)
					},
					{
						key: '3',
						label: '克隆记录',
						children: (
							<CloneRecord
								lock={params.lock}
								backupComponent={backupComponent}
							/>
						)
					}
				]);
			} else {
				list.find((item) => item.dataIndex === 'time') &&
					list.splice(6, 1);
				list.find((item) => item.dataIndex === 'successTime') &&
					list.splice(6, 1);
				// * 判断双活增量时的场景·
				if (
					!(
						basicData1.newBackupName &&
						basicData1.newBackupName !== ''
					)
				) {
					setItems([
						{
							key: '1',
							label: '全量记录',
							children: (
								<FullRecord
									lock={params.lock}
									backupComponent={backupComponent}
								/>
							)
						},
						{
							key: '3',
							label: '克隆记录',
							children: (
								<CloneRecord
									lock={params.lock}
									backupComponent={backupComponent}
								/>
							)
						}
					]);
				}
			}
			backupDetail.backupMode === 'period' && setInfoConfig(list);
		} else {
			setItems([
				{
					key: '1',
					label: '全量记录',
					children: (
						<FullRecord
							lock={params.lock}
							backupComponent={backupComponent}
						/>
					)
				},
				{
					key: '3',
					label: '克隆记录',
					children: (
						<CloneRecord
							lock={params.lock}
							backupComponent={backupComponent}
						/>
					)
				}
			]);
		}
	}, [basicData, basicData1]);
	useEffect(() => {
		const list1 = [...infoConfig1];
		if (params.name === 'mysql' || params.name === 'postgresql') {
			if (basicData1.mysqlBackup) {
				list1.find((item) => item.dataIndex === 'pause') &&
					list1.splice(5, 1);
			} else {
				!list1.find((item) => item.dataIndex === 'pause') &&
					list1.splice(5, 0, increment);
			}
			if (basicData1.newBackupName && basicData1.newBackupName !== '') {
				!list1.find((item) => item.dataIndex === 'time') &&
					list1.splice(6, 0, time);
				!list1.find((item) => item.dataIndex === 'successTime') &&
					list1.splice(7, 0, successTime);
			} else {
				list1.find((item) => item.dataIndex === 'time') &&
					list1.splice(6, 1);
				list1.find((item) => item.dataIndex === 'successTime') &&
					list1.splice(6, 1);
			}
			backupDetail.backupMode === 'period' && setInfoConfig1(list1);
		}
	}, [basicData1]);
	const getData = async () => {
		await getBackups({
			backupId: params.backupId,
			backupMode: backupDetail.backupMode,
			clusterId: params.clusterId,
			namespace: params.namespace,
			middlewareName: params.middlewareName,
			type: params.name
		}).then((res) => {
			if (res.success) {
				setDisabled(
					res.data?.filter((item: any) => item.phrase === 'Success')
						?.length === 0
				);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};

	const releaseMiddleware = () => {
		switch (backupDetail.sourceType) {
			case 'mysql':
				history.push(
					`/project/${
						params.type || middlewareInfo?.type
					}/mysql/MySQL/mysqlBackup/${middlewareInfo?.chartVersion}/${
						backupDetail.sourceName
					}/${backupDetail.namespace}/${backupDetail.clusterId}`
				);
				break;
			case 'postgresql':
				history.push(
					`/project/${
						params.type || middlewareInfo?.type
					}/postgresql/PostgreSQL/postgresqlBackup/${
						middlewareInfo?.chartVersion
					}/${backupDetail.sourceName}/${backupDetail.namespace}/${
						backupDetail.clusterId
					}`
				);
				break;
			case 'redis':
				history.push(
					`/project/${
						params.type || middlewareInfo?.type
					}/redis/Redis/redisBackup/${middlewareInfo?.chartVersion}/${
						backupDetail.sourceName
					}/${backupDetail.namespace}/${backupDetail.clusterId}`
				);
				break;
			case 'elasticsearch':
				history.push(
					`/project/${
						params.type || middlewareInfo?.type
					}/elasticsearch/Elasticsearch/elasticsearchBackup/${
						middlewareInfo?.chartVersion
					}/${backupDetail.sourceName}/${backupDetail.namespace}/${
						backupDetail.clusterId
					}`
				);
				break;
			case 'rocketmq':
				history.push(
					`/project/${
						params.type || middlewareInfo?.type
					}/rocketmq/rocketMQ/rocketmqBackup/${
						middlewareInfo?.chartVersion
					}/${backupDetail.sourceName}/${backupDetail.namespace}/${
						backupDetail.clusterId
					}`
				);
				break;
			case 'zookeeper':
				history.push(
					`/project/${
						params.type || middlewareInfo?.type
					}/zookeeper/Zookeeper/zookeeperBackup/${
						middlewareInfo?.chartVersion
					}/${backupDetail.sourceName}/${backupDetail.namespace}/${
						params.clusterId || backupDetail.clusterId
					}`
				);
				break;
			case 'kafka':
				history.push(
					`/project/${
						params.type || middlewareInfo?.type
					}/kafka/Kafka/kafkaBackup/${middlewareInfo?.chartVersion}/${
						backupDetail.sourceName
					}/${backupDetail.namespace}/${
						params.clusterId || backupDetail.clusterId
					}`
				);
				break;
		}
	};

	const getBasicInfo = (status: number) => {
		const sendData = {
			backupMode: backupDetail.backupMode,
			backupId: params.backupId,
			clusterId: params.clusterId,
			namespace: params.namespace
		};
		getTaskDetail(sendData).then((res) => {
			setTaskStatus(res.data[0]?.phrase);
			if (res.data[0]?.phrase === 'Deleting' || res.data.length === 0) {
				history.goBack();
				return;
			}
			if (res.success) {
				getIncBackup({
					clusterId: params.clusterId,
					namespace: params.namespace,
					backupId: params.backupId
				}).then((result) => {
					setIncCheck(result.data[0]);
					if (result.success) {
						const data = (index: number) => {
							return {
								cron: res.data[index]?.cron,
								phrase: res.data[index]?.phrase,
								sourceName: res.data[index]?.sourceName,
								position: res.data[index]?.activeActive
									? [
											res.data[0]?.position,
											res.data[1]?.position
									  ]
									: res.data[index]?.position,
								creationTime: res.data[index]?.creationTime,
								retentionTime: [
									res.data[index]?.retentionTime,
									res.data[index]?.dateUnit
								],
								activeArea: res.data[index]?.activeArea,
								areaAliasName: res.data[index]?.areaAliasName,
								backupId: res.data[index]?.backupId,
								backupName: res.data[index]?.backupName,
								dateUnit: res.data[index]?.dateUnit,
								mysqlBackup: res.data[index]?.mysqlBackup,
								limitRecord: res.data[index]?.limitRecord,
								activeActive: res.data[index]?.activeActive,
								endTime: result.data?.[index]?.endTime,
								successTime:
									res.data[index]?.sameActiveActiveBackup &&
									res.data[index]?.activeArea
										? compareTime(
												result.data?.[0]?.successTime,
												result.data?.[1]?.successTime
										  )
										: result.data?.[index]?.successTime,
								startTime: result.data?.[index]?.startTime,
								time: result.data?.[index]?.time,
								pause: result.data?.[index]?.pause,
								newBackupName: result.data?.[index]?.backupName,
								backupIncName: result.data?.[index]?.backupName,
								sameActiveActiveBackup:
									res.data[index]?.sameActiveActiveBackup &&
									(result.data.length
										? result.data?.[index]
												?.sameActiveActiveBackup
										: true)
							};
						};
						setBasicData({
							title: `基本信息${
								!data(0).sameActiveActiveBackup &&
								data(0).activeActive
									? `(${data(0).areaAliasName})`
									: ''
							}`,
							...data(0),
							backupComponent: status === 3 ? true : false
						});
						setRecord({
							title: `基本信息${
								!data(0).sameActiveActiveBackup &&
								data(0).activeActive
									? `(${data(0).areaAliasName})`
									: ''
							}`,
							...data(0)
						});
						res.data.length > 1 &&
							setBasicData1({
								title: `基本信息${
									!data(1).sameActiveActiveBackup
										? `(${data(1).areaAliasName})`
										: ''
								}`,
								...data(1),
								backupComponent: status === 3 ? true : false
							});
						// ! 问题源头 ! 将备份详情和增量的数据杂糅在一起了，在双活的情况下甚至只放了可用区A的数据
						// ! 后续如果没有对相关数据覆盖，则会出现在选择了可用区B的情况下，传值为可用区A的情况
						storage.setLocal('backupDetail', {
							...backupDetail,
							...data(0)
						});
						setVisible(false);
						setIncrVisible(false);
					} else {
						notification.error({
							message: '失败',
							description: result.errorMsg
						});
					}
				});
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const onCreate = async (data: any) => {
		const sendData = {
			backupId: record.backupId,
			backupName: record.backupName,
			clusterId: params.clusterId,
			namespace: params.namespace,
			type: params.name,
			increment: backupDetail.increment,
			time: backupDetail.time,
			sameActiveActiveBackup: backupDetail.sameActiveActiveBackup,
			...data
		};
		if (backupDetail.mysqlBackup) {
			delete sendData.increment;
			delete sendData.time;
		}
		await ExecuteOrderFuc();
		editBackupTasks(sendData).then((res) => {
			if (res.success) {
				getComponentInfo();
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const onIncrCreate = async (data: any) => {
		if (modalType === 'add') {
			const sendData = {
				backupId: record.backupId,
				backupName: record.backupName,
				clusterId: params.clusterId,
				namespace: params.namespace,
				increment: backupDetail.increment,
				sameActiveActiveBackup: backupDetail.sameActiveActiveBackup,
				...data
			};
			await ExecuteOrderFuc();
			editIncBackup(sendData).then((res) => {
				if (res.success) {
					getComponentInfo();
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		} else {
			const sendData = {
				backupId: record.backupId,
				backupName: record.backupName,
				clusterId: params.clusterId,
				namespace: params.namespace,
				type: params.name,
				sameActiveActiveBackup: backupDetail.sameActiveActiveBackup,
				...data
			};
			await ExecuteOrderFuc();
			editIncBackup(sendData).then((res) => {
				if (res.success) {
					getComponentInfo();
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		}
	};
	// * 增量数据验证
	const dataCheck = async () => {
		console.log(incCheck);
		const sendData = {
			...params,
			middleware: params.middlewareName,
			backupType: params.backupMode,
			backupName: incCheck.backupName
		};
		const res = await getBackupCheckResult(sendData);
		if (res.data.phase) {
			setIfCheck(true);
		} else {
			confirm({
				title: '操作确认',
				content:
					'即将开始备份数据有效性验证，请确保备份任务中至少存在一条备份成功的记录。',
				onOk: () => {
					handleBackupResult();
				}
			});
		}
	};

	const handleBackupResult = async () => {
		const sendData = {
			...params,
			middleware: params.middlewareName,
			backupType: params.backupMode
		};
		await ExecuteOrderFuc();
		const res = await backupCheck(sendData);
		if (!res.success) {
			notification.error({
				message: '错误',
				description: (
					<>
						<p>{res.errorMsg}</p>
						<p>{res.errorMDetail}</p>
					</>
				)
			});
			return;
		}
		setIfCheck(true);
	};

	const cloneService = () => {
		checkLicense({
			license: '',
			clusterId: params.clusterId
		}).then((res) => {
			if (res.success) {
				if (!res.data) {
					confirm({
						title: '可用余额不足',
						content:
							'当前平台可用余额已不足2Core，如果您想继续使用zeus中间件一体化管理平台，请联系我们申请授权码。',
						okText: '立即前往'
					});
				} else {
					if (basicData.activeActive) {
						if (params.type) {
							history.push(
								`/project/${params.type}/${params.name}/${params.aliasName}/container/backupRecovery/backupTaskDetail/backupTaskRecovery/${params.middlewareName}/${params.clusterId}/${params.namespace}/${backupDetail.backupId}`
							);
						} else {
							history.push(
								`/project/backup/recovery/${params.clusterId}/${params.namespace}/${backupDetail.backupId}/${backupDetail.sourceType}/${backupDetail.sourceName}/${middlewareInfo?.type}`
							);
						}
					} else {
						if (backupDetail.backupMode === 'period') {
							// * 周期备份
							if (params.type) {
								history.push(
									`/project/${params.type}/${params.name}/${params.aliasName}/container/backupRecovery/backupTaskDetail/backupTaskRecovery/${params.middlewareName}/${params.clusterId}/${params.namespace}/${backupDetail.backupId}`
								);
							} else {
								history.push(
									`/project/backup/recovery/${params.clusterId}/${params.namespace}/${backupDetail.backupId}/${backupDetail.sourceType}/${backupDetail.sourceName}/${middlewareInfo?.type}`
								);
							}
						} else {
							if (backupDetail.schedule) {
								if (params.type) {
									history.push(
										`/project/${params.type}/${params.name}/${params.aliasName}/container/backupRecovery/backupTaskDetail/backupTaskRecovery/${params.middlewareName}/${params.clusterId}/${params.namespace}/${backupDetail.backupId}`
									);
								} else {
									history.push(
										`/project/backup/recovery/${params.clusterId}/${params.namespace}/${backupDetail.backupId}/${backupDetail.sourceType}/${backupDetail.sourceName}/${middlewareInfo?.type}`
									);
								}
							} else {
								releaseMiddleware();
							}
						}
					}
				}
			}
		});
	};
	const onChange = (key: string | number) => {
		setActiveKey(key as string);
	};
	const tabOperation = () => {
		if (taskStatus === 'DeleteFailed') return <></>;
		if (activeKey !== '2') return <></>;
		if (!incrDataValidity) return <></>;
		if (params.name !== 'postgresql' && params.name !== 'redis')
			return <></>;
		return (
			<Button
				disabled={!backupComponent}
				title={
					!backupComponent
						? '备份控制器组件未安装，请前往平台组件页面安装相应组件'
						: undefined
				}
				onClick={() => {
					const operator_id =
						params.name === 'postgresql'
							? postgresqlDataValidityVerificationOperatorId
							: redisDataValidityVerificationOperatorId;
					WorkOrderFuc(
						dataCheck,
						params.lock,
						params.middlewareName,
						operator_id,
						history,
						params.type,
						params.name,
						params.aliasName,
						params.clusterId,
						params.namespace
					);
				}}
			>
				增量数据验证
			</Button>
		);
	};
	const extra = () => {
		if (taskStatus === 'DeleteFailed') {
			return (
				<Button
					onClick={() => {
						getComponentInfo();
						setActiveKey(activeKey);
					}}
					id="detailRefresh"
					icon={<ReloadOutlined id="detailRefresh" />}
				/>
			);
		}
		return (
			<>
				<Button
					type="primary"
					onClick={() => {
						WorkOrderFuc(
							cloneService,
							params.lock,
							params.middlewareName,
							cloneServiceOperatorId,
							history,
							params.type,
							params.name,
							params.aliasName,
							params.clusterId,
							params.namespace
						);
					}}
					title={
						!backupComponent
							? '备份控制器组件未安装，请前往平台组件页面安装相应组件'
							: undefined
					}
					disabled={
						!backupComponent ||
						disabled ||
						controlledOperationDisabled(
							'maintenance',
							params.lock
						) ||
						controlledOperationDisabled('base')
					}
				>
					克隆服务
				</Button>
				<Button
					onClick={() => {
						getComponentInfo();
						setActiveKey(activeKey);
					}}
					id="detailRefresh"
					icon={<ReloadOutlined id="detailRefresh" />}
				/>
			</>
		);
	};
	return (
		<ProPage>
			<ProHeader
				title={`${backupDetail.taskName}（${
					backupTaskStatus.find((item) => item.value === taskStatus)
						?.text || '未知'
				}）`}
				onBack={() => history.goBack()}
				extra={extra()}
			/>
			<ProContent>
				<DataFields dataSource={basicData} items={infoConfig} />
				<div className="detail-divider"></div>
				{basicData.activeActive &&
					!basicData.sameActiveActiveBackup && (
						<>
							<DataFields
								dataSource={basicData1}
								items={infoConfig1}
							/>
							<div className="detail-divider"></div>
						</>
					)}
				<h2>相关记录</h2>
				<Tabs
					activeKey={activeKey}
					onChange={onChange}
					items={items}
					tabBarStyle={{ marginBottom: 0 }}
					tabBarExtraContent={tabOperation()}
				/>
				{visible && (
					<EditTime
						data={record}
						visible={visible}
						onCreate={onCreate}
						onCancel={() => setVisible(false)}
						type={modalType}
					/>
				)}
				{incrVisible && (
					<EditIncrTime
						data={record}
						visible={incrVisible}
						onCreate={onIncrCreate}
						onCancel={() => setIncrVisible(false)}
						type={modalType}
					/>
				)}
			</ProContent>
			{ifCheck && (
				<DataCheck
					open={ifCheck}
					onClose={() => setIfCheck(false)}
					checkRecord={incCheck}
				/>
			)}
		</ProPage>
	);
}

export default BackupTaskDetail;

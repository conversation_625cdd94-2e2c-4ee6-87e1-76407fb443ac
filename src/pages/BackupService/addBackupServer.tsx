import React, { useState, useEffect } from 'react';
import { useHistory, useParams } from 'react-router';
import { ProPage, ProHeader, ProContent } from '@/components/ProPage';
import Backup from '@/assets/images/backup.svg';
import {
	Form,
	Input,
	InputNumber,
	Select,
	Divider,
	Button,
	Radio,
	notification
} from 'antd';
import SelectBlock from '@/components/SelectBlock';

import { addServer, editServer } from '@/services/backup';
import { licenseFeatures } from '@/services/user';
import storage from '@/utils/storage';
import pattern from '@/utils/pattern';
import { decrypt, encrypt } from '@/utils/utils';
import { PRIVATE_KEY, PUBLIC_KEY } from '@/utils/const';

const formItemLayout = {
	labelCol: {
		span: 3
	},
	wrapperCol: {
		span: 10
	}
};

export default function AddBackupServer(): JSX.Element {
	const history = useHistory();
	const [form] = Form.useForm();
	const params: { id?: string | undefined } = useParams();
	const [type, setType] = useState<number>(1);
	const [serveType, setServeType] = useState<string>('normal');
	const [buttonDisabled, setButtonDisabled] = useState<boolean>(false);
	const [activeOpen, setActiveOpen] = useState<boolean>(false);
	const record = storage.getSession('backupServer');
	const addressType = [
		{
			label: 'S3',
			value: 1
		},
		{
			label: 'Ftp',
			value: 2,
			disabled: true
		},

		{
			label: '服务器',
			value: 3,
			disabled: true
		}
	];

	useEffect(() => {
		if (params.id) {
			form.setFieldsValue({
				name: record.name,
				serveType: record.type === 1 ? 'normal' : 'shuanghuo',
				type: record.serverDetailList[0].type,
				protocol: record.serverDetailList[0].protocol,
				host: record.serverDetailList[0].host,
				port: record.serverDetailList[0].port,
				username: record.serverDetailList[0].username,
				password: decrypt(
					record.serverDetailList[0].password,
					PRIVATE_KEY
				)
			});
			record.type === 2 &&
				form.setFieldsValue({
					typeA: record.serverDetailList.find(
						(item: any) => item.serverUsage === 'A'
					)?.type,
					protocolA: record.serverDetailList.find(
						(item: any) => item.serverUsage === 'A'
					)?.protocol,
					hostA: record.serverDetailList.find(
						(item: any) => item.serverUsage === 'A'
					)?.host,
					portA: record.serverDetailList.find(
						(item: any) => item.serverUsage === 'A'
					)?.port,
					usernameA: record.serverDetailList.find(
						(item: any) => item.serverUsage === 'A'
					)?.username,
					passwordA: decrypt(
						record.serverDetailList.find(
							(item: any) => item.serverUsage === 'A'
						)?.password,
						PRIVATE_KEY
					),
					typeB: record.serverDetailList.find(
						(item: any) => item.serverUsage === 'B'
					)?.type,
					protocolB: record.serverDetailList.find(
						(item: any) => item.serverUsage === 'B'
					)?.protocol,
					hostB: record.serverDetailList.find(
						(item: any) => item.serverUsage === 'B'
					)?.host,
					portB: record.serverDetailList.find(
						(item: any) => item.serverUsage === 'B'
					)?.port,
					usernameB: record.serverDetailList.find(
						(item: any) => item.serverUsage === 'B'
					)?.username,
					passwordB: decrypt(
						record.serverDetailList.find(
							(item: any) => item.serverUsage === 'B'
						)?.password,
						PRIVATE_KEY
					)
				});
			setServeType(record.type === 1 ? 'normal' : 'shuanghuo');
		}
		licenseFeatures().then((res) => {
			if (res.success) {
				setActiveOpen(res.data.activeActiveEnable);
			}
		});

		return () => {
			storage.removeSession('backupServer');
		};
	}, []);

	const handleSubmit = (isCheck?: boolean) => {
		form.validateFields().then((values) => {
			const encrypted_password = encrypt(values.password, PUBLIC_KEY);
			const encrypted_passwordA = encrypt(values.passwordA, PUBLIC_KEY);
			const encrypted_passwordB = encrypt(values.passwordB, PUBLIC_KEY);
			encrypted_password &&
				console.log(decrypt(encrypted_password, PRIVATE_KEY));
			const sendData = {
				name: values.name,
				type: values.serveType === 'normal' ? 1 : 2,
				serverDetailList:
					values.serveType === 'normal'
						? [
								{
									type: values.type,
									protocol: values.protocol,
									host: values.host,
									port: values.port,
									username: values.username,
									password: encrypted_password
								}
						  ]
						: [
								{
									serverUsage: 'A',
									type: values.typeA,
									protocol: values.protocolA,
									host: values.hostA,
									port: values.portA,
									username: values.usernameA,
									password: encrypted_passwordA
								},
								{
									serverUsage: 'B',
									type: values.typeB,
									protocol: values.protocolB,
									host: values.hostB,
									port: values.portB,
									username: values.usernameB,
									password: encrypted_passwordB
								}
						  ]
			};
			if (
				values.protocolA + values.hostA + values.portA ===
				values.protocolB + values.hostB + values.portB
			) {
				notification.warning({
					message: '提示',
					description: '可用区备份地址不能相同'
				});
				setButtonDisabled(false);
				return;
			}
			setButtonDisabled(true);
			if (params.id) {
				editServer({
					...sendData,
					id: Number(params.id),
					serverDetailList:
						values.serveType === 'normal'
							? [
									{
										...sendData.serverDetailList[0],
										id: record.serverDetailList[0].id,
										backupServerId:
											record.serverDetailList[0]
												.backupServerId
									}
							  ]
							: [
									{
										...sendData.serverDetailList[0],
										id: record.serverDetailList.find(
											(item: any) =>
												item.serverUsage === 'A'
										)?.id,
										backupServerId:
											record.serverDetailList.find(
												(item: any) =>
													item.serverUsage === 'A'
											)?.backupServerId
									},
									{
										...sendData.serverDetailList[1],
										id: record.serverDetailList.find(
											(item: any) =>
												item.serverUsage === 'B'
										)?.id,
										backupServerId:
											record.serverDetailList.find(
												(item: any) =>
													item.serverUsage === 'B'
											)?.backupServerId
									}
							  ]
				}).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '修改成功'
						});
						setButtonDisabled(false);
						history.goBack();
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
						setButtonDisabled(false);
					}
				});
			} else {
				addServer(sendData).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '添加成功'
						});
						setButtonDisabled(false);
						history.goBack();
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
						setButtonDisabled(false);
					}
				});
			}
			// }
		});
	};

	return (
		<ProPage className="add-backup-position">
			<ProHeader
				onBack={() => history.goBack()}
				title={params.id ? '编辑备份服务器' : '新增备份服务器'}
				avatar={{
					children: <img src={Backup} />,
					shape: 'square',
					size: 48,
					style: { background: '#f5f5f5' }
				}}
			/>
			<ProContent>
				<Form form={form} {...formItemLayout} labelAlign="left">
					<h2 style={{ marginBottom: 8 }}>类型选择</h2>
					<Form.Item
						label="类型选择"
						name="serveType"
						rules={[
							{
								required: true,
								message: '请选择类型'
							}
						]}
						initialValue="normal"
					>
						<Radio.Group
							value={serveType}
							onChange={(e) => setServeType(e.target.value)}
							disabled={!!params.id}
						>
							<Radio key="normal" value="normal">
								普通备份服务器
							</Radio>
							{activeOpen && (
								<Radio key="shuanghuo" value="shuanghuo">
									双活备份服务器
								</Radio>
							)}
						</Radio.Group>
					</Form.Item>
					<h2 style={{ marginBottom: 8 }}>基础信息</h2>
					<Form.Item
						label="服务器名称"
						rules={[
							{
								required: true,
								message: '请输入服务器名称'
							},
							{
								pattern: new RegExp(pattern.storageName),
								message:
									'服务器名称由中文、大写字母、小写字母、数字和特殊字符.-/组成，长度不超过32个字符'
							}
						]}
						name="name"
					>
						<Input
							placeholder="请输入服务器名称"
							disabled={!!params.id}
						/>
					</Form.Item>
					{serveType === 'normal' ? (
						<>
							<Form.Item
								label="类型"
								rules={[
									{
										required: true,
										message: '请选择类型'
									}
								]}
								name="type"
								initialValue={type}
							>
								<SelectBlock
									itemStyle={{
										width: 'calc((100% - 16px) / 3)'
									}}
									options={addressType}
									currentValue={type}
									disabled={!!params.id}
									onCallBack={(value: any) => {
										console.log(value);

										setType(value);
										form.setFieldsValue({ type: value });
									}}
								/>
							</Form.Item>
							<Form.Item label="备份地址" required>
								<div className="flex-form">
									<Form.Item
										rules={[
											{
												required: true,
												message: '请选择协议'
											}
										]}
										name="protocol"
									>
										<Select placeholder="请选择协议">
											<Select.Option
												key="http"
												value="http"
											>
												http
											</Select.Option>
											<Select.Option
												key="https"
												value="https"
											>
												https
											</Select.Option>
										</Select>
									</Form.Item>
									<Form.Item
										name="host"
										rules={[
											{
												required: true,
												message: '请输入地址'
											}
										]}
									>
										<Input
											style={{
												width: '220px',
												borderLeft: 'none',
												borderRight: 'none'
											}}
											placeholder="请输入地址"
										/>
									</Form.Item>
									<Form.Item
										name="port"
										rules={[
											{
												required: true,
												message: '请输入端口'
											}
										]}
									>
										<InputNumber
											style={{ minWidth: '100%' }}
											placeholder="端口"
										/>
									</Form.Item>
									{/* <Form.Item
								name="bucketName"
								rules={[
									{
										required: true,
										message: '请输入存储桶'
									},
									{
										pattern: /^\/[a-zA-Z0-9-]*$/,
										message: '存储桶不符合规则'
									}
								]}
							>
								<Input
									placeholder="存储桶 如：/path"
									disabled={!!params.id}
								/>
							</Form.Item> */}
								</div>
							</Form.Item>
							<Form.Item
								label="用户ID"
								rules={[
									{
										required: true,
										message: '请输入用户ID'
									}
								]}
								name="username"
							>
								<Input placeholder="用户ID" />
							</Form.Item>
							<Form.Item
								name="password"
								label="密码"
								rules={[
									{
										required: true,
										message: '请输入密码'
									}
								]}
							>
								<Input.Password placeholder="密码" />
							</Form.Item>
						</>
					) : (
						<>
							<div style={{ display: 'flex' }}>
								<div style={{ width: '12.5%' }}>
									可用区A服务器
								</div>
								<div style={{ width: 500 }}>
									<Form.Item
										label="类型"
										rules={[
											{
												required: true,
												message: '请选择类型'
											}
										]}
										name="typeA"
										initialValue={type}
										labelCol={{ span: 6 }}
										wrapperCol={{ span: 18 }}
									>
										<SelectBlock
											itemStyle={{
												width: 'calc((100% - 16px) / 3)'
											}}
											options={addressType}
											currentValue={type}
											disabled={!!params.id}
											onCallBack={(value: any) => {
												setType(value);
												form.setFieldsValue({
													type: value
												});
											}}
										/>
									</Form.Item>
									<Form.Item
										label="备份地址"
										required
										labelCol={{ span: 6 }}
										wrapperCol={{ span: 18 }}
									>
										<div className="flex-form">
											<Form.Item
												rules={[
													{
														required: true,
														message: '请选择协议'
													}
												]}
												name="protocolA"
											>
												<Select placeholder="请选择协议">
													<Select.Option
														key="http"
														value="http"
													>
														http
													</Select.Option>
													<Select.Option
														key="https"
														value="https"
													>
														https
													</Select.Option>
												</Select>
											</Form.Item>
											<Form.Item
												name="hostA"
												rules={[
													{
														required: true,
														message: '请输入地址'
													}
												]}
												labelCol={{ span: 6 }}
												wrapperCol={{ span: 18 }}
											>
												<Input
													style={{
														width: '180px',
														borderLeft: 'none',
														borderRight: 'none'
													}}
													placeholder="请输入地址"
												/>
											</Form.Item>
											<Form.Item
												name="portA"
												rules={[
													{
														required: true,
														message: '请输入端口'
													}
												]}
												labelCol={{ span: 6 }}
												wrapperCol={{ span: 18 }}
											>
												<InputNumber
													style={{ minWidth: '96px' }}
													placeholder="端口"
												/>
											</Form.Item>
										</div>
									</Form.Item>
									<Form.Item
										label="用户ID"
										rules={[
											{
												required: true,
												message: '请输入用户ID'
											}
										]}
										name="usernameA"
										labelCol={{ span: 6 }}
										wrapperCol={{ span: 18 }}
									>
										<Input placeholder="用户ID" />
									</Form.Item>
									<Form.Item
										name="passwordA"
										label="密码"
										rules={[
											{
												required: true,
												message: '请输入密码'
											}
										]}
										labelCol={{ span: 6 }}
										wrapperCol={{ span: 18 }}
									>
										<Input.Password placeholder="密码" />
									</Form.Item>
								</div>
							</div>
							<div style={{ display: 'flex' }}>
								<div style={{ width: '12.5%' }}>
									可用区B服务器
								</div>
								<div style={{ width: 500 }}>
									<Form.Item
										label="类型"
										rules={[
											{
												required: true,
												message: '请选择类型'
											}
										]}
										name="typeB"
										initialValue={type}
										labelCol={{ span: 6 }}
										wrapperCol={{ span: 18 }}
									>
										<SelectBlock
											itemStyle={{
												width: 'calc((100% - 16px) / 3)'
											}}
											options={addressType}
											currentValue={type}
											disabled={!!params.id}
											onCallBack={(value: any) => {
												setType(value);
												form.setFieldsValue({
													type: value
												});
											}}
										/>
									</Form.Item>
									<Form.Item
										label="备份地址"
										required
										labelCol={{ span: 6 }}
										wrapperCol={{ span: 18 }}
									>
										<div className="flex-form">
											<Form.Item
												rules={[
													{
														required: true,
														message: '请选择协议'
													}
												]}
												name="protocolB"
											>
												<Select placeholder="请选择协议">
													<Select.Option
														key="http"
														value="http"
													>
														http
													</Select.Option>
													<Select.Option
														key="https"
														value="https"
													>
														https
													</Select.Option>
												</Select>
											</Form.Item>
											<Form.Item
												name="hostB"
												rules={[
													{
														required: true,
														message: '请输入地址'
													}
												]}
												labelCol={{ span: 6 }}
												wrapperCol={{ span: 18 }}
											>
												<Input
													style={{
														width: '180px',
														borderLeft: 'none',
														borderRight: 'none'
													}}
													placeholder="请输入地址"
												/>
											</Form.Item>
											<Form.Item
												name="portB"
												rules={[
													{
														required: true,
														message: '请输入端口'
													}
												]}
												labelCol={{ span: 6 }}
												wrapperCol={{ span: 18 }}
											>
												<InputNumber
													style={{ minWidth: '96px' }}
													placeholder="端口"
												/>
											</Form.Item>
										</div>
									</Form.Item>
									<Form.Item
										label="用户ID"
										rules={[
											{
												required: true,
												message: '请输入用户ID'
											}
										]}
										name="usernameB"
										labelCol={{ span: 6 }}
										wrapperCol={{ span: 18 }}
									>
										<Input placeholder="用户ID" />
									</Form.Item>
									<Form.Item
										name="passwordB"
										label="密码"
										rules={[
											{
												required: true,
												message: '请输入密码'
											}
										]}
										labelCol={{ span: 6 }}
										wrapperCol={{ span: 18 }}
									>
										<Input.Password placeholder="密码" />
									</Form.Item>
								</div>
							</div>
						</>
					)}
				</Form>
				<Divider />
				{/* <Button
					type="primary"
					style={{ marginRight: 16 }}
					onClick={() => handleSubmit(true)}
				>
					连接测试
				</Button> */}
				<Button
					type="primary"
					style={{ marginRight: 16 }}
					onClick={() => handleSubmit()}
					loading={buttonDisabled}
				>
					确认
				</Button>
				<Button onClick={() => history.goBack()}>取消</Button>
			</ProContent>
		</ProPage>
	);
}

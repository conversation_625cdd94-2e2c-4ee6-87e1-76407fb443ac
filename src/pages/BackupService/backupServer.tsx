import React, { useState, useEffect } from 'react';
import { useHistory } from 'react-router';
import { Button, Input, notification, Modal, Spin, Empty } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import { IconFont } from '@/components/IconFont';
import { ProPage, ProHeader, ProContent } from '@/components/ProPage';
import BackupServeIcon from '@/assets/images/backupServe.svg';
import ClusterIcon from '@/assets/images/cluster.svg';

import { ListPanel, ListCardItem } from '@/components/ListCard';
import Actions from '@/components/Actions';
import { getClusters } from '@/services/common';
import { getServer, removeServer, allocate, getCount } from '@/services/backup';
import { poolListItem } from '@/types/comment';
import storage from '@/utils/storage';
import PasswordEye from '@/components/PasswordEye';
import './index.less';
import { decrypt } from '@/utils/utils';
import { PRIVATE_KEY } from '@/utils/const';

const LinkButton = Actions.LinkButton;
const Search = Input.Search;
export default function BackupServer(): JSX.Element {
	const [poolList, setPoolList] = useState<poolListItem[]>([]);
	const [addressList, setAddressList] = useState<any[]>([]);
	const [keyword, setKeyword] = useState<string>('');
	const [spinning, setSpinning] = useState<boolean>(false);
	const [clusterVisible, setClusterVisible] = useState<boolean>(false);
	const [selectCluster, setSelectCluster] = useState<string>();
	const [backupServerId, setBackupServerId] = useState<number>(0);
	const [clusterList, setClusterList] = useState<any>();
	const [currentCluster, setCurrentCluster] = useState<string>('');
	const history = useHistory();

	useEffect(() => {
		getClusters().then((res) => {
			if (!res.data) return;
			setPoolList(res.data);
		});
	}, []);

	useEffect(() => {
		getData(keyword);
	}, [currentCluster]);

	const getData = (keyword: string) => {
		setSpinning(true);
		getServer({
			keyword,
			clusterIds: currentCluster ? [currentCluster] : [],
			withDetail: true
		})
			.then((res) => {
				if (res.success) {
					setAddressList(res.data);
					getCounts();
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			})
			.finally(() => {
				setSpinning(false);
			});
	};

	const getCounts = () => {
		getCount().then((res) => {
			if (res.success) {
				setClusterList(res.data);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};

	const handleDelete = (id: number) => {
		Modal.confirm({
			title: '操作确认',
			content: '备份服务器删除后将无法恢复，请确认执行',
			onOk: () => {
				removeServer({ backupServerId: id }).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '删除成功'
						});
						getData(keyword);
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			}
		});
	};

	const onOK = () => {
		if (!selectCluster) {
			notification.error({
				message: '失败',
				description: '请选择集群'
			});
			return;
		} else {
			allocate({ clusterId: selectCluster, backupServerId }).then(
				(res) => {
					if (res.success) {
						getData('');
						notification.success({
							message: '成功',
							description: '分配成功'
						});
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				}
			);
			setClusterVisible(false);
		}
	};

	return (
		<ProPage>
			<ProHeader title="备份服务器" />
			<ProContent>
				<div className="list-header">
					<div className="list-header-left">
						<Button
							type="primary"
							onClick={() =>
								history.push(
									'/platform/backupServer/addBackupServer'
								)
							}
						>
							新增
						</Button>
						<Search
							placeholder="请输入备份服务器名称搜索"
							value={keyword}
							onChange={(e) => setKeyword(e.target.value)}
							onSearch={(value) => getData(value)}
							allowClear
							style={{ width: 260 }}
						/>
					</div>
					<div className="list-header-right">
						<Button
							icon={<ReloadOutlined />}
							onClick={() => getData(keyword)}
						></Button>
					</div>
				</div>
				<div className="cluster-list">
					{clusterList &&
						clusterList.length &&
						clusterList.map((item: any) => (
							<Button
								type="link"
								key={item.clusterName}
								onClick={() =>
									setCurrentCluster(item.clusterId)
								}
							>
								{item.clusterName}（{item.clusterServerCount}）
							</Button>
						))}
				</div>
				{!spinning ? (
					addressList.length > 0 ? (
						addressList.map((item: any) => (
							<ListPanel
								title={item.name}
								subTitle={item.serverType || '/'}
								icon={
									<img
										src={BackupServeIcon}
										style={{
											marginLeft: 13,
											marginRight: 16
										}}
									/>
								}
								className="panel-serve"
								key={item.id}
								actionRender={
									<Actions>
										<LinkButton
											onClick={(e) => {
												e.stopPropagation();
												storage.setSession(
													'backupServer',
													item
												);
												history.push(
													`/platform/backupServer/addBackupServer/${item.id}`
												);
											}}
										>
											编辑
										</LinkButton>
										<LinkButton
											onClick={(e) => {
												e.stopPropagation();
												setSelectCluster(
													item.clusterId
												);
												setBackupServerId(item.id);
												setClusterVisible(true);
											}}
										>
											分配
										</LinkButton>
										<LinkButton
											onClick={(e) => {
												e.stopPropagation();
												handleDelete(item.id);
											}}
										>
											删除
										</LinkButton>
									</Actions>
								}
								render={
									item.positionList &&
									item.positionList.length ? (
										<div className="serve-tags">
											{item.positionList.map(
												(data: any) => {
													return (
														<div
															className="serve-tag"
															key={data.id}
														>
															<p>
																<span>
																	路径名称：
																</span>
																{
																	data.backupPosition
																}
															</p>
															<p>
																<span>
																	所属项目：
																</span>
																{
																	data.projectName
																}
															</p>
														</div>
													);
												}
											)}
										</div>
									) : (
										<div
											style={{
												textAlign: 'center',
												color: 'raba(0,0,0,0.25'
											}}
										>
											暂无数据
										</div>
									)
								}
							>
								<ListCardItem
									label="所属集群"
									value={item.clusterNickName || '/'}
									width={120}
								/>
								<ListCardItem
									render={
										<div>
											{item.serverDetailList.map(
												(value: any) => {
													return (
														<p
															key={value.id}
															className="text-overflow"
															title={`${value.protocol}://${value.host}:${value.port}`}
														>
															{`${value.protocol}://${value.host}:${value.port}`}
														</p>
													);
												}
											)}
											<p className="zeus-list-card-item-label">
												备份服务器地址
											</p>
										</div>
									}
									width={200}
								/>
								<ListCardItem
									render={
										<div>
											{item.serverDetailList.map(
												(value: any) => {
													return (
														<p key={value.id}>
															{value.username}
														</p>
													);
												}
											)}
											<p className="zeus-list-card-item-label">
												备份地址用户名
											</p>
										</div>
									}
									width={160}
								/>
								<ListCardItem
									render={
										<div style={{ width: '100%' }}>
											{item.serverDetailList.map(
												(value: any, index: number) => {
													return (
														<PasswordEye
															key={index}
															value={decrypt(
																value.password,
																PRIVATE_KEY
															)}
														/>
													);
												}
											)}
											<p className="zeus-list-card-item-label">
												备份地址密码
											</p>
										</div>
									}
									width={160}
								/>
							</ListPanel>
						))
					) : (
						<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
					)
				) : (
					<Spin>
						<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
					</Spin>
				)}
				<Modal
					title="分配"
					open={clusterVisible}
					onOk={onOK}
					onCancel={() => setClusterVisible(false)}
				>
					<p style={{ marginBottom: 16 }}>集群选择</p>
					<div className="cluster-items">
						{poolList.map((item) => {
							return (
								<div
									key={item.name}
									className={`cluster-item ${
										selectCluster === item.id
											? 'active'
											: ''
									}`}
									onClick={() => setSelectCluster(item.id)}
								>
									<div className="item-icon">
										<img src={ClusterIcon} />
									</div>
									<div className="item-content">
										<p className="content-title">
											{item.name}
										</p>
										<p className="content-info">
											{item.nickname}
										</p>
									</div>
									<IconFont
										type="icon-xuanzhong"
										style={
											selectCluster === item.id
												? {
														position: 'absolute',
														right: 0,
														bottom: 0
												  }
												: {
														visibility: 'hidden'
												  }
										}
									/>
								</div>
							);
						})}
					</div>
				</Modal>
			</ProContent>
		</ProPage>
	);
}

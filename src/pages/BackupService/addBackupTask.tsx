import * as React from 'react';
import { useState, useEffect } from 'react';
import { useHistory, useParams } from 'react-router';
import { ProContent, ProHeader, ProPage } from '@/components/ProPage';
import {
	Button,
	Divider,
	Steps,
	Form,
	TimePicker,
	InputNumber,
	Checkbox,
	Radio,
	Input,
	Select,
	Card,
	notification,
	Switch
} from 'antd';
import { api } from '@/api.json';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';
import TableRadio from '@/components/TableRadio';
import { list } from '@/utils/const';
import Backup from '@/assets/images/backup.svg';
import Database from '@/assets/images/database.svg';
import BackupRule from '@/assets/images/backupRule.svg';
import BackupPosition from '@/assets/images/backupPosition.svg';
import {
	getServiceList,
	addBackupConfig,
	checkScheduleBatch,
	getClusterPosition,
	getProjectOperator
} from '@/services/backup';
import moment from 'moment';
import { weekMap, minutes } from '@/utils/const';
import { serviceOnOrOffLineTag } from '@/utils/utils';
import storage from '@/utils/storage';
import './index.less';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';
import { getComponent, getNewMiddlewareMenu } from '@/services/common';

const { Step } = Steps;
const { Group: CheckboxGroup } = Checkbox;
const { Group: RadioGroup } = Radio;
const steps = [
	{
		title: '选择数据源'
	},
	{
		title: '选择备份规则'
	},
	{
		title: '创建备份任务'
	}
];
const formItemLayout = {
	labelCol: {
		span: 3
	},
	wrapperCol: {
		span: 16
	}
};

const columns = [
	{
		title: '数据源名称',
		dataIndex: 'name',
		render: (value: string, record: any) => (
			<div className="flex-space-between">
				{value}
				{serviceOnOrOffLineTag(record.lock)}
			</div>
		)
	},
	{ title: '类型', dataIndex: 'type' },
	{ title: '实例数', dataIndex: 'podNum' },
	{ title: '所属命名空间', dataIndex: 'namespace' },
	{ title: '所属集群', dataIndex: 'clusterId' }
];
const dataType = [
	{ label: '天', value: 'day', max: 3650 },
	{ label: '周', value: 'week', max: 521 },
	{ label: '月', value: 'month', max: 120 },
	{ label: '年', value: 'year', max: 10 }
];
function AddBackupTask(): JSX.Element {
	const params: any = useParams();
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const [form] = Form.useForm();
	const [formWay] = Form.useForm();
	const history = useHistory();
	const [middlewares, setMiddleware] = useState<any[]>([]);
	const [selectText, setSelectText] = useState<string>('');
	const [isStep, setIsStep] = useState(false);
	const [current, setCurrent] = useState<number>(0);
	const [selectedRow, setSelectedRow] = useState<any[]>([]);
	const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
	const [searchText, setSearchText] = useState<string>('');
	const [backupWay, setBackupWay] = useState<string>('time');
	const [backupTime, setBackupTime] = useState<string>('time');
	const [checks, setChecks] = useState<number[]>();
	const [allChecked, setAllChecked] = useState<boolean>();
	const [dataSelect, setDataSelect] = useState<string>('day');
	const [formData, setFormData] = useState<any>();
	const [formWayData, setFormWayData] = useState<any>();
	const [tableData, setTableData] = useState<any>({ '': [] });
	const [addressList, setAddressList] = useState<any>();
	const [selectAddress, setSelectAddress] = useState<any>();
	const [hasTask, setHasTask] = useState<any>([]);
	const [loading, setLoading] = useState<boolean>(false);
	const [incrementChecked, setIncrementChecked] = useState<boolean>(false);
	const [backupComponent, setBackupComponent] = useState<any>();
	const [middlewareInfos, setMiddlewareInfos] = useState<any>();
	const [currentMiddlewareInfo, setCurrentMiddlewareInfo] = useState<{
		type: string;
		name: string;
		aliasName: string;
	}>();
	const next = () => {
		if (current === 0) {
			if (selectedRow) {
				setCurrent(current + 1);
			}
		}
		if (current === 1) {
			form.validateFields().then((values) => {
				setFormData(values);
				setCurrent(current + 1);
			});
		}
	};

	const prev = () => {
		if (current === 1) {
			form.validateFields().then((values) => {
				setFormData(values);
			});
		}
		if (current === 2) {
			setFormWayData(formWay.getFieldsValue());
		}
		setCurrent(current - 1);
	};

	useEffect(() => {
		if (params.name) {
			setSelectedRowKeys([params.middlewareName]);
			setSelectText(params.name);
			setCurrent(1);
			setIsStep(true);
		} else {
			getProjectOperator({
				organId,
				projectId
			}).then((res) => {
				const data = res.data.filter(
					(item: any) =>
						item.chartName === 'redis' ||
						item.chartName === 'elasticsearch' ||
						item.chartName === 'mysql' ||
						item.chartName === 'postgresql' ||
						item.chartName === 'kafka' ||
						item.chartName === 'zookeeper'
				);
				setMiddleware(data);
				setSelectText(data[0]?.chartName);
			});
			getComponent({
				clusterId: '*',
				componentName: 'middlewarebackup-controller'
			}).then((res) => {
				if (res.success) {
					setBackupComponent(res.data);
				}
			});
			getNewMiddlewareMenu({ organId, projectId }).then((res) => {
				const list_temp: any = [];
				const a = res.data.map((item: any) => {
					const sub_menu_list_temp = item.subMenu.map((i: any) => {
						i.parentType = item.name;
						return i;
					});
					console.log(sub_menu_list_temp);
					list_temp.push(...sub_menu_list_temp);
				});
				console.log(list_temp);
				setMiddlewareInfos(list_temp);
			});
		}
	}, []);
	const getTableData = () => {
		setLoading(true);
		getServiceList({
			organId,
			projectId,
			clusterId: '*',
			namespace: '*',
			type: params.name || selectText,
			keyword: searchText
		}).then((res) => {
			setLoading(false);
			setTableData({
				[params.name || selectText]: res.data
			});
		});
	};

	useEffect(() => {
		setTableData({ '': [] });
		if (selectText && current === 0) {
			getTableData();
			const current_middleware = middlewareInfos?.find(
				(item: any) =>
					item.name === selectText || item.name === params.name
			);
			console.log(current_middleware);
			setCurrentMiddlewareInfo({
				type: current_middleware?.parentType,
				name: current_middleware?.name,
				aliasName: current_middleware?.aliasName
			});
		}
	}, [searchText, selectText, current, middlewareInfos]);

	useEffect(() => {
		if (current === 1) {
			form.setFieldsValue(formData);
			getTasks();
		}
		if (current === 2) {
			formWay.setFieldsValue(formWayData);
			getClusterPosition({
				organId: organId,
				projectId: projectId,
				clusterId: selectedRow?.[0]?.clusterId || params.clusterId,
				namespace: params.namespace || selectedRow[0]?.namespace,
				type: params.name || selectedRow?.[0]?.type,
				middlewareName: params.middlewareName || selectedRow?.[0]?.name
			}).then((res) => {
				setAddressList(res.data);
			});
		}
	}, [current]);

	const renderStep = () => {
		switch (current) {
			case 0:
				return (
					<div>
						<TableRadio
							showHeader
							label="数据库源名称"
							loading={loading}
							rowKey={(record: any) => record.name}
							select={{
								value: selectText,
								onChange: (value: string) => {
									setSelectText(value);
									setSelectedRow([]);
									setSelectedRowKeys([]);
								},
								style: {
									marginRight: '16px',
									width: '120px'
								},
								options: middlewares?.map((item) => {
									return {
										label: item.name,
										value: item.chartName
									};
								})
							}}
							search={{
								placeholder: '请输入关键字搜索',
								onSearch: (value: string) =>
									setSearchText(value),
								style: {
									width: '200px'
								}
							}}
							showRefresh
							onRefresh={getTableData}
							columns={columns}
							dataSource={tableData[params.name || selectText]}
							selectedRow={selectedRow}
							setSelectedRow={setSelectedRow}
							selectedRowKeys={selectedRowKeys}
							setSelectedRowKeys={setSelectedRowKeys}
							backupComponent={backupComponent}
							middlewareInfo={currentMiddlewareInfo}
						/>
					</div>
				);
			case 1:
				return (
					<div>
						<h2>备份规则</h2>
						<Form
							{...formItemLayout}
							form={form}
							style={{ marginTop: '24px' }}
							labelAlign="left"
						>
							<Form.Item
								label="备份方式"
								required
								name="way"
								rules={[
									{
										required: true,
										message: '请选择备份方式'
									}
								]}
								initialValue="time"
							>
								<RadioGroup
									value={backupWay}
									onChange={(e) =>
										setBackupWay(e.target.value)
									}
								>
									<Radio value="time">周期备份</Radio>
									<Radio value="one">单次备份</Radio>
								</RadioGroup>
							</Form.Item>
							{backupWay === 'time' ? (
								<Form.Item
									label="备份保留时间"
									name="retentionTime"
									rules={[
										{
											required: true,
											message: '备份保留时间不能为空'
										},
										{
											max: dataType.find(
												(item: any) =>
													item.value === dataSelect
											)?.max,
											type: 'number',
											message: '保留时间最长为10年'
										},
										{
											min: 0,
											type: 'number',
											message: '保留时间不能小于0'
										}
									]}
								>
									<InputNumber
										type="inline"
										min={1}
										addonAfter={
											<Select
												value={dataSelect}
												onChange={(value) => {
													setDataSelect(value);
													form.validateFields([
														'dateUnit'
													]);
												}}
												dropdownMatchSelectWidth={false}
											>
												{dataType?.map((item: any) => {
													return (
														<Select.Option
															key={item.value}
															value={item.value}
														>
															{item.label}
														</Select.Option>
													);
												})}
											</Select>
										}
									/>
								</Form.Item>
							) : null}
							{backupWay === 'time' ||
							backupTime === 'onetime' ? (
								<>
									<Form.Item
										label="备份周期"
										className="check-form"
										required
									>
										<Form.Item>
											<Checkbox
												style={{ marginRight: '12px' }}
												onChange={(
													value: CheckboxChangeEvent
												) => {
													value.target.checked
														? setChecks([
																0, 1, 2, 3, 4,
																5, 6
														  ])
														: setChecks([]);
													value.target.checked
														? form.setFieldsValue({
																cycle: [
																	0, 1, 2, 3,
																	4, 5, 6
																]
														  })
														: form.setFieldsValue({
																cycle: []
														  });
													form.validateFields([
														'cycle'
													]);
													setAllChecked(
														value.target.checked
													);
												}}
												checked={allChecked}
											>
												全选
											</Checkbox>
										</Form.Item>
										<Form.Item
											name="cycle"
											rules={[
												{
													required: true,
													message: '备份周期不能为空'
												}
											]}
										>
											<CheckboxGroup
												options={list}
												value={checks}
												onChange={(
													value: CheckboxValueType[]
												) => {
													setChecks(
														value as number[]
													);
													(value as number[])
														.sort(
															(
																a: number,
																b: number
															) => a - b
														)
														.join(',') ===
													'0,1,2,3,4,5,6'
														? setAllChecked(true)
														: setAllChecked(false);
												}}
											/>
										</Form.Item>
									</Form.Item>
									<Form.Item
										label="备份时间"
										name="backupTime"
										rules={[
											{
												required: true,
												message: '备份时间不能为空'
											}
										]}
									>
										<TimePicker
											showNow={false}
											format="HH:mm"
										/>
									</Form.Item>
									{selectedRow[0]?.type === 'mysql' ||
									params.name === 'mysql' ||
									selectedRow[0]?.type === 'postgresql' ||
									params.name === 'postgresql' ? (
										<Form.Item
											label="是否开启增量备份"
											name="increment"
											initialValue={false}
											valuePropName="checked"
											required
										>
											<Switch
												checked={incrementChecked}
												onChange={(checked) =>
													setIncrementChecked(checked)
												}
											/>
										</Form.Item>
									) : null}
									{incrementChecked ? (
										<Form.Item
											label="增量备份间隔时间"
											name="time"
											rules={[
												{
													required: true,
													message:
														'请选择增量备份间隔时间'
												}
											]}
											extra="分/次"
											className="pause-extra"
										>
											<Select
												style={{
													width: 150,
													marginRight: 8
												}}
											>
												{minutes.map((item) => {
													return (
														<Select.Option
															key={item}
															value={item}
														>
															{item}
														</Select.Option>
													);
												})}
												分/次
											</Select>
										</Form.Item>
									) : null}
								</>
							) : null}
						</Form>
					</div>
				);
			case 2:
				return (
					<div>
						<Form
							{...formItemLayout}
							form={formWay}
							labelAlign="left"
						>
							<Form.Item
								label="备份任务名称"
								name="taskName"
								rules={[
									{
										required: true,
										message: '请输入备份任务名称'
									},
									{
										max: 64,
										type: 'string',
										message: '备份任务名称长度不能超过64'
									}
								]}
								getValueFromEvent={(e) =>
									e.target.value.replace(/(^\s*)|(\s*$)/g, '')
								}
							>
								<Input
									placeholder="XXX计划"
									style={{ width: 260 }}
								/>
							</Form.Item>
							<Form.Item label="备份方式" required>
								<div className="backup-way">
									<Card
										title={
											<div>
												<img src={Database} />
												<span>1.数据源</span>
											</div>
										}
									>
										<p>
											数据源类型：
											{params.name ||
												selectedRow?.[0]?.type}
										</p>
										<p>
											数据源名称：
											{params.middlewareName ||
												selectedRow.map(
													(item: any, index: any) => {
														return (
															<>
																{' '}
																{index !==
																selectedRow.length -
																	1 ? (
																	<span>
																		{
																			item.name
																		}
																		,
																	</span>
																) : (
																	<span>
																		{
																			item.name
																		}
																	</span>
																)}
															</>
														);
													}
												)}
										</p>
										{!params.name ? (
											<Button
												type="primary"
												style={{ marginTop: '24px' }}
												onClick={() => {
													setCurrent(0);
													setFormWayData(
														formWay.getFieldsValue()
													);
												}}
											>
												修改
											</Button>
										) : null}
									</Card>
									<Card
										title={
											<div>
												<img src={BackupRule} />
												<span>2.备份规则</span>
											</div>
										}
									>
										<p>
											备份方式：
											{formData.way === 'time'
												? '周期备份'
												: '单次备份'}
										</p>
										<p
											style={{
												visibility:
													backupWay === 'one'
														? 'hidden'
														: 'visible'
											}}
										>
											备份时间：
											{formData.rule !== 'now'
												? '每周'
												: ''}
											{formData.rule !== 'now'
												? formData.cycle
														?.map(
															(item: string) =>
																weekMap[item]
														)
														.join('、')
												: '/'}
											{formData.rule !== 'now'
												? `（${
														moment(
															formData.backupTime
														).get('hour') >= 10
															? moment(
																	formData.backupTime
															  ).get('hour')
															: '0' +
															  moment(
																	formData.backupTime
															  ).get('hour')
												  }:${
														moment(
															formData.backupTime
														).get('minute') >= 10
															? moment(
																	formData.backupTime
															  ).get('minute')
															: '0' +
															  moment(
																	formData.backupTime
															  ).get('minute')
												  }）`
												: ''}
										</p>
										<Button
											type="primary"
											style={{ marginTop: '24px' }}
											onClick={() => {
												setCurrent(1);
												setFormWayData(
													formWay.getFieldsValue()
												);
											}}
										>
											修改
										</Button>
									</Card>
									<Card
										title={
											<div>
												<img src={BackupPosition} />
												<span>3.备份位置</span>
											</div>
										}
									>
										<span>备份位置：</span>
										<Form.Item
											name="backupPositionId"
											rules={[
												{
													required: true,
													message: '请选择备份位置'
												}
											]}
										>
											<Select
												placeholder="请选择备份位置"
												value={selectAddress}
												onChange={(value) => {
													setSelectAddress(value);
												}}
												style={{ width: '150px' }}
												dropdownMatchSelectWidth={false}
											>
												{addressList?.map(
													(item: any) => {
														return (
															<Select.Option
																key={item.id}
																value={item.id}
															>
																{`${item.backupServerName} - ${item.name}`}
															</Select.Option>
														);
													}
												)}
											</Select>
										</Form.Item>
									</Card>
								</div>
							</Form.Item>
						</Form>
					</div>
				);
		}
	};

	const getTasks = () => {
		const sendData: any = {
			clusterId: params.clusterId || '*',
			namespace: params.namespace || '*',
			type: params.type || selectedRow[0]?.type
		};
		if (params.middlewareName) {
			sendData.middlewareName = params.middlewareName;
		} else {
			const listArray: any = [];
			selectedRow.forEach((element: any) => {
				listArray.push({
					clusterId: element.clusterId,
					namespace: element.namespace,
					middlewareName: element.name
				});
			});
			sendData.middlewares = listArray;
		}
		checkScheduleBatch(sendData).then(async (res) => {
			if (res.success) {
				const list = res.data || [];
				const listFilter: any = [];
				for (const key in list) {
					if (list[key]) {
						listFilter.push(key);
					}
				}
				await setHasTask(listFilter);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};

	const handleSubmit = () => {
		formWay.validateFields().then(async (values) => {
			const minute = moment(formData.backupTime).get('minute');
			const hour = moment(formData.backupTime).get('hour');
			const week = formData.cycle?.join(',');
			const cron = `${minute} ${hour} ? ? ${week}`;
			const sendData = {
				...values,
				...formData,
				clusterId: selectedRow[0]?.clusterId || params.clusterId,
				namespace: params.namespace || selectedRow[0]?.namespace,
				middlewareName: params.middlewareName || selectedRow[0]?.name,
				type: selectedRow[0]?.type || params.name
			};
			if (params.middlewareName) {
				sendData.middlewareName = params.middlewareName;
			} else {
				const nameList: any = [];
				selectedRow.forEach((element: any) => {
					nameList.push({
						clusterId: element.clusterId,
						middlewareName: element.name,
						namespace: element.namespace
					});
				});
				sendData.middlewares = nameList;
			}
			if (formData.way === 'time') {
				sendData.cron = cron;
			}
			if (formData.retentionTime) {
				sendData.dateUnit = dataSelect;
			}
			if (formData.increment) {
				sendData.time = formData.time + 'm';
			}
			if (hasTask.length > 0 && formData.way === 'time') {
				notification.error({
					message: '失败',
					description: `服务${hasTask.join(
						'，'
					)}已有周期备份任务，不可多次创建`
				});
				return;
			}
			delete sendData.cycle;
			delete sendData.backupTime;
			await ExecuteOrderFuc();
			addBackupConfig(sendData).then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '创建成功'
					});
					history.goBack();
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		});
	};

	return (
		<ProPage>
			<ProHeader
				title="新增备份任务"
				avatar={{
					children: <img src={Backup} />,
					shape: 'square',
					size: 48,
					style: { background: '#f5f5f5' }
				}}
				onBack={() => {
					if (params.name) {
						history.goBack();
					} else {
						!isStep ? history.goBack() : setIsStep(false);
					}
				}}
			/>
			<ProContent>
				<h2>数据源类型</h2>
				{!isStep ? (
					<div className="cards">
						{middlewares?.map((item) => {
							return (
								<div key={item.id} className="card-box">
									<div
										className={`card ${
											selectText === item.chartName
												? 'active'
												: ''
										}`}
										onClick={() => {
											setSelectText(item.chartName);
											if (selectText !== item.chartName) {
												setCurrent(0);
												setSelectedRow([]);
												setSelectedRowKeys([]);
											}
										}}
									>
										<img
											src={`${api}/images/middleware/${item.imagePath}`}
										/>
									</div>
									<div className="card-title">
										{item.name}
									</div>
								</div>
							);
						})}
					</div>
				) : (
					<>
						<Steps current={current}>
							{steps?.map((item) => (
								<Step key={item.title} title={item.title} />
							))}
						</Steps>
						<div className="steps-content">{renderStep()}</div>
					</>
				)}
				<Divider
					style={{ marginTop: '40px', display: 'inline-block' }}
				></Divider>
				{!isStep ? (
					<div>
						<Button type="primary" onClick={() => setIsStep(true)}>
							开始备份
						</Button>
						<Button
							style={{ marginLeft: '8px' }}
							onClick={() => history.goBack()}
						>
							取消
						</Button>
					</div>
				) : (
					<div className="steps-action">
						{(current > 1 || (!params.name && current > 0)) && (
							<Button
								style={{ margin: '0 8px' }}
								onClick={() => prev()}
							>
								上一步
							</Button>
						)}
						{current < steps.length - 1 && (
							<Button
								type="primary"
								onClick={() => next()}
								disabled={current === 0 && !selectedRow.length}
							>
								下一步
							</Button>
						)}
						{current === steps.length - 1 && (
							<Button type="primary" onClick={handleSubmit}>
								完成
							</Button>
						)}
						<Button
							style={{ marginLeft: '8px' }}
							onClick={() => {
								if (params.name) {
									history.goBack();
								} else {
									!isStep
										? history.goBack()
										: setIsStep(false);
								}
							}}
						>
							取消
						</Button>
					</div>
				)}
			</ProContent>
		</ProPage>
	);
}

export default AddBackupTask;

.cards {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 32px;
	column-gap: 108px;
	row-gap: 36px;
	.card-box {
		text-align: center;
		&:first-child {
			margin-left: 0;
		}
		img {
			width: 64px;
			height: 64px;
		}
	}
	.card {
		width: 100px;
		height: 100px;
		line-height: 100px;
		text-align: center;
		background: #ffffff;
		box-shadow: 0px 2px 7px 0px rgba(0, 0, 0, 0.09);
		border-radius: 4px;
		border: 1px solid #e7e9eb;
		&:hover {
			background: #f8fbfd;
			border: 1px solid #226ee7;
		}
		&.active {
			background: #f8fbfd;
			border: 1px solid #226ee7;
		}
	}
	.card-title {
		font-size: 16px;
		margin-top: 8px;
		font-family: PingFangSC-Semibold, PingFang SC;
		font-weight: 600;
		color: #333333;
		line-height: 22px;
	}
}
.steps-content {
	margin-top: 16px;
}
.service-type {
	display: flex;
	label {
		width: 100px;
	}
	.service-cards {
		display: flex;
		.ant-card {
			margin-right: 24px;
			text-align: center;
			&:hover {
				background: #f8fbfd;
				border: 1px solid #226ee7;
			}
			&.active-card {
				background: #f8fbfd;
				border: 1px solid #226ee7;
			}
		}
	}
}
.check-form {
	.ant-form-item {
		margin-bottom: 0;
	}
	.ant-form-item-control-input-content {
		display: flex;
	}
	.ant-form-item-explain-error {
		margin-left: -68px;
	}
}
.backup-way {
	display: flex;
	align-items: stretch;
	.ant-card {
		flex: 1;
		.ant-card-head {
			background: #ececec;
			img {
				margin-right: 24px;
			}
		}
	}
}
h2 {
	font-size: @font-2 !important;
	font-family: PingFangSC-Medium, PingFang SC;
	font-weight: @font-weight !important;
	color: @text-color-title !important;
	line-height: 22px;
	margin-bottom: 16px !important;
	display: flex;
	align-items: center;
	&::before {
		content: '';
		display: inline-block;
		width: 1px;
		height: 14px;
		border: 1px solid #0064c8;
		margin-right: 5px;
	}
}
.list-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16px;
	.list-header-left {
		display: flex;
		button {
			margin-right: 16px;
		}
	}
}

.flex-form {
	display: flex;
	.ant-form-item {
		flex: 1;
		&:first-of-type {
			margin: 0;
		}
		// margin-left: 8px;
		margin-bottom: 0;
	}
}
.ant-table-row.disabled {
	background-color: #fbfbfb;
	color: #ccc;
	td:first-of-type {
		.name-link {
			color: #ccc;
		}
	}
}
.ant-table-expanded-row {
	.ant-table {
		background-color: #fbfbfb;
	}
}
.pause-extra {
	.ant-form-item-extra {
		margin: -30px 0 0 158px;
	}
}
.cluster-items {
	display: flex;
	flex-wrap: wrap;
	.cluster-item {
		width: calc(50% - 16px);
		margin-bottom: 16px;
		display: flex;
		box-sizing: border-box;
		background: #ffffff;
		box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.12);
		border: 1px solid transparent;
		border-radius: 4px;
		cursor: pointer;
		position: relative;
		&:nth-of-type(n) {
			margin-right: 16px;
		}
		&:hover,
		&.active {
			border: 1px solid @blue-5;
		}
		.item-icon {
			line-height: 60px;
			padding: 0 13px;
			text-align: center;
			background: rgba(223, 223, 223, 0.2);
			border-radius: 4px;
		}
		.item-content {
			width: calc(100% - 60px);
			padding: 8px;
			.content-title {
				font-size: @font-1;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: #333333;
				line-height: @line-height-1;
			}
			.content-info {
				font-size: @font-1;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: rgba(51, 51, 51, 0.6);
				line-height: @line-height-1;
				text-overflow: ellipsis;
				white-space: nowrap;
				overflow: hidden;
			}
		}
	}
}
.cluster-list {
	padding: 8px 12px;
	margin-bottom: 12px;
	background: #f8f8f9;
}
.panel-serve {
	.zeus-list-card-head {
		width: 200px;
	}
	.zeus-list-card-action {
		min-width: 156px;
	}
}
.serve-tags {
	display: flex;
	flex-wrap: wrap;
	.serve-tag {
		padding: 8px 12px;
		width: 151px;
		color: @black-2;
		border-radius: 2px;
		background: #ffffff;
		margin-right: 17px;
		margin-top: 17px;
		&:nth-of-type(7n) {
			margin-right: 0;
		}
		&:nth-of-type(-n + 7) {
			margin-top: 0;
		}
		border: 1px solid rgba(151, 151, 151, 0.12);
		span {
			color: @black-4;
		}
	}
}

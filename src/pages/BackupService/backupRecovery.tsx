import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router';
import { useHistory } from 'react-router';
import { ProPage, ProHeader, ProContent } from '@/components/ProPage';
import { Radio, notification, Button, Divider, Tooltip, Table } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';

import { getBackups, getIncBackup, getIncrBackups } from '@/services/backup';
import { getZones } from '@/services/activeActive';
import { middlewareProps } from '@/pages/ServiceList/service.list';
import { getCanReleaseMiddleware } from '@/services/middleware';
import { ZonesItem } from '@/pages/ActiveActive/activeActive';

import { statusBackupRender } from '@/utils/utils';
import moment from 'moment';
import storage from '@/utils/storage';
import { filtersProps } from '@/types/comment';

const columns = [
	{ title: '备份记录', dataIndex: 'recordName' },
	{ title: '备份状态', dataIndex: 'phrase', render: statusBackupRender },
	{
		title: '备份时间',
		dataIndex: 'creationTime',
		sorter: (a: any, b: any) =>
			moment(a.creationTime).unix() - moment(b.creationTime).unix()
	}
];

function ProBackupBask(): JSX.Element {
	const params: any = useParams();
	const history = useHistory();
	const {
		backupId,
		clusterId,
		middlewareName,
		namespace,
		type,
		name,
		serviceGroup
	} = params;
	const [list, setList] = useState();
	const [zones, setZones] = useState<filtersProps[]>([]);
	const [activeArea, setActiveArea] = useState<string>();
	const [recoveryType, setRecoveryType] = useState<string>('time');
	const [middlewareInfo, setMiddlewareInfo] = useState<middlewareProps>();
	const backupDetail = storage.getLocal('backupDetail');
	const [selectedRow, setSelectedRow] = useState<any>();
	const [selectedRowKeys, setSelectedRowKeys] = useState<any>();
	const [incData, setIncData] = useState<any[]>([]);
	const [incList, setIncList] = useState<any[]>([]);

	useEffect(() => {
		(!backupDetail.newBackupName ||
			(backupDetail.newBackupName &&
				!backupDetail.startTime &&
				!backupDetail.endTime)) &&
			setRecoveryType('record');
		async function getData() {
			const res1 = await getCanReleaseMiddleware({
				clusterId,
				type: type || name
			});
			if (res1.success) {
				setMiddlewareInfo(res1.data);
			} else {
				notification.error({
					message: '失败',
					description: res1.errorMsg
				});
			}
			// * 当前备份详情是双活时获取可用区接口
			if (backupDetail.activeActive) {
				const res2 = await getZones({ clusterId });
				if (res2.success) {
					setZones(
						res2.data
							.filter((item: ZonesItem) => item.name !== 'zoneC')
							.map((item: ZonesItem) => {
								return {
									label: item.aliasName,
									value: item.name
								};
							})
					);
					setActiveArea(
						res2.data.filter(
							(item: ZonesItem) => item.name !== 'zoneC'
						)[0].name
					);
				} else {
					setZones([]);
					notification.error({
						message: '失败',
						description: res2.errorMsg
					});
				}
			} else {
				// * 否则直接获取备份记录列表
				const res3 = await getBackups({
					type: type || name,
					backupId,
					clusterId,
					namespace,
					activeArea: '',
					middlewareName,
					backupMode: backupDetail.backupMode
				});
				if (res3.success) {
					setList(res3.data);
					setSelectedRow(
						res3.data.filter(
							(item: any) => item.phrase === 'Success'
						)[0]
					);
					setSelectedRowKeys([
						res3.data.filter(
							(item: any) => item.phrase === 'Success'
						)[0]?.recordName
					]);
				} else {
					notification.error({
						message: '失败',
						description: res3.errorMsg
					});
				}
			}
		}
		getData();
		getIncBackup({
			clusterId: params.clusterId,
			namespace: params.namespace,
			backupId: params.backupId
		}).then((res) => {
			if (res.success) {
				setIncData(res.data);
			}
		});
		getIncrBackups({
			backupId: backupId,
			backupMode: backupDetail.backupMode,
			clusterId: clusterId,
			namespace: namespace,
			middlewareName: middlewareName,
			type: type || name
		}).then((res) => {
			if (res.success) {
				setIncList(res.data);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	}, []);

	const releaseMiddleware = () => {
		switch (backupDetail.sourceType) {
			case 'mysql':
				history.push(
					`/project/${serviceGroup}/mysql/MySQL/mysqlBackup/${
						middlewareInfo?.chartVersion
					}/${selectedRow?.sourceName || backupDetail.sourceName}/${
						selectedRow?.namespace || backupDetail.namespace
					}/${params.clusterId || backupDetail.clusterId}`
				);
				if (
					recoveryType === 'time' &&
					backupDetail.backupMode === 'period'
				) {
					storage.setLocal('backupDetail', {
						...backupDetail,
						recoveryType: 'time',
						backupIncName: backupDetail.activeActive
							? incData.find(
									(item: any) =>
										item.activeArea === activeArea
							  )?.backupName
							: incData[0]?.backupName,
						startTime: backupDetail.activeActive
							? incData.find(
									(item: any) =>
										item.activeArea === activeArea
							  )?.startTime
							: incData[0]?.startTime,
						endTime: backupDetail.activeActive
							? incData.find(
									(item: any) =>
										item.activeArea === activeArea
							  )?.endTime
							: incData[0]?.endTime
					});
				}
				break;
			case 'postgresql':
				history.push(
					`/project/${serviceGroup}/postgresql/PostgreSQL/postgresqlBackup/${
						middlewareInfo?.chartVersion
					}/${selectedRow?.sourceName || backupDetail.sourceName}/${
						selectedRow?.namespace || backupDetail.namespace
					}/${params.clusterId || backupDetail.clusterId}`
				);
				if (
					recoveryType === 'time' &&
					backupDetail.backupMode === 'period'
				) {
					storage.setLocal('backupDetail', {
						...backupDetail,
						recoveryType: 'time',
						backupIncName: backupDetail.activeActive
							? incData.find(
									(item: any) =>
										item.activeArea === activeArea
							  )?.backupName
							: incData[0]?.backupName,
						startTime: backupDetail.activeActive
							? incData.find(
									(item: any) =>
										item.activeArea === activeArea
							  )?.startTime
							: incData[0]?.startTime,
						endTime: backupDetail.activeActive
							? incData.find(
									(item: any) =>
										item.activeArea === activeArea
							  )?.endTime
							: incData[0]?.endTime
					});
				}
				break;
			case 'redis':
				history.push(
					`/project/${serviceGroup}/redis/Redis/redisBackup/${
						middlewareInfo?.chartVersion
					}/${selectedRow?.sourceName}/${selectedRow?.namespace}/${
						params.clusterId || backupDetail.clusterId
					}`
				);
				break;
			case 'elasticsearch':
				history.push(
					`/project/${serviceGroup}/elasticsearch/Elasticsearch/elasticsearchBackup/${
						middlewareInfo?.chartVersion
					}/${selectedRow?.sourceName}/${selectedRow?.namespace}/${
						params.clusterId || backupDetail.clusterId
					}`
				);
				break;
			case 'rocketmq':
				history.push(
					`/project/${serviceGroup}/rocketmq/rocketMQ/rocketmqBackup/${
						middlewareInfo?.chartVersion
					}/${selectedRow?.sourceName}/${selectedRow?.namespace}/${
						params.clusterId || backupDetail.clusterId
					}`
				);
				break;
			case 'zookeeper':
				history.push(
					`/project/${serviceGroup}/zookeeper/Zookeeper/zookeeperBackup/${
						middlewareInfo?.chartVersion
					}/${selectedRow?.sourceName}/${selectedRow?.namespace}/${
						params.clusterId || backupDetail.clusterId
					}`
				);
				break;
			case 'kafka':
				history.push(
					`/project/${serviceGroup}/kafka/Kafka/kafkaBackup/${
						middlewareInfo?.chartVersion
					}/${selectedRow?.sourceName}/${selectedRow?.namespace}/${
						params.clusterId || backupDetail.clusterId
					}`
				);
				break;
		}
		storage.setLocal('backupDetail', {
			backupRecordName: selectedRow?.backupName,
			...backupDetail,
			activeArea: backupDetail.activeActive ? activeArea : '',
			recoveryType,
			backupIncName: backupDetail.activeActive
				? incData.find((item: any) => item.activeArea === activeArea)
						?.backupName
				: incData[0]?.backupName,
			startTime: backupDetail.activeActive
				? incData.find((item: any) => item.activeArea === activeArea)
						?.startTime
				: incData[0]?.startTime,
			endTime: backupDetail.activeActive
				? incData.find((item: any) => item.activeArea === activeArea)
						?.endTime
				: incData[0]?.endTime
		});
	};

	useEffect(() => {
		if (activeArea) {
			getBackups({
				type: type || name,
				backupId,
				clusterId,
				namespace,
				activeArea: activeArea,
				middlewareName,
				backupMode: backupDetail.backupMode
			}).then((res) => {
				if (res.success) {
					setList(res.data);
					setSelectedRow(
						res.data.filter(
							(item: any) => item.phrase === 'Success'
						)[0]
					);
					setSelectedRowKeys([
						res.data.filter(
							(item: any) => item.phrase === 'Success'
						)[0]?.recordName
					]);
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		}
	}, [activeArea]);

	useEffect(() => {
		if (activeArea && incData.length) {
			!(
				incData.find((item: any) => item.activeArea === activeArea)
					?.startTime &&
				incData.find((item: any) => item.activeArea === activeArea)
					?.endTime
			) && setRecoveryType('record');
		}
	}, [activeArea, incData]);

	return (
		<ProPage>
			<ProHeader
				title="备份恢复"
				subTitle="发布中间件需要使用的备份任务"
				onBack={() => history.goBack()}
			/>
			<ProContent>
				{type === 'mysql' ||
				type === 'postgresql' ||
				name === 'mysql' ||
				name === 'postgresql' ? (
					<>
						{backupDetail.activeActive && (
							<div className="mb-24">
								<h2>
									选择可用区
									<Tooltip title="由于当前选择的是基于双活备份任务的克隆恢复，需要选择使用某一可用区的数据作为克隆对象">
										<ExclamationCircleOutlined
											style={{
												fontSize: 14,
												marginLeft: 8,
												cursor: 'pointer'
											}}
										/>
									</Tooltip>
								</h2>
								<Radio.Group
									value={activeArea}
									onChange={(e) =>
										setActiveArea(e.target.value)
									}
									options={zones}
								/>
							</div>
						)}
						{backupDetail.backupMode === 'period' && (
							<>
								<h2>恢复方式</h2>
								<Radio.Group
									onChange={(e) =>
										setRecoveryType(e.target.value)
									}
									value={recoveryType}
								>
									{!incData.length ? null : (incData.find(
											(item) =>
												item.activeArea === activeArea
									  )?.startTime &&
											incData.find(
												(item) =>
													item.activeArea ===
													activeArea
											)?.endTime) ||
									  (!activeArea &&
											incList.filter(
												(item) =>
													item.phrase === 'Success'
											).length) ? (
										<Radio value="time">
											选择时间点恢复
										</Radio>
									) : (
										<Tooltip
											title={`当前${
												activeArea ? '可用区' : ''
											}暂无已经成功的全量及增量备份记录`}
										>
											<Radio value="time" disabled>
												选择时间点恢复
											</Radio>
										</Tooltip>
									)}
									<Radio value="record">
										选择备份记录恢复
									</Radio>
								</Radio.Group>
							</>
						)}
						{recoveryType === 'record' ? (
							<Table
								dataSource={list}
								columns={columns}
								rowKey="recordName"
								style={{ marginTop: 16 }}
								rowSelection={{
									type: 'radio',
									selectedRowKeys: selectedRowKeys,
									onChange: (
										selectedRowKeys: any,
										selectedRows: any
									) => {
										setSelectedRow(selectedRows[0]);
										setSelectedRowKeys(selectedRowKeys);
									},
									getCheckboxProps: (record: any) => ({
										disabled: record.phrase !== 'Success'
									})
								}}
							/>
						) : null}
					</>
				) : (
					<>
						{backupDetail.activeActive && (
							<div className="mb-24">
								<h2>
									选择可用区
									<Tooltip title="由于当前选择的是基于双活备份任务的克隆恢复，需要选择使用某一可用区的数据作为克隆对象">
										<ExclamationCircleOutlined
											style={{
												fontSize: 14,
												marginLeft: 8,
												cursor: 'pointer'
											}}
										/>
									</Tooltip>
								</h2>
								<Radio.Group
									value={activeArea}
									onChange={(e) =>
										setActiveArea(e.target.value)
									}
									options={zones}
								/>
							</div>
						)}
						<Table
							dataSource={list}
							columns={columns}
							rowKey="recordName"
							style={{ marginTop: 16 }}
							rowSelection={{
								type: 'radio',
								selectedRowKeys: selectedRowKeys,
								onChange: (
									selectedRowKeys: any,
									selectedRows: any
								) => {
									setSelectedRow(selectedRows[0]);
									setSelectedRowKeys(selectedRowKeys);
								},
								getCheckboxProps: (record: any) => ({
									disabled: record.phrase !== 'Success'
								})
							}}
						/>
					</>
				)}
				<Divider />
				<div>
					<Button
						type="primary"
						style={{ marginRight: 16 }}
						onClick={() => {
							if (recoveryType === 'record') {
								if (selectedRow) {
									releaseMiddleware();
								} else {
									notification.error({
										message: '失败',
										description: '请选择备份源'
									});
								}
							} else {
								if (
									backupDetail.sourceType === 'mysql' ||
									backupDetail.sourceType === 'postgresql'
								) {
									releaseMiddleware();
								} else {
									if (
										selectedRow ||
										backupDetail.backupMode === 'single'
									) {
										releaseMiddleware();
									} else {
										notification.error({
											message: '失败',
											description: '请选择备份源'
										});
									}
								}
							}
						}}
					>
						确认
					</Button>
					<Button onClick={() => history.goBack()}>取消</Button>
				</div>
			</ProContent>
		</ProPage>
	);
}

export default ProBackupBask;

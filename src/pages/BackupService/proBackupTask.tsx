import React, { useEffect, useState } from 'react';
import { Button, Modal, Select, Space, Tooltip, notification } from 'antd';
import { ProPage, ProHeader, ProContent } from '@/components/ProPage';
import Backup from '@/assets/images/backup.svg';
import storage from '@/utils/storage';
import ProTable from '@/components/ProTable';
import { DownOutlined, RightOutlined } from '@ant-design/icons';
import { useHistory } from 'react-router';
import Actions from '@/components/Actions';
import { BackupRecordItem } from '../ServiceListDetail/BackupRecovery/backup';
import { statusBackupRender, statusRecordRender } from '@/utils/utils';
import { backupTaskStatus } from '@/utils/const';
import { getClusters, getComponent } from '@/services/common';
import { clusterType } from '@/types';
import { deleteBackupTasks } from '@/services/backup';
import { getProjectBackupTasks } from '@/services/project';
const { confirm } = Modal;
function ProBackupTask(): JSX.Element {
	const history = useHistory();
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const [loading, setLoading] = useState<boolean>(false);
	const [clusters, setClusters] = useState<clusterType[]>([]);
	const [backups, setBackups] = useState<BackupRecordItem[]>([]);
	const [componentInfo, setComponentInfo] = useState<any>();
	const [curClusterId, setCurClusterId] = useState<string>('*');
	const [keyword, setKeyword] = useState<string>('');
	useEffect(() => {
		let mounted = true;
		async function getAllData() {
			setLoading(true);
			try {
				const res1 = await getClusters({ organId, projectId });
				if (res1.success) {
					setClusters(res1.data);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res1.errorMsg}</p>
								<p>{res1.errorDetail}</p>
							</>
						)
					});
				}
				const res2 = await getComponent({
					clusterId: '*',
					componentName: 'middlewarebackup-controller'
				});
				if (res2.success) {
					setComponentInfo(res2.data);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res2.errorMsg}</p>
								<p>{res2.errorDetail}</p>
							</>
						)
					});
				}
				const res3 = await getProjectBackupTasks({
					organId,
					projectId,
					keyword: ''
				});
				if (res3.success) {
					setBackups(res3.data);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res3.errorMsg}</p>
								<p>{res3.errorDetail}</p>
							</>
						)
					});
				}
				setLoading(false);
			} catch {
				setLoading(false);
			}
		}
		if (organId && projectId) {
			if (mounted) {
				getAllData();
			}
		}
		return () => {
			mounted = false;
		};
	}, [organId, projectId]);
	const handleSelectChange = (value: string) => {
		setCurClusterId(value);
		getData(value, keyword);
	};
	const Operation = {
		primary: (
			<Space>
				<Button
					type="primary"
					onClick={() => {
						history.push(`/project/backup/addBackupTask`);
					}}
				>
					新增
				</Button>
				<label>集群:</label>
				<Select
					style={{ width: 120 }}
					dropdownMatchSelectWidth={false}
					value={curClusterId}
					onChange={handleSelectChange}
				>
					<Select.Option value="*">全部</Select.Option>
					{clusters.map((item) => {
						return (
							<Select.Option key={item.id} value={item.id}>
								{item.name}
							</Select.Option>
						);
					})}
				</Select>
			</Space>
		)
	};

	const getData = (clusterId_temp: string, value: string) => {
		setLoading(true);
		getProjectBackupTasks({
			organId,
			projectId,
			clusterId: clusterId_temp === '*' ? '' : clusterId_temp,
			keyword: value
		})
			.then((res) => {
				if (res.success) {
					setBackups(res.data);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const taskNameRender = (value: string, record: any, index: number) => {
		return (
			<span
				className={record.phrase === 'Deleting' ? '' : 'name-link'}
				onClick={() => {
					if (record.phrase === 'Deleting') return;
					history.push(
						`/project/backup/detail/${record.clusterId}/${
							record.namespace
						}/${record.sourceName}/${
							record.backupName ||
							record.middlewareBackupRecords[0].backupName
						}/${record.backupId}/${record.sourceApplicationType}/${
							record.sourceType
						}/${record.backupMode}/${
							record.sourceLocked ? 'locked' : 'unlocked'
						}`
					);
					storage.setLocal('backupDetail', {
						...record.middlewareBackupRecords[0],
						...record
					});
				}}
			>
				{value}
			</span>
		);
	};
	const positionRender = (value: string, record: any, index: number) => {
		return record?.backupAddresses.length > 1 ? (
			<Tooltip
				title={
					<>
						<div>{record?.backupAddresses[0]}</div>
						<div>{record?.backupAddresses[1]}</div>
					</>
				}
			>
				{record?.backupAddresses[0].split('(')[0] || '/'}
			</Tooltip>
		) : (
			<Tooltip title={record?.backupAddresses[0]}>
				{record?.backupAddresses[0].split('(')[0] || '/'}
			</Tooltip>
		);
	};
	const actionRender = (
		value: any,
		record: BackupRecordItem,
		index: number
	) => {
		function disabledRender() {
			if (record.phrase === 'Deleting')
				return { disabled: true, title: '' };
			if (componentInfo?.[record?.clusterId]?.status !== 3)
				return {
					disabled: true,
					title: '当前备份任务所在集群的备份控制器组件未安装，请前往平台组件页面安装相应组件'
				};
			return { disabled: false, title: '' };
		}
		return (
			<Actions>
				<Button
					type="link"
					disabled={disabledRender().disabled}
					title={disabledRender().title}
					onClick={(e) => {
						e.stopPropagation();
						confirm({
							title: '操作确认',
							content:
								record.backupMode !== 'single' ? (
									`${
										record.phrase === 'DeleteFailed'
											? '强制删除周期备份任务将删除资源并无法恢复，若存在异常，备份数据将无法删除，请手动操作'
											: '删除周期备份任务，将清除此中间件所有备份数据且无法恢复，请确认执行？'
									}`
								) : record.phrase === 'DeleteFailed' ? (
									'强制删除单次备份任务将删除资源并无法恢复，若存在异常，备份数据将无法删除，请手动操作'
								) : (
									<>
										<p>
											删除单次备份任务，将清除对应备份数据且无法恢复
										</p>
										<p>请确认执行？</p>
									</>
								),
							onOk: () => {
								const sendData = {
									clusterId: record.clusterId,
									namespace: record.namespace,
									type: record.sourceType,
									cron: record.cron || '',
									backupNameList:
										record.middlewareBackupRecords
											? record.middlewareBackupRecords.map(
													(item: BackupRecordItem) =>
														item.backupName
											  )
											: [record.backupName],
									backupId: record.backupId,
									backupMode: record.backupMode,
									addressName: record.addressName,
									schedule: record.schedule,
									backupFileName: record.backupFileName || '',
									forceDelete:
										record.phrase === 'DeleteFailed'
											? true
											: false
								};
								deleteBackupTasks(sendData)
									.then((res) => {
										if (res.success) {
											notification.success({
												message: '成功',
												description: '备份任务删除成功'
											});
										} else {
											notification.error({
												message: '失败',
												description: res.errorMsg
											});
										}
									})
									.finally(() => {
										getData(curClusterId, keyword);
									});
							}
						});
					}}
				>
					{record.phrase === 'DeleteFailed' ? '强制删除' : '删除'}
				</Button>
			</Actions>
		);
	};
	return (
		<ProPage>
			<ProHeader
				title="备份任务"
				subTitle="发布中间件需要使用的备份任务"
				avatar={{
					children: <img src={Backup} />,
					shape: 'square',
					size: 48,
					style: { background: '#f5f5f5' }
				}}
			/>
			<ProContent>
				<ProTable
					dataSource={backups}
					showRefresh
					onRefresh={() => getData(curClusterId, keyword)}
					rowKey="backupId"
					loading={loading}
					operation={Operation}
					search={{
						value: keyword,
						onChange: (e) => setKeyword(e.target.value),
						placeholder: '请输入关键字搜索',
						onSearch: (value: string) =>
							getData(curClusterId, value),
						style: { width: '360px' }
					}}
					expandIcon={({ expanded, onExpand, record }) => {
						return record.middlewareBackupRecords.length > 1 ? (
							expanded ? (
								<DownOutlined
									onClick={(e) => onExpand(record, e)}
								/>
							) : (
								<RightOutlined
									onClick={(e) => onExpand(record, e)}
								/>
							)
						) : (
							''
						);
					}}
				>
					<ProTable.Column
						title="备份任务名称"
						dataIndex="taskName"
						render={taskNameRender}
						width={160}
					/>
					<ProTable.Column
						title="状态"
						dataIndex="phrase"
						render={statusBackupRender}
						width={120}
						filterMultiple={false}
						filters={backupTaskStatus}
						onFilter={(value, record: any) =>
							value !== 'Unknown'
								? record.phrase === value
								: record.phrase !== 'Running' &&
								  record.phrase !== 'Failed' &&
								  record.phrase !== 'Success' &&
								  record.phrase !== 'Deleting' &&
								  record.phrase !== 'DeleteFailed' &&
								  record.phrase !== 'Waiting' &&
								  record.phrase !== 'RecycleFailed'
						}
					/>
					<ProTable.Column
						title="备份源名称"
						dataIndex="sourceName"
						width={160}
					/>
					<ProTable.Column
						title="备份方式"
						dataIndex="schedule"
						render={(value, record: any) =>
							record?.backupMode !== 'period'
								? '单次备份'
								: '周期备份'
						}
						width={120}
						filterMultiple={false}
						filters={[
							{ text: '周期备份', value: 'period' },
							{ text: '单次备份', value: 'single' }
						]}
						onFilter={(value, record: any) =>
							record.backupMode === value
						}
					/>
					<ProTable.Column
						title="备份位置"
						dataIndex="position"
						render={positionRender}
					/>
					<ProTable.Column
						title="最近一次备份状态"
						dataIndex="recentBackupStatus"
						render={(value, record: any, index) =>
							record?.backupMode !== 'period'
								? '/'
								: statusRecordRender(value, record, index)
						}
					/>
					<ProTable.Column
						title="最近一次备份时间"
						dataIndex="recentBackupTime"
						render={(value) => value ?? '/'}
					/>
					<ProTable.Column
						title="操作"
						render={actionRender}
						width={120}
					/>
				</ProTable>
			</ProContent>
		</ProPage>
	);
}

export default ProBackupTask;

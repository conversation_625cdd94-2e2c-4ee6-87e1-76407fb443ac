import React, { useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router';
import { notification, Modal, Tooltip } from 'antd';
import type { TablePaginationConfig } from 'antd/es/table';

import ProTable from '@/components/ProTable';
import Actions from '@/components/Actions';

import {
	controlledOperationDisabled,
	nullRender,
	statusRecordRender
} from '@/utils/utils';
import { backupRecordStatus, maintenances } from '@/utils/const';
import {
	getBackups,
	deleteBackups,
	backupCheck,
	getTaskDetail,
	getBackupCheckResult
} from '@/services/backup';
import { getZones } from '@/services/activeActive';
import { ZonesItem } from '@/pages/ActiveActive/activeActive';
import { FiltersProps } from '@/types/comment';
import storage from '@/utils/storage';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import useRefresh from '@/utils/useRefresh';
import DataCheck from './dataCheck';

const LinkButton = Actions.LinkButton;
interface FullRecordProps {
	backupComponent: boolean;
	lock: string;
}
function FullRecord(props: FullRecordProps): JSX.Element {
	const { backupComponent, lock } = props;
	const history = useHistory();
	const params: any = useParams();
	const [data, setData] = useState();
	const [zones, setZones] = useState<FiltersProps[]>([]);
	const deleteBackupRecordOperatorId = maintenances['Delete Backup Record'];
	const postgresqlDataValidityVerificationOperatorId =
		maintenances['【Postgresql】Data Validity Verification'];
	const redisDataValidityVerificationOperatorId =
		maintenances['【Redis】Data Validity Verification'];
	const backupDetail = storage.getLocal('backupDetail');
	const { backupId, clusterId, namespace, middlewareName, name } = params;
	const [visible, setVisible] = useState(false);
	const [checkRecord, setCheckRecord] = useState<any>(false);
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		getData();
		getZones({ clusterId: params.clusterId }).then((res) => {
			if (res.success) {
				setZones(
					res.data
						.filter((item: ZonesItem) => item.name !== 'zoneC')
						.map((item: ZonesItem) => {
							return {
								text: item.aliasName,
								value: item.name
							};
						})
				);
			} else {
				setZones([]);
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	}, [refreshKey]);

	const fullRecordTest = async (record: any) => {
		const sendData = {
			...params,
			middleware: params.middlewareName,
			backupType: params.backupMode,
			backupName: record.backupName
		};
		setCheckRecord(record);
		const res = await getBackupCheckResult(sendData);
		if (res.data.phase) {
			// * 存在结果，打开抽屉
			setVisible(true);
		} else {
			// * 发起一次新的验证
			Modal.confirm({
				title: '操作确认',
				content:
					'即将开始备份数据有效性验证，请确保备份任务中至少存在一条备份成功的记录。',
				onOk: () => {
					handleBackupResult();
				}
			});
		}
	};

	const handleBackupResult = async () => {
		const sendData = {
			...params,
			middleware: params.middlewareName,
			backupType: params.backupMode
		};
		await ExecuteOrderFuc();
		const res = await backupCheck(sendData);
		if (!res.success) {
			notification.error({
				message: '错误',
				description: (
					<>
						<p>{res.errorMsg}</p>
						<p>{res.errorMDetail}</p>
					</>
				)
			});
			return;
		}
		setVisible(true);
	};

	const nameRender = (value: string, record: any) => {
		return (
			<div
				className={
					record.phrase === 'DeleteFailed' ||
					record.phrase === 'Deleting'
						? ''
						: 'name-link'
				}
				onClick={() => {
					if (
						record.phrase === 'DeleteFailed' ||
						record.phrase === 'Deleting'
					) {
						return;
					}
					if (params.type) {
						history.push(
							`/project/${params.type}/${params.name}/${params.aliasName}/container/${params.currentTab}/backupRecordDetail/${params.middlewareName}/${params.chartVersion}/${params.namespace}/${clusterId}/${record.backupName}/${record.recordName}`
						);
					} else {
						history.push(
							`/project/backup/record/detail/${clusterId}/${record.namespace}/${record.sourceName}/${record.backupName}/${record.recordName}`
						);
					}
				}}
			>
				{value}
			</div>
		);
	};
	const deleteBackupRecord = (record: any) => {
		Modal.confirm({
			title: '操作确认',
			content:
				record.phrase === 'DeleteFailed' ? (
					<>
						<p>当前minio删除失败，进行强制删除</p>
						<p>是否继续？</p>
					</>
				) : (
					<>
						<p>备份记录删除后将无法恢复</p>
						<p>请确认执行</p>
					</>
				),
			onOk: async () => {
				const result = {
					clusterId: params.clusterId,
					namespace: params.namespace,
					type: record.sourceType,
					backupId: record.backupId,
					backupName: record.backupName,
					forceDelete:
						record.phrase === 'DeleteFailed' ? 'true' : 'false'
				};
				await ExecuteOrderFuc();
				return deleteBackups(result).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '备份记录删除成功'
						});
						getData();
						getBackupTaskDetail();
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			}
		});
	};
	const actionRender = (value: any, record: any, index: number) => {
		return (
			<Actions>
				{record.sourceType === 'postgresql' ||
				record.sourceType === 'redis' ? (
					value.phrase !== 'Success' ? (
						<Tooltip title="仅能针对备份成功的备份记录进行数据有效性验证！">
							<LinkButton disabled>验证</LinkButton>
						</Tooltip>
					) : (
						<LinkButton
							disabled={!backupComponent}
							onClick={() => {
								const operator_id =
									record.sourceType === 'postgresql'
										? postgresqlDataValidityVerificationOperatorId
										: redisDataValidityVerificationOperatorId;
								WorkOrderFuc(
									() => {
										fullRecordTest(record);
									},
									lock,
									middlewareName,
									operator_id,
									history,
									params.type,
									params.name,
									params.aliasName,
									params.clusterId,
									params.namespace
								);
							}}
						>
							验证
						</LinkButton>
					)
				) : null}
				<LinkButton
					disabled={
						!backupComponent ||
						record.phrase === 'Deleting' ||
						record.backupMode === 'single' ||
						record.protect ||
						controlledOperationDisabled('maintenance', lock)
					}
					onClick={() => {
						WorkOrderFuc(
							() => {
								deleteBackupRecord(record);
							},
							lock,
							middlewareName,
							deleteBackupRecordOperatorId,
							history,
							params.type,
							params.name,
							params.aliasName,
							params.clusterId,
							params.namespace
						);
					}}
					title={
						!backupComponent
							? '备份控制器组件未安装，请前往平台组件页面安装相应组件'
							: ''
					}
				>
					{record.phrase === 'DeleteFailed' ? '强制删除' : '删除'}
				</LinkButton>
			</Actions>
		);
	};

	const getData = (orderBy?: string) => {
		getBackups({
			backupId: backupId,
			backupMode: backupDetail.backupMode,
			clusterId: clusterId,
			namespace: namespace,
			middlewareName: middlewareName,
			type: name,
			orderBy
		}).then((res) => {
			if (res.success) {
				setData(res.data);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const getBackupTaskDetail = async () => {
		await getTaskDetail({
			backupMode: backupDetail.backupMode,
			backupId: params.backupId,
			clusterId: params.clusterId,
			namespace: params.namespace
		}).then((res) => {
			if (res.data[0]?.phrase === 'Deleting' || res.data.length === 0) {
				history.goBack();
				return;
			}
		});
	};

	const handleTableChange = (
		pagination: TablePaginationConfig,
		filters: any,
		sorter: any
	) => {
		let sizeSorter = '';
		if (sorter.field === 'size') {
			sizeSorter = sorter.order
				? sorter.order === 'ascend'
					? 'asc'
					: 'desc'
				: '';
		} else if (sorter.field === 'creationTime') {
			sizeSorter = sorter.order
				? sorter.order === 'ascend'
					? 'asc'
					: 'desc'
				: '';
		}
		getData(
			sizeSorter
				? `${sorter.field === 'size' ? 'size' : 'time'},${sizeSorter}`
				: ''
		);
	};

	return (
		<>
			<ProTable
				dataSource={data}
				rowKey="recordName"
				style={{ marginTop: 16 }}
				onChange={handleTableChange}
			>
				<ProTable.Column
					title="备份记录名称"
					dataIndex="recordName"
					render={nameRender}
				/>
				<ProTable.Column
					title="状态"
					dataIndex="phrase"
					render={statusRecordRender}
					filterMultiple={false}
					filters={backupRecordStatus}
					onFilter={(value, record: any) =>
						value !== 'Unknown'
							? record.phrase === value
							: record.phrase !== 'Running' &&
							  record.phrase !== 'Failed' &&
							  record.phrase !== 'Success' &&
							  record.phrase !== 'Deleting' &&
							  record.phrase !== 'DeleteFailed'
					}
					width={160}
				/>
				{backupDetail.activeActive && (
					<ProTable.Column
						title="可用区"
						dataIndex="areaAliasName"
						filters={zones}
						filterMultiple={false}
						onFilter={(value, record: any) =>
							record.activeArea === value
						}
						render={nullRender}
					/>
				)}
				<ProTable.Column
					title="存储大小"
					dataIndex="size"
					render={nullRender}
					sorter={true}
				/>
				<ProTable.Column
					title="备份时间"
					dataIndex="creationTime"
					sorter={true}
				/>
				<ProTable.Column title="操作" render={actionRender} />
			</ProTable>
			{visible && (
				<DataCheck
					open={visible}
					onClose={() => setVisible(false)}
					checkRecord={checkRecord}
				/>
			)}
		</>
	);
}

export default FullRecord;

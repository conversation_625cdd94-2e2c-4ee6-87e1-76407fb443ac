import React, { useEffect, useState } from 'react';
import { useHistory } from 'react-router';

import ProTable from '@/components/ProTable';
import Actions from '@/components/Actions';
import { notification, Modal } from 'antd';

import {
	controlledOperationDisabled,
	nullRender,
	statusRestoreRender
} from '@/utils/utils';
import { backupRestoreStatus, maintenances } from '@/utils/const';
import { getZones } from '@/services/activeActive';
import { ZonesItem } from '@/pages/ActiveActive/activeActive';
import { FiltersProps } from '@/types/comment';
import { getRestoreList, deleteRestoreList } from '@/services/backup';
import moment from 'moment';
import { useParams } from 'react-router';
import storage from '@/utils/storage';
import { ExecuteOrderFuc, WorkOrderFuc } from '@/components/WorkOrderFuc';
import useRefresh from '@/utils/useRefresh';

const LinkButton = Actions.LinkButton;
interface CloneRecordProps {
	backupComponent: boolean;
	lock: string;
}
function CloneRecord(props: CloneRecordProps): JSX.Element {
	const { backupComponent, lock } = props;
	const params: any = useParams();
	const history = useHistory();
	const [data, setData] = useState();
	const [zones, setZones] = useState<FiltersProps[]>([]);
	const backupDetail = storage.getLocal('backupDetail');
	const deleteBackupRecordOperatorId = maintenances['Delete Backup Record'];
	const {
		backupId,
		clusterId,
		namespace,
		name,
		middlewareName,
		aliasName,
		type
	} = params;
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));

	useEffect(() => {
		getData();
		getZones({ clusterId: params.clusterId }).then((res) => {
			if (res.success) {
				setZones(
					res.data
						.filter((item: ZonesItem) => item.name !== 'zoneC')
						.map((item: ZonesItem) => {
							return {
								text: item.aliasName,
								value: item.name
							};
						})
				);
			} else {
				setZones([]);
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	}, [refreshKey]);

	const nameRender = (value: string, record: any) => {
		return (
			<div
				className={
					record.sourceStatus === 0 ||
					record.phrase === 'DeleteFailed' ||
					record.phrase === 'Deleting'
						? ''
						: 'name-link'
				}
				onClick={() => {
					if (
						record.phrase === 'DeleteFailed' ||
						record.phrase === 'Deleting'
					) {
						return;
					}
					if (
						history.location.pathname.indexOf(
							'backupTaskDetail'
						) !== -1
					) {
						history.push(
							`/project/${params.type}/${params.name}/${params.aliasName}/container/${params.currentTab}/backupRestoreDetail/${params.middlewareName}/${params.chartVersion}/${params.namespace}/${clusterId}/${record.restoreName}`
						);
					} else {
						history.push(
							`/project/backup/record/detail/${clusterId}/${record.namespace}/${params.middlewareName}/${record.restoreName}`
						);
					}
				}}
			>
				{value}
			</div>
		);
	};
	const deleteBackupRecord = (record: any) => {
		Modal.confirm({
			title: '操作确认',
			content: '克隆记录删除后将无法恢复，请确认执行',
			onOk: async () => {
				const result = {
					clusterId: params.clusterId,
					namespace: params.namespace,
					type: record.sourceType,
					restoreName: record.restoreName
				};
				await ExecuteOrderFuc();
				return deleteRestoreList(result).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '克隆记录删除成功'
						});
						getData();
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			}
		});
	};
	const actionRender = (value: any, record: any, index: number) => {
		return (
			<Actions>
				<LinkButton
					disabled={
						!backupComponent ||
						record.phrase === 'Deleting' ||
						controlledOperationDisabled('maintenance', lock)
					}
					onClick={() => {
						WorkOrderFuc(
							() => {
								deleteBackupRecord(record);
							},
							lock,
							middlewareName,
							deleteBackupRecordOperatorId,
							history,
							type,
							name,
							aliasName,
							clusterId,
							namespace
						);
					}}
					title={
						!backupComponent
							? '备份控制器组件未安装，请前往平台组件页面安装相应组件'
							: ''
					}
				>
					删除
				</LinkButton>
			</Actions>
		);
	};

	const getData = () => {
		getRestoreList({
			backupId: backupId,
			clusterId: clusterId,
			namespace: namespace
		}).then((res) => {
			if (res.success) {
				setData(res.data);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};

	return (
		<ProTable
			dataSource={data}
			rowKey="restoreName"
			style={{ marginTop: 16 }}
		>
			<ProTable.Column
				title="克隆记录名称"
				dataIndex="restoreName"
				render={nameRender}
			/>
			<ProTable.Column
				title="状态"
				dataIndex="phrase"
				render={statusRestoreRender}
				filterMultiple={false}
				filters={backupRestoreStatus}
				onFilter={(value, record: any) =>
					value !== 'Unknown'
						? record.phrase === value
						: record.phrase !== 'Running' &&
						  record.phrase !== 'Failed' &&
						  record.phrase !== 'Success' &&
						  record.phrase !== 'Deleting' &&
						  record.phrase !== 'DeleteFailed'
				}
				width={160}
			/>
			{backupDetail.activeActive && (
				<ProTable.Column
					title="可用区"
					dataIndex="activeArea"
					filters={zones}
					filterMultiple={false}
					onFilter={(value, record: any) =>
						record.activeArea === value
					}
					render={nullRender}
				/>
			)}
			<ProTable.Column
				title="克隆时间"
				dataIndex="creationTime"
				sorter={(a: any, b: any) =>
					moment(a.creationTime).unix() -
					moment(b.creationTime).unix()
				}
				render={nullRender}
			/>
			<ProTable.Column title="操作" render={actionRender} />
		</ProTable>
	);
}

export default CloneRecord;

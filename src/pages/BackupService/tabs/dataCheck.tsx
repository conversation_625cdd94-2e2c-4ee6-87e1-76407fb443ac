import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Drawer, notification } from 'antd';
import { backupCheck, getBackupCheckResult } from '@/services/backup';
import { useParams } from 'react-router';

export default function DataCheck({
	open,
	onClose,
	checkRecord
}: {
	open: boolean;
	onClose: () => void;
	checkRecord: any;
}): JSX.Element {
	const params: any = useParams();
	const [backupResult, setBackupResult] = useState('');
	const [backupReason, setBackupReason] = useState('');
	const resultRef = useRef<any>();
	useEffect(() => {
		resultRef.current = setInterval(() => {
			getResult();
		}, 3000);
		return () => {
			resultRef.current && clearInterval(resultRef.current);
		};
	}, []);
	const getResult = async () => {
		const sendData = {
			...params,
			middleware: params.middlewareName,
			backupType: params.backupMode,
			backupName: checkRecord.backupName
		};
		const res = await getBackupCheckResult(sendData);
		if (res.success) {
			setBackupResult(res.data.phase || '/');
			setBackupReason(res.data.reason || '');
			if (
				res.data.phase === 'Success' ||
				res.data.phase === 'Failed' ||
				res.data.phase === 'Error'
			) {
				clearInterval(resultRef.current);
			}
		}
	};
	const resultRender = () => {
		switch (backupResult) {
			case 'Success':
				return <Badge status="success" text="验证成功" />;
			case 'Running':
				return <Badge status="processing" text="验证中" />;
			case 'Failed':
				return <Badge status="error" text="验证失败" />;
			case 'Error':
				return <Badge status="error" text="数据损坏" />;
			default:
				return '/';
		}
	};
	const handleBackupCheck = async () => {
		setBackupResult('验证中');
		const sendData = {
			...params,
			middleware: params.middlewareName,
			backupType: params.backupMode
		};
		const res = await backupCheck(sendData);
		if (!res.success) {
			notification.error({
				message: '错误',
				description: (
					<>
						<p>{res.errorMsg}</p>
						<p>{res.errorMDetail}</p>
					</>
				)
			});
			return;
		}
	};
	return (
		<Drawer
			title={
				<div className="icon-type-content">
					<div>数据有效性验证</div>
				</div>
			}
			placement="right"
			onClose={onClose}
			open={open}
			mask={false}
			width={500}
		>
			<div className="install-title-content">
				{(backupResult === 'Success' ||
					backupResult === 'Failed' ||
					backupResult === 'Error') && (
					<Button
						type="primary"
						onClick={() => {
							handleBackupCheck();
						}}
						style={{ marginRight: '15px' }}
					>
						发起验证
					</Button>
				)}
			</div>
			<div style={{ marginTop: '15px', fontSize: 14 }}>
				<p>
					<span>验证结果：</span> <span>{resultRender()}</span>
				</p>
				{backupReason?.length ? (
					<p className="mt-8">
						<span>失败原因：</span> <span>{backupReason}</span>
					</p>
				) : (
					''
				)}
			</div>
		</Drawer>
	);
}

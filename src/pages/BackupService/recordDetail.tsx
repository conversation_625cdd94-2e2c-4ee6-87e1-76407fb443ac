import * as React from 'react';
import { useEffect, useState, useRef } from 'react';
import { useParams } from 'react-router';
import ProTable from '@/components/ProTable';
import { ProPage, ProHeader, ProContent } from '@/components/ProPage';
import { Button, notification, Progress, Select } from 'antd';
import {
	CheckCircleFilled,
	CloseCircleOutlined,
	ReloadOutlined
} from '@ant-design/icons';
import { UnControlled as CodeMirror } from 'react-codemirror2';
import { setRealLog, cleanRealLog } from '@/redux/log/log';

import { useHistory } from 'react-router';
import DataFields from '@/components/DataFields';
import { getTaskProgress, getTaskRestore } from '@/services/backup';
import storage from '@/utils/storage';
import {
	statusBackupRender,
	statusRecordRender,
	statusRestoreRender
} from '@/utils/utils';
import { StoreState } from '@/types';
import Socket from '@/services/websocket.js';
import { connect } from 'react-redux';
import { filtersProps } from '@/types/comment';

import '@/pages/ServiceListDetail/Log/log.less';
import 'codemirror/lib/codemirror.css';
import 'codemirror/theme/twilight.css';

const info = {
	title: '基本进度',
	size: '',
	phrase: '',
	activeArea: '',
	backupSourceName: '',
	backupControllerStatus: ''
};

function RecordDetail(props: any): JSX.Element {
	const { setRealLog, cleanRealLog } = props;
	const history = useHistory();
	const params: any = useParams();
	const options = {
		mode: 'xml',
		theme: 'twilight',
		readOnly: true,
		lineNumbers: true,
		fullScreen: false,
		lineWrapping: true
	};
	const ws = useRef<any>(null);
	const [data, setData] = useState<any>();
	const [basicData, setBasicData] = useState<any>(info);
	const backupDetail = storage.getLocal('backupDetail');
	const [websocketFlag, setWebsocketFlag] = useState<number>(0);
	const [process, setProgress] = useState<string>();
	const [processList, setProgressList] = useState<filtersProps[]>([]);
	const [infoConfig, setInfoConfig] = useState<any>([
		{
			dataIndex: 'title',
			render: (val: string) => (
				<div className="title-content">
					<div className="blue-line"></div>
					<div className="detail-title">{val}</div>
				</div>
			),
			span: 24
		},
		{
			dataIndex: 'phrase',
			label: '状态',
			render: (val: string) =>
				params.restoreName
					? statusRestoreRender(val, 0, backupDetail)
					: statusRecordRender(val, 0, backupDetail)
		},
		{
			dataIndex: 'backupSourceName',
			label: '备份源名称',
			render: (val: string) => (
				<div className="text-overflow-one" title={val}>
					{val || '/'}
				</div>
			)
		},
		{
			dataIndex: 'areaAliasName',
			label: '可用区',
			render: (val: string, record: any) => (
				<div className="text-overflow-one" title={val}>
					{val || record.activeArea || '/'}
				</div>
			)
		},
		{
			dataIndex: 'size',
			label: '存储大小',
			render: (val: string) => (
				<div className="text-overflow-one" title={val}>
					{val || '/'}
				</div>
			)
		},
		{
			dataIndex: 'backupControllerStatus',
			label: '备份控制器状态',
			render: (val: number) =>
				val === 1 ? (
					<div className="display-flex flex-align">
						<CheckCircleFilled
							style={{ color: '#00A700', marginRight: 4 }}
						/>
						运行正常
					</div>
				) : (
					<div className="display-flex flex-align">
						<CloseCircleOutlined
							style={{ color: '#C80000', marginRight: 4 }}
						/>
						运行异常
					</div>
				)
		},
		{
			dataIndex: 'createTime',
			label: '备份时间',
			render: (val: string) => (
				<div className="text-overflow-one" title={val}>
					{val || '/'}
				</div>
			)
		}
	]);

	useEffect(() => {
		const list = [...infoConfig];
		params.restoreName && list.splice(list.length - 1, 1);
		(params.restoreName || params.recordName.indexOf('incr') !== -1) &&
			list.splice(4, 1);
		!backupDetail.activeActive && list.splice(3, 1);
		setInfoConfig(list);
	}, []);

	useEffect(() => {
		if (params.clusterId && params.namespace) {
			getData();
		}
	}, [params.clusterId, params.namespace]);

	useEffect(() => {
		cleanRealLog();
		if (basicData.namespace && basicData.clusterId && process) {
			ws.current = new Socket({
				socketUrl: `/terminal?terminalType=stdoutlog&pod=${
					process.split('/')[0]
				}&namespace=${basicData.namespace}&container=${
					process.split('/')[1]
				}&clusterId=${basicData.clusterId}`,
				timeout: 5000,
				socketMessage: (receive: any) => {
					setWebsocketFlag(websocketFlag + 1);
					const content = props.log + JSON.parse(receive.data).text;
					console.log(content);

					setRealLog(content);
				},
				socketClose: (msg: any) => {
					cleanRealLog();
				},
				socketError: () => {
					console.log('连接建立失败');
				},
				socketOpen: () => {
					console.log('连接建立成功');
				}
			});
			try {
				ws.current.connection();
			} catch (e) {
				// * 捕获异常，防止js error
				console.log(e);
			}
			return () => {
				ws.current.onclose();
			};
		}
		return () => {
			cleanRealLog();
		};
	}, [basicData, process]);

	const Operation = {
		primary: (
			<div className="title-content">
				<div className="blue-line"></div>
				<div className="detail-title">备份进程</div>
			</div>
		)
	};

	const getData = () => {
		if (params.restoreName) {
			getTaskRestore({
				restoreName: params.restoreName,
				clusterId: params.clusterId,
				namespace: params.namespace,
				middlewareName: params.middlewareName
			}).then((res) => {
				if (res.success) {
					setData(res.data.taskPods);
					setBasicData({ title: '基本进度', ...res.data });
					res.data.taskPods.length &&
						setProgress(
							res.data.taskPods[0]?.podName +
								'/' +
								res.data.taskPods[0]?.containers[0].name
						);
					setProgressList(
						res.data.taskPods.map((item: any) => {
							return {
								key: item.podName,
								label: item.podAliasName,
								value:
									item.podName + '/' + item.containers[0].name
							};
						})
					);
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		} else {
			getTaskProgress({
				backupName: params.backupName,
				clusterId: params.clusterId,
				namespace: params.namespace,
				middlewareName: params.middlewareName
			}).then((res) => {
				if (res.success) {
					setData(res.data.taskPods);
					setBasicData({ title: '基本进度', ...res.data });
					res.data.taskPods.length &&
						setProgress(
							res.data.taskPods[0]?.podName +
								'/' +
								res.data.taskPods[0]?.containers[0].name
						);
					setProgressList(
						res.data.taskPods.map((item: any) => {
							return {
								key: item.podName,
								label: item.podAliasName,
								value:
									item.podName + '/' + item.containers[0].name
							};
						})
					);
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		}
	};

	return (
		<ProPage>
			<ProHeader
				title={params.recordName || params.restoreName}
				onBack={() => history.goBack()}
				extra={
					<Button
						icon={<ReloadOutlined />}
						onClick={() => {
							getData();
							cleanRealLog();
						}}
					/>
				}
			/>
			<ProContent>
				{basicData.currentProgress && basicData.currentProgress < 1 && (
					<div>
						<h2>当前进度</h2>
						<div className="mb-16">
							<div>{basicData.progressDescription}</div>
							<Progress
								percent={basicData.currentProgress * 100}
								showInfo={false}
							/>
						</div>
					</div>
				)}
				<DataFields dataSource={basicData} items={infoConfig} />
				<ProTable
					// showRefresh
					dataSource={data}
					// onRefresh={getData}
					rowKey="podAliasName"
					operation={Operation}
					style={{ marginTop: 16 }}
				>
					<ProTable.Column
						title="备份进程类"
						dataIndex="podAliasName"
					/>
					<ProTable.Column title="名称" dataIndex="podName" />
					<ProTable.Column title="状态" dataIndex="status" />
					<ProTable.Column
						title="就绪容器数"
						dataIndex="readyStatus"
					/>
					<ProTable.Column
						title="重启次数"
						dataIndex="restartCount"
					/>
				</ProTable>
				<h2 className="mt-16 mb-16">相关日志</h2>
				<div>
					<label className="mr-24">进程切换：</label>
					<Select
						style={{ width: '240px' }}
						value={process}
						options={processList}
						placeholder="请选择进程"
						onChange={(value) => setProgress(value)}
					/>
				</div>
				<div className="log-display" style={{ marginTop: 16 }}>
					<CodeMirror
						value={props.log}
						options={options}
						className="log-codeMirror"
					/>
				</div>
			</ProContent>
		</ProPage>
	);
}

const mapStateToProps = (state: StoreState) => ({
	log: state.log.log
});
export default connect(mapStateToProps, {
	setRealLog,
	cleanRealLog
})(RecordDetail);

import Actions from '@/components/Actions';
import ProTable from '@/components/ProTable';
import { ProjectItem } from '@/types';
import React, { useEffect, useState } from 'react';
import { Button, Modal, notification } from 'antd';
import { useHistory } from 'react-router';
import { nullRender, nullToZeroRender } from '@/utils/utils';
import { deleteProject, getProjects } from '@/services/project';
import EditProject from './EditProject';
import AllotProBackupServer from './allotProBackupServer';
import storage from '@/utils/storage';
import { connect } from 'react-redux';
import { setProClusters } from '@/redux/organization/organization';

const { confirm } = Modal;
const LinkButton = Actions.LinkButton;
function ProjectManage({
	setProClusters
}: {
	setProClusters: (value: any) => void;
}): JSX.Element {
	const isAccess = storage.getLocal('isAccessGYT');
	const organId = storage.getSession('organId');
	const history = useHistory();
	const [projects, setProjects] = useState<ProjectItem[]>([]);
	const [editData, setEditData] = useState<ProjectItem>();
	const [keyword, setKeyword] = useState<string>('');
	const [open, setOpen] = useState<boolean>(false);
	const [allotOpen, setAllotOpen] = useState<boolean>(false);
	useEffect(() => {
		let mounted = true;
		if (organId) {
			if (mounted) {
				getData('');
			}
		}
		return () => {
			mounted = false;
		};
	}, [organId]);
	const getData = (value: string) => {
		getProjects({ key: value, organId, manager: true }).then((res) => {
			if (res.success) {
				setProjects(res.data);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const handleSearch = (value: string) => {
		setKeyword(value);
		getData(value);
	};
	const Operation = {
		primary: (
			<Button
				onClick={() => {
					setEditData(undefined);
					setOpen(true);
				}}
				type="primary"
				disabled={isAccess}
				title={isAccess ? '平台已接入观云台，请联系观云台管理员' : ''}
			>
				新增
			</Button>
		)
	};
	const backupServerCountRender = (
		value: any,
		record: ProjectItem,
		index: number
	) => {
		return record?.backupServerList?.length || 0;
	};
	const actionRender = (
		value: string,
		record: ProjectItem,
		index: number
	) => {
		return (
			<Actions>
				<LinkButton
					onClick={() => {
						if (isAccess) {
							setEditData(record);
							setAllotOpen(true);
						} else {
							setProClusters([]);
							history.push(
								`/organization/project/add/${organId}/${record.projectId}/${record.name}`
							);
						}
					}}
				>
					{isAccess ? '备份服务器管理' : '管理配额'}
				</LinkButton>
				<LinkButton
					onClick={() => {
						setEditData(record);
						setOpen(true);
					}}
					disabled={isAccess}
					title={
						isAccess ? '平台已接入观云台，请联系观云台管理员' : ''
					}
				>
					编辑
				</LinkButton>
				<LinkButton
					disabled={isAccess}
					title={
						isAccess ? '平台已接入观云台，请联系观云台管理员' : ''
					}
					onClick={() =>
						confirm({
							title: '操作确认',
							content: (
								<>
									<p>删除将无法找回</p>
									<p>是否继续？</p>
								</>
							),
							okText: '确定',
							cancelText: '取消',
							onOk() {
								return deleteProject({
									projectId: record.projectId,
									organId: organId
								}).then((res) => {
									if (res.success) {
										notification.success({
											message: '成功',
											description: '项目删除成功！'
										});
										getData(keyword || '');
									} else {
										notification.error({
											message: '失败',
											description: res.errorMsg
										});
									}
								});
							},
							onCancel() {
								console.log('cancel');
							}
						})
					}
				>
					删除
				</LinkButton>
			</Actions>
		);
	};
	return (
		<>
			<ProTable
				dataSource={projects}
				showRefresh
				onRefresh={() => getData(keyword)}
				rowKey="name"
				operation={Operation}
				search={{
					onSearch: handleSearch,
					placeholder: '请输入关键字搜索'
				}}
			>
				<ProTable.Column
					title="项目名称"
					dataIndex="name"
					width={250}
					fixed="left"
				/>
				<ProTable.Column
					title="成员数"
					dataIndex="memberCount"
					width={100}
					render={nullToZeroRender}
				/>
				<ProTable.Column
					title="命名空间数"
					dataIndex="namespaceCount"
					width={100}
					render={nullToZeroRender}
				/>
				<ProTable.Column
					title="备份服务器数"
					dataIndex="backupServerCount"
					width={100}
					render={backupServerCountRender}
				/>
				<ProTable.Column
					title="备注"
					dataIndex="description"
					render={nullRender}
				/>
				<ProTable.Column
					title="操作"
					dataIndex="action"
					render={actionRender}
					width={200}
				/>
			</ProTable>
			{open && (
				<EditProject
					open={open}
					onCancel={() => setOpen(false)}
					organId={organId}
					data={editData}
					onRefresh={() => {
						getData(keyword);
					}}
				/>
			)}
			{allotOpen && editData && (
				<AllotProBackupServer
					open={allotOpen}
					organId={organId}
					projectId={editData.projectId}
					onCancel={() => setAllotOpen(false)}
				/>
			)}
		</>
	);
}
export default connect(() => ({}), {
	setProClusters
})(ProjectManage);

import { UserItem } from '@/pages/ProjectDetail/projectDetail';
import { getOrganizationUsers } from '@/services/organization';
import {
	createProject,
	updateProject,
	getProjectDetail,
	checkProjectId
} from '@/services/project';
import { formItemLayout618 } from '@/utils/const';
import pattern from '@/utils/pattern';
import { Form, Input, Modal, notification, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import { EditProjectProps } from '../organization.detail';
import storage from '@/utils/storage';

export default function EditProject(props: EditProjectProps): JSX.Element {
	const [form] = Form.useForm();
	const { open, onCancel, organId, data, onRefresh } = props;
	const [users, setUsers] = useState<UserItem[]>([]);
	const [loading, setLoading] = useState<boolean>(false);
	// * 马上办是否开启的判断
	const [ThirdPartyOACloseAPI] = useState<boolean>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'thirdPartyOAClose')?.enabled ??
			true
	);
	useEffect(() => {
		if (data) {
			form.setFieldsValue({
				name: data.name,
				description: data.description,
				projectId: data.projectId
			});
			getProjectDetail({
				organId,
				projectId: data.projectId,
				key: ''
			}).then((res: any) => {
				if (res.success) {
					form.setFieldsValue({
						userDtoList: res.data.userDtoList?.map(
							(item: any) => item.userName
						)
					});
				}
			});
		}
	}, [data]);
	useEffect(() => {
		getOrganizationUsers({
			organId: organId,
			allocatable: false
		}).then((res) => {
			if (res.success) {
				setUsers(res.data.filter((item) => item.isAdmin !== true));
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	}, []);
	const checkProject = async (_: any, value: any) => {
		if (data) {
			return Promise.resolve();
		}
		await checkProjectId({
			organId: organId,
			projectId: value
		}).then((res) => {
			if (res.success) {
				if (res.data) {
					return Promise.reject(
						new Error('当前输入的项目ID已存在，请修改后重试')
					);
				} else {
					return Promise.resolve();
				}
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	const onOk = () => {
		form.validateFields().then((values) => {
			setLoading(true);
			if (data) {
				updateProject({
					organId,
					projectId: data.projectId,
					...values,
					userDtoList:
						values.userDtoList?.map((item: string) => {
							return { userName: item };
						}) || null
				})
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '项目编辑成功！'
							});
							onCancel();
							onRefresh();
						} else {
							notification.error({
								message: '错误',
								description: (
									<>
										<p>{res.errorMsg}</p>
										<p>{res.errorDetail}</p>
									</>
								)
							});
						}
					})
					.finally(() => {
						setLoading(false);
					});
			} else {
				createProject({
					organId,
					...values,
					userDtoList:
						values.userDtoList?.map((item: string) => {
							return { userName: item };
						}) || null
				})
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '项目创建成功！'
							});
							onCancel();
							onRefresh();
						} else {
							notification.error({
								message: '错误',
								description: (
									<>
										<p>{res.errorMsg}</p>
										<p>{res.errorDetail}</p>
									</>
								)
							});
						}
					})
					.finally(() => {
						setLoading(false);
					});
			}
		});
	};
	return (
		<Modal
			title={data ? '编辑' : '新增'}
			open={open}
			onCancel={onCancel}
			onOk={onOk}
			okButtonProps={{ loading: loading }}
		>
			<Form form={form} labelAlign="left" {...formItemLayout618}>
				<Form.Item
					label="项目名称"
					name="name"
					rules={[
						{ required: true, message: '项目名称不能为空' },
						{
							pattern: new RegExp(pattern.projectAliasName),
							message:
								'项目名称支持中文、大小字母、数字和特殊字符_.-，长度不可超过64个字符'
						}
					]}
				>
					<Input placeholder="请输入项目名称" />
				</Form.Item>
				{!ThirdPartyOACloseAPI && (
					<Form.Item
						label="项目ID"
						name="projectId"
						rules={[
							{ required: true, message: '项目ID不能为空' },
							{
								pattern: new RegExp(pattern.projectId),
								message:
									'请输入由大小写英文字母及数字组成的1-16个字符长度的项目ID'
							},
							{ validator: checkProject }
						]}
					>
						<Input disabled={!!data} placeholder="请输入项目ID" />
					</Form.Item>
				)}
				<Form.Item label="备注" name="description">
					<Input placeholder="请输入备注" />
				</Form.Item>
				<Form.Item label="绑定项目管理员" name="userDtoList">
					<Select
						dropdownMatchSelectWidth={false}
						allowClear={true}
						mode="multiple"
						showSearch
						placeholder="请选择项目管理员"
						style={{ width: '100%' }}
					>
						{users.map((item: UserItem) => {
							return (
								<Select.Option
									value={item.userName}
									key={item.id}
								>
									{item.aliasName}
								</Select.Option>
							);
						})}
					</Select>
				</Form.Item>
			</Form>
		</Modal>
	);
}

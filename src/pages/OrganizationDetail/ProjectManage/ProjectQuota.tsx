import { ProContent, ProHeader, ProPage } from '@/components/ProPage';
import React, { useEffect, useState } from 'react';
import {
	Button,
	Col,
	Collapse,
	Divider,
	Empty,
	InputNumber,
	notification,
	Row,
	Space,
	Tag,
	Modal,
	Table,
	Input
} from 'antd';
import {
	CheckOutlined,
	DeleteOutlined,
	EditOutlined,
	MinusOutlined,
	PlusOutlined,
	RightOutlined
} from '@ant-design/icons';
import { IconFont } from '@/components/IconFont';
import { useHistory, useParams } from 'react-router';
import {
	AddProClusterParams,
	CpuMemoryItem,
	OrganizationBackupServerItem,
	ProjectQuotaProps,
	StorageListTableItem
} from '../organization.detail';
import { connect } from 'react-redux';
import {
	setProCluster,
	setProClusters,
	setEditCluster,
	deleteProClusterById
} from '@/redux/organization/organization';
import { StoreState } from '@/types';
import { StorageAllotItem } from '../../OrganizationManagement/organization.d';
import {
	allotProjectQuota,
	getProjectBackupServer,
	getProjectCpuAndMemory,
	getProjectStorage
} from '@/services/project';
import { getOrgBackupServer, getOrgStorage } from '@/services/organization';
import MidSlider from '@/components/MidSlider';
import { getAgents, getProjectAgentList } from '@/services/agent';
import { agentPhaseRender, objectRemoveDuplicatesByKey } from '@/utils/utils';
import { FiltersProps } from '@/types/comment';
const { CheckableTag } = Tag;
const { Panel } = Collapse;
const { Search } = Input;
const { confirm } = Modal;
function ProjectQuota(props: ProjectQuotaProps): JSX.Element {
	const {
		proClusters,
		setProClusters,
		setEditCluster,
		deleteProClusterById
	} = props;
	const history = useHistory();
	const params: AddProClusterParams = useParams();
	const [originClusters, setOriginClusters] = useState<any[]>([]);
	const [orgStorages, setOrgStorages] = useState<StorageListTableItem[]>([]);
	const [storages, setStorages] = useState<StorageListTableItem[]>([]);
	const [orgSelectStorages, setOrgSelectStorages] = useState<
		StorageAllotItem[]
	>([]);
	const [selectStorages, setSelectStorages] = useState<StorageAllotItem[]>(
		[]
	);
	const [backups, setBackups] = useState<OrganizationBackupServerItem[]>([]);
	const [selectBackups, setSelectBackups] = useState<
		OrganizationBackupServerItem[]
	>([]);
	const [selectedTags, setSelectedTags] = useState<string[]>([]);
	const [selectedBackupServerTags, setSelectedBackupServerTags] = useState<
		string[]
	>([]);
	const [agents, setAgents] = useState<AgentItem[]>([]);
	const [showDataSource, setShowDataSource] = useState<AgentItem[]>([]);
	const [selectAgents, setSelectedAgents] = useState<AgentItem[]>([]);
	const [primaryKeys, setPrimaryKeys] = useState<string[]>([]);
	const [controllerFilters, setControllerFilters] = useState<FiltersProps[]>(
		[]
	);
	useEffect(() => {
		if (proClusters.length === 0) {
			getProjectCpuAndMemory({
				organId: params.organId,
				projectId: params.projectId,
				detail: true
			}).then((res) => {
				if (res.success) {
					if (res.data.length > 0) {
						const list = res.data.map((item) => {
							item.cpu = {
								...item.cpu,
								allocatable: item.cpu.request
							};
							item.memory = {
								...item.memory,
								allocatable: item.memory.request
							};
							return item;
						});
						setProClusters(list);
					} else {
						setProClusters([]);
					}
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			});
		}
		getProjectStorage({
			organId: params.organId,
			projectId: params.projectId,
			detail: true
		}).then((res) => {
			if (res.success) {
				const st: any[] = [];
				res.data.map((item: CpuMemoryItem) => {
					item.storageList.map((i: any) => {
						const result = {
							clusterId: item.clusterId,
							storageId: i.storageId,
							request: i.storage.request, // * 固定当前已分配的分配额
							allocatable: i.storage.request, // * 可分配额
							used: i.storage.used // * 已使用额
						};
						st.push(result);
					});
				});
				setOrgSelectStorages(st);
				setSelectStorages(st);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
		getProjectBackupServer({
			organId: params.organId,
			projectId: params.projectId,
			detail: true
		}).then((res) => {
			if (res.success) {
				setSelectBackups(res.data);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
		getProjectAgentList({
			organId: params.organId,
			projectId: params.projectId
		}).then((res) => {
			if (res.success) {
				setSelectedAgents(res.data);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	}, [params.organId, params.projectId]);
	useEffect(() => {
		if (orgStorages && selectStorages) {
			const list = orgStorages.map((item) => {
				return {
					...item,
					storage: {
						...item.storage,
						sused: item.storage.used, // * 当前存储的已使用额（全局）
						used:
							selectStorages.find(
								(i) => i.storageId === item.storageId
							)?.used || 0 // * 项目下的已使用额
					}
				};
			});
			setStorages(list);
		}
	}, [orgStorages, selectStorages]);
	useEffect(() => {
		setOriginClusters(proClusters);
		setSelectedTags(proClusters.map((item) => item.clusterId));
		setSelectedBackupServerTags(proClusters.map((item) => item.clusterId));
		if (proClusters.length === 0) {
			setStorages([]);
			setBackups([]);
			setAgents([]);
		} else {
			const clusterIds = proClusters.map((item) => item.clusterId);
			getOrgStorage({
				organId: params.organId,
				clusterId: clusterIds.join(','),
				detail: true
			}).then((res) => {
				const st: any[] = [];
				res.data.map((item: CpuMemoryItem) => {
					item.storageList.map((i: any) => {
						const result = {
							...i,
							clusterId: item.clusterId,
							clusterNickName: item.clusterNickName
						};
						st.push(result);
					});
				});
				setOrgStorages([...st]);
			});
			getOrgBackupServer({
				organId: params.organId,
				clusterId: clusterIds.join(','),
				detail: true
			}).then((res) => {
				if (res.success) {
					setBackups(res.data);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			});
			getAgentList(clusterIds).then((res) => {
				const list: any[] = [];
				res.map((item) => {
					if (item?.data) {
						list.push(...item?.data);
					}
				});
				setAgents(list);
				const controllerList = list?.map((item) => ({
					text: item.controller,
					value: item.clusterId
				}));
				setControllerFilters(
					objectRemoveDuplicatesByKey(controllerList, 'value')
				);
			});
		}
		async function getAgentList(clusterIds: string[]) {
			const promise = [];
			for (let i = 0; i < clusterIds.length; i++) {
				const r = await getAgentData(clusterIds[i]);
				promise.push(r);
			}
			console.log(promise);
			return Promise.all(promise);
		}
	}, [proClusters]);
	useEffect(() => {
		if (agents.length > 0 && selectAgents) {
			const list = agents.filter(
				(item) =>
					!selectAgents.find(
						(i) =>
							`${i.name}${i.address}${i.clusterId}` ===
							`${item.name}${item.address}${item.clusterId}`
					)
			);
			setShowDataSource(list);
		}
	}, [agents, selectAgents]);
	const getAgentData = async (clusterId: string) => {
		const res = await getAgents({ clusterId, organ: params.organId });
		if (res.success) {
			return res;
		} else {
			notification.error({
				message: '错误',
				description: (
					<>
						<p>{res.errorMsg}</p>
						<p>{res.errorDetail}</p>
					</>
				)
			});
		}
	};
	const handleSubmit = () => {
		if (proClusters.length === 0) {
			notification.warning({
				message: '提醒',
				description: '请选择集群！'
			});
			return;
		}
		if (
			selectStorages.some(
				(item: { storageId: string; allocatable: number }) =>
					item.allocatable === 0 || !item.allocatable
			)
		) {
			notification.warning({
				message: '提醒',
				description: '请分配存储配额'
			});
			return;
		}
		if (
			selectStorages.some(
				(item) => item.used && item.allocatable < item.used
			)
		) {
			notification.warning({
				message: '提醒',
				description: '当前存储配额值小于已使用值！'
			});
			return;
		}
		const quotasTemp = proClusters.map((item) => {
			const result: any = {};
			result.clusterId = item.clusterId;
			result.cpu = {};
			result.cpu.request = item.cpu.allocatable;
			result.memory = {};
			result.memory.request = item.memory.allocatable;
			result.storageList = selectStorages
				.filter((i: any) => i.clusterId === item.clusterId)
				.map((it) => {
					return {
						storageId: it.storageId,
						storage: {
							request: it.allocatable
						}
					};
				});
			result.agentDTOList = selectAgents.filter(
				(o: any) => o.clusterId === item.clusterId
			);
			return result;
		});
		const sendData = {
			organId: params.organId,
			projectId: params.projectId,
			quotaList: quotasTemp,
			backupServerDTOList: selectBackups
		};
		allotProjectQuota(sendData).then((res) => {
			if (res.success) {
				notification.success({
					message: '成功',
					description: '项目配额分配成功！'
				});
				setProClusters([]);
				history.push('/organization/project');
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	const onChange = (
		newValue: number | null,
		record: StorageListTableItem
	) => {
		// * 存储超分判断
		if (storageMaxCompute(record) < 0) {
			// * 当拖动/输入的存储不是非选中的存储的情况下
			if (
				selectStorages.findIndex(
					(item) => item.storageId === record.storageId
				) < 0
			) {
				const st = {
					storageId: record.storageId as string,
					allocatable:
						(newValue as number) <= storageMaxCompute(record)
							? (newValue as number)
							: storageMaxCompute(record),
					clusterId: record.clusterId,
					used: record.storage.used
				};
				setSelectStorages([st, ...selectStorages]);
			} else {
				const list = selectStorages.map((item) => {
					const result: any = { ...item };
					if (item.storageId === record.storageId) {
						result.allocatable =
							(newValue as number) <= storageMaxCompute(record)
								? newValue
								: storageMaxCompute(record);
					}
					return result;
				});
				setSelectStorages(list);
			}
		} else {
			// * 当拖动/输入的存储不是非选中的存储的情况下
			if (
				selectStorages.findIndex(
					(item) => item.storageId === record.storageId
				) < 0
			) {
				const st = {
					storageId: record.storageId as string,
					allocatable: newValue as number,
					clusterId: record.clusterId,
					used: record.storage.used
				};
				setSelectStorages([st, ...selectStorages]);
			} else {
				const list = selectStorages.map((item) => {
					const result: any = { ...item };
					if (item.storageId === record.storageId) {
						result.allocatable = newValue;
					}
					return result;
				});
				setSelectStorages(list);
			}
		}
	};
	const handleChange = (tag: string, checked: boolean, type: string) => {
		if (type === 'storage') {
			const nextSelectedTags = checked
				? [...selectedTags, tag]
				: selectedTags.filter((t) => t !== tag);
			setSelectedTags(nextSelectedTags);
		} else {
			const nextSelectedTags = checked
				? [...selectedBackupServerTags, tag]
				: selectedBackupServerTags.filter((t) => t !== tag);
			setSelectedBackupServerTags(nextSelectedTags);
		}
	};
	const handleEdit = (record: any) => {
		setEditCluster(record);
		deleteProClusterById(record.clusterId);
		history.push(
			`/organization/project/add/${params.organId}/${params.projectId}/${params.projectName}/addCpuAndMemory/${record.clusterId}`
		);
	};
	const handleRemove = (record: any) => {
		if (originClusters.length === 1) {
			notification.warning({
				message: '提醒',
				description: '请保证当前项目下最少存在一个可用集群!'
			});
			return;
		}
		if (
			!record.cpu.used ||
			record.cpu.used === 0 ||
			!record.memory.used ||
			record.memory.used === 0
		) {
			const sl = storages.filter(
				(item) => item.clusterId === record.clusterId
			);
			if (sl.find((item) => item.storage.used !== 0)) {
				notification.warning({
					message: '提醒',
					description: '当前集群下存在已使用的存储，无法移除！'
				});
				return;
			}
			const bl = selectBackups.filter(
				(item) => item.clusterId === record.clusterId
			);
			if (bl.find((item) => item.using === true)) {
				notification.warning({
					message: '提醒',
					description: '当前集群下存在已使用的备份服务器，无法移除！'
				});
				return;
			}
			confirm({
				title: '操作提醒',
				content:
					'移除该集群会将其所属的存储和备份服务器一起移除，请确认！',
				onOk: () => {
					const list = originClusters.filter(
						(item) => item.clusterId !== record.clusterId
					);
					setOriginClusters(list);
					setProClusters(list);
					const slt = storages.filter(
						(item) => item.clusterId !== record.clusterId
					);
					setStorages(slt);
					const sslt = selectStorages.filter(
						(item) => item.clusterId !== record.clusterId
					);
					setSelectStorages(sslt);
					const blt = backups.filter(
						(item) => item.clusterId !== record.clusterId
					);
					setBackups(blt);
					const sblt = selectBackups.filter(
						(item: any) => item.clusterId !== record.clusterId
					);
					setSelectBackups(sblt);
				}
			});
		} else {
			notification.warning({
				message: '提醒',
				description: '当前集群下已使用CPU和内存，无法移除该集群！'
			});
			return;
		}
	};
	const handleCheckStorage = (record: StorageListTableItem) => {
		if (
			selectStorages.find((item) => item.storageId === record.storageId)
		) {
			const st = selectStorages.find(
				(item) => item.storageId === record.storageId
			);
			if (st?.used && st.used !== 0) {
				notification.warning({
					message: '提醒',
					description: '当前存储已被使用，无法取消分配！'
				});
				return;
			}
			const list = selectStorages.filter(
				(item: StorageAllotItem) => item.storageId !== record.storageId
			);
			setSelectStorages(list);
		} else {
			if (
				orgSelectStorages.find(
					(item) => item.storageId === record.storageId
				)
			) {
				const temp = orgSelectStorages.find(
					(item) => item.storageId === record.storageId
				);
				setSelectStorages([
					...selectStorages,
					temp as StorageAllotItem
				]);
			} else {
				setSelectStorages([
					...selectStorages,
					{
						storageId: record.storageId as string,
						allocatable: record.storage.allocatable as number,
						clusterId: record.clusterId,
						used: record.storage.used
					}
				]);
			}
		}
	};
	const handleCheckBackup = (record: any) => {
		if (selectBackups.find((item: any) => item.id === record.id)) {
			const list = selectBackups.filter(
				(item: any) => item.id !== record.id
			);
			setSelectBackups(list);
		} else {
			setSelectBackups([...selectBackups, record]);
		}
	};
	const storageMaxCompute: (record: StorageListTableItem) => number = (
		record: StorageListTableItem
	) => {
		const st = orgSelectStorages.find(
			(i) => i.storageId === record.storageId
		);
		const result =
			record.storage.request - record.storage.sused + (st?.request || 0);
		return result;
	};
	// * 服务器搜索
	const handleSearch = (value: string) => {
		const list = agents
			.filter(
				(item) =>
					!selectAgents.find(
						(i) =>
							`${i.name}${i.address}${i.clusterId}` ===
							`${item.name}${item.address}${item.clusterId}`
					)
			)
			.filter((item) => item.name.includes(value));
		setShowDataSource(list);
	};
	const onAgentChange = (selectedRowKeys: any) => {
		setPrimaryKeys(selectedRowKeys);
	};
	// * 将选中的服务器添加至右侧
	const onClick = () => {
		const list = primaryKeys.map((item: string) => {
			return agents.find(
				(i) => `${i.name}${i.address}${i.clusterId}` === item
			);
		});
		setPrimaryKeys([]);
		setSelectedAgents([...selectAgents, ...(list as AgentItem[])]);
	};
	const cancelSelected = (record: AgentItem) => {
		if (
			record.relationMiddlewareNum &&
			record.relationMiddlewareNum !== 0
		) {
			notification.warning({
				message: '提示',
				description: '该客户端存在已接入的服务，无法移除！'
			});
			return;
		}
		const list = selectAgents.filter(
			(item: AgentItem) =>
				`${item.name}${item.address}${item.clusterId}` !==
				`${record.name}${record.address}${record.clusterId}`
		);
		setSelectedAgents(list);
	};
	const handleClear = () => {
		if (
			selectAgents.find(
				(item) =>
					item.relationMiddlewareNum &&
					item.relationMiddlewareNum !== 0
			)
		) {
			notification.warning({
				message: '提示',
				description: '当前已选择的客户端存在已接入的服务，无法清空！'
			});
			return;
		}
		setSelectedAgents([]);
	};
	return (
		<ProPage>
			<ProHeader
				title="管理项目配额"
				onBack={() => {
					setProClusters([]);
					history.push('/organization/project');
				}}
			/>
			<ProContent>
				<Collapse expandIconPosition="end" defaultActiveKey={['1']}>
					<Panel key="1" header="资源配额分配概览">
						<div className="project-quota-add-quota-content">
							<div
								className="project-quota-add-quota-item"
								onClick={() => {
									setEditCluster({});
									history.push(
										`/organization/project/add/${params.organId}/${params.projectId}/${params.projectName}/addCpuAndMemory`
									);
								}}
							>
								<PlusOutlined />
							</div>
							{originClusters.map((item, index) => {
								return (
									<div
										className="project-quota-item"
										key={index}
									>
										<div className="project-quota-title">
											<IconFont
												type="icon-jiqun"
												style={{
													fontSize: 28,
													marginRight: 16
												}}
											/>
											<span>{item.clusterNickName}</span>
										</div>
										<div className="project-quota-content">
											<p>
												CPU(核)：
												{item?.cpu?.request.toFixed(1)}
											</p>
											<p>
												内存(GB)：
												{item?.memory?.request.toFixed(
													1
												)}
											</p>
										</div>
										<div className="project-delete">
											<div
												className="project-delete-item"
												onClick={() => handleEdit(item)}
											>
												<EditOutlined />
												修改
											</div>
											<div
												className="project-delete-item"
												onClick={() =>
													handleRemove(item)
												}
											>
												<DeleteOutlined />
												移除
											</div>
										</div>
									</div>
								);
							})}
						</div>
					</Panel>
				</Collapse>
				<Collapse expandIconPosition="end" defaultActiveKey={['1']}>
					<Panel key="1" header="存储配额分配概览">
						<div className="project-quota-storage-content">
							{storages.length === 0 && (
								<Empty
									style={{
										width: '100%'
									}}
									image={Empty.PRESENTED_IMAGE_SIMPLE}
									description={<div>当前无数据</div>}
								/>
							)}
							{storages.length !== 0 && (
								<>
									<div className="rapid-screening-content">
										<div>快速筛选</div>
										{proClusters.map((item) => (
											<CheckableTag
												key={item.clusterId}
												checked={
													selectedTags.indexOf(
														item.clusterId
													) > -1
												}
												onChange={(checked) => {
													handleChange(
														item.clusterId,
														checked,
														'storage'
													);
												}}
											>
												{item.clusterNickName}(
												{
													storages.filter(
														(i: any) =>
															i.clusterId ===
															item.clusterId
													)?.length
												}
												)
											</CheckableTag>
										))}
									</div>
									<div className="project-quota-storage">
										{storages
											.filter((item) =>
												selectedTags.includes(
													item.clusterId
												)
											)
											.map(
												(
													item: StorageListTableItem,
													index: number
												) => {
													return (
														<div
															className="project-quota-storage-item"
															key={index}
															style={
																selectStorages.find(
																	(
																		i: StorageAllotItem
																	) =>
																		i.storageId ===
																		item.storageId
																)
																	? {
																			borderColor:
																				'#226ee7'
																	  }
																	: {}
															}
														>
															<div
																className="project-quota-storage-title"
																onClick={() =>
																	handleCheckStorage(
																		item
																	)
																}
															>
																<div className="project-quota-storage-title-content">
																	<IconFont
																		type="icon-quota"
																		style={{
																			fontSize: 28,
																			marginRight: 16
																		}}
																	/>
																	<div className="storage-title-content">
																		<p>
																			(
																			{
																				item
																					?.storageType?.[0]
																			}
																			)
																			{
																				item?.name
																			}
																			(GB)
																			{item
																				.storageType
																				?.length >
																				1 && (
																				<span className="available-domain">
																					可用区
																				</span>
																			)}
																		</p>
																		<p>
																			{
																				item?.clusterNickName
																			}
																		</p>
																	</div>
																</div>
																{selectStorages.find(
																	(
																		i: StorageAllotItem
																	) =>
																		i?.storageId ===
																		item?.storageId
																) && (
																	<div>
																		<CheckOutlined
																			style={{
																				fontSize: 17,
																				color: '#226ee7'
																			}}
																		/>
																	</div>
																)}
															</div>
															<Row>
																<Col span={20}>
																	<MidSlider
																		max={storageMaxCompute(
																			item
																		)}
																		value={
																			selectStorages.find(
																				(
																					i
																				) =>
																					i.storageId ===
																					item.storageId
																			)
																				?.allocatable
																		}
																		setValue={(
																			value
																		) =>
																			onChange(
																				value,
																				item
																			)
																		}
																		tooltip={`已使用${item.storage.used}GB`}
																		type="storage"
																		used={
																			item
																				.storage
																				.used
																		}
																		style={{
																			width: '99%'
																		}}
																		step={1}
																	/>
																</Col>
																<Col span={4}>
																	<InputNumber
																		min={0}
																		max={storageMaxCompute(
																			item
																		)}
																		value={
																			selectStorages.find(
																				(
																					i
																				) =>
																					i.storageId ===
																					item.storageId
																			)
																				?.allocatable
																		}
																		onChange={(
																			value:
																				| number
																				| null
																		) =>
																			onChange(
																				value,
																				item
																			)
																		}
																		step={1}
																		precision={
																			0
																		}
																		addonAfter="GB"
																	/>
																</Col>
															</Row>
														</div>
													);
												}
											)}
									</div>
								</>
							)}
						</div>
					</Panel>
				</Collapse>
				<Collapse expandIconPosition="end" defaultActiveKey={['1']}>
					<Panel key="1" header="备份服务器分配概览">
						<div className="project-quota-backup-content">
							{backups.length === 0 && (
								<Empty
									style={{
										width: '100%'
									}}
									image={Empty.PRESENTED_IMAGE_SIMPLE}
									description={<div>当前无数据</div>}
								/>
							)}
							{backups.length !== 0 && (
								<>
									<div className="rapid-screening-content">
										<div>快速筛选</div>
										{proClusters.map((item) => (
											<CheckableTag
												key={item.clusterId}
												checked={
													selectedBackupServerTags.indexOf(
														item.clusterId
													) > -1
												}
												onChange={(checked) => {
													handleChange(
														item.clusterId,
														checked,
														'backupserver'
													);
												}}
											>
												{item?.clusterNickName}(
												{
													backups.filter(
														(i: any) =>
															i.clusterId ===
															item.clusterId
													)?.length
												}
												)
											</CheckableTag>
										))}
									</div>
									<div className="project-quota-backup">
										{backups
											.filter((item) =>
												selectedBackupServerTags.includes(
													item.clusterId
												)
											)
											.map((item: any, index) => {
												return (
													<div
														className="project-quota-backup-item"
														style={
															selectBackups.find(
																(i: any) =>
																	i.id ===
																	item.id
															) && {
																backgroundColor:
																	'#226ee7',
																color: 'white'
															}
														}
														key={index}
														onClick={() =>
															handleCheckBackup(
																item
															)
														}
													>
														<IconFont
															type={
																selectBackups.find(
																	(i: any) =>
																		i.id ===
																		item.id
																)
																	? 'icon-quota-white'
																	: 'icon-quota'
															}
															style={{
																fontSize: 28,
																marginRight: 16
															}}
														/>
														<div className="backup-title-content">
															<p>{item?.name}</p>
															<p>
																{
																	item?.clusterNickName
																}
															</p>
														</div>
														{selectBackups.find(
															(i: any) =>
																i.id === item.id
														) && (
															<div>
																<CheckOutlined
																	style={{
																		marginLeft: 18,
																		fontSize: 30,
																		color: 'white'
																	}}
																/>
															</div>
														)}
													</div>
												);
											})}
									</div>
								</>
							)}
						</div>
					</Panel>
				</Collapse>
				<Collapse expandIconPosition="end" defaultActiveKey={['1']}>
					<Panel key="1" header="服务器分配概览">
						<div className="agent-allocation-content">
							<div className="agent-allocation-box">
								<div className="agent-allocation-title">
									<span className="mr-8">选择服务器</span>
									<Search
										allowClear
										placeholder="请输入主机名称"
										onSearch={handleSearch}
										style={{ width: 200 }}
									/>
								</div>
								<Table
									rowKey={(record) =>
										record.name +
										record.address +
										record.clusterId
									}
									dataSource={showDataSource}
									rowSelection={{
										onChange: onAgentChange,
										selectedRowKeys: primaryKeys
									}}
									pagination={false}
									scroll={{ y: 180 }}
								>
									<Table.Column
										title="主机名称"
										dataIndex="name"
										width={150}
									/>
									<Table.Column
										title="IP地址"
										dataIndex="address"
										width={130}
									/>
									<Table.Column
										title="操作系统"
										dataIndex="osType"
										width={100}
										filterMultiple={false}
										filters={[
											{ text: 'linux', value: 'linux' },
											{ text: 'darwin', value: 'darwin' }
										]}
										onFilter={(
											value: any,
											record: AgentItem
										) => record.osType === value}
									/>
									<Table.Column
										title="客户端状态"
										dataIndex="phase"
										render={agentPhaseRender}
										width={120}
										filters={[
											{ text: '运行中', value: 'Online' },
											{ text: '离线', value: 'Offline' },
											{
												text: '卸载中',
												value: 'Terminating'
											},
											{ text: '未知', value: 'unknown' }
										]}
										filterMultiple={false}
										onFilter={(
											value: any,
											record: AgentItem
										) => record.phase === value}
									/>
									<Table.Column
										title="管控控制器"
										dataIndex="controller"
										filterMultiple={false}
										filters={controllerFilters}
										onFilter={(
											value: any,
											record: AgentItem
										) => record.clusterId === value}
									/>
								</Table>
							</div>
							<Button
								type="primary"
								icon={<RightOutlined />}
								size="small"
								disabled={primaryKeys.length === 0}
								onClick={onClick}
							/>
							<div className="agent-allocation-box-1">
								<div className="agent-allocation-title">
									<span>
										已选服务器{' '}
										<span
											className="ml-8"
											style={{
												fontSize: 12,
												fontWeight: 400
											}}
										>
											{selectAgents.length}台
										</span>
									</span>
									<Button
										type="link"
										onClick={handleClear}
										disabled={selectAgents.length === 0}
									>
										清空
									</Button>
								</div>
								{selectAgents.map((item: AgentItem) => {
									return (
										<div
											className="agent-selected-list-item"
											key={
												item.name +
												item.address +
												item.clusterId
											}
										>
											<div>{item.name}</div>
											<div>{item.address}</div>
											<div>
												<Button
													icon={<MinusOutlined />}
													size="small"
													onClick={() =>
														cancelSelected(item)
													}
												/>
											</div>
										</div>
									);
								})}
							</div>
						</div>
					</Panel>
				</Collapse>
				<Divider />
				<Space>
					<Button type="primary" onClick={handleSubmit}>
						确定
					</Button>
					<Button
						onClick={() => {
							setProClusters([]);
							history.push(`/organization/project`);
						}}
					>
						取消
					</Button>
				</Space>
			</ProContent>
		</ProPage>
	);
}
const mapStateToProps = (state: StoreState) => ({
	proClusters: state.organization.proClusters
});
export default connect(mapStateToProps, {
	setProCluster,
	setProClusters,
	setEditCluster,
	deleteProClusterById
})(ProjectQuota);

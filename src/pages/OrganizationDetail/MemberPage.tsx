import React from 'react';
import { ProContent, ProHeader, ProPage } from '@/components/ProPage';
import storage from '@/utils/storage';
import UserManage from './UserManage';

export default function MemberPage(): JSX.Element {
	const isAccess = storage.getLocal('isAccessGYT');
	return (
		<ProPage>
			<ProHeader title="成员管理" />
			<ProContent>
				<UserManage isAccess={isAccess} />
			</ProContent>
		</ProPage>
	);
}

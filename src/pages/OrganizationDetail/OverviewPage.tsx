import React, { useEffect } from 'react';
import ClusterResourcesOverview from '../PlatformManagementOverview/ClusterResourcesOverview';
import AgentManagement from '../PlatformManagementOverview/AgentManagement';
import StorageService from '../PlatformManagementOverview/StorageService';
import BackupService from '../PlatformManagementOverview/BackupService';
import OrganAndProjectInfo from '../PlatformManagementOverview/OrganAndProjectInfo';
import { getMyTopic } from '@/services/workOrder';
import storage from '@/utils/storage';

export default function OverviewPage(): JSX.Element {
	const organId = storage.getSession('organId');

	useEffect(() => {
		getMyTopic({ organId }).then((res) => {
			if (res.success) {
				storage.setLocal('myTopic', res.data);
			}
		});
	}, [organId]);

	return (
		<div className="organ-overview">
			<div className="overview-left">
				<ClusterResourcesOverview isOrgan />
				<AgentManagement isOrgan />
			</div>
			<div className="overview-right">
				<OrganAndProjectInfo isOrgan />
				<StorageService isOrgan />
				<BackupService isOrgan />
			</div>
		</div>
	);
}

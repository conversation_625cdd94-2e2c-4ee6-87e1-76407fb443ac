import React, { useState } from 'react';
import { Pro<PERSON>onte<PERSON>, ProHeader, ProPage } from '@/components/ProPage';
import { Button } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import { debounce } from '@/utils/utils';
import AgentContent from '../AgentManagement/content';

export default function AgentPage(): JSX.Element {
	const [refresh, setRefresh] = useState<boolean>(false);

	return (
		<ProPage>
			<ProHeader
				title="客户端管理"
				extra={
					<Button
						onClick={debounce(() => setRefresh(!refresh))}
						id="detailRefresh"
						icon={<ReloadOutlined id="detailRefresh" />}
					/>
				}
			/>
			<ProContent>
				<AgentContent refresh={refresh} />
			</ProContent>
		</ProPage>
	);
}

import React, { useEffect, useState } from 'react';
import { notification, Modal, Statistic, Spin, Button } from 'antd';
import FormBlock from '@/components/FormBlock';
import MidProgress from '@/components/MidProgress';
import { useHistory } from 'react-router';
// * E charts v5
import ReactEChartsCore from 'echarts-for-react/lib/core';
import * as echarts from 'echarts/core';
import { GaugeChart } from 'echarts/charts';
import {
	GridComponent,
	TooltipComponent,
	TitleComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { getGaugeOption } from '@/utils/echartsOption';
import {
	agentPhaseRender,
	formatNumber,
	objectRemoveDuplicatesByKey
} from '@/utils/utils';
import {
	deleteOrgBackupServer,
	deleteOrgStorage,
	getCpuMemory,
	getOrgBackupServer,
	getOrgStorage
} from '@/services/organization';
import {
	OrganizationBackupServerItem,
	CpuMemoryItem,
	StorageListTableItem,
	StorageListItem
} from './organization.detail';
import ProTable from '@/components/ProTable';
import Actions from '@/components/Actions';
import { ColumnFilterItem } from 'antd/lib/table/interface';
import { deleteOrganAgent, getOrganAgentList } from '@/services/agent';
import { getMenu } from '@/services/common';
import { FiltersProps } from '@/types/comment';
import storage from '@/utils/storage';
import { SERVER_INTEGRATE_FOR_ORGAN } from '@/utils/const';
const LinkButton = Actions.LinkButton;
const { confirm } = Modal;
// Register the required components
echarts.use([
	TitleComponent,
	TooltipComponent,
	GridComponent,
	GaugeChart,
	CanvasRenderer
]);
interface OverviewProps {
	isAccess: boolean;
	organId: string;
	refresh: boolean;
}
export default function Overview(props: OverviewProps): JSX.Element {
	const { isAccess, organId, refresh } = props;
	const history = useHistory();
	const [option1, setOption1] = useState(getGaugeOption(0, 'CPU(核)'));
	const [option2, setOption2] = useState(getGaugeOption(0, '内存(GB)'));
	const [cpuMemory, setCpuMemory] = useState<{
		cpuUsed: number;
		cpuRequest: number;
		memoryUsed: number;
		memoryRequest: number;
	}>();
	const [backupServers, setBackupServers] = useState<
		OrganizationBackupServerItem[]
	>([]);
	const [storages, setStorages] = useState<StorageListTableItem[]>([]);
	const [clusterStorageFilters, setClusterStorageFilters] = useState<
		ColumnFilterItem[]
	>([]);
	const [clusterBackupServerFilters, setClusterBackupServerFilters] =
		useState<ColumnFilterItem[]>([]);
	const [agents, setAgents] = useState<AgentItem[]>([]);
	const [cpuLoading, setCpuLoading] = useState<boolean>(false);
	const [storageLoading, setStorageLoading] = useState<boolean>(false);
	const [backupLoading, setBackupLoading] = useState<boolean>(false);
	const [agentLoading, setAgentLoading] = useState<boolean>(false);
	const [offline, setOffline] = useState<number>();
	const [online, setOnline] = useState<number>();
	const [moreDisplay, setMoreDisplay] = useState<boolean>(false);
	const [controllerFilters, setControllerFilters] = useState<FiltersProps[]>(
		[]
	);
	// * 组织详情是否存在客户端管理 feature功能
	const [showAgentManagerMenuToOrgManagerAPI] = useState<any>(
		storage
			.getLocal('featureAPI')
			?.find(
				(item: any) => item.name === 'showAgentManagerMenuToOrgManager'
			)?.enabled ?? true
	);
	const myTopic = storage.getLocal('myTopic');
	const role = JSON.parse(storage.getLocal('role'));
	const [roleTypeIsManager, setRoleTypeIsManager] = useState<boolean>(false);
	useEffect(() => {
		if (role) {
			if (role.isAdmin) {
				setRoleTypeIsManager(true);
			} else if (role.manager) {
				setRoleTypeIsManager(true);
			} else {
				setRoleTypeIsManager(false);
			}
		}
	}, [role]);
	useEffect(() => {
		function getAllData() {
			getCpuAndMemory();
			getStorages();
			getBackups();
			getAgents();
			getMenu({ organId }).then((res) => {
				if (res.success) {
					const org = res.data.find(
						(item: MenuResItem) =>
							item.url === 'platform/organizationManagement'
					);
					if (
						org.subMenu.find(
							(item: any) => item.url === 'organization/agent'
						)
					) {
						setMoreDisplay(true);
					} else {
						setMoreDisplay(false);
					}
				}
			});
		}
		if (organId) {
			getAllData();
		}
	}, [organId, refresh]);
	const getCpuAndMemory = () => {
		setCpuLoading(true);
		getCpuMemory({ organId, detail: true })
			.then((res) => {
				if (res.success) {
					const useT = res.data?.reduce((per, cur) => {
						return per + cur.cpu.used;
					}, 0);
					const useR = res.data?.reduce((per, cur) => {
						return per + cur.cpu.request;
					}, 0);
					const memoryT = res.data?.reduce((per, cur) => {
						return per + cur.memory.used;
					}, 0);
					const memoryR = res.data?.reduce((per, cur) => {
						return per + cur.memory.request;
					}, 0);
					setCpuMemory({
						cpuUsed: useT,
						cpuRequest: useR,
						memoryUsed: memoryT,
						memoryRequest: memoryR
					});
					const cpuRate = useT / useR;
					const option1Temp = getGaugeOption(cpuRate || 0, 'CPU(核)');
					setOption1(option1Temp);
					const memoryRate = memoryT / memoryR;
					const option2Temp = getGaugeOption(
						memoryRate || 0,
						'内存(GB)'
					);
					setOption2(option2Temp);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setCpuLoading(false);
			});
	};
	const getStorages = () => {
		setStorageLoading(true);
		getOrgStorage({ organId, detail: true })
			.then((res) => {
				if (res.success) {
					const lt: StorageListTableItem[] = [];
					const list = res.data.map((item: CpuMemoryItem) => {
						item.storageList.map((i: StorageListItem) => {
							lt.push({
								...i,
								clusterId: item.clusterId,
								clusterNickName:
									item.clusterNickName || item.clusterId
							});
						});
						return {
							value: item.clusterId,
							text: item.clusterNickName || item.clusterId
						};
					});
					setClusterStorageFilters(
						objectRemoveDuplicatesByKey(list, 'value')
					);
					setStorages(lt);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setStorageLoading(false);
			});
	};
	const getBackups = () => {
		setBackupLoading(true);
		getOrgBackupServer({ organId, detail: true })
			.then((res) => {
				if (res.success) {
					setBackupServers(res.data);
					const list = res.data.map((item) => {
						return {
							value: item.clusterId,
							text: item.clusterNickName || item.clusterId
						};
					});
					setClusterBackupServerFilters(
						objectRemoveDuplicatesByKey(list, 'value')
					);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setBackupLoading(false);
			});
	};
	const getAgents = () => {
		setAgentLoading(true);
		getOrganAgentList({ organId })
			.then((res) => {
				if (res.success) {
					const controllerList = res.data?.map((item) => ({
						text: item.controller,
						value: item.clusterId
					}));
					setAgents(res.data);
					setControllerFilters(
						objectRemoveDuplicatesByKey(controllerList, 'value')
					);
					setOnline(
						res.data?.filter(
							(item: AgentItem) => item.phase === 'Online'
						).length
					);
					setOffline(
						res.data?.filter(
							(item: AgentItem) => item.phase === 'Offline'
						).length
					);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setAgentLoading(false);
			});
	};
	const actionRender = (
		value: string,
		record: OrganizationBackupServerItem,
		index: number
	) => {
		return (
			<Actions>
				<LinkButton
					onClick={() => {
						if (record.using === true) {
							notification.error({
								message: '错误',
								description:
									'当前备份服务器已被使用，无法移除！'
							});
							return;
						}
						confirm({
							title: '操作确认',
							content: '请确认是否移除该组织下的此备份服务器？',
							onOk: () => {
								return deleteOrgBackupServer({
									organId,
									clusterId: record.clusterId,
									backupServerId: record.id + ''
								}).then((res) => {
									if (res.success) {
										notification.success({
											message: '成功',
											description:
												'当前备份服务器移除成功！'
										});
										getBackups();
									} else {
										notification.error({
											message: '失败',
											description: (
												<>
													<p>{res.errorMsg}</p>
													<p>{res.errorDetail}</p>
												</>
											)
										});
									}
								});
							}
						});
					}}
				>
					删除
				</LinkButton>
			</Actions>
		);
	};
	const quotaRender = (
		value: any,
		record: StorageListTableItem,
		index: number
	) => {
		return (
			<MidProgress
				fromColor="#5C0EDF"
				toColor="#853CFF"
				unit="GB"
				toFixed={0}
				used={record.storage.used || 0}
				total={record.storage.request || 0}
			/>
		);
	};
	const clusterNickNameRender = (
		value: string,
		record: StorageListTableItem | OrganizationBackupServerItem,
		index: number
	) => {
		return record.clusterNickName ?? record.clusterId;
	};
	const actionStorageRender = (
		value: string,
		record: StorageListTableItem,
		index: number
	) => {
		return (
			<Actions>
				<LinkButton
					disabled={isAccess}
					title={
						isAccess ? '平台已接入观云台，请联系观云台管理员' : ''
					}
					onClick={() => {
						if (record.storage.used === 0 || !record.storage.used) {
							confirm({
								title: '操作确认',
								content: '请确认是否移除该组织下的此存储？',
								onOk: () => {
									return deleteOrgStorage({
										organId,
										clusterId: record.clusterId,
										storageId: record.storageId
									}).then((res) => {
										if (res.success) {
											notification.success({
												message: '成功',
												description: '当前存储移除成功'
											});
											getStorages();
										} else {
											notification.error({
												message: '失败',
												description: (
													<>
														<p>{res.errorMsg}</p>
														<p>{res.errorDetail}</p>
													</>
												)
											});
										}
									});
								}
							});
						} else {
							notification.error({
								message: '错误',
								description: '当前存储已在使用，无法移除!'
							});
						}
					}}
				>
					删除
				</LinkButton>
			</Actions>
		);
	};
	const actionAgentRender = (value: string, record: AgentItem) => {
		return (
			<Actions>
				<LinkButton
					disabled={!!record.relationMiddlewareNum}
					onClick={() => {
						confirm({
							title: '操作确认',
							content: '请确认是否移除该组织下的此客户端？',
							onOk: () => {
								return deleteOrganAgent({
									organId,
									agentName: record.name,
									clusterId: record.clusterId
								}).then((res) => {
									if (res.success) {
										notification.success({
											message: '成功',
											description: '当前客户端移除成功！'
										});
										getAgents();
									} else {
										notification.error({
											message: '错误',
											description: (
												<>
													<p>{res.errorMsg}</p>
													<p>{res.errorDetail}</p>
												</>
											)
										});
									}
								});
							}
						});
					}}
				>
					删除
				</LinkButton>
			</Actions>
		);
	};
	return (
		<>
			<FormBlock title="配额信息" className="resource-pool-info">
				<div className="resource-pool-gauge-content">
					<div className="resource-pool-gauge-item">
						<ReactEChartsCore
							echarts={echarts}
							option={option1}
							notMerge={true}
							lazyUpdate={true}
							style={{
								height: '100%',
								width: 'calc(100% - 360px)'
							}}
							showLoading={cpuLoading}
						/>
						<div className="resource-pool-gauge-info">
							总配额：
							{formatNumber(
								Number(cpuMemory?.cpuRequest) || 0,
								1
							)}
							核 | 已分配：
							{formatNumber(Number(cpuMemory?.cpuUsed) || 0, 1)}核
							| 剩余配额：
							{formatNumber(
								(Number(cpuMemory?.cpuRequest) || 0) -
									Number(cpuMemory?.cpuUsed) || 0,
								1
							)}
							核
						</div>
					</div>
					<div className="resource-pool-gauge-item">
						<ReactEChartsCore
							echarts={echarts}
							option={option2}
							notMerge={true}
							lazyUpdate={true}
							style={{
								height: '100%',
								width: 'calc(100% - 360px)'
							}}
							showLoading={cpuLoading}
						/>
						<div className="resource-pool-gauge-info">
							总配额：
							{formatNumber(
								Number(cpuMemory?.memoryRequest) || 0,
								1
							)}
							GB | 已分配：
							{formatNumber(
								Number(cpuMemory?.memoryUsed) || 0,
								1
							)}
							GB | 剩余配额：
							{formatNumber(
								(Number(cpuMemory?.memoryRequest) || 0) -
									Number(cpuMemory?.memoryUsed) || 0,
								1
							)}
							GB
						</div>
					</div>
				</div>
			</FormBlock>
			<h2 className="mt-16">存储信息</h2>
			<ProTable
				dataSource={storages}
				rowKey={(record) => record.name + record.clusterId}
				loading={storageLoading}
			>
				<ProTable.Column dataIndex="name" title="存储项名称" />
				<ProTable.Column
					dataIndex="clusterNickName"
					title="所属集群"
					filters={clusterStorageFilters}
					onFilter={(value: any, record: StorageListTableItem) =>
						record.clusterId === value
					}
				/>
				<ProTable.Column
					dataIndex="quota"
					title="存储配额"
					render={quotaRender}
				/>
				<ProTable.Column
					dataIndex="action"
					title="操作"
					render={actionStorageRender}
				/>
			</ProTable>
			<h2 className="mt-16">备份服务器信息</h2>
			<ProTable
				dataSource={backupServers}
				rowKey="id"
				loading={backupLoading}
			>
				<ProTable.Column dataIndex="name" title="备份服务器名称" />
				<ProTable.Column
					dataIndex="clusterNickName"
					title="所属集群"
					render={clusterNickNameRender}
					filters={clusterBackupServerFilters}
					onFilter={(
						value: any,
						record: OrganizationBackupServerItem
					) => record.clusterId === value}
				/>
				<ProTable.Column
					dataIndex="action"
					title="操作"
					render={actionRender}
				/>
			</ProTable>
			<h2 className="mt-16">客户端信息</h2>
			<div className="organ-overview-agent-content">
				<div className="organ-overview-agent-content-item">
					{!roleTypeIsManager &&
						!showAgentManagerMenuToOrgManagerAPI && (
							<Button
								type="primary"
								disabled={
									!myTopic?.includes(
										'ServerIntegrateForOrgan'
									)
								}
								onClick={() => {
									history.push(
										`/organization/resourceDisplay/workOrder/${SERVER_INTEGRATE_FOR_ORGAN}`
									);
								}}
								style={{ marginBottom: 8 }}
							>
								申请接入
							</Button>
						)}
					<ProTable
						scroll={{ y: 160 }}
						dataSource={agents}
						rowKey={(record) => record.name + Math.random()}
						loading={agentLoading}
					>
						<ProTable.Column dataIndex="name" title="主机名称" />
						<ProTable.Column dataIndex="address" title="IP地址" />
						<ProTable.Column
							dataIndex="osType"
							title="操作系统"
							filterMultiple={false}
							filters={[
								{ text: 'linux', value: 'linux' },
								{ text: 'darwin', value: 'darwin' }
							]}
							onFilter={(value: any, record: AgentItem) =>
								record.osType === value
							}
						/>
						<ProTable.Column
							dataIndex="controller"
							title="管控控制器"
							filterMultiple={false}
							filters={controllerFilters}
							onFilter={(value: any, record: AgentItem) =>
								record.clusterId === value
							}
						/>
						<ProTable.Column
							dataIndex="phase"
							title="客户端状态"
							render={agentPhaseRender}
							filterMultiple={false}
							filters={[
								{ text: '运行中', value: 'Online' },
								{ text: '离线', value: 'Offline' },
								{ text: '卸载中', value: 'Terminating' },
								{ text: '未知', value: 'unknown' }
							]}
							onFilter={(value: any, record: AgentItem) =>
								record.phase === value
							}
						/>
						<ProTable.Column
							dataIndex="action"
							title="操作"
							render={actionAgentRender}
						/>
					</ProTable>
				</div>
				<div className="organ-overview-agent-content-item-2">
					<div className="organ-overview-agent-content-item-title">
						服务器客户端概览
						{moreDisplay && (
							<span
								className="more"
								onClick={() =>
									history.push('/organization/agent')
								}
							>
								更多
							</span>
						)}
					</div>
					<Spin spinning={agentLoading}>
						<div className="organ-overview-agent-box">
							<div className="organ-overview-agent-card">
								<Statistic
									title="在线客户端"
									value={online}
									precision={0}
								/>
							</div>
							<div className="organ-overview-agent-card">
								<Statistic
									title="离线客户端"
									value={offline}
									precision={0}
								/>
							</div>
						</div>
					</Spin>
				</div>
			</div>
		</>
	);
}

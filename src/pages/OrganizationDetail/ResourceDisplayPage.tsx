import React, { useEffect, useState } from 'react';
import { Button } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import { ProContent, ProHeader, ProPage } from '@/components/ProPage';
import Overview from './Overview';
import storage from '@/utils/storage';
import { useHistory } from 'react-router';
import { QUOTA_REQUEST_FOR_ORGAN } from '@/utils/const';
import { debounce } from '@/utils/utils';
export default function ResourceDisplayPage(): JSX.Element {
	const history = useHistory();
	const isAccess = storage.getLocal('isAccessGYT');
	const organId = storage.getSession('organId');
	const role = JSON.parse(storage.getLocal('role'));
	const myTopic = storage.getLocal('myTopic');
	const [roleTypeIsManager, setRoleTypeIsManager] = useState<boolean>(false);
	const [refresh, setRefresh] = useState<boolean>(false);

	useEffect(() => {
		if (role) {
			if (role.isAdmin) {
				setRoleTypeIsManager(true);
			} else if (role.manager) {
				setRoleTypeIsManager(true);
			} else {
				setRoleTypeIsManager(false);
			}
		}
	}, [role]);
	return (
		<ProPage>
			<ProHeader
				title="资源概览"
				extra={
					<>
						{!roleTypeIsManager && (
							<Button
								disabled={
									!myTopic?.includes('QuotaRequestForOrgan')
								}
								type="primary"
								onClick={() => {
									history.push(
										`/organization/resourceDisplay/workOrder/${QUOTA_REQUEST_FOR_ORGAN}`
									);
								}}
							>
								申请配额
							</Button>
						)}
						<Button
							onClick={debounce(() => setRefresh(!refresh))}
							id="detailRefresh"
							icon={<ReloadOutlined id="detailRefresh" />}
						/>
					</>
				}
			/>
			<ProContent>
				<Overview
					isAccess={isAccess}
					organId={organId}
					refresh={refresh}
				/>
			</ProContent>
		</ProPage>
	);
}

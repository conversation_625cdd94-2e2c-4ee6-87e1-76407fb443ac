import { updateOrganizationUser } from '@/services/organization';
import { getRoleList } from '@/services/role';
import { formItemLayout618 } from '@/utils/const';
import { Modal, Form, Input, Select, notification } from 'antd';
import React, { useEffect, useState } from 'react';
import { EditUserProps } from '../organization.detail';
import storage from '@/utils/storage';

export default function EditUser(props: EditUserProps): JSX.Element {
	const { open, onCancel, data, organId, onRefresh } = props;
	const [form] = Form.useForm();
	const role = JSON.parse(storage.getLocal('role'));
	const [organizationRoleId, setOrganizationRoleId] = useState<number | null>(
		null
	);
	useEffect(() => {
		getRoles();
	}, []);
	useEffect(() => {
		if (data) {
			form.setFieldsValue({
				userName: data.userName,
				aliasName: data.aliasName,
				roleId: data.roleId || 'normal'
			});
		}
	}, [data]);
	const getRoles = () => {
		getRoleList({ key: '' }).then((res) => {
			if (res.success) {
				const organId = res.data.find((item) => item.weight === 2)?.id;
				setOrganizationRoleId(organId || null);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const onOk = () => {
		form.validateFields().then((values) => {
			const sendData = {
				organId,
				username: data.userName,
				roleId: values.roleId === 'normal' ? null : organizationRoleId
			};
			onCancel();
			updateOrganizationUser(sendData).then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '用户修改成功！'
					});
					onRefresh();
				} else {
					notification.error({
						message: '失败',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			});
		});
	};
	return (
		<Modal
			open={open}
			onCancel={onCancel}
			onOk={onOk}
			title="编辑"
			width={550}
		>
			<Form form={form} labelAlign="left" {...formItemLayout618}>
				<Form.Item name="userName" label="登录账户">
					<Input disabled />
				</Form.Item>
				<Form.Item name="aliasName" label="用户名">
					<Input disabled />
				</Form.Item>
				<Form.Item name="roleId" label="选择角色" required>
					<Select
						style={{ width: '100%' }}
						dropdownMatchSelectWidth={false}
					>
						<Select.Option
							value={organizationRoleId}
							disabled={
								role.userRoleList.find(
									(item: any) => item.organId === organId
								)?.roleId
							}
						>
							组织管理员
						</Select.Option>
						<Select.Option value={'normal'}>普通成员</Select.Option>
					</Select>
				</Form.Item>
			</Form>
		</Modal>
	);
}

import React, { useEffect, useState } from 'react';
import { Modal, notification, Select } from 'antd';
import ProTable from '@/components/ProTable';
import { AddUserProps } from '../organization.detail';
import { UserItem } from '@/pages/ProjectDetail/projectDetail';
import { nullRender } from '@/utils/utils';
import { getRoleList } from '@/services/role';
import {
	addOrganizationUsers,
	getOrganizationUsers
} from '@/services/organization';
import storage from '@/utils/storage';

export default function AddUser(props: AddUserProps): JSX.Element {
	const { open, onCancel, organId, onRefresh } = props;
	const role = JSON.parse(storage.getLocal('role'));
	const [users, setUsers] = useState<UserItem[]>([]);
	const [dataSource, setDataSource] = useState<UserItem[]>([]);
	const [primaryKeys, setPrimaryKeys] = useState<string[]>([]);
	const [organizationRoleId, setOrganizationRoleId] = useState<number>();
	const [loading, setLoading] = useState<boolean>(false);
	useEffect(() => {
		getRoles();
		getOrganizationUsers({
			organId,
			allocatable: true
		})
			.then((res) => {
				if (res.success) {
					setUsers(res.data);
					setDataSource(res.data);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	}, []);
	const getRoles = () => {
		getRoleList({ key: '' }).then((res) => {
			if (res.success) {
				const organId = res.data.find((item) => item.weight === 2)?.id;
				setOrganizationRoleId(organId);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const onChange = (selectedRowKeys: any) => {
		setPrimaryKeys(selectedRowKeys);
	};
	const roleChange = (value: number | string, record: UserItem) => {
		record.roleId = value;
	};
	const roleRender = (value: string, record: UserItem, index: number) => {
		return (
			<Select
				onChange={(value: any) => roleChange(value, record)}
				style={{ width: '100%' }}
				dropdownMatchSelectWidth={false}
			>
				<Select.Option
					value={organizationRoleId}
					disabled={
						role.userRoleList.find(
							(item: any) => item.organId === organId
						)?.roleId
					}
				>
					组织管理员
				</Select.Option>
				<Select.Option value={'normal'}>普通成员</Select.Option>
			</Select>
		);
	};
	const handleSearch = (value: string) => {
		const lt = users.filter((item: UserItem) =>
			item.userName.includes(value)
		);
		setDataSource(lt);
	};
	const onOk = () => {
		let list: any[] = [];
		primaryKeys.forEach((item) => {
			dataSource.forEach((i) => {
				if (i.id === Number(item)) {
					list.push(i);
				}
			});
		});
		if (list.some((item: UserItem) => item.roleId === null)) {
			notification.warning({
				message: '提示',
				description: '请选择成员的角色权限'
			});
			return;
		}
		list = list.map((item) => {
			if (item.roleId === 'normal') {
				item.roleId = null;
			}
			return item;
		});
		addOrganizationUsers({
			organId,
			userDtoList: list
		}).then((res) => {
			if (res.success) {
				notification.success({
					message: '成功',
					description: '成员新增成功'
				});
				onCancel();
				onRefresh();
			} else {
				notification.error({
					message: '失败',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	return (
		<Modal
			title="新增"
			open={open}
			onCancel={onCancel}
			onOk={onOk}
			okButtonProps={{ disabled: primaryKeys?.length === 0 }}
			width={840}
			okText="确定"
			cancelText="取消"
		>
			<ProTable
				dataSource={dataSource}
				search={{
					onSearch: handleSearch,
					placeholder: '请输入关键字搜索'
				}}
				rowSelection={{
					onChange,
					selectedRowKeys: primaryKeys
				}}
				loading={loading}
				rowKey="id"
			>
				<ProTable.Column
					title="登录账户"
					dataIndex="userName"
					ellipsis={true}
				/>
				<ProTable.Column
					title="用户名"
					dataIndex="aliasName"
					ellipsis={true}
				/>
				<ProTable.Column
					title="邮箱"
					dataIndex="email"
					render={nullRender}
					ellipsis={true}
				/>
				<ProTable.Column
					title="创建时间"
					dataIndex="createTime"
					render={nullRender}
				/>
				<ProTable.Column
					title="角色"
					dataIndex="role"
					width={200}
					render={roleRender}
				/>
			</ProTable>
		</Modal>
	);
}

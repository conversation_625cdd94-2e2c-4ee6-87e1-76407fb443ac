import React, { useEffect, useState } from 'react';
import { Button, notification, Modal } from 'antd';
import Actions from '@/components/Actions';
import ProTable from '@/components/ProTable';
import { UserItem } from '@/pages/ProjectDetail/projectDetail';
import {
	deleteOrganizationUser,
	getOrganizationUsers
} from '@/services/organization';
import { nullRender } from '@/utils/utils';
import AddUser from './AddUser';
import EditUser from './EditUser';
import storage from '@/utils/storage';

const { confirm } = Modal;
const LinkButton = Actions.LinkButton;
interface UserManageProps {
	isAccess: boolean;
}
export default function UserManage(props: UserManageProps): JSX.Element {
	const { isAccess } = props;
	const role = JSON.parse(storage.getLocal('role'));
	const organId = storage.getSession('organId');
	const [users, setUsers] = useState<UserItem[]>([]);
	const [dataSource, setDataSource] = useState<UserItem[]>([]);
	const [open, setOpen] = useState<boolean>(false);
	const [editUser, setEditUSer] = useState<UserItem>();
	const [editOpen, setEditOpen] = useState<boolean>(false);
	const [loading, setLoading] = useState<boolean>(false);
	useEffect(() => {
		let mounted = true;
		if (organId) {
			if (mounted) {
				getData();
			}
		}
		return () => {
			mounted = false;
		};
	}, [organId]);
	const getData = () => {
		setLoading(true);
		getOrganizationUsers({
			organId: organId,
			allocatable: false
		})
			.then((res) => {
				if (res.success) {
					setUsers(res.data);
					setDataSource(res.data);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const handleSearch = (value: string) => {
		const list = users.filter((item) => item.userName.includes(value));
		setDataSource(list);
	};
	const roleNameRender = (value: string) => value || '普通成员';
	const Operation = {
		primary: (
			<Button
				disabled={isAccess}
				title={isAccess ? '平台已接入观云台，请联系观云台管理员' : ''}
				type="primary"
				onClick={() => setOpen(true)}
			>
				新增
			</Button>
		)
	};
	const actionRender = (value: any, record: UserItem, index: number) => {
		return (
			<Actions>
				<LinkButton
					onClick={() => {
						setEditUSer(record);
						setEditOpen(true);
					}}
					disabled={
						isAccess ||
						role.userRoleList.some(
							(item: any) =>
								item.organId === organId && item.roleId === 5
						)
					}
					title={
						isAccess ? '平台已接入观云台，请联系观云台管理员' : ''
					}
				>
					编辑
				</LinkButton>
				<LinkButton
					disabled={
						isAccess ||
						(role.userRoleList.find(
							(item: any) => item.organId === organId
						)?.roleId &&
							record.roleId === 5)
					}
					title={
						isAccess ? '平台已接入观云台，请联系观云台管理员' : ''
					}
					onClick={() => {
						confirm({
							title: '操作确认',
							content: '请确认是否删除该用户?',
							onOk: () => {
								return deleteOrganizationUser({
									organId: organId,
									username: record.userName
								}).then((res) => {
									if (res.success) {
										notification.success({
											message: '成功',
											description: '成员删除成功'
										});
										getData();
									} else {
										notification.error({
											message: '失败',
											description: (
												<>
													<p>{res.errorMsg}</p>
													<p>{res.errorDetail}</p>
												</>
											)
										});
									}
								});
							}
						});
					}}
				>
					删除
				</LinkButton>
			</Actions>
		);
	};
	return (
		<>
			<ProTable
				dataSource={dataSource}
				rowKey="id"
				operation={Operation}
				showRefresh
				onRefresh={getData}
				search={{
					style: { width: '250px' },
					onSearch: handleSearch,
					placeholder: '请输入登录账户进行搜索'
				}}
				loading={loading}
			>
				<ProTable.Column title="登录账户" dataIndex="userName" />
				<ProTable.Column title="用户名" dataIndex="aliasName" />
				<ProTable.Column
					title="角色"
					dataIndex="roleName"
					render={roleNameRender}
				/>
				<ProTable.Column
					title="邮箱"
					dataIndex="email"
					render={nullRender}
				/>
				<ProTable.Column
					title="操作"
					dataIndex="action"
					render={actionRender}
				/>
			</ProTable>
			{open && (
				<AddUser
					open={open}
					onCancel={() => setOpen(false)}
					onRefresh={getData}
					organId={organId}
				/>
			)}
			{editOpen && editUser && (
				<EditUser
					open={editOpen}
					data={editUser}
					onRefresh={getData}
					onCancel={() => setEditOpen(false)}
					organId={organId}
				/>
			)}
		</>
	);
}

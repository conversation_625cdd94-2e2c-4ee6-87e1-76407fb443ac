import { resProps } from '@/types/comment';
export interface DetailParams {
	organId: string;
	activeKey: string;
	projectId?: string;
	clusterId?: string;
	projectName?: string;
}
export interface AddProClusterParams extends DetailParams {
	projectId: string;
	projectName: string;
}
export interface StorageListItem {
	name: string;
	storage: {
		allocatable: number;
		request: number;
		total: number;
		usable: number;
		usage: number;
		used: number;
		sused: number;
		oused: number;
	};
	storageClass: string[];
	storageId: string;
	storageType: string[];
}
export interface StorageListTableItem extends StorageListItem {
	clusterId: string;
	clusterNickName: string;
}
export interface CpuMemoryItem {
	clusterId: string;
	clusterNickName: string;
	cpu: {
		request: number;
		total: number;
		usable: number;
		usage: number;
		used: number;
		allocatable?: number;
		occupy: number;
	};
	memory: {
		request: number;
		total: number;
		usable: number;
		usage: number;
		used: number;
		allocatable?: number;
		occupy: number;
	};
	storageList: StorageListItem[];
}
export interface CpuMemoryResProps extends resProps {
	data: CpuMemoryItem[];
}
export interface NamespaceCpuMemoryResProps extends resProps {
	data: CpuMemoryItem;
}
export interface BackupPositionItem {
	backupPosition: string;
	backupServerId: number;
	backupServerName: string;
	backupTaskNum: number;
	createTime: string;
	id: number;
	name: string;
	organId: string;
	projectId: string;
	projectName: string;
}
export interface BackupServerItem {
	backupServerId: number;
	createTime: string;
	host: string;
	id: number;
	password: string;
	port: string;
	protocol: string;
	serverUsage: string;
	type: number;
	username: string;
}
export interface OrganizationBackupServerItem {
	clusterId: string;
	clusterNickName: string;
	id: number;
	name: string;
	positionList: BackupPositionItem[] | null;
	serverDetailList: BackupServerItem[] | null;
	serverType: string;
	type: number;
	using: null | boolean;
}
export interface OrgBackupServerResProps extends resProps {
	data: OrganizationBackupServerItem[];
}
export interface EditProjectProps {
	open: boolean;
	organId: string;
	onCancel: () => void;
	data?: ProjectItem;
	onRefresh: () => void;
}

export interface OrganizationUsersRes extends resProps {
	data: UserItem[];
}
export interface getOrganizationProjectsRes extends resProps {
	data: ProjectItem[];
}
export interface AddUserProps {
	open: boolean;
	organId: string;
	onCancel: () => void;
	onRefresh: () => void;
}
export interface EditUserProps {
	open: boolean;
	onCancel: () => void;
	data: UserItem;
	organId: string;
	onRefresh: () => void;
}
export interface ProjectQuotaProps {
	setProCluster: (value: any) => void;
	setProClusters: (value: any) => void;
	proClusters: any[];
	setEditCluster: (value: any) => void;
	deleteProClusterById: (values: any) => void;
}

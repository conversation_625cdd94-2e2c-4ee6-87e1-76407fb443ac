.site-collapse-custom-collapse .site-collapse-custom-panel {
	overflow: hidden;
	background: @black-10;
	border-radius: 2px;
}
.active-active-box {
	.ant-collapse {
		border: none !important;
		border-top: 1px solid #d7d7d7 !important;
	}
	.ant-collapse-content {
		border-top: none !important;
		.ant-collapse-content-box {
			padding-left: 40px !important;
			padding-top: 0px !important;
		}
	}
	.active-list-content {
		display: flex;
		flex-wrap: wrap;
		column-gap: 24px;
		row-gap: 27px;
	}
}
.active-card-box {
	width: 278px;
	height: 132px;
	background: #ffffff;
	box-shadow: 0px 6px 14px 3px rgba(0, 0, 0, 0.02),
		0px 3px 8px 0px rgba(0, 0, 0, 0.05),
		0px 1px 3px -6px rgba(0, 0, 0, 0.08),
		0px 1px 3px -6px rgba(0, 0, 0, 0.08);
	border-radius: 2px;
	border: 1px solid #e7e9eb;
	padding: 24px;
	transition: 0.3s ease-in;
	cursor: pointer;
	&.active:hover {
		transform: translateY(-3px);
		border: 1px solid #226ee7;
		transition: 0.3s ease-in;
		.active-card-header {
			.active-card-title-box {
				.active-card-title {
					color: @blue-base;
				}
			}
		}
	}
	.active-card-header {
		display: flex;
		.active-card-title-box {
			.active-card-title {
				font-weight: @font-weight;
				color: @black-2;
			}
		}
	}
	.active-card-footer {
		display: flex;
		justify-content: space-between;
		margin-top: 16px;
		color: @black-5;
		line-height: 20px;
		font-weight: @font-weight-sm;
	}
}

// 可用区配置
.zeus-area-config-content {
	display: flex;
	width: 100%;
	margin-top: @margin;
	.zeus-area-label-content {
		width: 120px;
		height: 18px;
		font-size: @font-2;
		font-weight: @font-weight-sm;
		color: @text-color-title;
		line-height: @line-height-2;
		margin-top: 20px;
	}
	.zeus-area-ip-content {
		width: 100%;
		max-height: 100%;
		border-radius: 4px 4px 0px 0px;
		border: 1px solid #d9d9d9;
		margin-bottom: 16px;
		.zeus-area-search-content {
			display: flex;
			height: 56px;
			background: #fafafa;
			border-radius: 4px 4px 0px 0px;
			border-bottom: 1px solid #d9d9d9;
			align-items: center;
			padding: 12px 24px;
			div:nth-child(1) {
				margin-right: 24px;
			}
		}
		.zeus-area-node-content {
			width: 100%;
			padding: 24px;
			display: flex;
			flex-wrap: wrap;
			justify-content: flex-start;
			column-gap: 21px;
			row-gap: 24px;
			.zeus-area-node-item {
				width: 200px;
				height: 40px;
				display: flex;
				align-items: center;
				background: @white;
				padding: 12px 16px;
				box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.13);
				// margin-top: 24px;
				// margin-right: 16px;
				position: relative;
				border-radius: 4px;
				cursor: pointer;
				&:hover {
					border: 1px solid #4c92f5;
				}
				& > span {
					margin-left: 8px;
					word-break: break-all;
				}
				&.disabled {
					background-color: #f3f3f3;
					cursor: not-allowed;
					&:hover {
						border: none;
					}
				}
			}
			.zeus-area-node-item-active {
				border: 1px solid #4c92f5;
			}
		}
	}
}
.zeus-submit-btn-content {
	& > button {
		margin-right: 12px;
	}
}
.zeus-active-detail-resource-content {
	width: 100%;
	height: 178px;
	background: #ffffff;
	box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
	display: flex;
	align-items: center;
	margin-bottom: 20px;
	.zeus-active-detail-pie-content {
		height: 200px;
		width: 416px;
		margin-left: -100px;
		margin-top: 40px;
		position: relative;
		&::after {
			content: '';
			position: absolute;
			height: 130px;
			width: 1px;
			right: -20px;
			top: 22px;
			border-right: 1px solid #e3e4e6;
		}
		// flex-basis: 300px;
		// width: 250px;
	}
	.zeus-active-detail-dashboard-content {
		height: 140px;
		width: 400px;
		// min-width: 160px;
		display: flex;
		// justify-content: space-between;
		align-items: center;
		position: relative;
		margin-left: 26px;
		&:nth-child(2)::after {
			content: '';
			position: absolute;
			height: 130px;
			width: 1px;
			right: -20px;
			top: 5px;
			border-right: 1px solid #e3e4e6;
		}
		.zeus-active-detail-dashboard-info {
			width: 250px;
		}
	}
}
.zeus-error-circle,
.zeus-success-circle {
	margin-left: 15px;
	position: relative;
	&::before {
		content: '';
		position: absolute;
		left: -15px;
		top: 5px;
		width: 8px;
		height: 8px;
		border-radius: 4px;
		background-color: #52c41a;
	}
}
.zeus-error-circle {
	&::before {
		background-color: #ff4d4f;
	}
}
.available-domain {
	display: inline-block;
	margin-left: 8px;
	padding: 2px 4px;
	background: #eee;
	border-radius: 7px;
	line-height: 14px;
	color: #c80000;
	&.domain-normal {
		background: #ccc;
	}
}

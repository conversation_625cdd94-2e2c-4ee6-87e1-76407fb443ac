import React, { useEffect, useState } from 'react';
import { Button, notification, Modal } from 'antd';
import moment from 'moment';
import Actions from '@/components/Actions';
import MidProgress from '@/components/MidProgress';
import ProTable from '@/components/ProTable';
import { updateDomain } from '@/services/activeActive';
import { getNamespaces } from '@/services/common';
import { nullRender } from '@/utils/utils';
import { NamespaceResourceProps } from '../ResourcePoolManagement/resource.pool';
import AccessNamespace from './accessNamespace';
import { NamespaceTableProps } from './activeActive';

const { confirm } = Modal;
const LinkButton = Actions.LinkButton;
export default function NamespaceTable(
	props: NamespaceTableProps
): JSX.Element {
	const { clusterId } = props;
	const [keyword, setKeyword] = useState<string>('');
	const [visible, setVisible] = useState<boolean>(false);
	const [dataSource, setDataSource] = useState<NamespaceResourceProps[]>([]);
	useEffect(() => {
		let mounted = true;
		getNamespaces({
			clusterId: clusterId,
			all: true,
			withQuota: true,
			withMiddleware: true,
			keyword: keyword
		}).then((res) => {
			if (res.success) {
				if (mounted) {
					setDataSource(
						res.data.filter(
							(item: NamespaceResourceProps) =>
								item.availableDomain === true
						)
					);
				}
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
		return () => {
			mounted = false;
		};
	}, [keyword]);
	const onCreate = (value: any) => {
		handleChangeDomain(true, value.name);
	};
	const closeActive = (name: string) => {
		confirm({
			title: '操作确认',
			content: '是否确认关闭该命名空间的可用区操作？',
			onOk: () => {
				handleChangeDomain(false, name);
			}
		});
	};
	const handleChangeDomain = (value: boolean, name: string) => {
		updateDomain({
			clusterId: clusterId,
			name: name,
			availableDomain: value
		}).then((res) => {
			if (res.success) {
				const msg = value ? '命名空间接入成功' : '可用区关闭成功';
				notification.success({
					message: '成功',
					description: msg
				});
				setVisible(false);
				getData();
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const Operation = {
		primary: (
			<Button type="primary" onClick={() => setVisible(true)}>
				接入命名空间
			</Button>
		)
	};
	const handleSearch = (value: string) => {
		setKeyword(value);
	};
	const getData = () => {
		getNamespaces({
			clusterId: clusterId,
			all: true,
			withQuota: true,
			withMiddleware: true,
			keyword: keyword
		}).then((res) => {
			if (res.success) {
				setDataSource(
					res.data.filter(
						(item: NamespaceResourceProps) =>
							item.availableDomain === true
					)
				);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const nameRender = (
		value: any,
		record: NamespaceResourceProps,
		index: number
	) => {
		return <span>{record.aliasName || record.name}</span>;
	};
	const memoryRender = (
		value: any,
		record: NamespaceResourceProps,
		index: number
	) => {
		return (
			<MidProgress
				fromColor="#1AC1C4"
				toColor="#74DDDF"
				unit="GB"
				used={record.quotas?.memory.used}
				total={record.quotas?.memory.request}
			/>
		);
	};
	const cpuRender = (
		value: any,
		record: NamespaceResourceProps,
		index: number
	) => {
		return (
			<MidProgress
				fromColor="#226EE7"
				toColor="#47A7F5"
				unit="Core"
				used={record.quotas?.cpu.used}
				total={record.quotas?.cpu.request}
			/>
		);
	};
	const actionRender = (value: string, record: NamespaceResourceProps) => {
		return (
			<Actions>
				<LinkButton onClick={() => closeActive(record.name)}>
					关闭可用区
				</LinkButton>
			</Actions>
		);
	};
	return (
		<>
			<ProTable
				dataSource={dataSource}
				rowKey="name"
				operation={Operation}
				// showRefresh
				onRefresh={getData}
				search={{
					onSearch: handleSearch,
					placeholder: '请输入命名空间名称搜索'
				}}
			>
				<ProTable.Column
					title="命名空间"
					dataIndex="aliasName"
					render={nameRender}
				/>
				<ProTable.Column
					title="命名空间英文名"
					dataIndex="name"
					ellipsis={true}
				/>
				<ProTable.Column
					title="CPU配额（核）"
					dataIndex="cpu"
					render={cpuRender}
					sorter={(a, b) => {
						let aPer = 0;
						let bPer = 0;
						if (a.quotas) {
							aPer =
								(a.quotas?.cpu?.used || 0) /
								(a.quotas?.cpu?.request || 0);
						}
						if (b.quotas) {
							bPer =
								(b.quotas?.cpu?.used || 0) /
								(b.quotas?.cpu?.request || 0);
						}
						return aPer - bPer;
					}}
				/>
				<ProTable.Column
					title="内存配额（GB）"
					dataIndex="memory"
					render={memoryRender}
					sorter={(a, b) => {
						let aPer = 0;
						let bPer = 0;
						if (a.quotas) {
							aPer =
								(a.quotas?.memory?.used || 0) /
								(a.quotas?.memory?.request || 0);
						}
						if (b.quotas) {
							bPer =
								(b.quotas?.memory?.used || 0) /
								(b.quotas?.memory?.request || 0);
						}
						return aPer - bPer;
					}}
				/>
				<ProTable.Column
					title="已发布服务"
					dataIndex="middlewareReplicas"
					render={nullRender}
					sorter={(
						a: NamespaceResourceProps,
						b: NamespaceResourceProps
					) =>
						(a.middlewareReplicas || 0) -
						(b.middlewareReplicas || 0)
					}
				/>
				<ProTable.Column
					title="创建时间"
					dataIndex="createTime"
					width={180}
					sorter={(
						a: NamespaceResourceProps,
						b: NamespaceResourceProps
					) =>
						moment(a.createTime).unix() -
						moment(b.createTime).unix()
					}
				/>
				<ProTable.Column
					title="操作"
					dataIndex="action"
					render={actionRender}
				/>
			</ProTable>
			{visible && (
				<AccessNamespace
					visible={visible}
					onCancel={() => setVisible(false)}
					clusterId={clusterId}
					onCreate={onCreate}
					onRefresh={getData}
				/>
			)}
		</>
	);
}

.zeus-active-data-card,
.zeus-active-no-data-card {
	width: 378px;
	height: 330px;
	box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
	cursor: pointer;
	.zeus-active-data-title-content,
	.zeus-active-no-data-title-content{
		height: 100px;
		width: 100%;
		background: @black;
		opacity: 0.25;
		position: relative;
		.zeus-active-data-bg1,
		.zeus-active-no-data-bg1 {
			vertical-align: top;
		}
		.zeus-active-data-bg2,
		.zeus-active-no-data-bg2 {
			margin-left: 32px;
		}
		.zeus-active-data-title,
		.zeus-active-no-data-title {
			position: absolute;
			top: 0;
			width: 100%;
			height: 100%;
			font-size: 20px;
			font-weight: @font-weight;
			padding: 35px 30px;
			display: flex;
			align-items: center;
			.zeus-active-data-label {
				max-width: 250px;
				// height: 22px;
				line-height: 22px;
				color: @white;
				//.mixin(textEllipsis);;
			}
			.zeus-active-data-edit-icon {
				color: @white;
			}
		}
		.zeus-active-data-title {
			display: flex;
			justify-content: space-between;
		}
	}
	.zeus-active-data-title-content {
		background: #226EE7;
		opacity: 1;
		.zeus-active-data-to-detail {
			width: 24px;
			height: 24px;
			text-align: center;
			border-radius: 12px;
			line-height: 22px;
			background: rgba(201, 229, 255, 0.36);
			& > span {
				opacity: 1;
				color: @white;
				line-height: 20px;
				margin-left: 2px;
			}
		}
	}
	.zeus-active-no-data-info-content {
		width: 100%;
		height: 230px;
		line-height: 230px;
		text-align: center;
		color: @primary-color;
	}
	.zeus-active-data-info-content{
		width: 100%;
		height: 230px;
		padding: 26px 16px 12px 16px;
		.zeus-active-data-source-content{
			width: 100%;
			height: 72px;
			background: #FAFAFA;
			padding: 8px 7px;
			.ant-progress-line {
				width: 92%;
			}
			& > p {
				color: @black;
			}
			&:nth-child(2) {
				margin-bottom: 8px;
			}
		}
		.zeus-active-data-status{
			margin-bottom: 18px;
			color: @black;
			display: flex;
			align-items: center;
			.zeus-active-data-status-circle {
				width: 8px;
				height: 8px;
				border-radius: 4px;
				margin-left: 10px;
				margin-right: 8px;
			}
		}
	}
}
// .zeus-active-data-card{
// 	height: 330px;
// }
.success {
	background: #52C41A;
}
.error {
	background: #FF4D4F;
}

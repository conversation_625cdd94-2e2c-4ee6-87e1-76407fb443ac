import React from 'react';
import {
	PlusOutlined,
	DeleteOutlined,
	ReloadOutlined,
	CheckCircleFilled,
	CloseCircleFilled,
	SyncOutlined,
	EditOutlined
} from '@ant-design/icons';
import origin from '@/assets/images/o-instance.svg';
import backup from '@/assets/images/backup-instance.svg';
import PasswordDisplay from '@/components/PasswordDisplay';
import './index.less';

interface disasterCardProps {
	originData?: any;
	backupData?: any;
	toCreateBackup?: () => void;
	editInstance?: () => void;
	deleteInstance?: () => void;
	toDetail?: () => void;
	isMaster?: boolean;
}
export const DisasterOriginCard: (props: disasterCardProps) => JSX.Element = (
	props: disasterCardProps
) => {
	const { originData, isMaster, editInstance } = props;
	return (
		<div className="disaster-card">
			<div className="disaster-card-title">
				<img src={origin} />
				<span>主管理平台</span>
			</div>
			<ul className="disaster-card-info">
				<li>
					<label>名称 :</label>
					<span>{originData?.name || '/'}</span>
				</li>
				<li>
					<label>运行状态 :</label>
					{originData?.phase === 'Running' ? (
						<div>
							<CheckCircleFilled
								style={{
									color: '#00A700',
									marginRight: 4
								}}
							/>
							运行正常
						</div>
					) : (
						<div>
							<CloseCircleFilled
								style={{
									color: '#C80000',
									marginRight: 4
								}}
							/>
							运行异常
						</div>
					)}
				</li>
				<li>
					<label>连接地址 :</label>
					<span>
						{originData
							? `${originData?.protocol || ''}://${
									originData?.host || ''
							  }:${originData?.port || ''}`
							: ''}
					</span>
				</li>
			</ul>
			{isMaster ? (
				<div className="disaster-card-edit" onClick={editInstance}>
					<EditOutlined />
					<span>编辑</span>
				</div>
			) : null}
		</div>
	);
};

export const DisasterOriginCardNone: (
	props: disasterCardProps
) => JSX.Element = (props: disasterCardProps) => {
	const { toCreateBackup, isMaster } = props;
	return (
		<div className="disaster-card">
			<div className="disaster-card-title-backup">
				<img src={backup} />
				<span>{isMaster ? '主' : '备用'}管理平台</span>
			</div>
			<ul className="disaster-card-none">
				{isMaster ? (
					<div className="disaster-card-add" onClick={toCreateBackup}>
						<PlusOutlined />
						<span>添加{isMaster ? '主' : '备用'}管理平台</span>
					</div>
				) : (
					<div>暂无主平台接入</div>
				)}
			</ul>
		</div>
	);
};

export const DisasterBackupCardNone: (
	props: disasterCardProps
) => JSX.Element = (props: disasterCardProps) => {
	const { toCreateBackup } = props;
	return (
		<div className="disaster-card">
			<div className="disaster-card-title-backup">
				<img src={backup} />
				<span>备用管理平台</span>
			</div>
			<ul className="disaster-card-none">
				<div className="disaster-card-add" onClick={toCreateBackup}>
					<PlusOutlined />
					<span>添加备用管理平台</span>
				</div>
			</ul>
		</div>
	);
};

export const DisasterBackupCard: (props: disasterCardProps) => JSX.Element = (
	props: disasterCardProps
) => {
	const { backupData, isMaster, editInstance, toDetail } = props;
	return (
		<div className="disaster-card">
			<div className="disaster-card-title-backup" onClick={toDetail}>
				<img src={backup} />
				<span>灾备服务信息</span>
			</div>
			<ul className="disaster-card-info">
				<li>
					<label>名称 :</label>
					<span>{backupData?.name || '/'}</span>
				</li>
				<li>
					<label>运行状态 :</label>
					{backupData?.phase === 'Running' ? (
						<div>
							<CheckCircleFilled
								style={{
									color: '#00A700',
									marginRight: 4
								}}
							/>
							运行正常
						</div>
					) : (
						<div>
							<CloseCircleFilled
								style={{
									color: '#C80000',
									marginRight: 4
								}}
							/>
							运行异常
						</div>
					)}
				</li>
				<li>
					<label>连接地址 :</label>
					<span>
						{backupData
							? `${backupData?.protocol || ''}://${
									backupData?.host || ''
							  }:${backupData?.port || ''}`
							: ''}
					</span>
				</li>
			</ul>
			{isMaster ? (
				<div className="disaster-card-edit" onClick={editInstance}>
					<EditOutlined />
					<span>编辑</span>
				</div>
			) : null}
		</div>
	);
};

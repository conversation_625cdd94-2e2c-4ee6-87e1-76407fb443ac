.platform-disaster {
	.disaster-card-content {
		display: flex;
	}
	.disaster-card {
		width: 379px;
		height: 370px;
		background: @white;
		box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.09);
		border-radius: @border-radius-xl;
		margin-right: 70px;
		position: relative;
		.disaster-card-title {
			width: 100%;
			height: 135px;
			background: @primary-color;
			display: flex;
			align-items: center;
			border-radius: @border-radius-xl @border-radius-xl 0px 0px;
			cursor: pointer;
			& img {
				margin-top: 10px;
			}
			& span {
				height: 33px;
				font-size: 24px;
				font-weight: @font-weight;
				color: @white;
				line-height: 33px;
				margin-left: 24px;
			}
		}
		.disaster-card-title-backup {
			width: 100%;
			height: 135px;
			background: #dedede;
			display: flex;
			align-items: center;
			border-radius: @border-radius-xl @border-radius-xl 0px 0px;
			cursor: pointer;
			& img {
				margin-top: 10px;
			}
			& span {
				height: 33px;
				font-size: 24px;
				font-weight: @font-weight;
				color: @black;
				line-height: 33px;
				margin-left: 24px;
			}
		}
		.disaster-card-info {
			width: 100%;
			padding: 24px 32px 0px;
			& li {
				height: 18px;
				font-size: @font-1;
				font-weight: @font-weight-sm;
				color: @text-color-title;
				line-height: @line-height-3;
				margin-bottom: 16px;
				display: flex;
				& label {
					margin-right: 8px;
				}
				& span {
					max-width: 185px;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}
		}
		.disaster-card-none {
			text-align: center;
			padding-top: 90px;
			.disaster-card-add {
				color: @primary-color;
				vertical-align: middle;
				cursor: pointer;
				@include lineHeight(18px);
				& span {
					margin-left: 8px;
				}
			}
		}
		.disaster-card-edit {
			width: 100%;
			height: 32px;
			line-height: 32px;
			border-top: 1px solid #ebebeb;
			text-align: center;
			color: #7f7f7f;
			position: absolute;
			bottom: 0;
			cursor: pointer;
			visibility: hidden;
			& span {
				margin-left: 4px;
			}
		}
		&:hover {
			.disaster-card-edit {
				visibility: initial;
			}
		}
	}
	.ant-descriptions-item-content {
		align-items: center;
	}
}

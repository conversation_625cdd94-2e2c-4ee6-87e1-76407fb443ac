import React, { useState, useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import {
	Modal,
	notification,
	Button,
	Form,
	Input,
	Select,
	InputNumber,
	Result
} from 'antd';
import { CheckCircleFilled, ReloadOutlined } from '@ant-design/icons';
import { ProPage, ProHeader, ProContent } from '@/components/ProPage';
import DataFields from '@/components/DataFields';
import storage from '@/utils/storage';
import { TOKEN } from '@/services/request';

import {
	DisasterOriginCard,
	DisasterBackupCardNone,
	DisasterBackupCard
} from './disasterCard';

import {
	queryAccessInfo,
	saveDisaster,
	switchDisaster
} from '@/services/disaster';
import { formItemLayout618 } from '@/utils/const';

import './index.less';
import { DisasterOriginCardNone } from './disasterCard';

const clusterInfo = {
	title: '服务详情'
};
const runStateInit = {
	title: '应急切换',
	replicatePhase: '',
	lastSwitchTime: '',
	lastUpdateTime: ''
};
const originDataInit = {
	name: '',
	port: '',
	protocol: '',
	host: '',
	phase: ''
};
const backupDataInit = {
	name: '',
	port: '',
	protocol: '',
	host: '',
	phase: ''
};
interface OriginProps {
	name: string;
	port: string;
	protocol: string;
	phase: string;
	host: string;
}
interface runStateProps {
	title: string;
	replicatePhase: string;
	lastSwitchTime: string;
	lastUpdateTime: string;
}

export default function PlatformDisaster(): JSX.Element {
	const [form] = Form.useForm();
	const [originData, setOriginData] = useState<OriginProps>(originDataInit);
	const [backupData, setBackupData] = useState<OriginProps>(backupDataInit);
	const [runState, setRunState] = useState<runStateProps>(runStateInit);
	const [visible, setVisible] = useState<boolean>(false);
	const [cardVisible, setCardVisible] = useState<boolean>(false);
	const [isMaster, setIsMaster] = useState<boolean>(false);
	const [type, setType] = useState<string>('');
	const [successFlag, setSuccessFlag] = useState<boolean>(false);
	const [errorFlag, setErrorFlag] = useState<boolean>(false);
	const history = useHistory();
	useEffect(() => {
		getData();
	}, []);
	const getData = () => {
		queryAccessInfo().then((res) => {
			if (res.success) {
				setIsMaster(res.data.isMaster);
				setCardVisible(!res.data.isMaster);
				setOriginData(
					res.data.isMaster ? res.data.local : res.data.relation
				);
				setBackupData(
					res.data.isMaster ? res.data.relation : res.data.local
				);
				setRunState({
					title: '应急切换',
					replicatePhase: res.data?.replicatePhase,
					lastSwitchTime: res.data?.lastSwitchTime || '--',
					lastUpdateTime: res.data?.lastUpdateTime || '--'
				});
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const items: any = [
		{
			dataIndex: 'title',
			render: (val: string) => (
				<div className="title-content">
					<div className="blue-line"></div>
					<div className="detail-title">{val}</div>
				</div>
			)
		}
	];
	const runItems: any = [
		{
			dataIndex: 'title',
			render: (val: string) => (
				<div>
					<div className="title-content">
						<div className="blue-line"></div>
						<div className="detail-title">{val}</div>
					</div>
					<div>
						<Button
							type="primary"
							disabled={
								!backupData ||
								(backupData.phase !== 'Running' && isMaster)
							}
							onClick={() => {
								Modal.confirm({
									title: '操作',
									content:
										'该操作不可逆，只允许切换一次，是否继续',
									onOk: () => {
										switchDisaster({ isMaster }).then(
											(res) => {
												if (res.success) {
													notification.success({
														message: '成功',
														description:
															'灾备切换成功'
													});
													storage.removeLocal(TOKEN);
													window.location.reload();
												} else {
													notification.error({
														message: '失败',
														description:
															res.errorMsg
													});
												}
											}
										);
									}
								});
							}}
						>
							手动切换
						</Button>
					</div>
				</div>
			),
			span: 24
		},
		{
			dataIndex: 'replicatePhase',
			label: 'MySQL同步器状态',
			render: (val: any) => {
				if (val === 'Syncing') {
					return (
						<div>
							<CheckCircleFilled
								style={{
									color: '#00A700',
									marginRight: 4
								}}
							/>
							同步中
						</div>
					);
				} else if (val === 'StopSyncing') {
					return (
						<div>
							<CheckCircleFilled
								style={{
									color: '#00A700',
									marginRight: 4
								}}
							/>
							暂停同步
						</div>
					);
				} else {
					return '--';
				}
			}
		},
		{
			dataIndex: 'lastUpdateTime',
			label: 'MySQL同步器最近同步时间'
		},
		{
			dataIndex: 'lastSwitchTime',
			label: '上次切换时间'
		}
	];

	const onOk = () => {
		form.validateFields().then((values) => {
			if (
				type !== 'chief' &&
				originData?.protocol + originData?.host + originData?.port ===
					values?.protocol + values?.host + values?.port
			) {
				notification.warning({
					message: '提示',
					description: '备用平台地址不能和主平台一致'
				});
				return;
			}
			saveDisaster({
				...values,
				isRelation: type === 'chief' ? false : true
			}).then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '保存成功'
					});
					getData();
					setVisible(false);
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		});
	};

	if (successFlag || errorFlag) {
		return (
			<ProPage>
				<ProContent>
					<Result
						status={successFlag ? 'success' : 'error'}
						title={successFlag ? '切换成功' : '切换失败'}
						subTitle={
							successFlag
								? '当前平台流量已切换到灾备平台，服务将会重启'
								: '当前平台流量切换失败，请联系管理员解决'
						}
						extra={[
							<Button
								key="list"
								type="primary"
								onClick={() => {
									queryAccessInfo().then((res) => {
										if (res.success) {
											setIsMaster(res.data.isMaster);
											setSuccessFlag(false);
										} else {
											setCardVisible(true);
										}
									});
								}}
							>
								{successFlag ? '立即使用' : '立即重试'}
							</Button>
						]}
					/>
				</ProContent>
			</ProPage>
		);
	}

	return (
		<ProPage className="platform-disaster">
			<ProHeader
				title="平台灾备"
				extra={
					<Button
						onClick={() => getData()}
						id="detailRefresh"
						icon={<ReloadOutlined id="detailRefresh" />}
					/>
				}
			/>
			<ProContent>
				<div>
					<DataFields
						dataSource={runState}
						items={runItems}
						className="refresh-color"
					/>
					<div className="detail-divider"></div>
					<DataFields dataSource={clusterInfo} items={items} />
					<div className="disaster-card-content">
						{originData?.name ? (
							<DisasterOriginCard
								originData={originData}
								isMaster={isMaster}
								editInstance={() => {
									setType('chief');
									form.setFieldsValue(originData);
									setVisible(true);
								}}
							/>
						) : (
							<DisasterOriginCardNone
								isMaster={isMaster}
								toCreateBackup={() => {
									setVisible(true);
									setType('chief');
									form.resetFields();
								}}
							/>
						)}
						{originData?.name ? (
							backupData ? (
								<DisasterBackupCard
									backupData={backupData}
									isMaster={isMaster}
									editInstance={() => {
										form.setFieldsValue(backupData);
										setType('relation');
										setVisible(true);
									}}
								/>
							) : (
								<DisasterBackupCardNone
									toCreateBackup={() => {
										setVisible(true);
										setType('');
										form.resetFields();
									}}
								/>
							)
						) : null}
					</div>
				</div>
				{visible && (
					<Modal
						title={type ? '编辑' : '添加'}
						open={visible}
						onCancel={() => setVisible(false)}
						onOk={onOk}
						{...formItemLayout618}
					>
						<Form form={form}>
							<Form.Item
								label="平台名称"
								rules={[
									{
										required: true,
										message: '请输入平台名称'
									}
								]}
								name="name"
							>
								<Input placeholder="请输入平台名称" />
							</Form.Item>
							<Form.Item label="连接地址" required>
								<div className="flex-form">
									<Form.Item
										rules={[
											{
												required: true,
												message: '请选择协议'
											}
										]}
										name="protocol"
									>
										<Select placeholder="请选择">
											<Select.Option
												key="http"
												value="http"
											>
												http
											</Select.Option>
											<Select.Option
												key="https"
												value="https"
											>
												https
											</Select.Option>
										</Select>
									</Form.Item>
									<Form.Item
										name="host"
										rules={[
											{
												required: true,
												message: '请输入平台地址'
											}
										]}
									>
										<Input
											style={{ width: '220px' }}
											placeholder="请输入平台地址"
										/>
									</Form.Item>
									<Form.Item
										name="port"
										rules={[
											{
												required: true,
												message: '请输入端口'
											}
										]}
									>
										<InputNumber
											style={{ width: '100%' }}
											placeholder="端口"
										/>
									</Form.Item>
								</div>
							</Form.Item>
						</Form>
					</Modal>
				)}
				{/* {!isMaster && (
					<Modal
						title="切换确认"
						open={cardVisible}
						okText="立即切换"
						maskClosable={false}
						cancelText="暂时不用"
						onOk={() =>
							Modal.confirm({
								title: '操作',
								content:
									'该操作不可逆，只允许切换一次，是否继续',
								onOk: () => {
									switchDisaster({ isMaster }).then((res) => {
										if (res.success) {
											setSuccessFlag(true);
										} else {
											setErrorFlag(false);
										}
									});
								}
							})
						}
						onCancel={() => {
							storage.removeLocal(TOKEN);
							window.location.reload();
						}}
					>
						<div>
							<label>当前主管理平台状态：</label>
							{backupData?.phase === 'Running' ? (
								<span>
									<CheckCircleFilled
										style={{
											color: '#00A700',
											marginRight: 4
										}}
									/>
									运行正常
								</span>
							) : (
								<span>
									<CloseCircleFilled
										style={{
											color: '#C80000',
											marginRight: 4
										}}
									/>
									运行异常
								</span>
							)}
						</div>
						<div>
							灾备管理平台尚未开启，是否立即切换至灾备管理平台?
						</div>
					</Modal>
				)} */}
			</ProContent>
		</ProPage>
	);
}

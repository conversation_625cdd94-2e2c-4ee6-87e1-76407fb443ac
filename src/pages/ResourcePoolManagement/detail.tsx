import React, { useEffect, useState } from 'react';
import { useParams, useHistory } from 'react-router';
import { connect } from 'react-redux';
import { Tabs, notification, Button } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import { ProPage, ProHeader, ProContent } from '@/components/ProPage';
import Overview from './tabs/overview';
import Namespace from './tabs/namespace';
import Component from './tabs/component';
import Ingress from './tabs/ingress';
import ClusterMonitor from './tabs/clusterMonitor';
import MirrorWarehouse from './tabs/mirrorWarehouse';
import storage from '@/utils/storage';
import StorageManagement from '../StorageManagement';
import { getClusters } from '@/services/common';
import { clusterType } from '@/types';
import { StoreState } from '@/types';

import './index.less';

export interface paramsProps {
	id: string;
}
const { TabPane } = Tabs;
const ResourcePoolDetail = ({ buttonList }: { buttonList: any[] }) => {
	const history = useHistory();
	const [activeKey, setActiveKey] = useState<string>(
		storage.getSession('cluster-detail-current-tab') === ''
			? 'overview'
			: storage.getSession('cluster-detail-current-tab')
	);
	const params: paramsProps = useParams();
	const [clusterList, setClusterList] = useState<clusterType[]>([]);
	const tabList = [
		{
			key: 'overview',
			code: 'clusterOverview'
		},
		{
			key: 'namespace',
			code: 'clusterNameSpace'
		},
		{
			key: 'storage',
			code: 'clusterStorageManagement'
		},
		{
			key: 'ingress',
			code: 'clusterLoadBalance'
		},
		{
			key: 'monitor',
			code: 'clusterMonitoring'
		},
		{
			key: 'mirror',
			code: 'clusterImageWarehouse'
		},
		{
			key: 'component',
			code: 'clusterPlatformComponents'
		}
	];

	const showMenu = (code: string) =>
		buttonList?.find((item: any) => item.name === code);
	const subMenu: any = (code: string) =>
		buttonList?.find((item: any) => item.name === code)?.subMenu || [];
	const onChange = (key: string | number) => {
		setActiveKey(key as string);
		storage.setSession('cluster-detail-current-tab', key);
	};

	const refresh = () => {
		getData();
		setActiveKey(activeKey);
	};

	const getData = () => {
		getClusters({ detail: true }).then((res) => {
			if (res.success) {
				setClusterList(res.data);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};

	useEffect(() => {
		getData();
	}, []);

	useEffect(() => {
		const list =
			buttonList?.filter((item: any) => item.name !== 'clusterList') ||
			[];
		const key =
			tabList.find((item) => item.code === list[0]?.name)?.key ||
			'overview';
		!storage.getSession('cluster-detail-current-tab') && setActiveKey(key);
	}, [buttonList]);

	useEffect(() => {
		return () => storage.setSession('cluster-detail-current-tab', '');
	}, []);

	return (
		<ProPage>
			<ProHeader
				title={
					clusterList?.find((item) => item.id === params.id)?.nickname
				}
				onBack={() => history.push('/platform/clusterManagement')}
				extra={
					<Button
						key={'2'}
						onClick={() => refresh()}
						id="detailRefresh"
						icon={<ReloadOutlined id="detailRefresh" />}
					/>
				}
			/>
			<ProContent>
				<Tabs
					activeKey={activeKey}
					onChange={onChange}
					destroyInactiveTabPane
				>
					{showMenu('clusterOverview') && (
						<TabPane tab="概览" key="overview">
							<Overview />
						</TabPane>
					)}
					{showMenu('clusterNameSpace') && (
						<TabPane tab="命名空间" key="namespace">
							<Namespace subMenu={subMenu('clusterNameSpace')} />
						</TabPane>
					)}
					{showMenu('clusterStorageManagement') && (
						<TabPane tab="存储管理" key="storage">
							<StorageManagement />
						</TabPane>
					)}
					{showMenu('clusterLoadBalance') && (
						<TabPane tab="负载均衡" key="ingress">
							<Ingress subMenu={subMenu('clusterLoadBalance')} />
						</TabPane>
					)}
					{showMenu('clusterMonitoring') && (
						<TabPane tab="集群监控" key="monitor">
							<ClusterMonitor />
						</TabPane>
					)}
					{showMenu('clusterImageWarehouse') && (
						<TabPane tab="镜像仓库" key="mirror">
							<MirrorWarehouse />
						</TabPane>
					)}
					{showMenu('clusterPlatformComponents') && (
						<TabPane tab="平台组件" key="component">
							<Component
								subMenu={subMenu('clusterPlatformComponents')}
							/>
						</TabPane>
					)}
				</Tabs>
			</ProContent>
		</ProPage>
	);
};

export default connect((state: StoreState) => ({
	buttonList: state.auth.buttonList
}))(ResourcePoolDetail);

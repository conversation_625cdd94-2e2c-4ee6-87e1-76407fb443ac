.resource-pool-content {
	width: 361px;
	height: 120px;
	// border: 1px dashed #888888;
	border: 1px solid @primary-color;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin: 16px 0 32px 0;
	border-radius: @border-radius;
	cursor: pointer;
	margin-left: 2px;
	img {
		margin-left: 37px;
	}
	span {
		align-self: flex-end;
	}
	div {
		flex: 1;
		width: 80px;
		height: 24px;
		font-size: @font-3;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: @font-weight;
		color: #40454a;
		line-height: @line-height-1 * 2;
		margin-left: 40px;
	}
}
.second-form-content {
	background: #f1f1f2;
	padding: 16px 24px;
	margin-bottom: 16px;
}
.upload-parse-file {
	width: 100px;
	height: 32px;
	background-color: @primary-color;
	font-size: @font-1;
	font-weight: @font-weight;
	color: @white !important;
	line-height: @line-height-5;
	text-align: center;
	position: absolute;
	right: -115px;
	bottom: 30px;
	#my-upload-parse {
		width: 100px;
		height: 32px;
		opacity: 0;
		position: absolute;
		right: 0px;
		bottom: 0px;
		cursor: pointer;
	}
}
.upload-position {
	position: relative;
}
.cpu-content {
	display: flex;
	position: relative;
	.cpu-content-line {
		width: 48px;
		height: 16px;
		margin-right: 12px;
		background-color: #f4f6fb;
	}
	img {
		position: absolute;
		top: 0;
	}
}

// * 集群详情
.resource-pool-info {
	margin-top: 16px;
	margin-bottom: 0;
	border: none;
}
// .resource-pool-info-content {
// 	width: 100%;
// 	height: 350px;
// 	display: flex;
// 	.resource-pool-gauge-content{
// 		width: 342px;
// 		height: 100%;
// 		margin-right: 18px;
// 		margin-top: 16px;
// 		.resource-pool-gauge-item{
// 			width: 100%;
// 			height: 140px;
// 			display: flex;
// 			box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
// 			margin-bottom: 24px;
// 			margin-left: 2px;
// 			.resource-pool-gauge-info{
// 				width: 120px;
// 				// height: 54px;
// 				padding-top: 40px;
// 				font-size: @font-1;
// 				font-family: PingFangSC-Regular, PingFang SC;
// 				font-weight: @font-weight-sm;
// 				color: @text-color-title;
// 				line-height: @line-height-3;
// 			}
// 		}
// 	}
// 	.resource-pool-table-content{
// 		width: calc(100% - 326px - 16px - 20px);
// 		height: 100%;
// 		// padding-left: 16px;
// 		margin-top: 16px;
// 		.resource-pool-table-header{
// 			display: flex;
// 			justify-content: space-between;
// 		}
// 		.resource-pool-table-content{
// 			margin-top: 8px;
// 			width: 100%;
// 		}
// 	}
// }
.resource-pool-gauge-content {
	width: 100%;
	height: 100%;
	margin-top: 16px;
	display: flex;
	.resource-pool-gauge-item {
		width: 100%;
		height: 140px;
		display: flex;
		align-items: center;
		box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
		margin-left: 2px;
		&:nth-child(1) {
			margin-right: @margin;
		}
		.resource-pool-gauge-info {
			width: 360px;
			font-size: @font-1;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: @font-weight-sm;
			color: @text-color-title;
			line-height: @line-height-3;
		}
	}
}
.resource-pool-table-content {
	width: 100%;
	height: 100%;
	margin: @margin * 2 0;
	.resource-pool-table-header {
		display: flex;
		justify-content: space-between;
	}
	.resource-pool-table-content {
		margin-top: 8px;
		width: 100%;
	}
}
.batch-install-title-content,
.node-title-content {
	display: flex !important;
	align-items: center;
	.batch-install-name,
	.node-title-name {
		font-size: @font-2;
		font-weight: @font-weight;
		color: @text-color-title;
		line-height: 15px;
		border-left: 2px solid @primary-color;
		padding: 0 8px;
		margin: 8px 0;
	}
}
.delete-disabled {
	color: #cccccc;
}
.error-text {
	color: #c80000;
	font-size: @font-1;
}
.quick-model-content {
	width: 100%;
	min-height: 182px;
	background: #f5f5f5;
	padding: 16px;
	.quick-model-title {
		height: 22px;
		font-size: @font-1;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: @font-weight-sm;
		color: @text-color-title;
		line-height: 22px;
	}
	.quick-model-text {
		color: @white;
		width: calc(100% - 46px);
		margin-top: 16px;
		min-height: 113px;
		background: #272822;
		padding: 24px;
		word-wrap: break-word;
	}
	.quick-model-copy {
		margin-top: 16px;
		width: 46px;
		min-height: 113px;
		background: @primary-color;
	}
}
.component-plugging-content {
	display: flex;
	flex-wrap: wrap;
}
.batch-install-component-item {
	width: 667px;
	height: 72px;
	border-bottom: 1px solid #ebebeb;
	display: flex;
	align-items: center;
	margin-bottom: 1px;
	padding: 16px 30px;
	label {
		margin-bottom: 8px;
	}
	.batch-install-component-title {
		display: flex;
		width: 250px;
		.batch-install-component-title-icon {
			width: 40px;
			height: 40px;
			border-radius: @border-radius-lg;
			text-align: center;
			padding-top: 4.5px;
			&.operator {
				border: 1px solid #e7e9de;
			}
		}
		.batch-install-component-title-info {
			margin-left: 8px;
			display: flex;
			flex-direction: column;
			justify-content: center;
			.batch-install-component-title-name {
				height: 22px;
				font-size: @font-2;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: @font-weight;
				color: @text-color-title;
				line-height: 22px;
			}
			.batch-install-component-status-tag {
				width: 52px;
				height: 16px;
				border-radius: @border-radius-xxl;
				text-align: center;
				font-size: 8px;
				font-weight: @font-weight-sm;
				line-height: @line-height-2;
			}
		}
	}
}
.ingresses-content {
	display: flex;
	flex-wrap: wrap;
}
.display-form {
	display: flex;
	width: 100%;
	flex: 1 1;
	.label {
		width: 25%;
		height: 32px;
		line-height: 32px;
		margin-right: 11px;
		font-size: 12px;
		color: #555555;
		&::before {
			margin-right: 4px;
			content: '*';
			color: #ff4d4f;
			color: var(--form-error-color, #ff4d4f);
		}
		vertical-align: top;
	}
	.next-form-item {
		margin-left: 8px;
		&:first-of-type {
			margin-left: 0;
		}
	}
	&.user {
		.label {
			margin-right: 0;
		}
		.next-form-item {
			width: calc((75% - 8px) / 2);
		}
	}
}

.ne-require {
	label {
		padding-left: 10px;
	}
}
.zeus-model-select-content {
	display: flex;
	margin-bottom: @margin-sm;
	cursor: pointer;
	.zeus-model-select-item-active,
	.zeus-model-select-item {
		width: 300px;
		height: 130px;
		margin-right: @margin-sm;
		padding: @padding-sm;
		border: 1px solid @border-color;
		& > h2 {
			font-size: @font-2;
			font-weight: @font-weight-lg;
			margin-bottom: @margin-sm;
		}
		& > span {
			margin-top: -12px;
			float: right;
		}
	}
	.zeus-model-select-item-active {
		border: 1px solid @primary-color;
	}
}
.cluster-namespace-form-label,
.cluster-namespace-form-label-no-required {
	position: relative;
    display: inline-flex;
    align-items: center;
    max-width: 100%;
    height: 32px;
    color: #252525;
    font-size: 12px;
}
.cluster-namespace-form-label {
	&::before {
		display: inline-block;
		margin-right: 4px;
		color: #ff4d4f;
		font-size: 12px;
		line-height: 1;
		content: '*';
	}
}
.cluster-slider {
	margin-left: 6px;
	.cluster-slider-action {
		display: flex;
		justify-content: space-between;
	}
}
#storageSlider,
#memorySlider,
#cpuSlider {
	// width: 98%;
	position: relative;
	.slider-usage-tooltip {
		position: absolute;
		top: 4px;
		left: 6px;
		width: calc(10% - 4px);
		height: 6px;
		border-radius: 2px;
		background-color: @black-5;
		cursor: pointer;
		z-index: 1;
	}
	.ant-slider {
		height: 14px;
		.ant-slider-rail {
			height: 6px;
		}
		.ant-slider-track {
			height: 6px;
			background-color: #5C0EDF;
		}
		.ant-slider-step {
			height: 6px;
			.ant-slider-dot {
				&:nth-child(1) {
					visibility: hidden;
				}
				&:nth-last-child(1) {
					visibility: hidden;
				}
				top: -1px;
				z-index: 2;
			}
			.ant-slider-dot-active {
				border-color: #5C0EDF;
			}
		}
		.ant-slider-handle {
			margin-top: -4px;
			border: solid 2px #5C0EDF;
			z-index: 2;
		}
	}
}
#memorySlider {
	.ant-slider {
		.ant-slider-track {
			background-color: #1AC1C4;
		}
		.ant-slider-step {
			.ant-slider-dot-active {
				border-color: #1AC1C4;
			}
		}
		.ant-slider-handle {
			border: solid 2px #1AC1C4;
		}
	}
}
#storageSlider {
	width: 99%;
	.ant-slider {
		.ant-slider-track {
			background-color: #226EE7;
		}
		.ant-slider-step {
			.ant-slider-dot-active {
				border-color: #226EE7;
			}
		}
		.ant-slider-handle {
			border: solid 2px #226EE7;
		}
	}
}
.allot-project-title {
	font-size: @font-1;
	font-weight: 500;
	color: #333333;
	line-height: @line-height-1;
}
.allot-project-projects-content {
	display: flex;
	flex-wrap: wrap;
	margin-top: @margin;
	gap: 10px;
	.allot-project-item {
		width: 220px;
		height: 60px;
		background: @white;
		box-shadow: 0px 1px 4px 0px rgba(0,0,0,0.12);
		border-radius: @border-radius;
		display: flex;
		cursor: pointer;
		.allot-project-item-icon {
			background-color: #DFDFDF;
			opacity: 0.2;
			width: 60px;
			font-size: 36px;
			text-align: center;
			border-radius: @border-radius;
		}
		.allot-project-item-content {
			padding: @padding-sm;
			width: 160px;
			.allot-project-item-content-title {
				width: auto;
				font-size: @font-2;
				font-weight: @font-weight;
				color: #333333;
				line-height: @line-height-2;
				.mixin(textEllipsis);
			}
			.allot-project-item-content-description {
				font-size: @font-1;
				font-weight: 400;
				color: rgba(51,51,51,0.6);
				line-height: @line-height-1;
				.mixin(textEllipsis);
			}
		}
	}
}

.allot-project-item-active {
	border: 1px solid @primary-color;
}


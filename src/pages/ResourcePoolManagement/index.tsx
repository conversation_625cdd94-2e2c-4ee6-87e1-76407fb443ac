import React, { useState, useEffect } from 'react';
import { useHistory } from 'react-router';
import { Button, Modal, notification } from 'antd';
import moment from 'moment';
import { connect } from 'react-redux';
import { StoreState } from '@/types';
import Auth from '@/components/Auth';
import ProTable from '@/components/ProTable';
import Actions from '@/components/Actions';
import { ProPage, ProHeader, ProContent } from '@/components/ProPage';
import { deleteCluster, getClusters } from '@/services/common';
import { clusterType } from '@/types';
import storage from '@/utils/storage';
import MemoryItem from './memoryItem';
import './index.less';

const LinkButton = Actions.LinkButton;
const { confirm } = Modal;
function ResourcePoolManagement({
	buttonList
}: {
	buttonList: any[];
}): JSX.Element {
	const [clusterList, setClusterList] = useState<clusterType[]>([]);
	const [key, setKey] = useState<string>('');
	const [isAccess] = useState<boolean>(storage.getLocal('isAccessGYT'));
	const [loading, setLoading] = useState<boolean>(false);
	const [isRefresh, setIsRefresh] = useState<boolean>(false);
	// * 区域管理是否开启
	const [AreaManagementCloseAPI] = useState<boolean>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'areaManagementClose')
			?.enabled ?? true
	);
	const history = useHistory();
	useEffect(() => {
		let mounted = true;
		setLoading(true);
		getClusters({ detail: true, key })
			.then((res) => {
				if (res.success) {
					if (mounted) {
						setClusterList(res.data);
					}
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
		return () => {
			mounted = false;
		};
	}, []);
	const getData = (key: string) => {
		setLoading(true);
		getClusters({ detail: true, key })
			.then((res) => {
				if (res.success) {
					setClusterList(res.data);
					setIsRefresh(!isRefresh);
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const handleSearch = (value: string) => {
		setKey(value);
		getData(value);
	};
	const Operation = {
		primary: (
			<Auth code="clusterListAdd">
				<Button
					onClick={() =>
						history.push(
							'/platform/clusterManagement/addResourcePool'
						)
					}
					disabled={isAccess}
					title={
						isAccess ? '平台已接入观云台，请联系观云台管理员' : ''
					}
					type="primary"
				>
					添加集群
				</Button>
			</Auth>
		)
	};
	const actionRender = (
		value: string,
		record: clusterType,
		index: number
	) => {
		return (
			<Actions>
				<LinkButton
					code="clusterListUpdate"
					onClick={() => {
						history.push(
							`/platform/clusterManagement/editResourcePool/editOther/${record.id}`
						);
					}}
					disabled={isAccess}
					title={
						isAccess ? '平台已接入观云台，请联系观云台管理员' : ''
					}
				>
					编辑
				</LinkButton>
				<LinkButton
					code="clusterListDelete"
					disabled={isAccess}
					title={
						isAccess ? '平台已接入观云台，请联系观云台管理员' : ''
					}
					onClick={() => {
						if (record.removable) {
							confirm({
								title: '操作提醒',
								content:
									'该集群所有的数据都将被清空，无法找回，是否继续?',
								onOk() {
									return deleteCluster({
										clusterId: record.id
									}).then((res) => {
										if (res.success) {
											notification.success({
												message: '成功',
												description: '删除成功'
											});
											getData(key);
										} else {
											notification.error({
												message: '失败',
												description: res.errorMsg
											});
										}
									});
								}
							});
						} else {
							Modal.info({
								title: '提示',
								okText: '我知道了',
								content:
									'该集群正在被中间件服务使用，请删除后再试'
							});
						}
					}}
				>
					删除
				</LinkButton>
			</Actions>
		);
	};
	const cpuRender = (value: string, record: clusterType, index: number) => {
		return (
			<MemoryItem
				clusterId={record.id}
				type="cpu"
				isRefresh={isRefresh}
			/>
		);
	};
	const memoryRender = (
		value: string,
		record: clusterType,
		index: number
	) => {
		return (
			<MemoryItem
				clusterId={record.id}
				type="memory"
				isRefresh={isRefresh}
			/>
		);
	};
	const clusterNameRender = (value: string, record: any, index: number) => {
		const canLink = buttonList?.filter(
			(item: any) => item.name !== 'clusterList'
		)?.length;
		return (
			<span
				className={canLink ? 'name-link' : ''}
				onClick={() => {
					if (!canLink) return;
					history.push(
						`/platform/clusterManagement/resourcePoolDetail/${record.id}`
					);
				}}
			>
				{value}
			</span>
		);
	};
	const nameRender = (value: string, record: any, index: number) => {
		const canLink = buttonList?.filter(
			(item: any) => item.name === 'clusterNameSpace'
		)?.length;
		return (
			<span
				className={canLink ? 'name-link' : ''}
				onClick={() => {
					if (!canLink) return;
					history.push(
						`/platform/clusterManagement/resourcePoolDetail/${record.id}`
					);
					storage.setSession(
						'cluster-detail-current-tab',
						'namespace'
					);
				}}
			>
				{record.attributes.nsCount}
			</span>
		);
	};
	const createTimeRender = (
		value: string,
		record: clusterType,
		index: number
	) => {
		return record.attributes.createTime || '/';
	};
	return (
		<ProPage>
			<ProHeader
				title="集群管理"
				subTitle="发布中间件需要消耗CPU、内存等资源"
			/>
			<ProContent>
				<ProTable
					dataSource={clusterList}
					showRefresh
					onRefresh={() => getData(key)}
					search={{
						onSearch: handleSearch,
						placeholder: '请输入集群名称搜索'
					}}
					rowKey="name"
					operation={Operation}
					loading={loading}
				>
					<ProTable.Column
						title="集群名称"
						dataIndex="nickname"
						render={clusterNameRender}
					/>
					{!AreaManagementCloseAPI && (
						<ProTable.Column
							title="区域"
							dataIndex="areaName"
							render={(value, record: any) =>
								`${record.areaName || '/'}(${
									record.areaId || '/'
								})`
							}
							width={300}
							ellipsis={true}
						/>
					)}
					<ProTable.Column
						title="命名空间"
						dataIndex="nsCount"
						render={nameRender}
						width={100}
						sorter={(a, b) =>
							a.attributes.nsCount - b.attributes.nsCount
						}
					/>
					<ProTable.Column
						title="CPU(核)"
						dataIndex="cpu"
						render={cpuRender}
					/>
					<ProTable.Column
						title="内存(GB)"
						dataIndex="memory"
						render={memoryRender}
					/>
					<ProTable.Column
						title="创建时间"
						dataIndex="createTime"
						render={createTimeRender}
						width={180}
						sorter={(a, b) => {
							if (!b.attributes.createTime) return -1;
							return (
								moment(a.attributes.createTime).unix() -
								moment(b.attributes.createTime).unix()
							);
						}}
					/>
					<ProTable.Column
						title="操作"
						dataIndex="action"
						width={120}
						render={actionRender}
					/>
				</ProTable>
			</ProContent>
		</ProPage>
	);
}
export default connect((state: StoreState) => ({
	buttonList: state.auth.buttonList
}))(ResourcePoolManagement);

export interface NodeResourceProps {
	clusterId: string;
	cpuRate: number | null;
	cpuTotal: number;
	cpuUsed: number;
	createTime: string;
	ip: string;
	memoryRate: number;
	memoryTotal: number;
	memoryUsed: number;
	status: string;
	scheduable: boolean;
}
export interface MiddlewareResourceProps {
	aliasName?: string;
	clusterId: string;
	cpuRate: number;
	cpuRequest?: number;
	memoryRate: number;
	memoryRequest?: number;
	name: string;
	namespace?: string;
	per5MinCpu: number;
	per5MinMemory: number;
	per5MinStorage: number;
	requestCpu?: number;
	requestMemory?: number;
	requestStorage?: number;
	storageRate: number;
	type?: string;
	pvcRate: number;
	pvcRequest: number;
	per5MinPvc: number;
}

export interface StorageItem {
	name: string;
	storage: {
		allocatable: number;
		request: number;
		total: number;
		usable: number;
		usage: number;
		used: number;
	};
	storageClass: string[];
}
export interface NamespaceResourceProps {
	aliasName: string;
	clusterId: string;
	createTime: string;
	middlewareReplicas: number;
	name: string;
	organId: string;
	containerUIDRange: {
		min: number | null;
		max: number | null;
	};
	containerGIDRange: {
		min: number | null;
		max: number | null;
	};
	quotas: {
		cpu: {
			request: number;
			total: number;
			usable: number;
			usage: number;
			used: number;
		};
		memory: {
			request: number;
			total: number;
			usable: number;
			usage: number;
			used: number;
		};
		storageList: StorageItem[];
	};
	registered: boolean;
	phase: string;
	availableDomain: boolean;
	projectId: string;
	projectName: string;
}
export interface ClusterQuotaDTO {
	clusterNum: number;
	cpuUsedPercent: number | null;
	memoryUsedPercent: null;
	namespaceNum: number;
	totalCpu: number;
	totalMemory: number;
	usedCpu: number;
	usedMemory: number;
}
export interface ComponentProp {
	address: string | null;
	clusterId: string;
	clusterAliasName: string | null;
	component: string;
	status: number;
	type: null | string;
	protocol?: string | null;
	createTime: string | null;
	vgName: string | null;
	size: number | null;
	seconds: number;
	chartName?: string;
	silentTime?: string;
	host: string | null;
	logCollect: boolean | null;
	logSaveTime: string | null;
	password: string | null;
	port: string | null;
	seconds: number;
	username: string | null;
}

export interface IngressItemProps {
	address: string;
	clusterId: string;
	configMapName: string;
	defaultServerPort: null | number;
	healthzPort: null | number;
	httpPort: null | number;
	httpsPort: null | number;
	ingressClassName: string;
	namespace: string;
	status: number;
	id: number;
	createTime: string | null;
	seconds: number;
	type: string | null;
	startPort: string | null;
	endPort: string | null;
	traefikPortList: any[];
}

export interface MirrorParams {
	clusterId: string;
	keyword?: string;
	id?: number | number;
}
export interface AddMirrorWarehouseProps {
	visible: boolean;
	onCancel: () => void;
	clusterId: string;
	onRefresh: () => void;
	data: any;
}
export interface MonitorProps {
	clusterId?: string;
	namespace?: string;
	middlewareName?: string;
	type?: string;
	grafanaOpen?: boolean;
	customMid: boolean;
	capabilities: string[];
	chartVersion?: string;
}

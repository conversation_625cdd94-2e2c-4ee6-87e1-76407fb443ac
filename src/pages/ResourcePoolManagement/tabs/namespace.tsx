import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router';
import ProTable from '@/components/ProTable';
import { Button, notification, Switch, Tooltip, Modal } from 'antd';
import {
	getNamespaces,
	deleteNamespace,
	regNamespace,
	bindProject
} from '@/services/common';
import { NamespaceResourceProps } from '../resource.pool';
import { paramsProps } from '../detail';
import { nullRender, objectRemoveDuplicatesByKey } from '@/utils/utils';
import AddNamespace from './addNamespace';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import Actions from '@/components/Actions';
import MidProgress from '@/components/MidProgress';
import AllotProject from './allotProject';
import { ColumnFilterItem } from 'antd/lib/table/interface';
import storage from '@/utils/storage';
import useRefresh from '@/utils/useRefresh';
import Auth from '@/components/Auth';
const LinkButton = Actions.LinkButton;
const { confirm } = Modal;
const Namespace = ({ subMenu }: { subMenu: any[] }) => {
	const [dataSource, setDataSource] = useState<NamespaceResourceProps[]>([]);
	const [keyword, setKeyword] = useState<string>('');
	const [visible, setVisible] = useState<boolean>(false);
	const { id }: paramsProps = useParams();
	const [isAccess] = useState<boolean>(storage.getLocal('isAccessGYT'));
	const [allotVisible, setAllotVisible] = useState<boolean>(false);
	const [editNamespace, setEditNamespace] =
		useState<NamespaceResourceProps>();
	const [projectFilters, setProjectFilters] = useState<ColumnFilterItem[]>(
		[]
	);
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		let mounted = true;
		getNamespaces({
			clusterId: id,
			all: true,
			withQuota: true,
			withMiddleware: true,
			keyword: keyword
		}).then((res) => {
			if (res.success) {
				if (mounted) {
					const temp = res.data.sort(function (a: any, b: any) {
						const result =
							Number(a.registered) - Number(b.registered);
						return result > 0 ? -1 : 1;
					});
					const newTemp = temp.sort(function (a: any, b: any) {
						const result =
							Number(a.phase === 'Active') -
							Number(b.phase === 'Active');
						return result > 0 ? -1 : 1;
					});
					setDataSource([...newTemp]);
					setDataSource(res.data);
					const list = res.data?.map(
						(item: NamespaceResourceProps) => {
							return {
								value: item.projectName,
								text: item.projectName
							};
						}
					);
					setProjectFilters(
						objectRemoveDuplicatesByKey(list, 'value')
					);
				}
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
		return () => {
			mounted = false;
		};
	}, [keyword, refreshKey]);
	const getData = () => {
		getNamespaces({
			clusterId: id,
			all: true,
			withQuota: true,
			withMiddleware: true,
			keyword: keyword
		}).then((res) => {
			if (res.success) {
				const temp = res.data.sort(function (a: any, b: any) {
					const result = Number(a.registered) - Number(b.registered);
					return result > 0 ? -1 : 1;
				});
				const newTemp = temp.sort(function (a: any, b: any) {
					const result =
						Number(a.phase === 'Active') -
						Number(b.phase === 'Active');
					return result > 0 ? -1 : 1;
				});
				setDataSource([...newTemp]);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const Operation = {
		primary: (
			<Auth code="clusterNameSpaceAdd">
				<Button
					disabled={isAccess}
					title={
						isAccess ? '平台已接入观云台，请联系观云台管理员' : ''
					}
					type="primary"
					onClick={() => {
						setEditNamespace(undefined);
						setVisible(true);
					}}
				>
					新增
				</Button>
			</Auth>
		)
	};
	const handleSearch = (value: string) => {
		setKeyword(value);
	};
	const handleChange = (value: boolean, record: NamespaceResourceProps) => {
		regNamespace({
			clusterId: id,
			name: record.name,
			registered: value
		}).then((res) => {
			if (res.success) {
				const msg = value ? '命名空间注册成功' : '命名空间关闭成功';
				notification.success({
					message: '成功',
					description: msg
				});
				const list = dataSource.map((item) => {
					if (item.name === record.name) {
						item.registered = value;
					}
					return item;
				});
				setDataSource(list);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const registeredRender = (
		value: any,
		record: NamespaceResourceProps,
		index: number
	) => {
		return record.registered && record.middlewareReplicas ? (
			<Tooltip title="本命名空间已发布中间件服务，使用中，不可关闭">
				<Switch
					title={
						isAccess ? '平台已接入观云台，请联系观云台管理员' : ''
					}
					disabled={isAccess || record.name === 'middleware-operator'}
					checked={value}
				/>
			</Tooltip>
		) : (
			<Switch
				checked={value}
				title={isAccess ? '平台已接入观云台，请联系观云台管理员' : ''}
				disabled={
					record.phase !== 'Active' ||
					isAccess ||
					record.name === 'middleware-operator'
				}
				onChange={(value: boolean) => handleChange(value, record)}
			/>
		);
	};
	const actionRender = (
		value: any,
		record: NamespaceResourceProps,
		index: number
	) => {
		// * 已发布服务数不为0时，无法禁用，禁用后，才能删除
		// * isAccess 接入观云台后，无法进行操作
		if (isAccess) {
			return (
				<Actions>
					{record.registered && (
						<LinkButton
							code="clusterNameSpaceUpdate"
							disabled={true}
							title="平台已接入观云台，请联系观云台管理员"
						>
							编辑
						</LinkButton>
					)}
					{record.registered && (
						<LinkButton
							code="clusterNameSpaceDistribution"
							disabled={true}
							title="平台已接入观云台，请联系观云台管理员"
						>
							{record.projectId ? '解绑' : '分配'}
						</LinkButton>
					)}
					{!record.registered && (
						<LinkButton
							code="clusterNameSpaceDelete"
							disabled={true}
							title="平台已接入观云台，请联系观云台管理员"
						>
							删除
						</LinkButton>
					)}
				</Actions>
			);
		}
		if (record.phase !== 'Active') {
			return (
				<Actions>
					<LinkButton
						code="clusterNameSpaceDelete"
						disabled={true}
						title="该分区正在删除中，无法操作"
					>
						删除
					</LinkButton>
				</Actions>
			);
		}
		if (!record.registered) {
			return (
				<Actions>
					<LinkButton
						code="clusterNameSpaceDelete"
						disabled={record.name === 'middleware-operator'}
						onClick={() => {
							confirm({
								title: '确认删除',
								content: '确认要删除该命名空间？',
								icon: <ExclamationCircleOutlined />,
								okText: '确定',
								cancelText: '取消',
								onOk() {
									return deleteNamespace({
										clusterId: id,
										name: record.name
									}).then((res) => {
										if (res.success) {
											notification.success({
												message: '成功',
												description: '命名空间删除成功'
											});
											getData();
										} else {
											notification.error({
												message: '失败',
												description: res.errorMsg
											});
										}
									});
								}
							});
						}}
					>
						删除
					</LinkButton>
				</Actions>
			);
		} else {
			return (
				<Actions>
					<LinkButton
						code="clusterNameSpaceUpdate"
						disabled={record.name === 'middleware-operator'}
						onClick={() => {
							setEditNamespace(record);
							setVisible(true);
						}}
					>
						编辑
					</LinkButton>
					<LinkButton
						code="clusterNameSpaceDistribution"
						disabled={record.name === 'middleware-operator'}
						onClick={() => {
							if (record.projectId) {
								confirm({
									title: '操作确认',
									content:
										'解除绑定后将无法恢复，请谨慎操作！',
									onOk: () => {
										const sendData = {
											clusterId: record.clusterId,
											name: record.name,
											projectId: '',
											aliasName: record.aliasName,
											organId: ''
										};
										return bindProject(sendData).then(
											(res) => {
												if (res.success) {
													notification.success({
														message: '成功',
														description:
															'命名空间解绑成功'
													});
													getData();
												} else {
													notification.error({
														message: '失败',
														description:
															res.errorMsg
													});
												}
											}
										);
									}
								});
							} else {
								setEditNamespace(record);
								setAllotVisible(true);
							}
						}}
					>
						{record.projectId ? '解绑' : '分配'}
					</LinkButton>
				</Actions>
			);
		}
	};
	const memoryRender = (
		value: any,
		record: NamespaceResourceProps,
		index: number
	) => {
		return (
			<MidProgress
				fromColor="#1AC1C4"
				toColor="#74DDDF"
				unit="GB"
				used={record.quotas?.memory.used}
				total={record.quotas?.memory.request}
			/>
		);
	};
	const cpuRender = (
		value: any,
		record: NamespaceResourceProps,
		index: number
	) => {
		return (
			<MidProgress
				fromColor="#5C0EDF"
				toColor="#853CFF"
				unit="Core"
				used={record.quotas?.cpu.used}
				total={record.quotas?.cpu.request}
			/>
		);
	};
	const nameRender = (
		value: any,
		record: NamespaceResourceProps,
		index: number
	) => {
		return (
			<span
				className={
					record.phase !== 'Active'
						? 'delete-disabled table-col-w146-h2'
						: 'table-col-w146-h2'
				}
				title={record.aliasName || record.name}
				style={{ width: 140 }}
			>
				{record.aliasName || record.name}
			</span>
		);
	};

	return (
		<div>
			<ProTable
				dataSource={dataSource}
				rowKey="name"
				operation={Operation}
				// showColumnSetting
				// showRefresh
				onRefresh={getData}
				search={{
					onSearch: handleSearch,
					placeholder: '请输入命名空间名称搜索'
				}}
			>
				<ProTable.Column
					title="命名空间名称"
					dataIndex="aliasName"
					render={nameRender}
				/>
				<ProTable.Column
					title="命名空间英文名"
					dataIndex="name"
					ellipsis={true}
				/>
				<ProTable.Column
					title="CPU配额（核）"
					dataIndex="cpu"
					render={cpuRender}
					sorter={(a, b) => {
						let aPer = 0;
						let bPer = 0;
						if (a.quotas) {
							aPer =
								(a.quotas?.cpu?.used || 0) /
								(a.quotas?.cpu?.request || 0);
						}
						if (b.quotas) {
							bPer =
								(b.quotas?.cpu?.used || 0) /
								(b.quotas?.cpu?.request || 0);
						}
						return aPer - bPer;
					}}
				/>
				<ProTable.Column
					title="内存配额（GB）"
					dataIndex="memory"
					render={memoryRender}
					sorter={(a, b) => {
						let aPer = 0;
						let bPer = 0;
						if (a.quotas) {
							aPer =
								(a.quotas?.memory?.used || 0) /
								(a.quotas?.memory?.request || 0);
						}
						if (b.quotas) {
							bPer =
								(b.quotas?.memory?.used || 0) /
								(b.quotas?.memory?.request || 0);
						}
						return aPer - bPer;
					}}
				/>
				<ProTable.Column
					title="已发布服务"
					dataIndex="middlewareReplicas"
					render={nullRender}
					width={100}
					sorter={(
						a: NamespaceResourceProps,
						b: NamespaceResourceProps
					) =>
						Number(a.middlewareReplicas) -
						Number(b.middlewareReplicas)
					}
				/>
				<ProTable.Column
					title="绑定项目"
					dataIndex="projectName"
					filters={projectFilters}
					filterMultiple={false}
					onFilter={(value, record: NamespaceResourceProps) =>
						record.projectName === value
					}
					render={nullRender}
					width={100}
				/>
				{subMenu?.find(
					(item: any) => item.name === 'clusterNameSpaceEnable'
				) && (
					<ProTable.Column
						title="启用"
						dataIndex="registered"
						width={80}
						render={registeredRender}
					/>
				)}
				<ProTable.Column
					title="操作"
					dataIndex="action"
					width={100}
					render={actionRender}
				/>
			</ProTable>
			{visible && (
				<AddNamespace
					visible={visible}
					onCancel={() => setVisible(false)}
					clusterId={id}
					onRefresh={() => {
						getData();
					}}
					editData={editNamespace}
				/>
			)}
			{allotVisible && editNamespace && (
				<AllotProject
					open={allotVisible}
					onCancel={() => setAllotVisible(false)}
					onRefresh={getData}
					clusterId={id}
					projectId={editNamespace.projectId}
					organId={editNamespace.organId}
					name={editNamespace.name}
					aliasName={editNamespace.aliasName}
				/>
			)}
		</div>
	);
};
export default Namespace;

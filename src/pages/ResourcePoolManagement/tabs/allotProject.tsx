import React, { useEffect, useState } from 'react';
import { Modal, notification, Steps, Input, Button } from 'antd';
import { IconFont } from '@/components/IconFont';
import { getProjects } from '@/services/project';
import { ProjectItem } from '@/types';
import { bindProject, checkProject } from '@/services/common';
import { getOrganizations } from '@/services/organization';
import { OrganizationItem } from '@/pages/OrganizationManagement/organization';
import { TeamOutlined } from '@ant-design/icons';
import '../index.less';
const { Step } = Steps;
interface AllotProjectProps {
	open: boolean;
	onCancel: () => void;
	clusterId: string;
	name: string;
	aliasName: string;
	onRefresh: () => void;
	projectId: string | null;
	organId: string;
}
function AllotProject(props: AllotProjectProps): JSX.Element {
	const {
		open,
		onCancel,
		clusterId,
		name,
		aliasName,
		onRefresh,
		organId,
		projectId
	} = props;
	const [organizations, setOrganizations] = useState<OrganizationItem[]>([]);
	const [currentOrganization, setCurrentOrganization] =
		useState<OrganizationItem>();
	const [projects, setProjects] = useState<ProjectItem[]>([]);
	const [currentProject, setCurrentProject] = useState<ProjectItem>();
	const [current, setCurrent] = useState<number>(0);
	const [keyword, setKeyword] = useState<string>('');
	const [key, setKey] = useState<string>('');
	useEffect(() => {
		getOrganizationList('');
	}, []);
	useEffect(() => {
		if (currentOrganization) {
			getProjectList('');
		}
	}, [currentOrganization]);
	const getOrganizationList = (value: string) => {
		getOrganizations({ keyword: value }).then((res) => {
			if (res.success) {
				setOrganizations(res.data);
				if (res.data.find((item) => item.organId === organId)) {
					setCurrentOrganization(
						res.data.find((item) => item.organId === organId)
					);
				}
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	const getProjectList = (value: string) => {
		getProjects({
			key: value,
			organId: currentOrganization?.organId || ''
		}).then((res) => {
			if (res.success) {
				setProjects(res.data);
				if (res.data.find((item) => item.projectId === projectId)) {
					setCurrentProject(
						res.data.find((item) => item.projectId === projectId)
					);
				}
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	const onOk = async () => {
		if (current === 0) {
			setCurrent(1);
			return;
		}
		const result = await checkProject({
			clusterId,
			namespace: name,
			projectId: currentProject?.projectId || '',
			aliasName: aliasName,
			organId: currentOrganization?.organId
		});
		if (result.data) {
			const sendData = {
				clusterId,
				name,
				projectId: currentProject?.projectId || '',
				aliasName: aliasName,
				organId: currentOrganization?.organId
			};
			bindProject(sendData).then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: currentProject
							? '命名空间分配成功'
							: '命名空间解绑成功'
					});
					onRefresh();
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
			onCancel();
		} else {
			Modal.confirm({
				title: '接入失败',
				content:
					'当前选择项目配额不足，请保证项目有足够的资源接入此命名空间。'
			});
		}
	};
	const okButtonPropsReturn = () => {
		if (current === 0 && organizations.length === 0) {
			return { disabled: true, title: '当前没有可选组织' };
		}
		if (current === 1 && projects.length === 0) {
			return { disabled: true, title: '当前没有可选项目' };
		}
		if (current === 0 && !currentOrganization) {
			return { disabled: true, title: '请选择组织' };
		}
		if (current === 1 && !currentProject) {
			return { disabled: true, title: '请选择项目' };
		}
		return {};
	};
	const footerRender = () => {
		if (current === 0) {
			return [
				<Button key="cancel" onClick={onCancel}>
					取消
				</Button>,
				<Button
					key="next"
					type="primary"
					onClick={() => setCurrent(1)}
					disabled={okButtonPropsReturn().disabled}
					title={okButtonPropsReturn().title}
				>
					下一步
				</Button>
			];
		} else {
			return [
				<Button key="pre" onClick={() => setCurrent(0)}>
					上一步
				</Button>,
				<Button
					key="submit"
					type="primary"
					onClick={onOk}
					disabled={okButtonPropsReturn().disabled}
					title={okButtonPropsReturn().title}
				>
					确定
				</Button>
			];
		}
	};
	return (
		<Modal
			open={open}
			title="分配"
			width={500}
			onCancel={onCancel}
			footer={footerRender()}
		>
			<Steps current={current}>
				<Step title="选择组织" />
				<Step title="选择项目" />
			</Steps>
			{current === 0 && (
				<div className="allot-project-projects-content">
					<Input.Search
						placeholder="请输入组织名称搜索"
						style={{
							marginBottom: '12px',
							width: 'calc(100% - 16px)'
						}}
						allowClear
						value={keyword}
						onChange={(e) => setKeyword(e.target.value)}
						onSearch={(value) => getOrganizationList(value)}
					/>
					{organizations?.map((item: OrganizationItem) => {
						return (
							<div
								key={item.name}
								className={`allot-project-item ${
									currentOrganization?.organId ===
									item.organId
										? 'allot-project-item-active'
										: ''
								}`}
								onClick={() => {
									if (
										currentOrganization?.organId ===
										item.organId
									) {
										setCurrentOrganization(undefined);
									} else {
										setCurrentOrganization(item);
									}
								}}
							>
								<div className="allot-project-item-icon">
									<TeamOutlined />
								</div>
								<div className="allot-project-item-content">
									<div className="allot-project-item-content-title">
										{item.name}
									</div>
									<div className="allot-project-item-content-description">
										{item.description}
									</div>
								</div>
							</div>
						);
					})}
				</div>
			)}
			{current === 1 && (
				<div className="allot-project-projects-content">
					<Input.Search
						placeholder="请输入项目名称搜索"
						style={{
							marginBottom: '12px',
							width: 'calc(100% - 16px)'
						}}
						value={key}
						allowClear
						onChange={(e) => setKey(e.target.value)}
						onSearch={(value) => getProjectList(value)}
					/>
					{projects?.map((item: ProjectItem) => {
						return (
							<div
								key={item.name}
								className={`allot-project-item ${
									currentProject?.name === item.name
										? 'allot-project-item-active'
										: ''
								}`}
								onClick={() => {
									if (currentProject?.name === item.name) {
										setCurrentProject(undefined);
									} else {
										setCurrentProject(item);
									}
								}}
							>
								<div className="allot-project-item-icon">
									<IconFont type="icon-wodexiangmu" />
								</div>
								<div className="allot-project-item-content">
									<div className="allot-project-item-content-title">
										{item.aliasName}
									</div>
									<div className="allot-project-item-content-description">
										{item.description}
									</div>
								</div>
							</div>
						);
					})}
				</div>
			)}
		</Modal>
	);
}
export default AllotProject;

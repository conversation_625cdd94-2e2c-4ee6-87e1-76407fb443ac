import React, { useEffect, useState } from 'react';
import { Modal, Form, Select, Input, notification, InputNumber } from 'antd';
import { addMirror, updateMirror } from '@/services/common';
import { PRIVATE_KEY, PUBLIC_KEY, address } from '@/utils/const';
import { AddMirrorWarehouseProps } from '../resource.pool';
import { decrypt, encrypt } from '@/utils/utils';

const FormItem = Form.Item;
const { Option } = Select;
const formItemLayout = {
	labelCol: {
		span: 6
	},
	wrapperCol: {
		span: 18
	}
};
const formItemLayout2 = {
	labelCol: {
		span: 0
	},
	wrapperCol: {
		span: 24
	}
};

const AddMirrorWarehouse = (props: AddMirrorWarehouseProps) => {
	const { visible, onCancel, clusterId, onRefresh, data } = props;
	const [form] = Form.useForm();
	const [protocol, setProtocol] = useState<string>();

	const onOk = async () => {
		await form.validateFields();
		const values = form.getFieldsValue();
		if (!data) {
			addMirror({
				clusterId,
				...values,
				port: values.port
					? Number(values.port)
					: values.protocol === 'http'
					? 80
					: 443,
				protocol: protocol || values.protocol,
				password: encrypt(values.password, PUBLIC_KEY)
			}).then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '镜像仓库创建成功'
					});
					onCancel();
					onRefresh();
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		} else {
			updateMirror({
				clusterId,
				...values,
				id: data.id,
				port: values.port
					? Number(values.port)
					: values.protocol === 'http'
					? 80
					: 443,
				password: encrypt(values.password, PUBLIC_KEY)
			}).then((res) => {
				if (res.success) {
					notification.success({
						message: '成功',
						description: '镜像仓库修改成功'
					});
					onCancel();
					onRefresh();
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		}
	};
	useEffect(() => {
		if (data) {
			setProtocol(data.protocol);
			form.setFieldsValue({
				...data,
				port: data.port ? Number(data.port) : undefined,
				password: decrypt(data.password, PRIVATE_KEY)
			});
		}
	}, [data]);

	return (
		<Modal
			title={data ? '编辑镜像仓库' : '新增镜像仓库'}
			open={visible}
			onCancel={onCancel}
			onOk={onOk}
			okText="确定"
			cancelText="取消"
			width={800}
		>
			<Form labelAlign="left" form={form} {...formItemLayout}>
				<div className="display-form">
					<label className="label">镜像仓库地址</label>
					<FormItem
						{...formItemLayout2}
						name="protocol"
						required
						rules={[{ required: true, message: '请选择' }]}
						style={{ width: '23%', marginRight: 8 }}
					>
						<Select
							placeholder="请选择镜像地址"
							style={{ width: '100%' }}
							value={protocol}
							onChange={(value) => {
								setProtocol(value);
							}}
							dropdownMatchSelectWidth={false}
						>
							{address.map((item: any) => {
								return (
									<Option key={item.key} value={item.value}>
										{item.value}
									</Option>
								);
							})}
						</Select>
					</FormItem>
					<FormItem
						{...formItemLayout2}
						required
						name="hostAddress"
						style={{ marginRight: 8, width: '35%' }}
						rules={[
							{
								required: true,
								message: '请输入主机地址'
							},
							{ max: 200, message: '主机地址不能超过200位' }
						]}
					>
						<Input placeholder="请输入harbor主机地址" />
					</FormItem>
					<FormItem
						{...formItemLayout2}
						style={{ width: '20%' }}
						name="port"
					>
						<InputNumber
							style={{ width: '100%' }}
							placeholder={`端口，默认${
								protocol === 'https' ? '443' : '80'
							}`}
						/>
					</FormItem>
				</div>
				<FormItem
					label="镜像仓库项目"
					required
					rules={[
						{ required: true, message: '请输入镜像仓库项目' },
						{ max: 50, message: '镜像仓库项目不能超过50位' }
					]}
					name="project"
				>
					<Input placeholder="请输入镜像仓库项目" />
				</FormItem>
				<div className="display-form user">
					<label className="label">镜像仓库鉴权</label>
					<FormItem
						{...formItemLayout2}
						required
						rules={[{ required: true, message: '请输入用户名' }]}
						style={{ width: '36%', marginRight: 8 }}
						name="username"
					>
						<Input placeholder="请输入用户名" />
					</FormItem>
					<FormItem
						{...formItemLayout2}
						required
						name="password"
						rules={[{ required: true, message: '请输入密码' }]}
						style={{ width: '38%' }}
					>
						<Input.Password placeholder="请输入密码" />
					</FormItem>
				</div>
				<FormItem label="描述" name="description">
					<Input.TextArea
						maxLength={300}
						placeholder="请输入描述信息，限定300字符串"
					/>
				</FormItem>
			</Form>
		</Modal>
	);
};
export default AddMirrorWarehouse;

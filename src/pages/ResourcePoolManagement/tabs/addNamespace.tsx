import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, notification, InputNumber } from 'antd';
import { createNamespace } from '@/services/common';
import { formItemLayout618 } from '@/utils/const';
import { regNamespace } from '@/services/common';
import { NamespaceResourceProps } from '../resource.pool';
import '../index.less';

interface AddNamespaceProps {
	visible: boolean;
	onCancel: () => void;
	clusterId: string;
	onRefresh: () => void;
	editData: NamespaceResourceProps | undefined;
}
const FormItem = Form.Item;

const AddNamespace: (props: AddNamespaceProps) => JSX.Element = (
	props: AddNamespaceProps
) => {
	const { visible, onCancel, clusterId, onRefresh, editData } = props;
	const [form] = Form.useForm();
	const [min, setMin] = useState<number | null>(null);
	const [max, setMax] = useState<number | null>(null);
	const [gidMin, setGidMin] = useState<number | null>(null);
	const [gidMax, setGidMax] = useState<number | null>(null);
	const [loading, setLoading] = useState<boolean>(false);
	const [uidRequired, setUidRequired] = useState<boolean>(false);
	useEffect(() => {
		if (editData) {
			form.setFieldsValue({
				name: editData.name,
				aliasName: editData.aliasName,
				uidRange: editData.containerUIDRange?.min ?? null
			});
			setMin(editData.containerUIDRange?.min ?? null);
			setMax(editData.containerUIDRange?.max ?? null);
			setGidMin(editData.containerGIDRange?.min ?? null);
			setGidMax(editData.containerGIDRange?.max ?? null);
		}
	}, [editData]);
	useEffect(() => {
		if (typeof gidMax === 'number' || typeof gidMin === 'number') {
			setUidRequired(true);
		} else {
			setUidRequired(false);
		}
	}, [gidMax, gidMin]);
	useEffect(() => {
		form.validateFields(['uidRange']);
	}, [uidRequired, form]);
	const onOk = () => {
		form.validateFields().then((values) => {
			if (
				Number(min) < 0 ||
				Number(max) < 0 ||
				Number(gidMin) < 0 ||
				Number(gidMax) < 0
			) {
				notification.error({
					message: '错误',
					description: '最大值与最小值不能为负数'
				});
				return;
			}
			if (min || max) {
				if (Number(min) > Number(max)) {
					notification.error({
						message: '错误',
						description: 'uid最小值不能高于最大值'
					});
					return;
				}
			}
			if (gidMax || gidMin) {
				if (Number(gidMin) > Number(gidMax)) {
					notification.error({
						message: '错误',
						description: 'gid最小值不能高于最大值'
					});
					return;
				}
			}
			const uidr = { min, max };
			if (typeof min === 'number' || typeof max === 'number') {
				uidr.min = Number(min);
				uidr.max = Number(max);
			}
			const gidr = { min: gidMin, max: gidMax };
			if (typeof gidMin === 'number' || typeof gidMax === 'number') {
				gidr.min = Number(gidMin);
				gidr.max = Number(gidMax);
			}
			const sendData = {
				clusterId,
				...values,
				containerUIDRange: {
					min: uidr.min,
					max: uidr.max
				},
				containerGIDRange: {
					min: gidr.min,
					max: gidr.max
				}
			};
			setLoading(true);
			if (editData) {
				regNamespace(sendData)
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '命名空间修改成功'
							});
							onCancel();
							onRefresh();
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					})
					.finally(() => {
						setLoading(false);
					});
			} else {
				createNamespace(sendData)
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '命名空间创建成功'
							});
							onCancel();
							onRefresh();
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					})
					.finally(() => {
						setLoading(false);
					});
			}
		});
	};
	return (
		<Modal
			title={editData ? '编辑命名空间' : '新增命名空间'}
			open={visible}
			onCancel={onCancel}
			onOk={onOk}
			okButtonProps={{ loading: loading }}
			width={550}
			okText="确定"
			cancelText="取消"
		>
			<Form labelAlign="left" form={form} {...formItemLayout618}>
				<FormItem
					label="命名空间名称"
					required
					name="aliasName"
					rules={[
						{ required: true, message: '请输入命名空间名称' },
						{
							max: 64,
							message: '请输入1-64个字符'
						}
					]}
				>
					<Input placeholder="请输入命名空间名称" />
				</FormItem>
				<FormItem
					label="命名空间英文名"
					required
					name="name"
					rules={[
						{ required: true, message: '请输入命名空间英文名' },
						{
							pattern: new RegExp(
								'^[a-z][a-z0-9-]{0,38}[a-z0-9]$'
							),
							message:
								'命名空间是由小写字母数字及“-”组成，且以小写字母开头和结尾，不能以“-”结尾的2-40个字符'
						}
					]}
				>
					<Input
						disabled={editData ? true : false}
						placeholder="请输入命名空间英文名"
					/>
				</FormItem>
				<FormItem
					label="容器uid范围"
					name="uidRange"
					rules={[
						{
							required: uidRequired,
							message: '请输入uid范围'
						}
					]}
				>
					<div className="site-input-group-wrapper">
						<Input.Group compact>
							<InputNumber
								style={{ width: 174 }}
								placeholder="请输入uid最小值"
								value={min}
								max={2147483647}
								step={1}
								precision={0}
								onChange={(value) => setMin(value)}
							/>
							<Input
								className="site-input-split"
								style={{
									width: 30,
									borderLeft: 0,
									borderRight: 0,
									pointerEvents: 'none'
								}}
								placeholder="~"
								disabled
							/>
							<InputNumber
								className="site-input-right"
								style={{
									width: 174
								}}
								max={2147483647}
								value={max}
								step={1}
								precision={0}
								onChange={(value) => setMax(value)}
								placeholder="请输入uid最大值"
							/>
						</Input.Group>
					</div>
				</FormItem>
				<FormItem label="容器gid范围">
					<div className="site-input-group-wrapper">
						<Input.Group compact>
							<InputNumber
								style={{ width: 174 }}
								placeholder="请输入gid最小值"
								value={gidMin}
								step={1}
								precision={0}
								onChange={(value) => setGidMin(value)}
								max={2147483647}
							/>
							<Input
								className="site-input-split"
								style={{
									width: 30,
									borderLeft: 0,
									borderRight: 0,
									pointerEvents: 'none'
								}}
								placeholder="~"
								disabled
							/>
							<InputNumber
								className="site-input-right"
								style={{
									width: 174
								}}
								max={2147483647}
								step={1}
								precision={0}
								value={gidMax}
								onChange={(value) => setGidMax(value)}
								placeholder="请输入gid最大值"
							/>
						</Input.Group>
					</div>
				</FormItem>
			</Form>
		</Modal>
	);
};
export default AddNamespace;

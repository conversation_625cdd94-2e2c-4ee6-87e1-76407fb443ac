import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router';
import { Select, notification } from 'antd';
import { getMonitor } from '@/services/common';
import DefaultPicture from '@/components/DefaultPicture';

import svg from '@/assets/images/grafana_icon.svg';
import { paramsProps } from '../detail';
import { filterProps } from '@/utils/constant';
import '@/pages/ServiceListDetail/Monitor/monitor.less';
import useRefresh from '@/utils/useRefresh';

const ClusterMonitor = () => {
	const { id }: paramsProps = useParams();
	const [url, setUrl] = useState('');
	const [monitorList, setMonitorList] = useState<filterProps[]>();
	const [menuHide, setMenuHide] = useState(false);
	const [notAuth, setNotAuth] = useState<boolean>(false);
	const [refreshKey, setRefreshKey] = useState<number>(0);
	useRefresh(() => setRefreshKey((refreshKey) => refreshKey + 1));
	useEffect(() => {
		getMonitor({
			clusterId: id
		}).then((res) => {
			if (res.success) {
				if (!res.data) return;
				const arr = [];
				for (const key in res.data) {
					arr.push({
						label: key,
						value: res.data[key].url as string
					});
				}
				setMonitorList(arr);
				setUrl(arr[0].value);
				setNotAuth(false);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
				if (`${res.code}${res.errorMsg}` === '400007无权限') {
					setNotAuth(true);
				}
				setUrl('');
			}
		});
	}, [refreshKey]);

	useEffect(() => {
		if (url) {
			const iframe: any = document.getElementById('iframe');
			iframe.onload = function () {
				iframe.contentWindow.postMessage(
					{ showMenu: false },
					encodeURI(new URL(url).origin)
				);
			};
		}
	}, [url]);

	useEffect(() => {
		// 子页面去掉侧边栏之后再显示iframe
		window.addEventListener(
			'message',
			function (event) {
				if (event.data.menuHide) {
					setMenuHide(true);
				}
			},
			false
		);
	});
	if (notAuth) {
		return (
			<DefaultPicture title="当前用户无该操作权限，请联系项目管理员分配权限" />
		);
	}

	return (
		<div className="cluster-monitor">
			<div className="mb-16">
				<label className="mr-24">看板选择:</label>
				<Select
					options={monitorList}
					style={{ width: '260px' }}
					value={url}
					onChange={(value) => setUrl(value)}
				/>
			</div>
			<div className="monitor">
				<div
					style={{
						height: 'calc(100vh - 83px)',
						visibility: menuHide ? 'visible' : 'hidden'
					}}
				>
					{url && (
						<iframe
							id="iframe"
							src={url}
							frameBorder="no"
							// border="0"
							scrolling="no"
							style={{
								width: '100%',
								height: '100%',
								background: '#FFF'
							}}
						></iframe>
					)}
				</div>
				<div
					className="loading-gif"
					style={{
						visibility: menuHide ? 'hidden' : 'visible'
					}}
				>
					<div className="loading-icon">
						<img src={svg} width="60" />
					</div>
				</div>
			</div>
		</div>
	);
};

export default ClusterMonitor;

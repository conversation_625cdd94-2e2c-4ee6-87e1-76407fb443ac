import { rocketMQAccount } from '@/components/RocketACLForm/acl';
import { TolerationLabelItem } from '@/components/FormTolerations/formTolerations';
export interface AffinityItem {
	label: string;
	namespace?: string;
	required?: boolean;
}
export interface AffinityProps {
	flag: boolean;
	label: string;
	checked: boolean;
}
export interface AffinityLabelsItem {
	label: string;
	id: number;
	checked?: boolean;
	anti?: boolean;
	required?: boolean;
}
export interface TolerationsProps {
	flag: boolean;
	label: string;
}
export interface TolerationsLabelsItem {
	label: string;
	id: number;
}
export interface CommonSendDataParams {
	chartName?: string;
	chartVersion?: string;
	type: string;
	labels?: string;
	annotations?: string;
	description?: string;
	version?: string;
	mode?: string;
	filelogEnabled?: boolean;
	stdoutEnabled?: boolean;
}
export interface KafkaDTO {
	custom?: boolean;
	path: string;
	zkAddress: string;
	zkPort: number | undefined;
	exporterTolerations?: string[];
	exporterNodeAffinity?: AffinityItem[];
	managerTolerations?: string[];
	managerNodeAffinity?: AffinityItem[];
}

export interface DynamicSendDataParams {
	chartName: string;
	chartVersion: string;
	type: string;
	labels: string;
	description: string;
	annotations: string;
	version: string;
	clusterId: string;
	namespace: string;
	name: string;
	aliasName: string;
	capabilities: string[];
	nodeAffinity?: AffinityItem[];
	tolerations?: string[];
	scheduler: boolean;
	dynamicValues?: any;
	deployMod: string;
}
export interface DynamicCreateValueParams {
	name: string;
	aliasName: string;
	labels: string;
	description: string;
	annotations: string;
	tolerations: any[];
	tolerationsLabels: TolerationLabelItem[];
	nodeAffinity?: AffinityItem[];
	version: string;
	namespace: string;
	chartVersion: string;
}

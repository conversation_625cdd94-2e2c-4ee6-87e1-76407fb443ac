import React, { useEffect, useState } from 'react';
import { ProPage, ProContent, ProHeader } from '@/components/ProPage';
import { useHistory, useParams } from 'react-router-dom';
import { Input, Button, Form, Result, notification } from 'antd';
import FormBlock from '@/components/FormBlock';
import { renderFormItem } from '@/components/renderFormItem';
import { getDynamicFormData } from '@/services/middleware';
import { postMiddleware } from '@/services/middleware';
import pattern from '@/utils/pattern';
import { getMirror } from '@/services/common';

import { DynamicSendDataParams, DynamicCreateValueParams } from '../catalog';
import { NamespaceItem } from '@/pages/ProjectDetail/projectDetail';
import SchedulerForm from '../components/SchedulerForm';
import storage from '@/utils/storage';
import OrganAndProject from '@/pages/PublishService/components/OrganAndProject';
import ClusterAndNamespace from '@/pages/PublishService/components/ClusterAndNamespace';
import MiddlewareName from '@/pages/PublishService/components/MiddlewareName';
import MiddlewareAliasName from '@/pages/PublishService/components/MiddlewareAliasName';
import Description from '@/pages/PublishService/components/Description';
const { Item: FormItem } = Form;

function DynamicForm(): JSX.Element {
	const organization = storage.getSession('organization');
	const project = storage.getSession('project');
	const [form] = Form.useForm();
	const params: ParamsProps = useParams();
	const { type, name, aliasName } = params;
	const clusterAndNamespace = form.getFieldValue('clusterAndNamespace');
	const [chartVersion, setChartVersion] = useState<string>();
	const [dataSource, setDataSource] = useState<any>();
	const [capabilities, setCapabilities] = useState<string[]>([]);
	// * 是否点击提交跳转至结果页
	const [commitFlag, setCommitFlag] = useState<boolean>(false);
	// * 发布成功
	const [successFlag, setSuccessFlag] = useState<boolean>(false);
	// * 发布失败
	const [errorFlag, setErrorFlag] = useState<boolean>(false);
	// * 创建返回的服务名称
	const [createData, setCreateData] = useState<any>();
	// * 创建失败返回的失败信息
	const [errorData, setErrorData] = useState<string>('');
	const [mirrorList, setMirrorList] = useState<any[]>([]);
	const [currentNamespace, setCurrentNamespace] = useState<NamespaceItem>();
	// * 调度策略
	const [scheduler, setScheduler] = useState<boolean>(false);
	const history = useHistory();
	useEffect(() => {
		const values = form.getFieldsValue();
		if (clusterAndNamespace && values.chartVersion) {
			const sendData = {
				clusterId: clusterAndNamespace?.[0],
				chartName: name,
				chartVersion: values.chartVersion
			};
			getDynamicFormData(sendData).then((res) => {
				console.log(res);
				if (res.success) {
					const formatData = processData(res.data.questions);
					setDataSource(formatData);
					setCapabilities(res.data.capabilities);
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
			getMirror({
				clusterId: clusterAndNamespace?.[0]
			}).then((res) => {
				if (res.success) {
					setMirrorList(res.data.list);
				}
			});
		}
	}, [clusterAndNamespace]);

	const processData = (array: any) => {
		const obj = {};
		array.forEach((item: any) => {
			obj[item.group] = [];
		});
		array.forEach((item: any) => {
			if (Object.keys(obj).includes(item.group)) {
				obj[item.group].push(item);
			}
		});
		return obj;
	};
	const childrenRender = (values: any) => {
		if (values) {
			const keys = Object.keys(values);
			return (
				<div>
					{keys.map((item) => {
						return (
							<FormBlock key={item} title={item}>
								<div className="w-50">
									<ul className="form-layout">
										{values[item].map((formItem: any) => {
											return (
												<React.Fragment
													key={formItem.variable}
												>
													{renderFormItem(
														formItem,
														form,
														clusterAndNamespace?.[0],
														clusterAndNamespace?.[1]
													)}
												</React.Fragment>
											);
										})}
									</ul>
								</div>
							</FormBlock>
						);
					})}
				</div>
			);
		}
	};
	const handleSubmit = async () => {
		await form.validateFields().catch((error) => {
			form.scrollToField(error.errorFields[0].name[0], {
				block: 'center'
			});
			return Promise.reject();
		});
		form.validateFields().then((values: DynamicCreateValueParams) => {
			console.log(values);
			const sendData: DynamicSendDataParams = {
				clusterId: clusterAndNamespace?.[0],
				namespace: clusterAndNamespace?.[1],
				type: name,
				chartName: name,
				chartVersion: values.chartVersion,
				version: values.version,
				name: values.name,
				aliasName: values.aliasName,
				description: values.description,
				annotations: values.annotations,
				labels: values.labels,
				scheduler: scheduler,
				capabilities: capabilities,
				deployMod: 'container'
			};
			// * 主机亲和特殊处理
			if (values.nodeAffinity) {
				if (values.nodeAffinity.length) {
					sendData.nodeAffinity = values.nodeAffinity.map((item) => {
						return {
							label: item.label,
							required: item.required
						};
					});
				} else {
					notification.error({
						message: '失败',
						description: '请选择主机亲和。'
					});
				}
			}
			// * 删除动态表单中多余的主机亲和相关的值
			const dynamicValues = {};
			for (const index in values) {
				if (
					index !== 'nodeAffinityLabel' &&
					index !== 'nodeAffinityForce' &&
					index !== 'nodeAffinity' &&
					index !== 'name' &&
					index !== 'aliasName' &&
					index !== 'annotations' &&
					index !== 'tolerations' &&
					index !== 'tolerationsLabels' &&
					index !== 'clusterAndNamespace' &&
					index !== 'organAndProject' &&
					index !== 'organNameAndProjectName' &&
					index !== 'chartVersion'
				) {
					if (index === 'image.repository') {
						sendData['mirrorImageId'] = mirrorList.find(
							(item) => item.address === values[index]
						)
							? mirrorList
									.find(
										(item) => item.address === values[index]
									)
									.id.toString()
							: values[index];
					}
					dynamicValues[index] = values[index];
					if (index === 'storageClassName') {
						dynamicValues['storageClassName'] =
							values['storageClassName'].split('/')[0];
					}
				}
			}
			sendData.dynamicValues = dynamicValues;
			// * 主机容忍特殊处理
			if (values.tolerations) {
				if (values.tolerations.length) {
					sendData.tolerations = values.tolerations.map((item) => {
						return item.label;
					});
				} else {
					notification.error({
						message: '失败',
						description: '请选择主机容忍。'
					});
					return;
				}
			}
			// console.log(sendData);
			setCommitFlag(true);
			postMiddleware(sendData).then((res) => {
				if (res.success) {
					setCreateData(sendData);
					setSuccessFlag(true);
					setErrorFlag(false);
					setCommitFlag(false);
				} else {
					setErrorData(res.errorMsg);
					setSuccessFlag(false);
					setErrorFlag(true);
					setCommitFlag(false);
				}
			});
		});
	};
	const onFieldChange = (changed: any, all: any) => {
		const chartVersionTemp = all.find(
			(item: any) => item.name[0] === 'chartVersion'
		);
		if (chartVersionTemp.validated) {
			setChartVersion(chartVersionTemp?.value);
		}
	};
	// * 结果页相关
	if (commitFlag) {
		return (
			<ProPage>
				<ProHeader />
				<ProContent>
					<Result
						title="发布中"
						extra={
							<Button
								type="primary"
								onClick={() => {
									history.push(
										`/project/${type}/${name}/${aliasName}`
									);
								}}
							>
								返回列表
							</Button>
						}
					/>
				</ProContent>
			</ProPage>
		);
	}
	if (successFlag) {
		return (
			<ProPage>
				<ProHeader />
				<ProContent>
					<Result
						status="success"
						title="发布成功"
						extra={[
							<Button
								key="list"
								type="primary"
								onClick={() => {
									history.push(
										`/project/${type}/${name}/${aliasName}`
									);
								}}
							>
								返回列表
							</Button>,
							<Button
								key="detail"
								onClick={() => {
									history.push({
										pathname: `/project/${type}/${name}/${aliasName}/container/basicInfo/${createData?.name}/${chartVersion}/${clusterAndNamespace[0]}/${clusterAndNamespace[1]}`
									});
								}}
							>
								查看详情
							</Button>
						]}
					/>
				</ProContent>
			</ProPage>
		);
	}

	if (errorFlag) {
		return (
			<ProPage>
				<ProHeader />
				<ProContent>
					<Result
						status="error"
						title="发布失败"
						subTitle={errorData}
						extra={
							<Button
								type="primary"
								onClick={() => {
									history.push(
										`/project/${type}/${name}/${aliasName}`
									);
								}}
							>
								返回列表
							</Button>
						}
					/>
				</ProContent>
			</ProPage>
		);
	}
	return (
		<ProPage>
			<ProHeader
				title={`发布${aliasName}服务`}
				onBack={() => window.history.back()}
			/>
			<ProContent>
				<Form
					form={form}
					layout="vertical"
					onFieldsChange={onFieldChange}
				>
					<FormBlock title="基本信息">
						<div className="w-50">
							<ul className="form-layout">
								<OrganAndProject
									organId={organization.organId}
									organName={organization.name}
									projectId={project.projectId}
									projectName={project.aliasName}
								/>
								<ClusterAndNamespace
									setCurrentNamespace={setCurrentNamespace}
									type={name}
								/>
								<MiddlewareName type={name} />
								<MiddlewareAliasName />
								<li className="display-flex  flex-column">
									<label className="dynamic-form-name">
										<span>标签</span>
									</label>
									<div className="form-content">
										<FormItem
											rules={[
												{
													pattern: new RegExp(
														pattern.labels
													),
													message:
														'请输入key=value格式的标签，多个标签以英文逗号分隔'
												}
											]}
											name="labels"
										>
											<Input placeholder="请输入key=value格式的标签，多个标签以英文逗号分隔" />
										</FormItem>
									</div>
								</li>
								<Description />
								<li className="display-flex  flex-column">
									<label className="dynamic-form-name">
										<span>注解</span>
									</label>
									<div className="form-content">
										<FormItem
											name="annotations"
											rules={[
												{
													pattern: new RegExp(
														pattern.labels
													),
													message:
														'请输入key=value格式的注解，多个注解以英文逗号分隔'
												}
											]}
										>
											<Input placeholder="请输入key=value格式的注解，多个注解以英文逗号分隔" />
										</FormItem>
									</div>
								</li>
							</ul>
						</div>
					</FormBlock>
					<FormBlock title="调度策略">
						<div className="w-50">
							<ul className="form-layout">
								<SchedulerForm
									scheduler={scheduler}
									setScheduler={setScheduler}
								/>
							</ul>
						</div>
					</FormBlock>
					{dataSource && childrenRender(dataSource)}
					<div className="dynamic-summit-box">
						<Button
							type="primary"
							style={{ marginRight: 8 }}
							onClick={handleSubmit}
						>
							提交
						</Button>
						<Button onClick={() => window.history.back()}>
							取消
						</Button>
					</div>
				</Form>
			</ProContent>
		</ProPage>
	);
}
export default DynamicForm;

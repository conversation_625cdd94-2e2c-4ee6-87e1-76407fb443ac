import React from 'react';
import { useParams } from 'react-router';
import { InputNumber, Form } from 'antd';
import { NamespaceItem } from '@/pages/ProjectDetail/projectDetail';
import { formItemLayout614 } from '@/utils/const';

interface GIDSetProps {
	namespace?: NamespaceItem;
	form?: any;
}
export default function GIDSet(props: GIDSetProps): JSX.Element {
	const { namespace } = props;
	const params: ParamsProps = useParams();
	if (
		params.name === 'mysql' ||
		params.name === 'postgresql' ||
		params.name === 'redis'
	) {
		return (
			<Form.Item
				label="容器gid设置"
				name="containerGID"
				labelAlign="left"
				colon={false}
				{...formItemLayout614}
				rules={[
					{
						type: 'number',
						max: 2147483647,
						message: 'gid最大值为2147483647'
					},
					({ getFieldValue }) => ({
						validator(_, value) {
							if (!value) {
								return Promise.resolve();
							}
							if (value < 0) {
								if (
									namespace &&
									namespace.containerGIDRange &&
									namespace?.containerGIDRange?.min !==
										null &&
									namespace?.containerGIDRange?.max !== null
								) {
									return Promise.reject(
										new Error(
											`gid不在当前选择的命名空间指定范围内，可选范围为${namespace?.containerGIDRange.min}-${namespace?.containerGIDRange.max}`
										)
									);
								} else {
									return Promise.reject(
										new Error('gid不能为负数')
									);
								}
							}
							if (
								namespace &&
								namespace.containerGIDRange &&
								namespace?.containerGIDRange?.min !== null &&
								namespace?.containerGIDRange?.max !== null
							) {
								if (
									value < namespace?.containerGIDRange?.min ||
									value > namespace?.containerGIDRange?.max
								) {
									return Promise.reject(
										new Error(
											`gid不在当前选择的命名空间指定范围内，可选范围为${namespace?.containerGIDRange.min}-${namespace?.containerGIDRange.max}`
										)
									);
								} else {
									return Promise.resolve();
								}
							} else {
								return Promise.resolve();
							}
						}
					})
				]}
			>
				<InputNumber
					step={1}
					precision={0}
					style={{ width: '390px' }}
					placeholder="请输入容器gid, 默认命名空间指定范围内最小值"
				/>
			</Form.Item>
		);
	} else {
		return <></>;
	}
}

import React from 'react';
import { useParams } from 'react-router';
import { InputNumber, Form } from 'antd';
import { NamespaceItem } from '@/pages/ProjectDetail/projectDetail';
import { formItemLayout614 } from '@/utils/const';

interface UIDSetProps {
	namespace?: NamespaceItem;
	form?: any;
}
export default function UIDSet(props: UIDSetProps): JSX.Element {
	const { namespace } = props;
	const params: ParamsProps = useParams();
	if (
		params.name === 'mysql' ||
		params.name === 'postgresql' ||
		params.name === 'redis'
	) {
		return (
			<Form.Item
				label="容器uid设置"
				name="containerUID"
				labelAlign="left"
				colon={false}
				{...formItemLayout614}
				rules={[
					{
						type: 'number',
						max: 2147483647,
						message: 'uid最大值为2147483647'
					},
					({ getFieldValue }) => ({
						validator(_, value) {
							if (!value) {
								return Promise.resolve();
							}
							if (value < 0) {
								if (
									namespace &&
									namespace.containerUIDRange &&
									namespace?.containerUIDRange?.min !==
										null &&
									namespace?.containerUIDRange?.max !== null
								) {
									return Promise.reject(
										new Error(
											`uid不在当前选择的命名空间指定范围内，可选范围为${namespace?.containerUIDRange.min}-${namespace?.containerUIDRange.max}`
										)
									);
								} else {
									return Promise.reject(
										new Error(`uid不能为负数`)
									);
								}
							}
							if (
								namespace &&
								namespace.containerUIDRange &&
								namespace?.containerUIDRange?.min !== null &&
								namespace?.containerUIDRange?.max !== null
							) {
								if (
									value < namespace?.containerUIDRange?.min ||
									value > namespace?.containerUIDRange?.max
								) {
									return Promise.reject(
										new Error(
											`uid不在当前选择的命名空间指定范围内，可选范围为${namespace?.containerUIDRange.min}-${namespace?.containerUIDRange.max}`
										)
									);
								} else {
									return Promise.resolve();
								}
							} else {
								return Promise.resolve();
							}
						}
					})
				]}
			>
				<InputNumber
					step={1}
					precision={0}
					style={{ width: '390px' }}
					placeholder="请输入容器uid, 默认命名空间指定范围内最小值"
				/>
			</Form.Item>
		);
	} else {
		return <></>;
	}
}

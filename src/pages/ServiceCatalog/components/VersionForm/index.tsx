import SelectBlock from '@/components/SelectBlock';
import { getMiddlewareVersions } from '@/services/common';
import { AutoCompleteOptionItem } from '@/types/comment';
import { notification } from 'antd';
import React, { useEffect, useState } from 'react';
interface VersionFormProps {
	type: string;
	chartVersion: string;
	disabled?: boolean;
	version: string;
	setVersion: (value: string) => void;
}
export default function VersionForm(props: VersionFormProps): JSX.Element {
	const { type, chartVersion, disabled, version, setVersion } = props;
	const [versionOriginData, setVersionOriginData] = useState<any>([]);
	const [fatherVersion, setFatherVersion] = useState<string>('');
	const [versionList, setVersionList] = useState<AutoCompleteOptionItem[]>(
		[]
	);
	const [versionFatherList, setVersionFatherList] = useState<
		AutoCompleteOptionItem[]
	>([]);
	useEffect(() => {
		if (versionFatherList && versionFatherList.length) {
			if (version) {
				setFatherVersion(version?.split('.')[0]);
				setVersionList(
					versionOriginData
						.find(
							(item: any) =>
								item.masterVersion === version?.split('.')[0]
						)
						.slaveVersion.map((item: string) => {
							return { value: item, label: item };
						})
				);
				setVersion(version);
			} else {
				setFatherVersion(versionFatherList[0].value);
				setVersion(
					versionOriginData.find(
						(item: any) =>
							item.masterVersion === versionFatherList[0].value
					)?.slaveVersion[0]
				);
				setVersionList(
					versionOriginData[0].slaveVersion.map((item: string) => {
						return { value: item, label: item };
					})
				);
			}
		}
	}, [version, versionFatherList]);
	useEffect(() => {
		getMiddlewareVersions({
			type,
			chartVersion
		}).then((res) => {
			if (res.success) {
				if (res.data) {
					setVersionOriginData(res.data);
					const fatherList = res.data.map((item: any) => {
						return {
							value: item.masterVersion,
							label: item.masterVersion
						};
					});
					setVersionFatherList(fatherList);
				} else {
					notification.error({
						message: '错误',
						description: '没有获取到当前中间件版本'
					});
				}
			}
		});
	}, []);
	return (
		<>
			<li className="display-flex form-li">
				<label className="form-name">
					<span>版本</span>
				</label>
				<div className={`form-content display-flex`}>
					<SelectBlock
						options={versionFatherList}
						currentValue={fatherVersion}
						onCallBack={(value: any) => {
							setFatherVersion(value);
							setVersionList(
								versionOriginData
									.find(
										(item: any) =>
											item.masterVersion === value
									)
									.slaveVersion.map((item: string) => {
										return { value: item, label: item };
									})
							);
							setVersion(
								versionOriginData.find(
									(item: any) => item.masterVersion === value
								)?.slaveVersion[0]
							);
						}}
						disabled={disabled}
					/>
				</div>
			</li>
			<li className="display-flex form-li">
				<label className="form-name">
					<span></span>
				</label>
				<div className={`form-content display-flex`}>
					<SelectBlock
						options={versionList}
						currentValue={version}
						onCallBack={(value: any) => setVersion(value)}
						disabled={disabled}
					/>
				</div>
			</li>
		</>
	);
}

import React, { useState } from 'react';
import { Form, Modal, Button, notification, Upload, Alert } from 'antd';

import { api } from '@/api.json';
import storage from '@/utils/storage';
import { TOKEN } from '@/services/request';

import { UploadMiddlewareFormProps } from './upload';

const formItemLayout = {
	labelCol: {
		span: 4
	},
	wrapperCol: {
		span: 20
	}
};

function UploadMiddlewareForm(props: UploadMiddlewareFormProps): JSX.Element {
	const { visible, onCancel, onCreate, clusterId } = props;
	const [fileList, setFileList] = useState<any[]>([]);
	const [loading, setLoading] = useState<boolean>(false);
	const [form] = Form.useForm();
	const headers = {
		userToken: storage.getLocal(TOKEN),
		authType: storage.getLocal(TOKEN) ? '1' : '0'
	};

	function beforeUpload(file: any) {
		setFileList([file]);
		return false;
	}

	const handleUpload = () => {
		const formData = new FormData();
		fileList.forEach((file) => {
			formData.append('file', file);
		});
		setLoading(true);
		fetch(`${api}/clusters/${clusterId}/middlewares/upload`, {
			method: 'POST',
			body: formData,
			headers
		}).then((res: any) => {
			res.json()
				.then((r: any) => {
					if (r.success) {
						setFileList([]);
						notification.success({
							message: '成功',
							description: 'chart包上传成功'
						});
						onCreate();
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{r.errorMsg}</p>
									<p>{r.errorDetail}</p>
								</>
							)
						});
					}
				})
				.finally(() => {
					setLoading(false);
				});
		});
	};

	const onRemove = () => {
		setFileList([]);
	};

	const onOk = () => {
		handleUpload();
	};

	return (
		<Modal
			open={visible}
			onOk={onOk}
			onCancel={onCancel}
			title="中间件上架"
			width={420}
			footer={
				<>
					<Button onClick={onCancel}>取消</Button>
					<Button
						type="primary"
						disabled={!fileList.length}
						onClick={onOk}
						loading={loading}
					>
						确定
					</Button>
				</>
			}
		>
			<Alert
				message={
					<>
						自定义上架中间件，请参考
						<span
							className="name-link"
							onClick={() => {
								window.open(
									'https://www.yuque.com/docs/share/9e9ddcb4-7f37-42e2-8a9f-3c77407168ed?#'
								);
							}}
						>
							《自定义开发，上架中间件规范说明》
						</span>
					</>
				}
				type="info"
				style={{ marginBottom: 16 }}
			/>
			<Form {...formItemLayout} form={form}>
				<Form.Item
					label="上传包"
					rules={[{ required: true, message: '请上传chart包' }]}
					name="file"
					labelAlign="left"
				>
					<Upload
						beforeUpload={beforeUpload}
						onRemove={onRemove}
						fileList={fileList}
						maxCount={1}
						accept=".tgz"
					>
						{!fileList.length ? (
							<Button
								type="primary"
								style={{ margin: '0 0 10px' }}
							>
								上传文件
							</Button>
						) : null}
					</Upload>
				</Form.Item>
			</Form>
		</Modal>
	);
}
export default UploadMiddlewareForm;

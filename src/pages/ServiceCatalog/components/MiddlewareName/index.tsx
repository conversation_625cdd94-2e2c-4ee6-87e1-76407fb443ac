import React, { useState } from 'react';
import { Form, Input, notification } from 'antd';
import pattern from '@/utils/pattern';
import { getMiddlewareNameCheck } from '@/services/middleware';

const FormItem = Form.Item;
interface MiddlewareNameProps {
	clusterId?: string | null;
	namespace?: string;
	type: string;
	form: any;
	isDynamic?: boolean;
}
export default function MiddlewareName(
	props: MiddlewareNameProps
): JSX.Element {
	const { clusterId, namespace, type, form, isDynamic } = props;
	const [name, setName] = useState<string>('');

	const onBlur = () => {
		if (!form.getFieldValue('name')) return;
		if (!clusterId) {
			notification.warning({
				message: '提示',
				description: '请先选择集群，否则无法进行重名校验！'
			});
			return;
		}
		if (!namespace) {
			notification.warning({
				message: '提示',
				description: '请先选择命名空间，否则无法进行重名校验！'
			});
			return;
		}
		const reg = new RegExp(pattern.name);
		if (!reg.test(form.getFieldValue('name'))) return;
		getMiddlewareNameCheck({
			clusterId,
			namespace,
			type,
			middlewareName: form.getFieldValue('name'),
			deployMod: 'container'
		}).then((res) => {
			if (res.success) {
				if (!res.data) {
					return;
				} else {
					notification.warning({
						message: '提示',
						description: '当前中间件名称已存在！'
					});
				}
			} else {
				notification.error({
					message: '错误',
					description: res.errorMsg
				});
			}
		});
	};
	return (
		<li className={`display-flex ${isDynamic ? 'flex-column' : ''}`}>
			<label className={isDynamic ? 'dynamic-form-name' : 'form-name'}>
				<span className="ne-required">服务名称</span>
			</label>
			<div className="form-content">
				<FormItem
					name="name"
					rules={[
						{
							required: true,
							message: '请输入服务名称'
						},
						{
							pattern: new RegExp(pattern.name),
							message:
								'请输入以小写字母开头，小写字母数字及“-”组成的2-24个字符'
						}
					]}
				>
					<Input
						onBlur={onBlur}
						value={name}
						style={{ width: isDynamic ? '390px' : '100%' }}
						onChange={(e) => setName(e.target.value)}
						placeholder="请输入以小写字母开头，小写字母数字及“-”组成的2-24个字符"
					/>
				</FormItem>
			</div>
		</li>
	);
}

import { NamespaceItem } from '@/pages/ProjectDetail/projectDetail';
import React, { useEffect, useState } from 'react';
import MidProgress from '@/components/MidProgress';
import { CpuMemoryItem } from '@/pages/OrganizationDetail/organization.detail';
import transUnit from '@/utils/transUnit';

interface StockAssessmentProps {
	form?: any;
	cpu: number; // * 当需要特殊计算时，传0
	memory: number; // * 当需要特殊计算时，传0
	storage?: number;
	replicas: number; // * 当需要特殊计算时，传0
	type: string;
	mode?: string;
	nodeObj?: any;
	groupCount?: number;
	readWrite?: string;
	clusterMode?: string;
	sentinelMode?: string;
	onChange?: (
		cpuTotal: number,
		memoryTotal: number,
		storageTotal: number
	) => void;
	storageMax?: any;
	namespace?: NamespaceItem | CpuMemoryItem;
	storageClass?: string;
	isUpdate?: boolean; // * 是否用于修改规格
	isLateral?: boolean; // * 是否用于横向扩容
	directory?: boolean; // * 是否开启分盘
	volumeSize?: number; // * 分盘存储大小
	quota?: any; // * 编辑时获取当前节点/服务的原本quota，添加进request中
}
// * cpu/memory/storage 计算方法详见文档：《中间件资源使用预估计算方式》https://zeusharmonycloud.yuque.com/zkdwn1/eai56u/avv4fttftkkcptny
// * k8s storageclass 的命名规范中不允许出现英文逗号，英文逗号是平台双活添加时主动加上的，因此根据此来判定当前存储是否是双活存储
export default function StockAssessment(
	props: StockAssessmentProps
): JSX.Element {
	const {
		cpu,
		memory,
		storage,
		replicas,
		type,
		mode,
		groupCount,
		readWrite,
		clusterMode,
		sentinelMode,
		nodeObj,
		storageMax,
		namespace,
		onChange,
		storageClass,
		isUpdate,
		isLateral,
		directory,
		volumeSize,
		quota,
		form
	} = props;
	const [totalCpu, setTotalCpu] = useState<number>(0);
	const [totalMemory, setTotalMemory] = useState<number>(0);
	const [totalStorage, setTotalStorage] = useState<number>(0);
	const [storageStock, setStorageStock] = useState<number>(0);
	const [currentNamespace, setCurrentNamespace] = useState<any>();
	const [isActiveActive, setIsActiveActive] = useState<boolean>(false);
	const getNum = () => {
		if (type === 'mysql') {
			let replicasTemp = 0;
			if (mode === '1m-0s') replicasTemp = 1;
			else if (mode === '1m-1s') replicasTemp = 2;
			else if (mode === '1m-3s') replicasTemp = 4;
			else replicasTemp = replicas + 1;
			return replicasTemp;
		} else if (type === 'kafka') {
			return replicas;
		} else if (type === 'postgresql') {
			let replicasTemp = 0;
			if (mode === '1m-0s') replicasTemp = 1;
			else if (mode === '1m-1s') replicasTemp = 2;
			else if (mode === '1m-3s') replicasTemp = 4;
			else replicasTemp = replicas + 1;
			return replicasTemp;
		} else if (type === 'rocketmq') {
			let replicasTemp = 0;
			if (mode === '2m-noslave') replicasTemp = 2;
			else if (mode === '2m-2s') replicasTemp = 4;
			else if (mode === '3m-3s') replicasTemp = 6;
			else if (mode === 'dledger')
				replicasTemp = (groupCount as number) * replicas;
			return replicasTemp;
		} else {
			return replicas;
		}
	};
	const getCpu = () => {
		if (type === 'kafka') {
			let cpuTemp = Number(quota?.[type].cpu ?? 0) || 0;
			cpuTemp = cpuTemp * getNum() + 0.2 + 0.5;
			return cpuTemp;
		} else if (type === 'postgresql') {
			let cpuTemp = Number(quota?.[type].cpu ?? 0) || 0;
			cpuTemp = (cpuTemp + 0.1) * getNum();
			return cpuTemp;
		} else if (type === 'rocketmq') {
			let cpuTemp = Number(quota?.[type].cpu ?? 0) || 0;
			cpuTemp = cpuTemp * getNum() + 0.5 + 1 + 2;
			return cpuTemp;
		} else if (type === 'mysql') {
			let cpuTemp = Number(quota?.[type].cpu ?? 0) || 0;
			cpuTemp = (cpuTemp + 0.2 + 0.2) * getNum();
			return cpuTemp;
		} else if (type === 'redis') {
			if (mode === 'cluster') {
				let cpuTemp = Number(quota?.[type].cpu ?? 0) || 0;
				cpuTemp = (cpuTemp + 0.025) * getNum();
				return cpuTemp;
			} else {
				let cpuTemp = Number(quota.cpu ?? 0) || 0;
				cpuTemp = (cpuTemp + 0.025) * quota?.num;
				return cpuTemp;
			}
		} else if (type === 'elasticsearch') {
			let cpuTemp = Number(quota.cpu ?? 0) || 0;
			cpuTemp = cpuTemp * quota?.num + 0.1;
			return cpuTemp;
		} else {
			let cpuTemp = Number(quota?.[type].cpu ?? 0) || 0;
			cpuTemp = cpuTemp * getNum();
			return cpuTemp;
		}
	};
	const getMemory = () => {
		if (type === 'kafka') {
			let memoryTemp =
				Number(transUnit.removeUnit(quota?.[type].memory, 'Gi') ?? 0) ||
				0;
			memoryTemp = memoryTemp * getNum() + 0.5 + 0.5;
			return memoryTemp;
		} else if (type === 'postgresql') {
			let memoryTemp =
				Number(transUnit.removeUnit(quota?.[type].memory, 'Gi') ?? 0) ||
				0;
			memoryTemp = (memoryTemp + 0.125) * getNum();
			return memoryTemp;
		} else if (type === 'rocketmq') {
			let memoryTemp =
				Number(transUnit.removeUnit(quota?.[type].memory, 'Gi') ?? 0) ||
				0;
			memoryTemp = memoryTemp * getNum() + 1 + 2 + 2;
			return memoryTemp;
		} else if (type === 'mysql') {
			let memoryTemp =
				Number(transUnit.removeUnit(quota?.[type].memory, 'Gi') ?? 0) ||
				0;
			memoryTemp = (memoryTemp + 0.2 + 0.2) * getNum();
			return memoryTemp;
		} else if (type === 'redis') {
			if (mode === 'cluster') {
				let memoryTemp =
					Number(
						transUnit.removeUnit(quota?.[type].memory, 'Gi') ?? 0
					) || 0;
				memoryTemp = (memoryTemp + 0.025) * getNum();
				return memoryTemp;
			} else {
				let memoryTemp =
					Number(transUnit.removeUnit(quota.memory, 'Gi') ?? 0) || 0;
				memoryTemp = (memoryTemp + 0.05) * quota?.num;
				return memoryTemp;
			}
		} else if (type === 'elasticsearch') {
			let memoryTemp =
				Number(transUnit.removeUnit(quota.memory, 'Gi') ?? 0) || 0;
			memoryTemp = memoryTemp * quota?.num + 0.125;
			return memoryTemp;
		} else {
			let memoryTemp =
				Number(transUnit.removeUnit(quota?.[type].memory, 'Gi') ?? 0) ||
				0;
			memoryTemp = memoryTemp * getNum();
			return memoryTemp;
		}
	};
	useEffect(() => {
		if (storageClass) {
			if (storageClass.includes(',')) {
				setIsActiveActive(true);
			} else {
				setIsActiveActive(false);
			}
		}
	}, [storageClass]);
	useEffect(() => {
		if (nodeObj?.[type]?.storageClass) {
			if (nodeObj[type].storageClass.includes(',')) {
				setIsActiveActive(true);
			} else {
				setIsActiveActive(false);
			}
		}
	}, [nodeObj?.[type]?.storageClass]);
	useEffect(() => {
		if (namespace) {
			if (isUpdate) {
				setCurrentNamespace({
					cpu: {
						request:
							(namespace as CpuMemoryItem).cpu.request + getCpu(),
						used: (namespace as CpuMemoryItem).cpu.used
					},
					memory: {
						request:
							(namespace as CpuMemoryItem).memory.request +
							getMemory(),
						used: (namespace as CpuMemoryItem).memory.used
					}
				});
			} else {
				setCurrentNamespace({
					cpu: {
						request: (namespace as NamespaceItem)?.quotas?.cpu
							?.request,
						used: (namespace as NamespaceItem)?.quotas?.cpu?.used
					},
					memory: {
						request: (namespace as NamespaceItem)?.quotas?.memory
							?.request,
						used: (namespace as NamespaceItem)?.quotas?.memory?.used
					}
				});
			}
		}
	}, [namespace]);
	useEffect(() => {
		if (storageMax) {
			if (typeof storageMax === 'number') {
				setStorageStock(storageMax);
			} else {
				let r = 0;
				const st = [];
				for (const i in nodeObj) {
					if (!nodeObj[i].disabled) {
						st.push(nodeObj[i].storageClass);
					}
				}
				for (const i in storageMax) {
					if (i !== 'undefined/undefined') {
						if (st.includes(i)) {
							r += storageMax[i];
						}
					}
				}
				setStorageStock(r);
			}
		}
	}, [storageMax, nodeObj]);
	useEffect(() => {
		if (type === 'kafka') {
			const cpuTemp = cpu * replicas + 0.2 + 0.5;
			const memoryTemp = memory * replicas + 0.5 + 0.5;
			const storageTemp = (storage || 0) * replicas;
			setTotalCpu(cpuTemp || 0);
			setTotalMemory(memoryTemp || 0);
			setTotalStorage(storageTemp);
		} else if (type === 'postgresql') {
			if (isLateral) {
				const cpuTemp = (cpu + 0.1) * replicas;
				const memoryTemp = (memory + 0.125) * replicas;
				setTotalCpu(cpuTemp || 0);
				setTotalMemory(memoryTemp || 0);
			} else {
				let replicasTemp = 0;
				if (mode === '1m-0s') replicasTemp = 1;
				else if (mode === '1m-1s') replicasTemp = 2;
				else if (mode === '1m-3s') replicasTemp = 4;
				else replicasTemp = replicas + 1;
				const cpuTemp = (cpu + 0.1) * replicasTemp;
				const memoryTemp = (memory + 0.125) * replicasTemp;
				const storageTemp = (storage || 0) * replicasTemp;
				setTotalCpu(cpuTemp || 0);
				setTotalMemory(memoryTemp || 0);
				setTotalStorage(directory ? volumeSize || 0 : storageTemp);
			}
		} else if (type === 'rocketmq') {
			let replicasTemp = 0;
			if (mode === '2m-noslave') replicasTemp = 2;
			else if (mode === '2m-2s') replicasTemp = 4;
			else if (mode === '3m-3s') replicasTemp = 6;
			else if (mode === 'dledger')
				replicasTemp = (groupCount as number) * replicas;
			const cpuTemp = cpu * replicasTemp + 0.5 + 1 + 2;
			const memoryTemp = memory * replicasTemp + 1 + 2 + 2;
			const storageTemp = (storage || 0) * replicasTemp;
			setTotalCpu(cpuTemp || 0);
			setTotalMemory(memoryTemp || 0);
			setTotalStorage(storageTemp);
		} else if (type === 'mysql') {
			let replicasTemp = 0;
			if (mode === '1m-0s') replicasTemp = 1;
			else if (mode === '1m-1s') replicasTemp = 2;
			else if (mode === '1m-3s') replicasTemp = 4;
			else replicasTemp = replicas + 1;
			if (readWrite === 'true') {
				const cpuTemp = (nodeObj.mysql.cpu + 0.2 + 0.2) * replicasTemp;
				const memoryTemp =
					(nodeObj.mysql.memory + 0.2 + 0.2) * replicasTemp;
				const proxyCpu = nodeObj.proxy.cpu;
				const proxyMemory = nodeObj.proxy.memory;
				const proxyCpuTemp = proxyCpu * replicasTemp;
				const proxyMemoryTemp = proxyMemory * replicasTemp;
				const storageTemp = nodeObj?.mysql?.storageQuota * replicasTemp;
				setTotalCpu(cpuTemp + proxyCpuTemp || 0);
				setTotalMemory(memoryTemp + proxyMemoryTemp || 0);
				setTotalStorage(storageTemp);
			} else {
				const cpuTemp = (cpu + 0.2 + 0.2) * replicasTemp;
				const memoryTemp = (memory + 0.2 + 0.2) * replicasTemp;
				const storageTemp = (storage || 0) * replicasTemp;
				setTotalCpu(cpuTemp || 0);
				setTotalMemory(memoryTemp || 0);
				setTotalStorage(storageTemp);
			}
		} else if (type === 'redis') {
			if (isUpdate) {
				const cpuTemp = (cpu + 0.025) * replicas;
				const totalMemoryTemp = (memory + 0.05) * replicas;
				setTotalCpu(cpuTemp);
				setTotalMemory(totalMemoryTemp);
			} else {
				let replicasTemp = 0;
				if (mode === 'cluster' || mode === 'agent') {
					if (clusterMode === '3s-3m') replicasTemp = 6;
					if (clusterMode === '5s-5m') replicasTemp = 10;
					if (clusterMode === '7s-7m') replicasTemp = 14;
					if (clusterMode === '9s-9m') replicasTemp = 18;
				} else if (mode === 'sentinel') {
					if (sentinelMode === '1s-1m') replicasTemp = 2;
				} else if (mode === 'readWriteProxy') {
					if (sentinelMode === '1s-1m') replicasTemp = 2;
					if (sentinelMode === '2s-2m') replicasTemp = 4;
					if (sentinelMode === '4s-4m') replicasTemp = 8;
					if (sentinelMode === '8s-8m') replicasTemp = 16;
				}
				// * 只有集群和代理模式使用
				const totalCpuTemp = (cpu + 0.025) * replicasTemp;
				const totalMemoryTemp = (memory + 0.05) * replicasTemp;
				if (
					mode === 'sentinel' ||
					mode === 'readWriteProxy' ||
					mode === 'agent'
				) {
					setTotalStorage(
						directory
							? volumeSize || 0
							: nodeObj?.redis?.storageQuota * replicasTemp
					);
				} else {
					const storageTemp = (storage || 0) * replicasTemp;
					setTotalStorage(directory ? volumeSize || 0 : storageTemp);
				}
				if (mode === 'cluster') {
					setTotalCpu(totalCpuTemp || 0);
					setTotalMemory(totalMemoryTemp || 0);
				} else if (mode === 'agent') {
					const cpuTemp = (nodeObj.redis.cpu + 0.025) * replicasTemp;
					const memoryTemp =
						(nodeObj.redis.memory + 0.05) * replicasTemp;
					const proxyReplicas =
						replicasTemp / 2 == 1 ? replicasTemp : replicasTemp / 2;
					let proxyMemory = nodeObj.proxy.memory;
					if (proxyMemory < 0.256) {
						proxyMemory = 0.256;
					} else if (proxyMemory > 2) {
						proxyMemory = 2;
					}
					const totalProxyMemory = proxyMemory * replicasTemp;
					setTotalCpu(cpuTemp + proxyReplicas || 0);
					setTotalMemory(memoryTemp + totalProxyMemory || 0);
				} else if (mode === 'sentinel') {
					// const sentinelReplicasTemp =
					// 	replicasTemp + nodeObj.sentinel.num;
					const cpuTemp = (nodeObj.redis.cpu + 0.025) * replicasTemp;
					const memoryTemp =
						(nodeObj.redis.memory + 0.05) * replicasTemp;
					const senCpuTemp =
						nodeObj.sentinel.cpu * nodeObj.sentinel.num;
					const senMemoryTemp =
						nodeObj.sentinel.memory * nodeObj.sentinel.num;
					setTotalCpu(cpuTemp + senCpuTemp || 0);
					setTotalMemory(memoryTemp + senMemoryTemp || 0);
				} else if (mode === 'readWriteProxy') {
					// const sentinelReplicasTemp =
					// 	replicasTemp + nodeObj.sentinel.num;
					const cpuTemp = (nodeObj.redis.cpu + 0.025) * replicasTemp;
					const memoryTemp =
						(nodeObj.redis.memory + 0.05) * replicasTemp;
					const senCpuTemp =
						nodeObj.sentinel.cpu * nodeObj.sentinel.num;
					const senMemoryTemp =
						nodeObj.sentinel.memory * nodeObj.sentinel.num;
					const proxyReplicas =
						replicasTemp / 2 == 1 ? replicasTemp : replicasTemp / 2;
					const proxyCpuTemp = nodeObj.proxy.cpu * proxyReplicas;
					const proxyMemory = nodeObj.proxy.memory;
					const proxyMemoryTotal = proxyMemory * replicasTemp;
					setTotalCpu(cpuTemp + senCpuTemp + proxyCpuTemp || 0);
					setTotalMemory(
						memoryTemp + senMemoryTemp + proxyMemoryTotal || 0
					);
				}
			}
		} else if (type === 'elasticsearch') {
			if (isUpdate) {
				// * 横向扩容
				let cpuTemp = 0.1;
				let memoryTemp = 0.125;
				cpuTemp += replicas * cpu;
				memoryTemp += replicas * memory;
				setTotalCpu(cpuTemp);
				setTotalMemory(memoryTemp);
			} else {
				const obj: any = {};
				for (const i in nodeObj) {
					if (!nodeObj[i].disabled) {
						obj[i] = nodeObj[i];
					}
				}
				let cpuTemp = 0.1;
				let memoryTemp = 0.125;
				let storageTemp = 0;
				for (const i in obj) {
					cpuTemp += obj[i].num * obj[i].cpu;
					memoryTemp += obj[i].num * obj[i].memory;
					if (obj[i].storageQuota) {
						storageTemp += obj[i].num * obj[i].storageQuota;
					}
					if (directory) {
						if (i !== 'kibana') {
							storageTemp +=
								obj[i].num *
									form.getFieldValue(
										i + 'Node-data_volumeSize'
									) +
								obj[i].num *
									form.getFieldValue(
										i + 'Node-logs_volumeSize'
									);
						}
					}
				}
				setTotalCpu(cpuTemp);
				setTotalMemory(memoryTemp);
				setTotalStorage(storageTemp);
			}
		} else {
			const cpuTemp = cpu * replicas;
			const memoryTemp = memory * replicas;
			const storageTemp = (storage || 0) * replicas;
			setTotalCpu(cpuTemp || 0);
			setTotalMemory(memoryTemp || 0);
			setTotalStorage(storageTemp);
		}
	}, [props]);
	useEffect(() => {
		onChange && onChange(totalCpu, totalMemory, totalStorage);
	}, [totalCpu, totalMemory, totalStorage]);
	return (
		<>
			<label className="form-name">
				<span>资源评估</span>
			</label>
			<li className="display-flex flex-align">
				{!isUpdate && <label>当前规格预计使用资源</label>}
				<div
					className="ml-12"
					style={{
						width:
							currentNamespace &&
							(currentNamespace?.cpu?.request || 0) -
								(currentNamespace?.cpu?.used || 0) ===
								0
								? 230
								: 170
					}}
				>
					<label>
						CPU
						{currentNamespace &&
							(currentNamespace.cpu?.request || 0) -
								(currentNamespace.cpu?.used || 0) ===
								0 && (
								<span
									style={{ color: '#ff4d4f', marginLeft: 2 }}
								>
									(当前CPU无剩余可使用量，请切换！)
								</span>
							)}
					</label>
					<MidProgress
						fromColor="#226EE7"
						toColor="#47A7F5"
						unit="Core"
						used={totalCpu}
						total={
							(currentNamespace?.cpu?.request || 0) -
							(currentNamespace?.cpu?.used || 0)
						}
					/>
				</div>
				<div
					className="ml-12"
					style={{
						width:
							currentNamespace &&
							(currentNamespace?.cpu?.request || 0) -
								(currentNamespace?.cpu?.used || 0) ===
								0
								? 230
								: 170
					}}
				>
					<label>
						内存
						{currentNamespace &&
							(currentNamespace.memory?.request || 0) -
								(currentNamespace.memory?.used || 0) ===
								0 && (
								<span
									style={{ color: '#ff4d4f', marginLeft: 2 }}
								>
									(当前内存无剩余可使用量，请切换！)
								</span>
							)}
					</label>
					<MidProgress
						fromColor="#1AC1C4"
						toColor="#74DDDF"
						unit="GB"
						used={totalMemory}
						total={
							(currentNamespace?.memory?.request || 0) -
							(currentNamespace?.memory?.used || 0)
						}
					/>
				</div>
				{!isUpdate && (
					<div className="ml-12" style={{ width: 250 }}>
						<label>
							存储
							{storageClass && storageMax === 0 ? (
								<span
									style={{ color: '#ff4d4f', marginLeft: 2 }}
								>
									(当前存储剩余可使用量不足，请切换！)
								</span>
							) : null}
						</label>
						<MidProgress
							fromColor="#5C0EDF"
							toColor="#853CFF"
							unit="GB"
							used={
								isActiveActive ? totalStorage / 2 : totalStorage
							}
							total={storageStock}
						/>
					</div>
				)}
			</li>
		</>
	);
}

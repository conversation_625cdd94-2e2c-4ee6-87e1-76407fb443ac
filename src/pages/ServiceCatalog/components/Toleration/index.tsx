import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON>ple<PERSON>, Button, Switch, Tag, notification } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { AutoCompleteOptionItem } from '@/types/comment';
import { getNodeTaint } from '@/services/middleware';
import './index.less';

interface TolerationProps {
	flag: boolean;
	onChange: (value: any) => void;
	flagChange: (value: any) => void;
	values: any;
	cluster: any;
	formLabel: string;
}
export default function Toleration(props: TolerationProps): JSX.Element {
	const { flag, onChange, flagChange, values, cluster, formLabel } = props;
	const [label, setLabel] = useState<string>('');
	const [labelList, setLabelList] = useState<AutoCompleteOptionItem[]>([]);
	useEffect(() => {
		if (JSON.stringify(cluster) !== '{}') {
			getNodeTaint({ clusterid: cluster.id }).then((res) => {
				if (res.success) {
					const list = res.data.map((item: string) => {
						return {
							value: item,
							label: item
						};
					});
					setLabelList(list);
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		}
	}, [cluster]);
	return (
		<>
			<li className="display-flex form-li flex-align">
				<label className="form-name">
					<span className="mr-8">{formLabel}</span>
				</label>
				<div className="form-content display-flex host-affinity">
					<div className="switch">
						{flag ? '已开启' : '关闭'}
						<Switch
							checked={flag}
							onChange={(value) => {
								onChange([]);
								setLabel('');
								flagChange(value);
							}}
							size="small"
							style={{
								marginLeft: 16,
								verticalAlign: 'middle'
							}}
						/>
					</div>
					{flag ? (
						<>
							<div className="input">
								<AutoComplete
									value={label}
									placeholder="请选择或输入主机容忍"
									onChange={(value) => setLabel(value)}
									onBlur={() => {
										if (
											label &&
											!values.find(
												(item: any) =>
													item.label === label
											)
										) {
											onChange([
												...values,
												{
													label: label,
													id: Math.random()
												}
											]);
										}
									}}
									allowClear={true}
									options={labelList}
									style={{ width: 260 }}
								/>
							</div>
							<div className="add">
								<Button
									style={{
										marginLeft: '4px',
										padding: '0 9px'
									}}
									disabled={label ? false : true}
									onClick={() => {
										if (
											!values.find(
												(item: any) =>
													item.label === label
											)
										) {
											onChange([
												...values,
												{
													label: label,
													id: Math.random()
												}
											]);
										}
									}}
								>
									<PlusOutlined
										style={{
											color: '#005AA5'
										}}
									/>
								</Button>
							</div>
						</>
					) : null}
				</div>
			</li>
			{flag && values.length ? (
				<div className="tags">
					{values.map((item: any) => {
						return (
							<Tag
								key={item.label}
								closable
								style={{
									padding: '4px 10px'
								}}
								onClose={() => {
									onChange(
										values.filter(
											(arr: any) => arr.id !== item.id
										)
									);
								}}
							>
								{item.label}
							</Tag>
						);
					})}
				</div>
			) : null}
		</>
	);
}

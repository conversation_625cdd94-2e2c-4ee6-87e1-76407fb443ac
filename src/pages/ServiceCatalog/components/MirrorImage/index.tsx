import * as React from 'react';
import { useState, useEffect } from 'react';
import { Form, AutoComplete, Select } from 'antd';
import { getMirror } from '@/services/common';

const FormItem = Form.Item;
function MirrorImage(props: any): JSX.Element {
	const { clusterId, type } = props;
	const [mirrorList, setMirrorList] = useState<any>([]);
	const getData = () =>
		getMirror({
			clusterId
		}).then((res) => {
			if (res.success) {
				setMirrorList(res.data.list);
			}
		});
	useEffect(() => {
		if (clusterId) {
			getData();
		}
	}, [clusterId]);
	return (
		<li className="display-flex">
			<label className="form-name">
				<span className="ne-required" style={{ marginRight: 8 }}>
					镜像仓库
				</span>
			</label>
			<div className="form-content">
				<FormItem
					name={
						type === 'relation'
							? 'relationMirrorImageId'
							: 'mirrorImageId'
					}
					required
					rules={[
						{
							required: true,
							message: '请选择镜像仓库'
						}
					]}
					initialValue={mirrorList?.[0]?.address}
				>
					<Select
						placeholder="请选择"
						options={mirrorList.map((item: any) => {
							return {
								value: item.address,
								label: item.address
							};
						})}
						style={{
							width: '380px'
						}}
					/>
					{/* <AutoComplete
						placeholder="请选择"
						allowClear={true}
						options={mirrorList.map((item: any) => {
							return {
								value: item.address,
								label: item.address
							};
						})}
						style={{
							width: '380px'
						}}
					/> */}
				</FormItem>
			</div>
		</li>
	);
}

export default MirrorImage;

import storage from '@/utils/storage';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Switch, Tooltip } from 'antd';
import React, { useState } from 'react';
interface SchedulerFormProps {
	scheduler: boolean;
	setScheduler: (value: boolean) => void;
	backupFileName?: boolean;
}
// * 所有中间件都具备的智能调度开关，默认关闭
export default function SchedulerForm(props: SchedulerFormProps): JSX.Element {
	const { scheduler, setScheduler, backupFileName } = props;
	// * 智能调度 feature 功能
	const [extendSchedulerAPI] = useState<boolean>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'extendScheduler')?.enabled ??
			true
	);
	if (extendSchedulerAPI) {
		return (
			<li className="display-flex form-li flex-align">
				<label className="form-name">
					<span className="mr-8">
						智能调度
						<Tooltip
							title={
								'当某个节点的存储不够的时候，pod将不会往该节点上调度（仅限lvm存储可以使用）'
							}
						>
							<QuestionCircleOutlined style={{ marginLeft: 8 }} />
						</Tooltip>
					</span>
				</label>
				<div className={`form-content display-flex`}>
					<div className="">
						{scheduler ? '已开启' : '关闭'}
						<Switch
							checked={scheduler}
							onChange={(value) => setScheduler(value)}
							size="small"
							style={{
								marginLeft: 16,
								verticalAlign: 'middle'
							}}
							disabled={!!backupFileName}
						/>
					</div>
				</div>
			</li>
		);
	} else {
		return <></>;
	}
}

.database-mag-main,
.account-mag-main,
.sql-audit-main {
	padding: @padding-sm 24px;
	.account-mag-action-content {
		margin-bottom: @margin;
	}
	.account-mag-filter-content,
	.sql-audit-filter-content {
		margin-top: @margin;
		display: flex;
		justify-content: space-between;
	}
	.account-mag-pagination-content,
	.account-mag-table-content,
	.sql-audit-pagination-content,
	.sql-audit-table-content {
		margin-top: @margin;
	}
}
.sql-console-sider {
	background-color: #f5f5f5 !important;
	.ant-layout-sider-trigger {
		border-top: 1px solid @black-6;
		background-color: #f5f5f5 !important;
	}
	.sql-console-sider-title-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: @padding-lg;
		font-size: @font-2;
		font-weight: @font-weight-lg;
		height: 50px;
		border-bottom: 1px solid @black-6;
		& > div {
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			cursor: pointer;
		}
	}
	.sql-console-sider-search {
		padding: @padding-lg 0 @padding-lg @padding-lg;
		height: calc(100% - 80px);
		.sql-console-tree-content {
			max-height: calc(100% - 40px);
			min-height: calc(100% - 40px);
			.ant-tree {
				background: transparent !important;
				.ant-tree-list {
					.ant-tree-treenode {
						.ant-tree-node-content-wrapper {
							display: flex;
							.ant-dropdown-trigger {
								display: block;
							}
						}
					}
				}
			}
		}
		.redis-db-item {
			cursor: pointer;
			display: flex;
			align-items: center;
			padding-bottom: 16px;
			&:hover {
				color: @blue-base;
			}
			&.active {
				color: @blue-base;
			}
		}
	}
	.ant-layout-sider-children {
		overflow-y: auto;
	}
}
.sql-console-content {
	position: relative;
	.sql-console-tabs-content {
		padding: 0px 24px;
		height: calc(100% - 36px - 72px);
		&.tab-mb > .ant-tabs-nav{
			margin-bottom: 0;
		}
		.ant-tabs-content {
			height: 100%;
			.ant-tabs-tabpane-active {
				height: 100%;
			}
		}
	}
}
.code-console-main {
	height: 100%;
	.code-console-action-content {
		width: 100%;
		height: 34px;
		background: #222222;
		// margin-top: 16px;
		border-radius: 4px 4px 0 0;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: @padding-sm 22px @padding-sm;
	}
	#code-console-content {
		height: calc(100% - 60px);
		.CodeMirror {
			height: 100% !important;
			overflow-y: auto !important;
			// .CodeMirror-gutter-wrapper {
			// 	left: -24px !important;
			// }
		}
	}
}
.Resizer {
	background: #000;
	opacity: 0.2;
	z-index: 1;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	-moz-background-clip: padding;
	-webkit-background-clip: padding;
	background-clip: padding-box;
}

.Resizer:hover {
	-webkit-transition: all 2s ease;
	transition: all 2s ease;
}

.Resizer.horizontal {
	height: 11px;
	margin: -5px 0;
	border-top: 5px solid rgba(255, 255, 255, 0);
	border-bottom: 5px solid rgba(255, 255, 255, 0);
	cursor: row-resize;
	width: 100%;
}

.Resizer.horizontal:hover {
	border-top: 5px solid rgba(0, 0, 0, 0.5);
	border-bottom: 5px solid rgba(0, 0, 0, 0.5);
}

.Resizer.vertical {
	width: 11px;
	margin: 0 -5px;
	border-left: 5px solid rgba(255, 255, 255, 0);
	border-right: 5px solid rgba(255, 255, 255, 0);
	cursor: col-resize;
}

.Resizer.vertical:hover {
	border-left: 5px solid rgba(0, 0, 0, 0.5);
	border-right: 5px solid rgba(0, 0, 0, 0.5);
}
.Resizer.disabled {
	cursor: not-allowed;
}
.Resizer.disabled:hover {
	border-color: transparent;
}
.redis-tab-content {
	height: 100%;
}
.site-tree-search-value {
	color: #226ee7;
}
[data-theme='dark'] .site-tree-search-value {
	color: #226ee7;
}

.code-console-full-screen {
	position: absolute;
	top: 0;
	width: 100%;
	height: 216%;
	z-index: 9999;
}

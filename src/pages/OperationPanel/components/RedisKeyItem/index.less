.redis-key-item-content {
	width: 100%;
	height: 32px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 8px;
	cursor: pointer;
	.redis-key-item-label {
		width: 75%;

	}
	.redis-key-item-action {
		display: none;
		// width: 35px;
	}
	&:hover {
		background-color: @blue-1;
		color: @blue-base;
		.redis-key-item-action {
			display: block;
		}
	}
	&.active{
		background-color: @blue-1;
		color: @blue-base;
	}
}

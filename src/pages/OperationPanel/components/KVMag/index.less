.data-items {
	display: flex;
	align-items: center;
	line-height: 32px;
}
.data-item {
	display: flex;
	align-items: center;
	&.item-width {
		width: 50%;
	}
	&.mb {
		margin-bottom: 16px;
	}
	.label-item {
		width: 80px;
	}
	.ant-form-item {
		margin-bottom: 0;
	}
	.ant-form.ant-form-horizontal {
		display: flex;
	}
	.ant-form {
		width: calc(100% - 80px);
	}
	.ant-form-item {
		width: 100%;
	}
}
.title-container {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 8px;
}
.no-page.ant-pagination-simple {
	.ant-pagination-simple-pager {
		input {
			padding: 0;
			margin: 0;
			pointer-events: none;
			border: none;
		}
	}
}

import { formItemLayout618 } from '@/utils/const';
import {
	Form,
	Input,
	Modal,
	notification,
	Radio,
	RadioChangeEvent,
	Select
} from 'antd';
import React, { useEffect, useState } from 'react';
import { getList } from '@/services/serviceList';
import {
	authLogin,
	authLogout,
	getAccountsByToken
} from '@/services/operatorPanel';
import { LoginConsoleProps } from '../index.d';
import { serviceProps } from '@/pages/ServiceList/service.list';
import { encrypt } from '@/utils/utils';
import storage from '@/utils/storage';
import { getRsaKey } from '@/services/user';
import { ExecuteOrderFuc } from '@/components/WorkOrderFuc';
const { Option } = Select;
// ! 运维面板所有相关存储都存在sessionStorage中
export default function LoginConsole(props: LoginConsoleProps): JSX.Element {
	const {
		open,
		onCancel,
		organId,
		projectId,
		clusterId,
		namespace,
		middlewareName,
		middlewareType,
		version,
		currentUser,
		onCreate
	} = props;
	const [form] = Form.useForm();
	const middlewareNameForm = Form.useWatch('middlewareName', form);
	const [data, setData] = useState<any[]>([]);
	const [filterMiddlewares, setFilterMiddlewares] = useState<any[]>([]);
	const [loginMode, setLoginMode] = useState<'select' | 'input'>('select');
	const [accounts, setAccounts] = useState<MiddlewareAccount[]>([]);
	const [disabledSelectMiddleware, setDisabledSelectMiddleware] =
		useState<boolean>(false);
	useEffect(() => {
		getList({
			organId: organId,
			projectId: projectId,
			clusterId: clusterId,
			namespace: namespace,
			type: middlewareType,
			filterServerMod: false,
			keyword: ''
		}).then((res) => {
			if (res.success) {
				setData(res.data);
				const cur_middleware = res.data.find(
					(item: any) => item.name === middlewareName
				);
				if (cur_middleware.lock === 'locked') {
					setDisabledSelectMiddleware(true);
				} else {
					setDisabledSelectMiddleware(false);
				}
				if (middlewareType === 'redis') {
					if (loginMode === 'select') {
						if (cur_middleware?.supportSso) {
							form.setFieldValue(
								'middlewareName',
								middlewareName
							);
						}
						let list_temp = res.data.filter(
							(item: any) => item.supportSso === true
						);
						if (cur_middleware.lock !== 'locked') {
							list_temp = list_temp.filter((item: any) => {
								return item.lock !== 'locked';
							});
						}
						setFilterMiddlewares(list_temp);
					} else {
						form.setFieldValue('middlewareName', middlewareName);
						setFilterMiddlewares(res.data);
					}
				} else {
					setFilterMiddlewares(res.data);
					form.setFieldValue('middlewareName', middlewareName);
				}
			}
		});
		getRsaKey().then((res) => {
			if (res.success) {
				const pub = `-----BEGIN PUBLIC KEY-----${res.data}-----END PUBLIC KEY-----`;
				storage.setSession('rsa', pub);
			}
		});
	}, []);
	useEffect(() => {
		if (middlewareType !== 'redis') {
			setLoginMode('input');
		}
	}, [middlewareType]);
	useEffect(() => {
		form.setFieldValue('account', undefined);
		if (loginMode === 'input') return;
		if (!middlewareNameForm) return;
		if (data.length === 0) return;
		const middleware_temp = data.find(
			(item) => item.name === middlewareNameForm
		);
		getAccounts(middleware_temp);
	}, [middlewareNameForm, data]);
	const getAccounts = async (middleware_temp: any) => {
		await getAccountsByToken({
			clusterId: clusterId,
			namespace: namespace,
			type: middlewareType,
			deployMode: middleware_temp?.deployMod,
			middlewareName: middleware_temp?.name
		}).then((res) => {
			if (res.success) {
				setAccounts(res.data);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	const onChange = async (e: RadioChangeEvent) => {
		setLoginMode(e.target.value);
		const cur_middleware = data.find(
			(item: any) => item.name === middlewareName
		);
		if (e.target.value === 'select') {
			await getAccounts(cur_middleware);
			if (cur_middleware?.supportSso) {
				form.setFieldValue('middlewareName', middlewareName);
			} else {
				form.setFieldValue('middlewareName', undefined);
			}
			let list_temp = data.filter(
				(item: any) => item.supportSso === true
			);
			if (cur_middleware.lock !== 'locked') {
				list_temp = list_temp.filter((item: any) => {
					return item.lock !== 'locked';
				});
			}
			setFilterMiddlewares(list_temp);
		} else {
			form.setFieldsValue({
				middlewareName: middlewareName,
				username: undefined,
				password: undefined
			});
			let list_temp = data;
			if (cur_middleware.lock !== 'locked') {
				list_temp = data.filter((item: any) => item.lock !== 'locked');
			}
			setFilterMiddlewares(list_temp);
		}
	};
	const onOk = async () => {
		await form.validateFields();
		const values = form.getFieldsValue();
		let username_temp = '';
		let password_encrypt = '';
		if (loginMode === 'input') {
			username_temp = values.username;
			password_encrypt =
				encrypt(values.password, storage.getSession('rsa')) ||
				values.password;
		} else {
			const cur_account = accounts.find(
				(item) => item.account === values.account
			);
			if (!cur_account) {
				notification.warning({
					message: '提醒',
					description: '未找到当前账户信息'
				});
				return;
			}
			username_temp = cur_account.account;
			password_encrypt = cur_account.password;
		}
		const sendData = {
			clusterId,
			middlewareName: values.middlewareName,
			type: middlewareType,
			namespace: namespace,
			username: username_temp,
			password: password_encrypt
		};
		await ExecuteOrderFuc();
		authLogout({
			clusterId,
			middlewareName,
			namespace,
			type: middlewareType,
			username: currentUser?.username || values.username
		}).then((res) => {
			if (res.success) {
				authLogin(sendData).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '登录成功'
						});
						onCreate({
							...res.data,
							middlewareName: values.middlewareName
						});
					} else {
						notification.error({
							message: '失败',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			} else {
				notification.error({
					message: '失败',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	return (
		<Modal title="登录控制台" open={open} onCancel={onCancel} onOk={onOk}>
			{middlewareType === 'redis' && (
				<Radio.Group
					onChange={onChange}
					value={loginMode}
					className="mb-16"
				>
					<Radio value="select">账户选择登录</Radio>
					<Radio value="input">账户密码登录</Radio>
				</Radio.Group>
			)}
			<Form form={form} {...formItemLayout618} labelAlign="left">
				<Form.Item required label="实例ID" name="middlewareName">
					<Select
						placeholder="请选择实例ID"
						disabled={disabledSelectMiddleware}
					>
						{filterMiddlewares.map((item: serviceProps) => {
							return (
								<Option key={item.name} value={item.name}>
									{item.name}
								</Option>
							);
						})}
					</Select>
				</Form.Item>
				{loginMode === 'select' && (
					<Form.Item
						label="选择账户"
						name="account"
						rules={[{ required: true, message: '账户不能为空' }]}
					>
						<Select placeholder="请选择账户">
							{accounts.map((item: MiddlewareAccount) => {
								return (
									<Select.Option
										key={item.account}
										value={item.account}
									>
										{item.account}
									</Select.Option>
								);
							})}
						</Select>
					</Form.Item>
				)}
				{(loginMode === 'input' || middlewareType !== 'redis') && (
					<>
						<Form.Item
							label="账号"
							name="username"
							rules={[{ required: true, message: '请输入账号' }]}
							initialValue={
								middlewareType === 'redis' &&
								version.substring(0, 1) === '5'
									? 'default'
									: ''
							}
						>
							<Input
								placeholder="请输入"
								disabled={
									middlewareType === 'redis' &&
									version.substring(0, 1) === '5'
								}
								onKeyDown={(e) => e.keyCode === 13 && onOk()}
							/>
						</Form.Item>
						<Form.Item
							label="密码"
							name="password"
							rules={
								middlewareType === 'redis'
									? []
									: [
											{
												required: true,
												message: '请输入密码'
											}
									  ]
							}
						>
							<Input.Password
								placeholder="请输入"
								onKeyDown={(e) => e.keyCode === 13 && onOk()}
							/>
						</Form.Item>
					</>
				)}
			</Form>
		</Modal>
	);
}

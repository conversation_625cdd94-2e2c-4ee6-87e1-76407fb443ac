import { ProContent, ProMenu } from '@/components/ProPage';
import React, { useState } from 'react';
import RuleAndNotification from './RuleAndNotification';

export default function RuleCenter(): JSX.Element {
	const [selectedKey, setSelectedKey] = useState<string[]>([
		'ruleAndNotification'
	]);
	const menuSelect = (item: any) => {
		setSelectedKey(item.keyPath);
	};
	// * v1.3.3版本原本有两个tab，后改为1个，二级菜单就暂时去除
	const ConsoleMenu = () => {
		const items = [
			{
				label: '告警规则及通知',
				key: 'ruleAndNotification'
			}
		];
		return (
			<ProMenu
				selectedKeys={selectedKey}
				onClick={menuSelect}
				style={{ height: '100%', marginLeft: 0 }}
				items={items}
			/>
		);
	};
	const childrenRender = (selectedKey: string) => {
		switch (selectedKey) {
			case 'ruleAndNotification':
				return <RuleAndNotification />;
			default:
				break;
		}
	};
	return (
		<ProContent style={{ margin: 0, padding: 0 }}>
			{childrenRender(selectedKey[0])}
		</ProContent>
	);
}

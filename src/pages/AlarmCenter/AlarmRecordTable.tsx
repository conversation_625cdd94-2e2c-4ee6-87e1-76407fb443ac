import { alarmWarn } from '@/utils/const';
import { ReloadOutlined } from '@ant-design/icons';
import { Button, Input, Spin, Table, Tag, notification, Modal } from 'antd';
import type { TablePaginationConfig } from 'antd/es/table';
import React, { useEffect, useState } from 'react';
import {
	deleteRecords,
	getAlarmRecordIndex,
	getAlarmRecords,
	getAlarmRecordsFilter
} from '@/services/alarm';
import { AlarmRecordIndexItem, AlarmRecordSendData, IndexItem } from './alarm';
import { nullRender } from '@/utils/utils';
import { FiltersProps } from '@/types/comment';
import './index.less';
const { CheckableTag } = Tag;
const levelRender = (value: string) => {
	return (
		<span className={value + ' level'}>
			{value && alarmWarn.find((item) => item.value === value)
				? alarmWarn.find((item) => item.value === value).text
				: ''}
		</span>
	);
};
const systemColumns = [
	{
		title: '告警等级',
		dataIndex: 'level',
		key: 'level',
		render: levelRender,
		filters: alarmWarn,
		filterMultiple: false,
		width: 100
	},
	{
		title: '告警时间',
		dataIndex: 'alertTime',
		key: 'alertTime',
		render: nullRender,
		width: 200,
		sorter: true
	},
	{
		title: '接收时间',
		dataIndex: 'alertReceiveTime',
		key: 'alertReceiveTime',
		render: nullRender,
		width: 200,
		sorter: true
	},
	{
		title: '告警信息',
		dataIndex: 'message',
		key: 'message',
		render: (value: string) => value || '/',
		ellipsis: true
	}
];

const serviceColumns = [
	{
		title: '告警等级',
		dataIndex: 'level',
		key: 'level',
		render: levelRender,
		filters: alarmWarn,
		filterMultiple: false,
		width: 100
	},
	{
		title: '监测对象',
		dataIndex: 'name',
		key: 'name'
	},
	{
		title: '告警时间',
		dataIndex: 'alertTime',
		key: 'alertTime',
		render: nullRender,
		width: 200,
		sorter: true
	},
	{
		title: '接收时间',
		dataIndex: 'alertReceiveTime',
		key: 'alertReceiveTime',
		width: 200,
		render: nullRender,
		sorter: true
	},
	{
		title: '告警信息',
		dataIndex: 'message',
		key: 'message',
		render: (value: string) => value || '/',
		ellipsis: true
	}
];
interface AlarmRecordTableProps {
	type: string;
	refresh: boolean;
}
interface TableParams {
	pagination?: TablePaginationConfig;
	filters?: any;
	sorter?: any;
}
export default function AlarmRecordTable(
	props: AlarmRecordTableProps
): JSX.Element {
	const { type, refresh } = props;
	const [selectedTags, setSelectedTags] = useState<string[]>(['all']);
	const [records, setRecords] = useState([]);
	const [loading, setLoading] = useState<boolean>(false);
	const [spinning, setSpinning] = useState<boolean>(false);
	const [keyword, setKeyword] = useState<string>('');
	const [recordIndex, setRecordIndex] = useState<IndexItem[]>([
		{ value: 'all', label: '全部', count: 0 }
	]);
	const [recordFilter, setRecordFilter] = useState<FiltersProps[]>([]);
	const [tableParams, setTableParams] = useState<TableParams>({});
	const [paginationParams, setPaginationParams] = useState<TableParams>({
		pagination: {
			current: 1,
			pageSize: 10
		}
	});
	const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
	const clusterColumns = [
		{
			title: '告警等级',
			dataIndex: 'level',
			key: 'level',
			render: levelRender,
			filters: alarmWarn,
			filterMultiple: false,
			width: 100
		},
		{
			title: '监测对象',
			dataIndex: 'aliasName',
			key: 'aliasName',
			filters: recordFilter,
			filterMultiple: false
		},
		{
			title: '所属集群',
			dataIndex: 'nickname',
			key: 'nickname'
		},
		{
			title: '告警时间',
			dataIndex: 'alertTime',
			key: 'alertTime',
			width: 200,
			sorter: true
		},
		{
			title: '接收时间',
			dataIndex: 'alertReceiveTime',
			key: 'alertReceiveTime',
			width: 200,
			sorter: true
		},
		{
			title: '告警信息',
			dataIndex: 'message',
			key: 'message',
			render: (value: string) => value || '/',
			ellipsis: true
		}
	];
	useEffect(() => {
		if (type === 'cluster') {
			getAlarmRecordsFilter({ alertType: 'cluster' }).then((res) => {
				if (res.success) {
					console.log(res);
					const list = res.data.map((item: AlarmRecordIndexItem) => ({
						value: item.targetName,
						text: item.targetAliasName
					}));
					setRecordFilter(list);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			});
		}
		if (type !== 'system') {
			setSpinning(true);
			getAlarmRecordList();
		}
		const sendData: AlarmRecordSendData = {
			alertType: type,
			current: paginationParams.pagination?.current || 1,
			size: paginationParams.pagination?.pageSize || 10
		};
		getRecordData(sendData);
	}, [type, refresh]);
	const getAlarmRecordList = () => {
		getAlarmRecordIndex({ alertType: type })
			.then((res) => {
				if (res.success) {
					if (type === 'service') {
						const list = res.data.map(
							(item: AlarmRecordIndexItem) => ({
								value: item.middlewareType,
								label: item.middlewareName,
								count: item.count
							})
						);
						const all = list.reduce((pre, cur) => {
							return pre + cur.count;
						}, 0);
						setRecordIndex([
							{ label: '全部', value: 'all', count: all },
							...list
						]);
					}
					if (type === 'cluster') {
						const list = res.data.map(
							(item: AlarmRecordIndexItem) => ({
								value: item.clusterId,
								label: item.clusterAliasName,
								count: item.count
							})
						);
						const all = list.reduce((pre, cur) => {
							return pre + cur.count;
						}, 0);
						setRecordIndex([
							{ label: '全部', value: 'all', count: all },
							...list
						]);
					}
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setSpinning(false);
			});
	};
	const getRecordData = (sendData: AlarmRecordSendData) => {
		setLoading(true);
		getAlarmRecords(sendData)
			.then((res) => {
				if (res.success) {
					console.log(res);
					setRecords(res.data.list);
					setPaginationParams({
						pagination: {
							current: res.data.pageNum,
							pageSize: res.data.pageSize,
							total: res.data.total
						}
					});
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const columnRender = () => {
		switch (type) {
			case 'system':
				return systemColumns;
			case 'cluster':
				return clusterColumns;
			case 'service':
				return serviceColumns;
			default:
				return systemColumns;
		}
	};
	const handleChange = (tag: string, checked: boolean) => {
		setSelectedTags([tag]);
		let alertLevelFilter = '';
		let alertTargetFilter = '';
		let alertTimeSorter = '';
		let alertReceiveTimeSorter = '';
		if (
			tableParams?.filters &&
			JSON.stringify(tableParams.filters) !== '{}'
		) {
			alertLevelFilter = tableParams.filters.level?.[0] ?? '';
			alertTargetFilter = tableParams.filters.aliasName?.[0] ?? '';
		}
		if (tableParams.sorter && tableParams.sorter.field === 'alertTime') {
			alertTimeSorter = tableParams.sorter.order
				? tableParams.sorter.order === 'ascend'
					? 'asc'
					: 'desc'
				: '';
		}
		if (
			tableParams.sorter &&
			tableParams.sorter.field === 'alertReceiveTime'
		) {
			alertReceiveTimeSorter = tableParams.sorter.order
				? tableParams.sorter.order === 'ascend'
					? 'asc'
					: 'desc'
				: '';
		}
		if (type === 'cluster') {
			const sendData: AlarmRecordSendData = {
				current: tableParams?.pagination?.current || 1,
				size: tableParams?.pagination?.pageSize || 10,
				alertType: type,
				alertTarget: alertTargetFilter,
				alertLevel: alertLevelFilter ? alertLevelFilter[0] : '',
				alertTime: alertTimeSorter,
				receiveTime: alertReceiveTimeSorter,
				clusterId: tag === 'all' ? '' : tag,
				keyword: keyword
			};
			getRecordData(sendData);
		}
		if (type === 'service') {
			const sendData: AlarmRecordSendData = {
				current: tableParams?.pagination?.current || 1,
				size: tableParams?.pagination?.pageSize || 10,
				alertType: type,
				alertLevel: alertLevelFilter ? alertLevelFilter[0] : '',
				alertTime: alertTimeSorter,
				receiveTime: alertReceiveTimeSorter,
				middlewareType: tag === 'all' ? '' : tag,
				keyword: keyword
			};
			getRecordData(sendData);
		}
	};
	const onSearch = (value: string) => {
		let alertLevelFilter = '';
		let alertTimeSorter = '';
		let alertReceiveTimeSorter = '';
		if (
			tableParams?.filters &&
			JSON.stringify(tableParams.filters) !== '{}'
		) {
			alertLevelFilter = tableParams.filters.level;
		}
		if (tableParams.sorter && tableParams.sorter.field === 'alertTime') {
			alertTimeSorter = tableParams.sorter.order
				? tableParams.sorter.order === 'ascend'
					? 'asc'
					: 'desc'
				: '';
		}
		if (
			tableParams.sorter &&
			tableParams.sorter.field === 'alertReceiveTime'
		) {
			alertReceiveTimeSorter = tableParams.sorter.order
				? tableParams.sorter.order === 'ascend'
					? 'asc'
					: 'desc'
				: '';
		}
		const sendData: AlarmRecordSendData = {
			current: 1,
			size: tableParams?.pagination?.pageSize || 10,
			alertType: type,
			alertLevel: alertLevelFilter ? alertLevelFilter[0] : '',
			alertTime: alertTimeSorter,
			receiveTime: alertReceiveTimeSorter,
			keyword: value
		};
		if (type === 'cluster') {
			sendData.clusterId =
				selectedTags[0] === 'all' ? '' : selectedTags[0];
		}
		if (type === 'service') {
			sendData.middlewareType =
				selectedTags[0] === 'all' ? '' : selectedTags[0];
		}
		getRecordData(sendData);
	};
	const handleTableChange = (
		pagination: TablePaginationConfig,
		filters: any,
		sorter: any
	) => {
		console.log(pagination, filters, sorter);
		setTableParams({
			pagination,
			filters,
			sorter
		});
		let alertLevelFilter = '';
		let alertTargetFilter = '';
		let alertTimeSorter = '';
		let alertReceiveTimeSorter = '';
		if (JSON.stringify(filters) !== '{}') {
			alertLevelFilter = filters.level;
			alertTargetFilter = filters.aliasName;
		}
		if (sorter.field === 'alertTime') {
			alertTimeSorter = sorter.order
				? sorter.order === 'ascend'
					? 'asc'
					: 'desc'
				: '';
		}
		if (sorter.field === 'alertReceiveTime') {
			alertReceiveTimeSorter = sorter.order
				? sorter.order === 'ascend'
					? 'asc'
					: 'desc'
				: '';
		}
		if (type === 'cluster') {
			const sendData: AlarmRecordSendData = {
				current: pagination.current || 1,
				size: pagination.pageSize || 10,
				alertType: type,
				alertTarget: alertTargetFilter ? alertTargetFilter[0] : '',
				alertLevel: alertLevelFilter ? alertLevelFilter[0] : '',
				alertTime: alertTimeSorter,
				receiveTime: alertReceiveTimeSorter,
				clusterId: selectedTags[0] === 'all' ? '' : selectedTags[0],
				keyword: keyword
			};
			getRecordData(sendData);
		} else {
			const sendData: AlarmRecordSendData = {
				current: pagination.current || 1,
				size: pagination.pageSize || 10,
				alertType: type,
				alertTarget: alertTargetFilter ? alertTargetFilter[0] : '',
				alertLevel: alertLevelFilter ? alertLevelFilter[0] : '',
				alertTime: alertTimeSorter,
				receiveTime: alertReceiveTimeSorter,
				middlewareType:
					selectedTags[0] === 'all' ? '' : selectedTags[0],
				keyword: keyword
			};
			getRecordData(sendData);
		}
	};
	return (
		<>
			<div className="flex-space-between mb-8">
				<div>
					{selectedRowKeys.length ? (
						<Button
							type="primary"
							danger
							className="mr-16"
							onClick={() => {
								Modal.confirm({
									title: '操作确认',
									content: '是否确认删除当前所选的告警记录?',
									okText: '确定',
									cancelText: '取消',
									onOk: () => {
										deleteRecords({
											ids: selectedRowKeys
										}).then((res) => {
											if (res.success) {
												notification.success({
													message: '成功',
													description: '删除成功'
												});
												onSearch(keyword);
												getAlarmRecordList();
												setSelectedRowKeys([]);
											} else {
												notification.success({
													message: '失败',
													description: (
														<>
															<p>
																{res.errorMsg}
															</p>
															<p>
																{
																	res.errorDetail
																}
															</p>
														</>
													)
												});
											}
										});
									}
								});
							}}
						>
							删除选中
						</Button>
					) : null}
					<Input.Search
						style={{ width: '260px' }}
						placeholder="请输入告警信息搜索"
						value={keyword}
						onChange={(e) => setKeyword(e.target.value)}
						onSearch={onSearch}
						allowClear
					/>
				</div>
				{/* <Button
					type="default"
					icon={<ReloadOutlined />}
					onClick={() => {
						onSearch(keyword);
						getAlarmRecordList();
					}}
				/> */}
			</div>
			{type !== 'system' && (
				<Spin spinning={spinning}>
					<div className="checkable-tag-content">
						{recordIndex.map((item) => (
							<CheckableTag
								key={item.value}
								checked={selectedTags.indexOf(item.value) > -1}
								onChange={(checked) =>
									handleChange(item.value, checked)
								}
							>
								{item.label}({item.count})
							</CheckableTag>
						))}
					</div>
				</Spin>
			)}
			<Table
				rowKey="id"
				dataSource={records}
				loading={loading}
				pagination={paginationParams.pagination}
				columns={columnRender()}
				onChange={handleTableChange}
				rowSelection={{
					selectedRowKeys,
					onChange(selectedRowKeys, selectedRows, info) {
						setSelectedRowKeys(selectedRowKeys);
					}
				}}
			/>
		</>
	);
}

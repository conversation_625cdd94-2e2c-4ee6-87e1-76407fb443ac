.checkable-tag-content {
	width: 100%;
	height: auto;
	margin-bottom: @margin-sm;
	padding: @padding-sm @padding;
	background-color: #f8f8f9;
}
.contacts-content {
	width: 100%;
	height: auto;
	max-height: 160px;
	margin: @margin-sm 0px;
	padding: @padding;
	overflow-y: auto;
	border: 1px @border-color solid;
}
#alarmCollapse {
	.ant-collapse-borderless {
		background-color: @white;
	}
	.ant-collapse-borderless > .ant-collapse-item > .ant-collapse-content {
		background-color: @white;
	}
	.site-collapse-custom-collapse {
		.site-collapse-custom-panel {
			margin-top: 12px;
			overflow: hidden;
			background: #f7f7f7;
			border: 0px;
			border-radius: 2px;
		}
	}
}
.import-contact-from-role-content,
.import-contact-from-user-content {
	width: 500px;
	height: auto;
	max-height: 500px;
	border: 1px solid @border-color;
	padding: @padding;
}
.import-contact-from-role-content {
	.role-list-content {
		height: 430px;
		overflow-y: auto;
		.role-list-item {
			border: 1px solid @border-color;
			margin-bottom: 8px;
			background-color: #f7f7f7;
			display: flex;
			justify-content: space-between;
			padding: @padding;
			align-items: center;
			cursor: pointer;
		}
	}
}
.role-content-user-title {
	width: 100%;
	height: auto;
	margin-bottom: @margin;
	font-weight: @font-weight-lg;
	font-size: @font-3;
}

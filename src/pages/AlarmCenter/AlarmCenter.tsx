import { ProHeader, ProPage, ProContent } from '@/components/ProPage';
import { Ta<PERSON>, Button } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import AlarmRecordTable from './AlarmRecordTable';
import { useHistory, useParams } from 'react-router';
import RuleCenter from './RuleCenter';
import { debounce } from '@/utils/utils';
import { connect } from 'react-redux';
import { StoreState } from '@/types';
interface paramsProps {
	type: string;
}
function AlarmCenter({ buttonList }: { buttonList: any[] }): JSX.Element {
	const params: paramsProps = useParams();
	const history = useHistory();
	const [refresh, setRefresh] = useState<boolean>(false);
	const [activeKey, setActiveKey] = useState<string>('system');
	const [items, setItems] = useState<any[]>([]);

	useEffect(() => {
		if (buttonList?.length) {
			const list = [
				{
					label: `系统告警`,
					key: 'system',
					code: 'alarmCenterSystem',
					children: (
						<AlarmRecordTable type="system" refresh={refresh} />
					)
				},
				{
					label: `集群告警`,
					key: 'cluster',
					code: 'alarmCenterCluster',
					children: (
						<AlarmRecordTable type="cluster" refresh={refresh} />
					)
				},
				// {
				// 	label: `服务告警`,
				// 	key: 'service',
				// 	children: <AlarmRecordTable type="service" />
				// },
				{
					label: '规则中心',
					key: 'ruleCenter',
					code: 'alarmCenterRole',
					children: <RuleCenter />
				}
			];
			const menu = list.filter((item: any) =>
				buttonList.find((btn: MenuResItem) => btn.name === item.code)
			);
			setItems(menu);
		}
	}, [buttonList]);

	useEffect(() => {
		setActiveKey(params.type);
	}, [params.type]);
	const onChange = (key: string) => {
		setActiveKey(key);
		history.push(`/platform/alarmCenter/${key}`);
	};
	return (
		<ProPage>
			<ProHeader
				title="告警中心"
				subTitle="中间件平台的整合告警中心"
				extra={
					<Button
						onClick={debounce(() => setRefresh(!refresh))}
						id="detailRefresh"
						icon={<ReloadOutlined id="detailRefresh" />}
					/>
				}
			/>
			<ProContent>
				<Tabs onChange={onChange} activeKey={activeKey} items={items} />
			</ProContent>
		</ProPage>
	);
}

export default connect((state: StoreState) => ({
	buttonList: state.auth.buttonList
}))(AlarmCenter);

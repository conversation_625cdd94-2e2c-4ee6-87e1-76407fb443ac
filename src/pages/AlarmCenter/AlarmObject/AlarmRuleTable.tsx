import { getAlarmTargetRule } from '@/services/alarm';
import { Table, notification } from 'antd';
import React, { useEffect, useState } from 'react';
import { AlarmTargetItem } from '../alarm';
import { alarmWarn, silences } from '@/utils/const';
import moment from 'moment';
import { nullRender } from '@/utils/utils';

const columns: any = [
	{
		title: '告警名称',
		dataIndex: 'name',
		key: 'name',
		ellipsis: true,
		width: 200
	},
	{
		title: '告警规则',
		dataIndex: 'expr',
		key: 'expr',
		ellipsis: true,
		width: 320
	},
	{
		title: '触发时间',
		dataIndex: 'time',
		key: 'time',
		ellipsis: true,
		width: 80
	},
	{
		title: '告警等级',
		dataIndex: 'level',
		key: 'level',
		render: (value: string) => {
			return (
				<span className={value + ' level'}>
					{value && alarmWarn.find((item) => item.value === value)
						? alarmWarn.find((item) => item.value === value).text
						: ''}
				</span>
			);
		},
		filters: alarmWarn,
		onFilter: (value: string, record: any) => value === record.level,
		width: 100
	},
	{
		title: '告警间隔',
		dataIndex: 'silence',
		key: 'silence',
		ellipsis: true,
		filters: silences,
		filterMultiple: false,
		render: nullRender,
		onFilter: (value: any, record: any) => value === record.silence,
		width: 120
	},
	{
		title: '创建时间',
		dataIndex: 'createTime',
		key: 'createTime',
		ellipsis: true,
		sorter: (a: any, b: any) =>
			moment(a.createTime).unix() - moment(b.createTime).unix(),
		width: 150
	}
];

export default function AlarmRuleTable(props: AlarmTargetItem): JSX.Element {
	const { clusterId, namespace, name, prometheusRuleName, exist } = props;
	const [rules, setRules] = useState([]);
	const [loading, setLoading] = useState<boolean>(false);
	useEffect(() => {
		setLoading(true);
		getAlarmTargetRule({
			clusterId,
			namespace,
			prometheusRuleName,
			targetName: name
		})
			.then((res) => {
				if (res.success) {
					console.log(res);
					setRules(res.data);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	}, [exist]);
	return (
		<Table
			rowKey="name"
			dataSource={rules}
			loading={loading}
			columns={columns}
			pagination={false}
			scroll={{ x: 970 }}
		/>
	);
}

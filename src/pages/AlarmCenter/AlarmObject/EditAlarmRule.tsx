import { updateAlarmTargets } from '@/services/alarm';
import { clusterType } from '@/types';
import { formItemLayout618 } from '@/utils/const';
import pattern from '@/utils/pattern';
import { Col, Form, Input, Modal, Row, notification } from 'antd';
import React, { useEffect, useState } from 'react';

interface EditAlarmRuleProps {
	open: boolean;
	editData: any;
	onCancel: () => void;
	onRefresh: () => void;
	cluster?: clusterType;
}
export default function EditAlarmRule(props: EditAlarmRuleProps): JSX.Element {
	const { open, editData, onCancel, cluster, onRefresh } = props;
	const [form] = Form.useForm();
	const [loading, setLoading] = useState<boolean>(false);
	const [errorMsg, setErrorMsg] = useState<string>('');
	useEffect(() => {
		if (editData) {
			form.setFieldsValue({
				aliasName: editData.aliasName,
				namespace: editData.namespace,
				prometheusRuleName: editData.prometheusRuleName
			});
		}
	}, [editData]);
	const onOk = () => {
		form.validateFields().then((values) => {
			setLoading(true);
			const sendData = { ...values, clusterId: cluster?.id };
			if (editData) {
				sendData.name = editData.name;
			}
			updateAlarmTargets(sendData)
				.then((res) => {
					if (res.success) {
						onCancel();
						onRefresh();
						notification.success({
							message: '成功',
							description: `告警对象${
								editData ? '接入' : '新增'
							}成功`
						});
					} else {
						if (res.code === 650001) {
							setErrorMsg(res.errorMsg || res.errorDetail || '');
						} else {
							notification.error({
								message: '错误',
								description: (
									<>
										<p>{res.errorMsg}</p>
										<p>{res.errorDetail}</p>
									</>
								)
							});
						}
					}
				})
				.finally(() => {
					setLoading(false);
				});
		});
	};
	const onFieldsChange = () => {
		setErrorMsg('');
	};
	return (
		<Modal
			open={open}
			title={editData ? '接入' : '新增'}
			width={600}
			onCancel={onCancel}
			onOk={onOk}
			okButtonProps={{ loading: loading }}
		>
			<Form
				colon={false}
				form={form}
				labelAlign="left"
				onFieldsChange={onFieldsChange}
				{...formItemLayout618}
			>
				<Form.Item
					label="告警对象名称"
					name="aliasName"
					rules={[
						{ required: true, message: '告警对象名称不能为空' },
						{
							pattern: new RegExp(pattern.alarmRuleName),
							message:
								'请输入由中文、大小字母、数字和特殊字符“_.-”组成且长度不超过32个字符的对象名称'
						}
					]}
				>
					<Input
						disabled={!!editData}
						placeholder="请输入告警对象名称"
					/>
				</Form.Item>
				<Form.Item
					label="命名空间"
					name="namespace"
					rules={[
						{ required: true, message: '请输入命名空间' },
						{
							pattern: new RegExp(pattern.namespace),
							message:
								'命名空间是由小写字母数字及“-”组成，且以小写字母开头和结尾，不能以“-”结尾的2-40个字符'
						}
					]}
				>
					<Input placeholder="请输入命名空间" />
				</Form.Item>
				<Form.Item
					label="告警规则文件"
					name="prometheusRuleName"
					tooltip="该文件为Prometheus告警配置文件，对应资源类型PrometheusRules"
					rules={[
						{ required: true, message: '请输入告警规则文件名称' },
						{
							pattern: new RegExp(pattern.alarmRuleFileName),
							message:
								'请输入由小写字母数字及“-.”组成，且以小写字母开头、非“-.”结尾的2-64个字符'
						}
					]}
				>
					<Input placeholder="请输入告警规则文件名称" />
				</Form.Item>
				{errorMsg && (
					<Row style={{ marginTop: '-20px' }}>
						<Col span={6}></Col>
						<Col>
							<p className="red-name">{errorMsg}</p>
						</Col>
					</Row>
				)}
			</Form>
		</Modal>
	);
}

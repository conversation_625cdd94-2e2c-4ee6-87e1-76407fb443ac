import React, { useEffect, useState } from 'react';
import { Button, Collapse, Empty, Space, Spin, notification } from 'antd';
import { MinusCircleFilled } from '@ant-design/icons';
import AlarmRuleTable from './AlarmRuleTable';
import { clusterType } from '@/types';
import EditAlarmRule from './EditAlarmRule';
import { getAlarmTargets } from '@/services/alarm';
import { AlarmTargetItem } from '../alarm';
import '../index.less';
const { Panel } = Collapse;
interface AlarmObjectProps {
	cluster?: clusterType;
}
export default function AlarmObject(props: AlarmObjectProps): JSX.Element {
	const { cluster } = props;
	const [alarmObjects, setAlarmObjects] = useState<AlarmTargetItem[]>([]);
	const [spinning, setSpinning] = useState<boolean>(false);
	const [open, setOpen] = useState<boolean>(false);
	const [editData, setEditData] = useState<AlarmTargetItem>();
	const [activeKey, setActiveKey] = useState<string>('');
	useEffect(() => {
		getData();
	}, [cluster]);
	const getData = () => {
		if (cluster) {
			setSpinning(true);
			getAlarmTargets({ clusterId: cluster.id })
				.then((res) => {
					if (res.success) {
						console.log(res);
						setAlarmObjects(res.data);
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				})
				.finally(() => {
					setSpinning(false);
				});
		}
	};
	const genExtra = (record: any) => {
		return (
			<Space>
				<MinusCircleFilled style={{ color: '#fa8c16' }} /> 未接入
				<span
					className="name-link"
					onClick={() => {
						setEditData(record);
						setOpen(true);
					}}
				>
					接入
				</span>
			</Space>
		);
	};
	return (
		<div className="mt-16">
			<h2>告警对象</h2>
			<Button
				type="primary"
				onClick={() => {
					setEditData(undefined);
					setOpen(true);
				}}
			>
				新增
			</Button>
			{alarmObjects.length === 0 && (
				<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
			)}
			<div id="alarmCollapse">
				<Spin spinning={spinning}>
					<Collapse
						accordion
						bordered={false}
						className="site-collapse-custom-collapse"
						destroyInactivePanel={true}
						activeKey={activeKey}
						onChange={(key: string | string[]) => {
							console.log(key);
							if (!key) {
								setActiveKey(key);
							}
							if (
								alarmObjects.find(
									(item: AlarmTargetItem) => item.name === key
								)?.exist
							) {
								setActiveKey(key as string);
							}
						}}
					>
						{alarmObjects.length > 0 &&
							alarmObjects.map((item: AlarmTargetItem) => {
								return (
									<Panel
										className="site-collapse-custom-panel"
										header={item.aliasName}
										key={item.name}
										extra={
											!item.exist
												? genExtra(item)
												: undefined
										}
									>
										<AlarmRuleTable {...item} />
									</Panel>
								);
							})}
					</Collapse>
				</Spin>
			</div>
			{open && (
				<EditAlarmRule
					open={open}
					onCancel={() => setOpen(false)}
					editData={editData}
					cluster={cluster}
					onRefresh={getData}
				/>
			)}
		</div>
	);
}

import React, { useEffect, useState } from 'react';
import { Space, Input, notification, Spin } from 'antd';
import { ArrowLeftOutlined, RightOutlined } from '@ant-design/icons';
import UserContacts from './UserContacts';
import { getRoleList } from '@/services/role';
import { roleProps } from '@/pages/RoleManage/role';
import { getAlarmContacts } from '@/services/alarm';
import { useParams } from 'react-router';
import { paramsProps } from './AddAlarmContacts';
import { AlarmContactItem } from '../alarm';
import '../index.less';

interface RoleContactsProps {
	onChange: (values: AlarmContactItem[] | null) => void;
}
export default function RoleContacts(props: RoleContactsProps): JSX.Element {
	const { onChange } = props;
	const params: paramsProps = useParams();
	const [currentRole, setCurrentRole] = useState<any>();
	const [step, setStep] = useState<number>(1);
	const [roles, setRoles] = useState<roleProps[]>([]);
	const [dataSource, setDataSource] = useState<roleProps[]>([]);
	const [keywords, setKeywords] = useState<string>('');
	const [contacts, setContacts] = useState<AlarmContactItem[]>([]);
	const [loading, setLoading] = useState<boolean>(false);
	const [spinning, setSpinning] = useState<boolean>(false);
	useEffect(() => {
		setSpinning(true);
		getRoleList({ key: '' })
			.then((res) => {
				if (res.success) {
					setRoles(res.data);
					setDataSource(res.data);
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			})
			.finally(() => {
				setSpinning(false);
			});
	}, []);
	const roleCheck = (record: any) => {
		setCurrentRole(record);
		setLoading(true);
		getAlarmContacts({
			allocatable: true,
			clusterId: params.clusterId,
			roleId: record.id
		})
			.then((res) => {
				if (res.success) {
					setContacts(res.data);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
		setStep(2);
	};
	const handleSearch = (value: string) => {
		const list = roles.filter((item: roleProps) =>
			item.name?.includes(value)
		);
		setDataSource(list);
	};
	return (
		<div className="import-contact-from-role-content">
			{step === 1 && (
				<Space direction="vertical" style={{ width: '100%' }}>
					<Input.Search
						placeholder="请输入角色名称搜索"
						value={keywords}
						allowClear
						onSearch={handleSearch}
						onChange={(e) => setKeywords(e.target.value)}
					/>
					<Spin spinning={spinning}>
						<div className="role-list-content">
							{dataSource.map((item: roleProps) => {
								return (
									<div
										key={item.id}
										className="role-list-item"
										onClick={() => roleCheck(item)}
									>
										<span>{item.name}</span>
										<RightOutlined />
									</div>
								);
							})}
						</div>
					</Spin>
				</Space>
			)}
			{step === 2 && (
				<>
					<div className="role-content-user-title">
						<ArrowLeftOutlined
							onClick={() => {
								onChange([]);
								setStep(1);
							}}
						/>
						<span className="ml-12">{currentRole.name}</span>
					</div>
					<UserContacts
						loading={loading}
						users={contacts}
						onChange={onChange}
					/>
				</>
			)}
		</div>
	);
}

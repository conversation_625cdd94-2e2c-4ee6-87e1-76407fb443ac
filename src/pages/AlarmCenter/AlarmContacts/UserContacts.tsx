import { Input, Space, Table } from 'antd';
import React, { useEffect, useState } from 'react';
import { AlarmContactItem } from '../alarm';
const columns = [
	{
		title: '登录账户',
		dataIndex: 'username',
		width: 150,
		render: (val: string) => val || '/'
	},
	{
		title: '用户名',
		dataIndex: 'aliasName',
		width: 150,
		render: (val: string) => val || '/'
	},
	{
		title: '邮箱',
		dataIndex: 'mail',
		width: 180,
		render: (val: string) => val || '/'
	},
	{
		title: '手机号',
		dataIndex: 'phone',
		width: 150,
		render: (val: string) => val || '/'
	},
	{
		title: '创建时间',
		dataIndex: 'createTime',
		width: 200,
		render: (val: string) => val || '/'
	}
];
interface UserContactsProps {
	users: AlarmContactItem[];
	onChange: (values: AlarmContactItem[] | null) => void;
	loading: boolean;
}
export default function UserContacts(props: UserContactsProps): JSX.Element {
	const { users, onChange, loading } = props;
	const [dataSource, setDataSource] = useState<AlarmContactItem[]>([]);
	const [keyword, setKeyword] = useState<string>('');

	useEffect(() => {
		setDataSource(users);
	}, [users]);
	const handleSearch = (value: string) => {
		const list = users.filter(
			(item: AlarmContactItem) =>
				item.aliasName?.includes(value) ||
				item.username?.includes(value) ||
				item.mail?.includes(value) ||
				item.phone?.includes(value)
		);
		setDataSource(list);
	};
	const rowSelection = {
		onChange: (
			selectedRowKeys: React.Key[],
			selectedRows: AlarmContactItem[]
		) => {
			console.log(
				`selectedRowKeys: ${selectedRowKeys}`,
				'selectedRows: ',
				selectedRows
			);
			onChange(selectedRows);
		}
		// getCheckboxProps: (record: any) => ({
		// 	disabled: record.name === 'Disabled User', // Column configuration not to be checked
		// 	name: record.name
		// })
	};
	return (
		<Space direction="vertical" style={{ width: '100%' }}>
			<Input.Search
				placeholder="请输入登录账户、用户名、邮箱、手机号进行搜索"
				value={keyword}
				allowClear
				onChange={(e) => setKeyword(e.target.value)}
				onSearch={handleSearch}
			/>
			<Table
				rowKey="username"
				rowSelection={{
					type: 'checkbox',
					...rowSelection
				}}
				loading={loading}
				scroll={{ x: 830, y: 380 }}
				columns={columns}
				dataSource={dataSource}
				pagination={false}
			/>
		</Space>
	);
}

import { IconFont } from '@/components/IconFont';
import { Space, Tag, Tooltip } from 'antd';
import React from 'react';
import { AlarmContactItem } from '../alarm';
interface ContactItemProps {
	contact: AlarmContactItem;
	disabled?: boolean | undefined;
	onClose: (e: React.MouseEvent<HTMLElement>) => void;
}
export default function ContactItem(props: ContactItemProps): JSX.Element {
	const { contact, onClose, disabled } = props;
	return (
		<Tag
			closable={!disabled}
			onClose={(e: React.MouseEvent<HTMLElement>) => onClose(e)}
		>
			<Space align="center">
				<span>{contact.username}</span>
				<Tooltip title="已开启邮箱通知">
					<IconFont
						type="icon-email-1"
						style={{
							color: 'green',
							marginTop: 6,
							cursor: 'pointer'
						}}
					/>
				</Tooltip>
			</Space>
		</Tag>
	);
}

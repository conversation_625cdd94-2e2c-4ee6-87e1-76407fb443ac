import { <PERSON><PERSON><PERSON><PERSON>, ProHeader, ProPage } from '@/components/ProPage';
import {
	Button,
	Divider,
	Form,
	Radio,
	RadioChangeEvent,
	Space,
	Switch,
	notification
} from 'antd';
import React, { useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router';
import RoleContacts from './RoleContacts';
import UserContacts from './UserContacts';
import { addAlarmContacts, getAlarmContacts } from '@/services/alarm';
import { AlarmContactItem } from '../alarm';
import '../index.less';

export interface paramsProps {
	clusterId: string;
	type: string;
}
export default function AddAlarmContacts(): JSX.Element {
	const history = useHistory();
	const params: paramsProps = useParams();
	const [type, setType] = useState<string>('user');
	const [contacts, setContacts] = useState<AlarmContactItem[]>([]);
	const [loading, setLoading] = useState<boolean>(false);
	const [spinning, setSpinning] = useState<boolean>(false);
	const [checkedContacts, setCheckedContacts] = useState<
		AlarmContactItem[] | null
	>([]);
	const [switchChecked, setSwitchChecked] = useState<boolean>(false);
	useEffect(() => {
		setSpinning(true);
		getAlarmContacts({
			allocatable: true,
			clusterId: params.clusterId
		})
			.then((res) => {
				if (res.success) {
					setContacts(res.data);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setSpinning(false);
			});
	}, []);
	const handleChange = (values: AlarmContactItem[] | null) => {
		console.log(values);
		if (values && values.length > 0) {
			setSwitchChecked(true);
		} else {
			setSwitchChecked(false);
		}
		setCheckedContacts(values);
	};
	const handleSubmit = () => {
		if (checkedContacts && checkedContacts.length > 0) {
			setLoading(true);
			addAlarmContacts({
				clusterId: params.clusterId,
				alertUserDtoList: checkedContacts
			})
				.then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '告警联系人添加成功！'
						});
						history.push('/platform/alarmCenter/ruleCenter');
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				})
				.finally(() => {
					setLoading(false);
				});
		} else {
			notification.error({
				message: '错误',
				description: '请选择告警联系人'
			});
		}
	};
	return (
		<ProPage>
			<ProHeader title="告警联系人新增" onBack={() => history.goBack()} />
			<ProContent>
				<Form
					labelWrap
					wrapperCol={{ flex: 1 }}
					labelCol={{ flex: '200px' }}
					colon={false}
				>
					<Form.Item labelAlign="left" label="用户选择" required>
						<Radio.Group
							name="radiogroup"
							defaultValue="user"
							value={type}
							onChange={(e: RadioChangeEvent) => {
								setCheckedContacts([]);
								setSwitchChecked(false);
								setType(e.target.value);
							}}
						>
							<Radio value="user">按用户导入</Radio>
							<Radio value="role">按角色导入</Radio>
						</Radio.Group>
					</Form.Item>
					<Form.Item label=" ">
						{type === 'user' && (
							<div className="import-contact-from-user-content">
								<UserContacts
									users={contacts}
									onChange={handleChange}
									loading={spinning}
								/>
							</div>
						)}
						{type === 'role' && (
							<RoleContacts onChange={handleChange} />
						)}
					</Form.Item>
					<Form.Item
						labelAlign="left"
						label="邮箱告警通知"
						required
						valuePropName="checked"
					>
						<Switch
							checked={switchChecked}
							disabled={
								!checkedContacts ||
								(checkedContacts &&
									checkedContacts.length === 0)
							}
							onChange={(checked: boolean) =>
								setSwitchChecked(checked)
							}
						/>
					</Form.Item>
				</Form>
				<Divider />
				<Space>
					<Button
						loading={loading}
						type="primary"
						onClick={handleSubmit}
						disabled={
							spinning ||
							!checkedContacts ||
							(checkedContacts && checkedContacts.length === 0) ||
							!switchChecked
						}
					>
						确认
					</Button>
					<Button onClick={() => history.goBack()}>取消</Button>
				</Space>
			</ProContent>
		</ProPage>
	);
}

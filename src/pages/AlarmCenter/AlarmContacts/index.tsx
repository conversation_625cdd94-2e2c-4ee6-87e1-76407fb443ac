import { Button, Empty, Space, Spin, Modal, notification } from 'antd';
import React, { useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router';
import { clusterType } from '@/types';
import ContactItem from './ContactItem';
import { deleteAlarmContact, getAlarmContacts } from '@/services/alarm';
import { AlarmContactItem } from '../alarm';
import '../index.less';

const { confirm } = Modal;
interface AlarmContactsProps {
	cluster?: clusterType;
}
interface ParamsProps {
	type: string;
}
export default function AlarmContacts(props: AlarmContactsProps): JSX.Element {
	const { cluster } = props;
	const history = useHistory();
	const params: ParamsProps = useParams();
	const [contacts, setContacts] = useState<AlarmContactItem[]>([]);
	const [spinning, setSpinning] = useState<boolean>(false);
	useEffect(() => {
		getData();
	}, [cluster]);
	const getData = () => {
		if (cluster) {
			setSpinning(true);
			getAlarmContacts({
				allocatable: false,
				clusterId: cluster.id
			})
				.then((res) => {
					if (res.success) {
						setContacts(res.data);
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				})
				.finally(() => {
					setSpinning(false);
				});
		}
	};
	const onClose = (
		e: React.MouseEvent<HTMLElement>,
		record: AlarmContactItem
	) => {
		e.preventDefault();
		confirm({
			title: '操作确认',
			content: '请确认是否删除该告警联系人？',
			onOk: () => {
				if (cluster) {
					deleteAlarmContact({
						clusterId: cluster.id,
						username: record.username
					}).then((res) => {
						if (res.success) {
							getData();
							notification.success({
								message: '成功',
								description: '告警联系人删除成功！'
							});
						} else {
							notification.error({
								message: '错误',
								description: (
									<>
										<p>{res.errorMsg}</p>
										<p>{res.errorDetail}</p>
									</>
								)
							});
						}
					});
				} else {
					notification.error({
						message: '错误',
						description: '未找到集群ID'
					});
				}
			},
			onCancel: () => {
				e.preventDefault();
			}
		});
	};
	return (
		<div className="mt-16">
			<h2>告警联系人</h2>
			<Button
				type="primary"
				onClick={() => {
					history.push(
						`/platform/alarmCenter/${params.type}/addContacts/${cluster?.id}`
					);
				}}
			>
				新增
			</Button>
			<div className="contacts-content">
				<Spin spinning={spinning}>
					{contacts.length === 0 && (
						<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
					)}
					{contacts.length > 0 && (
						<Space wrap>
							{contacts.map(
								(item: AlarmContactItem, index: number) => {
									return (
										<ContactItem
											key={index}
											contact={item}
											onClose={(
												e: React.MouseEvent<HTMLElement>
											) => onClose(e, item)}
										/>
									);
								}
							)}
						</Space>
					)}
				</Spin>
			</div>
		</div>
	);
}

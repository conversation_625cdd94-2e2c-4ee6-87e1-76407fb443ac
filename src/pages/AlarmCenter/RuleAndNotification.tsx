import { getClusters } from '@/services/common';
import { clusterType } from '@/types';
import { Button, Select, Spin, notification } from 'antd';
import React, { useEffect, useState } from 'react';
import AlarmContacts from './AlarmContacts';
import AlarmObject from './AlarmObject';

export default function RuleAndNotification(): JSX.Element {
	const [spinning, setSpinning] = useState<boolean>(false);
	const [clusters, setClusters] = useState<clusterType[]>([]);
	const [currentCluster, setCurrentCluster] = useState<clusterType>();
	useEffect(() => {
		setSpinning(true);
		getClusters({ key: '' })
			.then((res) => {
				if (res.success) {
					console.log(res);
					setClusters(res.data);
					setCurrentCluster(res.data[0]);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setSpinning(false);
			});
	}, []);
	const handleChange = (value: string) => {
		const cc = clusters.find((item: clusterType) => item.id === value);
		setCurrentCluster(cc);
	};
	return (
		<Spin spinning={spinning}>
			<h2>集群选择</h2>
			<Select
				style={{ width: '500px' }}
				value={currentCluster?.id}
				onChange={handleChange}
			>
				{clusters.map((item: clusterType) => {
					return (
						<Select.Option value={item.id} key={item.id}>
							{item.nickname}
						</Select.Option>
					);
				})}
			</Select>
			<AlarmContacts cluster={currentCluster} />
			<AlarmObject cluster={currentCluster} />
		</Spin>
	);
}

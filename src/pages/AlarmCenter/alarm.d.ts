import { resProps } from '@/types/comment';

export interface AlarmRecordSendData {
	alertLevel?: string;
	alertTarget?: string;
	alertTime?: string;
	alertType: string;
	clusterId?: string;
	current: number;
	keyword?: string;
	middlewareType?: string;
	receiveTime?: string;
	size: number;
}

export interface IndexItem {
	value: string;
	count: number;
	label: string;
}
export interface AlarmRecordIndexItem {
	clusterAliasName: string;
	clusterId: string;
	count: number;
	middlewareName: string;
	middlewareType: string;
	targetAliasName: string;
	targetName: string;
}

export interface AlarmRecordIndexRes extends resProps {
	data: AlarmRecordIndexItem[];
}

export interface AlarmTargetItem {
	alertType: string;
	aliasName: string;
	clusterId: string;
	exist: boolean;
	name: string;
	namespace: string;
	prometheusRuleName: string;
}
export interface AlarmTargetItemRes extends resProps {
	data: AlarmTargetItem[];
}
export interface AlarmContactItem {
	clusterId: string;
	createTime: string;
	mail: string;
	mailAlert: boolean;
	messageAlert: boolean;
	phone: string;
	username: string;
	aliasName: string;
}
export interface AlarmContactItemRes extends resProps {
	data: AlarmContactItem[];
}
export interface AlarmRecordItem {
	alert: string;
	alertId: string;
	alertReceiveTime: string;
	alertTime: string;
	aliasName: string;
	capitalType: string;
	chartVersion: string;
	clusterId: string;
	content: string;
	expr: string;
	lay: string;
	level: string;
	message: string;
	name: string;
	namespace: string;
	nickname: string;
	organId: string;
	projectId: string;
	summary: string;
	type: string;
}

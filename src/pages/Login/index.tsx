import React, { useState, useEffect } from 'react';
import { useHistory } from 'react-router';
import { Modal, Button, Input, Form, notification, Space, Spin } from 'antd';
import {
	ExclamationCircleOutlined,
	UserOutlined,
	LockOutlined,
	LinkOutlined,
	RedoOutlined,
	ArrowLeftOutlined
} from '@ant-design/icons';
import { FormattedMessage } from 'react-intl';
import JSEncrypt from 'jsencrypt';

import EditPasswordForm from '@/layouts/Navbar/User/EditPasswordForm';
import { IconFont } from '@/components/IconFont';

import {
	postLogin,
	getRsaKey,
	getUserInformation,
	getVerifyCode,
	getWeChatConfig,
	loginWeChat
} from '@/services/user';
import { getFeatureApi, getIsAccessGYT } from '@/services/common';
import { getPersonalConfig } from '@/services/user';

import storage from '@/utils/storage';
import { loginProps } from './login.d';
import { User } from '@/types';
import { base64ToImageSrc } from '@/utils/utils';
import { TOKEN } from '@/services/request';

import './index.less';

export default function Login(): JSX.Element {
	const history = useHistory();
	const [account, setAccount] = useState({
		username: '',
		password: '',
		imgcode: ''
	});
	const [message, setMessage] = useState<string>('');
	const [publicKey, setPublicKey] = useState<string>('');
	const [visible, setVisible] = useState<boolean>(false);
	const [editVisible, setEditVisible] = useState<boolean>(false);
	const [userName, setUserName] = useState<string>('');
	const [rePassword, setRePassword] = useState<number>(0);
	const [passwordCode, setPasswordCode] = useState<number>();
	const [loading, setLoading] = useState<boolean>(false);
	const [role, setRole] = useState<User>();
	const [data, setData] = useState<loginProps>();
	const [verityCode, setVerityCode] = useState<VerifyCode>();
	const [verityCodeLoading, setVerityCodeLoading] = useState<boolean>(false);
	const [loginMode, setLoginMode] = useState<'normal' | 'wechat'>('normal');
	const [WeChatConfig, setWeChatConfig] = useState<WeChatConfig>();
	// * 企业微信是否开启的判断
	const [WeChatAccessCloseAPI, setWeChatAccessCloseAPI] =
		useState<boolean>(true);
	// * 验证码是否开启的判断
	const [verificationCodeCloseAPI, setVerificationCodeCloseAPI] =
		useState<boolean>(true);
	useEffect(() => {
		getPersonalConfig().then((res) => {
			setData(res.data);
			storage.setLocal('personalization', res.data);
			document.title =
				res.data && res.data.title ? res.data.title : 'Zeus';
			change_icon(res.data.tabLogo);
		});
		getFeatureApi().then((res) => {
			if (res.success) {
				const wechat_access_api_temp =
					res.data.find(
						(item: any) => item.name === 'weChatAccessClose'
					)?.enabled ?? true;
				setWeChatAccessCloseAPI(wechat_access_api_temp);
				const verification_code_close_temp =
					res.data.find(
						(item: any) => item.name === 'verificationCodeClose'
					)?.enabled ?? true;
				setVerificationCodeCloseAPI(verification_code_close_temp);
				storage.setLocal('featureAPI', res.data);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
		getIsAccessGYT().then((res) => {
			if (res.success) {
				storage.setLocal('isAccessGYT', res.data);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
		getRsaKey().then((res) => {
			if (res.success) {
				const pub = `-----BEGIN PUBLIC KEY-----${res.data}-----END PUBLIC KEY-----`;
				storage.setSession('rsa', pub);
				setPublicKey(pub);
			}
		});
	}, []);
	useEffect(() => {
		if (!verificationCodeCloseAPI) {
			getVerifyCodeString();
		}
		if (!WeChatAccessCloseAPI) {
			getWeChatConfigInfo();
		}
	}, [WeChatAccessCloseAPI, verificationCodeCloseAPI]);
	useEffect(() => {
		if (WeChatConfig) {
			const redirect_uri_temp = `http://${window.location.host}/wx/login`;
			// const redirect_uri_temp = `http://************/login`;
			const temp = new window.WwLogin({
				id: 'qr-code-container',
				host: WeChatConfig.address,
				appid: WeChatConfig.corpId,
				agentid: WeChatConfig.agentId,
				redirect_uri: encodeURIComponent(redirect_uri_temp),
				myLoad: myLoad
			});
		}
	}, [WeChatConfig]);
	const myLoad = (event: any) => {
		console.log('my load', event);
		const wechat_code = event.data.split('code=')[1]?.split('&')?.[0];
		console.log(wechat_code);
		loginWeChat({ code: wechat_code }).then((res) => {
			if (res.success) {
				if (res.success) {
					storage.setLocal('firstAlert', 0);
					storage.setLocal(TOKEN, res.data.token);
					getUserInfo().then((role) => {
						storage.setLocal('role', JSON.stringify(role));
						if (
							res.data.passwordRemindCode === -1 ||
							res.data.passwordRemindCode === 0
						) {
							setVisible(true);
							setPasswordCode(res.data.passwordRemindCode);
							setRePassword(res.data.passwordUsedDay);
							setUserName(res.data.userName);
							return;
						}
						jumpToPlatform(role);
					});
				} else {
					if (res.code === 400027) {
						Modal.confirm({
							title: '操作确认',
							content: res.errorDetail || res.errorMsg
						});
					}
				}
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	const getWeChatConfigInfo = () => {
		getWeChatConfig().then((res) => {
			if (res.success) {
				setWeChatConfig(res.data);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	};
	const getVerifyCodeString = () => {
		setVerityCodeLoading(true);
		getVerifyCode()
			.then((res) => {
				if (res.success) {
					setVerityCode(res.data);
				}
			})
			.finally(() => {
				setVerityCodeLoading(false);
			});
	};
	// * 公钥加密 ---- 修改密码也要加密，目前暂时没有做，可将此方法作为公共方法提取出来
	function encrypt(text: string) {
		const encrypt = new JSEncrypt();
		encrypt.setPublicKey(publicKey);
		const encrypted = encrypt.encrypt(text);
		return encrypted;
	}

	function change_icon(iconUrl: string) {
		const changeFavicon = (link: any) => {
			let $favicon: any = document.querySelector('link[rel="icon"]');
			if ($favicon !== null) {
				$favicon.href = link;
			} else {
				$favicon = document.createElement('link');
				$favicon.rel = 'icon';
				$favicon.href = link;
				document.head.appendChild($favicon);
			}
		};

		// 动态修改网站图标
		changeFavicon(iconUrl);
	}

	const submit = (e: any) => {
		e && e.preventDefault();
		setMessage('');
		const { username, password, imgcode } = account;
		if (!username || !password) {
			setMessage('请输入用户名/密码');
			return;
		}
		if (!verificationCodeCloseAPI) {
			if (!imgcode) {
				setMessage('验证码不能为空，请重新输入');
				return;
			}
		}
		// 对输入密码进行rsa加密
		const rsaPass = encrypt(password);

		let data: any = {
			userName: username,
			password: rsaPass,
			language: storage.getLocal('language') || 'ch'
		};
		if (!verificationCodeCloseAPI) {
			data = {
				...data,
				sig: verityCode?.sig,
				imgcode: imgcode
			};
		}
		setLoading(true);
		postLogin(data)
			.then((res) => {
				if (res.success) {
					storage.setLocal('firstAlert', 0);
					storage.setLocal(TOKEN, res.data.token);
					getUserInfo().then((role) => {
						storage.setLocal('role', JSON.stringify(role));
						if (
							res.data.passwordRemindCode === -1 ||
							res.data.passwordRemindCode === 0
						) {
							setVisible(true);
							setPasswordCode(res.data.passwordRemindCode);
							setRePassword(res.data.passwordUsedDay);
							setUserName(res.data.userName);
							return;
						}
						jumpToPlatform(role);
					});
				} else {
					if (res.code === 400027) {
						Modal.confirm({
							title: '操作确认',
							content: res.errorDetail || res.errorMsg
						});
					} else {
						setMessage(res.errorMsg || res.errorDetail);
						getVerifyCodeString();
					}
				}
			})
			.catch((err) => {
				setMessage(err.data);
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const onOk = () => {
		setVisible(false);
		setEditVisible(true);
	};
	const getUserInfo = async () => {
		const res: { aliasName?: string; [propsName: string]: any } =
			await getUserInformation();
		if (res.success) {
			setRole(res.data);
			return res.data;
		} else {
			notification.error({
				message: '失败',
				description: res.errorMsg
			});
			return null;
		}
	};
	const onCancel = () => {
		setVisible(false);
		jumpToPlatform();
	};
	const jumpToPlatform = (data?: any) => {
		const curRole = data || role;
		if (curRole?.isAdmin) {
			history.push('/');
			window.location.reload();
			return;
		} else if (
			// * 仅组织管理员
			curRole?.userRoleList.every((item: any) => item.weight === 2)
		) {
			storage.setSession('organId', curRole?.userRoleList[0].organId);
			history.push('/organization/overview');
			window.location.reload();
			return;
		} else if (
			curRole?.userRoleList.some((item: any) => item.weight === 3)
		) {
			// * 存在项目管理员
			history.push('/');
			window.location.reload();
			return;
		} else if (
			curRole?.userRoleList.every(
				(item: any) => item.roleType === 'manager'
			)
		) {
			// * 仅有 管理类型 角色的用户
			history.push('/platform');
			window.location.reload();
			return;
		} else {
			history.push('/');
			window.location.reload();
			return;
		}
	};
	return (
		<div
			className="login"
			style={{
				background: `transparent url(${
					data && data.backgroundImage
				}) no-repeat center center /cover`
			}}
		>
			<div className="header">
				<img className="logo" src={data && data.loginLogo} />
				<span className="info">
					{(data && data.platformName) ||
						'Zeus | 中间件管理一体化平台'}
				</span>
			</div>
			<div className="slogan">
				{(data && data.slogan) || '我是Slogan，让IT更美好'}
			</div>
			{loginMode === 'normal' && (
				<div
					className="login-form"
					style={{
						paddingBottom: WeChatAccessCloseAPI ? '50px' : '20px'
					}}
				>
					<header className="login-header">中间件平台登录</header>
					<div className="login-form-box">
						<Form className="form" layout="vertical">
							<Form.Item label="登录账户">
								<Input
									addonBefore={<UserOutlined />}
									placeholder="请输入登录账户"
									value={account.username}
									onChange={(e) =>
										setAccount({
											...account,
											username: e.target.value
										})
									}
									onKeyPress={(event) => {
										if (event.charCode === 13) {
											submit(event);
										}
									}}
								/>
							</Form.Item>
							<Form.Item
								label={<FormattedMessage id="password" />}
							>
								<Input.Password
									addonBefore={<LockOutlined />}
									placeholder="请输入密码"
									value={account.password}
									onChange={(e) =>
										setAccount({
											...account,
											password: e.target.value
										})
									}
									onKeyPress={(event) => {
										if (event.charCode === 13) {
											submit(event);
										}
									}}
								/>
							</Form.Item>
							{!verificationCodeCloseAPI && (
								<Form.Item
									label={
										<FormattedMessage id="verification_code" />
									}
								>
									<Space>
										<Input
											addonBefore={<LinkOutlined />}
											placeholder="请输入验证码"
											value={account.imgcode}
											onChange={(e) =>
												setAccount({
													...account,
													imgcode: e.target.value
												})
											}
											onKeyPress={(event) => {
												if (event.charCode === 13) {
													submit(event);
												}
											}}
										/>
										<Spin spinning={verityCodeLoading}>
											{!verityCode && (
												<div className="verify-code-no-data">
													暂无数据
												</div>
											)}
											{verityCode &&
												verityCode.imageStr && (
													<img
														src={base64ToImageSrc(
															verityCode.imageStr
														)}
														style={{
															width: '80px',
															height: '30px'
														}}
													/>
												)}
										</Spin>
										<RedoOutlined
											onClick={getVerifyCodeString}
										/>
									</Space>
								</Form.Item>
							)}
						</Form>
						<div className={`login-submit centered-item`}>
							<p className="login-message">{message}</p>
						</div>
						<div className="login-submit">
							<Button
								block
								loading={loading}
								type="primary"
								onClick={(e) => submit(e)}
							>
								登录
							</Button>
						</div>
					</div>
					{!WeChatAccessCloseAPI && (
						<div className="login-footer">
							<Space>
								<span>第三方账号登录</span>
								<IconFont
									type="icon-wechat"
									style={{
										color: '#429445',
										fontSize: 14,
										cursor: 'pointer'
									}}
									onClick={() => {
										setLoginMode('wechat');
									}}
								/>
							</Space>
						</div>
					)}
				</div>
			)}
			{!WeChatAccessCloseAPI && (
				<div
					className="login-form"
					style={{
						opacity: loginMode === 'wechat' ? 1 : 0,
						height: loginMode === 'wechat' ? '544px' : '1px',
						zIndex: loginMode === 'wechat' ? 1 : -1000
					}}
				>
					<header className="login-header">中间件平台登录</header>
					<Button
						type="text"
						icon={<ArrowLeftOutlined />}
						onClick={() => {
							setLoginMode('normal');
						}}
					>
						返回
					</Button>
					<div
						id="qr-code-container"
						style={{ textAlign: 'center' }}
					></div>
				</div>
			)}
			<Modal
				open={visible}
				onCancel={() => setVisible(false)}
				title="改密提示"
				footer={
					<div>
						<Button
							type="primary"
							onClick={onOk}
							style={{ marginRight: '16px' }}
						>
							现在就改
						</Button>
						{!passwordCode ? (
							<Button onClick={onCancel}>下次再说</Button>
						) : null}
					</div>
				}
			>
				<div style={{ display: 'flex', alignItems: 'flex-start' }}>
					<ExclamationCircleOutlined
						style={{
							fontSize: 20,
							color: '#faa700',
							marginRight: 8
						}}
					/>
					<div>
						<div style={{ lineHeight: '24px' }}>
							您的密码已经过期，为保障您的账户资产安全，请定期更改密码
							{passwordCode === -1 ? '' : '，是否现在修改密码？'}
						</div>
					</div>
				</div>
			</Modal>
			{editVisible && (
				<EditPasswordForm
					visible={editVisible}
					onCancel={() => setEditVisible(false)}
					userName={userName}
				/>
			)}
			<div className="copy">
				{(data && data.copyrightNotice) ||
					'Copyeight © 2021 杭州谐云科技有限公司 All rights reserved.Copyeight.'}
			</div>
		</div>
	);
}

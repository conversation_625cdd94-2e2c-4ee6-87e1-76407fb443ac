import React from 'react';
import ClusterResourcesOverview from '../PlatformManagementOverview/ClusterResourcesOverview';
import AgentManagement from '../PlatformManagementOverview/AgentManagement';
import StorageService from '../PlatformManagementOverview/StorageService';
import BackupService from '../PlatformManagementOverview/BackupService';
import OrganAndProjectInfo from '../PlatformManagementOverview/OrganAndProjectInfo';
import AlertRecord from '../PlatformManagementOverview/AlertRecord';
import ServiceInfo from '../PlatformManagementOverview/ServiceInfo';

import './index.less';

export default function OverviewPage(): JSX.Element {
	return (
		<div className="project-overview">
			<ServiceInfo />
			<div className="display-flex">
				<div className="overview-left">
					<ClusterResourcesOverview isProject />
					<AgentManagement isProject />
					<StorageService isProject />
				</div>
				<div className="overview-right">
					<OrganAndProjectInfo isProject />
					<BackupService isProject />
					<AlertRecord isProject />
				</div>
			</div>
		</div>
	);
}

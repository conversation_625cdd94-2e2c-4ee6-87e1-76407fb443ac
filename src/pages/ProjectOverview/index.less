.project-overview {
	padding: 16px;
	.overview-left {
		width: 60%;
		margin-right: 16px;
	}
	.overview-right {
		width: calc(40% - 16px);
	}
	.top-card,
	.center-card,
	.bottom-card {
		display: flex;
		flex-direction: column;
		height: 450px;
		box-shadow: 0px 2px 16px rgba(0, 0, 0, 0.12);
		border-radius: 4px;

		.header {
			margin-left: 12px;
			margin-right: 15px;
			margin-top: 10px;
			display: flex;
			justify-content: space-between;
			h3 {
				color: #333;
				font-family: PingFang SC;
				font-size: 14px;
				font-style: normal;
				font-weight: 600;
				height: 20px;
				letter-spacing: 0.01em;
				line-height: 20px;
			}
			.refresh {
				cursor: pointer;
				display: flex;
				height: 17px;
				margin-right: 12px;
			}
		}

		.upper-part {
			display: flex;
			flex-direction: column;
			height: 225px;
			border-bottom: 1px solid rgba(51, 51, 51, 0.05);
			margin-left: 12px;

			.line {
				width: 1px;
				height: 132px;
				align-self: center;
				border-right: 1px solid rgba(51, 51, 51, 0.05);
			}
		}

		//用量排行
		.usage-ranking {
			height: 100%;
			margin: 16px 0;
			overflow-y: auto;
			.header {
				margin-right: 12px;
				justify-content: space-between;

				h3 {
					font-family: 'PingFang SC';
					font-style: normal;
					font-weight: 500;
					font-size: 12px;
					line-height: 17px;
					/* identical to box height */
					letter-spacing: 0.01em;
					color: #333333;
				}

				.label {
					display: flex;
					font-family: 'PingFang SC';
					font-style: normal;
					font-weight: 500;
					font-size: 12px;
					line-height: 17px;
					/* identical to box height */
					letter-spacing: 0.01em;
					color: #333333;

					.tab-wrap {
						display: inline-block;

						.tab {
							display: inline-block;
							border-right: 1px solid rgba(51, 51, 51, 0.1);
							margin-right: 10px;
							&:last-child {
								border-right: unset;
							}
							padding-right: 10px;
							cursor: pointer;
						}

						.tab.active {
							font-family: 'PingFang SC';
							font-style: normal;
							font-weight: 600;
							font-size: 12px;
							line-height: 17px;
							/* identical to box height */
							letter-spacing: 0.01em;
							color: #226ee6;
						}
					}
				}
			}

			.content {
				display: none;
			}

			.content.active {
				display: flex;
				flex-wrap: wrap;
				flex-direction: column;
				margin-top: 4px;
				margin-left: 7px;
				margin-right: 7px;
				height: 40px;

				.item {
					display: flex;
					height: 37px;
					margin: 0 5px;
					margin-top: 6px;
					background: #fafafa;
					border-radius: 2px;

					.ranking1 {
						width: 37px;
						height: 37px;
						background: #226ee6;
						border-radius: 2px;
						font-family: 'D-DIN';
						font-style: normal;
						font-weight: 700;
						font-size: 16px;
						text-align: center;
						line-height: 37px;
						color: #ffffff;
					}

					.ranking2 {
						width: 37px;
						height: 37px;
						background: #4c92f4;
						border-radius: 2px;
						font-family: 'D-DIN';
						font-style: normal;
						font-weight: 700;
						font-size: 16px;
						text-align: center;
						line-height: 37px;
						color: #ffffff;
					}

					.ranking3 {
						width: 37px;
						height: 37px;
						background: #77b5fe;
						border-radius: 2px;
						font-family: 'D-DIN';
						font-style: normal;
						font-weight: 700;
						font-size: 16px;
						text-align: center;
						line-height: 37px;
						color: #ffffff;
					}

					.ranking {
						width: 37px;
						height: 37px;
						background: #f5f5f5;
						border-radius: 2px;
						font-family: 'D-DIN';
						font-style: normal;
						font-weight: 700;
						font-size: 16px;
						text-align: center;
						line-height: 37px;
						color: rgba(51, 51, 51, 0.6);
					}

					p {
						align-self: center;
						font-family: 'PingFang SC';
						font-style: normal;
						font-weight: 500;
						font-size: 12px;
						line-height: 17px;
						/* identical to box height */
						letter-spacing: 0.01em;
						color: #333333;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}

					.iCnt {
						display: flex;
						justify-content: space-between;
						width: calc(100% - 37px);
					}
					.item-name {
						display: flex;
						height: 37px;
						margin-left: 18px;
						font-weight: 600;
						width: 30%;

						//超过长度隐藏
						p {
							width: calc(100% - 12px);
							font-weight: 600;
						}
					}

					.item-content {
						display: flex;
						height: 37px;
						font-family: 'PingFang SC';
						font-style: normal;
						font-weight: 400;
						font-size: 12px;
						line-height: 37px;
						/* identical to box height */
						letter-spacing: 0.01em;
						color: rgba(51, 51, 51, 0.2);
						margin-left: 18px;
						min-width: 147px;
						justify-content: center;
						p {
							margin-right: 16px;
						}
						span {
							width: 67px;
							height: 37px;
						}
					}

					.ellipse {
						width: 6px;
						height: 6px;
						background: #226ee6;
						border: 1px solid #ffffff;
						box-shadow: 0px 2px 4px rgba(34, 110, 230, 0.2);
						border-radius: 50%;
						align-self: center;
						margin-right: 6px;
					}
				}
			}
		}
	}
	.center-card,
	.bottom-card {
		margin-top: 16px;
		overflow: hidden;
	}
	.system-info {
		text-align: center;
		.version {
			height: 28px;
			font-size: 20px;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: @font-weight;
			color: @primary-color;
			line-height: 28px;
			margin: 20px auto 10px;
		}
		& > div {
			line-height: 20px;
		}
		& > p {
			line-height: 18px;
		}
	}
	.page-right-buttom {
		display: flex;
		height: 120px;
		margin: 16px;
		background: linear-gradient(
			180deg,
			rgba(34, 110, 230, 0.09) 0%,
			rgba(255, 255, 255, 0) 100%
		);
		border-radius: 4px;
		flex-direction: column;

		.content {
			display: flex;
			justify-content: space-around;

			.line {
				align-self: end;
				border-right: 1px solid rgba(51, 51, 51, 0.05);
				height: 36px;
			}
			.content-item {
				width: 50%;
				padding: 0 10px;
				.divider {
					margin-bottom: unset;
					&::before {
						top: -5px;
						border-image: linear-gradient(
								to left,
								rgba(34, 110, 230, 0.15),
								rgba(34, 110, 230, 0)
							)
							1;
					}
					&::after {
						top: -5px;
						border-image: linear-gradient(
								to right,
								rgba(34, 110, 230, 0.15),
								rgba(34, 110, 230, 0)
							)
							1;
					}
				}

				.i-f {
					height: 46px;
					font-family: 'D-DIN';
					font-style: normal;
					font-weight: 700;
					font-size: 38px;
					text-align: center;
					background: linear-gradient(
						180deg,
						#3c9fff 21.74%,
						#226ee6 50.78%,
						#1351c2 80.43%
					);
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
					background-clip: text;
					text-fill-color: transparent;
					text-shadow: 0px 4px 4px rgba(51, 51, 51, 0.1);
				}
				.i-s {
					height: 46px;
					font-family: 'D-DIN';
					font-style: normal;
					font-weight: 700;
					font-size: 38px;
					// line-height: 32px;
					/* identical to box height, or 133% */
					text-align: center;
					color: #333333;
					text-shadow: 0px 4px 4px rgba(51, 51, 51, 0.1);
				}
			}

			.content-left {
				margin-left: 80px;
				align-self: end;

				font-family: 'PingFang SC';
				font-style: normal;
				font-weight: 500;
				font-size: 10px;
				// line-height: 14px;
				/* identical to box height */
				text-align: center;
				letter-spacing: 0.01em;
				color: #333333;

				h3 {
					height: 30px;

					font-family: 'D-DIN';
					font-style: normal;
					font-weight: 700;
					font-size: 24px;
					// line-height: 32px;
					/* identical to box height, or 133% */
					text-align: center;
					color: #333333;
					text-shadow: 0px 4px 4px rgba(51, 51, 51, 0.1);
				}
			}

			.content-center {
				align-self: end;

				font-family: 'PingFang SC';
				font-style: normal;
				font-weight: 500;
				font-size: 10px;
				// line-height: 14px;
				/* identical to box height */
				text-align: center;
				letter-spacing: 0.01em;
				color: #333333;

				h3 {
					height: 46px;
					font-family: 'D-DIN';
					font-style: normal;
					font-weight: 700;
					font-size: 38px;
					background: linear-gradient(
						180deg,
						#3c9fff 21.74%,
						#226ee6 50.78%,
						#1351c2 80.43%
					);
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
					background-clip: text;
					text-fill-color: transparent;
					text-shadow: 0px 4px 4px rgba(51, 51, 51, 0.1);
				}
			}

			.content-right {
				margin-right: 80px;
				align-self: end;

				font-family: 'PingFang SC';
				font-style: normal;
				font-weight: 500;
				font-size: 10px;
				// line-height: 14px;
				/* identical to box height */
				text-align: center;
				letter-spacing: 0.01em;
				color: #333333;

				h3 {
					height: 30px;
					font-family: 'D-DIN';
					font-style: normal;
					font-weight: 700;
					font-size: 24px;
					// line-height: 32px;
					/* identical to box height, or 133% */
					text-align: center;
					color: #333333;
					text-shadow: 0px 4px 4px rgba(51, 51, 51, 0.1);
				}
			}
		}

		.buttom {
			display: flex;
			justify-content: space-around;
			margin-top: 16px;
			h2 {
				height: 17px;
				font-family: 'PingFang SC';
				// font-style: normal;
				font-weight: 400;
				font-size: 12px;
				line-height: 17px;
				color: #333333;
			}

			p {
				height: 25px;
				font-family: 'D-DIN';
				font-style: normal;
				font-weight: 700;
				font-size: 20px;
				line-height: 22px;
				/* identical to box height */
			}

			//左环图
			.store-pie {
				display: flex !important;
				justify-content: space-around !important;
				width: 200px;

				p {
					color: #77b5fe;
					text-shadow: 0px 4px 4px rgba(162, 206, 255, 0.15);
				}

				.annotations {
					font-family: 'PingFang SC';
					font-style: normal;
					font-weight: 500;
					font-size: 10px;
					line-height: 14px;
					/* identical to box height */
					text-align: center;
					letter-spacing: 0.05em;
					color: #333333;
				}

				.pieLegend {
					p {
						color: #226ee6;
						text-shadow: 0px 4px 4px rgba(34, 110, 230, 0.15);
					}
				}
			}
			.pie-architecture {
				margin-top: unset;
				margin-bottom: 16px;
				display: flex !important;
				justify-content: center;
			}
			.pieInfo {
				display: flex;
				justify-content: space-around;
				flex-direction: column;
				margin-left: 40px;
				max-width: 100px;
			}
			.line {
				height: 92px;
				align-self: center;
				border-right: 1px solid rgba(51, 51, 51, 0.05);
			}

			.ant-carousel {
				width: 210px;
			}

			//轮播指示器
			.dots button {
				background: #226ee6 !important;
				color: #ffffff !important;
				width: 5px !important;
				height: 5px !important;
				border: none !important;
				border-radius: 50% !important;
			}

			// 轮播指示器
			.ant-carousel .slick-dots-bottom {
				bottom: 1px;
			}
		}
	}
	.control-container {
		height: calc(100% - 32px);
		margin-top: 10px;
		justify-content: space-between;
		.next-table td .next-table-cell-wrapper {
			overflow: hidden !important;
		}
	}
	.serve-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100%;
		overflow: auto;
		box-sizing: border-box;
		.info-item {
			min-width: 150px;
			padding: 16px 0 12px 0;
			text-align: center;
			flex: 1;
			position: relative;
			cursor: pointer;
			&:not(:last-of-type)::after {
				position: absolute;
				top: 40px;
				right: 0;
				display: block;
				content: '';
				width: 1px;
				height: 46px;
				background-color: #e3e4e6;
			}
			.info-img {
				width: 40px;
				height: 40px;
				position: relative;
				margin: 0 auto;
				.err-count {
					position: absolute;
					top: -9px;
					right: -9px;
					width: 18px;
					height: 18px;
					line-height: 14px;
					font-size: @font-1;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: @font-weight;
					color: @white;
					text-align: center;
					background: #c80000;
					border-radius: 50%;
					border: 2px solid @white;
				}
			}
			.info-name {
				height: 18px;
				font-size: @font-1;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: @font-weight-sm;
				color: #1a1a1a;
				line-height: @line-height-3;
			}
			.info-count {
				font-size: @font-1;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: @font-weight-sm;
				color: @text-color-title;
				line-height: @line-height-3;
				margin-top: 16px;
			}
		}
	}
}

import React, { useEffect, useState } from 'react';
import {
	<PERSON><PERSON>,
	But<PERSON>,
	Drawer,
	Input,
	Space,
	Table,
	notification,
	Modal
} from 'antd';
import { compareArrays, questionTooltipRender } from '@/utils/utils';
import { hideParams, updateHideParams } from '@/services/repository';
import { getConfigRole } from '@/services/middleware';
import { FiltersProps } from '@/types/comment';
const { confirm } = Modal;

export default function ParamsDrawer({
	open,
	onCancel,
	clusterId,
	type
}: {
	open: boolean;
	onCancel: () => void;
	clusterId: string;
	type: string;
}): JSX.Element {
	const [editFlag, setEditFlag] = useState<boolean>(false);
	const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
	const [selectedRows, setSelectedRows] = useState<ParamItem[]>([]);
	const [params, setParams] = useState<ParamItem[]>([]);
	const [dataSource, setDataSource] = useState<ParamItem[]>([]);
	const [keyword, setKeyword] = useState<string>();
	const [loading, setLoading] = useState<boolean>(false);
	const [buttonDisabled, setButtonDisabled] = useState<boolean>(true);
	const [filters, setFilters] = useState<FiltersProps[]>([]);
	useEffect(() => {
		getData();
	}, []);
	const getData = () => {
		setLoading(true);
		getConfigRole({
			clusterId,
			namespace: '*',
			middlewareName: '*',
			type
		}).then((res) => {
			if (res.success) {
				setFilters(
					res.data.map((item: string) => {
						return {
							value: item,
							text:
								item === 'major'
									? '服务节点'
									: item === 'proxy'
									? '代理节点'
									: `${item}节点`
						};
					})
				);
			}
		});
		hideParams({
			clusterId,
			type
		})
			.then((res) => {
				if (res.success) {
					setParams(res.data);
					setDataSource(
						res.data.filter((item) =>
							item.name.includes(keyword || '')
						)
					);
					const checked = res.data
						.filter((item) => item.hide)
						.map((item) => item.name);
					setSelectedRowKeys(checked);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const columns = [
		{
			title: '参数名',
			dataIndex: 'name',
			key: 'name'
		},
		{
			title: '所属节点类型',
			dataIndex: 'role',
			key: 'role',
			filters: filters,
			filterMultiple: false,
			onFilter: (value: any, record: any) => {
				if (value !== 'other') {
					return record.role === value;
				} else {
					return record.role !== 'major' && record.role !== 'proxy';
				}
			},
			render: (value: string) =>
				value === 'proxy'
					? '代理节点'
					: value === 'major'
					? '服务节点'
					: '其他'
		},
		{
			title: '参数描述',
			dataIndex: 'description',
			key: 'description',
			render: (value: string) => questionTooltipRender(value)
		}
	];
	const saveHideParams = () => {
		confirm({
			title: '操作确认',
			content:
				'保存后，勾选的参数将不会出现在对应中间件服务的参数设置列表中，是否确定保存？',
			onOk: async () => {
				const listTemp = selectedRows.map((item) => {
					return {
						name: item.name,
						role: item.role
					};
				});
				await updateHideParams({
					clusterId,
					type,
					middlewareParamsHideDoList: listTemp
				}).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '隐藏参数设置成功'
						});
						setEditFlag(false);
						setButtonDisabled(true);
						getData();
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			}
		});
	};
	const cancelHideParams = () => {
		confirm({
			title: '操作确认',
			content: (
				<>
					<p>取消保存后，当前已修改的参数选择配置将不会生效</p>
					<p>是否确定？</p>
				</>
			),
			onOk: () => {
				setEditFlag(false);
				setButtonDisabled(true);
				getData();
			}
		});
	};
	const rowSelection = {
		onChange: (
			selectedRowKeysTemp: React.Key[],
			selectedRows: ParamItem[]
		) => {
			const filtersData = dataSource.map((item) => item.name);
			const selectedTemp = selectedRowKeys.filter(
				(item) => !filtersData.includes(item as string)
			);
			const resultTemp = selectedRowKeysTemp.concat(selectedTemp);
			setSelectedRowKeys(resultTemp);
			const selectedRowTemp = params.filter((item) =>
				resultTemp.includes(item.name)
			);
			setSelectedRows(selectedRowTemp);
			const hideOrigin = params
				.filter((item) => item.hide)
				.map((item) => item.name);
			// console.log(compareArrays(hideOrigin, resultTemp));
			setButtonDisabled(compareArrays(hideOrigin, resultTemp));
		},
		getCheckboxProps: () => ({
			disabled: !editFlag
		})
	};
	const onSearch = (value: string) => {
		const temp = params.filter((item) => item.name.includes(value));
		setDataSource(temp);
	};
	return (
		<Drawer
			destroyOnClose={true}
			title="参数选配"
			placement="right"
			onClose={() => {
				if (editFlag) {
					confirm({
						title: '操作确认',
						content: (
							<>
								<p>
									取消保存后，当前已修改的参数选择配置将不会生效
								</p>
								<p>是否确定？</p>
							</>
						),
						onOk: () => {
							setEditFlag(false);
							setKeyword('');
							onCancel();
						}
					});
				} else {
					setKeyword('');
					onCancel();
				}
			}}
			open={open}
			width={600}
		>
			<Space>
				{!editFlag && (
					<Button type="primary" onClick={() => setEditFlag(true)}>
						编辑
					</Button>
				)}
				{editFlag && (
					<>
						<Button
							type="primary"
							disabled={buttonDisabled}
							onClick={saveHideParams}
						>
							保存
						</Button>
						<Button danger onClick={cancelHideParams}>
							取消
						</Button>
					</>
				)}
				<Input.Search
					placeholder="请输入搜索关键字"
					allowClear
					value={keyword}
					onChange={(e) => setKeyword(e.target.value)}
					onSearch={onSearch}
				/>
			</Space>
			<Alert
				message="勾选的参数将不会出现在对应中间件服务的参数设置列表中！"
				type="warning"
				showIcon
				closable
				style={{ margin: '8px 0' }}
			/>
			<Table<ParamItem>
				dataSource={dataSource}
				rowKey="name"
				columns={columns}
				loading={loading}
				rowSelection={{
					type: 'checkbox',
					selectedRowKeys: selectedRowKeys,
					...rowSelection
				}}
			/>
		</Drawer>
	);
}

import { Pro<PERSON>ontent, ProHeader, ProPage } from '@/components/ProPage';
import * as React from 'react';
import { useState, useEffect } from 'react';
import { useHistory } from 'react-router';
import {
	Menu,
	MenuProps,
	Button,
	Select,
	Input,
	Empty,
	Spin,
	notification
} from 'antd';
import UploadMiddlewareForm from '../ServiceCatalog/components/UploadMiddlewareForm';
import { SelectInfo } from '@/types/comment';
import MiddlewareItem from './MiddlewareItem';

import { getClusters, getComponents } from '@/services/common';
import { getGroups } from '@/services/repository';
import { clusterType } from '@/types/index';
import { middlewareProps } from '@/pages/MiddlewareRepository/middleware';
import MiddlewareImage from '@/assets/images/middleware.jpg';
import { IconFont } from '@/components/IconFont';

import './index.less';

export enum MiddlewareTypeIcon {
	db = 'icon-database',
	mq = 'icon-email-1',
	mse = 'icon-cangku',
	storage = 'icon-storage-manage',
	other = 'icon-qita',
	'null' = 'icon-email-1'
}

type MenuItem = Required<MenuProps>['items'][number];

function getItem(
	label: React.ReactNode,
	key: React.Key,
	icon?: React.ReactNode,
	children?: MenuItem[],
	type?: 'group'
): MenuItem {
	return {
		key,
		icon,
		children,
		label,
		type
	} as MenuItem;
}

const MiddlewareManage = (): JSX.Element => {
	const history = useHistory();
	const [visible, setVisible] = useState<boolean>(false);
	const [selectedKeys, setSelectedKeys] = useState<string[]>(['all']);
	const [clusterList, setClusterList] = useState<clusterType[]>([]);
	const [currentCluster, setCurrentCluster] = useState<clusterType>();
	const [data, setData] = useState<middlewareProps[]>([]);
	const [originData, setOriginData] = useState<middlewareProps[]>([]);
	const [keyword, setKeyword] = useState<string>();
	const [items, setItems] = useState<any>();
	const [groupData, setGroupData] = useState<any>();
	const [disabled, setDisabled] = useState(false);
	const [loading, setLoading] = useState<boolean>(false);

	useEffect(() => {
		setKeyword('');
		setSelectedKeys(['all']);
		getClusters().then((res) => {
			if (res.success) {
				setClusterList(res.data);
				setCurrentCluster(res.data[0]);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	}, [history.location.pathname]);

	useEffect(() => {
		if (currentCluster) {
			getData();
			getComponents({ clusterId: currentCluster?.id }).then((res) => {
				if (res.success) {
					const status = res.data.find(
						(item: any) =>
							item.component === 'middleware-controller'
					)?.status;
					setDisabled(status !== 3);
				}
			});
		}
	}, [currentCluster]);

	useEffect(() => {
		groupData && setSelectedKeys(['all']);
	}, [groupData, history.location.pathname]);

	useEffect(() => {
		if (selectedKeys[0] === 'all') {
			setOriginData(data);
		} else {
			setOriginData(
				groupData.find(
					(item: any) => item.groupCode === selectedKeys[0]
				)?.middlewareInfoDTOS || []
			);
		}
	}, [...selectedKeys]);

	const onChange = (value: string) => {
		const ct = clusterList.find((item) => item.id === value);
		setCurrentCluster(ct);
	};

	const onCreate = () => {
		setVisible(false);
		if (currentCluster) {
			getData();
		}
	};

	const getData = () => {
		setLoading(true);
		getGroups({
			clusterId: currentCluster?.id || '',
			filterUninstalled: false,
			keyword: ''
		}).then((res) => {
			if (res.success) {
				setLoading(false);
				const dataList: any[] = [];
				res.data.map((item: any) => {
					dataList.push(...item.middlewareInfoDTOS);
				});
				setData(dataList);
				setOriginData(dataList);
				setGroupData(res.data);
				let allNum = 0;
				res.data.map((item: any) => (allNum += item.num));
				const list = res.data.map((item: any) => {
					return getItem(
						`${item.groupName}(${item.num})`,
						item.groupCode,
						<IconFont
							type={MiddlewareTypeIcon[item.groupCode || 'null']}
						/>
					);
				});
				setItems(
					[
						getItem(
							`全部(${allNum})`,
							'all',
							<IconFont type="icon-liebiao" />
						)
					].concat(list)
				);
			}
		});
	};
	return (
		<div className="middleware-manage">
			<ProContent>
				<div
					className="middleware-head"
					style={{
						background: `url(${MiddlewareImage}) no-repeat center / cover`
					}}
				>
					<div className="middleware-title">中间件市场管理</div>
					<div className="middleware-subTitle">
						中间件市场集成多种多样的中间件应用以供选择使用，不仅包括内置中间件，还允许自主上传新中间件，统一纳管升级
					</div>
				</div>
				<div className="middleware-menu">
					<Menu
						mode="horizontal"
						items={items}
						selectedKeys={selectedKeys}
						onSelect={(info: SelectInfo) => {
							setKeyword('');
							setSelectedKeys([info.key]);
						}}
						className="middleware-menu-list"
					/>
				</div>
				<Spin spinning={loading}>
					<div className="middleware-header">
						<Button type="primary" onClick={() => setVisible(true)}>
							上架
						</Button>
						<label>集群：</label>
						<Select
							onChange={onChange}
							value={currentCluster?.id}
							dropdownMatchSelectWidth={false}
							style={{ marginRight: 16, width: 158 }}
						>
							{clusterList.map((item: clusterType) => {
								return (
									<Select.Option
										value={item.id}
										key={item.id}
									>
										{item.nickname}
									</Select.Option>
								);
							})}
						</Select>
						<Input.Search
							placeholder="请输入关键字搜索"
							style={{ width: 360 }}
							value={keyword}
							onChange={(e) => setKeyword(e.target.value)}
							onSearch={(value) => {
								setOriginData(
									selectedKeys[0] === 'all'
										? data.filter((item) =>
												item.chartName.includes(
													value.toLocaleLowerCase()
												)
										  )
										: data.filter(
												(item) =>
													item.chartName.includes(
														value.toLocaleLowerCase()
													) &&
													item.type ===
														selectedKeys[0]
										  )
								);
							}}
							allowClear
						/>
					</div>
					<div className="middleware-container">
						<div className="middleware-content">
							{originData.length ? (
								<div className="middleware-item-list">
									{originData.map((item: any) => {
										return (
											<MiddlewareItem
												key={item.id}
												{...item}
												disabled={disabled}
												isManage
												clusterId={currentCluster?.id}
											/>
										);
									})}
								</div>
							) : (
								<Empty
									image={Empty.PRESENTED_IMAGE_SIMPLE}
									style={{ marginTop: 100 }}
								/>
							)}
						</div>
					</div>
				</Spin>
				{visible && currentCluster && (
					<UploadMiddlewareForm
						visible={visible}
						clusterId={currentCluster.id}
						onCancel={() => setVisible(false)}
						onCreate={onCreate}
					/>
				)}
			</ProContent>
		</div>
	);
};

export default MiddlewareManage;

.middleware-manage {
	.middleware-head {
		height: 200px;
		padding: 52px 24px 0 24px;
		.middleware-title {
			color: #fff;
			font-family: <PERSON><PERSON><PERSON> P<PERSON>Hui<PERSON>i;
			font-size: 40px;
			font-style: normal;
			font-weight: 400;
			line-height: normal;
			margin-bottom: 15px;
		}
		.middleware-subTitle {
			// width: 387px;
			color: rgba(255, 255, 255, 0.85);
			font-family: PingFang SC;
			font-size: 14px;
			font-style: normal;
			font-weight: 400;
			line-height: normal;
		}
	}
	.middleware-container {
		// display: flex;
	}
	.middleware-header {
		display: flex;
		align-items: center;
		padding: 0 24px;
		& > .ant-btn {
			margin-right: 16px;
		}
		label {
			width: 60px;
		}
	}
	.middleware-content {
		width: 100%;
		padding: 16px 24px 24px;
		.middleware-item-list {
			display: grid;
			justify-content: space-between;
			grid-template-columns: repeat(auto-fill, 24%);
			grid-gap: 16px 8px;
		}
	}
	.middleware-menu {
		// width: 200px;
		.middleware-menu-title {
			color: rgba(0, 0, 0, 0.85);
			font-family: <PERSON>Fang SC;
			font-size: 12px;
			font-style: normal;
			font-weight: 500;
			line-height: 20px;
			margin: 0 0 12px 24px;
		}
		.middleware-menu-list {
			background: #f8f8f9;
			margin-bottom: 16px;
			.ant-menu-item:hover::after,
			.ant-menu-item-selected::after,
			.ant-menu-item::after {
				border-bottom: none;
			}
		}
		.ant-menu-overflow-item.ant-menu-item {
			padding: 0 24px;
		}
	}
}
.middleware-detail {
	.middleware-detail-header {
		height: 140px;
		padding: 16px 24px;
		.middleware-detail-back {
			display: inline-block;
			color: #fff;
			font-family: Roboto;
			font-size: 16px;
			font-style: normal;
			font-weight: 400;
			line-height: 26px;
			cursor: pointer;
			&:hover {
				color: #4c92f5;
			}
			& > .img {
				margin-right: 4px;
				transform: rotate(-90deg);
			}
		}
		.middleware-detail-info {
			margin-top: 8px;
			align-items: center;
			& > img {
				width: 56px;
				height: 56px;
				flex-shrink: 0;
				border-radius: 4px;
				background: #eff6ff;
				margin-right: 16px;
			}
			.middleware-detail-title {
				color: #fff;
				font-family: PingFang SC;
				font-size: 16px;
				font-style: normal;
				font-weight: 500;
				line-height: 20px;
			}
			.middleware-detail-description {
				color: rgba(255, 255, 255, 0.45);
				font-family: PingFang SC;
				font-size: 12px;
				font-style: normal;
				font-weight: 400;
				line-height: 18px;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
		.middleware-detail-buttons {
			display: flex;
			justify-content: flex-end;
			.ant-btn {
				margin-left: 16px;
			}
		}
	}
	.middleware-detail-content {
		padding: 16px 24px;
	}
	.ant-tree .ant-tree-node-content-wrapper:hover {
		background: none;
		.ant-tree-title {
			color: #fc0;
		}
	}
	.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
		background: none;
		.ant-tree-title {
			color: #fc0;
		}
	}
	.ant-tree-title {
		color: #fff;
		width: 100px;
		display: inline-block;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	.ant-descriptions-item-container {
		.ant-descriptions-item-content {
			align-items: center;
		}
	}
}

import * as React from 'react';
import { useState, useEffect } from 'react';
import { useHistory, useParams } from 'react-router';
import { api } from '@/api.json';
import { Button, Alert, notification, Modal, Tree } from 'antd';
import {
	ArrowUpOutlined,
	LoadingOutlined,
	CheckCircleFilled,
	MinusCircleFilled,
	ExclamationCircleFilled,
	ExclamationCircleOutlined
} from '@ant-design/icons';
import DataFields from '@/components/DataFields';
import { MiddlewareType } from '@/utils/enum';
import timerClass from '@/utils/timerClass';

import { UnControlled as CodeMirror } from 'react-codemirror2';
import 'codemirror/lib/codemirror.js';
import 'codemirror/lib/codemirror.css';
import 'codemirror/mode/css/css.js';
import 'codemirror/mode/yaml/yaml.js';
import 'codemirror/mode/yaml-frontmatter/yaml-frontmatter.js';
import 'codemirror/theme/twilight.css';
import 'codemirror/addon/selection/active-line';
import MiddlewareBackground from '@/assets/images/middlewareDetail.png';
import otherColor from '@/assets/images/nodata.svg';

import {
	getTypeVersion,
	chartMetadata,
	chartNames,
	operatorStatus,
	fileContent,
	unInstallMiddleware,
	shelvesTypeVersion
} from '@/services/repository';
import ParamsDrawer from './ParamsDrawer';
// * 固化的中间件
const fixedMiddleware = [
	'mysql',
	'redis',
	'postgresql',
	'kafka',
	'zookeeper',
	'elasticsearch',
	'rocketmq'
];
const info = {
	title: '基本信息',
	appName: '',
	categoryName: '',
	description: '',
	lastUpdateTime: '',
	owner: '',
	usageCount: '',
	vendor: '',
	questionYaml: {}
};

const MiddlewareItemDetail = (): JSX.Element => {
	const params: { name: string; clusterId: string; imagePath: string } =
		useParams();
	const { name, clusterId, imagePath } = params;
	const history = useHistory();
	const [timer, setTimer] = useState();
	const [basicData, setBasicData] = useState<any>(info);
	const [version, setVersion] = useState<string>('');
	const [versionList, setVersionList] = useState<any>([]);
	const [status, setStatus] = useState<boolean>(false);
	const [treeData, setTreeData] = useState<any>();
	const [treeValue, setTreeValue] = useState<any[]>([]);
	const [chartValue, setChartValue] = useState<string>('');
	const [installDisabled, setInstallDisabled] = useState<boolean>(false);
	const [uninstallLoading, setUninstallLoading] = useState<boolean>(false);
	const [open, setOpen] = useState<boolean>(false);
	const [infoConfig] = useState<any>([
		{
			dataIndex: 'title',
			render: (val: string) => (
				<div className="title-content">
					<div className="blue-line"></div>
					<div className="detail-title">{val}</div>
				</div>
			),
			span: 24
		},
		{
			dataIndex: 'version',
			label: '版本号',
			render: (val: string) => <div>{val}</div>
		},
		{
			dataIndex: 'appName',
			label: '中间件应用名称',
			render: (val: string) => (
				<div className="text-overflow-one" title={val}>
					{val || '/'}
				</div>
			)
		},
		{
			dataIndex: 'categoryName',
			label: '中间件类型',
			render: (val: string) => (
				<div className="text-overflow-one" title={val}>
					{`${MiddlewareType[val || 'null']}`}
				</div>
			)
		},
		{
			dataIndex: 'vendor',
			label: '供应商',
			render: (val: string) => (
				<div className="text-overflow-one" title={val}>
					{val}
				</div>
			)
		},
		{
			dataIndex: 'status',
			label: '当前控制器状态',
			render: (value: number) => {
				switch (value) {
					case 0:
						return (
							<>
								<LoadingOutlined
									style={{
										color: '#D1D5D9',
										marginRight: '6px',
										fontSize: '12px',
										verticalAlign: 'middle'
									}}
								/>
								安装中
							</>
						);
					case 1:
						return (
							<>
								<CheckCircleFilled
									style={{
										color: '#1DC11D',
										marginRight: '6px',
										fontSize: '12px',
										verticalAlign: 'middle'
									}}
								/>
								运行正常
							</>
						);
					case 2:
						return (
							<>
								<MinusCircleFilled
									style={{
										color: '#d7d7d7',
										marginRight: '6px',
										fontSize: '12px',
										verticalAlign: 'middle'
									}}
								/>
								待安装
							</>
						);
					case 3:
						return (
							<>
								<ExclamationCircleOutlined
									style={{
										color: '#D93026',
										marginRight: '6px',
										fontSize: '12px',
										verticalAlign: 'middle'
									}}
								/>
								不可用
							</>
						);
					case 4:
						return (
							<>
								<ExclamationCircleFilled
									style={{
										color: '#faad14',
										marginRight: '6px',
										fontSize: '12px',
										verticalAlign: 'middle'
									}}
								/>
								运行异常
							</>
						);
					default:
						return '/';
				}
			}
		},
		{
			dataIndex: 'usageCount',
			label: '使用量',
			render: (val: string) => (
				<div className="text-overflow-one" title={val}>
					{val || 0}
				</div>
			)
		},
		{
			dataIndex: 'description',
			label: '中间件介绍',
			render: (val: string) => (
				<div className="text-overflow-one" title={val}>
					{val || '/'}
				</div>
			)
		},
		{
			dataIndex: 'lastUpdateTime',
			label: '最近更新时间',
			render: (val: string) => (
				<div className="text-overflow-one" title={val}>
					{val || '/'}
				</div>
			)
		}
	]);

	function editObj(array: any): any {
		return array.map((item: any) => {
			return {
				children: item.children.length ? editObj(item.children) : [],
				key: item.currentDirectory,
				title: item.fileName
			};
		});
	}

	useEffect(() => {
		getTypeVersions();

		return () => {
			clearInterval(timer);
		};
	}, []);

	useEffect(() => {
		if (version) {
			getData();
			getChartNames();
		}
	}, [version]);

	useEffect(() => {
		version && treeValue[0] && getFileContent();
	}, [version, treeValue]);

	const getTypeVersions = () => {
		getTypeVersion({ clusterId, type: name }).then((res) => {
			if (res.success) {
				setVersion(
					res.data.find((item: any) => item.versionStatus === 'now')
						?.chartVersion || res.data[0]?.chartVersion
				);
				setVersionList(
					res.data.map((item: any) => {
						return {
							label: item.chartVersion,
							value: item.chartVersion
						};
					})
				);
			}
		});
	};

	const getChartNames = () => {
		chartNames({
			clusterId,
			type: name.toLocaleLowerCase(),
			chartVersion: version
		}).then((res) => {
			if (res.success) {
				if (!res.data) return;
				setTreeValue([res.data?.children[0].currentDirectory]);
				setTreeData(
					[res.data].map((item) => {
						return {
							children: item.children.length
								? editObj(item.children)
								: [],
							key: item.currentDirectory,
							title: item.fileName
						};
					})
				);
			}
		});
	};

	const getFileContent = () => {
		fileContent({
			clusterId,
			type: name.toLocaleLowerCase(),
			chartVersion: version,
			fileName: treeValue[0]
		}).then((res) => {
			if (res.success) {
				setChartValue(res.data);
			}
		});
	};

	const getData = () => {
		chartMetadata({
			clusterId,
			type: name.toLocaleLowerCase(),
			chartVersion: version
		}).then((result) => {
			if (result.success) {
				operatorStatus({
					clusterId,
					type: name.toLocaleLowerCase()
				}).then((res) => {
					if (res.success) {
						setStatus(res.data[0].installed);
						setInstallDisabled(res.data[0].version !== version);
						setBasicData({
							...basicData,
							status: res.data[0].status
						});
						setBasicData({
							title: '基本信息',
							version,
							...result.data,
							status: res.data[0].status
						});
					}
				});
			}
		});
	};

	const unInstall = () => {
		const sendData = {
			chartName: name.toLocaleLowerCase(),
			chartVersion: version,
			clusterId
		};
		Modal.confirm({
			title: '操作确认',
			content: '请确认是否卸载该中间件？',
			okText: '确认',
			cancelText: '取消',
			onOk: () => {
				setUninstallLoading(true);
				unInstallMiddleware(sendData).then((res) => {
					setUninstallLoading(false);
					if (res.success) {
						notification.success({
							message: '成功',
							description: '中间件卸载成功，3秒后刷新数据'
						});
						onRefresh();
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			}
		});
	};

	const shelves = () => {
		Modal.confirm({
			title: '操作确认',
			content: '是否确认下架该版本中间件？',
			okText: '确认',
			cancelText: '取消',
			onOk: () => {
				return shelvesTypeVersion({
					chartName: name.toLocaleLowerCase(),
					chartVersion: version
				}).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '已下架该版本'
						});
						if (versionList.length === 1) {
							history.goBack();
						} else {
							getData();
							getChartNames();
							getTypeVersions();
						}
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			}
		});
	};

	const onRefresh = () => {
		setTimer(
			timerClass.countdownTimer(() => {
				getData();
				getChartNames();
				getTypeVersions();
			}, 3)
		);
	};

	return (
		<div className="middleware-detail">
			<div
				className="middleware-detail-header"
				style={{
					background: `url(${MiddlewareBackground}) lightgray 50% / cover no-repeat`,
					mixBlendMode: 'multiply'
				}}
			>
				<div
					className="middleware-detail-back"
					onClick={() => history.goBack()}
				>
					<ArrowUpOutlined className="img" style={{ fontSize: 16 }} />
					<span>返回</span>
				</div>
				<div className="flex-space-between">
					<div className="middleware-detail-info display-flex">
						<img
							className="middleware-img"
							src={
								imagePath && imagePath !== 'null'
									? `${api}/images/middleware/${imagePath}`
									: otherColor
							}
							alt={name}
						/>
						<div>
							<div className="middleware-detail-title">
								{name}
							</div>
							<div
								className="middleware-detail-description"
								title={basicData.description}
							>
								{basicData.description}
							</div>
						</div>
					</div>
					{history.location.pathname.includes(
						'/platform/marketManagement'
					) ? (
						<div className="middleware-detail-buttons">
							<Button
								type="primary"
								disabled={uninstallLoading}
								onClick={() =>
									history.push(
										`/platform/marketManagement/versionManagement/${name.toLocaleLowerCase()}/${clusterId}`
									)
								}
							>
								版本管理
							</Button>
							{fixedMiddleware.includes(
								name.toLocaleLowerCase()
							) &&
								basicData.status !== 2 && (
									<Button onClick={() => setOpen(true)}>
										参数选配
									</Button>
								)}
							{!fixedMiddleware.includes(
								name.toLocaleLowerCase()
							) &&
								basicData.questionYaml?.capabilities?.includes(
									'config'
								) &&
								basicData.status !== 2 && (
									<Button onClick={() => setOpen(true)}>
										参数选配
									</Button>
								)}
							{status && !installDisabled ? (
								<Button
									danger
									type="primary"
									onClick={unInstall}
									loading={uninstallLoading}
								>
									卸载
								</Button>
							) : (
								<Button danger type="primary" onClick={shelves}>
									下架
								</Button>
							)}
						</div>
					) : null}
				</div>
			</div>
			<div className="middleware-detail-content">
				{status ? null : (
					<Alert
						closable
						type="warning"
						showIcon
						style={{ marginBottom: 16 }}
						message="当前中间件应用暂未安装控制器，请先前往版本管理页安装对应版本控制器"
					/>
				)}
				<div>
					<div>
						<DataFields items={infoConfig} dataSource={basicData} />
					</div>
					<div>
						<h2 className="mt-16">Helm Chart</h2>
						<div className="display-flex">
							{treeData && (
								<Tree
									treeData={treeData}
									style={{
										width: '200px',
										height: '674px',
										overflow: 'auto',
										background: 'rgb(28,28,28)'
									}}
									selectedKeys={treeValue}
									defaultExpandedKeys={treeValue}
									onSelect={(value) => setTreeValue(value)}
								/>
							)}
							<div style={{ width: '100%' }}>
								<div className="yaml-check-title border-radius-none">
									Chart文件
								</div>
								<div
									id="yaml-check-codemirror"
									style={{ height: '500px' }}
								>
									<CodeMirror
										value={chartValue}
										options={{
											mode: 'yaml',
											theme: 'twilight',
											lineNumbers: true,
											readOnly: true,
											lineWrapping: true
										}}
									/>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			{open && (
				<ParamsDrawer
					open={open}
					onCancel={() => setOpen(false)}
					clusterId={clusterId}
					type={name.toLocaleLowerCase()}
				/>
			)}
		</div>
	);
};

export default MiddlewareItemDetail;

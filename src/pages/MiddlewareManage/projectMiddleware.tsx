import React, { useEffect, useState } from 'react';
import { ProContent } from '@/components/ProPage';
import { Menu, MenuProps, Input, Empty, Spin, notification, Modal } from 'antd';
import MiddlewareImage from '@/assets/images/middleware.jpg';
import MiddlewareItem from './MiddlewareItem';
import { middlewareProps } from '../MiddlewareRepository/middleware';
import { SelectInfo } from '@/types/comment';
import { getProjectMarket } from '@/services/project';
import storage from '@/utils/storage';
import { IconFont } from '@/components/IconFont';
import { changeMiddlewareEnable } from '@/services/repository';

const { confirm } = Modal;
export enum MiddlewareTypeIcon {
	db = 'icon-database',
	mq = 'icon-email-1',
	mse = 'icon-cangku',
	storage = 'icon-storage-manage',
	other = 'icon-qita',
	'null' = 'icon-email-1'
}

type MenuItem = Required<MenuProps>['items'][number];

function getItem(
	label: React.ReactNode,
	key: React.Key,
	icon?: React.ReactNode,
	children?: MenuItem[],
	type?: 'group'
): MenuItem {
	return {
		key,
		icon,
		children,
		label,
		type
	} as MenuItem;
}

export default function ProjectMiddleware(): JSX.Element {
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const [originData, setOriginData] = useState<middlewareProps[]>([]);
	const [groupData, setGroupData] = useState<any>();
	const [data, setData] = useState<middlewareProps[]>([]);
	const [keyword, setKeyword] = useState<string>('');
	const [items, setItems] = useState<any>();
	const [selectedKeys, setSelectedKeys] = useState<string[]>(['all']);
	const [loading, setLoading] = useState<boolean>(false);
	useEffect(() => {
		let mounted = true;
		if (organId && projectId) {
			if (mounted) {
				getData('');
			}
		}
		return () => {
			mounted = false;
		};
	}, [organId, projectId]);

	useEffect(() => {
		groupData && setSelectedKeys(['all']);
	}, [groupData]);

	useEffect(() => {
		if (selectedKeys[0] === 'all') {
			setOriginData(data);
		} else {
			setOriginData(
				groupData.find(
					(item: any) => item.groupCode === selectedKeys[0]
				)?.middlewareInfoDTOS || []
			);
		}
	}, [...selectedKeys]);
	const getData = (keyword: string) => {
		setLoading(true);
		setOriginData([]);
		getProjectMarket({
			organId,
			projectId,
			keyword: keyword,
			withMiddleware: true
		})
			.then((res) => {
				if (res.success) {
					const dataList: any[] = [];
					res.data.map((item: any) => {
						dataList.push(...item.middlewareInfoDTOS);
					});
					setData(dataList);
					setOriginData(dataList);
					setGroupData(res.data);
					let allNum = 0;
					res.data.map((item: any) => (allNum += item.num));
					const list = res.data.map((item: any) => {
						return getItem(
							`${item.groupName}(${item.num})`,
							item.groupCode,
							<IconFont
								type={
									MiddlewareTypeIcon[item.groupCode || 'null']
								}
							/>
						);
					});
					setItems(
						[
							getItem(
								`全部(${allNum})`,
								'all',
								<IconFont type="icon-liebiao" />
							)
						].concat(list)
					);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const onEnableChange = (record: any, value: boolean) => {
		confirm({
			title: '操作确认',
			content: value
				? '勾选后该项目将拥有该中间件的管理权限，请谨慎操作！'
				: '取消勾选后该项目将失去该中间件的管理权限，请谨慎操作！',
			onOk: () => {
				return changeMiddlewareEnable({
					organId,
					projectId,
					middlewareType: record.chartName,
					enable: value
				}).then((res) => {
					if (res.success) {
						notification.success({
							message: '成功',
							description: '中间件添加成功'
						});
						window.location.reload();
					} else {
						notification.error({
							message: '错误',
							description: (
								<>
									<p>{res.errorMsg}</p>
									<p>{res.errorDetail}</p>
								</>
							)
						});
					}
				});
			},
			onCancel: () => {
				return false;
			}
		});
	};
	return (
		<div className="middleware-manage">
			<ProContent>
				<div
					className="middleware-head"
					style={{
						background: `url(${MiddlewareImage}) no-repeat center / cover`
					}}
				>
					<div className="middleware-title">中间件市场</div>
					<div className="middleware-subTitle">
						中间件市场集成多种多样的中间件应用以供选择使用
					</div>
				</div>
				<div className="middleware-menu">
					<Menu
						mode="horizontal"
						items={items}
						selectedKeys={selectedKeys}
						onSelect={(info: SelectInfo) => {
							setKeyword('');
							setSelectedKeys([info.key]);
						}}
						className="middleware-menu-list"
					/>
				</div>
				<Spin spinning={loading}>
					<div className="middleware-header">
						<Input.Search
							placeholder="请输入关键字搜索"
							style={{ width: 360 }}
							value={keyword}
							onChange={(e) => setKeyword(e.target.value)}
							onSearch={(value) => {
								setOriginData(
									selectedKeys[0] === 'all'
										? data.filter((item) =>
												item.chartName.includes(
													value.toLocaleLowerCase()
												)
										  )
										: data.filter(
												(item) =>
													item.chartName.includes(
														value.toLocaleLowerCase()
													) &&
													item.type ===
														selectedKeys[0]
										  )
								);
							}}
							allowClear
						/>
					</div>
					<div className="middleware-container">
						<div className="middleware-content">
							{originData.length ? (
								<div className="middleware-item-list">
									{originData.map((item: any) => {
										return (
											<MiddlewareItem
												key={item.id}
												{...item}
												onEnableChange={(
													value: boolean
												) =>
													onEnableChange(item, value)
												}
											/>
										);
									})}
								</div>
							) : (
								<Empty
									image={Empty.PRESENTED_IMAGE_SIMPLE}
									style={{ marginTop: 100 }}
								/>
							)}
						</div>
					</div>
				</Spin>
			</ProContent>
		</div>
	);
}

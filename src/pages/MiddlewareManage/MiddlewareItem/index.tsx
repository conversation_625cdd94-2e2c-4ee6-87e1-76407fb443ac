import React, { useState } from 'react';
import { Tooltip, Checkbox } from 'antd';
import { useHistory } from 'react-router';
import {
	LoadingOutlined,
	CheckCircleFilled,
	MinusCircleFilled,
	ExclamationCircleFilled,
	ExclamationCircleOutlined
} from '@ant-design/icons';

import { middlewareItemProps } from '@/pages/MiddlewareRepository/middleware';
import { MiddlewareType } from '@/utils/enum';
import otherColor from '@/assets/images/nodata.svg';
import { api } from '@/api.json';

import './index.less';

const MiddlewareItem = (props: middlewareItemProps): JSX.Element => {
	const {
		name,
		chartVersion,
		description,
		imagePath,
		status,
		clusterId,
		type,
		vendor,
		enable,
		isManage,
		withMiddleware,
		onEnableChange
	} = props;
	const history = useHistory();
	const [checked, setChecked] = useState<boolean>(enable);
	const controllerRender = (value: number) => {
		switch (value) {
			case 0:
				return (
					<>
						<Tooltip title="安装中">
							<LoadingOutlined
								style={{
									color: '#D1D5D9',
									marginRight: '6px',
									fontSize: '12px'
								}}
							/>
						</Tooltip>
					</>
				);
			case 1:
				return (
					<>
						<Tooltip title="运行正常">
							<CheckCircleFilled
								style={{
									color: '#52c41a',
									marginRight: '6px',
									fontSize: '12px'
								}}
							/>
						</Tooltip>
					</>
				);
			case 2:
				return (
					<>
						<Tooltip title="待安装">
							<MinusCircleFilled
								style={{
									color: '#d7d7d7',
									marginRight: '6px',
									fontSize: '12px'
								}}
							/>
						</Tooltip>
					</>
				);
			case 3:
				return (
					<>
						<Tooltip title="不可用">
							<ExclamationCircleOutlined
								style={{
									color: '#ff4d4f',
									marginRight: '6px',
									fontSize: '12px'
								}}
							/>
						</Tooltip>
					</>
				);
			case 4:
				return (
					<>
						<Tooltip title="运行异常">
							<ExclamationCircleFilled
								style={{
									color: '#faad14',
									marginRight: '6px',
									fontSize: '12px'
								}}
							/>
						</Tooltip>
					</>
				);
			default:
				return (
					<Tooltip
						title={
							withMiddleware
								? '当前项目下仍存在该中间件的服务，请在删除服务后重试'
								: ''
						}
					>
						<Checkbox
							disabled={!!withMiddleware && checked}
							checked={checked}
							onChange={(e) => {
								if (!onEnableChange?.(e.target.checked)) {
									return;
								}
								setChecked(e.target.checked);
							}}
						/>
					</Tooltip>
				);
		}
	};

	return (
		<div
			className="middleware-item"
			onClick={() => {
				if (clusterId) {
					history.push(
						`/platform/marketManagement/middlewareDetail/${name}/${clusterId}/${imagePath}`
					);
				}
			}}
		>
			<div className="display-flex flex-center">
				<img
					className="middleware-img"
					src={
						imagePath
							? `${api}/images/middleware/${imagePath}`
							: otherColor
					}
					alt={name}
				/>
				<div style={{ width: 'calc(100% - 80px)' }}>
					<div className="middleware-info">
						<div
							className={`display-flex flex-align flex-space-between`}
						>
							<div
								className={`middleware-title ${
									isManage ? 'link' : ''
								}`}
							>
								{name}
							</div>
							<span className="ml-12">
								{controllerRender(status)}
							</span>
						</div>
						<div className="middleware-description">{vendor}</div>
						<div
							className="middleware-description"
							title={description}
						>
							{description}
						</div>
					</div>
					{isManage ? (
						<Tooltip
							placement="topRight"
							title={`类型：${
								MiddlewareType[type || 'null']
							} ｜ 版本：${chartVersion}`}
						>
							{isManage ? (
								<div className="middleware-footer">
									{`类型：${
										MiddlewareType[type || 'null']
									} ｜ 版本：${chartVersion}`}
								</div>
							) : (
								<div className="middleware-footer">
									{`类型：${MiddlewareType[type || 'null']}`}
								</div>
							)}
						</Tooltip>
					) : (
						<div className="middleware-footer">
							{`类型：${MiddlewareType[type || 'null']}`}
						</div>
					)}
				</div>
			</div>
		</div>
	);
};

export default MiddlewareItem;

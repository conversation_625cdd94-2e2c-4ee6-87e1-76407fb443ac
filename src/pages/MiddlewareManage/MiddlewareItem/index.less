.middleware-item {
	cursor: pointer;
	padding: 16px;
	flex-shrink: 0;
	color: #000;
	line-height: 20px;
	border-radius: 4px;
	border: 1px solid transparent;
	box-shadow: 0px 0px 1px 0px rgba(12, 26, 75, 0.24),
		0px 3px 8px -1px rgba(50, 50, 71, 0.05);
	&:hover {
		border: 1px solid rgba(34, 110, 231, 0.1);
		box-shadow: 0px 0px 1px 0px rgba(12, 26, 75, 0.24),
			0px 3px 8px -1px rgba(50, 50, 71, 0.05),
			0px 2px 10px 0px rgba(26, 69, 137, 0.2);
		.middleware-title.link {
			color: var(--unnamed, #226ee7);
		}
	}
	.middleware-btn-header {
		height: 16px;
	}
	.middleware-btn {
		text-align: right;
		color: var(--unnamed, #226ee7);
		font-family: PingFang SC;
		font-size: 12px;
		font-style: normal;
		font-weight: 500;
		line-height: 16px;
		float: right;
		&.disabled {
			color: #cccccc;
		}
	}
	.middleware-img {
		width: 70px;
		height: 70px;
		margin-right: 16px;
		display: inline-block;
	}
	.middleware-info {
		width: calc(100%);
	}
	.middleware-title {
		color: rgba(0, 0, 0, 0.85);
		font-family: PingFang SC;
		font-size: 14px;
		font-style: normal;
		font-weight: 500;
		line-height: 20px;
	}
	.middleware-description {
		width: 150px;
		margin-top: 8px;
		color: rgba(0, 0, 0, 0.45);
		font-family: PingFang SC;
		font-size: 12px;
		font-style: normal;
		font-weight: 400;
		line-height: 18px;
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
	}
	.middleware-footer {
		// display: flex;
		margin-top: 8px;
		width: 100%;
		color: rgba(0, 0, 0, 0.25);
		font-family: PingFang SC;
		font-size: 12px;
		font-style: normal;
		font-weight: 400;
		line-height: 20px;
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
	}
	.middleware-divider {
		width: 1px;
		height: 18px;
		margin: 0 12px;
		background: #e4e4e4;
	}
	.middleware-type {
		font-weight: bold;
	}
	.middleware-office {
		font-weight: bold;
	}
}

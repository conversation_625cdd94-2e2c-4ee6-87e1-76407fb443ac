import { resProps } from '@/types/comment';

export interface userRoleItem {
	organId: null;
	organName: string | null;
	power: null;
	projectId: string | null;
	projectName: string | null;
	roleId: number;
	roleName: string;
	userName: string;
}
export interface userProps {
	userName: string;
	createTime?: string | null;
	email: string | null;
	id: number;
	isAdmin: boolean | null;
	password: string | null;
	passwordTime: string | null;
	phone: string | null;
	power: any;
	roleId: number | null;
	roleName?: string;
	aliasName: string | null;
	lockStatus: string;
	userRoleList: userRoleItem[];
	wxUserInfo: {
		wxUserId?: string;
	};
	thirdPartyOAUserInfo: {
		thirdPartyId?: string;
	};
	[propsName: string]: any;
}
export interface roleProps {
	value: any;
	label: string;
	description: string;
	id: number;
	name: string;
	status: string | null;
}
export interface usersProps extends resProps {
	data: {
		total?: number;
		current?: number;
		pageSize?: number;
		list: userProps[];
		userRoleList: any[];
	};
}
export interface usersDataProps extends resProps {
	data: userProps[];
}
export interface updateProps extends resProps {
	data: userProps;
}
export interface deleteProps extends resProps {
	data: boolean;
}
export interface rolesProps extends resProps {
	data: roleProps[];
}
export interface sendDataParams {
	userName: string;
	aliasName: string;
	phone: string;
	email: null | string;
	isAdmin: boolean;
}

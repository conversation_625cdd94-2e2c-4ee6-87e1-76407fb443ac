import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, notification, Radio, Select } from 'antd';
import { createUser, updateUser } from '@/services/user';
import { userProps } from './user';
import pattern from '@/utils/pattern';
import storage from '@/utils/storage';
import { getRoleList } from '@/services/role';
import { roleProps } from '../RoleManage/role';

const FormItem = Form.Item;
const formItemLayout = {
	labelCol: {
		span: 6
	},
	wrapperCol: {
		span: 18
	}
};
interface userFormProps {
	visible: boolean;
	onCreate: () => void;
	onCancel: () => void;
	data: userProps | undefined | null;
}
export default function UserForm(props: userFormProps): JSX.Element {
	const { visible, onCreate, onCancel, data } = props;
	const [managerRoles, setManagerRoles] = useState<roleProps[]>([]);
	const [dbaRoles, setDBARoles] = useState<roleProps[]>([]);
	const [loading, setLoading] = useState<boolean>(false);
	const [form] = Form.useForm();
	// * 企业微信是否开启的判断
	const [WeChatAccessCloseAPI] = useState<boolean>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'weChatAccessClose')?.enabled ??
			true
	);
	// * 马上办是否开启的判断
	const [ThirdPartyOACloseAPI] = useState<boolean>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'thirdPartyOAClose')?.enabled ??
			true
	);
	useEffect(() => {
		getRoleList({ key: '' }).then((res) => {
			if (res.success) {
				setManagerRoles(
					res.data.filter(
						(item: roleProps) =>
							item.type === 'manager' &&
							item.weight !== 2 &&
							item.weight !== 3
					)
				);
				setDBARoles(
					res.data.filter((item: roleProps) => item.type === 'dba')
				);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	}, []);
	useEffect(() => {
		if (data) {
			form.setFieldsValue({
				userName: data.userName,
				aliasName: data.aliasName,
				phone: data.phone,
				email: data.email,
				isManager: data.manager !== null ? true : false,
				manager: data.manager,
				isDBA: data.dba !== null ? true : false,
				dba: data.dba,
				wxUserId: data?.wxUserInfo?.wxUserId,
				thirdPartyId: data?.thirdPartyOAUserInfo?.thirdPartyId
			});
		}
	}, [data]);
	const onOk: () => void = () => {
		form.validateFields().then((values) => {
			let sendData: any = {
				...values
			};
			delete sendData.isManager;
			delete sendData.isDBA;
			if (!WeChatAccessCloseAPI) {
				sendData = {
					...sendData,
					wxUserInfo: {
						wxUserId: values.wxUserId
					}
				};
				delete sendData.wxUserId;
			}
			if (!ThirdPartyOACloseAPI) {
				sendData = {
					...sendData,
					thirdPartyOAUserInfo: {
						thirdPartyId: values.thirdPartyId
					}
				};
				delete sendData.thirdPartyId;
			}
			setLoading(true);
			if (data) {
				// * 修改用户
				updateUser(sendData)
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '用户修改成功'
							});
							onCreate();
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					})
					.finally(() => {
						setLoading(false);
					});
			} else {
				// * 创建用户
				createUser(sendData)
					.then((res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '用户创建成功'
							});
							onCreate();
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					})
					.finally(() => {
						setLoading(false);
					});
			}
		});
	};
	return (
		<Modal
			title={!data ? '新增用户' : '编辑用户'}
			open={visible}
			onOk={onOk}
			okButtonProps={{
				loading: loading
			}}
			onCancel={onCancel}
			okText="确定"
			cancelText="取消"
		>
			<Form labelAlign="left" form={form} {...formItemLayout}>
				<p style={{ color: '#Ef595C', marginBottom: 16 }}>
					默认密码：zeus123.com，
					登录后，请点击【个人头像-&gt;修改密码】重新设置
				</p>
				<FormItem
					label="登录账户名称"
					required
					rules={[
						{ required: true, message: '请输入登录账户名称' },
						{
							pattern: new RegExp(pattern.userName),
							message:
								'登录账户名只允许英文大小写+数字组合，长度不可超过25字符'
						}
					]}
					name="userName"
				>
					<Input
						disabled={data ? true : false}
						placeholder="请输入登录账户名称"
					/>
				</FormItem>
				<FormItem
					label="用户名"
					required
					rules={[
						{ required: true, message: '请输入用户名' },
						{
							pattern: new RegExp(pattern.aliasName),
							message:
								'用户名只允许中文、英文大小写+数字组合，长度不可超过18字符'
						}
					]}
					name="aliasName"
				>
					<Input placeholder="请输入用户名" />
				</FormItem>
				<FormItem
					label="手机号"
					required
					rules={[
						{ required: true, message: '请输入手机号' },
						{
							pattern: new RegExp(pattern.phone),
							message: '请输入正确的手机号'
						}
					]}
					name="phone"
				>
					<Input placeholder="请输入手机号" />
				</FormItem>
				<FormItem
					label="邮箱"
					name="email"
					rules={[
						{
							required: true,
							message: '请输入邮箱'
						},
						{
							pattern: new RegExp(pattern.email),
							message: '请输入正确的邮箱地址'
						}
					]}
				>
					<Input placeholder="请输入邮箱" />
				</FormItem>
				{!WeChatAccessCloseAPI && (
					<FormItem name="wxUserId" label="企业微信号(OA号)">
						<Input placeholder="请输入企业微信号(OA号)" />
					</FormItem>
				)}
				{!ThirdPartyOACloseAPI && (
					<FormItem name="thirdPartyId" label="马上办账号">
						<Input placeholder="请输入马上办账号" />
					</FormItem>
				)}
				<FormItem
					label="是否为管理员"
					name="isManager"
					initialValue={false}
				>
					<Radio.Group
						disabled={
							JSON.parse(storage.getLocal('role')).userName ===
							data?.userName
						}
					>
						<Radio value={false}>否</Radio>
						<Radio value={true}>是</Radio>
					</Radio.Group>
				</FormItem>
				<FormItem noStyle shouldUpdate>
					{({ getFieldValue }) => {
						if (getFieldValue('isManager') === true) {
							return (
								<FormItem
									label="选择管理类型角色"
									name="manager"
									rules={[
										{
											required: true,
											message: '请选择管理类型角色'
										}
									]}
								>
									<Select
										placeholder="请选择管理类型角色"
										disabled={
											JSON.parse(storage.getLocal('role'))
												.userName === data?.userName
										}
									>
										{managerRoles.map((item: roleProps) => {
											return (
												<Select.Option
													key={item.id}
													value={item.id}
													disabled={
														// * 非admin用户不可给他人分配超级管理员角色权限
														JSON.parse(
															storage.getLocal(
																'role'
															)
														).userName !==
															'admin' &&
														item.id === 1
													}
													title={
														JSON.parse(
															storage.getLocal(
																'role'
															)
														).userName !==
															'admin' &&
														item.id === 1
															? '非admin用户不可给他人分配超级管理员权限'
															: ''
													}
												>
													{item.name}
												</Select.Option>
											);
										})}
									</Select>
								</FormItem>
							);
						}
					}}
				</FormItem>
				<FormItem label="是否为DBA" name="isDBA" initialValue={false}>
					<Radio.Group
						disabled={
							JSON.parse(storage.getLocal('role')).userName ===
							data?.userName
						}
					>
						<Radio value={false}>否</Radio>
						<Radio value={true}>是</Radio>
					</Radio.Group>
				</FormItem>
				<FormItem noStyle shouldUpdate>
					{({ getFieldValue }) => {
						console.log(getFieldValue('isDBA'));
						if (getFieldValue('isDBA') === true) {
							return (
								<FormItem
									label="选择DBA类型角色"
									name="dba"
									rules={[
										{
											required: true,
											message: '请选择DBA类型角色'
										}
									]}
								>
									<Select
										placeholder="请选择DBA类型角色"
										disabled={
											JSON.parse(storage.getLocal('role'))
												.userName === data?.userName
										}
									>
										{dbaRoles.map((item: roleProps) => {
											return (
												<Select.Option
													key={item.id}
													value={item.id}
												>
													{item.name}
												</Select.Option>
											);
										})}
									</Select>
								</FormItem>
							);
						}
					}}
				</FormItem>
			</Form>
		</Modal>
	);
}

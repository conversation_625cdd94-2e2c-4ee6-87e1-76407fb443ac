import React, { useState, useEffect } from 'react';
import {
	Button,
	notification,
	Modal,
	Form,
	Select,
	TablePaginationConfig,
	InputNumber,
	Drawer,
	Table
} from 'antd';
import moment from 'moment';
import { ProPage, ProHeader, ProContent } from '@/components/ProPage';
import Actions from '@/components/Actions';
import ProTable from '@/components/ProTable';
import {
	getUserList,
	deleteUser,
	resetPassword,
	getLDAP,
	getPasswordDate,
	editPasswordDate,
	getUserInformation,
	lockUser,
	unBindWeChat
} from '@/services/user';
import { userProps, userRoleItem } from './user';
import { nullRender, objectRemoveDuplicatesByKey } from '@/utils/utils';
import { passwordPeriods } from '@/utils/const';
import UserForm from './UserForm';
import storage from '@/utils/storage';
import '../RoleManage/index.less';

const LinkButton = Actions.LinkButton;
const { confirm } = Modal;
function UserManage(): JSX.Element {
	const [form] = Form.useForm();
	const [dataSource, setDataSource] = useState<userProps[]>([]);
	const [total, setTotal] = useState<number>(0);
	const [current, setCurrent] = useState<number>(1);
	const [pageSize, setPageSize] = useState<number>(10);
	const [keyword, setKeyword] = useState<string>('');
	const [visible, setVisible] = useState<boolean>(false);
	const [passwordVisible, setPasswordVisible] = useState<boolean>(false);
	const [passwordPeriod, setPasswordPeriod] = useState<string>('90');
	const [updateData, setUpdateData] = useState<userProps>();
	const [isEdit, setIsEdit] = useState(true);
	const [isLDAP, setIsLDAP] = useState<boolean>(false);
	const [isAccess] = useState<boolean>(storage.getLocal('isAccessGYT'));
	const [roleOpen, setRoleOpen] = useState<boolean>(false);
	const [checkRole, setCheckRole] = useState<userProps>();
	const [orgFilters, setOrgFilters] = useState<any[]>([]);
	const [proFilters, setProFilters] = useState<any[]>([]);
	const [roleFilters, setRoleFilters] = useState<any[]>([]);
	const roleBasic = JSON.parse(storage.getLocal('role'));
	// * 企业微信是否开启的判断
	const [WeChatAccessCloseAPI] = useState<boolean>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'weChatAccessClose')?.enabled ??
			true
	);
	// * 马上办是否开启的判断
	const [ThirdPartyOACloseAPI] = useState<boolean>(
		storage
			.getLocal('featureAPI')
			?.find((item: any) => item.name === 'thirdPartyOAClose')?.enabled ??
			true
	);
	useEffect(() => {
		getLDAP().then((res) => {
			res.success && setIsLDAP(res.data.isOn);
		});
	}, []);
	useEffect(() => {
		getPasswordDate().then((res) => {
			if (res.success) {
				if (
					passwordPeriods.find(
						(item) => item.value === res.data.configValue
					)
				) {
					setPasswordPeriod(res.data.configValue);
				} else {
					setPasswordPeriod('customize');
					form.setFieldValue('configValue', res.data.configValue);
				}
			}
		});
	}, [passwordVisible]);
	useEffect(() => {
		let mounted = true;
		getUserList({
			keyword: keyword,
			current: current,
			size: pageSize
		}).then((res) => {
			if (res.success) {
				if (mounted) {
					setTotal(res.data.total);
					setCurrent(res.data.pageNum);
					// * 非admin查看用户管理，需要将admin账户进行隐藏
					if (roleBasic.userName !== 'admin') {
						setDataSource(
							res.data.list.filter(
								(item: userProps) => item.userName !== 'admin'
							)
						);
					} else {
						setDataSource(res.data.list);
					}
				}
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
		return () => {
			mounted = false;
		};
	}, []);
	const onRefresh: (value: string, currentPage: number) => void = (
		value,
		currentPage
	) => {
		getUserList({
			keyword: value,
			current: currentPage,
			size: pageSize
		}).then((res) => {
			if (res.success) {
				setTotal(res.data.total);
				setCurrent(res.data.pageNum);
				// * 非admin查看用户管理，需要将admin账户进行隐藏
				if (roleBasic.userName !== 'admin') {
					setDataSource(
						res.data.list.filter(
							(item: userProps) => item.userName !== 'admin'
						)
					);
				} else {
					setDataSource(res.data.list);
				}
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const handleSearch: (value: string) => void = (value: string) => {
		setKeyword(value);
		onRefresh(value, 1);
	};
	const edit: (record: userProps) => void = (record: userProps) => {
		setUpdateData(record);
		setVisible(true);
	};
	const deleteUserHandle: (record: userProps) => void = (
		record: userProps
	) => {
		confirm({
			title: '操作确认',
			content: '删除将无法找回，是否继续?',
			okText: '确定',
			cancelText: '取消',
			onOk: () => {
				if (record.userName === 'admin') {
					notification.error({
						message: '失败',
						description: 'admin用户无法删除'
					});
					return;
				}
				deleteUser({ userName: record.userName }).then((res) => {
					if (res.success) {
						const totalPages = Math.ceil((total - 1) / pageSize);
						notification.success({
							message: '成功',
							description: '该用户删除成功'
						});
						onRefresh(
							keyword,
							current > totalPages ? totalPages : current
						);
					} else {
						notification.error({
							message: '失败',
							description: res.errorMsg
						});
					}
				});
			}
		});
	};
	const resetPasswordHandle: (record: userProps) => void = (
		record: userProps
	) => {
		confirm({
			title: '操作确认',
			content: '该账户的密码将重置为：zeus123.com，是否继续？',
			okText: '确定',
			cancelText: '取消',
			onOk: () => {
				return resetPassword({ userName: record.userName }).then(
					(res) => {
						if (res.success) {
							notification.success({
								message: '成功',
								description: '该用户密码重置成功'
							});
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					}
				);
			}
		});
	};
	const lock = (record: userProps) => {
		if (record.lockStatus !== 'locked') {
			Modal.confirm({
				title: '操作确认',
				content:
					'锁定账号后，该账号无法登录平台，仅能由超级管理员解封，是否确定锁定该账户？',
				onOk: () =>
					lockUser({
						username: record.userName,
						locked: true
					}).then((res) => {
						if (res.success) {
							onRefresh(keyword, current);
							notification.success({
								message: '成功',
								description: `${
									record.lockStatus === 'locked'
										? '解锁'
										: '锁定'
								}成功`
							});
						} else {
							notification.error({
								message: '失败',
								description: res.errorMsg
							});
						}
					})
			});
		} else {
			lockUser({
				username: record.userName,
				locked: false
			}).then((res) => {
				if (res.success) {
					onRefresh(keyword, current);
					notification.success({
						message: '成功',
						description: `${
							record.lockStatus === 'locked' ? '解锁' : '锁定'
						}成功`
					});
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		}
	};
	const actionRender = (value: string, record: userProps, index: number) => {
		// * 禁用逻辑
		// * 1、禁用username为admin的所有修改操作
		// * 2、除了admin以外的超级管理员不能修改admin
		// * 3、当前用户不可删除、重置密码自己
		// * 4、开启LDAP后，不可编辑
		// * 5、平台接入观云台后，不可编辑
		// * 6、拥有用户管理页面的自定义管理员与超级管理员同级
		// * 7、超级管理员不能编辑、修改、重置其他超级管理员
		// * ------
		// admin账户可以解锁被锁定的账户
		// * record 是否为特殊用户 admin
		const isSuperAdmin = record.userName === 'admin';
		// * record 是否是当前用户自身
		const isMyself = roleBasic.id === record.id;
		// * 只有 admin 可以编辑、删除、重置密码 超级管理员
		const isAdmin =
			record.isAdmin === true && roleBasic.userName !== 'admin';
		return (
			<Actions threshold={3}>
				<LinkButton
					onClick={() => {
						getUserInformation({
							userName: record.userName,
							roleDetail: true
						}).then((res) => {
							if (res.success) {
								edit(res.data as unknown as userProps);
								setIsEdit(true);
							}
						});
					}}
					disabled={
						isLDAP ||
						isAccess ||
						isSuperAdmin ||
						(!isMyself && isAdmin)
					}
					title={
						isLDAP
							? '请联系LDAP管理员'
							: isAccess
							? '平台已接入观云台，请联系观云台管理员'
							: ''
					}
				>
					编辑
				</LinkButton>
				{record.userName !== 'admin' &&
					(roleBasic.isAdmin || roleBasic.manager) && (
						<LinkButton
							onClick={() => lock(record)}
							disabled={
								((record.isAdmin as boolean) &&
									roleBasic.userName !== 'admin') ||
								roleBasic.userName === record.userName
							}
						>
							{record.lockStatus === 'locked' ? '解锁' : '锁定'}
						</LinkButton>
					)}
				<LinkButton
					onClick={() => {
						getUserInformation({
							userName: record.userName,
							roleDetail: true
						}).then((res) => {
							if (res.success) {
								setCheckRole(res.data as unknown as userProps);
								const oft = objectRemoveDuplicatesByKey(
									res.data.userRoleList?.map((item) => {
										return {
											value: item.organName || '/',
											text: item.organName || '/'
										};
									}) || [],
									'value'
								);
								setOrgFilters(oft);
								const pft = objectRemoveDuplicatesByKey(
									res.data.userRoleList?.map((item) => {
										return {
											value: item.projectName || '/',
											text: item.projectName || '/'
										};
									}) || [],
									'value'
								);
								setProFilters(pft);
								const rft = objectRemoveDuplicatesByKey(
									res.data.userRoleList?.map((item) => {
										return {
											value: item.roleName || '/',
											text: item.roleName || '/'
										};
									}) || [],
									'value'
								);
								setRoleFilters(rft);
								setRoleOpen(true);
							}
						});
					}}
				>
					查看角色
				</LinkButton>
				{!isSuperAdmin && (
					<LinkButton
						disabled={isLDAP || isAccess || isMyself || isAdmin}
						onClick={() => deleteUserHandle(record)}
						title={
							isLDAP
								? '请联系LDAP管理员'
								: isAccess
								? '平台已接入观云台，请联系观云台管理员'
								: ''
						}
					>
						删除
					</LinkButton>
				)}
				<LinkButton
					onClick={() => resetPasswordHandle(record)}
					disabled={
						isLDAP ||
						isAccess ||
						isSuperAdmin ||
						isMyself ||
						(!isMyself && isAdmin)
					}
					title={
						isLDAP
							? '请联系LDAP管理员修改密码'
							: isAccess
							? '平台已接入观云台，请联系观云台管理员修改密码'
							: ''
					}
				>
					密码重置
				</LinkButton>
			</Actions>
		);
	};
	const createTimeRender = (value: string) => {
		if (!value) return '--';
		return moment(value).format('YYYY-MM-DD HH:mm:ss');
	};
	const Operation = {
		primary: (
			<>
				<Button
					type="primary"
					onClick={() => {
						setVisible(true);
						setIsEdit(false);
					}}
					disabled={isLDAP || isAccess}
					title={
						isLDAP
							? '请联系LDAP管理员'
							: isAccess
							? '平台已接入观云台，请联系观云台管理员'
							: ''
					}
				>
					新增
				</Button>
				<Button
					onClick={() => setPasswordVisible(true)}
					disabled={isLDAP || isAccess}
					title={
						isLDAP
							? '请联系LDAP管理员'
							: isAccess
							? '平台已接入观云台，请联系观云台管理员'
							: ''
					}
				>
					设置密码有效期
				</Button>
			</>
		)
	};
	const onTableChange = (
		pagination: TablePaginationConfig,
		filters: any,
		sorter: any
	) => {
		setCurrent(pagination.current || 1);
		setTotal(pagination.total || 0);
		setPageSize(pagination.pageSize || 10);
		getUserList({
			keyword: keyword,
			current: pagination.current || 1,
			size: pagination.pageSize,
			order: sorter.order
		}).then((res) => {
			if (res.success) {
				setTotal(res.data.total);
				setCurrent(res.data.pageNum);
				// * 非admin查看用户管理，需要将admin账户进行隐藏
				if (roleBasic.userName !== 'admin') {
					setDataSource(
						res.data.list.filter(
							(item: userProps) => item.userName !== 'admin'
						)
					);
				} else {
					setDataSource(res.data.list);
				}
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const onOK = () => {
		form.validateFields().then((values) => {
			editPasswordDate({
				configValue:
					passwordPeriod === 'customize'
						? values.configValue
						: passwordPeriod
			}).then((res) => {
				if (res.success) {
					setPasswordVisible(false);
					notification.success({
						message: '成功',
						description: '设置成功'
					});
				} else {
					notification.error({
						message: '失败',
						description: res.errorMsg
					});
				}
			});
		});
	};
	return (
		<ProPage>
			<ProHeader
				title="用户管理"
				subTitle="创建用于登录平台的用户账号，并赋予角色平台权限"
			/>
			<ProContent>
				<ProTable
					dataSource={dataSource}
					showRefresh
					onRefresh={() => onRefresh(keyword, current)}
					rowKey="userName"
					search={{
						placeholder:
							'请输入登录账户、用户名、邮箱、手机号进行搜索',
						onSearch: handleSearch,
						style: { width: '360px' }
					}}
					operation={Operation}
					pagination={{
						total: total,
						current: current,
						pageSize: pageSize
					}}
					onChange={onTableChange}
				>
					<ProTable.Column
						title="登录账户"
						dataIndex="userName"
						width={130}
						ellipsis={true}
					/>
					<ProTable.Column
						title="用户名"
						dataIndex="aliasName"
						width={130}
						ellipsis={true}
					/>
					<ProTable.Column
						title="邮箱"
						dataIndex="email"
						ellipsis={true}
						render={(value) => value || '/'}
					/>
					<ProTable.Column
						title="手机"
						dataIndex="phone"
						width={130}
						ellipsis={true}
						render={(value) => value || '/'}
					/>
					<ProTable.Column
						title="创建时间"
						dataIndex="createTime"
						render={createTimeRender}
						width={180}
						sorter={true}
					/>
					{!WeChatAccessCloseAPI && (
						<ProTable.Column
							title="企业微信号(OA号)"
							dataIndex="WxUserId"
							width={120}
							render={(value: string, record: userProps) =>
								record?.wxUserInfo?.wxUserId || '/'
							}
							ellipsis={true}
						/>
					)}
					{!ThirdPartyOACloseAPI && (
						<ProTable.Column
							title="马上办账号"
							dataIndex="thirdPartyId"
							width={100}
							ellipsis={true}
							render={(_: any, record: userProps) => {
								return (
									record?.thirdPartyOAUserInfo
										?.thirdPartyId || '/'
								);
							}}
						/>
					)}
					<ProTable.Column
						title="操作"
						dataIndex="action"
						width={200}
						render={actionRender}
					/>
				</ProTable>
			</ProContent>
			{visible && (
				<UserForm
					visible={visible}
					onCreate={() => {
						setVisible(false);
						onRefresh(keyword, current);
					}}
					onCancel={() => setVisible(false)}
					data={isEdit ? updateData : null}
				/>
			)}
			{passwordVisible && (
				<Modal
					title="密码有效期设置"
					open={passwordVisible}
					onOk={onOK}
					onCancel={() => setPasswordVisible(false)}
				>
					<Form
						form={form}
						labelAlign="left"
						labelCol={{ span: 12 }}
						wrapperCol={{ span: 12 }}
					>
						<div style={{ display: 'flex' }}>
							<Form.Item
								style={{ width: '50%' }}
								label="时间选择"
							>
								<Select
									style={{
										width:
											passwordPeriod === 'customize'
												? '119px'
												: '354px'
									}}
									value={passwordPeriod}
									onChange={(value) =>
										setPasswordPeriod(value)
									}
									options={passwordPeriods}
								/>
							</Form.Item>
							{passwordPeriod === 'customize' && (
								<Form.Item
									name="configValue"
									style={{ width: '50%' }}
									wrapperCol={{ span: 24 }}
									rules={[
										{
											required: true,
											message: '请输入密码有效期天数'
										}
									]}
								>
									<InputNumber
										min={1}
										step={1}
										parser={(value: any) =>
											value.split('.')[0]
										}
										style={{ width: '100%' }}
										addonAfter="天"
									/>
								</Form.Item>
							)}
						</div>
					</Form>
				</Modal>
			)}
			{roleOpen && (
				<Drawer
					title={`查看角色${checkRole?.userName}`}
					placement="right"
					onClose={() => setRoleOpen(false)}
					width={600}
					open={roleOpen}
				>
					<Table
						dataSource={checkRole?.userRoleList}
						rowKey={(record, index) => index || Math.random()}
					>
						<Table.Column
							dataIndex="organName"
							title="组织名称"
							render={nullRender}
							filters={orgFilters}
							onFilter={(value, record: userRoleItem) =>
								(record.organName || '/') === value
							}
						/>
						<Table.Column
							dataIndex="projectName"
							title="项目名称"
							render={nullRender}
							filters={proFilters}
							onFilter={(value, record: userRoleItem) => {
								return (record.projectName || '/') === value;
							}}
						/>
						<Table.Column
							dataIndex="roleName"
							title="关联角色"
							render={nullRender}
							filters={roleFilters}
							onFilter={(value, record: userRoleItem) =>
								(record.roleName || '/') === value
							}
						/>
					</Table>
				</Drawer>
			)}
		</ProPage>
	);
}

export default UserManage;

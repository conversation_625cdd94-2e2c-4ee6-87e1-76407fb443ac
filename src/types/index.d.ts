import { ProjectItem } from '@/types';
import { ConfigItem } from '@/pages/ServiceListDetail/detail';
import {
	monitorProps,
	storageProps,
	ingressProps,
	registryProps,
	clusterQuotaDTOProps
} from './comment';

export interface clusterAddType {
	accessToken?: string | null;
	address?: string;
	cert: any | null;
	dcId?: string;
	host: string;
	ingressList?: ingressProps[];
	logging?: any | null;
	monitor?: monitorProps;
	name: string;
	namespaceList?: any | null;
	nickname: string;
	port: number;
	protocol: string;
	registry: registryProps;
	storage?: storageProps;
	clusterQuotaDTO?: clusterQuotaDTOProps | null;
	removable?: boolean;
	[propsName: string]: any;
}
export interface clusterType extends clusterAddType {
	id: string;
	areaId: string;
	areaName: string;
}
export interface namespaceType {
	name: string;
	clusterId: string;
	[propsName: string]: any;
}
export interface NavbarNamespaceItem {
	aliasName: string;
	clusterAliasName?: string;
	clusterId?: string;
	createTime?: null;
	middlewareReplicas?: null;
	name: string;
	phase?: null;
	projectId?: string;
	quotas?: null;
	registered?: boolean;
}

export interface paramReduxProps {
	name: string;
	description: string;
	customConfigList: ConfigItem[];
}
export interface organizationReduxProps {
	clusters: any[];
	proClusters: any[];
	editCluster: any;
}
export interface authReduxProps {
	menu: any[];
	allMenu: any[];
	buttonList: any[];
}
export interface StoreState {
	auth: authReduxProps;
	log: any;
	param: paramReduxProps;
	execute: {
		refreshFlag: boolean;
	};
	organization: organizationReduxProps;
}

export interface RoleItem {
	organId: string;
	power: {
		[propsName: string]: string;
	};
	projectId: string;
	roleId: number;
	roleName: string;
	roleType: string;
	userName: string;
	weight: number;
}
export interface User {
	aliasName: string;
	createTime: string;
	dba: number | null;
	email: null | string;
	id: number | string;
	isAdmin: boolean;
	password: string;
	passwordTime: string;
	phone: string;
	power: null;
	roleId: null | number;
	roleName: null | string;
	userName: string;
	userRoleList: RoleItem[];
}
export interface ProjectItem {
	aliasName: string;
	clusterList: null | any;
	createTime: string;
	description: string;
	memberCount: number | null;
	middlewareCount: null | number;
	name: string;
	namespaceCount: null | number;
	projectId: string;
	user: string;
	userDtoList: null | any;
	roleName: string | null;
	roleId: number | null;
	backupServerList: any[];
	[propsName: string]: any;
}

declare interface FormParams {
	topic: string;
	middlewareName?: string;
	operatorId?: string;
	orderId?: string;
	name?: string;
	aliasName?: string;
	topic?: string;
	lock?: string;
}
declare interface QuotaInfoItem {
	id: number;
	type: string | null;
	cpu: number | null;
	memory: number | null;
	storageClassName: string | null;
	storageClassQuota: number | null;
}
declare interface ResourceQuotaDo {
	clusterId: string;
	clusterNickName: string;
	cpu: {
		allocatable: number | null;
		occupy: number | null;
		request: number | null;
		total: number | null;
		usable: number | null;
		usage: number | null;
		used: number | null;
	};
	memory: {
		allocatable: number | null;
		occupy: number | null;
		request: number | null;
		total: number | null;
		usable: number | null;
		usage: number | null;
		used: number | null;
	};
	storageList: any[];
}
declare interface WorkOrderContentDo {
	chartName?: string;
	chartVersion?: string;
	clusterAliasName?: string;
	clusterId?: string;
	deployMod: string;
	ip?: string[];
	maintenanceOperatorId?: string;
	maintenanceOperatorName?: string;
	middlewareAlisaName?: string;
	middlewareName?: string;
	middlewareType?: string;
	middlewareTypeAliasName?: string;
	namespace?: string;
	namespaceAliasName?: string;
	operationEndTime?: string;
	operationStartTime?: string;
	operatorExecuted?: boolean;
	resourceQuotaDo?: ResourceQuotaDo;
}
declare interface WorkOrderStepUserDoItem {
	username: string;
	aliasName: string;
	status: string;
	comment: string;
	WorkOrderStepUserDoItem: WorkOrderStepUserDoItem[];
}
declare interface WorkOrderStepItem {
	comment: string | null;
	handler: string;
	handlerAliasName: string | null;
	nextStepId: string | null;
	orderStepId: string | null;
	status: string;
	stepName: string;
	stepNameSuffix: string;
	stepType: string | null;
	targetRole: string;
	updateTime: string | null;
	userList: string[] | null;
	workOrderStepUserDoList: WorkOrderStepUserDoItem[] | null;
}
declare interface StepItemProps extends WorkOrderStepItem {
	index: number;
}
declare interface WorkOrderItem {
	createTime: string;
	creator: string;
	creatorAliasName: string;
	currentStepId: string;
	description: string;
	executed: boolean | null;
	orderId: string;
	organId: string;
	organName: string;
	projectId: string;
	projectName: string;
	status: string;
	topic: string;
	canRetry: boolean | null;
	updateTime: string;
	userList: string[];
	workOrderContentDo: WorkOrderContentDo;
	workOrderStepDoList: WorkOrderStepItem[];
	currentStepRule?: currentStepRuleItem;
}

declare interface currentStepRuleItem {
	needApproveOpinion: boolean;
	opinionTip: string | null;
	canRecall: boolean;
}
declare interface CompleteSendData {
	comment: string;
	orderId: string;
	result: string;
}
declare interface ImplementSendData {
	comment: string;
	operator: string;
	orderId: string;
	currentStepId: string;
}
declare interface MaintenanceItem {
	available: boolean;
	endTime: string | null;
	operatorAliasName: string;
	operatorId: string;
	operatorType: string;
	orderId: string;
	orderStatus: string;
	organId: string;
	projectId: string;
	startTime: string | null;
}
declare interface MaintenanceItemRes extends resProps {
	data: MaintenanceItem[];
}
declare interface UpdateMaintenanceSendData {
	organId: string;
	projectId: string;
	projectMaintenanceAuditDtoList: MaintenanceItem[];
}
declare interface OrdersRes extends resProps {
	data: {
		list: WorkOrderItem[];
		total: number;
		pageSize: number;
		pageNum: number;
	};
}
declare interface WorkOrderStepItemRes extends resProps {
	data: WorkOrderStepItem[];
}
declare interface OperatorWorkOrderRes extends resProps {
	data: MaintenanceItem;
}
declare interface OrderDetailRes extends resProps {
	data: WorkOrderItem;
}

// * 非工单相关接口定义
declare interface MiddlewareInfo {
	chartName: string;
	chartVersion: string;
	createTime: string;
	description: string;
	enable: boolean | null;
	grafanaId: null;
	id: number;
	image: null;
	imagePath: string;
	middlewares: null;
	name: string;
	official: boolean;
	replicas: number | null;
	replicasStatus: null;
	status: number | null;
	type: string;
	vendor: string;
	version: string;
	versionStatus: null;
	withMiddleware: null;
	checked?: boolean;
}

// * 不可变参数
declare interface ParamItem {
	addition: boolean;
	defaultValue: string;
	description: string;
	hide: boolean;
	name: string;
	paramType: string;
	pattern: null | string;
	ranges: string;
	restart: boolean;
	role: string;
	targetValue: string;
	topping: null;
	updateTime: string;
	value: string;
}
declare interface hideParamItem {
	name: string;
	role: string;
}
declare interface getParamsRes extends resProps {
	data: ParamItem[];
}
// * 区域相关
declare interface AreaClusterItem {
	clusterId: string;
	createTime: string;
	nickname: string;
	nsNumber: string;
}
declare interface AreaItem {
	areaId: string;
	areaName: string;
	description: string;
	clusters: AreaClusterItem[] | null;
}
declare interface AreaRes extends resProps {
	data: AreaItem[];
}
// * 虚拟机配置文件相关
declare interface AccessFileHistoryRecord {
	afterData: string;
	beforeData: string;
	clusterId: string;
	historyId: string;
	instanceName: string;
	name: string;
	namespace: string;
	updateBy: string;
	updateTime: string;
	lock?: string;
	configSameAsNow: boolean;
}
declare interface AccessFileHistoryRecordsRes extends resProps {
	data: {
		list: AccessFileHistoryRecord[];
		total: number;
		pageNum: number;
		pageSize: number;
	};
}
declare interface AccessFilePath {
	content: string;
	instanceName: string;
	charset?: string;
	name?: string;
	path?: string;
}
declare interface AccessFilePathRes extends resProps {
	data: AccessFilePath[];
}
declare interface VerifyCode {
	sig: string;
	imageStr: string;
	code: string;
}
declare interface VerifyCodeRes extends resProps {
	data: VerifyCode;
}
declare interface AccessFileContentRes extends resProps {
	data: string;
}
declare interface WeChatConfig {
	address: string;
	agentId: string;
	corpId: string;
	corpSecret: string;
}
declare interface MiddlewareAccount {
	account: string;
	clusterId: string;
	name: string;
	namespace: string;
	password: string;
	permissions: string;
	state: string;
	type: string;
	role: string[];
	stateErrorReason: string;
}
declare interface MiddlewareAccountRes extends resProps {
	data: {
		records: MiddlewareAccount[];
		current: number;
		size: number;
		total: number;
	};
}
declare interface MiddlewareAccountComment {
	clusterId: string;
	deployMode?: string;
	middlewareName: string;
	namespace: string;
	type?: string;
	accountName?: string;
}
declare interface GetMiddlewareAccountBindUsers
	extends MiddlewareAccountComment {
	size: number;
	current: number;
}
declare interface MiddlewareAccountBindUsers extends resProps {
	data: {
		list: userProps[];
		pageSize: number;
		pageNum: number;
		total: number;
	};
}
declare interface DisempowerMiddlewareAccount extends MiddlewareAccountComment {
	permissions: string;
}
declare interface EmpowerMiddlewareAccounts extends MiddlewareAccountComment {
	account: string[];
	permissions: string;
}
declare interface UnBindMiddlewareAccounts extends MiddlewareAccountComment {
	userId: string;
	accountName: string;
}
declare interface MiddlewareAccountPermission extends resProps {
	data: { permissions: string };
}
declare interface BatchBindUsers extends MiddlewareAccountComment {
	account: string[];
	userIds: number[];
}
declare interface GetMiddlewareAccountByToken extends resProps {
	data: MiddlewareAccount[];
}
declare interface TemplateCreateDetailParams {
	type: string;
	name: string;
	aliasName: string;
	chartVersion;
}
declare interface ServiceTemplateItem {
	content: string;
	createTime: string;
	creator: string;
	description: string;
	id: number;
	mode: string;
	modifier: string;
	name: string;
	organId: string;
	projectId: string;
	quota: any;
	type: string;
	updateTime: string;
	version: string;
}

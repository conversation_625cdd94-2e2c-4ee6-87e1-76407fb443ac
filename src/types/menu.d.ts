declare interface MenuResItem {
	aliasName: string;
	available: null;
	iconName: string;
	id: number;
	module: null;
	name: string;
	own: boolean;
	parentId: number;
	subMenu: MenuResItem[] | null;
	url: string;
	weight: number | null;
}
declare interface OperatorItem {
	agentAddress: string | null;
	aliasName: string;
	describe: string;
	exec: string;
	id: null | number | string;
	name: string;
	operationScope: number;
	instanceNameSet: string[];
	instances: { agentAddress?: string; instanceName: string }[];
	type: string;
	defaultAbility: boolean;
}
declare interface AgentInstanceItem {
	name: string;
	port: number;
	nodeName: string;
	role: string;
	status: string;
	address: string;
	id: number;
	protocol?: string;
}

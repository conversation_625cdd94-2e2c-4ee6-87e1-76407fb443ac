import React from 'react';
import ReactDOM from 'react-dom';
import { Provider } from 'react-redux';
import App from './App';
import store from './redux';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import enUS from 'antd/lib/locale/en_US';
import './index.less';
import './index.css';
import storage from '@/utils/storage';
import { IntlProvider } from 'react-intl';
import zh_CN from './locales/zh_CN';
import en_US from './locales/en_US';
function chooseMessage() {
	switch (storage.getLocal('language') || navigator.language.split('-')[0]) {
		case 'en':
			return en_US;
		case 'ch':
			return zh_CN;
		default:
			return zh_CN;
	}
}
function chooseLocale() {
	switch (storage.getLocal('language') || navigator.language.split('-')[0]) {
		case 'en':
			return enUS;
		case 'ch':
			return zhCN;
		default:
			return zhCN;
	}
}

// eslint-disable-next-line react/no-deprecated
ReactDOM.render(
	<Provider store={store}>
		<IntlProvider locale={navigator.language} messages={chooseMessage()}>
			<ConfigProvider locale={chooseLocale()}>
				<App />
			</ConfigProvider>
		</IntlProvider>
	</Provider>,
	document.getElementById('root')
);

/* 全局通用样式 */

html,
body,
#root {
	height: 100%;
	min-height: 100%;
	font-size: 12px;
	/* overflow: hidden; */
}
html,
body,
div,
span,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
pre,
a,
code,
img,
dl,
dt,
dd,
ol,
ul,
li,
form,
label,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
canvas,
figure,
figcaption,
footer,
header,
menu,
nav,
section,
audio,
video {
	margin: 0;
	padding: 0;
	border: 0;
	font-size: 100%;
	font: inherit;
}
body {
	line-height: 1;
}
ol,
ul {
	list-style: none;
}
a,
a:link,
a:visited,
a:hover,
a:active {
	text-decoration: none;
}
q {
	quotes: none;
}
q:before,
q:after {
	content: '';
	content: none;
}
table {
	border-collapse: collapse;
	border-spacing: 0;
}
body {
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>,
		'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji',
		'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}
code {
	font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
		monospace;
}
input {
	outline: none;
}
label {
	font-weight: normal;
}
p,
ul,
li {
	margin-bottom: 0;
}
::-webkit-input-placeholder {
	color: #c6c9cd;
}
::-moz-placeholder {
	color: #c6c9cd;
}
::-ms-input-placeholder {
	color: #c6c9cd;
}

.ascm-in-oneconsole .ascm-topbar {
	z-index: 999;
}
body ::-webkit-scrollbar {
	width: 4px;
	height: 4px;
	background-color: #eaedf0;
}
body ::-webkit-scrollbar-thumb {
	background-color: rgba(171, 182, 192, 0.5);
	border-radius: 4px;
}
/* body ::-webkit-scrollbar-thumb:hover {}
body ::-webkit-scrollbar-thumb:active {}
body ::-webkit-scrollbar-thumb:window-inactive {} */

/** 时间轴组件样式重写 */
.next-timeline-item-done .next-timeline-item-timeline {
	left: 20px;
}
.next-timeline-item-done .next-timeline-item-content {
	margin-left: 55px;
}
.next-timeline-item-done .next-timeline-item-timeline .next-timeline-item-tail {
	top: 35px;
	height: 60%;
}
.next-tabs {
	box-sizing: border-box;
	width: 100%;
	height: 100%;
}
.next-tabs-tabpane.active {
	visibility: visible;
	opacity: 1;
	height: 100%;
}
.next-tabs-content {
	overflow: visible;
	height: 100%;
}
/* .next-timeline-item-has-left-content{
	min-height: 100px;
}
.next-timeline-item-done.next-timeline-item-has-left-content > .next-timeline-item-timeline{
	margin-left: 90px;
	margin-top: 2px;
}
.next-timeline-item-done.next-timeline-item-has-left-content > .next-timeline-item-content{
	margin-left: 124px;
}
.next-timeline-item-done .next-timeline-item-timeline .next-timeline-item-tail{
	height: 50px;
	top: 42px;
}
.next-timeline-item-done .next-timeline-item-timeline .next-timeline-item-node.next-timeline-item-node-custom{
	margin-left: -16px !important;
} */
/** TimeSelect 特定样式覆盖 */
#timepicker .next-input:hover,
.next-input.next-focus {
	box-shadow: none !important;
}
#timepicker .next-select.next-active .next-select-inner {
	box-shadow: none !important;
}
#timepicker .next-select .next-select-inner {
	color: #1a1a1a;
}
.timepicker-filter-item {
	width: 100%;
}
/* .page-header .next-icon:hover {
  color: #226ee7;
} */

/** 常用显示 */
.display-flex {
	display: flex;
}
.display-inline-block {
	display: inline-block;
}
.flex-center {
	justify-content: center;
	align-items: center;
}
.flex-end{
	display: flex;
	justify-content: end;
}
.flex-space-between {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.flex-column {
	flex-direction: column;
}
.flex-warp {
	flex-wrap: wrap;
}
.flex-align {
	display: flex;
	align-items: center;
}

/** 常用布局 */
.relative {
	position: relative;
}

/** 表单布局 */
.form-layout {
	width: 100%;
}
.form-layout .form-li {
	padding: 12px 0;
}
.form-name {
	width: 140px;
	text-align: left;
	font-weight: 500 !important;
	color: #333;
	line-height: 32px;
	padding: 0 8px;
}
.form-content {
	flex: 1 1 0;
}
/** 动态表单布局 */
.w-50 {
	width: 50%;
}
.dynamic-form-name {
	text-align: left;
	font-weight: 500 !important;
	color: #252525;
	line-height: 32px;
	/* padding: 0 8px; */
}
.dynamic-summit-box {
	position: fixed;
	z-index: 799;
	bottom: 0;
	width: calc(100% - 256px);
	padding: 16px 16px 16px 0px;
	background: #fff;
	border-top: 1px solid #e3e4e6;
}
.dynamic-second-form-box {
	display: inline-block;
	margin-top: 16px;
	margin-bottom: 24px;
	padding: 16px 24px;
	background: #efefef;
}
.dynamic-switch-label {
	height: 24px;
	line-height: 24px;
}
.dynamic-form-node-affinity-content {
	display: inline-block;
	width: 260px;
	margin-left: 24px;
}
.dynamic-form-node-affinity-check {
	display: inline-block;
	margin-left: 16px;
}
.dynamic-form-node-tolerations-content {
	display: inline-block;
	width: 260px;
	margin-left: 24px;
}
/* Tab */
.next-tabs-tab-inner {
	font-size: 12px !important;
}
/** table */
table.table-list {
	width: 100%;
	/* border-radius:4px 4px 0px 0px; */
}
table.table-list thead tr th,
table.table-list thead tr td {
	font-weight: normal;
	border: none;
	padding: 12px;
	border-bottom: 1px solid #c0c6cc;
}
table.table-list thead tr th span,
table.table-list thead tr td span {
	display: inline-block;
	width: 22.6%;
}
table.table-list thead tr th span:nth-child(1) {
	width: 6%;
}
table.table-list thead tr th {
	background-color: #fbfcfd;
	color: #252525;
	font-weight: 500;
}
table.table-list tbody tr th,
table.table-list tbody tr td {
	padding: 12px;
	border: none;
	line-height: 150%;
	border-bottom: 1px solid #e3e4e6;
	display: inline-block;
	width: 33%;
}
table.table-list tbody .ant-space-item {
	padding: 12px;
	border: none;
	line-height: 150%;
	border-bottom: 1px solid #e3e4e6;
	border-bottom: 1px solid #e3e4e6 !important;
}
table.table-list tbody .ant-space-item label {
	width: 100%;
	display: flex;
	align-items: center;
}
table.table-list tbody .ant-space-item label span:nth-child(1) {
	width: 2.6%;
}
table.table-list tbody .ant-space-item label span:nth-child(2) {
	width: 97.4%;
}
table.table-list tbody .ant-space-item label span tr {
	display: flex;
	width: 100%;
}
table.table-list tbody .ant-space-item label span tr span {
	display: inline-block;
	width: 33% !important;
}
table.table-list tbody .ant-space-item label span tr span:nth-child(1) {
	width: 0% !important;
}

/** 星号前缀 */
.ne-required {
	position: relative;
}
.ne-required:before {
	content: '*';
	position: absolute;
	top: 9px;
	left: -9px;
	display: block;
	width: 0;
	height: 0;
	line-height: 0;
	color: #ff4d4f;
	font-size: 14px;
}
/** 星号前缀 - ingress*/
.ne-required-ingress {
	position: relative;
}
.ne-required-ingress:before {
	content: '*';
	position: absolute;
	top: 18px;
	left: -9px;
	display: block;
	width: 0;
	height: 0;
	line-height: 0;
	color: #ff4d4f;
	font-size: 14px;
}
/** 名称连接 */
.name-link {
	color: #226ee7;
	cursor: pointer;
}
.displayed-name {
	color: #cccccc;
	cursor: not-allowed;
}
.normal-name {
	color: #585858;
}
.name-disabled-link {
	color: #c0c6cc;
	cursor: pointer;
}
.red-name {
	color: #ff4d4f;
}

/** 分割线 */
.detail-divider {
	width: 100%;
	height: 1px;
	border-top: 1px solid #e3e4e6;
	margin: 24px 0;
}
.detail-divider-dash {
	width: 100%;
	height: 1px;
	border-top: 1px dashed #e3e4e6;
	margin: 8px 0 12px 0px;
}

/** 竖分割线 */
.vertical-divider {
	width: 1px;
	height: 100%;
	border-left: 1px solid #e3e4e6;
}

/** 常用外边距 */
.mt-8 {
	margin-top: 8px;
}
.mt-16 {
	margin-top: 16px;
}
.mr-8 {
	margin-right: 8px;
}
.mr-12 {
	margin-right: 12px;
}
.mr-16 {
	margin-right: 16px;
}
.mr-24 {
	margin-right: 24px;
}
.mb-8 {
	margin-bottom: 8px;
}
.mb-16 {
	margin-bottom: 16px;
}
.mb-24 {
	margin-bottom: 24px;
}
.ml-12 {
	margin-left: 12px;
}
.ml-24 {
	margin-left: 24px;
}
/** react-codemirror2自适应高度 */
.CodeMirror {
	height: auto;
}
.log-codeMirror .CodeMirror {
	height: 650px;
}
.CodeMirror-scroll {
	overflow: auto !important;
}

/** 服务日志全屏 */
.log-full-screen {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 1000;
	border-radius: 0;
	margin: 0 !important;
}
.log-full-screen .log-codemirror {
	height: 100vh;
}
.log-full-screen .CodeMirror {
	height: 100vh;
}

/*  */
.middleware-line {
	flex: 1;
	width: 1px;
	height: 45px;
	border-left: 1px solid #e3e4e6;
	margin-top: 12px;
}
/* .next-table td .next-table-cell-wrapper {
  overflow: auto !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 5 !important;
  -webkit-box-orient: vertical !important;
} */
.mid-table-col {
	overflow: hidden;
	text-overflow: ellipsis;
}
.table-col-w146-h2 {
	width: 168px;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box; /*必须结合的属性 ，将对象作为弹性伸缩盒子模型显示 。*/
	-webkit-line-clamp: 2; /*用来限制在一个块元素显示的文本的行数。*/
	-webkit-box-orient: vertical; /*必须结合的属性 ，设置或检索伸缩盒对象的子元素的排列方式 。*/
}
.hidden {
	width: auto;
	position: absolute;
	z-index: -999;
	top: 0;
	visibility: hidden;
	opacity: 0;
}
.next-search.next-normal .next-after .next-btn-normal {
	min-width: 32px !important;
}

@font-face {
	font-family: 'iconfont'; /* Project id 989715 */
	src: url('./assets/iconfont.ttf') format('truetype');
}
.next-badge .next-badge-scroll-number {
	top: 0px !important;
}

/* *修改图片颜色 */
.grey-img {
	filter: grayscale(10);
}
/* * 修改页码总数字体颜色 */
.next-pagination-total {
	color: #555555 !important;
}
#audit-cas .next-input.next-medium .next-input-label {
	padding-left: 0px !important;
}
#audit-cas .next-select .next-select-inner {
	background-color: #fbfcfd !important;
}
#audit-cas .next-select .next-select-inner:hover {
	box-shadow: none;
}
#audit-cas .next-select.next-active .next-select-inner {
	box-shadow: none;
}
/* * 主备标识符 */
.gray-circle {
	width: 24px;
	height: 24px;
	border-radius: 12px;
	background: #dedede;
	color: #333333;
	text-align: center;
	line-height: 24px;
	margin-right: 4px;
}
.blue-circle {
	width: 24px;
	height: 24px;
	border-radius: 12px;
	background: #e1eeff;
	color: #0668c9;
	text-align: center;
	line-height: 24px;
	margin-right: 4px;
}
/* 离线字体 */
/* @font-face {
  font-family: NextIcon;
  src: url("./assets/roboto-bold.woff2");
  src: url("./assets/roboto-medium.woff2");
  src: url("./assets/roboto-regular.woff2");
  src: url("./assets/wind-font-2.woff2");
  src: url("./assets/wind-font.woff2");
}
.next-icon {
	display: inline-block;
	font-family: NextIcon !important;
	font-style: normal;
	font-weight: normal;
	text-transform: none;
} */

.text-hidden {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
.icon-type-content {
	display: flex;
	align-items: center;
}
.icon-type-content > img {
	margin-right: 4px;
}
.next-table-filter-footer {
	margin: 10px 10px 10px;
}

.text-overflow {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
.text-overflow-2 {
	overflow: hidden;
	text-overflow: ellipsis;
	word-wrap: break-word; /*单词 & 数字换行*/
	display: -webkit-box; /*必须结合的属性 ，将对象作为弹性伸缩盒子模型显示 。*/
	-webkit-line-clamp: 2; /*用来限制在一个块元素显示的文本的行数。*/
	-webkit-box-orient: vertical; /*必须结合的属性 ，设置或检索伸缩盒对象的子元素的排列方式 。*/
}
.text-overflow-one {
	max-height: 20px;
	overflow: hidden;
	text-overflow: ellipsis;
	word-break: break-all;
	display: -webkit-box; /*必须结合的属性 ，将对象作为弹性伸缩盒子模型显示 。*/
	-webkit-line-clamp: 1; /*用来限制在一个块元素显示的文本的行数。*/
	-webkit-box-orient: vertical; /*必须结合的属性 ，设置或检索伸缩盒对象的子元素的排列方式 。*/
}
.cursor-pointer {
	cursor: pointer;
}

.mid-codemirror .CodeMirror {
	width: 900px;
}
.CodeMirror-merge-2pane .CodeMirror-merge-pane {
	width: 50% !important;
	height: 100%;
	position: absolute;
}
.CodeMirror-merge-2pane .CodeMirror-merge-gap {
	display: none;
}
.CodeMirror-merge,
.CodeMirror-merge .CodeMirror {
	min-height: 350px;
	height: 100%;
}
.CodeMirror-merge {
	border: none;
	height: 100%;
}
.CodeMirror-merge-r-chunk {
	background: #444023;
}
.CodeMirror-merge-r-chunk-start {
	border-top: 1px solid #444023;
}
.CodeMirror-merge-r-chunk-end {
	border-bottom: 1px solid #444023;
}

.mid-table-col {
	overflow: hidden;
	text-overflow: ellipsis;
}
.table-col-w146-h2 {
	width: 168px;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box; /*必须结合的属性 ，将对象作为弹性伸缩盒子模型显示 。*/
	-webkit-line-clamp: 2; /*用来限制在一个块元素显示的文本的行数。*/
	-webkit-box-orient: vertical; /*必须结合的属性 ，设置或检索伸缩盒对象的子元素的排列方式 。*/
}
.hidden {
	width: auto;
	position: absolute;
	z-index: -999;
	top: 0;
	visibility: hidden;
	opacity: 0;
}

/* 自定义标签样式 */
.blue-tip {
	display: inline-block;
	padding: 2px;
	height: 20px;
	max-width: 250px;
	line-height: 15px;
	border-radius: 2px;
	font-weight: 400;
	font-size: 12px;
	background: #c7ecff;
	border: 1px solid #94dbff;
	color: #00a7fa;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.orange-tip {
	display: inline;
	padding: 2px;
	height: 20px;
	line-height: 20px;
	border-radius: 2px;
	font-weight: 400;
	font-size: 12px;
	background: #ffecc7;
	border: 1px solid #ffdb94;
	color: #faa700;
}
.red-tip {
	display: inline;
	padding: 2px;
	height: 20px;
	line-height: 20px;
	border-radius: 2px;
	font-weight: 400;
	font-size: 12px;
	background: #fff1f0;
	border: 1px solid #ffa39e;
	color: #ff4d4f;
}
.role-tips-more {
	width: 12px;
	height: 22px;
	line-height: 22px;
	text-align: center;
	background-color: #f7f9fa;
	cursor: pointer;
	margin-left: 4px;
}
.next-search.next-normal .next-after .next-btn-normal {
	margin-left: 0px !important;
}
/*
.next-menu-item .next-menu-item-text{
	font-size: 12px !important;
} */
.table-row-delete {
	background: #f8f8f9;
	color: #cccccc;
}
.table-row-delete > .ant-table-cell-fix-left,
.ant-table-cell-fix-right {
	background: #f8f8f9;
}
.table-row-topping {
	background: #f8f8f9;
}
.table-row-topping > .ant-table-cell-fix-left,
.ant-table-cell-fix-right {
	background: #f8f8f9;
}
.ant-menu-item {
	margin-top: 0px !important;
	margin-bottom: 0px !important;
}
/* .CodeMirror-gutter-wrapper {
	left: -38px !important;
} */
.zeus-nav > .ant-popover-content {
	width: 150px;
}
.zeus-nav
	> .ant-popover-content
	> .ant-popover-inner
	> .ant-popover-inner-content {
	padding: 0px !important;
}
/* * 小圆点状态显示 */
.circle-success-status,
.circle-failed-status {
	position: relative;
}
.circle-success-status::before,
.circle-failed-status::before {
	content: '';
	position: absolute;
	top: 6.5px;
	left: 5px;
	width: 8px;
	height: 8px;
	border-radius: 4px;
	background-color: #ff4d4f;
}
.circle-success-status::before {
	background-color: #52c41a;
}
@media screen and (max-width: 1280px) {
	body {
		zoom: 0.91;
	}
}
.site-input-group-wrapper .site-input-split {
	background-color: #fff;
}

.site-input-group-wrapper .site-input-right {
	border-left-width: 0;
}

.site-input-group-wrapper .site-input-right:hover,
.site-input-group-wrapper .site-input-right:focus {
	border-left-width: 1px;
}

.site-input-group-wrapper .ant-input-rtl.site-input-right {
	border-right-width: 0;
}

.site-input-group-wrapper .ant-input-rtl.site-input-right:hover,
.site-input-group-wrapper .ant-input-rtl.site-input-right:focus {
	border-right-width: 1px;
}
.service-off-line {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 0 8px;
	height: 22px;
	border-radius: 2px;
	border: 1px solid rgba(229, 229, 229, 1);
	color: rgba(151, 151, 151, 1);
	background-color: rgba(229, 229, 229, 1);
}
.service-on-line {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 0 8px;
	height: 22px;
	border-radius: 2px;
	border: 1px solid rgba(234, 255, 143, 1);
	background-color: rgba(244, 255, 184, 1);
	color: rgba(124, 179, 5, 1);
}

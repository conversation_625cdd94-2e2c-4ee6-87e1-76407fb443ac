import { SET_MENU, SET_ALL_MENU, SET_BUTTON_LIST } from './auth';
const defaultState = {
	menu: [],
	allMenu: [],
	buttonList: []
};

export default function authReducer(state = defaultState, action: any) {
	const { type, data } = action;

	switch (type) {
		case SET_MENU:
			return { ...state, menu: data };
		case SET_ALL_MENU:
			return { ...state, allMenu: data };
		case SET_BUTTON_LIST:
			return { ...state, buttonList: data };
		default:
			return state;
	}
}

import { createStore, combineReducers, applyMiddleware, compose } from 'redux';
import thunk from 'redux-thunk';
import log from './log/logReducer';
import param from './param/paramReducer';
import execute from './execute/executeReducer';
import organization from './organization/organizationReducer';
import auth from './auth/authReducer';

const store = createStore(
	combineReducers({ log, param, execute, organization, auth }),
	compose(applyMiddleware(thunk))
);

export default store;

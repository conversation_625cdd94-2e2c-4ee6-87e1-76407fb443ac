export const SET_ORG_CLUSTER = 'SET_ORG_CLUSTER';
export const SET_ORG_CLUSTERS = 'SET_ORG_CLUSTERS';
export const SET_PRO_CLUSTER = 'SET_PRO_CLUSTER';
export const SET_PRO_CLUSTERS = 'SET_PRO_CLUSTERS';
export const SET_EDIT_CLUSTER = 'SET_EDIT_CLUSTER';
export const DELETE_CLUSTER_BY_ID = 'DELETE_CLUSTER_BY_ID';
export const DELETE_PRO_CLUSTER_BY_ID = 'DELETE_PRO_CLUSTER_BY_ID';

export function deleteProClusterById(data) {
	return (dispatch) => {
		dispatch({
			type: DELETE_PRO_CLUSTER_BY_ID,
			data: data
		});
	};
}
export function deleteClusterById(data) {
	return (dispatch) => {
		dispatch({
			type: DELETE_CLUSTER_BY_ID,
			data: data
		});
	};
}
export function setOrgCluster(data) {
	return (dispatch) => {
		dispatch({
			type: SET_ORG_CLUSTER,
			data: data
		});
	};
}
export function setOrgClusters(data) {
	return (dispatch) => {
		dispatch({
			type: SET_ORG_CLUSTERS,
			data: data
		});
	};
}
export function setProCluster(data) {
	return (dispatch) => {
		dispatch({
			type: SET_PRO_CLUSTER,
			data: data
		});
	};
}
export function setProClusters(data) {
	return (dispatch) => {
		dispatch({
			type: SET_PRO_CLUSTERS,
			data: data
		});
	};
}
export function setEditCluster(data) {
	return (dispatch) => {
		dispatch({
			type: SET_EDIT_CLUSTER,
			data: data
		});
	};
}

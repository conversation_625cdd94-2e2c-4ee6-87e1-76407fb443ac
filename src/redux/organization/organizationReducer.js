import {
	SET_ORG_CLUSTER,
	SET_ORG_CLUSTERS,
	SET_PRO_CLUSTER,
	SET_PRO_CLUSTERS,
	SET_EDIT_CLUSTER,
	DELETE_CLUSTER_BY_ID,
	DELETE_PRO_CLUSTER_BY_ID
} from './organization';

const defaultState = {
	clusters: [],
	proClusters: [],
	editCluster: {}
};
export default function organizationReducer(state = defaultState, action) {
	const { type, data } = action;
	switch (type) {
		case SET_ORG_CLUSTER:
			return { ...state, clusters: [data, ...state.clusters] };
		case SET_ORG_CLUSTERS:
			return { ...state, clusters: data };
		case SET_PRO_CLUSTER:
			return { ...state, proClusters: [data, ...state.proClusters] };
		case SET_PRO_CLUSTERS:
			return { ...state, proClusters: data };
		case SET_EDIT_CLUSTER:
			return { ...state, editCluster: data };
		case DELETE_PRO_CLUSTER_BY_ID:
			return {
				...state,
				proClusters: state.proClusters.filter(
					(item) => item.clusterId !== data
				)
			};
		case DELETE_CLUSTER_BY_ID:
			return {
				...state,
				clusters: state.clusters.filter(
					(item) => item.clusterId !== data
				)
			};
		default:
			return state;
	}
}

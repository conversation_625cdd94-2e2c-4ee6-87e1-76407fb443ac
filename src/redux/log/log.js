export const SET_REAL_LOG = 'SET_REAL_LOG';
export const CLEAN_REAL_LOG = 'CLEAN_REAL_LOG';
export const SET_PVC_LOG = 'SET_PVC_LOG';
export const CLEAN_PVC_LOG = 'CLEAN_PVC_LOG';
export const SET_FINISH_LOG = 'SET_FINISH_LOG';

export function setRealLog(log) {
	return (dispatch) => {
		dispatch({
			type: SET_REAL_LOG,
			data: log
		});
	};
}
export function cleanRealLog() {
	return (dispatch) => {
		dispatch({
			type: CLEAN_REAL_LOG
		});
	};
}
export function setPvcLog(pvcLog) {
	return (dispatch) => {
		dispatch({
			type: SET_PVC_LOG,
			data: pvcLog
		});
	};
}
export function cleanPvcLog() {
	return (dispatch) => {
		dispatch({
			type: CLEAN_PVC_LOG
		});
	};
}
export function setPvcFinishLog(pvcLog) {
	return (dispatch) => {
		dispatch({
			type: SET_FINISH_LOG,
			data: pvcLog
		});
	};
}

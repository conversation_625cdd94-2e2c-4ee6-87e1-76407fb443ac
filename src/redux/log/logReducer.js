import {
	SET_REAL_LOG,
	CLEAN_REAL_LOG,
	SET_PVC_LOG,
	CLEAN_PVC_LOG,
	SET_FINISH_LOG
} from './log';

const defaultState = {
	log: ``,
	pvcLog: []
};
export default function logReducer(state = defaultState, action) {
	const { type, data } = action;
	switch (type) {
		case SET_REAL_LOG:
			return { ...state, log: state.log + data };
		case CLEAN_REAL_LOG:
			return { ...state, log: '' };
		case SET_PVC_LOG:
			return { ...state, pvcLog: [...data] };
		case CLEAN_PVC_LOG:
			return { ...state, pvcLog: [] };
		case SET_FINISH_LOG:
			return { ...state, pvcLog: [...state.pvcLog, ...data] };
		default:
			return state;
	}
}

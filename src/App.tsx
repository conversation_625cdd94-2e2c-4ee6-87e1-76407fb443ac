import React, { useEffect } from 'react';
import Layout from './layouts/myLayout';

function App(): JSX.Element {
	useEffect(() => {
		JSON.parse(localStorage.getItem('personalization') as string)
			?.tabLogo &&
			change_icon(
				JSON.parse(localStorage.getItem('personalization') as string)
					?.tabLogo
			);
		document.title =
			JSON.parse(localStorage.getItem('personalization') as string)
				?.title || 'Zeus';
	}, []);

	return <Layout />;
}

function change_icon(iconUrl: any) {
	const changeFavicon = (link: any) => {
		let $favicon: any = document.querySelector('link[rel="icon"]');
		if ($favicon !== null) {
			$favicon.href = link;
		} else {
			$favicon = document.createElement('link');
			$favicon.rel = 'icon';
			$favicon.href = link;
			document.head.appendChild($favicon);
		}
	};

	// 动态修改网站图标
	changeFavicon(iconUrl);
}

export default App;

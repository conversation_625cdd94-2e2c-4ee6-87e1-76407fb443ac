const { createProxyMiddleware } = require('http-proxy-middleware');
const { apiUrl, userUrl, wsUrl } = require('./utils/url');

// 配置代理后端IP
module.exports = function (app) {
	app.use(
		createProxyMiddleware('/zeusapi', {
			target: apiUrl,
			changeOrigin: true,
			pathRewrite: {
				'^/zeusapi': ''
			},
			secure: false
		})
	);
	app.use(
		createProxyMiddleware('/zeususer', {
			target: userUrl,
			changeOrigin: true,
			pathRewrite: {
				'^/zeususer': ''
			},
			secure: false
		})
	);
	app.use(
		createProxyMiddleware('/api/ws', {
			target: wsUrl,
			changeOrigin: true,
			ws: true,
			secure: false
		})
	);
};

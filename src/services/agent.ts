import Axios from './request.js';
import * as Agent from './agent.constant';
import { resProps } from '@/types/comment.js';

export const getAgents: (params?: {
	clusterId?: string;
	namespace?: string;
	organ?: string;
}) => Promise<GetAgentsRes> = (params?: {
	clusterId?: string;
	namespace?: string;
	organ?: string;
}) => {
	return Axios.get(Agent.getAgents, params);
};
export const getAgentOrder = (params: {
	clusterId: string;
	agentPort: string;
	arch: string;
	osType: string;
	organ?: string;
}) => {
	return Axios.get(Agent.getAgentOrder, params);
};
export const deleteAgent: (params: {
	clusterId: string;
	name: string;
}) => Promise<resProps> = (params: { clusterId: string; name: string }) => {
	return Axios.delete(Agent.deleteAgent, params);
};
export const getAgentControllers = (params?: { organId: string }) => {
	return Axios.get(Agent.getAgentControllers);
};
export const getOrganAgentList: (params: {
	organId: string;
}) => Promise<GetAgentsRes> = (params: { organId: string }) => {
	return Axios.get(Agent.getOrganAgentList, params);
};
export const getProjectAgentList: (params: {
	organId: string;
	projectId: string;
	clusterId?: string;
}) => Promise<GetAgentsRes> = (params: {
	organId: string;
	projectId: string;
	clusterId?: string;
}) => {
	return Axios.get(Agent.getProjectAgentList, params);
};
export const checkStatus = (params: any) => {
	return Axios.json(Agent.checkStatus, params, {}, 'POST');
};
export const getAbilityDefault = (params: { type: string }) => {
	return Axios.get(Agent.getAbilityDefault, params);
};
export const deleteOrganAgent = (params: {
	agentName: string;
	clusterId: string;
	organId: string;
}) => {
	return Axios.delete(Agent.deleteOrganAgent, params);
};
export const deleteProjectAgent = (params: {
	organId: string;
	projectId: string;
	agentName: string;
	clusterId: string;
}) => {
	return Axios.delete(Agent.deleteProjectAgent, params);
};

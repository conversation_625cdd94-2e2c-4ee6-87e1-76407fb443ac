import Axios from './request.js';
import * as BACKUP from './backup.constants';

interface listParams {
	clusterId: string;
	namespace: string;
	middlewareName?: string;
	type: string;
	[propName: string]: any;
}
interface backupParams {
	clusterId?: string;
	namespace?: string;
	middlewareName?: string;
	type?: string;
	backupName?: string;
	backupId?: string;
	backupMode?: string;
	restoreName?: string;
	middlewares?: any;
}

interface backupCheckParams {
	clusterId: string;
	namespace: string;
	middleware?: string;
	type: string;
	backupType: string;
	backupName?: string;
	backupId?: string;
	backupMode?: string;
	middlewareName?: string;
}
interface addressParams {
	accessKeyId: string;
	capacity: string;
	clusterIds: string[];
	endpoint: string;
	id?: number;
	name: string;
	secretAccessKey: string;
	type: string;
	[propName: string]: any;
}
export const getBackups = (params: any) => {
	return Axios.get(BACKUP.backupList, params);
};
export const getIncrBackups = (params: any) => {
	return Axios.get(BACKUP.incrBackupList, params);
};
export const getRestoreList = (params: any) => {
	return Axios.get(BACKUP.getRestoreList, params);
};
export const deleteRestoreList = (params: any) => {
	return Axios.delete(BACKUP.getRestoreList, params);
};
export const backupNow = (params: listParams) => {
	return Axios.post(BACKUP.useBackup, params);
};
export const getBackupConfig = (params: listParams) => {
	return Axios.get(BACKUP.backups, params);
};
export const addBackupConfig = (params: listParams) => {
	return Axios.json(BACKUP.backups, params, {}, 'POST');
};
export const updateBackupConfig = (params: listParams) => {
	return Axios.put(BACKUP.backups, params);
};
export const deleteBackupConfig = (params: listParams) => {
	return Axios.delete(BACKUP.backups, params);
};
export const deleteBackups = (params: backupParams) => {
	return Axios.delete(BACKUP.backupList, params);
};
export const applyBackup = (params: any) => {
	return Axios.json(BACKUP.useBackup, params, {}, 'POST');
};
export const getBackupAddress = (params: { keyword: string }) => {
	return Axios.get(BACKUP.backupAddress, params);
};
export const getBackupAddressDetail = (params: { id: number }) => {
	return Axios.get(BACKUP.backupAddressDetail, params);
};
export const addBackupAddress = (params: addressParams) => {
	return Axios.json(BACKUP.backupAddress, params, {}, 'POST');
};
export const backupAddressCheck = (params: addressParams) => {
	return Axios.json(BACKUP.backupAddressCheck, params, {}, 'POST');
};
export const editBackupAddress = (params: addressParams) => {
	return Axios.json(BACKUP.backupAddress, params, {}, 'PUT');
};
export const deleteBackupAddress = (params: {
	id: number;
	clusterId?: string;
}) => {
	return Axios.delete(BACKUP.backupAddress, params);
};
export const getMiddlewares = (params: {
	clusterId: string;
	organId: string;
}) => {
	return Axios.get(BACKUP.organOperator, params);
};
export const getServiceList = (params?: any) => {
	return Axios.get(BACKUP.serviceList, params);
};
export const getBackupTasks = (params: any) => {
	return Axios.get(BACKUP.backupTask, params);
};
export const editBackupTasks = (params: any) => {
	return Axios.json(BACKUP.backupTask, params, {}, 'PUT');
};
export const deleteBackupTasks = (params: any) => {
	return Axios.json(BACKUP.backupTask, params, {}, 'DELETE');
};

export const addIncBackup = (params: any) => {
	return Axios.post(BACKUP.getInc, params);
};

export const editIncBackup = (params: any) => {
	return Axios.put(BACKUP.editInc, params);
};

export const getIncBackup = (params: any) => {
	return Axios.get(BACKUP.getInc, params);
};

// * 查询备份服务器
export const getServer = (params: any) => {
	return Axios.json(BACKUP.serverList, params, {}, 'POST');
};
// * 创建备份服务器
export const addServer = (params: any) => {
	return Axios.json(BACKUP.server, params, {}, 'POST');
};
// *更新备份服务器
export const editServer = (params: any) => {
	return Axios.json(BACKUP.server, params, {}, 'PUT');
};
// * 删除备份服务器
export const removeServer = (params: any) => {
	return Axios.delete(BACKUP.deleteServer, params);
};
// * 分配备份服务器给集群
export const allocate = (params: any) => {
	return Axios.json(BACKUP.allocate, params, {}, 'PUT');
};
// * 查询备份服务器数量信息
export const getCount = () => {
	return Axios.get(BACKUP.count);
};
// * 查询项目可用备份服务器(1个备份服务器只能被1个项目创建1个备份位置)
export const getEnable = (params: any) => {
	return Axios.get(BACKUP.enable, params);
};

// * 创建备份位置
export const addPosition = (params: any) => {
	return Axios.json(BACKUP.position, params, {}, 'POST');
};
// *更新备份位置
export const editPosition = (params: any) => {
	return Axios.json(BACKUP.position, params, {}, 'PUT');
};
// * 删除备份位置
export const removePosition = (params: any) => {
	return Axios.delete(BACKUP.deletePosition, params);
};
// * 查询集群分区可用备份位置列表
export const getClusterPosition = (params: any) => {
	return Axios.get(BACKUP.getClusterPosition, params);
};
// * 查询项目备份位置列表
export const getProjectPosition = (params: backupParams) => {
	return Axios.get(BACKUP.getProjectPosition, params);
};
// * 查询中间件是否已经创建周期备份
export const checkSchedule = (params: backupParams) => {
	return Axios.get(BACKUP.checkSchedule, params);
};
// * 查询多选中间件是否已经创建周期备份
export const checkScheduleBatch = (params: backupParams) => {
	return Axios.json(BACKUP.checkSchedule, params);
};
// * 查询备份任务详情
export const getTaskDetail = (params: backupParams) => {
	return Axios.get(BACKUP.taskDetail, params);
};
// * 查询备份记录详情
export const getTaskProgress = (params: backupParams) => {
	return Axios.get(BACKUP.getTaskProgress, params);
};
// * 查询克隆记录详情
export const getTaskRestore = (params: backupParams) => {
	return Axios.get(BACKUP.getTaskRestore, params);
};
// * 查询某个组织下拥有运维权限的operator
export const getProjectOperator = (params: {
	organId: string;
	projectId: string;
}) => {
	return Axios.get(BACKUP.projectOperator, params);
};
// * 备份数据有效性-再次验证
export const backupCheck = (params: backupCheckParams) => {
	return Axios.json(BACKUP.singleBackupcheck, params, {}, 'POST');
};
// * 备份数据有效性-再次验证结果
export const getBackupCheckResult = (params: backupCheckParams) => {
	return Axios.get(BACKUP.singleBackupcheck, params);
};
export const backupCheckResult = (params: backupCheckParams) => {
	return Axios.get(BACKUP.singleBackupcheckResult, params);
};
export const periodBackupCheckResult = (params: {
	clusterId: string;
	namespace: string;
	backupId: string;
}) => {
	return Axios.get(BACKUP.periodBackupcheckResult, params);
};

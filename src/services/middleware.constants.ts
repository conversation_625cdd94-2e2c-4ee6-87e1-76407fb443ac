import { api } from '@/api.json';

export const middlewares = `${api}/middlewares/info`;
export const nodePorts = `${api}/clusters/{clusterId}/nodes/labels`;
export const nodeTaints = `${api}/clusters/{clusterid}/nodes/taints`;
export const storageClasses = `${api}/clusters/{clusterId}/storageclasses`;
export const middleware = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares`;
export const middlewareBatch = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/batch`;
// * 获取中间件详情 / 修改日志开关状态
export const getMiddlewareDetail = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}`;
// * 修改中间件
export const updateMiddleware = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}`;
// * 查询pod列表
export const getPods = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/topology`;
// * 重启pod
export const restartPod = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/pods/{podName}/restart`;
export const middlewareName = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}`;
// * 查询备份数据 & 立即备份
export const backups = `${api}/clusters/{clusterId}/middlewares/mysql/{mysqlName}/backups`;
// * 删除备份
export const deleteBackup = `${api}/clusters/{clusterId}/middlewares/mysql/{mysqlName}/backups/{backupName}`;
// * 查询定时备份配置
export const getBackupConfig = `${api}/clusters/{clusterId}/middlewares/mysql/{mysqlName}/backups/schedule`;
// * 创建定制备份
export const createTimingBackup = `${api}/clusters/{clusterId}/middlewares/mysql/{mysqlName}/backups/schedule`;
// * 中间件切换/手动切换
export const switchMiddleware = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/switch`;
// * 中间件切换/自动切换
export const autoSwitchMiddleware = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/autoSwitch`;
// * pg主从切换
export const pgSwitchHover = `${api}/clusters/{clusterId}/namespace/{namespace}/middlewares/{middlewareName}/postgresql/switchover`;
// * 性能监控
export const middlewareMonitor = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/monitor`;
// * 服务事件
export const middlewareEvents = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/events`;
// * 查询已设置的告警规则-服务
export const getUsedAlarmRules = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/alert/rules/used`;
// * 查询已设置的告警规则-系统
export const getUsedAlarmRule = `${api}/clusters/{clusterId}/rules/used`;
// * 查询可以设置的告警规则
export const getCanUseAlarmRules = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/alert/rules`;
// * 创建告警规则-服务
export const addAlarmRules = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/alert/rules`;
// * 创建、修改告警规则-系统
export const addAlarmRule = `${api}/clusters/{clusterId}/rules/system`;
// * 删除告警规则-服务
export const deleteAlarmRules = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/alert/rules/{alertName}`;
// * 删除告警规则-系统
export const deleteAlarmRule = `${api}/clusters/{clusterId}/rules/system`;
// * 更新告警规则-服务
export const updateAlarmRules = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/alert/rules/{alertName}`;
// * 获取告警规则详情
export const getAlarmDetail = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/alert/rules/{alertName}`;
// * 获取自定义配置
export const getCustomConfig = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/config`;
// * 更新自定义配置
export const updateCustomConfig = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/config`;
// * 获取自定义配置修改记录
export const getConfigHistory = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/config/history`;
// * 获取参数模板列表
export const getParamTemplate = `${api}/middlewares/template`;
// * 获取参数模板列表详情
export const getParamTemplateDetail = `${api}/middlewares/template/{templateName}`;
// * 获取慢日志
export const getSlowLog = `${api}/clusters/{clusterId}/middlewares/mysql/{middlewareName}/slowsql`;
// * 动态表单接口获取
export const getDynamicForm = `${api}/clusters/{clusterId}/dynamic`;
// * 获取pvc数据
export const getPVC = `${api}/clusters/{clusterId}/namespaces/{namespace}/pvc`;
// * 获取secret数据
export const getSecret = `${api}/clusters/{clusterId}/namespaces/{namespace}/secret`;
// * 获取标准日志文件
export const getContainerFiles = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/applogs/filenames`;
// * 获取单个日志的文件详情
export const getLogDetails = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/applogs`;
// * 导出日志
export const downloadLog = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/applogs/export`;
// * 日志文件导出
export const downloadLogFile = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/pods/{podName}/log/export`;
// * 获取日志目录
export const getLogIndex = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/applogs/queryLogfiles`;
// * 添加灾备服务
export const addDisasterInstance = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}`;
// * 灾备切换
export const switchDisasterInstance = `${api}/clusters/{clusterId}/middlewares/mysql/{mysqlName}/disasterRecovery`;
// * 灾备解绑
export const unBindDisasterInstance = `${api}/clusters/{clusterId}/middlewares/mysql/{middlewareName}/disasterRecovery`;
// * 获取mysql对外访问信息
export const getMysqlExternal = `${api}/clusters/{clusterId}/middlewares/mysql/{mysqlName}/queryAccessInfo`;
// * 重启服务
export const restartService = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/reboot`;
// * 查看pod yaml
export const getPodYaml = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/pods/{podName}/yaml`;
// * value.yaml
export const valueYamlApi = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/values`;
// * 存储扩容
export const dilatationStorage = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/storage`;
// * 查询可发布的中间件信息
export const getCanReleaseMiddleware = `${api}/middlewares/info/{type}`;
// * 置顶/取消置顶参数
export const topConfigParam = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/config/{configName}/top`;
// * 创建数据库用户
export const createUser = `${api}/clusters/{clusterId}/middlewares/mysql/createUser`;
// * 查询数据库用户列表
export const listUser = `${api}/clusters/{clusterId}/middlewares/mysql/listUser`;
// * 创建数据库/更改用户备注
export const createDb = `${api}/clusters/{clusterId}/middlewares/mysql/createDb`;
// * 查询mysql字符集
export const listCharset = `${api}/clusters/{clusterId}/middlewares/mysql/listCharset`;
// * 查询数据库列表
export const listDb = `${api}/clusters/{clusterId}/middlewares/mysql/listDb`;
// * 删除数据库
export const deleteDb = `${api}/clusters/{clusterId}/middlewares/mysql/deleteDb`;
// * 更改数据库备注
export const updateDb = `${api}/clusters/{clusterId}/middlewares/mysql/updateDb`;
// * 修改用户
export const grantUser = `${api}/clusters/{clusterId}/middlewares/mysql/grantUser`;
// * 修改密码
export const updatePassword = `${api}/clusters/{clusterId}/middlewares/mysql/updatePassword`;
// * 删除用户
export const deleteUser = `${api}/clusters/{clusterId}/middlewares/mysql/deleteUser`;
// * sql审计
export const queryAuditSql = `${api}/clusters/{clusterId}/middlewares/mysql/queryAuditSql`;
// * 增删改查kv
export const redis = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/redis`;
// * 获取可用区key
export const getKey = `${api}/area/keys`;
// * 获取可用区tolerations
export const getTolerations = `${api}/area/tolerations`;
// * 获取当前从节点绑定主节点
export const getMasterName = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/redis/burstMaster`;
// * 获取redis主从关系列表
export const getBurstList = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/redis/burstList`;
// * 获取中间件切换信息
export const getSwitch = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/switch`;
// * 获取中间件切换信息
export const getManualSwitch = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/manualSwitch`;
// * 中间件pvc获取
export const getMiddlewarePVC = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/storage`;
// * pvc 日志获取
export const getPVClog = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/storage/{pvcName}/event`;
// * pvc 扩容
export const scalePVCStorage = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/storage/{pvcName}/scale`;
// * pvc 回滚
export const rollbackPVCStorage = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/storage/{pvcName}/rollBack`;
// * 节点迁移 / 获取节点迁移状态
export const migrate = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/pods/migrate`;
// * 本地节点迁移
export const migrateLocal = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/pods/migrateLocal`;
// * 获取节点迁移列表
export const getNodes = `${api}/clusters/{clusterId}/nodes/zone`;
// * 删除节点迁移失败状态
export const deleteMigrate = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/pods/migrate`;
// * 查询审计日志
export const getAuditSql = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/applogs/audit`;
// * 查询服务全部资源
export const getYamlResource = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/resources`;
// * 查看yaml
export const getYaml = `${api}/yaml/view`;
// * 获取服务参数配置可选的节点类型
export const getConfigRole = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/config/role`;
// * 中间件pod group信息
export const getPodGroupsStatus = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/pods/group`;
// * 中间件重名接口校验
export const getMiddlewareNameCheck = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/checkExist`;
// * 获取redis灾备信息/故障恢复/灾备关系解绑
export const getDisaster = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/disasterRecovery`;
// * 灾备切换
export const switchServiceDisaster = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/disasterRecovery/switch`;
// * 取消切换
export const cancelSwitch = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/disasterRecovery/cancelSwitch`;
// * 创建灾备关系
export const createDisaster = `${api}/clusters/{oldClusterId}/namespaces/{oldNamespace}/middlewares/{middlewareName}/disasterRecovery`;
// * 获取重新散列状态 / 重新散列
export const getReBalanceStatus = `${api}/clusters/{clusterId}/namespaces/{namespace}/kafka/{name}/rebalanced`;
// * 查询额外参数库、添加额外参数库
export const getParam = `${api}/type/{middlewareType}/param`;
// * 更新额外参数库、删除额外参数库
export const editParam = `${api}/type/{middlewareType}/param/{paramName}`;
// * 添加应用额外参数
export const addition = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/config/addition`;
// * 删除额外参数
export const deleteAddition = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/config/addition/{paramName}`;
//* 获取服务灾备信息
export const getDisasterService = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/disasterRecovery/list`;
// * 查询中间件运维能力列表/新增/更新运维能力
export const getOperators = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/devopsAbility`;
// * 执行/删除运维能力
export const executeOperator = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/devopsAbility/{devopsAbilityName}`;
// * 查看纳入虚拟机的服务操作审计
export const getServerAudit = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/sql/actionHistory`;
// * 删除实例
export const uninstallInstance = `${api}/clusters/{clusterId}/instance/{instanceName}`;
// * 新增实例
export const addInstance = `${api}/clusters/{clusterId}/instance`;
// * 服务上/下线
export const lockService = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/lock`;
// * 查询、保存实例日志文件路径
export const getLogPath = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/pods/{podName}/log`;
// * 查询、保存实例日志文件列表
export const getLogFiles = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/pods/{podName}/log/files`;
// * 查询实例日志文件内容
export const getLogContent = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/pods/{podName}/log/content`;
// * 查询、保存实例配置文件路径
export const configFile = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/pods/{podName}/configfile`;
// * 查询实例配置文件文件内容
export const configFileContent = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/pods/{podName}/configfile/content`;
// * 接入服务监控
export const accessMonitor = `${api}/clusters/{clusterId}/namespaces/{namespace}/extraMiddleware/{middlewareName}/monitor`;
// * 删除存储
export const deleteStorage = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/storage/{pvcName}`;
// * 强制删除
export const forceDeleteStorage = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/storage/{pvcName}/force`;
// * 查询应用版本
export const getAppVersion = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/appVersion`;
// * 应用版本升级降级
export const upgradeApp = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/upgradeApp`;
// * 查看命令是否安全
export const safeCheck = `${api}/devopsAbility/safeCheck`;
// * 强制删除实例
export const forceDeletePod = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/pods/{podName}/force`;
// * 回退参数
export const rollbackParam = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/config/rollback`;
// * 获取文件修改历史
export const getAccessFileHistoryRecord = `${api}/virtual/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/config/history`;
// * 文件还原
export const restoreAccessFile = `${api}/virtual/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/config/rollback`;
// * 获取接入服务的配置文件路径 / 保存接入服务的配置文件路径
export const getAccessFilePaths = `${api}/virtual/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/config/path`;
// * 获取配置文件实例内容 / 保存并下发配置文件
export const getAccessFileContent = `${api}/virtual/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/config`;
// * 下载配置文件
export const downloadAccessFile = `${api}/virtual/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/config/download`;
// * 获取需过滤后缀列表
export const getAccessFileSuffixes = `${api}/virtual/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/config/file/suffix`;
// * 查询中间件账户 / 用户新增
export const getMiddlewareAccounts = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/accounts`;
// * 查询中间件账户权限信息
export const getMiddlewareAccountPermissions = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/accounts/{accountName}/permissions`;
// * 查询中间件账户绑定的平台用户
export const getMiddlewareAccountBindUsers = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/accounts/{accountName}/users`;
// * 删除中间件账户
export const deleteMiddlewareAccount = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/accounts/{accountName}`;
// * 账户批量绑定用户
export const middlewareAccountBindToUsers = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/accounts/bind`;
// * 账户释放权限(redis,nacos)
export const disempowerMiddlewareAccountPermissions = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/accounts/{accountName}/disempower`;
// * 账户批量授权
export const empowerMiddlewareAccounts = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/accounts/empower`;
// * 账户解绑用户
export const unBindMiddlewareAccount = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/accounts/{accountName}/users/{userId}/unbind`;
// * 账户同步密码
export const syncMiddlewareAccount = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/accounts/{accountName}/syncUpdate`;
// * 账户修改密码
export const updateMiddlewareAccountPassword = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/accounts/{accountName}/updatePassword`;
// * 获取不同中间件的授权信息接口
export const getPermissionInfo = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/permissions`;
// * 上传表格文件批量修改密码
export const uploadMultipleUpdate = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/accounts/formUpdate`;
// * 后台管理账户密码保存修改
export const saveOperateAccount = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/dev/accounts/{accountName}/savePassword`;
// * 查询中间件批量修改密码进度
export const getUploadProcess = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/accounts/formUpdate`;
// * mysql备库重搭
export const mysqlRecover = `${api}/clusters/{clusterId}/middlewares/mysql/{middlewareName}/recover`;
// * mysql故障恢复
export const mysqlRecovery = `${api}/clusters/{clusterId}/middlewares/mysql/{middlewareName}/recovery`;
// * minio联通性测试
export const minio = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/disasterRecovery/minio/ping`;
// * pg备库重搭
export const reCreateBackup = `${api}/clusters/{clusterId}/namespace/{namespace}/middlewares/{middlewareName}/postgresql/reCreateBackup`;
// * pg备库重搭状态查询
export const reCreateBackupStatus = `${api}/clusters/{clusterId}/namespace/{namespace}/middlewares/{middlewareName}/postgresql/reCreateBackupStatus`;
// * pg节点降级
export const nodeDegradation = `${api}/clusters/{clusterId}/namespace/{namespace}/middlewares/{middlewareName}/postgresql/nodeDegradation`;
// * 获取模板
export const middlewaresTemplate = `${api}/middlewares/template`;
// * 获取模板详情
export const getTemplateInfo = `${api}/middlewares/template/info`;
// * 启用备份
// export const enableScaleByBackup = `${api}/clusters/{clusterId}/namespace/{namespace}/middlewares/{middlewareName}/postgresql/enableScaleByBackup`;
// * 禁用备份
// export const disableScaleByBackup = `${api}/clusters/{clusterId}/namespace/{namespace}/middlewares/{middlewareName}/postgresql/disableScaleByBackup`;
// * 检测基于备份的操作的开关是否开启
export const checkScaleByBackup = `${api}/clusters/{clusterId}/namespace/{namespace}/middlewares/{middlewareName}/postgresql/checkScaleByBackup`;
// * 修改基于备份的操作开关的状态
export const updateScaleByBackup = `${api}/clusters/{clusterId}/namespace/{namespace}/middlewares/{middlewareName}/postgresql/scaleByBackup`;
// * 实例停机
export const stopgracefully = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/pod/{podName}/stopgracefully`;
// * 实例恢复
export const resumegracefully = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/pod/{podName}/resumegracefully`;
// * 校验模板名称
export const checkTemplateNameExist = `${api}/middlewares/template/checkExist`;

import Axios from './request.js';
import * as Area from './area.constants';

// * 查询区域列表
export const getAreas: () => Promise<AreaRes> = () => {
	return Axios.get(Area.getAreas);
};
// * 创建区域
export const createArea: (params: {
	areaName: string;
	description: string;
}) => Promise<resProps> = (params: {
	areaName: string;
	description: string;
}) => {
	return Axios.json(Area.getAreas, params, {}, 'POST');
};
// * 编辑区域
export const updateArea: (params: {
	areaName: string;
	description: string;
	areaId: string;
}) => Promise<resProps> = (params: {
	areaName: string;
	description: string;
	areaId: string;
}) => {
	return Axios.json(Area.getAreas, params, {}, 'PUT');
};
// * 删除区域
export const deleteArea: (params: {
	areaId: string;
}) => Promise<resProps> = (params: { areaId: string }) => {
	return Axios.put(Area.deleteArea, params);
};

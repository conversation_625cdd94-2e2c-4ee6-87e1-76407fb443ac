import { api } from '../api.json';

// * 创建角色
export const createRole = `${api}/role`;
// * 修改角色信息 / 删除角色信息
export const updataRole = `${api}/role/{roleId}`;
// * 获取角色列表
export const getRoleList = `${api}/role/list`;
// * 获取菜单列表
export const getMenus = `${api}/user/menu`;
// * 查询管理类角色可分配menu列表
export const getRoleMenus = `${api}/role/menu`;
// * 查询当前角色的menu权限分配情况
export const getRoleMenusByRoleId = `${api}/role/{roleId}/menu`;
// * 查询所有角色可分配menu列表
export const getNewRoleMenus = `${api}/role/newmenu`;
// * 修改角色菜单权限
export const updateRoleMenu = `${api}/role/update`;
// * 创建角色（新）
export const createNewRole = `${api}/role/newadd`;

import * as platManagementOverview from './platManagementOverview.constants';
import { resProps } from '@/types/comment';
import Axios from './request';

export const getOverviewAlert: (params: any) => Promise<resProps> = (
	params: any
) => {
	return Axios.get(platManagementOverview.alertInfo, params);
};

export const getOverviewServer: () => Promise<resProps> = () => {
	return Axios.get(platManagementOverview.backupServer);
};
export const getOverviewResources: (params?: {
	clusterId: string;
}) => Promise<resProps> = (params?: { clusterId: string }) => {
	return Axios.get(platManagementOverview.resources, params);
};
export const getOperatorStatus: () => Promise<resProps> = () => {
	return Axios.get(platManagementOverview.operatorStatus);
};
export const getOverviewRank: (params: {
	clusterId: string;
	type: string;
}) => Promise<resProps> = (params: { clusterId: string; type: string }) => {
	return Axios.get(platManagementOverview.rank, params);
};
export const getOverviewStoragePie: () => Promise<resProps> = () => {
	return Axios.get(platManagementOverview.pie);
};
export const getOverviewStorageRank: () => Promise<resProps> = () => {
	return Axios.get(platManagementOverview.storageRank);
};
export const getOverviewVersion: () => Promise<resProps> = () => {
	return Axios.get(platManagementOverview.version);
};
export const getOverviewAgentRank: (params?: {
	organId: string;
}) => Promise<resProps> = (params?: { organId: string }) => {
	return Axios.get(platManagementOverview.agentRank, params);
};
export const getOverviewAgentPie: (params?: {
	organId?: string;
	clusterId?: string;
}) => Promise<resProps> = (params?: {
	organId?: string;
	clusterId?: string;
}) => {
	return Axios.get(platManagementOverview.agentPie, params);
};

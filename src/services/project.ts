import Axios from './request.js';
import * as PROJECT from './project.constants';
import {
	CpuMemoryResProps,
	getOrganizationProjectsRes,
	NamespaceCpuMemoryResProps,
	OrganizationUsersRes,
	OrgBackupServerResProps
} from '@/pages/OrganizationDetail/organization.detail';
import { resProps } from '@/types/comment';
import {
	getProjectNamespacesRes,
	ProjectMiddlewareRes
} from '@/pages/ProjectDetail/projectDetail';

interface paramProps {
	projectId: string;
	organId: string;
	[propName: string]: any;
}

export const getProjects: (params: {
	key: string;
	organId: string;
	manager?: boolean;
}) => Promise<getOrganizationProjectsRes> = (params: {
	key: string;
	organId: string;
	manager?: boolean;
}) => {
	return Axios.get(PROJECT.getProjects, params);
};
export const getProjectDetail: (params: {
	key: string;
	organId: string;
	projectId: string;
}) => Promise<getOrganizationProjectsRes> = (params: {
	key: string;
	organId: string;
	projectId: string;
}) => {
	return Axios.get(PROJECT.getProjectDetail, params);
};
export const createProject: (params: {
	organId: string;
	name: string;
	description: string;
	user: string;
}) => Promise<resProps> = (params: {
	organId: string;
	name: string;
	description: string;
	user: string;
}) => {
	return Axios.json(PROJECT.getProjects, params, {}, 'POST');
};
export const deleteProject: (param: {
	projectId: string;
	organId: string;
}) => Promise<resProps> = (param: { projectId: string; organId: string }) => {
	return Axios.delete(PROJECT.deleteProject, param);
};
export const updateProject: (param: {
	organId: string;
	name: string;
	description: string;
	projectId: string;
}) => Promise<resProps> = (param: {
	organId: string;
	name: string;
	description: string;
	projectId: string;
}) => {
	return Axios.json(PROJECT.deleteProject, param, {}, 'PUT');
};
export const getProjectNamespace: (param: {
	projectId: string;
	organId: string;
	withQuota?: boolean;
	withMiddleware?: boolean;
	clusterId?: string;
}) => Promise<getProjectNamespacesRes> = (param: {
	projectId: string;
	organId: string;
	withQuota?: boolean;
	withMiddleware?: boolean;
	clusterId?: string;
}) => {
	return Axios.get(PROJECT.getProjectNamespace, param);
};
export const createProNamespace: (params: {
	projectId: string;
	organId: string;
	clusterId: string;
	name: string;
	aliasName: string;
}) => Promise<resProps> = (params: {
	projectId: string;
	organId: string;
	clusterId: string;
	name: string;
	aliasName: string;
}) => {
	return Axios.json(PROJECT.getProjectNamespace, params, {}, 'POST');
};
export const unBindNamespace = (params: paramProps) => {
	return Axios.delete(PROJECT.getProjectNamespace, params);
};
export const getProjectMember: (param: {
	organId: string;
	projectId: string;
	allocatable: boolean;
	dbaUser?: boolean;
}) => Promise<OrganizationUsersRes> = (param: {
	organId: string;
	projectId: string;
	allocatable: boolean;
	dbaUser?: boolean;
}) => {
	return Axios.get(PROJECT.getProjectMember, param);
};
export const bindProjectMember = (param: paramProps) => {
	return Axios.json(PROJECT.getProjectMember, param, {}, 'POST');
};
export const updateProjectMember = (param: paramProps) => {
	return Axios.json(PROJECT.getProjectMember, param, {}, 'PUT');
};
export const deleteProjectMember: (param: {
	projectId: string;
	organId: string;
	username: string;
}) => Promise<resProps> = (param: {
	projectId: string;
	organId: string;
	username: string;
}) => {
	return Axios.delete(PROJECT.getProjectMember, param);
};
export const getProjectMiddleware: (param: {
	organId: string;
	projectId: string;
}) => Promise<ProjectMiddlewareRes> = (param: {
	organId: string;
	projectId: string;
}) => {
	return Axios.get(PROJECT.getProjectMiddleware, param);
};
export const switchProjectGetToken = (param: paramProps) => {
	return Axios.get(PROJECT.switchProject, param);
};
// export const getAllocatableNamespace = () => {
// 	return Axios.get(PROJECT.getAllocatableNamespace);
// };
export const getProjectMiddlewareCount = (params: { organId: string }) => {
	return Axios.get(PROJECT.getProjectMiddlewareCount, params);
};
// * 获取项目关联集群
export const getClusters = (param: any) => {
	return Axios.get(PROJECT.getClusters, param);
};
// * 查询备份服务器
export const getProjectBackupServer: (params: {
	organId: string;
	projectId: string;
	position?: boolean;
	detail: boolean;
}) => Promise<OrgBackupServerResProps> = (params: {
	organId: string;
	projectId: string;
	position?: boolean;
	detail: boolean;
}) => {
	return Axios.get(PROJECT.getProjectBackupServer, params);
};
// * 查询项目下存储配额
export const getProjectStorage: (params: {
	organId: string;
	projectId: string;
	detail: boolean;
}) => Promise<CpuMemoryResProps> = (params: {
	organId: string;
	projectId: string;
	detail: boolean;
}) => {
	return Axios.get(PROJECT.getProjectStorage, params);
};
// * 查询项目下cpu memory配额
export const getProjectCpuAndMemory: (params: {
	organId: string;
	projectId: string;
	detail: boolean;
}) => Promise<CpuMemoryResProps> = (params: {
	organId: string;
	projectId: string;
	detail: boolean;
}) => {
	return Axios.get(PROJECT.getProjectCpuAndMemory, params);
};
// * 项目下分配资源
export const allotProjectQuota: (params: any) => Promise<resProps> = (
	params: any
) => {
	return Axios.json(PROJECT.allotProjectQuota, params, {}, 'POST');
};
// * 移除项目存储配额
export const deleteProStorage: (params: {
	clusterId: string;
	projectId: string;
	organId: string;
	storageId: string;
}) => Promise<resProps> = (params: {
	clusterId: string;
	projectId: string;
	organId: string;
	storageId: string;
}) => {
	return Axios.delete(PROJECT.deleteProStorage, params);
};
// * 移除项目下备份服务器
export const deleteProBackupServer: (params: {
	clusterId: string;
	projectId: string;
	organId: string;
	backupServerId: string;
}) => Promise<resProps> = (params: {
	clusterId: string;
	projectId: string;
	organId: string;
	backupServerId: string;
}) => {
	return Axios.delete(PROJECT.deleteProBackupServer, params);
};
// * 查询命名空间下的cpu和内存
export const getNamespaceCpuMemory: (params: {
	clusterId: string;
	namespace: string;
}) => Promise<NamespaceCpuMemoryResProps> = (params: {
	clusterId: string;
	namespace: string;
}) => {
	return Axios.get(PROJECT.getNamespaceCpuMemory, params);
};
// * 查询项目下中间件市场信息
export const getProjectMarket = (params: {
	organId: string;
	projectId: string;
	keyword: string;
	withMiddleware?: boolean;
}) => {
	return Axios.get(PROJECT.getProjectMarket, params);
};
export const getProjectBackupTasks = (params: {
	organId: string;
	projectId: string;
	clusterId?: string;
	keyword: string;
}) => {
	return Axios.get(PROJECT.getProjectBackupTasks, params);
};
export const getProjectAlert: (params: any) => Promise<resProps> = (
	params: any
) => {
	return Axios.get(PROJECT.alertRecord, params);
};
export const getProjectServer: (params: {
	organId: string;
	projectId: string;
}) => Promise<resProps> = (params: { organId: string; projectId: string }) => {
	return Axios.get(PROJECT.backupServer, params);
};
export const getProjectResources: (params: {
	clusterId: string;
	organId: string;
	projectId: string;
}) => Promise<resProps> = (params: {
	clusterId: string;
	organId: string;
	projectId: string;
}) => {
	return Axios.get(PROJECT.resources, params);
};
export const getProjectStoragePie: (params: {
	organId: string;
	projectId: string;
}) => Promise<resProps> = (params: { organId: string; projectId: string }) => {
	return Axios.get(PROJECT.pie, params);
};
export const getProjectStorageRank: (params: {
	organId: string;
	projectId: string;
}) => Promise<resProps> = (params: { organId: string; projectId: string }) => {
	return Axios.get(PROJECT.storageRank, params);
};
export const getProjectAgentRank: (params: {
	organId: string;
	projectId: string;
}) => Promise<resProps> = (params: { organId: string; projectId: string }) => {
	return Axios.get(PROJECT.agentRank, params);
};
export const getProjectAgentPie: (params: {
	organId: string;
	projectId: string;
	clusterId: string;
}) => Promise<resProps> = (params: {
	organId: string;
	clusterId: string;
	projectId: string;
}) => {
	return Axios.get(PROJECT.agentPie, params);
};
// * 查询项目下的审批配置
export const getMaintenance: (params: {
	organId: string;
	projectId: string;
	operatorType?: string;
}) => Promise<MaintenanceItemRes> = (params: {
	organId: string;
	projectId: string;
	operatorType?: string;
}) => {
	return Axios.get(PROJECT.getMaintenance, params);
};
// * 修改项目下的审批配置
export const updateMaintenance: (
	params: UpdateMaintenanceSendData
) => Promise<resProps> = (params: UpdateMaintenanceSendData) => {
	return Axios.json(PROJECT.getMaintenance, params, {}, 'PUT');
};
// * 查询某个功能下的工单情况
export const getOrdersByOperatorId: (params: {
	organId: string;
	projectId: string;
	operatorId: string;
	middlewareName?: string;
	namespace?: string;
	clusterId?: string;
	middlewareType: string;
}) => Promise<OperatorWorkOrderRes> = (params: {
	organId: string;
	projectId: string;
	operatorId: string;
	middlewareName?: string;
	namespace?: string;
	clusterId?: string;
	middlewareType: string;
}) => {
	return Axios.get(PROJECT.getOrdersByOperatorId, params);
};
// * 校验项目ID
export const checkProjectId: (params: {
	organId: string;
	projectId: string;
}) => Promise<resProps> = (params: { organId: string; projectId: string }) => {
	return Axios.get(PROJECT.checkProjectId, params);
};
// * rabbitmq账户修改角色
export const updateAccountRole: (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	deployMode: string;
	accountName: string;
	role: string[];
}) => Promise<resProps> = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	deployMode: string;
	accountName: string;
	role: string[];
}) => {
	return Axios.json(PROJECT.updateAccountRole, params, {}, 'POST');
};

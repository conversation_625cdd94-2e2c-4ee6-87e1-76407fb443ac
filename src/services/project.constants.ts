import { api } from '@/api.json';

// * 获取项目列表/创建项目
export const getProjects = `${api}/organizations/{organId}/project`;
// * 获取项目列表/创建项目
export const getProjectDetail = `${api}/organizations/{organId}/project/{projectId}`;
// * 删除项目/ 编辑项目
export const deleteProject = `${api}/organizations/{organId}/project/{projectId}`;
// * 获取项目下分区 / 项目绑定分区
export const getProjectNamespace = `${api}/organizations/{organId}/project/{projectId}/namespace`;
// * 获取项目下成员
export const getProjectMember = `${api}/organizations/{organId}/project/{projectId}/user`;
// * 获取项目下的服务相关
export const getProjectMiddleware = `${api}/organizations/{organId}/project/{projectId}/middleware`;
// * 导航栏切换项目时，发送修改token的请求
export const switchProject = `${api}/user/switchProject`;
// // * 获取项目下可分配的分区 v.1.3.1版本删减
//// export const getAllocatableNamespace = `${api}/project/namespace/allocatable`;
// * 获取项目下服务数量
export const getProjectMiddlewareCount = `${api}/organizations/{organId}/project/middleware/count`;
// * 获取项目关联集群
export const getClusters = `${api}/organizations/{organId}/project/{projectId}/clusters`;
// * 查询项目下备份服务器
export const getProjectBackupServer = `${api}/organizations/{organId}/project/{projectId}/backupServer`;
// * 查询项目下存储配额
export const getProjectStorage = `${api}/organizations/{organId}/project/{projectId}/storage`;
// * 查询项目下cpu memory配额
export const getProjectCpuAndMemory = `${api}/organizations/{organId}/project/{projectId}/cpuMemory`;
// * 项目下分配资源
export const allotProjectQuota = `${api}/organizations/{organId}/project/{projectId}/quota`;
// * 移除项目存储配额
export const deleteProStorage = `${api}/organizations/{organId}/project/{projectId}/storage/{storageId}`;
// * 移除项目备份服务器
export const deleteProBackupServer = `${api}/organizations/{organId}/project/{projectId}/backupServer/{backupServerId}`;
// * 查看命名空间以分配的cpu和memory
export const getNamespaceCpuMemory = `${api}/clusters/{clusterId}/namespaces/{namespace}/cpuMemory`;
// * 查询项目下中间件市场信息
export const getProjectMarket = `${api}/organizations/{organId}/project/{projectId}/market`;
// * 查询项目下备份任务
export const getProjectBackupTasks = `${api}/organizations/{organId}/project/{projectId}/backup`;
// * 项目下告警事件概览
export const alertRecord = `${api}/organizations/{organId}/project/{projectId}/overview/alertRecord`;
// * 备份服务器概览
export const backupServer = `${api}/organizations/{organId}/project/{projectId}/overview/backupServer`;
// * 获取资源信息
export const resources = `${api}/organizations/{organId}/project/{projectId}/overview/cluster/resources`;
// * 存储服务概览
export const pie = `${api}/organizations/{organId}/project/{projectId}/overview/storage/pie`;
// * 存储服务概览
export const storageRank = `${api}/organizations/{organId}/project/{projectId}/overview/storage/rank`;
// * 客户端资源排行榜
export const agentRank = `${api}/organizations/{organId}/project/{projectId}/overview/agent/rank`;
// * 客户端概览
export const agentPie = `${api}/organizations/{organId}/project/{projectId}/overview/agent/pie`;
// * 查询项目下审批配置
export const getMaintenance = `${api}/organizations/{organId}/project/{projectId}/maintenance`;
// * 查询某个功能的工单情况
export const getOrdersByOperatorId = `${api}/organizations/{organId}/project/{projectId}/maintenance/{operatorId}`;
// * 校验组织ID
export const checkProjectId = `${api}/organizations/{organId}/project/{projectId}/checkProjectId`;
// * rabbitmq账户修改角色
export const updateAccountRole = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/accounts/{accountName}/updateRole`;

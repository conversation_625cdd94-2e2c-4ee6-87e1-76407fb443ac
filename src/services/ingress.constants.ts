import { api } from '@/api.json';

// * 获取中间件列表
export const getInstances = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares`;
// * 获取暴露服务
export const getService = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/services`;
// * 获取中间件对外访问
export const getIngressByMiddleware = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/ingress`;
// * ingress 获取 vip
export const getVIPs = `${api}/clusters/{clusterId}/ingress/vip`;
// * 获取集群内访问
export const getInternalServices = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/internalServices`;
// * 获取主机网络对外访问
export const getHostNetworkAddress = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/hostNetworkAddress`;
// * traefik端口校验
export const checkTraefikPort = `${api}/clusters/{clusterId}/ingress/check`;
// * 查询Ingress详情
export const getIngressDetail = `${api}/clusters/{clusterId}/ingress/{ingressClassName}/detail`;
// * 查询端口列表
export const getPorts = `${api}/clusters/{clusterId}/ingress/{ingressClassName}/ports`;
// * 查询pod列表
export const getPods = `${api}/clusters/{clusterId}/ingress/{ingressName}/pods`;
// * 重启pod
export const restartPod = `${api}/clusters/{clusterId}/ingress/{ingressClassName}/pods/{podName}`;
// * 查询pod yaml
export const getPodYaml = `${api}/clusters/{clusterId}/ingress/{ingressName}/pods/{podName}/yaml`;
// * 查询ingress yaml/修改ingress yaml
export const getIngressYaml = `${api}/clusters/{clusterId}/ingress/{ingressClassName}/values`;
// * 查询pod列表
export const getPodList = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/pods`;
// * 判断端口是否冲突
export const judgePortCheck = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/portCheck`;
// * 获取可用端口列表
export const getAvailablePorts = `${api}/clusters/{clusterId}/ingress/availablePort`;
// * 获取中间件服务暴露类型 (v2.0.0)
export const getServiceIngressType = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/expose/type`;
// * 创建/删除中间件服务暴露 (v2.0.0)
export const updateServiceIngress = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/expose`;
// * 获取中间件服务暴露列表（v2.0.0）
export const getServiceIngress = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/expose`;
// * 获取中间件HostNetWork对外访问（v2.0.0）
export const getHostNetwork = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/expose/hostNetworkAddress`;
// * 获取中间件服务暴露详情 （v2.0.0）
export const getServiceIngressDetail = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/expose/{exposeId}`;
// * 判断端口是否冲突 （v2.0.0）
export const checkPort = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/expose/portCheck`;
// * 查询项目下的服务暴露列表
export const getExposes = `${api}/organizations/{organId}/project/{projectId}/expose`;
// * 删除服务暴露
export const deleteExpose = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/expose`;

import Axios from './request.js';
import * as WORKSPACE from './workspace.constants';

// * 获取工作台概览页中间件信息
export const getWorkspaceMiddleware: (params?: {
	organId?: string;
	projectId?: string;
}) => Promise<resProps> = (params?: {
	organId?: string;
	projectId?: string;
}) => {
	return Axios.get(WORKSPACE.middleware, params);
};
// * 获取工作台概览页实时告警
export const getWorkspaceAlertRecord: (params: {
	current?: number | string;
	level?: string;
	size?: number | string;
}) => Promise<resProps> = (params: {
	current?: number | string;
	level?: string;
	size?: number | string;
}) => {
	return Axios.get(WORKSPACE.alertRecord, params);
};
// * 获取工作台概览页告警总量趋势
export const getWorkspaceALertTrend: (params: {
	time: string;
}) => Promise<resProps> = (params: { time: string }) => {
	return Axios.get(WORKSPACE.alertTrent, params);
};
// * 获取当前用户所有组织项目
export const getOrganAndProject: () => Promise<resProps> = () => {
	return Axios.get(WORKSPACE.getOrganAndProject);
};

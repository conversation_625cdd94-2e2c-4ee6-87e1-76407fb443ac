import Axios from './request.js';
import * as Role from './role.constants';
import {
	rolesProps,
	deleteProps,
	roleProps,
	updateProps
} from '@/pages/RoleManage/role';

// * 获取用户列表
export const getRoleList: (params: {
	key: string;
}) => Promise<rolesProps> = (params: { key: string }) => {
	return Axios.get(Role.getRoleList, params);
};
// * 创建用户
export const createRole: (params: roleProps) => Promise<updateProps> = (
	params: roleProps
) => {
	return Axios.json(Role.createRole, params, {}, 'POST');
};
// * 删除用户
export const deleteRole: (params: {
	roleId: number;
}) => Promise<deleteProps> = (params: { roleId: number }) => {
	return Axios.delete(Role.updataRole, params);
};
// * 编辑用户
export const updateRole: (params: roleProps) => Promise<updateProps> = (
	params: roleProps
) => {
	return Axios.json(Role.updataRole, params, {}, 'PUT');
};

// * 查询管理类角色可分配menu列表
export const getRoleMenus = () => {
	return Axios.get(Role.getRoleMenus);
};
// * 查询当前角色的menu权限分配情况
export const getRoleMenusByRoleId = (params: { roleId: number }) => {
	return Axios.get(Role.getRoleMenusByRoleId, params);
};
// * 查询所有角色可分配menu列表
export const getNewRoleMenus = (params: { roleId: number }) => {
	return Axios.get(Role.getNewRoleMenus, params);
};
// * 编辑用户权限
export const updateRoleMenu: (params: roleProps) => Promise<updateProps> = (
	params: roleProps
) => {
	return Axios.json(Role.updateRoleMenu, params, {}, 'PUT');
};
// * 创建用户(新)
export const createNewRole: (params: roleProps) => Promise<updateProps> = (
	params: roleProps
) => {
	return Axios.json(Role.createNewRole, params, {}, 'POST');
};

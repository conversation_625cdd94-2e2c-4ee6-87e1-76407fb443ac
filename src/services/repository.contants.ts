import { api } from '@/api.json';

// * 查询中间仓库列表
export const getMiddlewareRepository = `${api}/middlewares/info`;
// * 查询中间件版本管理列表
export const getMiddlewareVersions = `${api}/middlewares/info/version`;
// * 安装中间件
export const installMiddleware = `${api}/clusters/{clusterId}/middlewares/install/`;
// * 下架中间件
export const unInstallMiddleware = `${api}/clusters/{clusterId}/middlewares/delete`;
// * 中间件更新升级
export const updateMiddleware = `${api}/clusters/{clusterId}/middlewares/update`;
// * 中间件版本下架
export const shelvesMiddlewareVersion = `${api}/middlewares/info/delete`;
// * 获取chart包基本信息
export const chartMetadata = `${api}/middlewares/info/chartMetadata`;
// * 获取chart包文件列表
export const chartNames = `${api}/middlewares/info/chartNames`;
// * 检查集群是否已安装指定类型的中间件控制器
export const operatorStatus = `${api}/middlewares/info/operatorStatus`;
// * 查看文件内容
export const fileContent = `${api}/middlewares/info/fileContent`;
// * 查询可用的中间件列表组
export const getGroups = `${api}/middlewares/info/groups`;
// * 中间件市场中中间件是否启用
export const changeMiddlewareEnable = `${api}/organizations/{organId}/project/{projectId}/market/{middlewareType}/enable`;
// * 获取中间件隐藏参数数据
export const hideParams = `${api}/middlewares/{type}/hideParams`;

import { api } from '@/api.json';
// * 查询存储列表 / 添加存储
export const getStorages = `${api}/clusters/{clusterId}/storage`;
// * 获取存储详情
export const handleStorage = `${api}/clusters/{clusterId}/storage/{storageName}`;
// * 更新存储信息 / 删除存储
export const UpdateStorage = `${api}/clusters/{clusterId}/storage/{storageId}`;
// * 获取中间件存储使用情况
export const getMiddlewareStorage = `${api}/clusters/{clusterId}/storage/{storageName}/middlewares`;
// * 获取中间件存储使用详情
export const getMiddlewareStorageDetail = `${api}/clusters/{clusterId}/storage/{storageName}/middlewares/{middlewareName}`;
// * 查询存储类型
export const getStorageTypes = `${api}/clusters/{clusterId}/storage/type`;
// * 获取资源存储
export const getMonitor = `${api}/clusters/{clusterId}/storage/monitor`;
// * 获取分区下可使用的存储信息
export const getNamespaceStorages = `${api}/clusters/{clusterId}/namespaces/{namespace}/storage`;

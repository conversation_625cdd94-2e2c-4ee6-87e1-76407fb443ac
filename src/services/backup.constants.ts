import { api } from '@/api.json';

// * 查询备份规则 / 删除备份规则 / 创建备份（立即备份） / 更新备份规则
export const backups = `${api}/clusters/{clusterId}/namespaces/{namespace}/backup`;
// * 查询全量备份记录列表 / 删除备份列表
export const backupList = `${api}/clusters/{clusterId}/namespaces/{namespace}/backup/record`;
// * 查询增量备份记录列表
export const incrBackupList = `${api}/clusters/{clusterId}/namespaces/{namespace}/backup/incrRecord`;
// * 查询克隆记录列表
export const getRestoreList = `${api}/clusters/{clusterId}/namespaces/{namespace}/backup/restore`;
// 创建恢复
export const useBackup = `${api}/clusters/{clusterId}/namespaces/{namespace}/backup/restore`;

// // * 使用备份
// export const useBackup = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/backups/restore`;
export const backupAddress = `${api}/backup/address`;
export const backupAddressDetail = `${api}/backup/address/detail`;
export const backupAddressCheck = `${api}/backup/address/check`;
export const middlewares = `${api}/middlewares/info/middleware`;
export const serviceList = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares`;
export const backupTask = `${api}/clusters/{clusterId}/namespaces/{namespace}/backup`;
export const getInc = `${api}/clusters/{clusterId}/namespaces/{namespace}/backup/{backupId}/inc`;
export const editInc = `${api}/clusters/{clusterId}/namespaces/{namespace}/backup/{backupId}/inc`;

// * 查询备份服务器
export const serverList = `${api}/backup/server/list`;
// * 更新备份服务器 /创建备份服务器
export const server = `${api}/backup/server`;
// * 删除备份服务器
export const deleteServer = `${api}/backup/server/{backupServerId}`;
// * 分配备份服务器给集群
export const allocate = `${api}/backup/server/{backupServerId}/clusters/{clusterId}/allocate`;
// * 查询备份服务器数量信息
export const count = `${api}/backup/server/count`;
// * 查询项目可用备份服务器(1个备份服务器只能被1个项目创建1个备份位置)
export const enable = `${api}/backup/server/project/{projectId}/enable`;

// * 更新备份位置 /创建备份位置
export const position = `${api}/organizations/{organId}/project/{projectId}/position`;
// * 删除备份位置
export const deletePosition = `${api}/organizations/{organId}/project/{projectId}/position/{backupPositionId}`;
// * 查询集群分区可用备份位置列表
export const getClusterPosition = `${api}/organizations/{organId}/project/{projectId}/position/usable`;
// * 查询项目备份位置列表
export const getProjectPosition = `${api}/organizations/{organId}/project/{projectId}/position`;
// * 查询用户在指定组织下拥有运维权限的operator
export const organOperator = `${api}/organizations/{organId}/project/operator`;
// * 查询用户在指定项目下拥有运维权限的operator
export const projectOperator = `${api}/organizations/{organId}/project/{projectId}/operator`;
// * 查询中间件是否已经创建周期备份
export const checkSchedule = `${api}/clusters/{clusterId}/namespaces/{namespace}/backup/checkSchedule`;
// * 查询备份任务详情
export const taskDetail = `${api}/clusters/{clusterId}/namespaces/{namespace}/backup/detail`;
// * 查询备份进度
export const getTaskProgress = `${api}/clusters/{clusterId}/namespaces/{namespace}/backup/record/{backupName}/progress`;
// * 查询克隆进度
export const getTaskRestore = `${api}/clusters/{clusterId}/namespaces/{namespace}/backup/restore/{restoreName}/progress`;
// * 全量备份数据有效性验证
export const singleBackupcheck = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middleware}/backupName/{backupName}/backupcheck`;
export const singleBackupcheckResult = `${api}/clusters/{clusterId}/namespaces/{namespace}/backup/record`;
export const periodBackupcheckResult = `${api}/clusters/{clusterId}/namespaces/{namespace}/backup/{backupId}/inc`;

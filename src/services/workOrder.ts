import Axios from './request.js';
import * as WorkOrder from './workOrder.constants';

// * 完成工单
export const completeOrder: (params: CompleteSendData) => Promise<resProps> = (
	params: CompleteSendData
) => {
	return Axios.put(WorkOrder.completeOrder, params);
};
// * 审批工单
export const implementOrder: (
	params: ImplementSendData
) => Promise<resProps> = (params: ImplementSendData) => {
	return Axios.put(WorkOrder.implementOrder, params);
};
// * 撤回工单
export const withdrawOrder: (params: {
	orderId: string;
	comment: string;
}) => Promise<resProps> = (params: { orderId: string; comment: string }) => {
	return Axios.delete(WorkOrder.withdrawOrder, params);
};
// * 查询工单列表
export const getOrders: (params: {
	tab: string;
	current: number;
	size: number;
	keyword: string;
	sortByUpdateTime: string;
	status: string;
	topic: string;
}) => Promise<OrdersRes> = (params: {
	tab: string;
	current: number;
	size: number;
	keyword: string;
	sortByUpdateTime: string;
	status: string;
	topic: string;
}) => {
	return Axios.get(WorkOrder.getOrders, params);
};
// * 获取初始化工单流程
export const getOrderProcess: (params: {
	organId?: string;
	topic?: string;
	projectId?: string;
	needThirdParty?: boolean;
}) => Promise<WorkOrderStepItemRes> = (params: {
	organId?: string;
	topic?: string;
	projectId?: string;
	needThirdParty?: boolean;
}) => {
	return Axios.get(WorkOrder.getOrderProcess, params);
};
// * 创建工单
export const createWorkOrder: (params: any) => Promise<resProps> = (
	params: any
) => {
	return Axios.json(WorkOrder.createWorkOrder, params, {}, 'POST');
};
// * 查询工单详情
export const getOrderDetail: (params: {
	orderId: string;
}) => Promise<OrderDetailRes> = (params: { orderId: string }) => {
	return Axios.get(WorkOrder.getOrderDetail, params);
};
// * 记录执行工单
export const executeOrder = (params: { orderId: string }) => {
	return Axios.put(WorkOrder.executeOrder, params);
};
// * 查看流程列表
export const getWorkFlow = () => {
	return Axios.get(WorkOrder.getWorkFlow);
};
// * 查看流程详情
export const getWorkFlowDetail = (params: { uid: string }) => {
	return Axios.get(WorkOrder.workFlow, params);
};
// * 更新流程
export const editWorkFlow = <T>(params: { uid: string } & T) => {
	return Axios.json(WorkOrder.workFlow, params, {}, 'PUT');
};
// * 获取相关人员列表
export const getManagers = (params: { isFlow: boolean; type: string }) => {
	return Axios.get(WorkOrder.managers, params);
};
// * 获取相关人员列表
export const getMyTopic = (params: { organId: string; projectId?: string }) => {
	return Axios.get(WorkOrder.myTopic, params);
};

import Axios from './request.js';
import * as DISASTER from './disaster.constane';

interface SaveParamsProps {
	chiefName?: string;
	spareName?: string;
	name: string;
	nodePort: number;
	port: number;
	protocol: string;
	targetPort: number;
	remote: boolean;
}

// * 查询平台访问信息
export const queryAccessInfo = () => {
	return Axios.get(DISASTER.queryAccessInfo);
};
// * 保存主平台访问信息
export const saveDisaster = (params: SaveParamsProps) => {
	return Axios.json(DISASTER.saveDisaster, params, {}, 'POST');
};
// * 主备平台切换
export const switchDisaster = (params: { isMaster: boolean }) => {
	return Axios.post(DISASTER.switchDisaster, params);
};

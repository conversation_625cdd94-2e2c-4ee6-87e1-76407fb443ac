import Axios from './request.js';
import * as Home from './home.constants';
import { getNamespaceQuotaRes } from '@/pages/ServiceListDetail/detail.js';

export const getEvents = async (params: any) => {
	// 获取告警事件接口
	const result = Axios.get(Home.getOverviewEvents, params);
	return result;
};

export const getInstanceStatus = async (params: any) => {
	const result = Axios.get(Home.getInstances, params);
	return result;
};

export const getResources = async (params: any) => {
	const result = Axios.get(Home.getResource, params);
	return result;
};
export const getNamespaceQuota: (params: {
	clusterId: string;
	namespace: string;
	storageClass: string;
}) => Promise<getNamespaceQuotaRes> = async (params: {
	clusterId: string;
	namespace: string;
	storageClass: string;
}) => {
	return Axios.get(Home.getQuota, params);
};

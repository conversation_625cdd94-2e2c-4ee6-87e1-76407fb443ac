import { api } from '@/api.json';

export const clusters = `${api}/clusters`;
export const cluster = `${api}/clusters/{clusterId}`;
export const namespaces = `${api}/clusters/{clusterId}/namespaces`;
// 集群组件部署
export const components = `${api}/clusters/{clusterId}/components/{componentName}`;
// * 集群详情接口
export const getMiddlewareResource = `${api}/clusters/{clusterId}/middleware/resource`;
export const getNodeResource = `${api}/clusters/{clusterId}/node/resource`;
export const getNamespaceResource = `${api}/clusters/{clusterId}/namespace/resource`;
export const updateNamespace = `${api}/clusters/{clusterId}/namespaces/{name}`;
export const getComponents = `${api}/clusters/{clusterId}/components`;
export const multipleComponents = `${api}/clusters/{clusterId}/components/multiple`;
export const updateComponents = `${api}/clusters/{clusterId}/components/{componentName}`;
export const getClusterJoinCommand = `${api}/clusters/clusterJoinCommand`;
// * 多ingress支持
export const getIngresses = `${api}/clusters/{clusterId}/ingress`;
export const deleteIngress = `${api}/clusters/{clusterId}/ingress/{ingressName}`;
// * 获取外接动态表单
export const getAspectFrom = `${api}/aspect/form`;
// * 接入组件
export const cutInComponents = `${api}/clusters/{clusterId}/components/{componentName}/integrate`;
// * 镜像仓库
export const mirror = `${api}/clusters/{clusterId}/mirror`;
// * 查询是否接入观云台
export const isAccessGYT = `${api}/user/useOpenUserCenter`;
// * 获取中间件图片接口
export const getMidImage = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/middlewareImage`;
// * 获取集群cpu，内存信息
export const getClusterCpuAndMemory = `${api}/clusters/{clusterId}/monitoring`;
export const getIngressTCPPort = `${api}/port/ingressTcp`;
export const getNodePort = `${api}/port/nodePort`;
// * 获取灾备相关是否显示 --> 已废弃
// export const getDisaster = `${api}/system/disasterRecovery/enable`;
// * 查询日志采集组件的安装情况
export const getLogCollect = `${api}/clusters/{clusterId}/components/logging/logCollect`;
// * 查询指定中间件发布时可指定版本
export const getMiddlewareVersions = `${api}/middlewares/info/{type}/version`;
// * 查询集群下资源配额情况
export const getClusterQuota = `${api}/clusters/{clusterId}/quota`;
// * 绑定项目前检查
export const checkProject = `${api}/clusters/{clusterId}/namespaces/{namespace}/project/check`;
// * 绑定/解绑项目
export const bindProject = `${api}/clusters/{clusterId}/namespaces/{name}/project`;
// * 查询平台集群资源配额情况
export const getClustersQuotas = `${api}/clusterQuota`;
// * 获取所有组件列表
export const getAlertManagers = `${api}/alertmanager`;
// * 获取feature接口
export const getFeatureApi = `${api}/feature`;
// * 获取集群监控
export const getMonitor = `${api}/clusters/{clusterId}/monitor`;

// * 获取吊顶菜单列表
export const getTopMenu = `${api}/user/topMenu`;
// * 获取菜单
export const getMenu = `${api}/user/menu`;
// * 获取项目下服务列表的菜单
export const getMiddlewareMenu = `${api}/user/menu/middlewares`;
// * 获取吊顶菜单列表（新）
export const getNewTopMenu = `${api}/user/newtopMenu`;
// * 获取菜单（新）
export const getNewMenu = `${api}/user/newmenu`;
// * 获取项目下服务列表的菜单（新）
export const getNewMiddlewareMenu = `${api}/user/menu/newmiddlewares`;

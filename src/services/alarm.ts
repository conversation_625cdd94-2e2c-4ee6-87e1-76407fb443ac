import Axios from './request.js';
import * as Alarm from './alarm.constants';
import {
	AlarmContactItem,
	AlarmContactItemRes,
	AlarmRecordIndexRes,
	AlarmRecordSendData,
	AlarmTargetItemRes
} from '@/pages/AlarmCenter/alarm.d';
import { resProps } from '@/types/comment';
import { ServiceRuleItemRes } from '@/pages/ServiceListDetail/detail.js';
// import {
// 	operationAuditsProps,
// 	modulesProps,
// 	sendDataAuditProps
// } from '@/pages/OperationAudit/audit';

export const setMail = (params: any) => {
	return Axios.json(Alarm.setMail, params, {}, 'POST');
};

export const getMailInfo = (params?: any) => {
	return Axios.get(Alarm.getMail, params);
};

export const sendMail = (params: any) => {
	return Axios.post(Alarm.sendMail, params);
};

export const getInsertUser = (params: any) => {
	return Axios.post(Alarm.insertUser, params);
};

export const connectMail = (params: any) => {
	return Axios.json(Alarm.connectMail, params, {}, 'POST');
};

export const setDing = (params: any) => {
	return Axios.json(Alarm.ding, params, {}, 'POST');
};

export const getDing = (params?: any) => {
	return Axios.get(Alarm.ding, params);
};

export const sendDing = (params: any) => {
	return Axios.post(Alarm.sendDing, params);
};

export const connectDing = (params: any) => {
	return Axios.json(Alarm.connectDing, params, {}, 'POST');
};

export const getAlarmSetting = (params?: any) => {
	return Axios.get(Alarm.alertSetting, params);
};

export const postAlarmSetting = (params: any) => {
	return Axios.json(Alarm.alertSetting, params, {}, 'POST');
};

export const getSystemAlarmSetting = (params?: any) => {
	return Axios.get(Alarm.systemAlertSetting, params);
};

export const postSystemAlarmSetting = (params: any) => {
	// return Axios.post(Alarm.systemAlertSetting, params);
	return Axios.json(Alarm.systemAlertSetting, params, params, 'POST');
};
// * 获取服务告警记录
export const getServiceAlertRecords = (params: any) => {
	return Axios.json(Alarm.getServiceRecords, params, {}, 'POST');
};
// * 查询告警记录
export const getAlarmRecords = (params: AlarmRecordSendData) => {
	return Axios.json(Alarm.getAlarmRecords, params, {}, 'POST');
};
// * 查询告警记录索引
export const getAlarmRecordIndex: (params: {
	alertType: string;
}) => Promise<AlarmRecordIndexRes> = (params: { alertType: string }) => {
	return Axios.get(Alarm.getAlarmRecordIndex, params);
};
// * 查询告警对象列表
export const getAlarmTargets: (params: {
	clusterId: string;
}) => Promise<AlarmTargetItemRes> = (params: { clusterId: string }) => {
	return Axios.get(Alarm.alarmTarget, params);
};
// * 新增/接入告警对象
export const updateAlarmTargets: (params: {
	clusterId: string;
	namespace: string;
	aliasName: string;
	prometheusRuleName: string;
	name?: string;
}) => Promise<resProps> = (params: {
	clusterId: string;
	namespace: string;
	aliasName: string;
	prometheusRuleName: string;
	name?: string;
}) => {
	return Axios.json(Alarm.alarmTarget, params);
};
// * 查询告警对象规则
export const getAlarmTargetRule = (params: {
	clusterId: string;
	namespace: string;
	prometheusRuleName: string;
	targetName: string;
}) => {
	return Axios.get(Alarm.getAlarmTargetRule, params);
};
// * 查询告警用户列表
export const getAlarmContacts: (params: {
	allocatable: boolean;
	clusterId: string;
	roleId?: number;
}) => Promise<AlarmContactItemRes> = (params: {
	allocatable: boolean;
	clusterId: string;
	roleId?: number;
}) => {
	return Axios.get(Alarm.getAlarmContacts, params);
};
// * 删除告警用户
export const deleteAlarmContact: (params: {
	clusterId: string;
	username: string;
}) => Promise<resProps> = (params: { clusterId: string; username: string }) => {
	return Axios.delete(Alarm.deleteAlarmContact, params);
};
// * 查询告警记录过滤条件
export const getAlarmRecordsFilter = (params: {
	alertType: string;
	clusterId?: string;
}) => {
	return Axios.get(Alarm.getAlarmRecordsFilter, params);
};
// * 新增告警用户
export const addAlarmContacts: (params: {
	clusterId: string;
	alertUserDtoList: AlarmContactItem[];
}) => Promise<resProps> = (params: {
	clusterId: string;
	alertUserDtoList: AlarmContactItem[];
}) => {
	return Axios.json(Alarm.getAlarmContacts, params, {}, 'POST');
};
// * 查询服务告警规则
export const getServiceAlarmRule: (params: {
	clusterId: string;
	middlewareName: string;
	namespace: string;
	type: string;
}) => Promise<ServiceRuleItemRes> = (params: {
	clusterId: string;
	middlewareName: string;
	namespace: string;
	type: string;
}) => {
	return Axios.get(Alarm.getServiceAlarmRule, params);
};
// * 查询服务告警联系人
export const getServiceAlarmContacts: (params: {
	organId?: string;
	projectId?: string;
	clusterId: string;
	namespace: string;
	allocatable: boolean;
	middlewareName: string;
}) => Promise<AlarmContactItemRes> = (params: {
	organId?: string;
	projectId?: string;
	clusterId: string;
	namespace: string;
	allocatable: boolean;
	middlewareName: string;
}) => {
	return Axios.get(Alarm.getServiceAlarmContacts, params);
};
// * 删除服务告警联系人
export const deleteServiceAlarmContact: (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	username: string;
}) => Promise<resProps> = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	username: string;
}) => {
	return Axios.delete(Alarm.deleteServiceAlarmContact, params);
};
// * 新增服务告警联系人
export const addServiceAlarmContacts: (params: {
	alertUserDtoList: AlarmContactItem[];
	clusterId: string;
	namespace: string;
	middlewareName: string;
}) => Promise<resProps> = (params: {
	alertUserDtoList: AlarmContactItem[];
	clusterId: string;
	namespace: string;
	middlewareName: string;
}) => {
	return Axios.json(Alarm.getServiceAlarmContacts, params, {}, 'POST');
};
interface notiRes extends resProps {
	data: boolean;
}
// * 查询是否打开服务备份通知
export const getServiceBackupNoti: (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
}) => Promise<notiRes> = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
}) => {
	return Axios.get(Alarm.getServiceBackupNotification, params);
};
// * 修改服务备份通知开关
export const updateServiceBackupNoti: (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	enable: boolean;
	type: string;
}) => Promise<resProps> = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	enable: boolean;
	type: string;
}) => {
	return Axios.put(Alarm.getServiceBackupNotification, params);
};
export const deleteRecords = (params: { ids: string[] }) => {
	return Axios.json(Alarm.deleteRecords, params, {}, 'post');
};

import { api } from '@/api.json';
// * 查询组织列表/创建组织
export const getOrganizations = `${api}/organizations`;
// * 更新组织信息/删除组织/获取组织基本详情
export const updateOrganizations = `${api}/organizations/{organId}`;
// * 查询组织cpu memory配额
export const getCpuMemory = `${api}/organizations/{organId}/cpuMemory`;
// * 查询组织存储配额
export const getOrgStorage = `${api}/organizations/{organId}/storage`;
// * 查询备份服务器
export const getOrgBackupServer = `${api}/organizations/{organId}/backupServer`;
// * 查询组织下的所有成员/更新组织成员角色/添加组织用户成员/删除组织用户成员
export const getOrganizationUsers = `${api}/organizations/{organId}/user`;
// * 分配资源
export const allotQuota = `${api}/organizations/{organId}/quota`;
// * 移除组织存储配额
export const deleteOrgStorage = `${api}/organizations/{organId}/storage/{storageId}`;
// * 移除备份服务器
export const deleteOrgBackupServer = `${api}/organizations/{organId}/backupServer/{backupServerId}`;
// * 备份服务器概览
export const backupServer = `${api}/organizations/{organId}/overview/backupServer`;
// * 获取资源信息
export const resources = `${api}/organizations/{organId}/overview/cluster/resources`;
// * 项目资源排行榜
export const rank = `${api}/organizations/{organId}/overview/project/rank`;
// * 存储服务概览
export const pie = `${api}/organizations/{organId}/overview/storage/pie`;
// * 存储服务概览
export const storageRank = `${api}/organizations/{organId}/overview/storage/rank`;
// * 客户端资源排行榜
export const agentRank = `${api}/organizations/{organId}/overview/agent/rank`;
// * 客户端概览
export const agentPie = `${api}/organizations/{organId}/overview/agent/pie`;

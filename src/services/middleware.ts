import Axios from './request.js';
import * as MIDDLEWARE from './middleware.constants';
import {
	getPVCLogSendDataParams,
	getPVCRes,
	getPVCSendDataParams,
	podGroupStatusRes,
	scalePVCStorageSendDataParams
} from '@/pages/ServiceListDetail/detail';
import { resProps } from '@/types/comment.js';

interface disasterProps {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
}

export const getMiddlewares = (params: any) => {
	return Axios.get(MIDDLEWARE.middlewares, params);
};

export const getNodePort = (params: any) => {
	return Axios.get(MIDDLEWARE.nodePorts, params);
};

export const getNodeTaint = (params: any) => {
	return Axios.get(MIDDLEWARE.nodeTaints, params);
};

export const getStorageClass = (params: any) => {
	return Axios.get(MIDDLEWARE.storageClasses, params);
};

export const postMiddleware = (params: any) => {
	return Axios.json(MIDDLEWARE.middleware, params);
};

export const getMiddlewareList = (params: any) => {
	return Axios.get(MIDDLEWARE.middleware, params);
};

export const getMiddlewareDetail = (params: any) => {
	return Axios.get(MIDDLEWARE.getMiddlewareDetail, params);
};

export const updateMiddleware = (params: any) => {
	return Axios.json(MIDDLEWARE.updateMiddleware, params, {}, 'PUT');
};

export const getPods = (params: any) => {
	return Axios.get(MIDDLEWARE.getPods, params);
};

export const restartPods = (params: any) => {
	return Axios.post(MIDDLEWARE.restartPod, params);
};

export const deleteMiddleware = (params: any) => {
	return Axios.delete(MIDDLEWARE.middlewareName, params);
};

export const getBackups = (params: any) => {
	return Axios.get(MIDDLEWARE.backups, params);
};

export const addBackup = (params: any) => {
	return Axios.post(MIDDLEWARE.backups, params);
};

export const deleteBackup = (params: any) => {
	return Axios.delete(MIDDLEWARE.deleteBackup, params);
};

export const getBackupConfig = (params: any) => {
	return Axios.get(MIDDLEWARE.getBackupConfig, params);
};

export const addBackupConfig = (params: any) => {
	return Axios.post(MIDDLEWARE.createTimingBackup, params);
};

export const switchMiddlewareMasterSlave = (params: any) => {
	return Axios.put(MIDDLEWARE.switchMiddleware, params);
};

export const autoSwitchMiddlewareMasterSlave = (params: any) => {
	return Axios.put(MIDDLEWARE.autoSwitchMiddleware, params);
};

export const pgSwitchHover = (params: any) => {
	return Axios.put(MIDDLEWARE.pgSwitchHover, params);
};

export const getMiddlewareMonitorUrl = (params: any) => {
	return Axios.get(MIDDLEWARE.middlewareMonitor, params);
};

export const getMiddlewareEvents = (params: any) => {
	return Axios.get(MIDDLEWARE.middlewareEvents, params);
};
export const getUsedAlarms = (params: any) => {
	return Axios.get(MIDDLEWARE.getUsedAlarmRules, params);
};
export const getUsedAlarm = (params: any) => {
	return Axios.get(MIDDLEWARE.getUsedAlarmRule, params);
};
export const getCanUseAlarms = (params: any) => {
	return Axios.get(MIDDLEWARE.getCanUseAlarmRules, params);
};
export const getAlarmDetail = (params: any) => {
	return Axios.get(MIDDLEWARE.getAlarmDetail, params);
};
export const createAlarms = (params: any) => {
	const { restUrl } = Axios.restfulAPI(MIDDLEWARE.addAlarmRules, params.url);
	return Axios.json(restUrl + '?ding=' + params.ding, params.data);
};
export const createAlarm = (params: any) => {
	const { restUrl } = Axios.restfulAPI(MIDDLEWARE.addAlarmRule, params.url);
	return Axios.json(restUrl + '?ding=' + params.ding, params.data);
};
export const updateAlarms = (params: any) => {
	return Axios.json(MIDDLEWARE.updateAlarmRules, params, {}, 'PUT');
};
export const updateAlarm = (params: any) => {
	const { restUrl } = Axios.restfulAPI(MIDDLEWARE.addAlarmRule, params.url);
	return Axios.json(
		restUrl + '?alertRuleId=' + params.alertRuleId + '&ding=' + params.ding,
		params.data,
		{},
		'PUT'
	);
};
export const deleteAlarms = (params: any) => {
	return Axios.delete(MIDDLEWARE.deleteAlarmRules, params);
};
export const deleteAlarm = (params: any) => {
	return Axios.delete(MIDDLEWARE.deleteAlarmRule, params);
};
export const getConfigs = (params: any) => {
	return Axios.get(MIDDLEWARE.getCustomConfig, params);
};
export const updateConfig = (params: any) => {
	const { restUrl } = Axios.restfulAPI(
		MIDDLEWARE.updateCustomConfig,
		params.url
	);
	return Axios.json(restUrl, params.data, {}, 'PUT');
};
export const getConfigHistory = (params: any) => {
	return Axios.get(MIDDLEWARE.getConfigHistory, params);
};
export const getParamTemp = (params: any) => {
	return Axios.get(MIDDLEWARE.getParamTemplate, params);
};
export const getParamDetail = (params: any) => {
	return Axios.get(MIDDLEWARE.getParamTemplateDetail, params);
};
export const getSlowLogs = (params: any) => {
	return Axios.get(MIDDLEWARE.getSlowLog, params);
};
export const getDynamicFormData = (params: any) => {
	return Axios.get(MIDDLEWARE.getDynamicForm, params);
};
export const getPvcs = (params: any) => {
	return Axios.get(MIDDLEWARE.getPVC, params);
};
export const getSecrets = (params: any) => {
	return Axios.get(MIDDLEWARE.getSecret, params);
};
export const getStandardLogFiles = (params: any) => {
	return Axios.json(MIDDLEWARE.getContainerFiles, params, {}, 'POST');
};
export const getLogDetail = (params: any) => {
	return Axios.json(MIDDLEWARE.getLogDetails, params, {}, 'POST');
};
export const download = (params: any) => {
	const { restUrl } = Axios.restfulAPI(MIDDLEWARE.downloadLog, params);
	return restUrl;
};
export const downloadFile = (params: any) => {
	const { restUrl } = Axios.restfulAPI(MIDDLEWARE.downloadLogFile, params);
	return restUrl;
};
export const getLogFileIndex = (params: any) => {
	return Axios.get(MIDDLEWARE.getLogIndex, params);
};
export const addDisasterIns = (params: any) => {
	return Axios.json(MIDDLEWARE.addDisasterInstance, params, {}, 'PUT');
};
export const switchDisasterIns = (params: any) => {
	return Axios.post(MIDDLEWARE.switchDisasterInstance, params);
};
export const getMysqlExternal = (params: any) => {
	return Axios.get(MIDDLEWARE.getMysqlExternal, params);
};
// * 重启服务
export const rebootService = (params: any) => {
	return Axios.post(MIDDLEWARE.restartService, params);
};

// * 查看pod yaml
export const getPodNameYaml = (params: any) => {
	return Axios.get(MIDDLEWARE.getPodYaml, params);
};

// * 获取value.yaml
export const getValueYaml = (params: any) => {
	return Axios.get(MIDDLEWARE.valueYamlApi, params);
};

// * 更新value.yaml
export const updateValueYaml = (params: any) => {
	return Axios.json(MIDDLEWARE.valueYamlApi, params, {}, 'PUT');
};

// * 存储扩容
export const storageDilatation = (params: any) => {
	return Axios.json(MIDDLEWARE.dilatationStorage, params, {}, 'PUT');
};
// * 查询可发布的中间件信息
export const getCanReleaseMiddleware = (params: any) => {
	return Axios.get(MIDDLEWARE.getCanReleaseMiddleware, params);
};
// * 修改日志开关状态
export const uploadLogSwitch = (params: any) => {
	return Axios.json(MIDDLEWARE.getMiddlewareDetail, params, {}, 'PUT');
};

// * 置顶/取消置顶参数
export const topParam = (param: any) => {
	return Axios.put(MIDDLEWARE.topConfigParam, param);
};

// * 创建数据库用户
export const createUser = (param: any) => {
	return Axios.json(MIDDLEWARE.createUser, param, {}, 'POST', true);
};
// * 查询数据库用户列表
export const listUser = (param: any) => {
	return Axios.get(MIDDLEWARE.listUser, param);
};
// * 创建数据库
export const createDb = (param: any) => {
	return Axios.json(MIDDLEWARE.createDb, param, {}, 'POST', true);
};
// * 更改用户备注
export const updateUserInfo = (param: any) => {
	return Axios.put(MIDDLEWARE.createDb, param);
};
// * 查询mysql字符集
export const listCharset = (param: any) => {
	return Axios.get(MIDDLEWARE.listCharset, param);
};
// * 查询数据库列表
export const listDb = (param: any) => {
	return Axios.get(MIDDLEWARE.listDb, param);
};
// * 删除数据库
export const deleteDb = (param: any) => {
	return Axios.delete(MIDDLEWARE.deleteDb, param);
};
// * 更改数据库备注
export const updateDb = (param: any) => {
	return Axios.json(MIDDLEWARE.updateDb, param, {}, 'PUT', true);
};
// * 修改用户
export const grantUser = (param: any) => {
	return Axios.json(MIDDLEWARE.grantUser, param, {}, 'POST', true);
};
// * 修改密码
export const updatePassword = (param: any) => {
	return Axios.json(MIDDLEWARE.updatePassword, param, {}, 'PUT', true);
};
// * 删除用户
export const deleteUser = (param: any) => {
	return Axios.delete(MIDDLEWARE.deleteUser, param);
};
// * sql审计
export const queryAuditSql = (param: any) => {
	return Axios.json(MIDDLEWARE.queryAuditSql, param, {}, 'POST', true);
};
// 查询kv集合
export const getKv = (param: any) => {
	return Axios.get(MIDDLEWARE.redis, param);
};
// 修改kv
export const updateKv = (param: any) => {
	return Axios.json(MIDDLEWARE.redis, param, {}, 'PUT');
};
// 添加kv
export const addKv = (param: any) => {
	return Axios.json(MIDDLEWARE.redis, param, {}, 'POST');
};
// 删除kv
export const deleteKv = (param: any) => {
	return Axios.json(MIDDLEWARE.redis, param, {}, 'DELETE');
};
// 查询可用区key
export const getKey = () => {
	return Axios.get(MIDDLEWARE.getKey);
};
// 查询可用区tolerations
export const getTolerations = () => {
	return Axios.get(MIDDLEWARE.getTolerations);
};

// * 获取当前从节点绑定主节点
export const getMasterName = (params: any) => {
	return Axios.get(MIDDLEWARE.getMasterName, params);
};

// * 获取redis主从关系列表
export const getBurstList = (params: any) => {
	return Axios.get(MIDDLEWARE.getBurstList, params);
};

// * 获取中间件切换信息
export const getSwitch = (params: any) => {
	return Axios.get(MIDDLEWARE.getSwitch, params);
};
// * 获取中间件切换信息
export const getManualSwitch = (params: any) => {
	return Axios.get(MIDDLEWARE.getManualSwitch, params);
};
// * 中间件pvc获取
export const getMiddlewarePVC: (
	params: getPVCSendDataParams
) => Promise<getPVCRes> = (params: getPVCSendDataParams) => {
	return Axios.get(MIDDLEWARE.getMiddlewarePVC, params);
};
// * pvc获取日志
export const getPVClog = (params: getPVCLogSendDataParams) => {
	return Axios.get(MIDDLEWARE.getPVClog, params);
};
// * pvc 扩容
export const scalePVCStorage: (
	params: scalePVCStorageSendDataParams
) => Promise<resProps> = (params: scalePVCStorageSendDataParams) => {
	return Axios.put(MIDDLEWARE.scalePVCStorage, params);
};
// * pvc 回滚
export const rollbackPVCStorage: (
	params: getPVCLogSendDataParams
) => Promise<resProps> = (params: getPVCLogSendDataParams) => {
	return Axios.put(MIDDLEWARE.rollbackPVCStorage, params);
};
// * 节点迁移
export const migratePod = (params: any) => {
	return Axios.json(MIDDLEWARE.migrate, params, {}, 'POST');
};
// * 本地节点迁移
export const migrateLocalPod = (params: any) => {
	return Axios.json(MIDDLEWARE.migrateLocal, params, {}, 'POST');
};
// * 获取节点迁移列表
export const getNodes = (params: { clusterId: string; zone: string }) => {
	return Axios.get(MIDDLEWARE.getNodes, params);
};
// * 查询审计日志
export const getAuditSql = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	searchWord: string;
	current: number;
	size: number;
	sortOrder: string;
}) => {
	return Axios.json(MIDDLEWARE.getAuditSql, params);
};
// * 获取节点迁移状态
export const getMigrate = (params: any) => {
	return Axios.get(MIDDLEWARE.migrate, params);
};
// * 删除节点迁移失败状态
export const deleteMigrate = (params: any) => {
	return Axios.delete(MIDDLEWARE.deleteMigrate, params);
};
// * 查询服务全部资源
export const getYamlResource = (params: any) => {
	return Axios.get(MIDDLEWARE.getYamlResource, params);
};
// * 查看yaml
export const getYaml = (params: any) => {
	return Axios.get(MIDDLEWARE.getYaml, params);
};
// * 获取服务参数配置可选的节点类型
export const getConfigRole = (params: any) => {
	return Axios.get(MIDDLEWARE.getConfigRole, params);
};
// * 中间件pod group信息
export const getPodGroupsStatus: (params: {
	middlewareName: string;
	clusterId: string;
	namespace: string;
	type: string;
}) => Promise<podGroupStatusRes> = (params: {
	middlewareName: string;
	clusterId: string;
	namespace: string;
	type: string;
}) => {
	return Axios.get(MIDDLEWARE.getPodGroupsStatus, params);
};
// * 中间件重名接口校验
export const getMiddlewareNameCheck: (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	deployMod: string;
}) => Promise<resProps> = (params: disasterProps) => {
	return Axios.get(MIDDLEWARE.getMiddlewareNameCheck, params);
};
// * 获取灾备信息
export const getDisaster: (params: disasterProps) => Promise<resProps> = (
	params: disasterProps
) => {
	return Axios.get(MIDDLEWARE.getDisaster, params);
};
// * 灾备切换
export const switchServiceDisaster: (
	params: disasterProps
) => Promise<resProps> = (params: disasterProps) => {
	return Axios.post(MIDDLEWARE.switchServiceDisaster, params);
};
// * 取消切换
export const cancelSwitch: (params: disasterProps) => Promise<resProps> = (
	params: disasterProps
) => {
	return Axios.post(MIDDLEWARE.cancelSwitch, params);
};
// * 故障恢复
export const repairDisaster: (params: disasterProps) => Promise<resProps> = (
	params: disasterProps
) => {
	return Axios.put(MIDDLEWARE.getDisaster, params);
};
// * 灾备关系解绑
export const removeDisaster: (params: disasterProps) => Promise<resProps> = (
	params: disasterProps
) => {
	return Axios.delete(MIDDLEWARE.getDisaster, params);
};
// * 创建灾备关系
export const createDisaster: (params: any) => Promise<resProps> = (
	params: any
) => {
	return Axios.json(MIDDLEWARE.createDisaster, params, {}, 'POST');
};
// * 获取重新散列的状态
export const getReBalanceStatus = (params: {
	clusterId: string;
	namespace: string;
	name: string;
}) => {
	return Axios.get(MIDDLEWARE.getReBalanceStatus, params);
};
// * 重新散列
export const reBalance = (params: {
	clusterId: string;
	namespace: string;
	name: string;
}) => {
	return Axios.post(MIDDLEWARE.getReBalanceStatus, params);
};
// * 查询额外参数库
export const getParam: (params: {
	middlewareType: string;
	keyword?: string;
}) => Promise<resProps> = (params: {
	middlewareType: string;
	keyword?: string;
}) => {
	return Axios.get(MIDDLEWARE.getParam, params);
};
// * 添加额外参数库
export const addParam: (params: {
	middlewareType: string;
}) => Promise<resProps> = (params: any) => {
	return Axios.json(MIDDLEWARE.getParam, params, {}, 'POST');
};
// * 查询额外参数库
export const editParam: (params: {
	middlewareType: string;
	paramName: string;
	[key: string]: string;
}) => Promise<resProps> = (params: {
	middlewareType: string;
	paramName: string;
	[key: string]: string;
}) => {
	return Axios.json(MIDDLEWARE.editParam, params, {}, 'PUT');
};
// * 查询额外参数库
export const deleteParam: (params: {
	middlewareType: string;
	paramName: string;
}) => Promise<resProps> = (params: { middlewareType: string }) => {
	return Axios.delete(MIDDLEWARE.editParam, params);
};
// * 添加额外参数
export const addition: (params: any) => Promise<resProps> = (params: any) => {
	return Axios.json(MIDDLEWARE.addition, params, {}, 'POST');
};
// * 删除额外参数库
export const deleteAddition: (params: any) => Promise<resProps> = (
	params: any
) => {
	return Axios.delete(MIDDLEWARE.deleteAddition, params);
};
// * 获取服务灾备信息
export const getDisasterService: (params: any) => Promise<resProps> = (
	params: any
) => {
	return Axios.get(MIDDLEWARE.getDisasterService, params);
};
// * 查询中间件服务运维能力列表
export const getOperators = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
}) => {
	return Axios.get(MIDDLEWARE.getOperators, params);
};
// * 执行运维能力
export const executeOperator = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	devopsAbilityName: string;
	operationScope: number;
	pod?: string;
	exec: string;
}) => {
	return Axios.post(MIDDLEWARE.executeOperator, params);
};
// * 删除运维能力
export const deleteOperator = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	devopsAbilityName: string;
	operationScope: number;
	pod?: string;
}) => {
	return Axios.delete(MIDDLEWARE.executeOperator, params);
};
// * 新增运维能力
export const addOperator = (params: any) => {
	return Axios.json(MIDDLEWARE.getOperators, params, {}, 'POST');
};
// * 查看操作审计
export const getServerAudit = (params: {
	clusterId: string;
	middlewareName: string;
	namespace: string;
	pageNum: number;
	pageSize: number;
}) => {
	return Axios.get(MIDDLEWARE.getServerAudit, params);
};
// * 更新运维能力
export const updateOperator = (params: any) => {
	return Axios.json(MIDDLEWARE.getOperators, params, {}, 'PUT');
};
// * 删除实例
export const deleteInstance = (params: {
	clusterId: string;
	instanceName: string;
	middlewareName: string;
	namespace?: string;
}) => {
	return Axios.delete(MIDDLEWARE.uninstallInstance, params);
};
// * 新增实例
export const addInstance = (params: any) => {
	return Axios.json(MIDDLEWARE.addInstance, params, {}, 'POST');
};
// * mysql灾备解绑
export const unBindMysqlDisaster = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
}) => {
	return Axios.delete(MIDDLEWARE.unBindDisasterInstance, params);
};
// * 服务上/下线
export const lockService: (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	chartVersion: string;
	deployMod: string;
	lock: boolean;
}) => Promise<resProps> = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	chartVersion: string;
	deployMod: string;
	lock: boolean;
}) => {
	return Axios.put(MIDDLEWARE.lockService, params);
};
// * 查询实例日志文件路径
export const getLogPath = (params: any) => {
	return Axios.get(MIDDLEWARE.getLogPath, params);
};
// * 查询实例日志文件路径
export const getLogFiles = (params: any) => {
	return Axios.post(MIDDLEWARE.getLogFiles, params);
};
// * 保存实例日志文件路径
export const saveLogPath = (params: any) => {
	return Axios.json(MIDDLEWARE.getLogPath, params, {}, 'POST');
};
// * 查询实例日志文件内容
export const getLogContent = (params: any) => {
	return Axios.post(MIDDLEWARE.getLogContent, params);
};
// * 查询实例配置文件路径
export const getConfigFilePath = (params: any) => {
	return Axios.get(MIDDLEWARE.configFile, params);
};
// * 保存实例配置文件路径
export const saveConfigFilePath = (params: any) => {
	return Axios.json(MIDDLEWARE.configFile, params, {}, 'POST');
};
// * 查询实例配置文件内容
export const getConfigFileContent = (params: any) => {
	return Axios.post(MIDDLEWARE.configFileContent, params);
};
// * 保存实例配置文件内容
export const saveConfigFileContent = (params: any) => {
	return Axios.json(MIDDLEWARE.configFileContent, params, {}, 'PUT');
};
// * 接入监控服务
export const accessMonitor = (params: any) => {
	return Axios.json(MIDDLEWARE.accessMonitor, params, {}, 'PUT');
};
// * 删除存储
export const deletePvcStorage = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	pvcName: string;
}) => {
	return Axios.delete(MIDDLEWARE.deleteStorage, params);
};
// * 强制删除
export const forceDeleteStorage = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	pvcName: string;
}) => {
	return Axios.delete(MIDDLEWARE.forceDeleteStorage, params);
};
// * 查询应用版本
export const getAppVersion = (params: {
	clusterId: string;
	namespace: string;
	type: string;
	middlewareName: string;
}) => {
	return Axios.get(MIDDLEWARE.getAppVersion, params);
};
// * 升级、降级应用版本
export const upgradeApp = (params: {
	clusterId: string;
	namespace: string;
	type: string;
	middlewareName: string;
	chartName: string;
	chartVersion: string;
	upgradeAppVersion: string;
}) => {
	return Axios.post(MIDDLEWARE.upgradeApp, params);
};
// * 查看命令是否安全
export const safeCheck = (params: { cmd: string }) => {
	return Axios.get(MIDDLEWARE.safeCheck, params);
};
// * 强制删除实例
export const forceDeletePod = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	podName: string;
}) => {
	return Axios.delete(MIDDLEWARE.forceDeletePod, params);
};
// * 回退参数
export const rollbackParam = (params: {
	item: string;
	last: string;
	after: string;
	restart: boolean;
	role: string;
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
}) => {
	return Axios.json(MIDDLEWARE.rollbackParam, params, {}, 'POST');
};
// * 获取文件修改历史
export const getAccessFileHistoryRecord: (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	current: number;
	size: number;
	keyword: string;
	startTime: string;
	endTime: string;
}) => Promise<AccessFileHistoryRecordsRes> = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	current: number;
	size: number;
	keyword: string;
	startTime: string;
	endTime: string;
}) => {
	return Axios.get(MIDDLEWARE.getAccessFileHistoryRecord, params);
};
// * 文件还原
export const restoreAccessFile: (params: {
	clusterId: string;
	historyId: string;
	middlewareName: string;
	namespace: string;
}) => Promise<resProps> = (params: {
	clusterId: string;
	historyId: string;
	middlewareName: string;
	namespace: string;
}) => {
	return Axios.put(MIDDLEWARE.restoreAccessFile, params);
};
// * 获取配置文件路径
export const getAccessFilePaths: (params: {
	clusterId: string;
	namespace: string;
	checkExists?: boolean;
	middlewareName: string;
}) => Promise<AccessFilePathRes> = (params: {
	clusterId: string;
	namespace: string;
	checkExists?: boolean;
	middlewareName: string;
}) => {
	return Axios.get(MIDDLEWARE.getAccessFilePaths, params);
};
// * 保存接入服务的配置文件路径
export const saveAccessFilePaths: (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	configFileDtoList: AccessFilePath[];
}) => Promise<resProps> = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	configFileDtoList: AccessFilePath[];
}) => {
	return Axios.json(MIDDLEWARE.getAccessFilePaths, params, {}, 'POST');
};
// * 查询配置文件内容
export const getAccessFileContent: (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	path: string;
	podName: string;
	charset: string;
}) => Promise<AccessFileContentRes> = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	path: string;
	podName: string;
	charset: string;
}) => {
	return Axios.get(MIDDLEWARE.getAccessFileContent, params);
};
// * 保存并下发配置文件
export const saveAndIssuedAccessFile: (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	configFileDtoList: AccessFilePath[];
}) => Promise<resProps> = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	configFileDtoList: AccessFilePath[];
}) => {
	return Axios.json(MIDDLEWARE.getAccessFileContent, params, {}, 'PUT');
};
// * 下载配置文件
export const downloadAccessFile: (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
}) => string = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
}) => {
	const { restUrl } = Axios.restfulAPI(MIDDLEWARE.downloadAccessFile, params);
	return restUrl;
};
// * 获取需过滤后缀列表
export const getAccessFileSuffixes: (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
}) => Promise<resProps<string[]>> = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
}) => {
	return Axios.get(MIDDLEWARE.getAccessFileSuffixes, params);
};
// * 查询中间件账户
export const getMiddlewareAccounts: (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type?: string;
	deployMode?: string;
	current: number;
	size: number;
}) => Promise<MiddlewareAccountRes> = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type?: string;
	deployMode?: string;
	current: number;
	size: number;
}) => {
	return Axios.get(MIDDLEWARE.getMiddlewareAccounts, params);
};
// * 创建中间件账户
export const createMiddlewareAccount: (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type?: string;
	deployMode?: string;
	account?: string;
	confirmPassword?: string;
	oldPassword?: string;
	password?: string;
}) => Promise<resProps> = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type?: string;
	deployMode?: string;
	account?: string;
	confirmPassword?: string;
	oldPassword?: string;
	password?: string;
}) => {
	return Axios.json(MIDDLEWARE.getMiddlewareAccounts, params, {}, 'POST');
};
// * 查询中间件账户权限信息
export const getMiddlewareAccountPermissions: (
	params: MiddlewareAccountComment
) => Promise<MiddlewareAccountPermission> = (
	params: MiddlewareAccountComment
) => {
	return Axios.get(MIDDLEWARE.getMiddlewareAccountPermissions, params);
};
// * 查询中间件账户绑定的平台用户
export const getMiddlewareAccountBindUsers: (
	params: GetMiddlewareAccountBindUsers
) => Promise<MiddlewareAccountBindUsers> = (
	params: GetMiddlewareAccountBindUsers
) => {
	return Axios.get(MIDDLEWARE.getMiddlewareAccountBindUsers, params);
};
// * 删除中间件账户
export const deleteMiddlewareAccount: (
	params: MiddlewareAccountComment
) => Promise<resProps> = (params: MiddlewareAccountComment) => {
	return Axios.delete(MIDDLEWARE.deleteMiddlewareAccount, params);
};
// * 账户批量绑定用户
export const middlewareAccountBindToUsers: (
	params: BatchBindUsers
) => Promise<resProps> = (params: BatchBindUsers) => {
	return Axios.json(
		MIDDLEWARE.middlewareAccountBindToUsers,
		params,
		{},
		'POST'
	);
};
// * 账户释放权限(redis,nacos)
export const disempowerMiddlewareAccountPermissions: (
	params: DisempowerMiddlewareAccount
) => Promise<resProps> = (params: DisempowerMiddlewareAccount) => {
	return Axios.json(
		MIDDLEWARE.disempowerMiddlewareAccountPermissions,
		params,
		{},
		'POST'
	);
};
// * 账户批量授权
export const empowerMiddlewareAccounts: (
	params: EmpowerMiddlewareAccounts
) => Promise<resProps> = (params: EmpowerMiddlewareAccounts) => {
	return Axios.json(MIDDLEWARE.empowerMiddlewareAccounts, params, {}, 'POST');
};
// * 账户解绑用户
export const unBindMiddlewareAccount: (
	params: UnBindMiddlewareAccounts
) => Promise<resProps> = (params: UnBindMiddlewareAccounts) => {
	return Axios.json(MIDDLEWARE.unBindMiddlewareAccount, params, {}, 'POST');
};
// * 账户同步密码
export const syncMiddlewareAccount: (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type?: string;
	deployMode?: string;
	account?: string;
	confirmPassword?: string;
	oldPassword?: string;
	password?: string;
	accountName: string;
}) => Promise<resProps> = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type?: string;
	deployMode?: string;
	account?: string;
	confirmPassword?: string;
	oldPassword?: string;
	password?: string;
	accountName: string;
}) => {
	return Axios.json(MIDDLEWARE.syncMiddlewareAccount, params, {}, 'POST');
};
// * 账户修改密码
export const updateMiddlewareAccountPassword: (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type?: string;
	deployMode?: string;
	account?: string;
	confirmPassword?: string;
	oldPassword?: string;
	password?: string;
	accountName: string;
}) => Promise<resProps> = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type?: string;
	deployMode?: string;
	account?: string;
	confirmPassword?: string;
	oldPassword?: string;
	password?: string;
	accountName: string;
}) => {
	return Axios.json(
		MIDDLEWARE.updateMiddlewareAccountPassword,
		params,
		{},
		'POST'
	);
};
// * 获取不同中间件的授权信息接口
export const getPermissionInfo: (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type?: string;
	deployMode?: string;
	info?: string;
	infoType: string;
}) => Promise<MiddlewareAccountPermission> = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type?: string;
	deployMode?: string;
	info?: string;
	infoType: string;
}) => {
	return Axios.json(MIDDLEWARE.getPermissionInfo, params, {}, 'POST');
};
// * 上传表格文件批量修改密码
export const uploadMultipleUpdate: (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	deployMode?: string;
	file: any;
}) => Promise<resProps> = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	deployMode?: string;
	file: any;
}) => {
	return Axios.json(MIDDLEWARE.uploadMultipleUpdate, params, {}, 'POST');
};
// * 后台管理账户密码保存修改
export const saveOperateAccount: (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	deployMode?: string;
	accountName: string;
	account: string;
	password: string;
}) => Promise<resProps> = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	deployMode?: string;
	accountName: string;
	account: string;
	password: string;
}) => {
	return Axios.json(MIDDLEWARE.saveOperateAccount, params, {}, 'POST');
};
// * 查询中间件批量修改密码进度
export const getUploadProcess = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	deployMode: string;
}) => {
	return Axios.get(MIDDLEWARE.getUploadProcess, params);
};
// * mysql备库重搭
export const mysqlRecover: (params: any) => Promise<resProps> = (
	params: any
) => {
	return Axios.post(MIDDLEWARE.mysqlRecover, params);
};

// * 查询mysql备库重搭状态
export const getMysqlRecover: (params: any) => Promise<resProps> = (
	params: any
) => {
	return Axios.get(MIDDLEWARE.mysqlRecover, params);
};
// * mysql故障恢复
export const mysqlRecovery: (params: any) => Promise<resProps> = (
	params: any
) => {
	return Axios.post(MIDDLEWARE.mysqlRecovery, params);
};
// * minio联通性测试
export const minioPing: (params: any) => Promise<resProps> = (params: any) => {
	return Axios.json(MIDDLEWARE.minio, params, {}, 'POST');
};
// * 备库重搭
export const reCreateBackup = (params: any) => {
	return Axios.json(MIDDLEWARE.reCreateBackup, params);
};
// * 查询备库重搭状态
export const getReCreateBackupStatus = (params: any) => {
	return Axios.get(MIDDLEWARE.reCreateBackupStatus, params);
};
// * 节点降级
export const nodeDegradation = (params: any) => {
	return Axios.json(MIDDLEWARE.nodeDegradation, params);
};
// * 检测基于备份操作的开关是否开启
export const checkScaleByBackup: (params: {
	clusterId: string;
	middlewareName: string;
	namespace: string;
}) => Promise<resProps<number | null>> = (params: {
	clusterId: string;
	middlewareName: string;
	namespace: string;
}) => {
	return Axios.get(MIDDLEWARE.checkScaleByBackup, params);
};
// * 修改基于备份操作的开关状态
export const updateScaleByBackup: (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	chartName: string;
	chartVersion: string;
	enable: boolean;
}) => Promise<resProps> = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	chartName: string;
	chartVersion: string;
	enable: boolean;
}) => {
	return Axios.post(MIDDLEWARE.updateScaleByBackup, params);
};

// * 实例停机
export const stopgracefully = (params: any) => {
	return Axios.post(MIDDLEWARE.stopgracefully, params);
};
// * 实例恢复
export const resumegracefully = (params: any) => {
	return Axios.post(MIDDLEWARE.resumegracefully, params);
};
// * 查询模版
export const getMiddlewaresTemplate = (params: any) => {
	return Axios.get(MIDDLEWARE.middlewaresTemplate, params);
};
// * 删除模板
export const delMiddlewaresTemplate = (params: { id: string }) => {
	return Axios.json(MIDDLEWARE.middlewaresTemplate, params, {}, 'delete');
};
// * 创建服务模板
export const createMiddlewaresTemplate = (params: any) => {
	return Axios.json(MIDDLEWARE.middlewaresTemplate, params);
	// return Axios.json(MIDDLEWARE.middlewareName, param, {}, 'POST', true);
};
// * 修改模板
export const editMiddlewaresTemplate = (params: any) => {
	return Axios.put(MIDDLEWARE.middlewaresTemplate, params);
};
// * 获取模板详情
export const getTemplateInfo = (params: any) => {
	return Axios.get(MIDDLEWARE.getTemplateInfo, params);
};
// * 以模版发布功能
export const postMiddlewareBatch = (params: any) => {
	return Axios.json(MIDDLEWARE.middlewareBatch, params);
};
// * 校验模版名称
export const checkTemplateNameExist = (params: {
	name: string;
	organId: string;
	projectId: string;
	type: string;
}) => {
	return Axios.post(MIDDLEWARE.checkTemplateNameExist, params);
};

import { api } from '@/api.json';

// * 设置邮箱
export const setMail = `${api}/mail`;
// * 获取邮箱信息
export const getMail = `${api}/mail`;
// * 选择被通知人
export const insertUser = `${api}/mail/insertUser`;
// * 发送邮箱
export const sendMail = `${api}/mail/sendMail`;
// * 邮箱连接测试
export const connectMail = `${api}/mail/connect`;
// * 设置、获取钉钉机器人
export const ding = `${api}/ding`;
// * 钉钉告警
export const sendDing = `${api}/ding/sendDing`;
// * 钉钉连接测试
export const connectDing = `${api}/ding/connect`;
// * 服务告警联系人设置
export const alertSetting = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/rules/alertSetting`;
// * 系统告警联系人设置
export const systemAlertSetting = `${api}/rules/systemAlertSetting`;
// * 获取服务告警记录
export const getServiceRecords = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/records`;
// * 查询告警记录
export const getAlarmRecords = `${api}/alert/{alertType}/record`;
// * 查询告警记录索引
export const getAlarmRecordIndex = `${api}/alert/{alertType}/record/index`;
// * 告警对象 查询/新增
export const alarmTarget = `${api}/alert/target`;
// * 查询告警对象规则
export const getAlarmTargetRule = `${api}/alert/target/{targetName}/rule`;
// * 查询告警用户列表 / 新增告警用户
export const getAlarmContacts = `${api}/alert/user`;
// * 移除告警用户
export const deleteAlarmContact = `${api}/alert/user/{username}`;
// * 查询告警记录过滤条件
export const getAlarmRecordsFilter = `${api}/alert/{alertType}/record/filter`;
// * 查询服务告警规则
export const getServiceAlarmRule = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/alert/rules`;
// * 查询服务告警联系人/新增服务告警联系人
export const getServiceAlarmContacts = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/alert/user`;
// * 删除服务告警联系人
export const deleteServiceAlarmContact = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/alert/user/{username}`;
// * 查询服务是否接受备份通知 / 修改
export const getServiceBackupNotification = `${api}/clusters/{clusterId}/namespaces/{namespace}/middlewares/{middlewareName}/alert/backup`;
// * 批量删除告警记录
export const deleteRecords = `${api}/alert/record/delete`;

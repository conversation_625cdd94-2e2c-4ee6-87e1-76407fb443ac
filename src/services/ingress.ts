import Axios from './request.js';
import * as Ingress from './ingress.constants';
import { ServiceIngressTypeRes } from '@/pages/ServiceListDetail/detail.d';
import { resProps } from '@/types/comment.js';

export const getInstances = async (params: any) => {
	const result = Axios.get(Ingress.getInstances, params);
	return result;
};
export const getServices = async (params: any) => {
	const result = Axios.get(Ingress.getService, params);
	return result;
};
export const getIngressMid = async (params: any) => {
	const result = Axios.get(Ingress.getIngressByMiddleware, params);
	return result;
};
export const getVIPs = async (params: { clusterId: string }) => {
	const result = Axios.get(Ingress.getVIPs, params);
	return result;
};
export const getInternalServices = async (params: {
	clusterId: string;
	namespace: string;
	middlewareType: string;
	middlewareName: string;
}) => {
	const result = Axios.get(Ingress.getInternalServices, params);
	return result;
};
export const getHostNetworkAddress = async (params: {
	clusterId: string;
	namespace: string;
	type: string;
	middlewareName: string;
}) => {
	const result = Axios.get(Ingress.getHostNetworkAddress, params);
	return result;
};
export const checkTraefikPort = async (params: {
	clusterId: string;
	startPort?: number;
	endPort?: number;
	ingressClassName?: string;
	namespace?: string;
}) => {
	const result = Axios.get(Ingress.checkTraefikPort, params);
	return result;
};
export const getIngressDetail = async (params: {
	clusterId: string;
	ingressClassName: string;
}) => {
	const result = Axios.get(Ingress.getIngressDetail, params);
	return result;
};
export const getPorts = async (params: {
	clusterId: string;
	ingressClassName: string;
}) => {
	const result = Axios.get(Ingress.getPorts, params);
	return result;
};
export const getPods = async (params: {
	clusterId: string;
	ingressName: string;
}) => {
	const result = Axios.get(Ingress.getPods, params);
	return result;
};
export const restartPod = async (params: {
	clusterId: string;
	ingressClassName: string;
	podName: string;
}) => {
	const result = Axios.delete(Ingress.restartPod, params);
	return result;
};
export const getPodYaml = async (params: {
	clusterId: string;
	ingressName: string;
	podName: string;
}) => {
	const result = Axios.get(Ingress.getPodYaml, params);
	return result;
};
export const getIngressYaml = async (params: {
	clusterId: string;
	ingressClassName: string;
}) => {
	const result = Axios.get(Ingress.getIngressYaml, params);
	return result;
};

export const updateIngressYaml = async (params: {
	clusterId: string;
	ingressClassName: string;
	values: string;
}) => {
	const result = Axios.json(Ingress.getIngressYaml, params, {}, 'PUT');
	return result;
};
export const getPodList = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
}) => {
	return Axios.get(Ingress.getPodList, params);
};
// * 检查端口是否冲突
export const checkIngressPort = (params: {
	clusterId: string;
	namespace: string;
	type: string;
	middlewareName: string;
	startPort?: number;
	endPort?: number;
}) => {
	return Axios.get(Ingress.judgePortCheck, params);
};
// * 获取可用的端口列表
export const getAvailablePorts = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	exposeType?: string;
	ingressClassName?: string;
}) => {
	return Axios.get(Ingress.getAvailablePorts, params);
};

// * 获取中间件服务暴露类型
export const getServiceIngressType: (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	middlewareType: string;
	chartVersion: string;
}) => Promise<ServiceIngressTypeRes> = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	middlewareType: string;
	chartVersion: string;
}) => {
	return Axios.get(Ingress.getServiceIngressType, params);
};
// * 创建中间件服务暴露
export const createServiceIngress: (params: any) => Promise<resProps> = (
	params: any
) => {
	return Axios.json(Ingress.updateServiceIngress, params, {}, 'POST');
};
// * 获取中间件服务暴露列表
export const getServiceIngress = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	middlewareType: string;
	chartVersion: string;
}) => {
	return Axios.get(Ingress.getServiceIngress, params);
};
// * 获取中间件HostNetWork对外访问（v2.0.0）
export const getHostNetwork = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	middlewareType: string;
	chartVersion: string;
}) => {
	return Axios.get(Ingress.getHostNetwork, params);
};
// * 删除中间件服务暴露
export const deleteServiceIngress: (params: any) => Promise<resProps> = (
	params: any
) => {
	return Axios.json(Ingress.updateServiceIngress, params, {}, 'DELETE');
};
// * 编辑中间件服务暴露
export const updateServiceIngress: (params: any) => Promise<resProps> = (
	params: any
) => {
	return Axios.json(Ingress.updateServiceIngress, params, {}, 'PUT');
};
// * 获取中间件服务暴露详情
export const getServiceIngressDetail = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	middlewareType: string;
	chartVersion: string;
	exposeId: string;
}) => {
	return Axios.get(Ingress.getServiceIngressDetail, params);
};
// * 检查端口是否冲突
export const checkPort = (params: {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	middlewareType: string;
	chartVersion: string;
	startPort: string;
	endPort: string;
}) => {
	return Axios.get(Ingress.checkPort, params);
};
// * 项目下服务暴露查询
export const getExposes = (params: {
	organId: string;
	projectId: string;
	clusterId?: string;
	keyword: string;
}) => {
	return Axios.get(Ingress.getExposes, params);
};
// * 删除服务暴露
export const deleteExposes = (params: any) => {
	return Axios.json(Ingress.deleteExpose, params, {}, 'DELETE');
};

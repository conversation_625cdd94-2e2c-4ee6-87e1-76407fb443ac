import { api } from '@/api.json';

// * 获取主机列表
export const getAgents = `${api}/agent/list`;
// * 获取agent安装指令
export const getAgentOrder = `${api}/agent/installCmd`;
// * 删除主机
export const deleteAgent = `${api}/agent/{name}`;
// * 查询agent管控控制器列表
export const getAgentControllers = `${api}/clusters/agentController`;
// * 查询组织下的主机列表
export const getOrganAgentList = `${api}/organizations/{organId}/agent`;
// * 查询项目下的主机列表
export const getProjectAgentList = `${api}/organizations/{organId}/project/{projectId}/agent`;
// * 检查实例状态
export const checkStatus = `${api}/clusters/{clusterId}/instance/checkStatus`;
// * 添加实例
export const addInstance = `${api}/clusters/{clusterId}/instance/instance`;
// * 删除实例
export const deleteInstance = `${api}/clusters/{clusterId}/instance/{instanceName}`;
// * 查询中间件默认运维能力
export const getAbilityDefault = `${api}/devopsAbility/default`;
// * 组织下移除agent
export const deleteOrganAgent = `${api}/organizations/{organId}/agent/{agentName}`;
// * 项目下移除agent
export const deleteProjectAgent = `${api}/organizations/{organId}/project/{projectId}/agent/{agentName}`;

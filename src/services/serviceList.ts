import Axios from './request.js';
import * as SERVICE from './serviceList.constants';

interface listParamsProps {
	organId: string;
	projectId: string;
	clusterId: string;
	namespace: string;
	keyword: string;
	type?: string;
	filterServerMod?: boolean;
}

export interface ParamsProps {
	clusterId: string;
	namespace: string;
	middlewareName: string;
	type: string;
	chartName?: string;
	chartVersion?: string | null;
}

export const getList = (params: listParamsProps) => {
	return Axios.get(SERVICE.getServiceLit, params);
};

export const getVersions = (params: any) => {
	return Axios.get(SERVICE.getServiceVersion, params);
};

export const upgradeChart = (params: any) => {
	return Axios.post(SERVICE.upgradeChart, params);
};

export const upgradeCheck = (params: any) => {
	return Axios.post(SERVICE.upgradeCheck, params);
};

export const deleteMiddlewareStorage = (params: ParamsProps) => {
	return Axios.delete(SERVICE.deleteMiddlewareStorage, params);
};

export const recoveryMiddleware = (params: ParamsProps) => {
	return Axios.json(SERVICE.recoveryMiddleware, params, {}, 'POST');
};

export const getPlatformAdd = (params: ParamsProps) => {
	return Axios.get(SERVICE.getPlatform, params);
};

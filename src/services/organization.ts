import Axios from './request.js';
import * as URL from './organization.constants';
import {
	CreateOrganizationProps,
	getOrganizationResProps,
	organizationDetailResProps
} from '@/pages/OrganizationManagement/organization.d';
import { resProps } from '@/types/comment.js';
import {
	CpuMemoryResProps,
	OrganizationUsersRes,
	OrgBackupServerResProps
} from '@/pages/OrganizationDetail/organization.detail.d';

// * 获取组织列表
export const getOrganizations: (params?: {
	keyword: string;
	manager?: boolean;
}) => Promise<getOrganizationResProps> = (params?: {
	keyword: string;
	manager?: boolean;
}) => {
	return Axios.get(URL.getOrganizations, params);
};
// * 删除组织
export const deleteOrganization: (params: {
	organId: string;
}) => Promise<resProps> = (params: { organId: string }) => {
	return Axios.delete(URL.updateOrganizations, params);
};
// * 创建组织
export const createOrganization: (
	params: CreateOrganizationProps
) => Promise<resProps> = (params: CreateOrganizationProps) => {
	return Axios.json(URL.getOrganizations, params);
};
// * 更新组织
export const updateOrganizationApi: (
	params: CreateOrganizationProps
) => Promise<resProps> = (params: CreateOrganizationProps) => {
	return Axios.json(URL.updateOrganizations, params, '', 'PUT');
};
// * 查询组织cpu memory配额
export const getCpuMemory: (params: {
	organId: string;
	detail?: boolean;
}) => Promise<CpuMemoryResProps> = (params: {
	organId: string;
	detail?: boolean;
}) => {
	return Axios.get(URL.getCpuMemory, params);
};
// * 查询组织存储配额
export const getOrgStorage: (params: {
	organId: string;
	clusterId?: string;
	detail?: boolean;
}) => Promise<CpuMemoryResProps> = (params: {
	organId: string;
	clusterId?: string;
	detail?: boolean;
}) => {
	return Axios.get(URL.getOrgStorage, params);
};
// * 查询备份服务器
export const getOrgBackupServer: (params: {
	organId: string;
	clusterId?: string;
	detail: boolean;
}) => Promise<OrgBackupServerResProps> = (params: {
	organId: string;
	clusterId?: string;
	detail: boolean;
}) => {
	return Axios.get(URL.getOrgBackupServer, params);
};
// * 获取组织基本详情
export const getOrganizationDetail: (params: {
	organId: string;
}) => Promise<organizationDetailResProps> = (params: { organId: string }) => {
	return Axios.get(URL.updateOrganizations, params);
};
// * 查询组织下的所有成员
export const getOrganizationUsers: (params: {
	organId: string;
	allocatable: boolean;
}) => Promise<OrganizationUsersRes> = (params: {
	organId: string;
	allocatable: boolean;
}) => {
	return Axios.get(URL.getOrganizationUsers, params);
};
// * 添加组织用户角色
export const addOrganizationUsers: (params: any) => Promise<resProps> = (
	params: any
) => {
	return Axios.json(URL.getOrganizationUsers, params, {}, 'POST');
};
// * 更新组织用户角色
export const updateOrganizationUser: (params: {
	organId: string;
	username: string;
	roleId: number | null;
}) => Promise<resProps> = (params: {
	organId: string;
	username: string;
	roleId: number | null;
}) => {
	return Axios.put(URL.getOrganizationUsers, params);
};
export const deleteOrganizationUser: (params: {
	organId: string;
	username: string;
}) => Promise<resProps> = (params: { organId: string; username: string }) => {
	return Axios.delete(URL.getOrganizationUsers, params);
};
// * 分配资源
export const allotQuota: (params: any) => Promise<resProps> = (params: any) => {
	return Axios.json(URL.allotQuota, params, {}, 'POST');
};
// * 移除组织存储配额
export const deleteOrgStorage: (params: {
	organId: string;
	clusterId: string;
	storageId: string;
}) => Promise<resProps> = (params: {
	organId: string;
	clusterId: string;
	storageId: string;
}) => {
	return Axios.delete(URL.deleteOrgStorage, params);
};
// * 移除备份服务器
export const deleteOrgBackupServer: (params: {
	organId: string;
	clusterId: string;
	backupServerId: string;
}) => Promise<resProps> = (params: {
	organId: string;
	clusterId: string;
	backupServerId: string;
}) => {
	return Axios.delete(URL.deleteOrgBackupServer, params);
};
export const getOrganServer: (params: {
	organId: string;
}) => Promise<resProps> = (params: { organId: string }) => {
	return Axios.get(URL.backupServer, params);
};
export const getOrganResources: (params: {
	clusterId: string;
	organId: string;
}) => Promise<resProps> = (params: { clusterId: string; organId: string }) => {
	return Axios.get(URL.resources, params);
};
export const getOrganRank: (params: {
	clusterId: string;
	type: string;
	organId: string;
}) => Promise<resProps> = (params: {
	clusterId: string;
	type: string;
	organId: string;
}) => {
	return Axios.get(URL.rank, params);
};
export const getOrganStoragePie: (params: {
	organId: string;
}) => Promise<resProps> = (params: { organId: string }) => {
	return Axios.get(URL.pie, params);
};
export const getOrganStorageRank: (params: {
	organId: string;
}) => Promise<resProps> = (params: { organId: string }) => {
	return Axios.get(URL.storageRank, params);
};
export const getOrganAgentRank: (params: {
	organId: string;
}) => Promise<resProps> = (params: { organId: string }) => {
	return Axios.get(URL.agentRank, params);
};
export const getOrganAgentPie: (params: {
	organId: string;
	clusterId: string;
}) => Promise<resProps> = (params: { organId: string; clusterId: string }) => {
	return Axios.get(URL.agentPie, params);
};

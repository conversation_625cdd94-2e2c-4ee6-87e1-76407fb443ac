import Axios from './request';
import * as COMMON from './common.constants';
import { MirrorParams } from '@/pages/ResourcePoolManagement/resource.pool';
import { judgeResProps } from '@/types/comment';

// * 获取集群列表
export const getClusters = (params?: any) => {
	return Axios.get(COMMON.clusters, params);
};

export const postCluster = (sendData: any) => {
	return Axios.json(COMMON.clusters, sendData);
};

export const getCluster = (params: any) => {
	return Axios.get(COMMON.cluster, params);
};

export const putCluster = (sendData: any) => {
	return Axios.json(COMMON.cluster, sendData, {}, 'put');
};

export const deleteCluster = (params: any) => {
	return Axios.delete(COMMON.cluster, params);
};

export const getNamespaces = (params: any) => {
	return Axios.get(COMMON.namespaces, params);
};
// * 部署组件
export const deployComponent = (params: any) => {
	const { restUrl } = Axios.restfulAPI(COMMON.components, params.url);
	return Axios.json(restUrl, params.data, {}, 'POST');
};
// * 对接组件
export const dockComponent = (params: any) => {
	const { restUrl } = Axios.restfulAPI(COMMON.components, params.url);
	return Axios.json(restUrl, params.data, {}, 'PUT');
};
export const putNamespaces = (params: any, data: string) => {
	return Axios.json(
		COMMON.namespaces,
		params,
		{ data: JSON.stringify(data) },
		'put'
	);
};
// * 集群详情等接口
// * 概览
export const getMiddlewareResource = (params: any) => {
	return Axios.get(COMMON.getMiddlewareResource, params);
};
export const getNodeResource = (params: any) => {
	return Axios.get(COMMON.getNodeResource, params);
};
export const getNamespaceResource = (params: any) => {
	return Axios.get(COMMON.getNamespaceResource, params);
};
// * 命名空间
export const createNamespace = (params: any) => {
	return Axios.json(COMMON.namespaces, params, {}, 'POST');
};
export const deleteNamespace = (params: any) => {
	return Axios.delete(COMMON.updateNamespace, params);
};
export const regNamespace = (params: any) => {
	return Axios.json(COMMON.updateNamespace, params, {}, 'PUT');
};
// * 镜像仓库
export const getMirror = (params: MirrorParams) => {
	return Axios.get(COMMON.mirror, params);
};
export const updateMirror = (params: MirrorParams) => {
	return Axios.json(COMMON.mirror, params, {}, 'PUT');
};
export const addMirror = (params: any) => {
	return Axios.json(COMMON.mirror, params, {}, 'POST');
};
export const deleteMirror = (params: MirrorParams) => {
	return Axios.delete(COMMON.mirror, params);
};
// * 平台组件
export const getComponents = (params: any) => {
	return Axios.get(COMMON.getComponents, params);
};
// * 部署（安装）组件
export const postComponent = (params: any) => {
	return Axios.json(COMMON.updateComponents, params, {}, 'POST');
};
// * 编辑组件
export const putComponent = (params: any) => {
	return Axios.json(COMMON.updateComponents, params, {}, 'PUT');
};
// * 接入组件
export const cutInComponent = (params: any) => {
	return Axios.json(COMMON.cutInComponents, params, {}, 'PUT');
};
// * 卸载（取消接入）组件
export const deleteComponent = (params: any) => {
	return Axios.delete(COMMON.updateComponents, params);
};
// * 批量安装组件
export const mulInstallComponent = (params: any) => {
	return Axios.json(COMMON.multipleComponents, params, {}, 'POST');
};
// * 获取集群纳管命令指令
export const getJoinCommand = (params: any) => {
	return Axios.get(COMMON.getClusterJoinCommand, params);
};
// * 多Ingress接入-获取
export const getIngresses = (params: any) => {
	return Axios.get(COMMON.getIngresses, params);
};
export const deleteIngress = (params: any) => {
	return Axios.delete(COMMON.deleteIngress, params);
};
export const installIngress = (params: any) => {
	return Axios.json(COMMON.getIngresses, params, {}, 'POST');
};
export const accessIngress = (params: any) => {
	return Axios.json(COMMON.getIngresses, params, {}, 'PUT');
};
export const updateIngress = (params: any) => {
	return Axios.json(COMMON.deleteIngress, params, {}, 'PUT');
};
// * 获取外接动态表单
export const getAspectFrom = () => {
	return Axios.get(COMMON.getAspectFrom);
};
// * 查询是否接入观云台
export const getIsAccessGYT: () => Promise<judgeResProps> = () => {
	return Axios.get(COMMON.isAccessGYT);
};
// * 获取中间件图片
export const getMidImagePath = (params: {
	clusterId: string;
	namespace: string;
	type: string;
	version: string;
}) => {
	return Axios.get(COMMON.getMidImage, params);
};
// * 获取集群cpu，内存信息
export const getClusterCpuAndMemory = (params: { clusterId: string }) => {
	return Axios.get(COMMON.getClusterCpuAndMemory, params);
};

// * 获取Ingress TCP 端口范围
export const getIngressTCPPort = () => {
	return Axios.get(COMMON.getIngressTCPPort);
};

// * 获取NodePrt 端口范围
export const getNodePort = () => {
	return Axios.get(COMMON.getNodePort);
};
// * 获取灾备相关是否显示 --> 已废弃
// export const getDisaster = () => {
// 	return Axios.get(COMMON.getDisaster);
// };
export const getLogCollect = (params: { clusterId: string }) => {
	return Axios.get(COMMON.getLogCollect, params);
};
// *  查询指定中间件发布时可指定版本
export const getMiddlewareVersions = (params: {
	type: string;
	chartVersion: string;
}) => {
	return Axios.get(COMMON.getMiddlewareVersions, params);
};
// * 查询集群下资源配额情况
export const getClusterQuota = (params: {
	clusterId: string;
	detail: boolean;
}) => {
	return Axios.get(COMMON.getClusterQuota, params);
};
// * 绑定/解绑项目
export const bindProject = (params: {
	clusterId: string;
	name: string;
	aliasName: string;
	projectId?: string;
	organId?: string;
}) => {
	return Axios.put(COMMON.bindProject, params);
};
// * 绑定项目前检查
export const checkProject = (params: {
	clusterId: string;
	namespace: string;
	aliasName: string;
	projectId?: string;
	organId?: string;
}) => {
	return Axios.get(COMMON.checkProject, params);
};
// * 查询平台集群资源配额情况
export const getClustersQuota = (params: {
	clusterIdList: string[];
	detail: boolean;
}) => {
	return Axios.json(COMMON.getClustersQuotas, params, {}, 'POST');
};
// * 查询平台组件列表
export const getAlertManagers = (params: { clusterId?: string }) => {
	return Axios.get(COMMON.getAlertManagers, params);
};
// * 获取feature接口
export const getFeatureApi = () => {
	return Axios.get(COMMON.getFeatureApi);
};
// * 获取集群监控
export const getMonitor = (params: { clusterId: string }) => {
	return Axios.get(COMMON.getMonitor, params);
};
// * 获取指定集群组件信息
export const getComponent = (params: {
	clusterId: string;
	componentName: string;
}) => {
	return Axios.get(COMMON.updateComponents, params);
};
// * 获取吊顶接口
export const getTopMenu = () => {
	return Axios.get(COMMON.getTopMenu);
};
// * 获取菜单
export const getMenu = (params?: { organId?: string; projectId?: string }) => {
	return Axios.get(COMMON.getMenu, params);
};
// * 获取项目下服务列表的菜单
export const getMiddlewareMenu = (params: {
	organId: string;
	projectId: string;
}) => {
	return Axios.get(COMMON.getMiddlewareMenu, params);
};
// * 获取吊顶接口
export const getNewTopMenu = () => {
	return Axios.get(COMMON.getNewTopMenu);
};
// * 获取菜单
export const getNewMenu = (params?: {
	organId?: string;
	projectId?: string;
}) => {
	return Axios.get(COMMON.getNewMenu, params);
};
// * 获取项目下服务列表的菜单
export const getNewMiddlewareMenu = (params: {
	organId: string;
	projectId: string;
}) => {
	return Axios.get(COMMON.getNewMiddlewareMenu, params);
};

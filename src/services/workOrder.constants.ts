import { api } from '@/api.json';

// * 完成工单
export const completeOrder = `${api}/workOrder/order/{orderId}/complete`;
// * 审批工单
export const implementOrder = `${api}/workOrder/order/{orderId}/validate`;
// * 撤回工单
export const withdrawOrder = `${api}/workOrder/order/{orderId}/withdraw`;
// * 查询工单列表
export const getOrders = `${api}/workOrder/order`;
// * 获取初始化工单流程
export const getOrderProcess = `${api}/workOrder/process/init`;
// * 创建工单
export const createWorkOrder = `${api}/workOrder/order`;
// * 查询工单详情
export const getOrderDetail = `${api}/workOrder/order/{orderId}`;
// * 记录执行工单
export const executeOrder = `${api}/workOrder/order/{orderId}/execute`;
// * 查看工单流程
export const getWorkFlow = `${api}/workOrder/workFlow`;
// * 查看流程详情
export const workFlow = `${api}/workOrder/workFlow/{uid}`;
// * 获取相关人员列表
export const managers = `${api}/workOrder/workFlow/managers`;
// * 获取当前用户可发起的流程
export const myTopic = `${api}/workOrder/workFlow/myTopic`;

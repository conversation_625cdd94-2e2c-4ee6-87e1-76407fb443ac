import { api, user } from '@/api.json';

export const loginApi = `${user}/auth/login`;
export const logoutApi = `${user}/auth/logout`;
export const userInfoApi = `${api}/users/current`;
export const menuApi = `${api}/roles/{roleId}/menu`;
// * lisence
export const license = `${api}/system/configs/license`;
// * 获取rsa 公钥
export const getRsaKey = `${api}/auth/rsa/public`;
// * 用户管理相关接口
export const getUserList = `${api}/user/list`;
// * 创建用户 / 获取用户信息
export const createUser = `${api}/user`;
// * 修改用户信息 / 删除用户信息 / 获取用户信息
export const updateUser = `${api}/user/{userName}`;
// * 修改用户密码
export const updatePassword = `${api}/user/{userName}/password`;
// * 重置密码
export const resetPassword = `${api}/user/{userName}/password/reset`;
// * 获取菜单列表
export const getMenus = `${api}/user/menu`;
// * 获取角色列表
export const getRoles = `${api}/role/list`;
// * 获取角色列表
export const getPersonalConfig = `${api}/user/getPersonalConfig`;
// * 添加个性化配置
export const personalized = `${api}/user/personalized`;
// * 获取登录用户列表及通知人列表
export const users = `${api}/user/users`;
// * 选择被通知人
export const insertUser = `${api}/mail/insertUser`;
// * 选择被通知人
export const insertDing = `${api}/mail/ding/insertUser`;
// * LDAP 连接测试
export const connectionCheck = `${api}/ldap/connectionCheck`;
// * 开启LDAP
export const enable = `${api}/ldap/enable`;
// * 查询LDAP信息
export const detail = `${api}/ldap/detail`;
// * 关闭LDAP
export const disable = `${api}/ldap/disable`;
// * 获取服务列表子菜单
export const getChildMenu = `${api}/user/menu/middlewares`;
// * license相关
export const licenseInfo = `${api}/license`;
// * 发布中间件能力检验
export const licenseCheck = `${api}/license/check`;
// * 检查license是否开启
export const licenseEnable = `${api}/license/enable`;
// * 查看是否开启双活和灾备
export const licenseFeatures = `${api}/license/features`;
// * 获取、设置密码有效期天数
export const passwordExpiredDate = `${api}/user/passwordExpiredDate`;
// * 获取、下载用户k8s conf文件
export const conf = `${api}/user/{username}/conf`;
// * 用户锁定、解锁
export const lock = `${api}/user/{username}/lock`;
// * 获取验证码
export const getVerifyCode = `${api}/verifycode`;
// * 获取微信服务器配置信息
export const getWeChatConfig = `${api}/wx/info`;
// * 绑定企业微信
export const bindWeChat = `${api}/wx/bind`;
// * 企业微信登录
export const loginWeChat = `${user}/wx/login`;
// * 解绑企业微信
export const unBindWeChat = `${api}/wx/unbind`;
// * 获取当前用户绑定企业微信信息
export const getWeChatInfoByUser = `${api}/wx/user`;

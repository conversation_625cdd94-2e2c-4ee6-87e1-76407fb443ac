import Axios from './request';
import * as REPOSITORY from './repository.contants';

interface listParamsProps {
	clusterId: string;
	namespace?: string;
	keyword?: string;
	filterUninstalled?: boolean;
}
interface typeListParamsProps {
	clusterId?: string;
	type: string;
	chartVersion?: string;
	fileName?: string;
}
interface installParamsProps {
	chartName: string;
	chartVersion: string;
	clusterId: string;
}
interface deleteParams {
	chartName: string;
	chartVersion: string;
}
export const getMiddlewareRepository = (params?: listParamsProps) => {
	return Axios.get(REPOSITORY.getMiddlewareRepository, params);
};
export const getTypeVersion = (params: typeListParamsProps) => {
	return Axios.get(REPOSITORY.getMiddlewareVersions, params);
};
export const installMiddleware = (params: installParamsProps) => {
	return Axios.json(REPOSITORY.installMiddleware, params, {}, 'POST');
};
export const unInstallMiddleware = (params: installParamsProps) => {
	return Axios.delete(REPOSITORY.unInstallMiddleware, params);
};
export const updateMiddleware = (params: installParamsProps) => {
	return Axios.put(REPOSITORY.updateMiddleware, params);
};
export const shelvesTypeVersion = (params: deleteParams) => {
	return Axios.delete(REPOSITORY.shelvesMiddlewareVersion, params);
};
export const chartMetadata = (params: typeListParamsProps) => {
	return Axios.get(REPOSITORY.chartMetadata, params);
};
export const chartNames = (params: typeListParamsProps) => {
	return Axios.get(REPOSITORY.chartNames, params);
};
export const operatorStatus = (params: typeListParamsProps) => {
	return Axios.get(REPOSITORY.operatorStatus, params);
};
export const fileContent = (params: typeListParamsProps) => {
	return Axios.get(REPOSITORY.fileContent, params);
};
export const getGroups = (params: listParamsProps) => {
	return Axios.get(REPOSITORY.getGroups, params);
};
export const changeMiddlewareEnable = (params: {
	organId: string;
	projectId: string;
	middlewareType: string;
	enable: boolean;
}) => {
	return Axios.put(REPOSITORY.changeMiddlewareEnable, params);
};
export const hideParams: (params: {
	clusterId: string;
	type: string;
}) => Promise<getParamsRes> = (params: { clusterId: string; type: string }) => {
	return Axios.get(REPOSITORY.hideParams, params);
};
export const updateHideParams: (params: {
	clusterId: string;
	type: string;
	middlewareParamsHideDoList: hideParamItem[];
}) => Promise<resProps> = (params: {
	clusterId: string;
	type: string;
	middlewareParamsHideDoList: hideParamItem[];
}) => {
	return Axios.json(REPOSITORY.hideParams, params, {}, 'POST');
};

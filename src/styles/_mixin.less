// 设置元素宽高
.mixin (wh, @width, @height) {
    width: @width;
    height: @height;
}
// 设置字体大小及颜色
.mixin (fs, @fontSize, @color) {
    font-size: @fontSize;
    color: @color;
}
// 设置高度跟行高
.mixin (lineHeight, @height, @lineHeight: @height) {
    height: @height;
    line-height: @lineHeight;
}

.mixin (border, @color, @borderRadius) {
    border: 1px solid @color;
    border-radius: @borderRadius;
}

// 设置css3样式过度时长
.mixin (transition, @time) {
    -webkit-transition: @time;
    transition: @time;
}
.mixin (textEllipsis) {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
.mixin(textEllipsis2) {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box; /*必须结合的属性 ，将对象作为弹性伸缩盒子模型显示 。*/
	-webkit-line-clamp: 2; /*用来限制在一个块元素显示的文本的行数。*/
	-webkit-box-orient: vertical; /*必须结合的属性 ，设置或检索伸缩盒对象的子元素的排列方式 。*/
}
.mixin alarm-tip {
	width: 40px;
	border-radius: @border-radius;
	font-weight: @font-weight-sm;
	font-size: @font-1;
	.mixin (lineHeight, 22px);
}

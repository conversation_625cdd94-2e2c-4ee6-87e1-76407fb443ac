import { formatNumber } from './utils';

const getPieOption = (
	data: { [propName: string]: any } | null | undefined,
	legend?: boolean
) => {
	const option = {
		title: {
			text: data ? data.total + '个' : '',
			left: legend ? '45%' : '25%',
			top: legend ? '25%' : '35%',
			textStyle: {
				fontSize: 24,
				color: 'rgba(0, 0, 0, 0.85)',
				align: 'center',
				fontWeight: 500
			}
		},
		tooltip: {
			trigger: 'item'
		},
		legend: legend
			? {
					left: '70%',
					bottom: '50%',
					icon: 'circle',
					orient: 'vertical',
					itemWidth: 8,
					itemStyle: {
						fontSize: 12
					},
					formatter: (name: string) => {
						const total = data?.total || 0;
						let target = 0;
						if (data) {
							switch (name) {
								case '运行正常':
									target = data.running;
									break;
								case '运行异常':
									target = data.error;
									break;
							}
						}
						const arr = [
							'{a|' + name + '}',
							'{b|' + '|' + '}',
							'{c|' + formatNumber((target / total) * 100) + '%}',
							'{d|' + target + '个}'
						];
						return arr.join(' ');
					},
					textStyle: {
						lineHeight: 10,
						fontSize: 10,
						rich: {
							b: {
								fontSize: 9,
								color: 'rgba(0, 0, 0, 0.45)'
							},
							c: {
								fontSize: 9,
								color: 'rgba(0, 0, 0, 0.45)'
							}
						}
					}
			  }
			: {
					left: '50%',
					bottom: '25%',
					icon: 'circle',
					itemWidth: 8,
					itemStyle: {
						fontSize: 12
					},
					orient: 'vertical',
					formatter: (name: string) => {
						const total = data?.total || 0;
						let target = 0;
						if (data) {
							switch (name) {
								case '运行正常':
									target = data.running;
									break;
								case '运行异常':
									target = data.error;
									break;
								case '不可用':
									target = data.unUsable;
									break;
								case '待安装':
									target = data.unInstall;
									break;
								default:
									target = 0;
							}
						}
						const arr = [
							'{a|' + name + '}',
							'{b|' + '|' + '}',
							'{c|' + formatNumber((target / total) * 100) + '%}',
							'{d|' + target + '个}'
						];
						return arr.join(' ');
					},
					textStyle: {
						lineHeight: 10,
						fontSize: 10,
						rich: {
							b: {
								fontSize: 9,
								color: 'rgba(0, 0, 0, 0.45)'
							},
							c: {
								fontSize: 9,
								color: 'rgba(0, 0, 0, 0.45)'
							}
						}
					}
			  },
		graphic: {
			type: 'text',
			left: legend ? '47%' : '27%',
			top: legend ? '47%' : '57%',
			style: {
				text: data ? '总数' : '',
				textAlign: 'center',
				fill: 'rgba(0, 0, 0, 0.45)',
				fontSize: 14
			}
		},
		series: [
			{
				name: legend ? '节点状态' : '控制器状态',
				type: 'pie',
				center: legend ? ['50%', '40%'] : ['30%', '50%'],
				radius: legend ? ['45%', '60%'] : ['63%', '78%'],
				avoidLabelOverlap: false,
				color: legend
					? ['#00a700', '#faad14']
					: ['#00a700', '#faad14', '#ff4d4f', '#d7d7d7'],
				label: {
					show: false,
					position: 'center'
				},
				emphasis: {
					label: {
						show: false
					}
				},
				labelLine: {
					show: false
				},
				data: legend
					? [
							{ value: data?.running || 0, name: '运行正常' },
							{
								value: data?.error || 0,
								name: '运行异常'
							}
					  ]
					: [
							{ value: data?.running || 0, name: '运行正常' },
							{
								value: data?.error || 0,
								name: '运行异常'
							},
							{ value: data?.unUsable || 0, name: '不可用' },
							{ value: data?.unInstall || 0, name: '待安装' }
					  ]
			}
		]
	};

	return option;
};
const getLineOption = (
	data: { [propName: string]: any } | null | undefined
) => {
	const option = {
		tooltip: {
			trigger: 'axis'
		},
		legend: {
			bottom: '5%',
			left: '5%',
			align: 'left', // 图例在容器中的水平对齐方式
			orient: 'horizontal', // 图例的排列方向，支持水平(horizontal)和垂直(vertical)
			itemGap: 20, // 图例之间的距离
			itemWidth: 20, // 图例标记的宽度
			itemHeight: 10, // 图例标记的高度
			padding: [10, 20] // 图例内边距,
		},
		grid: {
			top: '15%',
			left: '5%',
			bottom: '20%',
			containLabel: true
		},
		xAxis: {
			type: 'category',
			boundaryGap: false,
			max: 'dataMax',
			data:
				data &&
				data.x &&
				data.x.map((item: any) => item.alerttime.substring(11, 16))
		},
		yAxis: {
			type: 'value',
			axisLine: {
				show: false
			},
			splitNumber: 3,
			minInterval: 1,
			splitLine: {
				lineStyle: {
					type: 'dashed' // y轴分割线类型
				}
			}
		},
		series: [
			{
				name: '一般',
				type: 'line',
				symbol: 'none',
				data:
					data &&
					data.infoList &&
					data.infoList.map((item: any) => item.num),
				itemStyle: {
					color: '#00A7FA'
				}
			},
			{
				name: '次要',
				type: 'line',
				symbol: 'none',
				data:
					data &&
					data.warningList &&
					data.warningList.map((item: any) => item.num),
				itemStyle: {
					color: '#FAA700'
				}
			},
			{
				name: '重要',
				type: 'line',
				symbol: 'none',
				data:
					data &&
					data.criticalList &&
					data.criticalList.map((item: any) => item.num),
				itemStyle: {
					color: '#FF4D4F'
				}
			}
		]
	};

	return option;
};

const getGaugeOption = (data: number, name: string) => {
	const option = {
		series: [
			{
				type: 'gauge',
				startAngle: 180,
				endAngle: 0,
				min: 0,
				max: 1,
				splitNumber: 8,
				axisLine: {
					show: false,
					lineStyle: {
						width: 6,
						color: [
							[0.25, '#00a700'],
							[0.5, '#0070cc'],
							[0.75, '#FFAA3A'],
							[1, '#Ef595C']
						]
					}
				},
				center: ['50%', '75%'],
				radius: '145%',
				pointer: {
					icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
					length: '13%',
					width: 10,
					offsetCenter: [0, '-60%'],
					itemStyle: {
						color: 'auto'
					}
				},
				axisTick: {
					length: 12,
					lineStyle: {
						color: 'auto',
						width: 2
					}
				},
				splitLine: {
					show: false
				},
				axisLabel: {
					show: false
				},
				title: {
					offsetCenter: [0, '0%'],
					fontSize: 14
				},
				detail: {
					fontSize: 29,
					offsetCenter: [0, '-30%'],
					valueAnimation: true,
					formatter: function (value: any) {
						return Math.round(value * 100) + '%';
					},
					color: 'auto'
				},
				data: [
					{
						value: data,
						name: name
					}
				]
			}
		]
	};
	return option;
};

export { getPieOption, getLineOption, getGaugeOption };

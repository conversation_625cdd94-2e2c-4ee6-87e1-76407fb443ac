import { filtersProps, FiltersProps } from '@/types/comment';

export const states: FiltersProps[] = [
	{ value: 'Creating', text: '启动中', color: '#0091FF' },
	{ value: 'Running', text: '运行正常', color: '#52c41a' },
	{ value: 'Other', text: '运行异常', color: '#DA372E' },
	{ value: 'Preparing', text: '创建中', color: '#0091FF' },
	{ value: 'Recover', text: '恢复中', color: '0091FF' },
	{ value: 'Switching', text: '切换中', color: '0091FF' },
	{ value: 'GracefulRestart', text: '重启中', color: '0091FF' },
	{ value: 'Upgrading', text: '更新中', color: '0091FF' },
	{ value: 'failed', text: '创建失败', color: '#DA372E' }
];
export const podStatus: filtersProps[] = [
	{
		value: 'Completed',
		color: '#1E8E3E',
		label: '即将完成'
	},
	{
		value: 'NotReady',
		color: '#D93026',
		label: '运行异常'
	},
	{
		value: 'Running',
		color: '#1E8E3E',
		label: '运行正常'
	},
	{
		value: 'Terminating',
		color: '#FFC440',
		label: '停止异常'
	}
];
export const exposedWay: filtersProps[] = [
	{ value: 'Ingress', label: 'Ingress' },
	{ value: 'NodePort', label: 'NodePort' }
];
export const instanceType: filtersProps[] = [
	{ value: 'mysql', label: 'MySQL' },
	{ value: 'redis', label: 'Redis' },
	{ value: 'elasticsearch', label: 'Elasticsearch' },
	{ value: 'rocketmq', label: 'RocketMQ' }
];
export const instanceSpecList = [
	{
		label: '通用规格',
		value: 'General'
	},
	{
		label: '自定义',
		value: 'Customize'
	}
];
export const list = [
	{ value: 1, label: '星期一' },
	{ value: 2, label: '星期二' },
	{ value: 3, label: '星期三' },
	{ value: 4, label: '星期四' },
	{ value: 5, label: '星期五' },
	{ value: 6, label: '星期六' },
	{ value: 0, label: '星期日' }
];
export const weekMap: any = {
	1: '一',
	2: '二',
	3: '三',
	4: '四',
	5: '五',
	6: '六',
	0: '日'
};
export const esMap = {
	master: '主节点',
	data: '数据节点',
	kibana: 'kibana',
	client: '协调节点',
	cold: '冷数据节点'
};
export const modelMap = {
	MasterSlave: '一主一从',
	'1m-1s': '一主一从',
	simple: 'N主',
	complex: 'N主N数据N协调',
	'complex-cold': 'N主N数据N冷',
	'cold-complex': 'N主N数据N冷N协调',
	regular: 'N主N数据',
	sentinel: '哨兵',
	'2m-noslave': '两主',
	'2m-2s': '两主两从',
	'3m-3s': '三主三从',
	6: '三主三从',
	10: '五主五从'
};
export const radioList = [
	{
		value: '',
		label: '全部'
	},
	{
		value: 'info',
		label: '一般'
	},
	{
		value: 'warning',
		label: '次要'
	},
	{
		value: 'critical',
		label: '重要'
	}
];
export const searchTypes: filtersProps[] = [
	{ label: '分词搜索', value: 'match' },
	{ label: '精确搜索', value: 'matchPhrase' },
	{ label: '模糊搜索', value: 'wildcard' },
	{ label: '正则表达式搜索', value: 'regexp' }
];
export const symbols = [
	{ value: '>=', label: '>=' },
	{ value: '>', label: '>' },
	{ value: '==', label: '=' },
	{ value: '<', label: '<' },
	{ value: '<=', label: '<=' },
	{ value: '!=', label: '!=' }
];
export const alarmWarn: any[] = [
	{
		value: 'info',
		text: '一般'
	},
	{
		value: 'warning',
		text: '次要'
	},
	{
		value: 'critical',
		text: '重要'
	}
];
export const silences = [
	{ value: '5m', text: '5分钟' },
	{ value: '10m', text: '10分钟' },
	{ value: '15m', text: '15分钟' },
	{ value: '30m', text: '30分钟' },
	{ value: '1h', text: '1小时' },
	{ value: '2h', text: '2小时' },
	{ value: '3h', text: '3小时' },
	{ value: '6h', text: '6小时' },
	{ value: '12h', text: '12小时' },
	{ value: '24h', text: '24小时' }
];
export const recordTime = [
	{ value: '1', text: '	1个月' },
	{ value: '3', text: '3个月' },
	{ value: '6', text: '6个月' },
	{ value: '12', text: '12个月' },
	{ value: '-1', text: '永久' },
	{ value: 'customize', text: '自定义' }
];
export const formItemLayout410 = {
	labelCol: {
		span: 4
	},
	wrapperCol: {
		span: 10
	}
};
export const formItemLayout420 = {
	labelCol: {
		span: 4
	},
	wrapperCol: {
		span: 20
	}
};
export const formItemLayout516 = {
	labelCol: {
		span: 5
	},
	wrapperCol: {
		span: 16
	}
};
export const formItemLayout519 = {
	labelCol: {
		span: 5
	},
	wrapperCol: {
		span: 19
	}
};
export const formItemLayout614 = {
	labelCol: {
		span: 6
	},
	wrapperCol: {
		span: 14
	}
};
export const formItemLayout618 = {
	labelCol: {
		span: 6
	},
	wrapperCol: {
		span: 18
	}
};
export const formItemLayout619 = {
	labelCol: {
		fixedSpan: 6
	},
	wrapperCol: {
		span: 19
	}
};
export const timeSelectDataSource = [
	{
		value: '1',
		label: '1',
		children: [
			{ value: '1-minutes', label: '分钟' },
			{ value: '1-hours', label: '小时' },
			{ value: '1-days', label: '天' }
		]
	},

	{
		value: '3',
		label: '3',
		children: [
			{ value: '3-minutes', label: '分钟' },
			{ value: '3-hours', label: '小时' },
			{ value: '3-days', label: '天' }
		]
	},
	{
		value: '5',
		label: '5',
		children: [
			{ value: '5-minutes', label: '分钟' },
			{ value: '5-hours', label: '小时' },
			{ value: '5-days', label: '天' }
		]
	},
	{
		value: '7',
		label: '7',
		children: [
			{ value: '7-minutes', label: '分钟' },
			{ value: '7-hours', label: '小时' },
			{ value: '7-days', label: '天' }
		]
	},
	{
		value: '15',
		label: '15',
		children: [
			{ value: '15-minutes', label: '分钟' },
			{ value: '15-hours', label: '小时' },
			{ value: '15-days', label: '天' }
		]
	},
	{
		value: '30',
		label: '30',
		children: [
			{ value: '30-minutes', label: '分钟' },
			{ value: '30-hours', label: '小时' },
			{ value: '30-days', label: '天' }
		]
	}
];
export const protocolFilter = [
	{ label: 'HTTP', value: 'HTTP' },
	{ label: 'TCP', value: 'TCP' }
];
export const address = [
	{
		key: 'https',
		value: 'https'
	},
	{
		key: 'http',
		value: 'http'
	}
];
export const authorityList = [
	{
		authority: 1,
		value: '只读'
	},
	{
		authority: 2,
		value: '读写(DDL+DML)'
	},
	{
		authority: 3,
		value: '仅DDL'
	},
	{
		authority: 4,
		value: '仅DML'
	}
];
// * mysql版本
export const mysqlDataList = [
	{
		id: '1',
		cpu: '2 Core',
		memory: '4 Gi'
	},
	{
		id: '2',
		cpu: '4 Core',
		memory: '8 Gi'
	},
	{
		id: '3',
		cpu: '4 Core',
		memory: '16 Gi'
	},
	{
		id: '4',
		cpu: '8 Core',
		memory: '16 Gi'
	},
	{
		id: '5',
		cpu: '8 Core',
		memory: '32 Gi'
	},
	{
		id: '6',
		cpu: '16 Core',
		memory: '64 Gi'
	}
];
// * mysql读写分离版本
export const mysqlProxyList = [
	{
		id: '0',
		cpu: '0.512 Core',
		memory: '0.512 Gi'
	},
	{
		id: '1',
		cpu: '2 Core',
		memory: '4 Gi'
	},
	{
		id: '2',
		cpu: '4 Core',
		memory: '8 Gi'
	},
	{
		id: '3',
		cpu: '4 Core',
		memory: '16 Gi'
	},
	{
		id: '4',
		cpu: '8 Core',
		memory: '16 Gi'
	},
	{
		id: '5',
		cpu: '8 Core',
		memory: '32 Gi'
	},
	{
		id: '6',
		cpu: '16 Core',
		memory: '64 Gi'
	}
];
// * redis 哨兵
export const redisSentinelDataList = [
	{
		id: '1',
		cpu: '0.256 Core',
		memory: '0.512 Gi'
	}
];
//* redis 集群模式
export const redisDataList = [
	{
		id: '1',
		cpu: '2 Core',
		memory: '1 Gi'
	},
	{
		id: '2',
		cpu: '2 Core',
		memory: '2 Gi'
	},
	{
		id: '3',
		cpu: '2 Core',
		memory: '4 Gi'
	},
	{
		id: '4',
		cpu: '2 Core',
		memory: '8 Gi'
	},
	{
		id: '5',
		cpu: '2 Core',
		memory: '16 Gi'
	},
	{
		id: '6',
		cpu: '2 Core',
		memory: '32 Gi'
	}
];
// * redis 节点
export const redisNodeList = [
	{
		id: '1',
		cpu: '2 Core',
		memory: '1 Gi'
	},
	{
		id: '2',
		cpu: '2 Core',
		memory: '2 Gi'
	},
	{
		id: '3',
		cpu: '2 Core',
		memory: '8 Gi'
	},
	{
		id: '4',
		cpu: '2 Core',
		memory: '16 Gi'
	},
	{
		id: '5',
		cpu: '2 Core',
		memory: '32 Gi'
	}
];
// * es
export const esDataList = [
	{
		id: '1',
		cpu: '2 Core',
		memory: '4 Gi'
	},
	{
		id: '2',
		cpu: '2 Core',
		memory: '8 Gi'
	},
	{
		id: '3',
		cpu: '4 Core',
		memory: '8 Gi'
	},
	{
		id: '4',
		cpu: '4 Core',
		memory: '16 Gi'
	},
	{
		id: '5',
		cpu: '8 Core',
		memory: '32 Gi'
	}
];
// * mq
export const mqDataList = [
	{
		id: '1',
		cpu: '2 Core',
		memory: '4 Gi'
	},
	{
		id: '2',
		cpu: '4 Core',
		memory: '8 Gi'
	},
	{
		id: '3',
		cpu: '8 Core',
		memory: '16 Gi'
	},
	{
		id: '4',
		cpu: '12 Core',
		memory: '24 Gi'
	},
	{
		id: '5',
		cpu: '16 Core',
		memory: '32 Gi'
	}
];
// * kafka
export const kafkaDataList = [
	{
		id: '1',
		cpu: '2 Core',
		memory: '4 Gi'
	},
	{
		id: '2',
		cpu: '4 Core',
		memory: '8 Gi'
	},
	{
		id: '3',
		cpu: '8 Core',
		memory: '16 Gi'
	},
	{
		id: '4',
		cpu: '12 Core',
		memory: '24 Gi'
	},
	{
		id: '5',
		cpu: '16 Core',
		memory: '32 Gi'
	}
];
// * zkp
export const zkpDataList = [
	{
		id: '1',
		cpu: '1 Core',
		memory: '2 Gi'
	},
	{
		id: '2',
		cpu: '2 Core',
		memory: '4 Gi'
	},
	{
		id: '3',
		cpu: '4 Core',
		memory: '8 Gi'
	},
	{
		id: '4',
		cpu: '8 Core',
		memory: '16 Gi'
	},
	{
		id: '5',
		cpu: '16 Core',
		memory: '32 Gi'
	}
];
export const backupTaskStatus = [
	{ value: 'Waiting', text: '等待中', color: '#a0d911' },
	{ value: 'Running', text: '备份中', color: '#faad14' },
	{ value: 'Success', text: '备份成功', color: '#52c41a' },
	{ value: 'Failed', text: '备份失败', color: '#ff4d4f' },
	{ value: 'Deleting', text: '删除中', color: '#d7d7d7' },
	{ value: 'DeleteFailed', text: '删除失败', color: '#d7d7d7' },
	{ value: 'Unknown', text: '未知', color: '#d7d7d7' }
];
export const backupRecordStatus = [
	{ value: 'Unknown', text: '未知', color: '#d7d7d7' },
	{ value: 'Running', text: '备份中', color: '#faad14' },
	{ value: 'Success', text: '备份成功', color: '#52c41a' },
	{ value: 'Failed', text: '备份失败', color: '#ff4d4f' },
	{ value: 'Deleting', text: '删除中', color: '#d7d7d7' },
	{ value: 'DeleteFailed', text: '删除失败', color: '#d7d7d7' }
];
export const backupRestoreStatus = [
	{ value: 'Unknown', text: '未知', color: '#d7d7d7' },
	{ value: 'Running', text: '恢复中', color: '#faad14' },
	{ value: 'Success', text: '恢复成功', color: '#52c41a' },
	{ value: 'Failed', text: '恢复失败', color: '#ff4d4f' },
	{ value: 'Deleting', text: '删除中', color: '#d7d7d7' },
	{ value: 'DeleteFailed', text: '删除失败', color: '#d7d7d7' }
];

export const minutes = ['10', '15', '20', '30', '40', '50', '60'];
export const pgsqlCharactorSet = [
	'BIG5',
	'EUC_CN',
	'EUC_JP',
	'EUC_JIS_2004 ',
	'EUC_KR',
	'EUC_TW',
	'GB18030',
	'GBK',
	'ISO_8859_5',
	'ISO_8859_б',
	'ISO_8859_7',
	'ISO_8859_8',
	'JOHAB',
	'KOI8R',
	'KOI 8U',
	'LATIN1',
	'LATIN2',
	'LATIN3',
	'LATIN4',
	'LATIN5',
	'LATIN6',
	'LATIN7',
	'LATIN8',
	'LATIN9',
	'LATIN10',
	'MULE_INTERNAL',
	'SJIS',
	'SHIFT_JIS_2004',
	'SQL_ASCII',
	'UHC',
	'UTF8',
	'WIN86 6',
	'WIN8 74',
	'WIN1250',
	'WIN1251',
	'WIN1 252',
	'WIN1253',
	'WIN1254',
	'WIN1255',
	'WIN1256',
	'WIN1257',
	'WIN1258'
];
export const passwordPeriods = [
	{
		label: '7天',
		value: '7'
	},
	{
		label: '30天',
		value: '30'
	},
	{
		label: '60天',
		value: '60'
	},
	{
		label: '90天',
		value: '90'
	},
	{
		label: '180天',
		value: '180'
	},
	{
		label: '自定义',
		value: 'customize'
	}
];
export const minmax = (value: number, min: number, max?: number) => {
	if (!max) return value < min ? min : value;
	if (value > max) {
		return max;
	} else if (value < min) {
		return min;
	} else {
		return value;
	}
};
export const storageFormat = (data: any, type: string) => {
	return `${data[type].storageClassName}/${data[type].storageClassAliasName}`;
};

export const trendAlarm = [
	{ label: '最近12小时', value: '12h' },
	{ label: '最近24小时', value: '24h' },
	{ label: '最近3天', value: '3d' },
	{ label: '最近1周', value: '7d' }
];

// * 菜单需要选中的三段菜单
export const threeMenu = [
	'/project/projectManagement/overview',
	'/project/projectManagement/namespace',
	'/project/projectManagement/member',
	'/project/projectManagement/backup'
];
// * 菜单需要选中的四段菜单
export const fourMenu = [
	'/project/db',
	'/project/mse',
	'/project/mq',
	'/project/other'
];
export const agentFilters = [
	{
		text: '运行中',
		value: 'Online'
	},
	{
		text: '离线',
		value: 'Offline'
	},
	{
		text: '卸载中',
		value: 'Terminating'
	},
	{
		text: '未知',
		value: 'unknown'
	}
];
export const MIDDLEWARE_DEPLOYMENT = 'ServiceLock';
// export const MIDDLEWARE_DECOMMISSIONING = 'ServiceLock';
export const SERVER_INTEGRATE_FOR_PROJECT = 'ServerIntegrateForProject';
export const SERVER_INTEGRATE_FOR_ORGAN = 'ServerIntegrateForOrgan';
export const QUOTA_REQUEST_FOR_NAMESPACE = 'QuotaRequestForNamespace';
export const QUOTA_REQUEST_FOR_PROJECT = 'QuotaRequestForProject';
export const QUOTA_REQUEST_FOR_ORGAN = 'QuotaRequestForOrgan';
export const CONTROLLED_OPERATION_BASE = 'ControlledOperationBase';
export const CONTROLLED_OPERATION_Maintenance =
	'ControlledOperationMaintenance';
export const CONTROLLED_OPERATION_EXPERT = 'ControlledOperationExpert';
export const workOrderTopic = {
	[MIDDLEWARE_DEPLOYMENT]: '服务锁定/解锁',
	// [MIDDLEWARE_DECOMMISSIONING]: '服务解锁',
	[SERVER_INTEGRATE_FOR_PROJECT]: '服务器接入-项目内接入',
	[SERVER_INTEGRATE_FOR_ORGAN]: '服务器接入-组织内接入',
	[QUOTA_REQUEST_FOR_NAMESPACE]: '配额申请-命名空间配额',
	[QUOTA_REQUEST_FOR_PROJECT]: '配额申请-项目配额',
	[QUOTA_REQUEST_FOR_ORGAN]: '配额申请-组织配额',
	[CONTROLLED_OPERATION_BASE]: '受限操作-基础操作',
	[CONTROLLED_OPERATION_Maintenance]: '受限操作-运维操作',
	[CONTROLLED_OPERATION_EXPERT]: '受限操作-高级操作'
};
export const maintenanceTypes: any = {
	ControlledOperationBase: [
		'Service Publishing',
		'Service Access',
		'Service Deletion',
		'Service Recovery',
		'Secondary Deletion'
	],
	ControlledOperationMaintenance: [
		'Restart Instance',
		'Restart Service',
		'Storage Expansion',
		'Storage Deletion',
		'Node Migration',
		'Modify Specification',
		'Horizontal Scaling',
		'Master-Slave Switching',
		'Service Version Management',
		'Middleware Version Upgrade',
		'Create Backup',
		'Clone Service',
		'Delete Backup Task',
		'Delete Backup Record',
		'Add Service Exposure',
		'Delete Service Exposure',
		'Edit Service Exposure',
		'Add Parameter',
		'Edit Parameter',
		'Delete Parameter',
		'Add Parameter Template',
		'Edit Parameter Template',
		'Delete Parameter Template',
		'Use Parameter Template',
		'More Add Parameter',
		'More Add to List',
		'More Edit Parameter',
		'More Delete Parameter',
		'Alarm Record Management',
		'Add Alarm Rule',
		'Edit Alarm Rule',
		'Delete Alarm Rule',
		'Add Alarm Contact',
		'Delete Alarm Contact',
		'Standard Log Collection',
		'Standard Log Download',
		'Log File Collection',
		'Log File Download',
		'Audit Log Collection',
		'Audit Log Download',
		'Slow Log Collection',
		'Slow Log Download',
		'Access Instance',
		'Uninstall Instance',
		'Add Operational Capability',
		'Edit Operational Capability',
		'Delete Operational Capability',
		'Execute Operational Capability',
		'Log Configuration',
		'Monitor Configuration',
		'Configuration File Path',
		'Upload Configuration File',
		'Edit Configuration File',
		'Download Configuration File',
		'Restore Edit History',
		'【Kafka】Rehash',
		'【RocketMQ】Access Control',
		'【Postgresql】 Agent Authentication',
		'【Elasticsearch】 Edit Console Address',
		'【RabbitMQ】 Edit Console Address',
		'【Kibana】 Edit Console Address',
		'【Nacos】 Edit Console Address',
		'【Skywalking】 Edit Console Address',
		'【Redis】Instance Down',
		'【Redis】Force Restart',
		'【Postgresql】Based On Backup Operation Switch',
		'【Mysql】Backup Database Reconfiguration',
		'【Postgresql】Backup Database Reconfiguration',
		'【Postgresql】Sync Node Downgrade',
		'Edit Backup Tasks',
		'【Postgresql】Data Validity Verification',
		'【Redis】Data Validity Verification',
		'Modify Backup To Receive Notifications'
	],
	ControlledOperationExpert: [
		'Enter Operations Panel/Service Console',
		'Enter Console',
		'Deployment Configuration',
		'Configure Disaster Recovery Relationship',
		'Account Management Add Account',
		'Account Management Delete Account',
		'Account Management Update Permission',
		'Account Management Update Password',
		'Account Management Batch Update Password',
		'Account Management Bind Users'
	]
};
export const maintenances = {
	'Service Publishing': '*********',
	'Service Access': '*********',
	'Service Deletion': '*********',
	'Service Recovery': '*********',
	'Secondary Deletion': '*********',
	'Restart Instance': '*********',
	'Restart Service': '*********',
	'Storage Expansion': '*********',
	'Storage Deletion': '*********',
	'Node Migration': '*********',
	'Modify Specification': '*********',
	'Horizontal Scaling': '*********',
	'Master-Slave Switching': '*********',
	'Service Version Management': '*********',
	'Middleware Version Upgrade': '*********',
	'Create Backup': '*********',
	'Clone Service': '*********',
	'Delete Backup Task': '*********',
	'Delete Backup Record': '*********',
	'Add Service Exposure': '*********',
	'Delete Service Exposure': '*********',
	'Edit Service Exposure': '*********',
	'Add Parameter': '*********',
	'Edit Parameter': '*********',
	'Delete Parameter': '*********',
	'Add Parameter Template': '*********',
	'Edit Parameter Template': '100010027',
	'Delete Parameter Template': '100010028',
	'Use Parameter Template': '100010029',
	'More Add Parameter': '100010030',
	'More Add to List': '100010031',
	'More Edit Parameter': '100010032',
	'More Delete Parameter': '100010033',
	'Alarm Record Management': '100010034',
	'Add Alarm Rule': '100010035',
	'Edit Alarm Rule': '100010036',
	'Delete Alarm Rule': '100010037',
	'Add Alarm Contact': '100010038',
	'Delete Alarm Contact': '100010039',
	'Standard Log Collection': '100010040',
	'Standard Log Download': '100010041',
	'Log File Collection': '100010042',
	'Log File Download': '100010043',
	'Audit Log Collection': '100010044',
	'Audit Log Download': '100010045',
	'Slow Log Collection': '100010046',
	'Slow Log Download': '100010047',
	'Add Operational Capability': '100010048',
	'Edit Operational Capability': '100010049',
	'Delete Operational Capability': '100010050',
	'Execute Operational Capability': '100010051',
	'Log Configuration': '100010052',
	'Extra Log Download': '*********',
	'Monitor Configuration': '100010152',
	'Configuration File Path': '100010053',
	'Upload Configuration File': '100010153',
	'Edit Configuration File': '100010253',
	'Download Configuration File': '*********',
	'Restore Edit History': '*********',
	'【Kafka】Rehash': '*********',
	'【RocketMQ】Access Control': '*********',
	'【Postgresql】 Agent Authentication': '*********',
	'Enter Operations Panel/Service Console': '*********',
	'Enter Console': '*********',
	'Deployment Configuration': '*********',
	'Configure Disaster Recovery Relationship': '*********',
	'Access Instance': '*********',
	'Uninstall Instance': '*********',
	'Service Lock': '*********',
	'Service Unlock': '*********',
	'Account Management Add Account': '*********',
	'Account Management Delete Account': '*********',
	'Account Management Bind Users': '*********',
	'Account Management Update Permission': '*********',
	'Account Management Update Password': '*********',
	'Account Management Batch Update Password': '*********',
	'【Elasticsearch】 Edit Console Address': '*********',
	'【RabbitMQ】 Edit Console Address': '*********',
	'【Kibana】 Edit Console Address': '*********',
	'【Nacos】 Edit Console Address': '*********',
	'【Skywalking】 Edit Console Address': '*********',
	'【Redis】Instance Down': '*********',
	'【Redis】Force Restart': '*********',
	'【Postgresql】Based On Backup Operation Switch': '*********',
	'【Mysql】Backup Database Reconfiguration': '*********',
	'【Postgresql】Backup Database Reconfiguration': '*********',
	'【Postgresql】Sync Node Downgrade': '*********',
	'Edit Backup Tasks': '*********',
	'【Postgresql】Data Validity Verification': '*********',
	'【Redis】Data Validity Verification': '*********',
	'Modify Backup To Receive Notifications': '*********'
};
// * 禁止 middlewareName 命名的一些关键词
export const notAllowedName = ['null', 'undefined'];
// * 服务部署方式
export const DEPLOY_MOD_SERVER = 'server';
export const DEPLOY_MOD_CONTAINER = 'container';
// * 固化中间件
export const FIXED_MIDDLEWARES = [
	'mysql',
	'postgresql',
	'kafka',
	'rocketmq',
	'zookeeper',
	'elasticsearch',
	'redis'
];
// * 公钥
export const PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuXH4e3M0/WhXcEAR7p2Y
HFKxsCOg/c4cF/3wphLq9U5m5zB0NxpDntZjUvECzMN6E9TU51NNcLNPTZAMCbhV
/nhh9J1frJc9jlrTjXr8vnTJcdhgjx6+qcHz5KyURBwYocpjxEjLqiPCmX52oEHr
fCXrEPwUiC6fA7bbhzubZBGl280a6CfzLyWiIvd9boZL8KaJdAUW0hzoyVlYB1YY
M7qVCBsJD5TGFH4FNE6Cdn2hf3+mMYs6cwdHBtzRjJLoWdp0t/SvnFUNccprkFuY
q/3jpL1chaW/fkTQUogZIKKAY7HLsVsRXlPyBqYQSs0Eh6YeAlkiZ3l5NVLXIZpn
XwIDAQAB
-----END PUBLIC KEY-----
`;
// * 私钥
export const PRIVATE_KEY = `-----BEGIN RSA PRIVATE KEY-----MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC5cfh7czT9aFdwQBHunZgcUrGwI6D9zhwX/fCmEur1TmbnMHQ3GkOe1mNS8QLMw3oT1NTnU01ws09NkAwJuFX+eGH0nV+slz2OWtONevy+dMlx2GCPHr6pwfPkrJREHBihymPESMuqI8KZfnagQet8JesQ/BSILp8DttuHO5tkEaXbzRroJ/MvJaIi931uhkvwpol0BRbSHOjJWVgHVhgzupUIGwkPlMYUfgU0ToJ2faF/f6YxizpzB0cG3NGMkuhZ2nS39K+cVQ1xymuQW5ir/eOkvVyFpb9+RNBSiBkgooBjscuxWxFeU/IGphBKzQSHph4CWSJneXk1UtchmmdfAgMBAAECggEAfFBfj3oakqmIvBHnM3tnxczVmss3mAy8HoU39kyGP22QY/jCX1H29JqpOKeiBdwgkPzCwFPCfQNm7QrcVwPAefb/S3jX8x3rwOs4GAbTOWWWyP+RM0Ab9jazpZ7fQY1IIeVH3gQiq2qPgFAbe7DVCINuwhMKPIRsSpQhM9WMhJZ/r4pCvCixsqbSckj3fkB4yu6eP3eYzeYA+SJaNnAyIjZd/W/4I59cevuqw8k72HY4qPE9kGf4oYj/PIBrY8vMxouHifFyABn7TRXU90enaHvPSCaXPz9bnBRmIDS9x9mrPAGtaSHhFM7zjh0ok3Mq8njwAfSSu3gC4HPTAno8kQKBgQDiXTa7DujP6IaxPTs/2Q15BNPefo0aH+XL2tCBgpe5vmqxF3ZDD0M15KU+Zy8QhYtL78AxThNfS6pC0PBT9txXTC7aZoCrGJpfvpZCFZSHly02RW5jLkkFgPExrhi8uNNW4IQLw6vq8iWcdftj59IsIdr6FYw9aiAiHxgrgnkw1wKBgQDRuVMNhwHEoX2iC4nxVekg5AGmejQ++dwrwUebnrBqabmXjGgpst5JN5oLQT6QTsj9lWyL2uM8oDN+gwSKGZ58Y37H1hytHgGk1LJnpdX/WUmf/N9vnqiVff5o03FQJ1GPOzuKOLqoQ0laKAymuxkb1JV/3ufA0iSS/fkPlrtEuQKBgQCTKEAFWUr2plax8HmNJ2D/1Kib0y+N1UDBxu2X1Dso0GZx3CvQqqJp7cuTBuds2TRWWHVcWEJ1yC3WpBgorapOwkMPTmXFVQg+yBrMuVTG1PvadUavCY8FGe8y+dgxEAKsTubo8vrXkTXsQjL8nt9eO2AtBkcWQeDD7ez7lCWPHwKBgASR2TFuQ6eZLTeesL7FdHL3BOIvv3uvzhBWAc1uQ8HN0/Ftacee0iEeSQj9vvM+BDsiSl8xwcnMY9LZ3/tyW3WyZUzNaYqMINUKnWiMwIDz3RrTU7sG5f175VM7aDiq4qgQTlfQrKCF9s80GDJ52S42D5vdq4cUDR/20Lp6OlJhAoGAMvQhllWp3fA8GiIJQ2YXHHI/Y5/ELL3bISm+YjnEyD3wEC72pu3C4yBcS+N7Y0nD7LW5L4zw9spBIoIpO/71f1hvixnwYhXn4YcR47Uo2Qu1nA6G/8ujwKkYt7ruB3PVWyWefGROA1z3lSA7i8Bk7oxfd37gayq8FC1Svc4jsMM=-----END RSA PRIVATE KEY-----
`;
// * 需要走马上办的操作Id
export const NeedThirdPartyOperators = [
	{
		operatorAliasName: '配置文件-修改配置文件',
		operatorId: maintenances['Edit Configuration File']
	},
	{
		operatorAliasName: '运维能力-执行运维能力',
		operatorId: maintenances['Execute Operational Capability']
	},
	{
		operatorAliasName: '重启服务',
		operatorId: maintenances['Restart Service']
	},
	{
		operatorAliasName: '中间件版本升级',
		operatorId: maintenances['Middleware Version Upgrade']
	},
	{
		operatorAliasName: '服务接入',
		operatorId: maintenances['Service Access']
	},
	{
		operatorAliasName: '数据备份-创建备份',
		operatorId: maintenances['Create Backup']
	},
	{
		operatorAliasName: '数据备份-克隆服务',
		operatorId: maintenances['Clone Service']
	},
	{
		operatorAliasName: '服务删除',
		operatorId: maintenances['Service Deletion']
	},
	{
		operatorAliasName: '二次删除',
		operatorId: maintenances['Secondary Deletion']
	},
	{
		operatorAliasName: '账户管理-账户密码修改',
		operatorId: maintenances['Account Management Update Password']
	},
	{
		operatorAliasName: '账户管理-账户批量修改密码',
		operatorId: maintenances['Account Management Batch Update Password']
	}
];
export const serviceDetailMenu = [
	{
		label: '基本信息',
		key: 'basicInfo',
		code: 'baseInfo'
	},
	{
		label: '实例详情',
		key: 'highAvailability',
		code: 'InstanceDetail'
	},
	{
		label: '运维操作',
		key: 'operatorAbility',
		code: 'ops'
	},
	{
		label: '数据安全',
		key: 'backupRecovery',
		code: 'dataSecurity'
	},
	{
		label: '服务暴露',
		key: 'externalAccess',
		code: 'serviceExposure'
	},
	{
		label: '数据监控',
		key: 'monitor',
		code: 'dataMonitor'
	},
	{
		label: '日志详情',
		key: 'log',
		code: 'logDetail'
	},
	{
		label: '参数设置',
		key: 'paramterSetting',
		code: 'parameter'
	},
	{
		label: '服务告警',
		key: 'alarm',
		code: 'alarm'
	},
	{
		label: '灾备服务',
		key: 'disaster',
		code: 'disasterBackup'
	},
	{
		label: '运维能力',
		key: 'operator',
		code: 'opsPower'
	},
	{
		label: '配置文件',
		key: 'file',
		code: 'configFile'
	},
	{
		label: '操作审计',
		key: 'audit',
		code: 'operationsAudit'
	}
];

export const middlewareTypes = [
	'mysql',
	'redis',
	'zookeeper',
	'kafka',
	'rocketmq',
	'elasticsearch',
	'postgresql',
	'minio',
	'mongodb',
	'kibana',
	'nacos',
	'logstash',
	'rabbitmq'
];
export const authenticationType = [
	{ label: 'local', value: 'local' },
	{ label: 'host', value: 'host' },
	{ label: 'hostssl', value: 'hostssl' },
	{ label: 'hostnossl', value: 'hostnossl' },
	{ label: 'hostgssenc', value: 'hostgssenc' },
	{ label: 'hostnogsssenc', value: 'hostnogsssenc' }
];
export const authenticationMode = [
	{ label: 'trust', value: 'trust' },
	{ label: 'reject', value: 'reject' },
	{ label: 'scram-sha-256', value: 'scram-sha-256' },
	{ label: 'md5', value: 'md5' },
	{ label: 'password', value: 'password' },
	{ label: 'gss', value: 'gss' },
	{ label: 'sspi', value: 'sspi' },
	{ label: 'ident', value: 'ident' },
	{ label: 'peer', value: 'peer' },
	{ label: 'Idap', value: 'Idap' },
	{ label: 'radius', value: 'radius' },
	{ label: 'cert', value: 'cert' },
	{ label: 'pam', value: 'pam' },
	{ label: 'bsd', value: 'bsd' }
];
export const labelSimple: { [propName: string]: string } = {
	alertmanager: 'CPU：0.2核；内存：0.5G；存储：0G',
	prometheus: 'CPU：1核；内存：2G；存储：10G',
	logging: 'CPU：2.5核；内存：7G；存储：5G',
	minio: 'CPU：0.5核；内存：1G；存储：20G',
	grafana: 'CPU：1核；内存：1G；存储：0G',
	'middleware-controller': 'CPU：0.5核；内存：0.5G；存储：0G',
	'middleware-scheduler': 'CPU：0.1核；内存：0G；存储：0G',
	'middlewarebackup-controller': 'CPU：0.2核；内存：0.5G；存储：0G',
	'middleware-admission-webhook': 'CPU：0.2核；内存：0.5G；存储：0G',
	'fs-exporter': 'CPU：0.1核；内存：0.18G；存储：0G',
	'gossip-operator': 'CPU：0.1核；内存：0.25G；存储：0G',
	'extra-middleware-controller': 'CPU：0.1核；内存：0.125G；存储：0G',
	replicator: 'CPU：0.2核；内存：0.5G；存储：0G'
};
export const labelHigh: { [propName: string]: string } = {
	alertmanager: 'CPU：0.6核；内存：1.5G；存储：0G',
	prometheus: 'CPU：3核；内存：6G；存储：30G',
	logging: 'CPU：4.5核；内存：15G；存储：15G',
	minio: 'CPU：1.5核；内存：3G；存储：30G',
	grafana: 'CPU：3核；内存：3G；存储：0G',
	'middleware-controller': 'CPU：1.5核；内存：1.5G；存储：0G',
	'middleware-scheduler': 'CPU：0.3核；内存：0G；存储：0G',
	'middlewarebackup-controller': 'CPU：0.6核；内存：1.5G；存储：0G',
	'middleware-admission-webhook': 'CPU：0.6核；内存：1.5G；存储：0G',
	'fs-exporter': 'CPU：0.3核；内存：0.54G；存储：0G',
	'gossip-operator': 'CPU：0.3核；内存：0.75G；存储：0G',
	'extra-middleware-controller': 'CPU：0.3核；内存：0.375G；存储：0G',
	replicator: 'CPU：0.6核；内存：1.5G；存储：0G'
};
export const name: { [propsName: string]: string } = {
	alertmanager: '监控告警',
	prometheus: '数据监控',
	logging: '日志采集',
	minio: '备份存储',
	grafana: '监控面板',
	ingress: '负载均衡',
	'local-path': '资源存储',
	'middleware-controller': '中间件管理',
	'middleware-scheduler': '扩展调度器',
	lvm: 'LVM存储',
	'middlewarebackup-controller': '备份控制器',
	'middleware-admission-webhook': '中间件WebHook',
	'fs-exporter': '存储监控',
	'gossip-operator': '灾备同步器',
	'extra-middleware-controller': '客户端控制器',
	replicator: '数据同步管理'
};
export const color: { [propsName: string]: string } = {
	alertmanager: '#12C1C6',
	prometheus: '#F7786C',
	logging: '#6069FF',
	minio: '#846CF7',
	grafana: '#60C1FF',
	ingress: '#FFAA3A',
	'local-path': '#E871AF',
	'middleware-controller': '#C5D869',
	lvm: '#EAC110',
	'middleware-scheduler': '#d3adf7',
	'middlewarebackup-controller': '#8ba3c7',
	'middleware-admission-webhook': '#92ab9c',
	'fs-exporter': '#4fa06b',
	'gossip-operator': '#E9AB86',
	'extra-middleware-controller': '#f5d47b',
	replicator: '#E9AB86'
};
export const icon: { [propsName: string]: string } = {
	alertmanager: 'icon-gaojingshijian1',
	prometheus: 'icon-shujujiankong1',
	logging: 'icon-rizhicaiji',
	minio: 'icon-beifen',
	grafana: 'icon-shujujiankong',
	ingress: 'icon-fuzaijunheng',
	'local-path': 'icon-ziyuan-cunchu',
	'middleware-controller': 'icon-zhongjianjianguanli',
	lvm: 'icon-cunchu1',
	'middleware-scheduler': 'icon-kuozhantiaoduqi-1',
	'middlewarebackup-controller': 'icon-backups',
	'middleware-admission-webhook': 'icon-webhook',
	'fs-exporter': 'icon-cunchujiankong1',
	'gossip-operator': 'icon-zaibeishiliicon',
	'extra-middleware-controller': 'icon-kehuduanguanli',
	replicator: 'icon-jifen'
};
export const status: any = {
	Syncing: '同步中',
	StopSyncing: '停止同步',
	Error: '错误',
	switching: '切换中'
};
export const recoverStatusList: any = {
	Running: '进行中',
	Success: '成功',
	Failed: '失败'
};
export const modeAliasList: any = {
	simple: 'N主',
	regular: 'N主N数据',
	complex: 'N主N数据N协调',
	'complex-cold': 'N主N数据N冷',
	'cold-complex': 'N主N数据N冷N协调',
	cluster: '集群模式',
	sentinel: '哨兵模式',
	agent: '集群代理模式',
	readWriteProxy: '哨兵代理模式',
	'1m-1s': '一主一从',
	'1m-3s': '一主三从',
	'1m-ns': '一主多从',
	'1m-0s': '单实例'
};
export const modeTypeList = [
	{
		label: 'N主',
		value: 'simple'
	},
	{
		label: 'N主N数据',
		value: 'regular'
	},
	{
		label: 'N主N数据N协调',
		value: 'complex'
	},
	{
		label: 'N主N数据N冷',
		value: 'complex-cold'
	},
	{
		label: 'N主N数据N冷N协调',
		value: 'cold-complex'
	},
	{
		label: '集群模式',
		value: 'cluster'
	},
	{
		label: '哨兵模式',
		value: 'sentinel'
	},
	{
		label: '集群代理模式',
		value: 'agent'
	},
	{
		label: '哨兵代理模式',
		value: 'readWriteProxy'
	},
	{
		label: '一主一从',
		value: '1m-1s'
	},
	{
		label: '一主多从',
		value: '1m-ns'
	},
	{
		label: '单实例',
		value: '1m-0s'
	}
];

export const adminRole = {
	id: 1,
	userName: 'admin',
	nameList: null,
	aliasName: '超级管理员',
	password: null,
	email: null,
	phone: null,
	createTime: null,
	passwordTime: null,
	userRoleList: [
		{
			userName: 'admin',
			organId: null,
			projectId: null,
			roleId: 1,
			roleName: '超级管理员',
			roleType: 'manager',
			weight: 1,
			power: {
				elasticsearch: '1111',
				rocketmq: '1111',
				zookeeper: '1111',
				postgresql: '1111',
				kafka: '1111',
				minio: '1111',
				mysql: '1111',
				rabbitmq: '1111',
				mongodb: '1111',
				redis: '1111'
			},
			organName: null,
			projectName: null
		}
	],
	roleId: null,
	roleName: null,
	power: null,
	isAdmin: true,
	isDba: null,
	manager: 1,
	dba: null,
	failedTimeList: [],
	lockStatus: 'unlocked',
	enable: true,
	passwordExpireData: 0,
	wxUserInfo: null,
	thirdPartyOAUserInfo: null
};

export const exampleProject = {
	name: '应用服务平台',
	aliasName: '应用服务平台',
	description: '应用服务平台开发测试',
	user: null,
	clusterList: null,
	organId: '17da50d2422242b4',
	projectId: '15321cf3f00a4e5c',
	memberCount: 0,
	namespaceCount: 1,
	middlewareCount: null,
	roleId: 1,
	roleName: '超级管理员',
	roleWeight: null,
	createTime: '2025-04-09 20:55:44',
	userDtoList: null,
	tenantAliasName: null,
	backupServerList: null,
	pmUserList: null
};
export const exampleProjectId = '15321cf3f00a4e5c';

export const exampleOrganId = '17da50d2422242b4';

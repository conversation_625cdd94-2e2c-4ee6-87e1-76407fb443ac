import { useEffect } from 'react';
export default function useRefresh(func: any) {
	useEffect(() => {
		const handleDocumentClick = (event: any) => {
			// console.log(event);
			// 处理点击事件逻辑
			if (event.target.id === 'detailRefresh') {
				func();
				return;
			}
			if (event.target?.parentElement?.id === 'detailRefresh') {
				func();
				return;
			}
			if (
				event.target?.parentElement?.target?.parentElement?.id ===
				'detailRefresh'
			) {
				func();
				return;
			}
		};
		// 添加点击事件监听器
		document.addEventListener('click', handleDocumentClick);
		// 在组件卸载时移除监听器，以防止内存泄漏
		return () => {
			document.removeEventListener('click', handleDocumentClick);
		};
	}, []);
}

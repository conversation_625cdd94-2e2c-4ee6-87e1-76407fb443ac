interface patternProps {
	[propsName: string]: string;
}

const pattern: patternProps = {
	name: '^[a-z][a-z0-9-]{0,22}[a-z0-9]$',
	clusterName: '^[a-z][a-z0-9-]{0,38}[a-z0-9]$',
	labels: '^[a-zA-Z0-9-./_]+[=][a-zA-Z0-9-./_]+([,][a-zA-Z0-9-./_]+[=][a-zA-Z0-9-./_]+)*$',
	path: '^/$|^(/[A-Za-z0-9]+([-_.][A-Za-z0-9]+)*)+$',
	agentPath: '^/$|^(/[A-Za-z0-9]+([-_.\'"][A-Za-z0-9]+)*)+$',
	domain: '[a-z0-9]([-a-z0-9]*[a-z0-9])?(.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*',
	host: '^([0-9]|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.([0-9]|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.([0-9]|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.([0-9]|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])$',
	ip: '^([0-9]|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.([0-9]|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.([0-9]|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.([0-9]|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])$',
	posInt: '^[1-9]{1,21}\\d*$',
	nickname: '^[\u4E00-\u9FA5A-Za-z0-9_.-]{2,80}$',
	mysqlPwd:
		'^(?![0-9]+$)(?![a-z]+$)(?![A-Z]+$)(?![@~!%^*-/_=+?,()&]+$)(?![0-9a-z]+$)(?![0-9A-Z]+$)(?![0-9@~!%^*-/_=+?,()&]+$)(?![a-zA-Z]+$)(?![a-z@~!%^*-/_=+?,()&]+$)(?![A-Z@~!%^*-/_=+?,()&]+$)[0-9a-zA-Z@~!%^*-/_=+?,()&]{8,32}$',
	pwd: '^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z@~!%^*-/_=+?,()&]+$)(?![a-z0-9]+$)(?![a-z@~!%^*-/_=+?,()&]+$)(?![0-9@~!%^*-/_=+?,()&]+$)[a-zA-Z0-9@~!%^*-/_=+?,()&]{3,}$',
	phone: '^1(3\\d|4[5-9]|5[0-35-9]|6[567]|7[0-8]|8\\d|9[0-35-9])\\d{8}$',
	email: '^(([a-zA-Z0-9_\\-\\.]+)@([a-zA-Z0-9_\\-\\.]+)\\.([a-zA-Z]{2,5}){1,25})+([;.](([a-zA-Z0-9_\\-\\.]+)@([a-zA-Z0-9_\\-\\.]+)\\.([a-zA-Z]{2,5}){1,25})+)*$',
	userName: '^[A-Za-z0-9-]{1,25}$',
	aliasName: '^[\u4E00-\u9FA5A-Za-z0-9_.-]{1,18}$',
	roleName: '^[\u4E00-\u9FA5A-Za-z0-9_.-]{1,10}$',
	ingressName: '^[a-z][a-z0-9-]{1,30}[a-z0-9]$',
	paramTemplateName: '^[a-z0-9-]{2,30}$',
	databaseUser: '^[0-9a-zA-Z_-]{1,32}$',
	databaseName: '^[a-zA-Z][0-9a-zA-Z_-]{0,62}[0-9a-zA-Z]$',
	dbName: '^.{1,64}$',
	projectName: '^[a-z][a-z0-9-]{0,38}[a-z0-9]$',
	projectAliasName: '^[\u4E00-\u9FA5A-Za-z0-9_.-]{0,64}$',
	backupAliasName: '^[\u4E00-\u9FA5]*$',
	backupName: '^[\u4E00-\u9FA5A-Za-z0-9]{1,15}$',
	storageName: '^[\u4E00-\u9FA5A-Za-z0-9./-]{1,32}$',
	modeName: '^[\u4E00-\u9FA5A-Za-z0-9_.-]{1,64}$',
	onlyNumber: '^[0-9]*$',
	serverName: '^[A-Za-z0-9]{1,32}$',
	positionName: '^[\u4E00-\u9FA5A-Za-z0-9]{1,32}$',
	mirrorPwd: '^[A-Za-z0-9]{8,16}$',
	organizationName: '^[\u4E00-\u9FA5A-Za-z0-9_.-]{1,20}$',
	alarmRuleName: '^[\u4E00-\u9FA5A-Za-z0-9_.-]{1,32}$',
	alarmRuleFileName: '^[a-z][a-z0-9-.]{0,62}[a-z0-9]$',
	namespace: '^[a-z][a-z0-9-]{0,38}[a-z0-9]$',
	zkPath: '^/[a-zA-Z0-9]*$',
	label: '^[a-zA-Z0-9-./_]+[=]([a-zA-Z0-9-./_]+)?$',
	paramName: '^[A-Za-z]+[a-zA-Z0-9\\W]+$',
	operatorName: '^[\u4E00-\u9FA5A-Za-z0-9_./-]{2,32}$',
	version: '^[A-Za-z0-9_.-]*$',
	agentInstanceName: '^[a-z0-9_.-]*$',
	esDomain:
		'^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$',
	projectId: '^[A-Za-z0-9]{1,16}$',
	esAccount: '^(?!\\s)(?!.*\\s$)[\x20-\x7E]{1,507}$',
	notSupportChinese: '^[^\u4E00-\u9FFF]+$'
};

export default pattern;

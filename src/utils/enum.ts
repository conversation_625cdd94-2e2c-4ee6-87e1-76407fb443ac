export enum stateProps {
	'error' = 'error',
	'loading' = 'loading',
	'success' = 'success',
	'warning' = 'warning'
}
export enum versionStatus {
	now = '当前版本',
	future = '可安装升级版本',
	history = '历史版本',
	updating = '升级中'
}
export enum serviceVersionStatus {
	now = '当前版本',
	future = '可升级版本',
	history = '历史版本',
	updating = '升级中'
}

export enum MiddlewareType {
	db = '数据库',
	mq = '消息队列',
	mse = '微服务引擎',
	storage = '存储',
	'other' = '其他'
}

export enum agentStatus {
	'Online' = '运行中',
	'Offline' = '离线',
	'Terminating' = '卸载中'
}

export enum nacosAction {
	'r' = '只读',
	'w' = '只写',
	'rw' = '读写'
}
export enum rabbitmqRole {
	'administrator' = '超级管理员',
	'monitoring' = '监控者',
	'policymaker' = '策略制定者',
	'management' = '普通管理者',
	'none' = '无角色'
}
export enum rabbitmqPermission {
	'configure' = '配置权限',
	'write' = '写权限',
	'read' = '读权限'
}

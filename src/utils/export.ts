import axios from 'axios';
import cache from '@/utils/storage';
import { TOKEN } from '@/services/request';
export const exportFile = (
	url: string,
	params: any,
	name: string,
	type: string
) => {
	axios
		.post(url, params, {
			responseType: 'blob',
			headers: {
				userToken: cache.getSession(TOKEN),
				mwToken: cache.getSession('mwToken')
			}
		})
		.then((res: any) => {
			const blob = new Blob([res.data]);
			const eLink = document.createElement('a');
			eLink.download = name + type;
			eLink.href = URL.createObjectURL(blob);
			eLink.click();
		});
};

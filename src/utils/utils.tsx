import React from 'react';
import { Tooltip, Badge, Tag, Space, message } from 'antd';
import { api } from '@/api.json';
import JSEncrypt from 'jsencrypt';
import moment from 'moment';
import { renderFormItem } from '@/components/renderFormItem';
import FormBlock from '@/components/FormBlock';
import storage from './storage';
import nodata from '@/assets/images/nodata.svg';
import {
	CheckCircleFilled,
	DeleteOutlined,
	ExclamationCircleFilled,
	QuestionCircleOutlined,
	SmallDashOutlined,
	SyncOutlined
} from '@ant-design/icons';
import { IconFont } from '@/components/IconFont';
import {
	CONTROLLED_OPERATION_BASE,
	CONTROLLED_OPERATION_Maintenance,
	CONTROLLED_OPERATION_EXPERT,
	// MIDDLEWARE_DECOMMISSIONING,
	MIDDLEWARE_DEPLOYMENT,
	QUOTA_REQUEST_FOR_NAMESPACE,
	QUOTA_REQUEST_FOR_ORGAN,
	QUOTA_REQUEST_FOR_PROJECT,
	SERVER_INTEGRATE_FOR_PROJECT,
	SERVER_INTEGRATE_FOR_ORGAN
} from './const';
import pattern from './pattern';
// * 组件复用
export const statusRender: (value: string) => JSX.Element = (value: string) => {
	switch (value) {
		case 'Creating':
			return (
				<div className="display-flex flex-align">
					<SyncOutlined
						style={{ color: '#0091FF', marginRight: 4 }}
					/>{' '}
					启动中
				</div>
			);
		case 'Running':
			return (
				<div className="display-flex flex-align">
					<CheckCircleFilled
						style={{ color: '#00A700', marginRight: 4 }}
					/>{' '}
					运行正常
				</div>
			);
		case 'Failed':
			return (
				<div className="display-flex flex-align">
					<ExclamationCircleFilled
						style={{ color: '#C80000', marginRight: 4 }}
					/>{' '}
					运行异常
				</div>
			);
		case 'RunningError':
			return (
				<div className="display-flex flex-align">
					<ExclamationCircleFilled
						style={{ color: '#C80000', marginRight: 4 }}
					/>{' '}
					运行异常
				</div>
			);
		case 'Recover':
			return (
				<div className="display-flex flex-align">
					<SyncOutlined
						style={{ color: '#0091FF', marginRight: 4 }}
					/>{' '}
					恢复中
				</div>
			);
		case 'GracefulRestart':
			return (
				<div className="display-flex flex-align">
					<SyncOutlined
						style={{ color: '#0091FF', marginRight: 4 }}
					/>{' '}
					重启中
				</div>
			);
		default:
			return (
				<div className="display-flex flex-align">
					<ExclamationCircleFilled
						style={{ color: '#C80000', marginRight: 4 }}
					/>{' '}
					运行异常
				</div>
			);
	}
};

export const agentStatusRender: (value: string) => JSX.Element = (
	value: string
) => {
	switch (value) {
		case 'Pending':
			return (
				<div className="display-flex flex-align">
					<SyncOutlined
						style={{ color: '#0091FF', marginRight: 4 }}
					/>{' '}
					运行异常
				</div>
			);
		case 'Running':
			return (
				<div className="display-flex flex-align">
					<CheckCircleFilled
						style={{ color: '#00A700', marginRight: 4 }}
					/>{' '}
					运行正常
				</div>
			);
		case 'Unhealthy':
			return (
				<div className="display-flex flex-align">
					<ExclamationCircleFilled
						style={{ color: '#C80000', marginRight: 4 }}
					/>{' '}
					运行异常
				</div>
			);
		case 'Deleted':
			return (
				<div className="display-flex flex-align">
					<SyncOutlined
						style={{ color: '#0091FF', marginRight: 4 }}
					/>{' '}
					删除中
				</div>
			);
		default:
			return (
				<div className="display-flex flex-align">
					<ExclamationCircleFilled
						style={{ color: '#C80000', marginRight: 4 }}
					/>{' '}
					运行异常
				</div>
			);
	}
};

// * 备份列表状态
export const statusBackupRender: (
	value: string,
	record: any,
	index: number
) => JSX.Element = (value: string, record: any, index: number) => {
	switch (value) {
		case 'Running':
			return <Badge status="warning" text="备份中" />;
		case 'Failed':
			return (
				<Tooltip title={record?.reason}>
					<Badge status="error" text="备份失败" />
				</Tooltip>
			);
		case 'Success':
			return <Badge status="success" text="备份成功" />;
		case 'Creating':
			return <Badge status="processing" text="创建中" />;
		case 'Deleting':
			return <Badge status="default" text="删除中" />;
		case 'RecycleFailed':
			return <Badge status="default" text="数据回收失败" />;
		case 'DeleteFailed':
			return <Badge status="default" text="删除失败" />;
		case 'Waiting':
			return <Badge status="warning" text="等待中" />;
		default:
			return <Badge status="default" text="未知" />;
	}
};
// * 备份记录列表状态
export const statusRecordRender: (
	value: string,
	record: any,
	index: number
) => JSX.Element = (value: string, record: any, index: number) => {
	switch (value) {
		case 'Running':
			return <Badge status="warning" text="备份中" />;
		case 'Failed':
			return (
				<Tooltip title={record?.reason}>
					<Badge status="error" text="备份失败" />
				</Tooltip>
			);
		case 'Success':
			return <Badge status="success" text="备份成功" />;
		case 'Deleting':
			return <Badge status="default" text="删除中" />;
		case 'DeleteFailed':
			return <Badge status="default" text="删除失败" />;
		default:
			return <Badge status="default" text="未知" />;
	}
};
// * 克隆记录列表状态
export const statusRestoreRender: (
	value: string,
	record: any,
	index: number
) => JSX.Element = (value: string, record: any, index: number) => {
	switch (value) {
		case 'Running':
			return <Badge status="warning" text="恢复中" />;
		case 'Failed':
			return (
				<Tooltip title={record?.reason}>
					<Badge status="error" text="恢复失败" />
				</Tooltip>
			);
		case 'Success':
			return <Badge status="success" text="恢复成功" />;
		case 'Deleting':
			return <Badge status="default" text="删除中" />;
		default:
			return <Badge status="default" text="未知" />;
	}
};

// * 报警阈值中使用
export const alarmStatusRender: (value: string) => JSX.Element = (
	value: string
) => {
	switch (value) {
		case 'ok':
			return (
				<>
					<CheckCircleFilled style={{ color: '#00A700' }} /> 正常
				</>
			);
		case 'unknown':
			return (
				<>
					<ExclamationCircleFilled style={{ color: '#C80000' }} />{' '}
					未知
				</>
			);
		case 'creating':
			return (
				<>
					<SyncOutlined style={{ color: '#0091FF' }} /> 创建中
				</>
			);
		case 'deleting':
			return (
				<>
					<ExclamationCircleFilled style={{ color: '#C80000' }} />{' '}
					删除中
				</>
			);
		default:
			return (
				<>
					<ExclamationCircleFilled style={{ color: '#C80000' }} />{' '}
					未知
				</>
			);
	}
};
// * 服务列表中使用
export const serviceListStatusRender: (
	value: string,
	record: any,
	index: number
) => JSX.Element = (value: string, record: any) => {
	switch (value) {
		case 'Creating':
			return (
				<>
					<SyncOutlined style={{ color: '#0091FF' }} /> 启动中
				</>
			);
		case 'Recover':
			return (
				<>
					<SyncOutlined style={{ color: '#0091FF' }} /> 恢复中
				</>
			);
		case 'GracefulRestart':
			return (
				<>
					<SyncOutlined style={{ color: '#0091FF' }} /> 重启中
				</>
			);
		case 'Switching':
			return (
				<>
					<SyncOutlined style={{ color: '#0091FF' }} /> 切换中
				</>
			);
		case 'Upgrading':
			return (
				<>
					<SyncOutlined style={{ color: '#0091FF' }} /> 更新中
				</>
			);
		case 'Running':
			return (
				<>
					<CheckCircleFilled style={{ color: '#00A700' }} /> 运行正常
				</>
			);
		case 'Failed':
			return (
				<Tooltip
					title={() => {
						return (
							<>
								中间件状态异常原因 <br />
								<span
									style={{
										lineHeight: '18px',
										color: '#FA6400'
									}}
								>
									{record.reason}
								</span>
							</>
						);
					}}
				>
					<ExclamationCircleFilled style={{ color: '#C80000' }} />{' '}
					运行异常
				</Tooltip>
			);
		case 'RunningError':
			return (
				<Tooltip
					title={() => {
						return (
							<>
								中间件状态异常原因 <br />
								<span
									style={{
										lineHeight: '18px',
										color: '#FA6400'
									}}
								>
									{record.reason}
								</span>
							</>
						);
					}}
				>
					<ExclamationCircleFilled style={{ color: '#C80000' }} />{' '}
					运行异常
				</Tooltip>
			);
		case 'Preparing':
			return (
				<Tooltip
					title={() => {
						return (
							<>
								中间件状态异常原因 <br />
								<span
									style={{
										lineHeight: '18px',
										color: '#FA6400'
									}}
								>
									服务创建中，无法操作
								</span>
							</>
						);
					}}
				>
					<SyncOutlined style={{ color: '#0091FF' }} /> 创建中
				</Tooltip>
			);
		case 'failed':
			return (
				<Tooltip
					title={() => {
						return (
							<>
								中间件状态异常原因 <br />
								<span
									style={{
										lineHeight: '18px',
										color: '#FA6400'
									}}
								>
									服务创建失败，无法操作
								</span>
							</>
						);
					}}
				>
					<ExclamationCircleFilled style={{ color: '#C80000' }} />{' '}
					创建失败
				</Tooltip>
			);
		case 'Deleted':
			return (
				<>
					<DeleteOutlined style={{ color: '#888888' }} />
					已删除
				</>
			);
		case 'Deleting':
			return (
				<>
					<DeleteOutlined style={{ color: '#888888' }} />
					数据删除中
				</>
			);
		case '':
			return <></>;
		default:
			return (
				<Tooltip
					title={() => {
						return (
							<>
								中间件状态异常原因 <br />
								<span
									style={{
										lineHeight: '18px',
										color: '#FA6400'
									}}
								>
									{record.reason}
								</span>
							</>
						);
					}}
				>
					<ExclamationCircleFilled style={{ color: '#C80000' }} />{' '}
					运行异常
				</Tooltip>
			);
	}
};
export const iconTypeRender = (value: string, record: any) => {
	return (
		<div className="icon-type-content">
			<img
				width={14}
				height={14}
				src={
					record.imagePath
						? `${api}/images/middleware/${record.imagePath}`
						: nodata
				}
				alt={record.chartName}
			/>
			{value}
		</div>
	);
};
export const timeRender = (value: string, record: any, index: number) => {
	return value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '/';
};
// * 简单表格列为空
export const nullRender: (value: string | null) => JSX.Element = (
	value: string | null
) => {
	return (
		<div className="text-overflow-one" title={value || '/'}>
			{value || '/'}
		</div>
	);
};
// * 蓝字显示
export const nameRender = (value: string) => {
	return <span className="name-link">{value}</span>;
};
// * 表格超出长度用气泡显示
export const tooltipRender = (
	value: string,
	index: number,
	record: any,
	width: number
) => {
	const e1 = document.createElement('div');
	e1.className = 'hidden';
	e1.innerText = value;
	document.body.appendChild(e1);
	if (e1.clientWidth > width) {
		document.body.removeChild(e1);
		return (
			<Tooltip title={value}>
				<div
					className="mid-table-col"
					style={{ width: `${width - 32}px` }}
				>
					{value}
				</div>
			</Tooltip>
		);
	} else {
		document.body.removeChild(e1);
		return (
			<div className="mid-table-col" style={{ width: `${width - 32}px` }}>
				{value}
			</div>
		);
	}
};
export const questionTooltipRender = (value: string) => {
	return (
		<Tooltip title={value}>
			<QuestionCircleOutlined />
		</Tooltip>
	);
};
export const defaultValueRender = (value: string) => {
	return (
		<div title={value} style={{ width: '100%' }} className="text-overflow">
			{value}
		</div>
	);
};
export const nullToZeroRender = (value: string) => {
	return value || 0;
};
// * 浏览器复制到剪切板方法
export const copyValue = (value: any) => {
	const input = document.createElement('input');
	document.body.appendChild(input);
	input.style.position = 'absolute';
	input.style.bottom = '0px';
	input.style.opacity = '0';
	input.value = value;
	input.focus();
	input.select();
	if (document.execCommand('copy')) {
		document.execCommand('copy');
	}
	input.blur();
	document.body.removeChild(input);
	message.success('复制成功！');
};
// * 函数复用
// * 判断两个数组中是否含有相同的元素（简单数组）
export const judgeArrays: (
	arr1: Array<string>,
	arr2: Array<string>
) => boolean = (arr1: Array<string>, arr2: Array<string>) => {
	const arr1Temp = Array.from(new Set(arr1));
	const arr2Temp = Array.from(new Set(arr2));
	const allArrays = [...arr1Temp, ...arr2Temp];
	const newArrays = Array.from(new Set(allArrays));
	return allArrays.length !== newArrays.length;
};
// * 公钥加密
export const encrypt = (text: string, publicKey: string) => {
	const encrypt = new JSEncrypt();
	encrypt.setPublicKey(publicKey);
	const encrypted = encrypt.encrypt(text);
	return encrypted;
};
// * 私钥解密
export const decrypt = (text: string, privateKey: string) => {
	const decrypt = new JSEncrypt();
	decrypt.setPrivateKey(privateKey);
	const decrypted = decrypt.decrypt(text);
	return decrypted || '解密失败';
};
// * 判断某个对象数据的某些属性是否为空字符传
export const judgeObjArrayAttrIsNull: (
	arr: any[],
	...argument: any[]
) => boolean = (arr: any[] = [], ...argument: any[]) => {
	if (arr.length > 0) {
		let flag = false;
		argument.map((key) => {
			flag = arr.find((item) => item[key] === '');
		});
		if (flag) return true;
		return false;
	} else {
		return true;
	}
};
// * 对象数组属性值重复判断（根据某个字段进行判断）
export const judgeObjArrayHeavyByAttr: (arr: any[], attr: string) => boolean = (
	arr: any[],
	attr: string
) => {
	if (arr.length === 0) return false;
	const values = arr.map((item) => item[attr]);
	const t = Array.from(new Set(values));
	return values.length !== t.length;
};
// * 调换对象属性位置
export const changeObjectIndex: (
	obj: any,
	prop: string,
	index: number
) => any = (obj: any, prop: string, index: number) => {
	const keyArr = Object.keys(obj);
	if (keyArr.length > 1) {
		const propIndex = keyArr.indexOf(prop);
		if (propIndex > 0) {
			keyArr.splice(propIndex, 0);
			keyArr.splice(index, 0, prop);
			const result = {};
			for (let i = 0; i < keyArr.length; i++) {
				result[keyArr[i]] = obj[keyArr[i]];
			}
			return result;
		} else {
			return obj;
		}
	} else {
		return obj;
	}
};

// * 获取customForm中的所有variable-递归
export const getCustomFormKeys: (value: any) => string[] = (value: any) => {
	let keys: string[] = [];
	for (let i = 0; i < value.length; i++) {
		if (value[i].subQuestions) {
			keys = [...getCustomFormKeys(value[i].subQuestions), ...keys];
		}
		keys.push(value[i].variable);
	}
	return keys;
};
export const childrenRender = (
	values: any,
	field: any,
	globalCluster: any,
	globalNamespace: any
) => {
	if (values) {
		const keys = Object.keys(values);
		return (
			<div>
				{keys.map((item) => {
					return (
						<FormBlock key={item} title={item}>
							<div className="w-50">
								<ul className="form-layout">
									{values[item].map((formItem: any) => {
										return (
											<React.Fragment
												key={formItem.variable}
											>
												{renderFormItem(
													formItem,
													field,
													globalCluster,
													globalNamespace
												)}
											</React.Fragment>
										);
									})}
								</ul>
							</div>
						</FormBlock>
					);
				})}
			</div>
		);
	}
};
// * 对象数组根据某个字段判断去重
export const objectRemoveDuplicatesByKey = (objArr: any, key: string) => {
	const arr = objArr.reduce((total: any, current: any) => {
		const keys = total.map((item: any) => item[key]);
		return keys.includes(current[key]) ? total : [...total, current];
	}, []);
	return arr;
};
// * 保留小数点位数，默认2位
// * toFixed 保留小数点位数
// * round 当前保留方式是向上取整还是向下取整 默认四舍五入，为floor时向上取整，为ceil时向下取整
export const formatNumber = (value: any, toFixed?: number, round?: string) => {
	let t = 100;
	if (!value && typeof value !== 'number') return '-';
	if (value === 0) return 0;
	const temp = Number(value);
	if (isNaN(temp)) return '-';
	if (toFixed === 0)
		return round === 'floor'
			? Math.floor(temp)
			: round === 'ceil'
			? Math.ceil(temp)
			: Math.round(temp);
	if (toFixed) t = Math.pow(10, toFixed);
	const result =
		round === 'floor'
			? Math.floor(temp * t)
			: round === 'ceil'
			? Math.ceil(temp * t)
			: Math.round(temp * t);
	return result / t;
};

// * 获取符合条件的对象中对象某个key值的和
export const objSum = (obj: any, type: string, disableType: string) => {
	const array: any[] = [];
	Object.keys(obj).forEach((key) => {
		obj[key][disableType] && array.push(obj[key][type]);
	});

	return array.reduce((pre, cur) => pre + cur, 0);
};

// * 防抖
export function debounce(fun: any, wait = 1000) {
	let timer: any;
	return function () {
		if (timer) {
			clearTimeout(timer);
		}
		const isNow = !timer;
		timer = setTimeout(() => {
			timer = null;
		}, wait);
		if (isNow) fun();
	};
}

// 对象格式转换
export function transformObject(obj: any) {
	const result = {};
	for (const [key, value] of Object.entries(obj)) {
		const [newKey, name] = key.split('_').map((str) => str.trim());
		if (!result[newKey]) {
			result[newKey] = {
				targetContainers: ['es-cluster']
			};
		}
		if (name) {
			result[newKey][name] = value;
		} else {
			result[newKey] = value;
		}
	}
	return result;
}

// 过滤对象
export function filterObject(obj: any) {
	const result = {};
	for (const [key, value] of Object.entries(obj)) {
		if (key.includes('-')) {
			result[key] = value;
		}
	}
	return result;
}

export function compareTime(time1: string, time2: string) {
	const date1 = new Date(time1);
	const date2 = new Date(time2);
	return date1 > date2 ? time1 : time2;
}

export function hasNullOrUndefinedValue(obj: any): boolean {
	for (const value of Object.values(obj)) {
		if (value === undefined || value === null) {
			return true;
		}
		if (typeof value === 'object') {
			if (hasNullOrUndefinedValue(value)) {
				return true;
			}
		}
	}
	return false;
}
export function isOdd(number: number | null | undefined): boolean {
	if (number === null || number === undefined) {
		return false; // 或者根据需求返回 true，表示无法确定奇偶性
	}
	return number % 2 !== 0;
}
// * 判断当前文件名是否是无后缀文件
export function judgeFileNameIsNoExtension(file_name: string): boolean {
	if (!file_name.includes('.')) {
		return true;
	}
	const fileExtensions = file_name.split('.').pop();
	if (fileExtensions === '') {
		return true;
	} else {
		return false;
	}
}
export function agentPhaseRender(value: any): JSX.Element {
	switch (value) {
		case 'Online':
			return <Badge status="success" text="运行中" />;
		case 'Offline':
			return <Badge status="error" text="离线" />;
		case 'Terminating':
			return <Badge status="warning" text="卸载中" />;
		case 'unknown':
			return <Badge status="error" text="未知" />;
		default:
			return <Badge status="error" text="未知" />;
	}
}
export function podRoleRender(value: string, type: string) {
	switch (value) {
		case 'master':
			return '主节点';
		case 'slave':
			return '从节点';
		case 'data':
			return '数据节点';
		case 'client':
			return '协调节点';
		case 'cold':
			return '冷节点';
		case 'kibana':
			return 'Kibana';
		case 'nameserver':
			return 'Nameserver';
		case 'exporter':
			return 'Exporter';
		case 'sentinel':
			return '哨兵';
		case 'proxy':
			return '代理';
		case 'syncslave':
			return '同步节点';
		case 'sync_slave':
			return '同步节点';
		case 'default':
			return '/';
		default:
			return value
				? value.substring(0, 1).toUpperCase() + value.substring(1)
				: '/';
	}
}
export const workOrderStatusRender = (value: string) => {
	switch (value) {
		case 'pending':
			return <Badge status="processing" text="待处理" />;
		case 'resolving':
			return <Badge status="success" text="处理中" />;
		case 'waitExecuted':
			return <Badge status="warning" text="待执行" />;
		case 'completed':
			return <Badge status="default" text="已完成" />;
		case 'closed':
			return <Badge status="default" text="已关闭" />;
		default:
			break;
	}
};
export const serviceOnOrOffLineTag: (lock: string) => JSX.Element = (
	lock: string
) => {
	switch (lock) {
		case 'locked':
			return (
				<Tooltip title="该服务已锁定，部分操作需要审核后进行。">
					<IconFont
						type="icon-yisuoding"
						style={{
							fontSize: 34,
							color: '#4c92f5'
						}}
					/>
				</Tooltip>
			);
		case 'locking':
			return (
				<Tooltip title="审批中">
					<IconFont
						type="icon-shenpizhong"
						style={{ fontSize: 36, color: '#ff7a45' }}
					/>
				</Tooltip>
			);
		case 'unlocking':
			return (
				<Tooltip title="审批中">
					<IconFont
						type="icon-shenpizhong"
						style={{ fontSize: 36, color: '#ff7a45' }}
					/>
				</Tooltip>
			);
		default:
			return <></>;
	}
};
export const workOrderStepTagRender: (value: string) => JSX.Element = (
	value: string
) => {
	switch (value) {
		case 'pending':
			return (
				<Tag
					color="default"
					style={{
						border: 'none',
						backgroundColor: '#F0F0F0',
						color: '#979797'
					}}
				>
					未开始
				</Tag>
			);
		case 'published':
			return (
				<Tag color="processing" style={{ border: 'none' }}>
					已提交
				</Tag>
			);
		case 'waitValidate':
			return (
				<Tag color="orange" style={{ border: 'none' }}>
					待审批
				</Tag>
			);
		case 'approved':
			return (
				<Tag color="processing" style={{ border: 'none' }}>
					已通过
				</Tag>
			);
		case 'refused':
			return (
				<Tag color="error" style={{ border: 'none' }}>
					已驳回
				</Tag>
			);
		case 'withDraw':
			return (
				<Tag color="error" style={{ border: 'none' }}>
					已撤回
				</Tag>
			);
		case 'waitExecute':
			return (
				<Tag color="orange" style={{ border: 'none' }}>
					待执行
				</Tag>
			);
		case 'notExecuted':
			return (
				<Tag
					color="default"
					style={{
						border: 'none',
						backgroundColor: '#F0F0F0',
						color: '#979797'
					}}
				>
					未执行
				</Tag>
			);
		case 'failed':
			return (
				<Tag color="error" style={{ border: 'none' }}>
					执行失败
				</Tag>
			);
		case 'succeed':
			return (
				<Tag color="processing" style={{ border: 'none' }}>
					执行成功
				</Tag>
			);
		case 'notOver':
			return (
				<Tag
					color="default"
					style={{
						border: 'none',
						backgroundColor: '#F0F0F0',
						color: '#979797'
					}}
				>
					未结束
				</Tag>
			);
		case 'over':
			return (
				<Tag color="processing" style={{ border: 'none' }}>
					已结束
				</Tag>
			);
		default:
			return (
				<Tag color="error" style={{ border: 'none' }}>
					异常
				</Tag>
			);
	}
};
export const accountRuleRender = (
	middleware_type: string,
	render_type: string
) => {
	if (render_type === 'min') {
		switch (middleware_type) {
			case 'elasticsearch':
				return 6;
			default:
				return undefined;
		}
	} else if (render_type === 'min_max_message') {
		switch (middleware_type) {
			case 'elasticsearch':
				return '请输入大于等于6个字符的密码信息';
			default:
				return undefined;
		}
	} else if (render_type === 'account_pattern') {
		switch (middleware_type) {
			case 'elasticsearch':
				return RegExp(pattern.esAccount);
			default:
				return undefined;
		}
	} else if (render_type === 'account_pattern_message') {
		switch (middleware_type) {
			case 'elasticsearch':
				return '请输入1-507字符长度且不以空格开头或结尾，由英文字母、数字和特殊字符组成的账户名信息';
			default:
				return undefined;
		}
	}
};
export const topicRender = (value: string) => {
	switch (value) {
		case MIDDLEWARE_DEPLOYMENT:
			return '服务锁定/解锁';
		// case MIDDLEWARE_DECOMMISSIONING:
		// 	return '服务解锁';
		case SERVER_INTEGRATE_FOR_ORGAN:
			return '服务器接入-组织内接入';
		case SERVER_INTEGRATE_FOR_PROJECT:
			return '服务器接入-项目内接入';
		case QUOTA_REQUEST_FOR_NAMESPACE:
			return '配额申请-命名空间配额';
		case QUOTA_REQUEST_FOR_PROJECT:
			return '配额申请-项目配额';
		case QUOTA_REQUEST_FOR_ORGAN:
			return '配额申请-组织配额';
		case CONTROLLED_OPERATION_BASE:
			return '受限操作-基础操作';
		case CONTROLLED_OPERATION_Maintenance:
			return '受限操作-运维操作';
		case CONTROLLED_OPERATION_EXPERT:
			return '受限操作-高级操作';
		default:
			return '异常主题';
	}
};
export const orgAndProRender: (value: any, record: WorkOrderItem) => string = (
	value: any,
	record: WorkOrderItem
) => {
	return `${record.organName || '-'}/${record.projectName || '-'}`;
};

export const processorRender = (value: any, record: WorkOrderItem) => {
	const list = [...(record?.userList || [])];
	if (!record.userList) return '--';
	if (record.userList.length === 0) return '--';
	if (record.userList.length === 1) return record.userList[0];
	list.shift();
	return (
		<Space>
			{record.userList[0]}
			<Tooltip title={list?.join(',')}>
				<SmallDashOutlined />
			</Tooltip>
		</Space>
	);
};
// * 对比两个简单数组是否相等
export const compareArrays = (arr1: any, arr2: any) => {
	if (Array.isArray(arr1) && Array.isArray(arr2)) {
		const arr1Temp = arr1.slice().sort();
		const arr2Temp = arr2.slice().sort();
		const result =
			arr1Temp.length === arr2Temp.length &&
			arr1Temp.every(
				(item: string | number, index: number) =>
					item === arr2Temp[index]
			);
		return result;
	}
	return true;
};
export const base64ToImageSrc: (base64_string: string | undefined) => string = (
	base64_string: string | undefined
) => {
	if (!base64_string || base64_string === '') return '加载失败';
	return `data:image/png;base64,${base64_string}`;
};

export const controlledOperationDisabled = (
	operatorType: string,
	locked?: string
): boolean | undefined => {
	const projectId = storage.getSession('projectId');
	const role = JSON.parse(storage.getLocal('role'));
	const myTopic = storage.getLocal('myTopic');
	const curProjectAuth = role?.userRoleList.find(
		(item: any) => item.projectId === projectId
	);
	// * 内置普通用户权限没有细分，未锁定服务可以操作，暂时将普通用户所有权限禁用，仅支持现有菜单查看
	if (curProjectAuth?.roleId === 4) return true;
	if (locked && (locked === 'unlocked' || locked === 'locking')) {
		// * 判断当前服务是否已解锁 或处于 锁定的审批中时
		return false;
	}
	if (role.isAdmin) {
		// * 判断当前用户是否为超级管理员
		return false;
	}
	if (curProjectAuth) {
		if (curProjectAuth.weight === 3 || curProjectAuth.weight === 2) {
			// * 判断当前项目下用户的权限为组织管理员或者项目管理员，可以直接进行操作
			return false;
		}
	} else {
		if (!role.dba) {
			// * 当用户找不到当前项目的权限且不是dba类型的角色时
			return true;
		}
	}
	if (operatorType === 'lock') {
		if (myTopic.includes('ServiceLock')) {
			return false;
		} else {
			return true;
		}
	}
	if (operatorType === 'base') {
		if (myTopic.includes('ControlledOperationBase')) {
			return false;
		} else {
			return true;
		}
	}
	if (operatorType === 'maintenance') {
		if (myTopic.includes('ControlledOperationMaintenance')) {
			return false;
		} else {
			return true;
		}
	}
	if (operatorType === 'expert') {
		if (myTopic.includes('ControlledOperationExpert')) {
			return false;
		} else {
			return true;
		}
	}
};
// * 查找当前url对应菜单、子菜单及按钮权限
export function findInNestedArray(arr: any, str: string, isBtn?: boolean): any {
	for (const item of arr) {
		// 如果当前元素符合条件，则返回该元素
		if (isBtn) {
			if (item.name === str && item.resourceType === 'button') {
				return item;
			}
		} else {
			if (item.module === 'middleware') {
				if (window.location.hash.includes(item.middlewareType)) {
					return item;
				}
			}
			if (item.url === str) {
				return item;
			}
		}
		// 如果当前元素是一个包含子节点的对象，则递归地在子节点中查找
		if (item.subMenu && item.subMenu.length > 0) {
			const foundInChildren: any = findInNestedArray(
				item.subMenu,
				str,
				isBtn
			);
			// 如果在子节点中找到符合条件的值，则返回该值
			if (foundInChildren) {
				return foundInChildren;
			}
		}
	}
	// 如果未找到符合条件的值，则返回 null
	return null;
}
// * 树形菜单结构获取选择项和其父级
export function getPermissionList(preTree: any, preObj: any) {
	const treeData = JSON.parse(JSON.stringify(preTree));
	let perArr: any[] = [];
	Object.keys(preObj).map((item: any) => {
		perArr = [...perArr, ...preObj[item]];
	});
	const permissionNodes: any[] = [];
	const recursionTree = (treeData: any) => {
		let includeTreeNode = false;
		let isPush = false;
		let isPushParent = false;
		treeData.forEach((item: any, index: number) => {
			if (item.subMenu) {
				const _item = JSON.parse(JSON.stringify(item));
				delete _item['subMenu'];
				isPush = recursionTree(item.subMenu);
				if (isPush === true) {
					isPushParent = isPush;
				}
				if (isPush) {
					permissionNodes.push({ ..._item, own: true });
				}
			} else {
				const includeLeaf = perArr?.some((permission) => {
					return permission === item.id;
				});
				if (includeLeaf) {
					permissionNodes.push({ ...item, own: true });
					includeTreeNode = true;
					isPushParent = true;
				}
			}
		});
		if (includeTreeNode) {
			isPush = true;
		}
		return isPushParent;
	};
	recursionTree(treeData);
	return permissionNodes;
}
// * 对象数组根据key扁平化
export function flattenObjectArray(array: any[], childrenProp = 'children') {
	const result: any[] = [];

	function recurse(arr: any[]) {
		arr.forEach((item) => {
			// 将当前项添加到结果数组
			result.push(item);

			// 如果存在子项属性并且是数组，则递归处理
			if (Array.isArray(item[childrenProp])) {
				recurse(item[childrenProp]);
			}
		});
	}

	// 开始递归处理
	recurse(array);

	return result;
}

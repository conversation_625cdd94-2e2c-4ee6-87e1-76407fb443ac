import React, { useEffect, useState } from 'react';
import { Mo<PERSON>, Spin, notification } from 'antd';
import { TeamOutlined } from '@ant-design/icons';
import { OrganizationItem } from '@/pages/OrganizationManagement/organization';
import { getOrganizations } from '@/services/organization';
import './layout.less';
import { useHistory } from 'react-router';
import storage from '@/utils/storage';
export default function ChangeOrgan({
	open,
	onCancel
}: {
	open: boolean;
	onCancel: () => void;
}): JSX.Element {
	const history = useHistory();
	const organization = storage.getSession('organization');
	const [spinning, setSpinning] = useState<boolean>(false);
	const [organs, setOrgans] = useState<OrganizationItem[]>([]);
	const [currentOrganization, setCurrentOrganization] =
		useState<OrganizationItem>(organization);
	useEffect(() => {
		getOrgans();
	}, []);
	const getOrgans = () => {
		setSpinning(true);
		getOrganizations({ keyword: '', manager: true })
			.then((res) => {
				if (res.success) {
					setOrgans(res.data);
				} else {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res.errorMsg}</p>
								<p>{res.errorDetail}</p>
							</>
						)
					});
				}
			})
			.finally(() => {
				setSpinning(false);
			});
	};
	const onOk = () => {
		if (currentOrganization) {
			storage.setSession('organization', currentOrganization);
			storage.setSession('organId', currentOrganization.organId);
			history.push(`/organization`);
			onCancel();
		}
	};
	return (
		<Modal
			width={880}
			title="切换组织"
			open={open}
			onCancel={onCancel}
			onOk={onOk}
			okButtonProps={{
				disabled: currentOrganization.organId === organization.organId
			}}
		>
			<Spin spinning={spinning}>
				<div className="top-organ-list-content">
					{organs?.map((item: OrganizationItem) => {
						return (
							<div
								key={item.name}
								className={`allot-project-item ${
									currentOrganization?.organId ===
									item.organId
										? 'allot-project-item-active'
										: ''
								}`}
								onClick={() => {
									setCurrentOrganization(item);
								}}
							>
								<div className="allot-project-item-icon">
									<TeamOutlined />
								</div>
								<div className="allot-project-item-content">
									<div className="allot-project-item-content-title">
										{item.name}
									</div>
								</div>
							</div>
						);
					})}
				</div>
			</Spin>
		</Modal>
	);
}

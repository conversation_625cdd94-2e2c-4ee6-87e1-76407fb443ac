import React, { useState, useEffect } from 'react';
import {
	HashRouter as Router,
	Route,
	Switch,
	Redirect,
	withRouter,
	useHistory
} from 'react-router-dom';
import { Button, Result } from 'antd';
import { getNewTopMenu } from '@/services/common';
import InstanceDetails from '@/pages/ServiceListDetail/index';
import DynamicForm from '@/pages/ServiceCatalog/DynamicForm';
import OperationAudit from '@/pages/OperationAudit/index';
import UserManage from '@/pages/UserManage';
import RoleManage from '@/pages/RoleManage';
import OperationAuditDetail from '@/pages/OperationAudit/detail';
import AlarmCenter from '@/pages/AlarmCenter/AlarmCenter';
import MiddlewareVersion from '@/pages/MiddlewareRepository/middlewareVersion';
import ResourcePoolManagement from '@/pages/ResourcePoolManagement';
import AddResourcePool from '@/pages/ResourcePoolManagement/addResourcePool';
import AddForm from '@/pages/ResourcePoolManagement/addForm';
import ServiceListByType from '@/pages/ServiceList/serviceListByType';
import ResourcePoolDetail from '@/pages/ResourcePoolManagement/detail';
import CreateAlarm from '@/pages/ServiceListDetail/ServeAlarm/create';
import EditParamTemplate from '@/pages/ServiceListDetail/ParamterSetting/editParamTemplate';
import CompareParamTemplate from '@/pages/ServiceListDetail/ParamterSetting/compareParamTemplate';
import UseTemplate from '@/pages/ServiceListDetail/ParamterSetting/useTemplate';
import AddServiceAvailableForm from '@/pages/ServiceAvailable/AddServiceAvailableForm';
import AllotMenu from '@/pages/RoleManage/AllotMenu';
import BackupTask from '@/pages/BackupService/proBackupTask';
import AddBackupTask from '@/pages/BackupService/addBackupTask';
import BackupTaskDetail from '@/pages/BackupService/backupTaskDetail';
import BackupRecordDetail from '@/pages/BackupService/recordDetail';
import BackupRecovery from '@/pages/BackupService/backupRecovery';
import BackupServer from '@/pages/BackupService/backupServer';
import AddBackupServer from '@/pages/BackupService/addBackupServer';
import AddStorage from '@/pages/StorageManagement/addStorage';
import StorageDetail from '@/pages/StorageManagement/storageDetail';
import ServiceIngress from '@/pages/ServiceIngress';
import ActiveActive from '@/pages/ActiveActive';
import ActiveDetail from '@/pages/ActiveActive/activeDetail';
import AreaConfig from '@/pages/ActiveActive/AreaConfig';
import AreaDetail from '@/pages/ActiveActive/AreaDetail';
import IngressDetail from '@/components/IngressCard/IngressDetail';
import EditYaml from '@/components/IngressCard/editYaml';
import AuthorManage from '@/pages/AuthorManage/index';
import HostConfig from '@/pages/ServiceListDetail/OperatorAbility/NodeMigration/hostConfig';
import PlatformDisaster from '@/pages/PlatformDisaster';
import OrganizationManagement from '@/pages/OrganizationManagement';
import OrganizationQuota from '@/pages/OrganizationManagement/organizationQuota';
import ClusterQuota from '@/pages/OrganizationManagement/ClusterQuota';
import ProjectQuota from '@/pages/OrganizationDetail/ProjectManage/ProjectQuota';
import NamespaceQuota from '@/pages/ProjectDetail/NamespaceQuota';
import AddAlarmContacts from '@/pages/AlarmCenter/AlarmContacts/AddAlarmContacts';
import AddServiceAlarmContacts from '@/pages/ServiceListDetail/ServeAlarm/AddServiceAlarmContacts';
// * 发布页面重构
import KafkaPublish from '@/pages/PublishService/Kafka';
import ZooKeeperPublish from '@/pages/PublishService/ZooKeeper';
import RocketMQPublish from '@/pages/PublishService/RocketMQ';
import ElasticsearchPublish from '@/pages/PublishService/Elasticsearch';
import RedisPublish from '@/pages/PublishService/Redis';
import PostgreSQLPublish from '@/pages/PublishService/PostgreSQL';
import MysqlPublish from '@/pages/PublishService/Mysql';
// * ----------------
import MiddlewareManage from '@/pages/MiddlewareManage';
import MiddlewareItemDetail from '@/pages/MiddlewareManage/detail';
import AddDisaster from '@/pages/ServiceListDetail/Disaster/addDisaster';
// * v2.0.0
import Workspace from '@/pages/Workspace';
import AddIngress from '@/pages/ServiceListDetail/ServiceIngress/newAddIngress';
import AddEsIngress from '@/pages/ServiceListDetail/ServiceIngress/newAddEsIngress';
import AgentManagement from '@/pages/AgentManagement';
import AccessService from '@/pages/PublishService/access';
import AccessIndex from '@/pages/ServiceListDetail/accessIndex';

import WorkspaceLayout from './workspaceLayout';
import PlatformLayout from './platformLayout';
import ProjectLayout from './projectLayout';
import OrganLayout from './organLayout';
// * 开放中心
import OpenCenter from '@/pages/OpenCenter';
// * 个性化设置
import Individuation from '@/pages/Individuation';
// * 平台概览
import PlatformManagementOverview from '@/pages/PlatformManagementOverview';
// * 中间件市场
import ProjectMiddleware from '@/pages/MiddlewareManage/projectMiddleware';
// * 组织下页面
import OverviewPage from '@/pages/OrganizationDetail/OverviewPage';
import ProjectPage from '@/pages/OrganizationDetail/ProjectPage';
import ResourceDisplayPage from '@/pages/OrganizationDetail/ResourceDisplayPage';
import AgentPage from '@/pages/OrganizationDetail/AgentPage';
import MemberPage from '@/pages/OrganizationDetail/MemberPage';
// * 项目下页面
import ProjectOverview from '@/pages/ProjectOverview';
import ProjectDisplayPage from '@/pages/ProjectDetail/DisplayPage';
import NamespacePage from '@/pages/ProjectDetail/NamespacePage';
import ProjectMemberPage from '@/pages/ProjectDetail/ProjectMemberPage';
import BackupPositionPage from '@/pages/ProjectDetail/BackupPositionPage';
import WorkOrderManagement from '@/pages/WorkOrderManagement';
import ApprovalConfig from '@/pages/ProjectDetail/ApprovalConfig';
import WorkOrderForm from '@/pages/WorkOrderManagement/WorkOrderForm';
import OrderDetail from '@/pages/WorkOrderManagement/Detail';
import AllotDBA from '@/pages/RoleManage/AllotDBA';
import AreaManagement from '@/pages/AreaManagement';
import ProgressManage from '@/pages/WorkOrderManagement/ProgressManage';
import Initiator from '@/pages/WorkOrderManagement/Initiator';
import EditWorkOrder from '@/pages/WorkOrderManagement/EditWorkOrder';
import AccessHistoryDetail from '@/pages/ServiceListDetail/AccessFile/AccessHistoryDetail';
import AllotMenuAndBtn from '@/pages/RoleManage/AllotMenuAndBtn';
import { findInNestedArray } from '@/utils/utils';
// * 模板发布页面
import ServiceTemplateManage from '@/pages/ServiceList/template';
import PublishTemplate from '@/pages/PublishTemplate/FixedMiddleware';
import DynamicFormTemplate from '@/pages/PublishTemplate/Aas';

// 无权限页面组件
const NoPermission = () => {
	const history = useHistory();
	return (
		<Result
			status="403"
			title="403"
			subTitle="对不起，您没有权限访问该页面，请联系管理员"
			extra={
				<Button
					type="primary"
					onClick={() => {
						history.push('/');
					}}
				>
					回到首页
				</Button>
			}
		/>
	);
};
const Routes = withRouter(() => {
	const [redirectPath, setRedirectPath] = useState('');
	const [alarmRedirectPath, setAlarmRedirectPath] = useState('');

	useEffect(() => {
		getNewTopMenu().then((response) => {
			if (response.success) {
				const alarmTab =
					findInNestedArray(response.data, 'platform/alarmCenter')
						?.subMenu || [];
				response.data?.some((item) => item.url === 'workspace')
					? setRedirectPath('/workspace')
					: setRedirectPath('/platform');
				if (alarmTab[0]?.name?.indexOf('System') !== -1) {
					setAlarmRedirectPath('system');
				} else if (alarmTab[0]?.name?.indexOf('Cluster') !== -1) {
					setAlarmRedirectPath('cluster');
				} else {
					setAlarmRedirectPath('ruleCenter');
				}
			}
		});
	}, []);

	return (
		<>
			<Switch>
				<Route
					path="/"
					exact
					render={() => <Redirect to={redirectPath} />}
				/>
				<Route exact path="/no-permission" component={NoPermission} />
				{/* 工作台 */}
				<Route path="/workspace">
					<WorkspaceLayout>
						<Route path="/workspace" exact component={Workspace} />
						{/* 授权管理 */}
						<Route
							path="/workspace/authorManage"
							component={AuthorManage}
							exact
						/>
						<Route
							path="/workspace/workOrderManage"
							render={() => (
								<Redirect to="/workspace/workOrderManage/pending" />
							)}
							exact
						/>
						<Route
							path="/workspace/workOrderManage/:activeKey"
							component={WorkOrderManagement}
							exact
						/>
						<Route
							path="/workspace/workOrderManage/detail/:orderId"
							exact
							component={OrderDetail}
						/>
						<Route
							path="/workspace/workOrderManage/form/:topic/:name/:middlewareName/:orderId"
							exact
							component={WorkOrderForm}
						/>
						<Route
							path="/workspace/workOrderManage/progressManage/list"
							exact
							component={ProgressManage}
						/>
						<Route
							path="/workspace/workOrderManage/progressManage/initiator/:type/:uid"
							exact
							component={Initiator}
						/>
						<Route
							path="/workspace/workOrderManage/progressManage/edit/:type/:uid"
							exact
							component={EditWorkOrder}
						/>
					</WorkspaceLayout>
				</Route>
				{/* 平台管理 */}
				<Route path="/platform">
					<PlatformLayout>
						{/* 区域管理 */}
						<Route
							exact
							path="/platform/areaManagement"
							component={AreaManagement}
						/>
						{/* 集群管理 */}
						<Route
							exact
							path="/platform/clusterManagement"
							component={ResourcePoolManagement}
						/>
						<Route
							path="/platform/clusterManagement/addResourcePool"
							component={AddResourcePool}
							exact
						/>
						<Route
							path="/platform/clusterManagement/addResourcePool/addOther"
							component={AddForm}
							exact
						/>
						<Route
							path="/platform/clusterManagement/editResourcePool/editOther/:clusterId"
							component={AddForm}
							exact
						/>
						<Route
							path="/platform/clusterManagement/resourcePoolDetail/:id"
							component={ResourcePoolDetail}
							exact
						/>
						{/* 负载均衡详情 */}
						<Route
							path="/platform/clusterManagement/resourcePoolDetail/:id/ingress/:ingressClassName/:type"
							component={IngressDetail}
							exact
						/>
						<Route
							path="/platform/clusterManagement/resourcePoolDetail/:id/ingress/:ingressClassName"
							component={EditYaml}
							exact
						/>
						<Route
							path="/platform/clusterManagement/resourcePoolDetail/:id/storage/create"
							component={AddStorage}
							exact
						/>
						<Route
							path="/platform/clusterManagement/resourcePoolDetail/:id/storage/edit/:storageId/:name"
							component={AddStorage}
							exact
						/>
						<Route
							path="/platform/clusterManagement/resourcePoolDetail/:id/storage/:name/:aliasName"
							component={StorageDetail}
							exact
						/>
						{/* 市场管理 */}
						<Route
							path="/platform/marketManagement"
							exact
							component={MiddlewareManage}
						/>
						<Route
							path="/platform/marketManagement/middlewareDetail/:name/:clusterId/:imagePath"
							component={MiddlewareItemDetail}
							exact
						/>
						<Route
							exact
							path="/platform/marketManagement/versionManagement/:type/:clusterId"
							component={MiddlewareVersion}
						/>
						{/* 组织管理 */}
						<Route
							exact
							path="/platform/organizationManagement"
							component={OrganizationManagement}
						/>
						<Route
							path="/platform/organizationManagement/add/:organId"
							component={OrganizationQuota}
							exact
						/>
						<Route
							path="/platform/organizationManagement/add/:organId/addCpuAndMemory"
							component={ClusterQuota}
							exact
						/>
						<Route
							path="/platform/organizationManagement/add/:organId/addCpuAndMemory/:clusterId"
							component={ClusterQuota}
							exact
						/>
						<Route
							path="/platform/organizationManagement/add/:organId/:projectId/:projectName/addCpuAndMemory/:clusterId"
							component={ClusterQuota}
							exact
						/>
						<Route
							path="/platform/organizationManagement/detail/:organId/:projectId/:activeKey/:projectName/namespaceQuota/:clusterId/:namespace/:namespaceAlias"
							component={NamespaceQuota}
							exact
						/>
						{/* 用户管理 */}
						<Route
							path="/platform/userManagement"
							exact
							component={UserManage}
						/>
						<Route
							path="/platform/roleManagement"
							exact
							component={RoleManage}
						/>
						<Route
							path="/platform/roleManagement/allotRole/menu"
							component={AllotMenu}
							exact
						/>
						<Route
							path="/platform/roleManagement/allotRole/dba"
							component={AllotDBA}
							exact
						/>
						<Route
							path="/platform/roleManagement/allotRole/menuAndBtn/:isEdit"
							component={AllotMenuAndBtn}
							exact
						/>
						<Route
							path="/platform/operationAudit"
							component={OperationAudit}
							exact
						/>
						<Route
							path="/platform/operationAudit/:auditId"
							component={OperationAuditDetail}
							exact
						/>
						{/* 告警中心 */}
						<Route
							path="/platform/alarmCenter"
							render={() => (
								<Redirect
									to={`/platform/alarmCenter/${alarmRedirectPath}`}
								/>
							)}
							exact
						/>
						<Route
							path="/platform/alarmCenter/:type"
							component={AlarmCenter}
							exact
						/>
						<Route
							path="/platform/alarmCenter/:type/addContacts/:clusterId"
							component={AddAlarmContacts}
							exact
						/>
						{/* 备份服务器 */}
						<Route
							path="/platform/backupServer"
							component={BackupServer}
							exact
						/>
						<Route
							path="/platform/backupServer/addBackupServer"
							component={AddBackupServer}
							exact
						/>
						<Route
							path="/platform/backupServer/addBackupServer/:id"
							component={AddBackupServer}
							exact
						/>
						<Route
							path="/platform/activeActive"
							exact
							component={ActiveActive}
						/>
						<Route
							path="/platform/activeActive/:id/:nickname"
							component={ActiveDetail}
							exact
						/>
						<Route
							path="/platform/activeActive/active-active/:id/:nickname/:areaName/:aliasName"
							component={AreaConfig}
							exact
						/>
						<Route
							path="/platform/activeActive/active-detail/:id/:nickname/:areaName/:aliasName"
							component={AreaDetail}
							exact
						/>
						<Route
							path="/platform/disasterBackup"
							component={PlatformDisaster}
							exact
						/>
						<Route
							exact
							path="/platform/openCenter"
							component={OpenCenter}
						/>
						<Route
							path="/platform/agentManagement"
							component={AgentManagement}
							exact
						/>
						<Route
							path="/platform/individuation"
							component={Individuation}
							exact
						/>
						<Route
							path="/platform/overview"
							component={PlatformManagementOverview}
							exact
						/>
					</PlatformLayout>
				</Route>
				{/* 业务侧 - 项目 */}
				<Route path="/project">
					<ProjectLayout>
						<Route
							path="/project/:type/:name/:aliasName"
							exact
							component={ServiceListByType}
						/>
						<Route
							path="/project/:type/:name/:aliasName/workOrder/:topic"
							exact
							component={WorkOrderForm}
						/>
						<Route
							path="/project/:type/:name/:aliasName/workOrder/:topic/:middlewareName"
							exact
							component={WorkOrderForm}
						/>
						<Route
							path="/project/:type/:name/:aliasName/workOrder/:topic/:middlewareName/:operatorId"
							exact
							component={WorkOrderForm}
						/>
						{/* 服务锁定/解锁换成一个工单类型字段，通过url区分 */}
						<Route
							path="/project/:type/:name/:aliasName/workOrder/:topic/:middlewareName/:operatorId/:lock"
							exact
							component={WorkOrderForm}
						/>
						{/* 发布模版 */}
						<Route
							path="/project/:type/:name/:aliasName/templateManage"
							component={ServiceTemplateManage}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/templateCreate"
							component={PublishTemplate}
							exact
						/>
						<Route
							path="/project/:type/dynamicFormTemplateCreate/:chartVersion/:name/dynamicForm/:aliasName"
							component={DynamicFormTemplate}
							exact
						/>
						{/* mysql */}
						<Route
							path="/project/:type/:name/:aliasName/mysqlCreate"
							component={MysqlPublish}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/mysqlBackup/:chartVersion/:middlewareName/:namespace/:clusterId"
							component={MysqlPublish}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/mysqlDisaster/:chartVersion/:clusterId/:namespace/:disasterOriginName"
							component={MysqlPublish}
							exact
						/>
						{/* redis */}
						<Route
							path="/project/:type/:name/:aliasName/redisCreate"
							component={RedisPublish}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/redisCreateByTemplate/:isTemplate"
							component={RedisPublish}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/redisBackup/:chartVersion/:middlewareName/:namespace/:clusterId"
							component={RedisPublish}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/redisPublishDisaster/:chartVersion/:middlewareName/:namespace/:clusterId/:isDisaster"
							component={RedisPublish}
							exact
						/>
						{/* es */}
						<Route
							path="/project/:type/:name/:aliasName/elasticsearchCreate"
							component={ElasticsearchPublish}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/elasticsearchCreateByTemplate/:isTemplate"
							component={ElasticsearchPublish}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/elasticsearchBackup/:chartVersion/:middlewareName/:namespace/:clusterId"
							component={ElasticsearchPublish}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/elasticsearchPublishDisaster/:chartVersion/:middlewareName/:namespace/:clusterId/:isDisaster"
							component={ElasticsearchPublish}
							exact
						/>
						{/* rmq */}
						<Route
							path="/project/:type/:name/:aliasName/rocketmqCreate"
							component={RocketMQPublish}
							exact
						/>
						{/* kafka */}
						<Route
							path="/project/:type/:name/:aliasName/kafkaCreate"
							component={KafkaPublish}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/kafkaBackup/:chartVersion/:middlewareName/:namespace/:clusterId"
							component={KafkaPublish}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/kafkaPublishDisaster/:chartVersion/:middlewareName/:namespace/:clusterId/:isDisaster"
							component={KafkaPublish}
							exact
						/>
						{/* zkp */}
						<Route
							path="/project/:type/:name/:aliasName/zookeeperCreate"
							component={ZooKeeperPublish}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/zookeeperBackup/:chartVersion/:middlewareName/:namespace/:clusterId"
							component={ZooKeeperPublish}
							exact
						/>
						{/* pgsql */}
						<Route
							path="/project/:type/:name/:aliasName/PostgreSQLCreate"
							component={PostgreSQLPublish}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/postgresqlCreateByTemplate/:isTemplate"
							component={PostgreSQLPublish}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/postgresqlBackup/:chartVersion/:middlewareName/:namespace/:clusterId"
							component={PostgreSQLPublish}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/postgresqlPublishDisaster/:chartVersion/:middlewareName/:namespace/:clusterId/:isDisaster"
							component={PostgreSQLPublish}
							exact
						/>
						{/* custom */}
						<Route
							path="/project/:type/:name/:aliasName/dynamicForm"
							component={DynamicForm}
							exact
						/>
						{/* 服务详情*/}
						<Route
							path="/project/:type/:name/:aliasName/container/:currentTab/:middlewareName/:chartVersion/:clusterId/:namespace"
							component={InstanceDetails}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/container/hostConfig/:clusterId/:namespace/:middlewareName/operatorAbility/:podName/:zone/:nodeZone/:nodeName"
							component={HostConfig}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/container/alarm/addContacts/:middlewareName/:chartVersion/:namespace/:clusterId/:organId/:projectId"
							component={AddServiceAlarmContacts}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/container/:currentTab/createAlarm/:middlewareName/:chartVersion/:clusterId/:namespace"
							component={CreateAlarm}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/container/:currentTab/createAlarm/:middlewareName/:chartVersion/:clusterId/:namespace/:alertName"
							component={CreateAlarm}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/container/backupRecovery/addBackupTask/:middlewareName/:chartVersion/:clusterId/:namespace"
							component={AddBackupTask}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/container/backupRecovery/backupTaskDetail/:middlewareName/:chartVersion/:namespace/:clusterId/:backupName/:backupId/:backupMode/:lock"
							component={BackupTaskDetail}
							exact
						/>
						{/* 新建模板 */}
						<Route
							path="/project/:type/:name/:aliasName/container/paramterSetting/template/:middlewareName/:chartVersion/:clusterId/:namespace"
							component={EditParamTemplate}
							exact
						/>
						{/* 模板对比 */}
						<Route
							path="/project/:type/:name/:aliasName/container/paramterSetting/compareTemplate/:chartVersion/:uid1/:uid2/:clusterId/:namespace/compare"
							component={CompareParamTemplate}
							exact
						/>
						{/* 编辑模板 */}
						<Route
							path="/project/:type/:name/:aliasName/container/paramterSetting/template/:middlewareName/:chartVersion/:uid/:templateName/:clusterId/:namespace/:role"
							component={EditParamTemplate}
							exact
						/>
						{/* 使用模板 */}
						<Route
							path="/project/:type/:name/:aliasName/container/paramterSetting/useTemplate/:middlewareName/:chartVersion/:uid/:clusterId/:namespace/:role"
							component={UseTemplate}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/container/externalAccess/add/es/:middlewareName/:clusterId/:chartVersion/:namespace/:mode"
							component={AddEsIngress}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/container/externalAccess/edit/es/:middlewareName/:clusterId/:chartVersion/:namespace/:mode/:exposeId"
							component={AddEsIngress}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/container/externalAccess/edit/other/:middlewareName/:clusterId/:chartVersion/:namespace/:mode/:exposeId"
							component={AddIngress}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/container/externalAccess/add/other/:middlewareName/:clusterId/:chartVersion/:namespace/:mode/:external"
							component={AddIngress}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/container/externalAccess/addExternalAccess/:middlewareName/:clusterId/:chartVersion/:namespace/:exposeId"
							component={AddServiceAvailableForm}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/container/externalAccess/addExternalAccess/:middlewareName/:clusterId/:chartVersion/:namespace"
							component={AddServiceAvailableForm}
							exact
						/>
						{/* 接入中间件 */}
						<Route
							path="/project/:type/:name/:aliasName/server/:organId/:projectId"
							component={AccessService}
							exact
						/>
						{/* 接入中间件详情 */}
						<Route
							path="/project/:type/:name/:aliasName/server/:currentTab/:middlewareName/:clusterId/:namespace"
							component={AccessIndex}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/server/:currentTab/:middlewareName/:clusterId/:namespace/:historyId"
							component={AccessHistoryDetail}
							exact
						/>
						<Route
							path="/project/ingress"
							component={ServiceIngress}
							exact
						/>
						<Route
							path="/project/backup"
							exact
							component={BackupTask}
						/>
						<Route
							path="/project/backup/addBackupTask"
							component={AddBackupTask}
							exact
						/>
						<Route
							path="/project/backup/detail/:clusterId/:namespace/:middlewareName/:backupName/:backupId/:type/:name/:backupMode/:lock"
							component={BackupTaskDetail}
							exact
						/>
						<Route
							path="/project/backup/record/detail/:clusterId/:namespace/:middlewareName/:backupName/:recordName"
							component={BackupRecordDetail}
							exact
						/>
						<Route
							path="/project/backup/record/detail/:clusterId/:namespace/:middlewareName/:restoreName"
							component={BackupRecordDetail}
							exact
						/>
						<Route
							path="/project/backup/recovery/:clusterId/:namespace/:backupId/:type/:middlewareName/:serviceGroup"
							component={BackupRecovery}
							exact
						/>
						<Route
							path="/project/:serviceGroup/:name/:aliasName/container/:currentTab/backupTaskDetail/backupTaskRecovery/:middlewareName/:clusterId/:namespace/:backupId"
							component={BackupRecovery}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/container/:currentTab/backupRecordDetail/:middlewareName/:chartVersion/:namespace/:clusterId/:backupName/:recordName"
							component={BackupRecordDetail}
							exact
						/>
						<Route
							path="/project/:type/:name/:aliasName/container/:currentTab/backupRestoreDetail/:middlewareName/:chartVersion/:namespace/:clusterId/:restoreName"
							component={BackupRecordDetail}
							exact
						/>
						{/* 添加灾备 */}
						<Route
							path="/project/:type/:name/:aliasName/container/:currentTab/addDisaster/:middlewareName/:chartVersion/:namespace/:clusterId/:version/:mode"
							component={AddDisaster}
							exact
						/>
						<Route
							path="/project/overview"
							component={ProjectOverview}
							exact
						/>
						<Route
							path="/project/projectManagement/namespace"
							component={NamespacePage}
							exact
						/>
						<Route
							path="/project/projectManagement/namespace/:organId/:projectId/:activeKey/:projectName/namespaceQuota/:clusterId/:namespace/:namespaceAlias"
							component={NamespaceQuota}
							exact
						/>
						<Route
							path="/project/projectManagement/member"
							component={ProjectMemberPage}
							exact
						/>
						<Route
							path="/project/projectManagement/backup"
							component={BackupPositionPage}
							exact
						/>
						<Route
							path="/project/projectManagement/approval"
							exact
							component={ApprovalConfig}
						/>
						<Route
							path="/project/middlewareMarket"
							exact
							component={ProjectMiddleware}
						/>
						<Route
							path="/project/display"
							exact
							component={ProjectDisplayPage}
						/>
						<Route
							path="/project/display/work/order/:topic"
							exact
							component={WorkOrderForm}
						/>
					</ProjectLayout>
				</Route>
				{/* 组织详情 */}
				<Route path="/organization">
					<OrganLayout>
						<Route
							path="/organization/overview"
							component={OverviewPage}
							exact
						/>
						<Route
							component={ProjectPage}
							exact
							path="/organization/project"
						/>
						<Route
							path="/organization/project/add/:organId/:projectId/:projectName"
							component={ProjectQuota}
							exact
						/>
						<Route
							path="/organization/project/add/:organId/:projectId/:projectName/addCpuAndMemory"
							component={ClusterQuota}
							exact
						/>
						<Route
							path="/organization/project/add/:organId/:projectId/:projectName/addCpuAndMemory/:clusterId"
							component={ClusterQuota}
							exact
						/>
						<Route
							component={ResourceDisplayPage}
							path="/organization/resourceDisplay"
							exact
						/>
						<Route
							path="/organization/resourceDisplay/workOrder/:topic"
							exact
							component={WorkOrderForm}
						/>
						<Route
							exact
							path="/organization/agent"
							component={AgentPage}
						/>
						<Route
							exact
							path="/organization/member"
							component={MemberPage}
						/>
					</OrganLayout>
				</Route>
				<Route path="/webpage">
					{/* 服务详情*/}
					<Route
						path="/webpage/:type/:name/:aliasName/container/:currentTab/:middlewareName/:chartVersion/:clusterId/:namespace"
						component={InstanceDetails}
						exact
					/>
				</Route>
			</Switch>
		</>
	);
});

export default () => (
	<Router>
		<Routes />
	</Router>
);

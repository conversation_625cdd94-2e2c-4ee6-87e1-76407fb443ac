import React, { useEffect, useState } from 'react';
import { Menu, MenuProps } from 'antd';
import { useHistory } from 'react-router';
import { HashRouter as Router, Route, useLocation } from 'react-router-dom';
import { MenuInfo } from '@/types/comment';
import './menu.less';
import { fourMenu, threeMenu } from '@/utils/const';
import { connect } from 'react-redux';
import { StoreState } from '@/types';
import { setButtonList } from '@/redux/auth/auth';
import { findInNestedArray } from '@/utils/utils';

interface MyMenuProps {
	items: MenuItem[];
	menu: any;
	setButtonList: (data: any) => void;
}
type MenuItem = Required<MenuProps>['items'][number];
function MyMenu(props: MyMenuProps): JSX.Element {
	const location: any = useLocation();
	const { items, menu, setButtonList } = props;
	const history = useHistory();
	const [curItems, setCurItems] = useState<MenuItem[]>([]);

	useEffect(() => {
		setCurItems(items);
	}, [items]);

	useEffect(() => {
		setButtonList(
			findInNestedArray(menu, mapLocationToActiveKey(location)[0])
				?.subMenu || []
		);
	}, [menu, window.location.hash]);

	const mapLocationToActiveKey = (location: Location) => {
		const pathArray = location.pathname.split('/');
		pathArray.shift();
		if (threeMenu.find((item) => location.pathname === item)) {
			pathArray.length = 3;
		} else if (fourMenu.find((item) => location.pathname.includes(item))) {
			pathArray.length = 4;
		} else {
			pathArray.length = 2;
		}
		if (!location || !location.pathname || location.pathname === '/') {
			return ['workspace'];
		}
		return [pathArray.join('/')];
	};

	const onMenuItemClick = (info: MenuInfo) => {
		history.push(`/${info.key}`);
		setButtonList(
			findInNestedArray(menu, window.location.hash.split('#/')[1])
				?.subMenu
		);
	};

	return (
		<Router>
			<Route>
				{({ location }: { location: Location }) => {
					const openKey = `${location.pathname.split('/')[1]}/${
						location.pathname.split('/')[2]
					}`;
					return (
						<Menu
							theme="light"
							mode="inline"
							items={curItems}
							onClick={onMenuItemClick}
							defaultOpenKeys={[
								'project/projectManagement',
								openKey
							]}
							selectedKeys={mapLocationToActiveKey(location)}
						/>
					);
				}}
			</Route>
		</Router>
	);
}
export default connect((state: StoreState) => ({ menu: state.auth.menu }), {
	setButtonList
})(MyMenu);

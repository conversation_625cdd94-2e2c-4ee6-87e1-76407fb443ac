import React, { useEffect, useState } from 'react';
import { Route, HashRouter as Router } from 'react-router-dom';
import { notification } from 'antd';
import Navbar from './Navbar/navbar';
import Routes from './routes';
import { getUserInformation } from '@/services/user';
import storage from '@/utils/storage';
import Login from '@/pages/Login';
import './index.less';
import OperationPanel from '@/pages/OperationPanel';
import NacosTest from '@/pages/ServiceList/NacosTest';
import {
	adminRole,
	exampleOrganId,
	exampleProject,
	exampleProjectId
} from '@/utils/const';
import { TOKEN } from '@/services/request';
export default function Layout(): JSX.Element {
	const [role, setRole] = useState<any>();

	// 直接获取指定参数值
	function getHashParam(name: string) {
		const hash = window.location.hash;
		const regex = new RegExp(`[?&]${name}=([^&]+)`);
		const match = regex.exec(hash);
		return match ? decodeURIComponent(match[1]) : null;
	}
	const token = getHashParam('token');
	if (token) {
		storage.setLocal(TOKEN, token);
		storage.setLocal('role', JSON.stringify(adminRole));
		storage.setLocal('myTopic', []);
		storage.setLocal('featureAPI', []);
		storage.setSession('organId', exampleOrganId);
		storage.setSession('projectId', exampleProjectId);
		storage.setSession('project', exampleProject);
		console.log('projectId :>> ', storage.getSession('projectId'));
		const [path] = window.location.hash.split('?');
		window.location.hash = path;
	}
	useEffect(() => {
		async function getAllData() {
			await getUserInfo();
		}
		if (storage.getLocal(TOKEN)) {
			getAllData();
		}
	}, []);
	const getUserInfo = async () => {
		const res = await getUserInformation();
		if (res.success) {
			setRole(res.data);
		} else {
			notification.error({
				message: '失败',
				description: res.errorMsg
			});
		}
	};

	// * 没有token时，跳转到登陆页
	if (!storage.getLocal(TOKEN)) {
		return (
			<Router>
				<Route path={['/', '/login']} component={Login} />
			</Router>
		);
	}
	// * 运维面板页面
	const operationalPanel = () => (
		<Router>
			<Route
				path="/operationalPanel/:currentTab/:organId/:projectId/:clusterId/:namespace/:type/:name/:version/:mode"
				component={OperationPanel}
				exact
			/>
			<Route path="/nacostest" component={NacosTest} exact />
		</Router>
	);
	// * 当路由中包含operationalPanel字段时，显示运维面板页面
	if (
		window.location.href.includes('operationalPanel') ||
		window.location.href.includes('nacostest')
	) {
		return operationalPanel();
	}

	const isFrame = window !== window.parent;
	// 内嵌父页面的 详情页面不需要 头部 侧边栏
	if (window.location.href.includes('webpage')) {
		return (
			<div
				className={
					isFrame ? 'zeus-mid-layout is-frame' : 'zeus-mid-layout'
				}
			>
				<Router>
					<Routes />
				</Router>
			</div>
		);
	}

	return (
		<div
			className={isFrame ? 'zeus-mid-layout is-frame' : 'zeus-mid-layout'}
		>
			<Router>
				<Navbar role={role} />
				<Routes />
			</Router>
		</div>
	);
}

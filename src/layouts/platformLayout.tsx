import React, { useEffect, useState } from 'react';
import { MenuProps, notification } from 'antd';
import { useHistory, useLocation } from 'react-router';
import { connect } from 'react-redux';
import { setMenu } from '@/redux/auth/auth';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import MyMenu from './Menu/MyMenu';
import { getMenu, getNewTopMenu } from '@/services/common';
import { IconFont } from '@/components/IconFont';
import './layout.less';
import storage from '@/utils/storage';
import { StoreState } from '@/types';

type MenuItem = Required<MenuProps>['items'][number];

function getItem(
	label: React.ReactNode,
	key: React.Key,
	icon?: React.ReactNode,
	children?: MenuItem[]
): MenuItem {
	return {
		key,
		icon,
		children,
		label
	} as MenuItem;
}
function PlatformLayout({
	children,
	menu,
	setMenu
}: {
	children: any;
	menu: any;
	setMenu: (data: any) => void;
}): JSX.Element {
	const [collapsed, setCollapsed] = useState<boolean>(false); // * 是否收起侧边栏
	const [items, setItems] = useState<MenuItem[]>([]);
	const location = useLocation();
	const history = useHistory();
	useEffect(() => {
		// getMenu().then((res) => {
		// 	if (res.success) {
		// 		const lt = res.data.map((item: MenuResItem) => {
		// 			return getItem(
		// 				item.aliasName,
		// 				item.url,
		// 				<IconFont type={item.iconName} size={14} />
		// 			);
		// 		});
		// 		setItems(lt);
		// 		storage.setSession('menu', res.data);
		// 	} else {
		// 		notification.error({
		// 			message: '错误',
		// 			description: (
		// 				<>
		// 					<p>{res.errorMsg}</p>
		// 					<p>{res.errorDetail}</p>
		// 				</>
		// 			)
		// 		});
		// 	}
		// });
		getNewTopMenu().then((res) => {
			if (res.success) {
				const platMenu = res.data.find(
					(item: any) => item.id === 2
				)?.subMenu;
				const lt = platMenu.map((item: MenuResItem) => {
					return getItem(
						item.aliasName,
						item.url,
						<IconFont type={item.iconName} size={14} />
					);
				});
				setItems(lt);
				setMenu(platMenu);
				storage.setSession('menu', platMenu);
			} else {
				notification.error({
					message: '错误',
					description: (
						<>
							<p>{res.errorMsg}</p>
							<p>{res.errorDetail}</p>
						</>
					)
				});
			}
		});
	}, []);
	useEffect(() => {
		if (items.length !== 0 && location.pathname === '/platform') {
			history.push(`/${items[0]?.key}`);
		}
	}, [items]);
	return (
		<>
			<div className="zeus-mid-content">
				<aside
					style={{
						width: collapsed ? '0px' : '216px',
						overflowY: collapsed ? 'hidden' : 'auto'
					}}
				>
					<div className="platform-header-content">
						<IconFont
							style={{ fontSize: '34px' }}
							type="icon-platform-menu-management-title"
						/>
						平台管理
					</div>
					<MyMenu items={items} />
				</aside>
				<div
					className="zeus-mid-left-content"
					style={{
						marginLeft: collapsed ? '0px' : '216px',
						minWidth: collapsed ? '100%' : 'calc(100% - 216px)'
					}}
				>
					{children}
				</div>
			</div>
			<div
				className="zeus-mid-flod-content"
				style={{
					left: collapsed ? '0px' : '216px'
				}}
				onClick={() => setCollapsed(!collapsed)}
			>
				{collapsed ? <RightOutlined /> : <LeftOutlined />}
			</div>
		</>
	);
}

export default connect((state: StoreState) => ({ menu: state.auth.menu }), {
	setMenu
})(PlatformLayout);

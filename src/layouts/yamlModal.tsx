import * as React from 'react';
import { useState, useEffect } from 'react';
import { Button, Modal, Select, notification } from 'antd';
import {
	DownloadOutlined,
	ArrowsAltOutlined,
	ShrinkOutlined
} from '@ant-design/icons';
import { UnControlled as CodeMirror } from 'react-codemirror2';
import { getYaml, getYamlResource } from '@/services/middleware';
import 'codemirror/lib/codemirror.js';
import 'codemirror/lib/codemirror.css';
import 'codemirror/mode/css/css.js';
import 'codemirror/mode/yaml/yaml.js';
import 'codemirror/mode/yaml-frontmatter/yaml-frontmatter.js';
import 'codemirror/theme/twilight.css';
import 'codemirror/addon/selection/active-line';
import { getClusters } from '@/services/common';
import { clusterType, StoreState } from '@/types/index';
import { downloadConf, getConf } from '@/services/user';
import storage from '@/utils/storage';
import { api } from '@/api.json';
import { exportFile } from '@/utils/export';

interface YamlModalProps {
	open: boolean;
	onCreate: () => void;
	onCancel: () => void;
}

function YamlModal(props: YamlModalProps): JSX.Element {
	const { open, onCreate, onCancel } = props;
	const [yamlValue, setYamlValue] = useState<string>('');
	const [clusterList, setClusterList] = useState<clusterType[]>([]);
	const [currentCluster, setCurrentCluster] = useState<string>();
	const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
	const [disabled, setDisabled] = useState<boolean>(true);

	useEffect(() => {
		getClusters().then((res) => {
			if (res.success) {
				setClusterList(
					res.data.map((item: clusterType) => ({
						label: item.nickname,
						value: item.id
					}))
				);
				setCurrentCluster(res.data[0].id);
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	}, []);

	useEffect(() => {
		currentCluster &&
			getConf({
				clusterId: currentCluster,
				username: JSON.parse(storage.getLocal('role'))?.userName
			}).then((res) => {
				if (res.success) {
					setYamlValue(res.data);
				}
			});
	}, [currentCluster]);

	const onOk = () => {
		onCancel();
	};

	// *------------显示-------------------
	const screenExtend = () => {
		setIsFullscreen(true);
	};

	const screenShrink = () => {
		setIsFullscreen(false);
	};

	const download = () => {
		currentCluster &&
			exportFile(
				`${api}/user/${
					JSON.parse(storage.getLocal('role'))?.userName
				}/conf`,
				{
					clusterId: currentCluster,
					username: JSON.parse(storage.getLocal('role'))?.userName
				},
				JSON.parse(storage.getLocal('role'))?.userName,
				'.conf'
			);
		setDisabled(false);
	};

	return (
		<Modal
			title="文件下载"
			width={800}
			onOk={onOk}
			open={open}
			onCancel={onCancel}
			footer={
				<>
					<Button onClick={onCancel}>取消</Button>
					<Button
						onClick={onCreate}
						type="primary"
						disabled={disabled}
					>
						确定
					</Button>
				</>
			}
		>
			<h2>选择集群</h2>
			<Select
				style={{ width: '100%' }}
				options={clusterList}
				value={currentCluster}
				onChange={(value) => setCurrentCluster(value)}
			/>
			<h2 className="mt-16">文件预览</h2>
			<div className={`${isFullscreen ? 'log-full-screen' : ''}`}>
				<div
					className={`yaml-check-title display-flex flex-space-between`}
				>
					<span>YAML文件</span>
					<div className={`display-flex flex-align`}>
						<DownloadOutlined className="mr-8" onClick={download} />
						{!isFullscreen && (
							<ArrowsAltOutlined onClick={screenExtend} />
						)}
						{isFullscreen && (
							<ShrinkOutlined onClick={screenShrink} />
						)}
					</div>
				</div>
				<div id="yaml-check-codemirror">
					<CodeMirror
						value={yamlValue}
						options={{
							mode: 'yaml',
							theme: 'twilight',
							lineNumbers: true,
							readOnly: true,
							lineWrapping: true
						}}
					/>
				</div>
			</div>
		</Modal>
	);
}

export default YamlModal;

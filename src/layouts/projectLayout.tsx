import React, { useEffect, useState } from 'react';
import { MenuProps, notification } from 'antd';
import { useHistory, useLocation } from 'react-router';
import { connect } from 'react-redux';
import { setMenu } from '@/redux/auth/auth';
import { LeftOutlined, RightOutlined, SwapOutlined } from '@ant-design/icons';
import MyMenu from './Menu/MyMenu';
import {
	getMenu,
	getMiddlewareMenu,
	getNewMenu,
	getNewMiddlewareMenu
} from '@/services/common';
import { IconFont } from '@/components/IconFont';
import ChangeProject from './changeProject';
import { StoreState } from '@/types';
import storage from '@/utils/storage';
import './layout.less';

type MenuItem = Required<MenuProps>['items'][number];

function getItem(
	label: React.ReactNode,
	key: React.Key,
	icon?: React.ReactNode,
	children?: MenuItem[]
): MenuItem {
	return {
		key,
		icon,
		children,
		label
	} as MenuItem;
}
function ProjectLayout({
	children,
	setMenu
}: {
	children: any;
	setMenu: (item: any) => void;
}): JSX.Element {
	const history = useHistory();
	const location = useLocation();
	const organId = storage.getSession('organId');
	const projectId = storage.getSession('projectId');
	const project = storage.getSession('project');
	const [collapsed, setCollapsed] = useState<boolean>(false); // * 是否收起侧边栏
	const [items, setItems] = useState<any[]>([]);
	const [open, setOpen] = useState<boolean>(false);
	useEffect(() => {
		let mounted = true;
		async function getAllData() {
			try {
				// const res1 = await getMenu({ organId, projectId });
				const res1 = await getNewMenu({ organId, projectId });
				if (!res1.success) {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res1.errorMsg}</p>
								<p>{res1.errorDetail}</p>
							</>
						)
					});
				}
				// const res2 = await getMiddlewareMenu({ organId, projectId });
				const res2 = await getNewMiddlewareMenu({ organId, projectId });
				if (!res2.success) {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res2.errorMsg}</p>
								<p>{res2.errorDetail}</p>
							</>
						)
					});
				}
				const subMenu = res1.data?.[0].subMenu.filter(
					(item: any) => item.url
				);
				const projectMenu = res1.data.find(
					(item: any) => item.id === 1
				)?.subMenu;
				const index = subMenu.findIndex(
					(item: any) => item?.weight > 3
				);
				setMenu(projectMenu);
				subMenu.splice(index, 0, ...(res2?.data || []));
				const lt = subMenu.map((item: MenuResItem) => {
					const children = item.subMenu?.map((i) => {
						return getItem(i.aliasName, i.url);
					});
					return getItem(
						item.aliasName,
						item.url,
						<IconFont type={item.iconName} size={14} />,
						children
					);
				});
				setItems(lt);
			} catch {
				console.log('something error');
			}
		}
		if (organId && projectId) {
			if (mounted) {
				getAllData();
			}
		}
		return () => {
			mounted = false;
		};
	}, [organId, projectId]);
	useEffect(() => {
		if (items.length !== 0 && location.pathname === '/project') {
			if (items[0].children?.length > 0) {
				history.push(`/${items[0].children[0]?.key}`);
			} else {
				history.push(`/${items[0]?.key}`);
			}
		}
	}, [items]);
	return (
		<>
			<div className="zeus-mid-content">
				<aside
					style={{
						width: collapsed ? '0px' : '216px',
						overflowY: collapsed ? 'hidden' : 'auto'
					}}
				>
					<div className="project-header-content">
						<IconFont
							type="icon-fanhui"
							style={{ fontSize: '24px' }}
							onClick={() => {
								const preUrl = storage.getSession('preUrl');
								preUrl
									? history.push(`${preUrl}`)
									: history.goBack();
							}}
						/>
						<div className="project-title-content">
							<p title={project.aliasName || project.name}>
								{project.aliasName || project.name}
							</p>
							<p>项目</p>
						</div>
						<div
							className="project-change-content"
							onClick={() => setOpen(true)}
						>
							<SwapOutlined />
						</div>
					</div>
					<MyMenu items={items} />
				</aside>
				<div
					className="zeus-mid-left-content"
					style={{
						marginLeft: collapsed ? '0px' : '216px',
						minWidth: collapsed ? '100%' : 'calc(100% - 218px)'
					}}
				>
					{children}
				</div>
			</div>
			<div
				className="zeus-mid-flod-content"
				style={{
					left: collapsed ? '0px' : '216px'
				}}
				onClick={() => setCollapsed(!collapsed)}
			>
				{collapsed ? <RightOutlined /> : <LeftOutlined />}
			</div>
			{open && (
				<ChangeProject
					items={items}
					open={open}
					onCancel={() => setOpen(false)}
				/>
			)}
		</>
	);
}

export default connect((state: StoreState) => ({ menu: state.auth.menu }), {
	setMenu
})(ProjectLayout);

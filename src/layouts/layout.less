.middleware-layout {
	padding-top: 50px;
	overflow: hidden;
}

// 重构后样式
.zeus-mid-layout {
	position: relative;
	width: 100%;
	height: 100%;
	.zeus-mid-content {
		padding-top: 50px;
		position: absolute;
		width: 100%;
		height: 100%;
		& > aside {
			padding-top: 12px;
			padding-left: 12px;
			border-right: 1px solid #f0f0f0;
			height: calc(100% - 50px);
			position: fixed;
			left: 0;
			overflow-y: auto;
			transition: all 0.1s ease 0s;
			-moz-transition: all 0.1s ease 0s;
			-webkit-transition: all 0.1s ease 0s;
			-o-transition: all 0.1s ease 0s;
			.zeus-mid-title {
				height: 54px;
				font-size: @font-2;
				font-weight: @font-weight-lg;
				color: @black-2;
				padding: @padding-llg @padding-lg;
				border-right: 1px solid #f0f0f0;
			}
		}
		.zeus-mid-left-content {
			min-width: calc(100% - 200px);
			height: calc(100% - 50px);
			transition: all 0.1s ease 0s;
			-moz-transition: all 0.1s ease 0s;
			-webkit-transition: all 0.1s ease 0s;
			-o-transition: all 0.1s ease 0s;
		}
	}
	.zeus-mid-flod-content {
		position: fixed;
		cursor: pointer;
		top: 50%;
		left: 200px;
		width: 20px;
		height: 80px;
		text-align: center;
		line-height: 80px;
		background-color: @black-9;
		z-index: 999;
		border-radius: 0 @border-radius-xxl @border-radius-xxl 0;
		transition: all 0.1s ease 0s;
		-moz-transition: all 0.1s ease 0s;
		-webkit-transition: all 0.1s ease 0s;
		-o-transition: all 0.1s ease 0s;
	}
}
.ant-menu-inline .ant-menu-item::after {
	border-right: none !important;
}
.project-header-content,
.platform-header-content {
	width: 188px;
	height: 58px;
	background-color: @primary-color;
	border-radius: @border-radius-lg;
	font-size: @font-2;
	color: white;
	display: flex;
	align-items: center;
	margin-bottom: @margin-lg;
	padding: 12px;
	gap: 8px;
}
.project-header-content {
	justify-content: space-between;
	.project-title-content {
		width: 100px;
		flex-grow: 2;
		p {
			text-overflow: ellipsis;
			overflow: hidden;
			white-space: nowrap;
		}
		& > p:nth-child(2) {
			font-size: 12px;
			line-height: 22px;
			color: hsla(0, 0%, 100%, 0.5) !important;
		}
	}
	.project-change-content {
		width: 22px;
		height: 22px;
		font-size: 14px;
		line-height: 22px;
		text-align: center;
		background-color: rgba(20, 57, 150, 0.4);
		border-radius: @border-radius;
		cursor: pointer;
	}
}
.top-organ-list-content,
.top-project-list-content {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	align-items: center;
	.allot-project-item {
		width: 196px;
		height: 40px;
		background: @white;
		box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.12);
		border-radius: @border-radius;
		display: flex;
		cursor: pointer;
		.allot-project-item-icon {
			background-color: #dfdfdf;
			opacity: 0.2;
			width: 40px;
			font-size: 24px;
			text-align: center;
			border-radius: @border-radius;
		}
		.allot-project-item-content {
			padding: @padding-sm;
			width: 157px;
			.allot-project-item-content-title {
				width: auto;
				font-size: @font-2;
				font-weight: @font-weight;
				color: #333333;
				line-height: @line-height-2;
				.mixin(textEllipsis);
			}
		}
	}
}
#change-project {
	height: 400px;
	overflow-y: auto;
	.ant-collapse-header-text {
		width: 97%;
	}
	.organization-active {
		border: 1px solid @primary-color;
		.ant-collapse-header {
			background-color: rgba(248, 251, 253, 1);
		}
		.ant-collapse-item-active {
			background-color: rgba(248, 251, 253, 1);
			.ant-collapse-header {
				background-color: transparent;
			}
		}
	}
}

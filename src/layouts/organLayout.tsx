import React, { useEffect, useState } from 'react';
import { MenuProps, Spin, notification } from 'antd';
import { LeftOutlined, RightOutlined, SwapOutlined } from '@ant-design/icons';
import MyMenu from './Menu/MyMenu';
import { getMenu, getNewMenu } from '@/services/common';
import { IconFont } from '@/components/IconFont';
import storage from '@/utils/storage';
import { getOrganizationDetail } from '@/services/organization';
import { OrganizationItem } from '@/pages/OrganizationManagement/organization';
import { useHistory, useLocation } from 'react-router';
import ChangeOrgan from './changeOrgan';
import './layout.less';

type MenuItem = Required<MenuProps>['items'][number];

function getItem(
	label: React.ReactNode,
	key: React.Key,
	icon?: React.ReactNode,
	children?: MenuItem[]
): MenuItem {
	return {
		key,
		icon,
		children,
		label
	} as MenuItem;
}
export default function OrganLayout({
	children
}: {
	children: any;
}): JSX.Element {
	const history = useHistory();
	const location = useLocation();
	const organId = storage.getSession('organId');
	const [collapsed, setCollapsed] = useState<boolean>(false); // * 是否收起侧边栏
	const [items, setItems] = useState<MenuItem[]>([]);
	const [open, setOpen] = useState<boolean>(false);
	const [organization, setOrganization] = useState<OrganizationItem>();
	const [spinning, setSpinning] = useState<boolean>(false);
	useEffect(() => {
		let mounted = true;
		async function getAllData() {
			setSpinning(true);
			try {
				// const res1 = await getMenu({ organId });
				const res1 = await getNewMenu({ organId });
				if (!res1.success) {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res1.errorMsg}</p>
								<p>{res1.errorDetail}</p>
							</>
						)
					});
				}
				const org = res1.data.find(
					(item: MenuResItem) =>
						item.url === 'platform/organizationManagement'
				);
				const lt = org.subMenu?.map((item: MenuResItem) => {
					return getItem(
						item.aliasName,
						item.url,
						<IconFont type={item.iconName} size={14} />
					);
				});
				setItems(lt.filter((item: MenuItem) => item?.key));
				const res2 = await getOrganizationDetail({ organId });
				if (!res2.success) {
					notification.error({
						message: '错误',
						description: (
							<>
								<p>{res2.errorMsg}</p>
								<p>{res2.errorDetail}</p>
							</>
						)
					});
				}
				setOrganization(res2.data);
				storage.setSession('organization', res2.data);
				setSpinning(false);
			} catch {
				setSpinning(false);
			}
		}
		if (organId) {
			if (mounted) {
				getAllData();
			}
		}
		return () => {
			mounted = false;
		};
	}, [organId]);
	useEffect(() => {
		if (items.length !== 0 && location.pathname === '/organization') {
			history.push(`/${items[0]?.key}`);
		}
	}, [items]);
	return (
		<Spin spinning={spinning}>
			<div className="zeus-mid-content">
				<aside
					style={{
						width: collapsed ? '0px' : '216px',
						overflowY: collapsed ? 'hidden' : 'auto'
					}}
				>
					<div className="project-header-content">
						<IconFont
							type="icon-fanhui"
							style={{ fontSize: '24px' }}
							onClick={() => {
								history.push(
									'/platform/organizationManagement'
								);
							}}
						/>
						<div className="project-title-content">
							<p title={organization?.name}>
								{organization?.name}
							</p>
							<p>组织</p>
						</div>
						<div
							className="project-change-content"
							onClick={() => setOpen(true)}
						>
							<SwapOutlined />
						</div>
					</div>
					<MyMenu items={items} />
				</aside>
				<div
					className="zeus-mid-left-content"
					style={{
						marginLeft: collapsed ? '0px' : '216px',
						minWidth: collapsed ? '100%' : 'calc(100% - 216px)'
					}}
				>
					{children}
				</div>
			</div>
			<div
				className="zeus-mid-flod-content"
				style={{
					left: collapsed ? '0px' : '216px'
				}}
				onClick={() => setCollapsed(!collapsed)}
			>
				{collapsed ? <RightOutlined /> : <LeftOutlined />}
			</div>
			{open && (
				<ChangeOrgan open={open} onCancel={() => setOpen(false)} />
			)}
		</Spin>
	);
}

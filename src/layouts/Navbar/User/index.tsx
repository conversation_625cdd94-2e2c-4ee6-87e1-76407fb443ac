import React, { useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { Popover, notification } from 'antd';
import { EditOutlined } from '@ant-design/icons';
import { IconFont } from '@/components/IconFont';

import { postLogout } from '@/services/user';
import EditPasswordForm from './EditPasswordForm';
import { getLDAP } from '@/services/user';
import Storage from '@/utils/storage';
import logoutSvg from '@/assets/images/navbar/logout.svg';
import storage from '@/utils/storage';
import { TOKEN } from '@/services/request';
import './index.less';

function User({ role }: { role: any; className: string }): JSX.Element {
	const [visible, setVisible] = useState<boolean>(false);
	const [isLDAP, setIsLDAP] = useState<boolean>(false);
	const [isAccess] = useState<boolean>(storage.getLocal('isAccessGYT'));
	const history = useHistory();
	const logout = () => {
		postLogout().then((res) => {
			if (res.success) {
				Storage.removeLocal(TOKEN, true);
				Storage.removeSession('service-available-current', true);
				Storage.removeLocal('firstAlert');
				history.push('/login');
				window.location.reload();
			} else {
				notification.error({
					message: '失败',
					description: res.errorMsg
				});
			}
		});
	};
	const editPassword = () => {
		setVisible(true);
	};

	useEffect(() => {
		getLDAP().then((res: any) => {
			res.success && setIsLDAP(res.data.isOn);
		});
	}, []);
	const title = (
		<div className="nav-user-icon-box">
			<div>{role?.nickName}</div>
			<span className="nav-user-role-p">{role?.userName}</span>
		</div>
	);
	const content = () => {
		return (
			<ul>
				{Storage.getLocal('role') &&
				JSON.parse(Storage.getLocal('role')).isAdmin &&
				(isLDAP || isAccess) ? null : (
					<li
						className="nav-user-container-item"
						onClick={editPassword}
					>
						<EditOutlined
							style={{ fontSize: '14px', marginRight: '4px' }}
						/>
						修改密码
					</li>
				)}
				<li className="nav-user-container-item" onClick={logout}>
					<img src={logoutSvg} alt="退出" />
					退出登录
				</li>
			</ul>
		);
	};
	return (
		<>
			<Popover
				overlayClassName="zeus-nav"
				placement="bottomRight"
				title={title}
				content={content}
			>
				<div className="nav-icon-font">
					<IconFont
						type="icon-user-circle"
						style={{
							fontSize: '20px'
						}}
					/>
				</div>
			</Popover>
			{visible && (
				<EditPasswordForm
					visible={visible}
					onCancel={() => setVisible(false)}
					userName={role.userName}
				/>
			)}
		</>
	);
}

export default User;

.nav-user-icon-box {
	.nav-user-role-p {
		margin-top: 2px;
		padding: 2px 6px;
		line-height: @line-height-4;
		font-size: @font-1;
		border: 1px solid @primary-color;
		color: @primary-color;
	}
}
.nav-icon-font {
	width: 60px;
	height: 48px;
	text-align: center;
	line-height: 48px;
	cursor: pointer;
	&:hover {
		background-color: #edf0f5;
	}
	& > span {
		margin-top: 4px;
	}
}
.nav-user-container {
	position: relative;
}
.nav-user-role {
	display: inline;
	margin-right: 4px;
	padding: 0 2px;
	border-radius: 50%;
	border: 1px solid #49a9e1;
	font-size: @font-1;
	color: #49a9e1;
}
.nav-user-operator {
	display: none;
	position: absolute;
	top: 50px;
	right: 0;
	z-index: 100;
	box-shadow: @shadow-base;
	transition: all 0.1s ease 0s;
	-moz-transition: all 0.1s ease 0s;
	-webkit-transition: all 0.1s ease 0s;
	-o-transition: all 0.1s ease 0s;
}
.nav-user-container:hover .nav-user-operator {
	display: block;
}
.nav-user-container-item {
	width: 150px;
	padding-left: 20px;
	line-height: 36px;
	background: @white;
	border-bottom: 1px solid @border-color;
	text-align: left;
	cursor: pointer;
	&:hover {
		background-color: @background;
	}
}

.nav-user-container-item img {
	margin-right: 6px;
	vertical-align: middle;
}
.edit-form-icon-style {
	line-height: @line-height-1;
	display: flex;
	align-items: center;
}

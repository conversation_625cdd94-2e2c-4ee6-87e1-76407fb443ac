import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>u, Tooltip } from 'antd';
import { useHistory } from 'react-router';
import { connect } from 'react-redux';
import { setButtonList, setAllMenu } from '@/redux/auth/auth';
import User from './User';
import storage from '@/utils/storage';
import { IconFont } from '@/components/IconFont';
import { getNewTopMenu } from '@/services/common';
import YamlModal from '../yamlModal';
import { licenseEnable } from '@/services/user';
import { BellFilled } from '@ant-design/icons';
import { getOrders } from '@/services/workOrder';
import { StoreState } from '@/types';
import './navbar.less';

function Navbar({
	role,
	allMenu,
	setAllMenu
}: {
	role: any;
	allMenu: any[];
	setAllMenu: (data: any) => void;
}): JSX.Element {
	// * 设置logo
	const personalization = storage.getLocal('personalization');
	const history = useHistory();
	// * originData 用来解决在Menu上出现多余的subMenu的属性的告警
	const [originData, setOriginData] = useState<any[]>([]);
	const [items, setItems] = useState<any[]>([]);
	const [open, setOpen] = useState<boolean>(false);
	// * 控制license授权管理
	const [licenseVisible, setLicenseVisible] = useState<string>('false');
	// * 工单数量
	const [orderCount, setOrderCount] = useState<number>(0);
	const [topMenu, setTopMenu] = useState<MenuResItem[]>([]);
	const show = (code: string) =>
		topMenu.find((item: MenuResItem) => item.name === code);
	useEffect(() => {
		// getTopMenu().then((res) => {
		// 	const originList = res.data.map((item: MenuResItem) => {
		// 		return {
		// 			key: item.url,
		// 			label: item.aliasName,
		// 			icon: <IconFont type={item.iconName} />,
		// 			url: item.url,
		// 			subMenu: item.subMenu
		// 		};
		// 	});
		// 	const list = res.data.map((item: MenuResItem) => {
		// 		return {
		// 			key: item.url,
		// 			label: item.aliasName,
		// 			icon: <IconFont type={item.iconName} />,
		// 			url: item.url
		// 		};
		// 	});
		// 	setOriginData(originList);
		// 	setItems(list);
		// });
		getNewTopMenu().then((res) => {
			const menu = res.data.filter((item: MenuResItem) => item.url);
			const originList = menu.map((item: MenuResItem) => {
				return {
					key: item.url,
					label: item.aliasName,
					icon: <IconFont type={item.iconName} />,
					url: item.url,
					subMenu: item.subMenu
				};
			});
			const list = menu.map((item: MenuResItem) => {
				return {
					key: item.url,
					label: item.aliasName,
					icon: <IconFont type={item.iconName} />,
					url: item.url
				};
			});
			setItems(list);
			setAllMenu(res.data);
			setOriginData(originList);
		});
		licenseEnable().then((res) => {
			if (res.success) {
				setLicenseVisible(res.data);
			}
		});
		getOrders({
			tab: 'waiting',
			current: 1,
			size: 10,
			sortByUpdateTime: 'asc',
			status: '',
			keyword: '',
			topic: ''
		}).then((res) => {
			if (res.success) {
				setOrderCount(res.data.total);
			}
		});
	}, [window.location.hash]);
	useEffect(() => {
		const orderInterval = setInterval(() => {
			getOrderData();
		}, 30000);
		return () => {
			clearInterval(orderInterval);
		};
	}, []);
	useEffect(() => {
		setTopMenu(
			allMenu?.find((item: MenuResItem) => item.module === 'topArea')
				?.subMenu || []
		);
	}, [allMenu]);
	const getOrderData = () => {
		getOrders({
			tab: 'waiting',
			current: 1,
			size: 10,
			sortByUpdateTime: 'asc',
			status: '',
			keyword: '',
			topic: ''
		}).then((res) => {
			if (res.success) {
				setOrderCount(res.data.total);
			}
		});
	};
	const onClick = (e: any) => {
		if (e.key === 'workspace') {
			history.push(`/${e.key}`);
		} else {
			const firstMenu = originData.find((item) => item.key === e.key);
			history.push(`/${firstMenu?.subMenu?.[0]?.url}`);
		}
	};
	return (
		<div className="middleware-navbar">
			<nav className="nav">
				<div
					className="logo-box"
					style={{
						lineHeight: '48px',
						textAlign: 'center',
						padding: '5px 0px'
					}}
				>
					<img
						className="logo-png"
						src={personalization && personalization.homeLogo}
						alt=""
					/>
				</div>
				<div id="nav-content">
					<Menu
						selectedKeys={[]}
						mode="horizontal"
						items={items}
						onClick={onClick}
					/>
				</div>
				{show('userPermissionFileDownload') && (
					<Tooltip title="用户权限文件下载" placement="bottom">
						<div
							className="license-icon"
							onClick={() => setOpen(true)}
						>
							<IconFont
								type="icon-YAML"
								style={{
									fontSize: 20,
									color: 'rgb(29,29,29)',
									marginTop: '12px'
								}}
							/>
						</div>
					</Tooltip>
				)}
				{licenseVisible === 'true' &&
					show('authorizationManagement') && (
						<Tooltip title="授权管理" placement="bottom">
							<div
								className="license-icon"
								onClick={() =>
									history.push('/workspace/authorManage')
								}
							>
								<IconFont
									type="icon-shouquan"
									style={{
										fontSize: 20,
										color: 'rgb(29,29,29)'
									}}
								/>
							</div>
						</Tooltip>
					)}
				{show('workOrderManagement') && (
					<Tooltip title="工单管理" placement="bottom">
						<div
							className="license-icon"
							onClick={() => {
								history.push('/workspace/workOrderManage');
							}}
						>
							<Badge size="small" count={orderCount}>
								<BellFilled
									style={{
										fontSize: 20,
										color: 'rgb(29,29,29)'
									}}
								/>
							</Badge>
						</div>
					</Tooltip>
				)}
				<User className="module" role={role} />
			</nav>
			{open && (
				<YamlModal
					open={open}
					onCreate={() => setOpen(false)}
					onCancel={() => setOpen(false)}
				/>
			)}
		</div>
	);
}

export default connect(
	(state: StoreState) => ({
		allMenu: state.auth.allMenu
	}),
	{
		setAllMenu
	}
)(Navbar);

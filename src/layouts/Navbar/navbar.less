.no-shadow.ant-select:hover {
	box-shadow: none !important;
	background-color: #EEEEEE !important;
}
.no-shadow.ant-select {
	box-shadow: none !important;
	background-color: @background !important;
}
.license-icon{
	width: 48px;
	height: 48px;
	line-height: 48px;
	text-align: center;
	cursor: pointer;
	&:hover{
		background-color: #edf0f5;
	}
}
.middleware-navbar {
	width: 100%;
	height: 50px;
	z-index: 800;
	position: fixed;
	top: 0;
	background-color: @black-10;
}

.nav {
	z-index: 800;
	height: 100%;
	line-height: 48px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.08),
		0px 1px 0px 0px rgba(0, 0, 0, 0.02) !important;
}

.nav {
	.logo-box {
		width: 208px;
		position: relative;
	}
	.logo-png {
		height: 40px;
		// padding: 12px 16px;
		text-align: center;
	}
	#nav-content {
		width: calc(100% - 208px - 44px);
		.ant-menu-horizontal {
			border-bottom: none !important;
		}
	}
	.module {
		position: relative;
		line-height: 48px;
		padding: 0 12px;
		text-align: center;
		cursor: pointer;
		// &:hover {
		// 	background-color: @background;
		// }
	}
}

.next-menu-header {
	height: 48px;
}


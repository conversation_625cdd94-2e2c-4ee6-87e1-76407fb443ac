import React, { useEffect, useState } from 'react';
import { Modal, Spin, notification } from 'antd';
import { useHistory } from 'react-router';
import { ListPanel } from '@/components/ListCard';
import ProList from '@/components/ProList';
import { OrganizationItem } from '@/pages/OrganizationManagement/organization';
import { getOrganizations } from '@/services/organization';
import { IconFont } from '@/components/IconFont';
import { getProjects } from '@/services/project';
import { getMyTopic } from '@/services/workOrder';
import storage from '@/utils/storage';
import { ProjectItem } from '@/types';
import './layout.less';

export default function ChangeProject({
	open,
	onCancel,
	items
}: {
	open: boolean;
	onCancel: () => void;
	items: any;
}): JSX.Element {
	const organId = storage.getSession('organId');
	const project = storage.getSession('project');
	const history = useHistory();
	const [spinning, setSpinning] = useState<boolean>(false);
	const [organs, setOrgans] = useState<OrganizationItem[]>([]);
	const [currentProject, setCurrentProject] = useState<ProjectItem>(project);
	useEffect(() => {
		async function getAllData() {
			setSpinning(true);
			try {
				await getOrgans();
				setSpinning(false);
			} catch {
				setSpinning(false);
				throw new Error('接口发生错误');
			}
		}
		getAllData();
	}, []);
	const getOrgans = async () => {
		const res = await getOrganizations({ keyword: '' });
		const res2 = await getProjects({
			organId: organId || '',
			key: ''
		});
		if (!res.success) {
			notification.error({
				message: '错误',
				description: (
					<>
						<p>{res.errorMsg}</p>
						<p>{res.errorDetail}</p>
					</>
				)
			});
			return;
		}
		if (!res2.success) {
			notification.error({
				message: '错误',
				description: (
					<>
						<p>{res2.errorMsg}</p>
						<p>{res2.errorDetail}</p>
					</>
				)
			});
			return;
		}
		const list = res.data.map((item) => {
			if (item.organId === organId) {
				item.children = res2.data;
			} else {
				item.children = [];
			}
			return item;
		});
		setOrgans(list);
	};
	const getProject = async (organId: string) => {
		const res = await getProjects({
			organId: organId || '',
			key: ''
		});
		if (res.success) {
			const list = organs.map((item) => {
				if (item.organId === organId) {
					item.children = res.data;
				}
				return item;
			});
			setOrgans(list);
		} else {
			notification.error({
				message: '错误',
				description: (
					<>
						<p>{res.errorMsg}</p>
						<p>{res.errorDetail}</p>
					</>
				)
			});
		}
	};
	const onChange = (record: OrganizationItem, number: any) => {
		getProject(record.organId);
	};
	const onOk = () => {
		if (currentProject) {
			const curOrgan = organs.find(
				(item) => item.organId === currentProject.organId
			);
			getMyTopic({
				organId: currentProject.organId,
				projectId: currentProject.projectId
			}).then((res) => {
				if (res.success) {
					storage.setLocal('myTopic', res.data);
				}
			});
			storage.setSession('organId', currentProject.organId);
			storage.setSession('organization', curOrgan);
			storage.setSession('projectId', currentProject.projectId);
			storage.setSession('project', currentProject);
			history.push('/project');
			onCancel();
		}
	};
	return (
		<Modal
			width={880}
			title="切换项目"
			open={open}
			onCancel={onCancel}
			onOk={onOk}
			okButtonProps={{
				disabled: currentProject.projectId === project.projectId
			}}
		>
			<div id="change-project">
				<Spin spinning={spinning}>
					<ProList>
						{organs.map((item: OrganizationItem) => {
							return (
								<ListPanel
									key={item.organId}
									title={item.name}
									subTitle="组织名称"
									icon={
										organId === item.organId ? (
											<IconFont
												type="icon-a-bianzu56"
												style={{
													width: '40px',
													height: '40px',
													fontSize: '24px',
													lineHeight: '43px',
													marginRight: '8px',
													color: 'white'
												}}
											/>
										) : (
											<IconFont
												type="icon-zuzhikongjian"
												style={{
													width: '40px',
													height: '40px',
													fontSize: '24px',
													lineHeight: '43px',
													marginRight: '8px',
													color: 'white'
												}}
											/>
										)
									}
									onChange={(number: any) =>
										onChange(item, number)
									}
									canHeaderExpand={true}
									headStyle={{ width: '500px' }}
									render={
										<div className="top-project-list-content">
											{item.children?.map(
												(i: ProjectItem) => {
													return (
														<div
															key={i.projectId}
															className={`allot-project-item ${
																currentProject?.name ===
																i.name
																	? 'allot-project-item-active'
																	: ''
															}`}
															onClick={() => {
																setCurrentProject(
																	i
																);
															}}
														>
															<div className="allot-project-item-icon">
																<IconFont type="icon-wodexiangmu" />
															</div>
															<div className="allot-project-item-content">
																<div className="allot-project-item-content-title">
																	{
																		i.aliasName
																	}
																</div>
															</div>
														</div>
													);
												}
											)}
										</div>
									}
									activeKey={{
										defaultActiveKey:
											organId === item.organId ? 1 : ''
									}}
									className={
										item.organId === organId
											? 'organization-active'
											: ''
									}
								/>
							);
						})}
					</ProList>
				</Spin>
			</div>
		</Modal>
	);
}

worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;

    sendfile        on;
    #tcp_nopush     on;
    server_tokens off;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    gzip on;
    gzip_buffers 32 4k;
    gzip_comp_level 5;
    gzip_min_length 200;
    gzip_types text/css text/xml application/javascript;
    gzip_vary on;

    server {
        listen       80;
        server_name  localhost;
        #client_max_body_size 200M;
        #charset koi8-r;

        #access_log  logs/host.access.log  main;

         location /zeus-ui/ {
            alias /opt/sinopec/zeus-ui/;
            try_files $uri /index.html;
            index index.html index.htm;
            autoindex off;
            add_header  Access-Control-Allow-Origin * ;
            add_header  Access-Control-Allow-Methods *;
            add_header  Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
        }

        location ~ ^/(?!zeus-ui/).*\.(js|css|htm|html|gif|jpg|jpeg|png|bmp|swf|ioc|rar|zip|txt|flv|mid|doc|ppt|pdf|xls|mp3|mp4|wma|svg|otf|ttf|ttc|eot|woff|woff2|json)$ {
            add_header  Access-Control-Allow-Origin   * ;
            add_header  Access-Control-Allow-Methods *;
            add_header  Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
            add_header  Cache-Control no-cache;
            root /opt/sinopec/zeus-ui;
        }

        location /zeusapi/ {
                proxy_pass http://*************:31089/;
                proxy_http_version 1.1;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
				add_header Cache-Control no-cache;
				add_header Pragma no-cache;
            }

            location /zeususer/ {
                proxy_pass http://*************:31089/;
                proxy_http_version 1.1;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            }

			location /ws/ {
                proxy_pass http://*************:31089/;
                proxy_http_version 1.1;
                proxy_connect_timeout 10m;
                proxy_read_timeout 10m;
                proxy_send_timeout 10m;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header Upgrade $http_upgrade;   # 升级协议头
                proxy_set_header Connection upgrade;
            }

        #error_page  404              /404.html;

        # redirect server error pages to the static page /50x.html
        #
        error_page   500 502 503 504  /50x.html;
        # To allow POST on static pages 允许静态页使用POST方法
        error_page  405     =200 $uri;
        location = /50x.html {
            root   html;
        }
    }
  	server{
  		listen 80;
  		server_name _;
  		location /{
  			return 403;
  		}
  	}
}

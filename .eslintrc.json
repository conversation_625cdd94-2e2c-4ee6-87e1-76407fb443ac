{"parser": "@typescript-eslint/parser", "env": {"browser": true, "es6": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:prettier/recommended", "plugin:@typescript-eslint/recommended"], "globals": {"Atomics": "readonly", "SharedArrayBuffer": "readonly"}, "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 2020, "sourceType": "module"}, "plugins": ["react", "@typescript-eslint"], "rules": {"react/display-name": "off", "react/prop-types": "off", "no-explicit-any": "off", "no-unused-expressions": "off"}, "overrides": [{"files": ["*.ts"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "extends": ["plugin:@typescript-eslint/recommended"]}]}
{"name": "zeus-ui", "version": "1.0.0", "private": true, "homepage": ".", "dependencies": {"@ant-design/icons": "4.8.0", "@ant-design/plots": "^1.2.5", "@antv/g6": "4.3.11", "@babel/helper-builder-react-jsx": "^7.10.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@types/codemirror": "^5.60.5", "@types/jest": "^26.0.24", "@types/js-yaml": "^4.0.5", "@types/react": "17.0.39", "@types/react-dom": "17.0.11", "@types/react-router-dom": "^5.1.8", "@types/react-split-pane": "^0.1.67", "@types/sql-formatter": "^4.0.1", "@typescript-eslint/eslint-plugin": "^4.28.3", "@typescript-eslint/parser": "^4.28.3", "antd": "^4.23.0", "axios": "^1.6.8", "codemirror": "^5.64.0", "css-loader": "^4.3.0", "decimal.js": "^10.4.3", "echarts": "5.2.2", "echarts-for-react": "^3.0.1", "js-yaml": "^4.1.0", "jsencrypt": "^3.2.0", "less": "^4.1.2", "less-loader": "6.0.0", "ml-matrix": "6.10.4", "moment": "^2.29.1", "nprogress": "^0.2.0", "react": "^16.13.1", "react-codemirror2": "^7.2.1", "react-dom": "^16.13.1", "react-intl": "^5.8.3", "react-redux": "^7.2.1", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-split-pane": "^0.1.92", "redux": "^4.0.5", "redux-thunk": "^2.3.0", "sass-resources-loader": "^2.1.1", "style-loader": "^1.2.1", "typescript": "4.4.3", "webpack": "4.42.0", "webpack-dev-server": "3.11.0", "xterm": "^4.14.1", "xterm-addon-fit": "^0.5.0"}, "scripts": {"start": "react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{jsx,js,tsx,ts}": ["prettier --write", "eslint --fix"]}, "devDependencies": {"@types/babel__traverse": "7.20.6", "@types/minimatch": "5.1.2", "@types/node": "16.18.106", "@types/react-custom-scrollbars": "^4.0.13", "@types/semver": "^7.5.3", "babel-plugin-import": "^1.13.3", "cross-env": "^7.0.3", "customize-cra": "^1.0.0", "eslint": "^6.6.0", "eslint-config-prettier": "^6.12.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.21.2", "http-proxy-middleware": "^1.0.6", "husky": "^4.3.0", "lint-staged": "^10.4.0", "prettier": "^2.1.2", "react-app-rewired": "^2.1.6", "react-custom-scrollbars": "^4.2.1", "react-scripts": "3.4.3", "semver": "^7.5.4"}}